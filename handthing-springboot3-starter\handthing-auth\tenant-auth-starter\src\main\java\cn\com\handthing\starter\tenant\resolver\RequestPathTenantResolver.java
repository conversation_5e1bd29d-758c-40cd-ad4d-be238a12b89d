package cn.com.handthing.starter.tenant.resolver;

import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * URL路径租户解析器
 * <p>
 * 从URL路径解析租户ID，支持自定义路径模式匹配。
 * 例如：/api/tenant-a/users -> tenant-a
 * </p>
 *
 * <AUTHOR>
 * @since V3.0.0
 */
public class RequestPathTenantResolver implements TenantResolver {

    private static final Logger log = LoggerFactory.getLogger(RequestPathTenantResolver.class);

    /**
     * 默认路径模式：/api/{tenant}/...
     */
    private static final String DEFAULT_PATTERN = "^/api/([a-zA-Z0-9][a-zA-Z0-9_-]*[a-zA-Z0-9]|[a-zA-Z0-9])(/.*)?$";

    /**
     * 路径模式
     */
    private final Pattern pathPattern;

    /**
     * 路径前缀
     */
    private final String pathPrefix;

    /**
     * 是否移除路径前缀
     */
    private final boolean removePrefix;

    /**
     * 构造函数（使用默认模式）
     */
    public RequestPathTenantResolver() {
        this(DEFAULT_PATTERN, "/api", false);
    }

    /**
     * 构造函数（指定路径前缀）
     *
     * @param pathPrefix 路径前缀，例如 "/api"
     */
    public RequestPathTenantResolver(String pathPrefix) {
        this(buildPatternFromPrefix(pathPrefix), pathPrefix, false);
    }

    /**
     * 构造函数（指定模式和前缀）
     *
     * @param pattern      路径模式
     * @param pathPrefix   路径前缀
     * @param removePrefix 是否移除路径前缀
     */
    public RequestPathTenantResolver(String pattern, String pathPrefix, boolean removePrefix) {
        this.pathPattern = Pattern.compile(pattern);
        this.pathPrefix = pathPrefix;
        this.removePrefix = removePrefix;
        
        log.debug("RequestPathTenantResolver initialized with pattern: {}, prefix: {}, removePrefix: {}", 
                  pattern, pathPrefix, removePrefix);
    }

    @Override
    public String resolveTenantId(HttpServletRequest request) {
        String requestPath = getRequestPath(request);
        if (!StringUtils.hasText(requestPath)) {
            log.debug("No request path found");
            return null;
        }

        log.debug("Resolving tenant from path: {}", requestPath);

        // 使用正则表达式提取租户ID
        Matcher matcher = pathPattern.matcher(requestPath);
        if (matcher.matches() && matcher.groupCount() >= 1) {
            String tenantId = matcher.group(1);
            log.debug("Extracted tenant ID: {} from path: {}", tenantId, requestPath);
            
            // 如果需要，修改请求路径（移除租户前缀）
            if (removePrefix && pathPrefix != null) {
                modifyRequestPath(request, tenantId);
            }
            
            return tenantId;
        }

        log.debug("No tenant ID found in path: {}", requestPath);
        return null;
    }

    @Override
    public boolean supports(HttpServletRequest request) {
        String requestPath = getRequestPath(request);
        return StringUtils.hasText(requestPath);
    }

    @Override
    public int getOrder() {
        return 30; // 较低优先级
    }

    @Override
    public String getName() {
        return "RequestPathTenantResolver";
    }

    @Override
    public String getDescription() {
        return "Resolves tenant ID from request path using pattern: " + pathPattern.pattern();
    }

    /**
     * 获取请求路径
     *
     * @param request HTTP请求
     * @return 请求路径
     */
    private String getRequestPath(HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        String contextPath = request.getContextPath();
        
        if (StringUtils.hasText(contextPath) && requestURI.startsWith(contextPath)) {
            return requestURI.substring(contextPath.length());
        }
        
        return requestURI;
    }

    /**
     * 修改请求路径（移除租户前缀）
     * <p>
     * 注意：这个方法在某些Servlet容器中可能不起作用，
     * 因为HttpServletRequest的某些属性是只读的。
     * </p>
     *
     * @param request  HTTP请求
     * @param tenantId 租户ID
     */
    private void modifyRequestPath(HttpServletRequest request, String tenantId) {
        try {
            // 尝试设置请求属性，供后续处理使用
            request.setAttribute("ORIGINAL_REQUEST_URI", request.getRequestURI());
            request.setAttribute("TENANT_ID", tenantId);
            request.setAttribute("TENANT_PREFIX_REMOVED", true);
            
            log.debug("Set tenant attributes for request modification: tenantId={}", tenantId);
        } catch (Exception e) {
            log.warn("Failed to modify request path for tenant: {}", tenantId, e);
        }
    }

    /**
     * 根据路径前缀构建模式
     *
     * @param prefix 路径前缀
     * @return 路径模式
     */
    private static String buildPatternFromPrefix(String prefix) {
        if (prefix == null || prefix.trim().isEmpty()) {
            return "^/([a-zA-Z0-9][a-zA-Z0-9_-]*[a-zA-Z0-9]|[a-zA-Z0-9])(/.*)?$";
        }
        
        String normalizedPrefix = prefix.trim();
        if (!normalizedPrefix.startsWith("/")) {
            normalizedPrefix = "/" + normalizedPrefix;
        }
        if (normalizedPrefix.endsWith("/")) {
            normalizedPrefix = normalizedPrefix.substring(0, normalizedPrefix.length() - 1);
        }
        
        // 转义特殊字符
        String escapedPrefix = normalizedPrefix.replace("/", "\\/");
        
        return "^" + escapedPrefix + "/([a-zA-Z0-9][a-zA-Z0-9_-]*[a-zA-Z0-9]|[a-zA-Z0-9])(/.*)?$";
    }

    /**
     * 创建基于模式的路径解析器
     *
     * @param pattern 路径模式，使用{tenant}作为占位符
     * @return 路径租户解析器实例
     */
    public static RequestPathTenantResolver fromPattern(String pattern) {
        if (pattern == null || pattern.trim().isEmpty()) {
            return new RequestPathTenantResolver();
        }

        // 将{tenant}占位符转换为正则表达式
        String regexPattern = pattern.replace("{tenant}", "([a-zA-Z0-9][a-zA-Z0-9_-]*[a-zA-Z0-9]|[a-zA-Z0-9])");
        
        // 转义其他特殊字符（除了已经转换的部分）
        regexPattern = regexPattern.replace(".", "\\.");
        
        // 确保匹配整个字符串
        if (!regexPattern.startsWith("^")) {
            regexPattern = "^" + regexPattern;
        }
        if (!regexPattern.endsWith("$")) {
            regexPattern = regexPattern + "$";
        }

        return new RequestPathTenantResolver(regexPattern, null, false);
    }

    /**
     * 创建基于前缀的路径解析器
     *
     * @param prefix       路径前缀
     * @param removePrefix 是否移除前缀
     * @return 路径租户解析器实例
     */
    public static RequestPathTenantResolver forPrefix(String prefix, boolean removePrefix) {
        return new RequestPathTenantResolver(buildPatternFromPrefix(prefix), prefix, removePrefix);
    }

    /**
     * 创建移除前缀的路径解析器
     *
     * @param prefix 路径前缀
     * @return 路径租户解析器实例
     */
    public static RequestPathTenantResolver withPrefixRemoval(String prefix) {
        return forPrefix(prefix, true);
    }

    /**
     * 获取当前配置信息
     *
     * @return 配置信息字符串
     */
    public String getConfigInfo() {
        return String.format("RequestPathTenantResolver{pattern=%s, prefix=%s, removePrefix=%s}", 
                           pathPattern.pattern(), pathPrefix, removePrefix);
    }

    /**
     * 获取路径模式
     *
     * @return 路径模式
     */
    public Pattern getPathPattern() {
        return pathPattern;
    }

    /**
     * 获取路径前缀
     *
     * @return 路径前缀
     */
    public String getPathPrefix() {
        return pathPrefix;
    }

    /**
     * 是否移除路径前缀
     *
     * @return 如果移除前缀返回true，否则返回false
     */
    public boolean isRemovePrefix() {
        return removePrefix;
    }
}
