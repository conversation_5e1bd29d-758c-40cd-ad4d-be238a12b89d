package cn.com.handthing.starter.crypto.service;

import cn.com.handthing.starter.crypto.core.KeyProvider;
import cn.com.handthing.starter.crypto.exception.CryptoException;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import javax.crypto.spec.SecretKeySpec;

import java.security.Key;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;

/**
 * AesEncryptor 的单元测试。
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
@DisplayName("AES Encryptor Service Tests")
class AesEncryptorTest {

    private static final String BEAN_NAME = "testAesEncryptor";
    private static final byte[] AES_KEY_BYTES = new byte[16]; // 128-bit key
    private static final Key AES_KEY = new SecretKeySpec(AES_KEY_BYTES, "AES");
    private static final String PLAINTEXT = "Hello, HandThing Crypto Starter!";

    @Mock
    private KeyProvider keyProvider;

    private MeterRegistry meterRegistry;
    private AesEncryptor aesEncryptor;

    @BeforeEach
    void setUp() throws Exception {
        // 使用一个真实的 MeterRegistry 以便测试指标
        meterRegistry = new SimpleMeterRegistry();

        // 模拟 KeyProvider 的行为
        when(keyProvider.getKeyBytes()).thenReturn(AES_KEY_BYTES);
        when(keyProvider.getKey()).thenReturn(AES_KEY);

        aesEncryptor = new AesEncryptor(BEAN_NAME, keyProvider, meterRegistry);
    }

    @Test
    @DisplayName("应能成功加密和解密数据")
    void shouldEncryptAndDecryptSuccessfully() {
        String encryptedText = aesEncryptor.encrypt(PLAINTEXT);
        assertThat(encryptedText).isNotNull().isNotEqualTo(PLAINTEXT);

        String decryptedText = aesEncryptor.decrypt(encryptedText);
        assertThat(decryptedText).isEqualTo(PLAINTEXT);
    }

    @Test
    @DisplayName("健康自检应能成功通过")
    void selfCheckShouldPass() {
        // selfCheck 方法不应抛出任何异常
        aesEncryptor.selfCheck();
    }

    @Test
    @DisplayName("当 KeyProvider 为 null 时构造函数应抛出异常")
    void constructorShouldThrowExceptionForNullKeyProvider() {
        assertThatThrownBy(() -> new AesEncryptor(BEAN_NAME, null, meterRegistry))
                .isInstanceOf(NullPointerException.class);
    }

    @Test
    @DisplayName("当初始化时 KeyProvider 抛出异常应能正确处理")
    void shouldHandleExceptionDuringInitialization() throws Exception {
        doThrow(new RuntimeException("Key loading failed")).when(keyProvider).checkReady();

        assertThatThrownBy(() -> new AesEncryptor(BEAN_NAME, keyProvider, meterRegistry))
                .isInstanceOf(CryptoException.class)
                .hasMessageContaining("Failed to initialize AES key");
    }

    @Test
    @DisplayName("加密和解密操作应记录性能指标")
    void operationsShouldRecordMetrics() {
        // 执行操作
        String encrypted = aesEncryptor.encrypt(PLAINTEXT);
        aesEncryptor.decrypt(encrypted);

        // 验证加密指标
        Timer encryptTimer = meterRegistry.find("crypto.operations.duration")
                .tag("processor", BEAN_NAME)
                .tag("operation", "encrypt")
                .timer();
        assertThat(encryptTimer).isNotNull();
        assertThat(encryptTimer.count()).isEqualTo(1);

        // 验证解密指标
        Timer decryptTimer = meterRegistry.find("crypto.operations.duration")
                .tag("processor", BEAN_NAME)
                .tag("operation", "decrypt")
                .timer();
        assertThat(decryptTimer).isNotNull();
        assertThat(decryptTimer.count()).isEqualTo(1);
    }
}