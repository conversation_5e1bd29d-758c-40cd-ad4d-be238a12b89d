package cn.com.handthing.starter.cache.level;

import cn.com.handthing.starter.cache.level.sync.CacheSyncService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cache.Cache;
import org.springframework.cache.support.SimpleValueWrapper;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * LevelCache 单元测试
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@ExtendWith(MockitoExtension.class)
class LevelCacheTest {

    @Mock
    private Cache l1Cache;

    @Mock
    private Cache l2Cache;

    @Mock
    private CacheSyncService syncService;

    private LevelCache levelCache;

    @BeforeEach
    void setUp() {
        levelCache = new LevelCache("testCache", l1Cache, l2Cache, syncService, true);
    }

    @Test
    void testGetName() {
        assertEquals("testCache", levelCache.getName());
    }

    @Test
    void testGetNativeCache() {
        assertEquals(levelCache, levelCache.getNativeCache());
    }

    @Test
    void testGet_L1Hit() {
        // Given
        String key = "testKey";
        String value = "testValue";
        SimpleValueWrapper wrapper = new SimpleValueWrapper(value);
        when(l1Cache.get(key)).thenReturn(wrapper);

        // When
        Cache.ValueWrapper result = levelCache.get(key);

        // Then
        assertNotNull(result);
        assertEquals(value, result.get());
        verify(l1Cache).get(key);
        verify(l2Cache, never()).get(key);
    }

    @Test
    void testGet_L1Miss_L2Hit() {
        // Given
        String key = "testKey";
        String value = "testValue";
        SimpleValueWrapper wrapper = new SimpleValueWrapper(value);
        when(l1Cache.get(key)).thenReturn(null);
        when(l2Cache.get(key)).thenReturn(wrapper);

        // When
        Cache.ValueWrapper result = levelCache.get(key);

        // Then
        assertNotNull(result);
        assertEquals(value, result.get());
        verify(l1Cache).get(key);
        verify(l2Cache).get(key);
        verify(l1Cache).put(key, value); // 验证回写到L1
    }

    @Test
    void testGet_BothMiss() {
        // Given
        String key = "testKey";
        when(l1Cache.get(key)).thenReturn(null);
        when(l2Cache.get(key)).thenReturn(null);

        // When
        Cache.ValueWrapper result = levelCache.get(key);

        // Then
        assertNull(result);
        verify(l1Cache).get(key);
        verify(l2Cache).get(key);
    }

    @Test
    void testPut() {
        // Given
        String key = "testKey";
        String value = "testValue";

        // When
        levelCache.put(key, value);

        // Then
        verify(l2Cache).put(key, value); // 先写L2
        verify(l1Cache).put(key, value); // 再写L1
        verify(syncService).sendPutMessage("testCache", key); // 发送同步消息
    }

    @Test
    void testEvict() {
        // Given
        String key = "testKey";

        // When
        levelCache.evict(key);

        // Then
        verify(l2Cache).evict(key); // 先删除L2
        verify(l1Cache).evict(key); // 再删除L1
        verify(syncService).sendEvictMessage("testCache", key); // 发送同步消息
    }

    @Test
    void testClear() {
        // When
        levelCache.clear();

        // Then
        verify(l2Cache).clear(); // 先清空L2
        verify(l1Cache).clear(); // 再清空L1
        verify(syncService).sendClearMessage("testCache"); // 发送同步消息
    }

    @Test
    void testHandleSyncMessage_Evict() {
        // Given
        String key = "testKey";

        // When
        levelCache.handleSyncMessage(key, "EVICT");

        // Then
        verify(l1Cache).evict(key); // 只操作L1缓存
        verify(l2Cache, never()).evict(key); // 不操作L2缓存
    }

    @Test
    void testHandleSyncMessage_Clear() {
        // When
        levelCache.handleSyncMessage(null, "CLEAR");

        // Then
        verify(l1Cache).clear(); // 只操作L1缓存
        verify(l2Cache, never()).clear(); // 不操作L2缓存
    }

    @Test
    void testHandleSyncMessage_Put() {
        // Given
        String key = "testKey";

        // When
        levelCache.handleSyncMessage(key, "PUT");

        // Then
        // PUT操作不需要处理，验证没有调用任何缓存操作
        verify(l1Cache, never()).put(anyString(), any());
        verify(l2Cache, never()).put(anyString(), any());
    }

    @Test
    void testGetL1Cache() {
        assertEquals(l1Cache, levelCache.getL1Cache());
    }

    @Test
    void testGetL2Cache() {
        assertEquals(l2Cache, levelCache.getL2Cache());
    }

    @Test
    void testSyncDisabled() {
        // Given
        LevelCache levelCacheNoSync = new LevelCache("testCache", l1Cache, l2Cache, syncService, false);
        String key = "testKey";
        String value = "testValue";

        // When
        levelCacheNoSync.put(key, value);

        // Then
        verify(l2Cache).put(key, value);
        verify(l1Cache).put(key, value);
        verify(syncService, never()).sendPutMessage(anyString(), anyString()); // 不发送同步消息
    }
}