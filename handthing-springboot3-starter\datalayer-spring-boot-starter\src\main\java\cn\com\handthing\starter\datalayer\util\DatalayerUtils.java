package cn.com.handthing.starter.datalayer.util;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 数据层工具类
 * <p>
 * 提供常用的数据操作工具方法，包括分页参数处理、查询条件构建等功能。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
public final class DatalayerUtils {
    
    private DatalayerUtils() {
        // 工具类，禁止实例化
    }
    
    // ========================================
    // 分页工具方法
    // ========================================
    
    /**
     * 创建分页对象
     *
     * @param current 当前页
     * @param size    每页大小
     * @param <T>     实体类型
     * @return 分页对象
     */
    public static <T> IPage<T> createPage(long current, long size) {
        return new Page<>(current, size);
    }
    
    /**
     * 创建分页对象（带最大限制）
     *
     * @param current 当前页
     * @param size    每页大小
     * @param maxSize 最大每页大小
     * @param <T>     实体类型
     * @return 分页对象
     */
    public static <T> IPage<T> createPage(long current, long size, long maxSize) {
        if (size > maxSize) {
            log.warn("Page size {} exceeds max size {}, using max size", size, maxSize);
            size = maxSize;
        }
        return new Page<>(current, size);
    }
    
    /**
     * 创建安全的分页对象
     * <p>
     * 自动处理非法的分页参数
     * </p>
     *
     * @param current     当前页
     * @param size        每页大小
     * @param defaultSize 默认每页大小
     * @param maxSize     最大每页大小
     * @param <T>         实体类型
     * @return 分页对象
     */
    public static <T> IPage<T> createSafePage(Long current, Long size, long defaultSize, long maxSize) {
        // 处理当前页
        if (current == null || current < 1) {
            current = 1L;
        }
        
        // 处理每页大小
        if (size == null || size < 1) {
            size = defaultSize;
        } else if (size > maxSize) {
            log.warn("Page size {} exceeds max size {}, using max size", size, maxSize);
            size = maxSize;
        }
        
        return new Page<>(current, size);
    }
    
    /**
     * 检查分页参数是否有效
     *
     * @param current 当前页
     * @param size    每页大小
     * @return 如果有效返回true，否则返回false
     */
    public static boolean isValidPageParams(Long current, Long size) {
        return current != null && current > 0 && size != null && size > 0;
    }
    
    // ========================================
    // 查询条件构建工具
    // ========================================
    
    /**
     * 创建查询包装器
     *
     * @param <T> 实体类型
     * @return 查询包装器
     */
    public static <T> QueryWrapper<T> createQueryWrapper() {
        return new QueryWrapper<>();
    }
    
    /**
     * 创建更新包装器
     *
     * @param <T> 实体类型
     * @return 更新包装器
     */
    public static <T> UpdateWrapper<T> createUpdateWrapper() {
        return new UpdateWrapper<>();
    }
    
    /**
     * 构建等值查询条件
     *
     * @param wrapper 查询包装器
     * @param column  字段名
     * @param value   值
     * @param <T>     实体类型
     * @return 查询包装器
     */
    public static <T> QueryWrapper<T> eq(QueryWrapper<T> wrapper, String column, Object value) {
        if (value != null) {
            wrapper.eq(column, value);
        }
        return wrapper;
    }
    
    /**
     * 构建模糊查询条件
     *
     * @param wrapper 查询包装器
     * @param column  字段名
     * @param value   值
     * @param <T>     实体类型
     * @return 查询包装器
     */
    public static <T> QueryWrapper<T> like(QueryWrapper<T> wrapper, String column, String value) {
        if (value != null && !value.trim().isEmpty()) {
            wrapper.like(column, value.trim());
        }
        return wrapper;
    }
    
    /**
     * 构建IN查询条件
     *
     * @param wrapper 查询包装器
     * @param column  字段名
     * @param values  值集合
     * @param <T>     实体类型
     * @return 查询包装器
     */
    public static <T> QueryWrapper<T> in(QueryWrapper<T> wrapper, String column, Collection<?> values) {
        if (values != null && !values.isEmpty()) {
            wrapper.in(column, values);
        }
        return wrapper;
    }
    
    /**
     * 构建时间范围查询条件
     *
     * @param wrapper   查询包装器
     * @param column    字段名
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param <T>       实体类型
     * @return 查询包装器
     */
    public static <T> QueryWrapper<T> between(QueryWrapper<T> wrapper, String column, 
                                             Object startTime, Object endTime) {
        if (startTime != null && endTime != null) {
            wrapper.between(column, startTime, endTime);
        } else if (startTime != null) {
            wrapper.ge(column, startTime);
        } else if (endTime != null) {
            wrapper.le(column, endTime);
        }
        return wrapper;
    }
    
    /**
     * 构建动态查询条件
     *
     * @param params 查询参数Map
     * @param <T>    实体类型
     * @return 查询包装器
     */
    public static <T> QueryWrapper<T> buildDynamicQuery(Map<String, Object> params) {
        QueryWrapper<T> wrapper = new QueryWrapper<>();
        
        if (params == null || params.isEmpty()) {
            return wrapper;
        }
        
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            
            if (value == null) {
                continue;
            }
            
            // 根据key的后缀决定查询类型
            if (key.endsWith("_like")) {
                String column = key.substring(0, key.length() - 5);
                like(wrapper, column, value.toString());
            } else if (key.endsWith("_in")) {
                String column = key.substring(0, key.length() - 3);
                if (value instanceof Collection) {
                    in(wrapper, column, (Collection<?>) value);
                }
            } else if (key.endsWith("_ge")) {
                String column = key.substring(0, key.length() - 3);
                wrapper.ge(column, value);
            } else if (key.endsWith("_le")) {
                String column = key.substring(0, key.length() - 3);
                wrapper.le(column, value);
            } else if (key.endsWith("_gt")) {
                String column = key.substring(0, key.length() - 3);
                wrapper.gt(column, value);
            } else if (key.endsWith("_lt")) {
                String column = key.substring(0, key.length() - 3);
                wrapper.lt(column, value);
            } else {
                // 默认等值查询
                eq(wrapper, key, value);
            }
        }
        
        return wrapper;
    }
    
    // ========================================
    // 数据验证工具
    // ========================================
    
    /**
     * 检查集合是否为空
     *
     * @param collection 集合
     * @return 如果为空返回true，否则返回false
     */
    public static boolean isEmpty(Collection<?> collection) {
        return collection == null || collection.isEmpty();
    }
    
    /**
     * 检查集合是否不为空
     *
     * @param collection 集合
     * @return 如果不为空返回true，否则返回false
     */
    public static boolean isNotEmpty(Collection<?> collection) {
        return !isEmpty(collection);
    }
    
    /**
     * 检查字符串是否为空
     *
     * @param str 字符串
     * @return 如果为空返回true，否则返回false
     */
    public static boolean isEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }
    
    /**
     * 检查字符串是否不为空
     *
     * @param str 字符串
     * @return 如果不为空返回true，否则返回false
     */
    public static boolean isNotEmpty(String str) {
        return !isEmpty(str);
    }
    
    // ========================================
    // 批处理工具
    // ========================================
    
    /**
     * 分批处理集合
     *
     * @param list      原始列表
     * @param batchSize 批次大小
     * @param processor 处理器
     * @param <T>       元素类型
     */
    public static <T> void processBatch(List<T> list, int batchSize, BatchProcessor<T> processor) {
        if (isEmpty(list) || processor == null) {
            return;
        }
        
        if (batchSize <= 0) {
            batchSize = 1000; // 默认批次大小
        }
        
        for (int i = 0; i < list.size(); i += batchSize) {
            int end = Math.min(i + batchSize, list.size());
            List<T> batch = list.subList(i, end);
            
            try {
                processor.process(batch, i / batchSize + 1);
            } catch (Exception e) {
                log.error("Error processing batch {}: {}", i / batchSize + 1, e.getMessage(), e);
                throw new RuntimeException("Batch processing failed", e);
            }
        }
    }
    
    /**
     * 批处理器接口
     */
    @FunctionalInterface
    public interface BatchProcessor<T> {
        /**
         * 处理批次
         *
         * @param batch     批次数据
         * @param batchNum  批次号（从1开始）
         */
        void process(List<T> batch, int batchNum);
    }
    
    // ========================================
    // 其他工具方法
    // ========================================
    
    /**
     * 安全转换为Long类型
     *
     * @param value 值
     * @return Long值，如果转换失败返回null
     */
    public static Long safeLong(Object value) {
        if (value == null) {
            return null;
        }
        
        if (value instanceof Long) {
            return (Long) value;
        }
        
        if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        
        try {
            return Long.valueOf(value.toString());
        } catch (NumberFormatException e) {
            log.debug("Failed to convert {} to Long", value);
            return null;
        }
    }
    
    /**
     * 安全转换为Integer类型
     *
     * @param value 值
     * @return Integer值，如果转换失败返回null
     */
    public static Integer safeInteger(Object value) {
        if (value == null) {
            return null;
        }
        
        if (value instanceof Integer) {
            return (Integer) value;
        }
        
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        
        try {
            return Integer.valueOf(value.toString());
        } catch (NumberFormatException e) {
            log.debug("Failed to convert {} to Integer", value);
            return null;
        }
    }
    
    /**
     * 获取分页摘要信息
     *
     * @param page 分页对象
     * @return 分页摘要字符串
     */
    public static String getPageSummary(IPage<?> page) {
        if (page == null) {
            return "Page{null}";
        }
        
        return String.format("Page{current=%d, size=%d, total=%d, pages=%d, records=%d}", 
                page.getCurrent(), page.getSize(), page.getTotal(), page.getPages(), page.getRecords().size());
    }
}
