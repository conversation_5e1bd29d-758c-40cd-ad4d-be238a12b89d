package cn.com.handthing.starter.connector.wecom;

import cn.com.handthing.starter.connector.PlatformType;
import cn.com.handthing.starter.connector.auth.AuthProvider;
import cn.com.handthing.starter.connector.auth.UnifiedAccessToken;
import cn.com.handthing.starter.connector.auth.UnifiedUserInfo;
import cn.com.handthing.starter.connector.exception.AuthException;
import cn.com.handthing.starter.connector.wecom.config.WeComConfig;
import cn.com.handthing.starter.connector.wecom.model.WeComAccessTokenResponse;
import cn.com.handthing.starter.connector.wecom.model.WeComUserInfoResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 企业微信认证提供者
 * <p>
 * 实现企业微信OAuth2.0认证流程，包括授权URL生成、Token交换、用户信息获取等功能。
 * 支持企业微信网页授权和扫码登录。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "handthing.connector.wecom", name = "enabled", havingValue = "true")
public class WeComAuthProvider implements AuthProvider {

    private final WeComConfig config;
    private final RestTemplate restTemplate;

    // 企业微信API端点
    private static final String AUTH_URL = "https://open.weixin.qq.com/connect/oauth2/authorize";
    private static final String TOKEN_URL = "https://qyapi.weixin.qq.com/cgi-bin/gettoken";
    private static final String USER_INFO_URL = "https://qyapi.weixin.qq.com/cgi-bin/user/getuserinfo";
    private static final String USER_DETAIL_URL = "https://qyapi.weixin.qq.com/cgi-bin/user/get";

    @Override
    public PlatformType getPlatformType() {
        return PlatformType.WECOM;
    }

    @Override
    public String getAuthorizationUrl(String state, String redirectUri) throws AuthException {
        try {
            String effectiveRedirectUri = config.getEffectiveRedirectUri(redirectUri);
            String effectiveScope = config.getEffectiveScope("snsapi_base");
            
            return UriComponentsBuilder.fromHttpUrl(AUTH_URL)
                    .queryParam("appid", config.getCorpId())
                    .queryParam("redirect_uri", URLEncoder.encode(effectiveRedirectUri, StandardCharsets.UTF_8))
                    .queryParam("response_type", "code")
                    .queryParam("scope", effectiveScope)
                    .queryParam("agentid", config.getAgentId())
                    .queryParam("state", state != null ? state : "")
                    .fragment("wechat_redirect")
                    .build()
                    .toUriString();
                    
        } catch (Exception e) {
            log.error("Failed to generate WeCom authorization URL", e);
            throw AuthException.configurationError(PlatformType.WECOM, "Failed to generate authorization URL: " + e.getMessage());
        }
    }

    @Override
    public UnifiedAccessToken exchangeToken(String code, String state, String redirectUri) throws AuthException {
        try {
            // 1. 获取企业访问令牌
            String accessToken = getCorpAccessToken();
            
            // 2. 使用code获取用户信息
            WeComUserInfoResponse userInfo = getUserInfoByCode(accessToken, code);
            
            // 3. 构建统一访问令牌
            return UnifiedAccessToken.builder()
                    .platform(PlatformType.WECOM)
                    .accessToken(accessToken)
                    .tokenType("Bearer")
                    .expiresIn(7200L) // 企业微信访问令牌有效期2小时
                    .userId(userInfo.getUserId())
                    .scope("snsapi_base")
                    .createdAt(LocalDateTime.now())
                    .expiresAt(LocalDateTime.now().plusSeconds(7200))
                    .extraInfo(Map.of(
                            "deviceId", userInfo.getDeviceId() != null ? userInfo.getDeviceId() : "",
                            "openId", userInfo.getOpenId() != null ? userInfo.getOpenId() : ""
                    ))
                    .build();
                    
        } catch (Exception e) {
            log.error("Failed to exchange WeCom token for code: {}", code, e);
            if (e instanceof AuthException) {
                throw e;
            }
            throw AuthException.networkError(PlatformType.WECOM, e);
        }
    }

    @Override
    public UnifiedAccessToken refreshToken(String refreshToken) throws AuthException {
        // 企业微信不支持刷新令牌，需要重新获取
        throw new AuthException("WeCom does not support refresh token", 
                AuthException.REFRESH_TOKEN_EXPIRED, PlatformType.WECOM);
    }

    @Override
    public UnifiedUserInfo getUserInfo(String accessToken) throws AuthException {
        try {
            // 从accessToken的extraInfo中获取userId
            // 这里简化处理，实际应该从Token中解析userId
            String userId = extractUserIdFromToken(accessToken);
            
            // 获取用户详细信息
            WeComUserInfoResponse userDetail = getUserDetail(accessToken, userId);
            
            return UnifiedUserInfo.builder()
                    .platform(PlatformType.WECOM)
                    .userId(userDetail.getUserId())
                    .openId(userDetail.getOpenId())
                    .nickname(userDetail.getName())
                    .realName(userDetail.getName())
                    .avatar(userDetail.getAvatar())
                    .email(userDetail.getEmail())
                    .mobile(userDetail.getMobile())
                    .gender(parseGender(userDetail.getGender()))
                    .status(userDetail.getStatus() == 1 ? "active" : "inactive")
                    .fetchedAt(LocalDateTime.now())
                    .extraInfo(Map.of(
                            "department", userDetail.getDepartment() != null ? userDetail.getDepartment() : new int[0],
                            "position", userDetail.getPosition() != null ? userDetail.getPosition() : "",
                            "isLeader", userDetail.getIsLeaderInDept() != null ? userDetail.getIsLeaderInDept() : new int[0]
                    ))
                    .build();
                    
        } catch (Exception e) {
            log.error("Failed to get WeCom user info", e);
            if (e instanceof AuthException) {
                throw e;
            }
            throw AuthException.networkError(PlatformType.WECOM, e);
        }
    }

    @Override
    public boolean supportsRefreshToken() {
        return false; // 企业微信不支持刷新令牌
    }

    @Override
    public String getDefaultScope() {
        return "snsapi_base";
    }

    /**
     * 获取企业访问令牌
     */
    private String getCorpAccessToken() throws AuthException {
        try {
            String url = UriComponentsBuilder.fromHttpUrl(TOKEN_URL)
                    .queryParam("corpid", config.getCorpId())
                    .queryParam("corpsecret", config.getSecret())
                    .build()
                    .toUriString();
                    
            ResponseEntity<WeComAccessTokenResponse> response = restTemplate.getForEntity(url, WeComAccessTokenResponse.class);
            WeComAccessTokenResponse tokenResponse = response.getBody();
            
            if (tokenResponse == null || tokenResponse.getErrcode() != 0) {
                throw new AuthException("Failed to get corp access token: " + 
                        (tokenResponse != null ? tokenResponse.getErrmsg() : "No response"),
                        AuthException.CONFIGURATION_ERROR, PlatformType.WECOM);
            }
            
            return tokenResponse.getAccessToken();
            
        } catch (Exception e) {
            log.error("Failed to get WeCom corp access token", e);
            throw AuthException.networkError(PlatformType.WECOM, e);
        }
    }

    /**
     * 通过code获取用户信息
     */
    private WeComUserInfoResponse getUserInfoByCode(String accessToken, String code) throws AuthException {
        try {
            String url = UriComponentsBuilder.fromHttpUrl(USER_INFO_URL)
                    .queryParam("access_token", accessToken)
                    .queryParam("code", code)
                    .build()
                    .toUriString();
                    
            ResponseEntity<WeComUserInfoResponse> response = restTemplate.getForEntity(url, WeComUserInfoResponse.class);
            WeComUserInfoResponse userInfo = response.getBody();
            
            if (userInfo == null || userInfo.getErrcode() != 0) {
                throw new AuthException("Failed to get user info by code: " + 
                        (userInfo != null ? userInfo.getErrmsg() : "No response"),
                        AuthException.INVALID_CODE, PlatformType.WECOM);
            }
            
            return userInfo;
            
        } catch (Exception e) {
            log.error("Failed to get WeCom user info by code: {}", code, e);
            throw AuthException.networkError(PlatformType.WECOM, e);
        }
    }

    /**
     * 获取用户详细信息
     */
    private WeComUserInfoResponse getUserDetail(String accessToken, String userId) throws AuthException {
        try {
            String url = UriComponentsBuilder.fromHttpUrl(USER_DETAIL_URL)
                    .queryParam("access_token", accessToken)
                    .queryParam("userid", userId)
                    .build()
                    .toUriString();
                    
            ResponseEntity<WeComUserInfoResponse> response = restTemplate.getForEntity(url, WeComUserInfoResponse.class);
            WeComUserInfoResponse userDetail = response.getBody();
            
            if (userDetail == null || userDetail.getErrcode() != 0) {
                throw new AuthException("Failed to get user detail: " + 
                        (userDetail != null ? userDetail.getErrmsg() : "No response"),
                        AuthException.INVALID_TOKEN, PlatformType.WECOM);
            }
            
            return userDetail;
            
        } catch (Exception e) {
            log.error("Failed to get WeCom user detail for userId: {}", userId, e);
            throw AuthException.networkError(PlatformType.WECOM, e);
        }
    }

    /**
     * 从访问令牌中提取用户ID（简化实现）
     */
    private String extractUserIdFromToken(String accessToken) {
        // 这里应该从Token的extraInfo中获取userId
        // 简化实现，实际应该有更复杂的逻辑
        return "default_user";
    }

    /**
     * 解析性别
     */
    private Integer parseGender(String gender) {
        if (gender == null) return 0;
        switch (gender) {
            case "1": return 1; // 男
            case "2": return 2; // 女
            default: return 0;  // 未知
        }
    }
}
