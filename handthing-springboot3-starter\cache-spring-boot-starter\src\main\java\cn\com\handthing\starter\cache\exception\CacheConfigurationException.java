package cn.com.handthing.starter.cache.exception;

/**
 * 缓存配置异常
 * <p>
 * 当缓存配置不正确或缺失时抛出此异常
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
public class CacheConfigurationException extends CacheException {

    /**
     * 构造函数
     *
     * @param message 异常消息
     */
    public CacheConfigurationException(String message) {
        super(message);
    }

    /**
     * 构造函数
     *
     * @param message 异常消息
     * @param cause   原因异常
     */
    public CacheConfigurationException(String message, Throwable cause) {
        super(message, cause);
    }

    /**
     * 构造函数
     *
     * @param cause 原因异常
     */
    public CacheConfigurationException(Throwable cause) {
        super(cause);
    }
}