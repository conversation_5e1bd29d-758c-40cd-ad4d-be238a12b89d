package cn.com.handthing.starter.auth;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;

import java.util.Map;

/**
 * 认证健康检查
 * <p>
 * 提供认证系统的健康状态检查，用于监控和运维。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@RequiredArgsConstructor
@ConditionalOnClass(name = "org.springframework.boot.actuator.health.HealthIndicator")
public class AuthenticationHealthIndicator {

    private final ProviderRegistry providerRegistry;
    private final AuthProperties authProperties;

    /**
     * 获取健康状态
     *
     * @return 健康状态Map
     */
    public Map<String, Object> health() {
        try {
            Map<String, Object> health = new java.util.HashMap<>();

            // 检查认证框架是否启用
            if (!authProperties.isEnabled()) {
                health.put("status", "DOWN");
                health.put("reason", "Authentication framework is disabled");
                return health;
            }

            // 检查提供者注册表
            int totalProviders = providerRegistry.getProviderCount();
            int availableProviders = providerRegistry.getAvailableProviderCount();

            if (totalProviders == 0) {
                health.put("status", "DOWN");
                health.put("reason", "No authentication providers registered");
                return health;
            }

            if (availableProviders == 0) {
                health.put("status", "DOWN");
                health.put("reason", "No authentication providers available");
                health.put("totalProviders", totalProviders);
                return health;
            }

            // 构建健康状态详情
            health.put("status", "UP");
            health.put("totalProviders", totalProviders);
            health.put("availableProviders", availableProviders);
            health.put("supportedGrantTypes", providerRegistry.getSupportedGrantTypes());
            health.put("providerNames", providerRegistry.getProviderNames());
            health.put("cacheEnabled", authProperties.isCacheEnabled());
            health.put("eventsEnabled", authProperties.isEventsEnabled());
            health.put("metricsEnabled", authProperties.isMetricsEnabled());

            // 如果有部分提供者不可用，标记为警告状态
            if (availableProviders < totalProviders) {
                health.put("status", "WARNING");
                health.put("warning", "Some authentication providers are not available");
            }

            return health;

        } catch (Exception e) {
            log.error("Authentication health check failed", e);
            Map<String, Object> health = new java.util.HashMap<>();
            health.put("status", "DOWN");
            health.put("reason", "Health check failed");
            health.put("error", e.getMessage());
            return health;
        }
    }
}
