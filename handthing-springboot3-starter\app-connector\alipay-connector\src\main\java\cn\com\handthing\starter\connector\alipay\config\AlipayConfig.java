package cn.com.handthing.starter.connector.alipay.config;

import cn.com.handthing.starter.connector.config.PlatformConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import jakarta.validation.constraints.NotBlank;

/**
 * 支付宝配置类
 * <p>
 * 继承PlatformConfig，包含支付宝特有的配置项，
 * 如应用ID、私钥、公钥等。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ConfigurationProperties(prefix = "handthing.connector.alipay")
public class AlipayConfig extends PlatformConfig {

    /**
     * 应用ID（AppId）
     */
    @NotBlank(message = "Alipay app ID cannot be blank")
    private String appId;

    /**
     * 应用私钥
     */
    @NotBlank(message = "Alipay private key cannot be blank")
    private String privateKey;

    /**
     * 支付宝公钥
     */
    @NotBlank(message = "Alipay public key cannot be blank")
    private String alipayPublicKey;

    /**
     * 签名类型（RSA2、RSA）
     */
    private String signType = "RSA2";

    /**
     * 支付宝网关URL
     */
    private String gatewayUrl = "https://openapi.alipay.com/gateway.do";

    /**
     * 支付宝授权URL
     */
    private String authUrl = "https://openauth.alipay.com/oauth2/publicAppAuthorize.htm";

    /**
     * 是否为沙箱环境
     */
    private boolean sandbox = false;

    /**
     * 沙箱网关URL
     */
    private String sandboxGatewayUrl = "https://openapi.alipaydev.com/gateway.do";

    /**
     * 沙箱授权URL
     */
    private String sandboxAuthUrl = "https://openauth.alipaydev.com/oauth2/publicAppAuthorize.htm";

    /**
     * 应用证书路径（可选，用于证书模式）
     */
    private String appCertPath;

    /**
     * 支付宝根证书路径（可选，用于证书模式）
     */
    private String alipayRootCertPath;

    /**
     * 支付宝公钥证书路径（可选，用于证书模式）
     */
    private String alipayCertPath;

    /**
     * 是否启用证书模式
     */
    private boolean enableCert = false;

    /**
     * 异步通知URL
     */
    private String notifyUrl;

    /**
     * 是否启用异步通知
     */
    private boolean enableNotify = false;

    @Override
    public boolean isValid() {
        // 支付宝特有的验证逻辑
        boolean baseValid = isEnabled() && 
                appId != null && !appId.trim().isEmpty() &&
                privateKey != null && !privateKey.trim().isEmpty() &&
                alipayPublicKey != null && !alipayPublicKey.trim().isEmpty();

        if (!baseValid) {
            return false;
        }

        // 如果启用证书模式，需要验证证书相关配置
        if (enableCert) {
            return appCertPath != null && !appCertPath.trim().isEmpty() &&
                   alipayRootCertPath != null && !alipayRootCertPath.trim().isEmpty() &&
                   alipayCertPath != null && !alipayCertPath.trim().isEmpty();
        }

        return true;
    }

    /**
     * 获取有效的网关URL
     *
     * @return 网关URL
     */
    public String getEffectiveGatewayUrl() {
        return sandbox ? sandboxGatewayUrl : gatewayUrl;
    }

    /**
     * 获取有效的授权URL
     *
     * @return 授权URL
     */
    public String getEffectiveAuthUrl() {
        return sandbox ? sandboxAuthUrl : authUrl;
    }

    /**
     * 检查是否为沙箱模式
     *
     * @return 如果是沙箱模式返回true，否则返回false
     */
    public boolean isSandboxMode() {
        return sandbox;
    }

    /**
     * 检查是否启用证书模式
     *
     * @return 如果启用证书模式返回true，否则返回false
     */
    public boolean isCertMode() {
        return enableCert && 
               appCertPath != null && !appCertPath.trim().isEmpty() &&
               alipayRootCertPath != null && !alipayRootCertPath.trim().isEmpty() &&
               alipayCertPath != null && !alipayCertPath.trim().isEmpty();
    }

    /**
     * 检查是否启用异步通知
     *
     * @return 如果启用异步通知返回true，否则返回false
     */
    public boolean isNotifyEnabled() {
        return enableNotify && notifyUrl != null && !notifyUrl.trim().isEmpty();
    }

    /**
     * 获取支付相关URL前缀
     *
     * @return 支付URL前缀
     */
    public String getPaymentUrlPrefix() {
        return getEffectiveGatewayUrl();
    }

    /**
     * 获取用户信息URL
     *
     * @return 用户信息URL
     */
    public String getUserInfoUrl() {
        return getEffectiveGatewayUrl() + "?method=alipay.user.info.share";
    }

    /**
     * 获取Token URL
     *
     * @return Token URL
     */
    public String getTokenUrl() {
        return getEffectiveGatewayUrl() + "?method=alipay.system.oauth.token";
    }

    /**
     * 获取小程序相关URL前缀
     *
     * @return 小程序URL前缀
     */
    public String getMiniProgramUrlPrefix() {
        return getEffectiveGatewayUrl() + "?method=alipay.open.mini";
    }

    /**
     * 获取生活号相关URL前缀
     *
     * @return 生活号URL前缀
     */
    public String getLifeServiceUrlPrefix() {
        return getEffectiveGatewayUrl() + "?method=alipay.open.public";
    }

    /**
     * 获取营销相关URL前缀
     *
     * @return 营销URL前缀
     */
    public String getMarketingUrlPrefix() {
        return getEffectiveGatewayUrl() + "?method=alipay.marketing";
    }

    /**
     * 获取芝麻信用相关URL前缀
     *
     * @return 芝麻信用URL前缀
     */
    public String getZhimaCreditUrlPrefix() {
        return getEffectiveGatewayUrl() + "?method=zhima.credit";
    }

    /**
     * 获取花呗分期相关URL前缀
     *
     * @return 花呗分期URL前缀
     */
    public String getHuabeiUrlPrefix() {
        return getEffectiveGatewayUrl() + "?method=alipay.pcredit";
    }
}
