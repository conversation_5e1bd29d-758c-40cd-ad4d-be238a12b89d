package cn.com.handthing.starter.auth.thirdparty;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * 默认企业微信API服务实现
 * <p>
 * 提供企业微信API调用的默认实现，包括获取访问令牌、用户信息等功能。
 * 使用WebClient进行HTTP调用，支持超时和错误处理。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnMissingBean(WecomApiService.class)
public class DefaultWecomApiService implements WecomApiService {

    private final WebClient webClient;
    private final ObjectMapper objectMapper;

    /**
     * 企业微信API基础URL
     */
    private static final String WECOM_API_BASE_URL = "https://qyapi.weixin.qq.com";

    /**
     * 获取访问令牌URL
     */
    private static final String GET_TOKEN_URL = WECOM_API_BASE_URL + "/cgi-bin/gettoken";

    /**
     * 获取用户信息URL
     */
    private static final String GET_USER_INFO_URL = WECOM_API_BASE_URL + "/cgi-bin/user/getuserinfo";

    /**
     * 获取用户详细信息URL
     */
    private static final String GET_USER_DETAIL_URL = WECOM_API_BASE_URL + "/cgi-bin/user/get";

    /**
     * 默认构造函数
     */
    public DefaultWecomApiService() {
        this.webClient = WebClient.builder()
                .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(1024 * 1024))
                .build();
        this.objectMapper = new ObjectMapper();
    }

    @Override
    public WecomTokenResult getAccessToken(String corpId, String corpSecret) {
        try {
            log.debug("Getting access token for corpId: {}", corpId);

            String response = webClient.get()
                    .uri(GET_TOKEN_URL + "?corpid={corpId}&corpsecret={corpSecret}", corpId, corpSecret)
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(10))
                    .block();

            JsonNode jsonNode = objectMapper.readTree(response);
            int errcode = jsonNode.get("errcode").asInt();

            if (errcode == 0) {
                String accessToken = jsonNode.get("access_token").asText();
                long expiresIn = jsonNode.get("expires_in").asLong();
                
                log.debug("Access token obtained successfully for corpId: {}", corpId);
                return WecomTokenResult.success(accessToken, expiresIn);
            } else {
                String errmsg = jsonNode.get("errmsg").asText();
                log.warn("Failed to get access token for corpId: {}, errcode: {}, errmsg: {}", corpId, errcode, errmsg);
                return WecomTokenResult.failure(String.valueOf(errcode), errmsg);
            }

        } catch (Exception e) {
            log.error("Error getting access token for corpId: {}", corpId, e);
            return WecomTokenResult.failure("NETWORK_ERROR", "网络请求失败: " + e.getMessage());
        }
    }

    @Override
    public WecomUserResult getUserInfoByCode(String accessToken, String code) {
        try {
            log.debug("Getting user info by code");

            String response = webClient.get()
                    .uri(GET_USER_INFO_URL + "?access_token={accessToken}&code={code}", accessToken, code)
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(10))
                    .block();

            JsonNode jsonNode = objectMapper.readTree(response);
            int errcode = jsonNode.get("errcode").asInt();

            if (errcode == 0) {
                String userId = jsonNode.has("UserId") ? jsonNode.get("UserId").asText() : null;
                String deviceId = jsonNode.has("DeviceId") ? jsonNode.get("DeviceId").asText() : null;
                String userType = jsonNode.has("user_type") ? jsonNode.get("user_type").asText() : "member";

                log.debug("User info obtained successfully, userId: {}", userId);
                return WecomUserResult.success(userId, deviceId, userType);
            } else {
                String errmsg = jsonNode.get("errmsg").asText();
                log.warn("Failed to get user info by code, errcode: {}, errmsg: {}", errcode, errmsg);
                return WecomUserResult.failure(String.valueOf(errcode), errmsg);
            }

        } catch (Exception e) {
            log.error("Error getting user info by code", e);
            return WecomUserResult.failure("NETWORK_ERROR", "网络请求失败: " + e.getMessage());
        }
    }

    @Override
    public WecomUserDetailResult getUserDetail(String accessToken, String userId) {
        try {
            log.debug("Getting user detail for userId: {}", userId);

            String response = webClient.get()
                    .uri(GET_USER_DETAIL_URL + "?access_token={accessToken}&userid={userId}", accessToken, userId)
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(10))
                    .block();

            JsonNode jsonNode = objectMapper.readTree(response);
            int errcode = jsonNode.get("errcode").asInt();

            if (errcode == 0) {
                WecomUserDetailResult result = WecomUserDetailResult.success();
                
                result.setUserId(getTextValue(jsonNode, "userid"));
                result.setName(getTextValue(jsonNode, "name"));
                result.setMobile(getTextValue(jsonNode, "mobile"));
                result.setGender(getTextValue(jsonNode, "gender"));
                result.setEmail(getTextValue(jsonNode, "email"));
                result.setAvatar(getTextValue(jsonNode, "avatar"));
                result.setStatus(getTextValue(jsonNode, "status"));
                result.setPosition(getTextValue(jsonNode, "position"));

                // 处理部门信息
                if (jsonNode.has("department")) {
                    JsonNode deptNode = jsonNode.get("department");
                    if (deptNode.isArray()) {
                        String[] departments = new String[deptNode.size()];
                        for (int i = 0; i < deptNode.size(); i++) {
                            departments[i] = deptNode.get(i).asText();
                        }
                        result.setDepartment(departments);
                    }
                }

                // 处理扩展属性
                if (jsonNode.has("extattr")) {
                    JsonNode extNode = jsonNode.get("extattr");
                    Map<String, Object> extattr = objectMapper.convertValue(extNode, Map.class);
                    result.setExtattr(extattr);
                }

                log.debug("User detail obtained successfully for userId: {}", userId);
                return result;
            } else {
                String errmsg = jsonNode.get("errmsg").asText();
                log.warn("Failed to get user detail for userId: {}, errcode: {}, errmsg: {}", userId, errcode, errmsg);
                return WecomUserDetailResult.failure(String.valueOf(errcode), errmsg);
            }

        } catch (Exception e) {
            log.error("Error getting user detail for userId: {}", userId, e);
            return WecomUserDetailResult.failure("NETWORK_ERROR", "网络请求失败: " + e.getMessage());
        }
    }

    @Override
    public boolean validateConfig(String corpId, String corpSecret, String agentId) {
        try {
            log.debug("Validating wecom config for corpId: {}, agentId: {}", corpId, agentId);

            WecomTokenResult tokenResult = getAccessToken(corpId, corpSecret);
            if (!tokenResult.isSuccess()) {
                log.warn("Invalid wecom config: failed to get access token");
                return false;
            }

            // 可以进一步验证应用信息
            // 这里简化处理，只验证能否获取到访问令牌
            log.debug("Wecom config validation successful");
            return true;

        } catch (Exception e) {
            log.error("Error validating wecom config", e);
            return false;
        }
    }

    /**
     * 安全地获取JSON节点的文本值
     *
     * @param jsonNode JSON节点
     * @param fieldName 字段名
     * @return 文本值，如果不存在返回null
     */
    private String getTextValue(JsonNode jsonNode, String fieldName) {
        return jsonNode.has(fieldName) ? jsonNode.get(fieldName).asText() : null;
    }

    /**
     * 创建模拟的企业微信API服务（用于测试）
     *
     * @return 模拟的API服务
     */
    public static WecomApiService createMockService() {
        return new WecomApiService() {
            @Override
            public WecomTokenResult getAccessToken(String corpId, String corpSecret) {
                // 模拟成功响应
                if ("test_corp_id".equals(corpId) && "test_corp_secret".equals(corpSecret)) {
                    return WecomTokenResult.success("mock_access_token_" + System.currentTimeMillis(), 7200L);
                }
                return WecomTokenResult.failure("40013", "invalid corpid");
            }

            @Override
            public WecomUserResult getUserInfoByCode(String accessToken, String code) {
                // 模拟成功响应
                if (accessToken.startsWith("mock_access_token") && "test_code".equals(code)) {
                    return WecomUserResult.success("test_user_id", "test_device_id", "member");
                }
                return WecomUserResult.failure("40029", "invalid code");
            }

            @Override
            public WecomUserDetailResult getUserDetail(String accessToken, String userId) {
                // 模拟成功响应
                if (accessToken.startsWith("mock_access_token") && "test_user_id".equals(userId)) {
                    WecomUserDetailResult result = WecomUserDetailResult.success();
                    result.setUserId("test_user_id");
                    result.setName("测试用户");
                    result.setMobile("13800138000");
                    result.setEmail("<EMAIL>");
                    result.setGender("1");
                    result.setAvatar("http://example.com/avatar.jpg");
                    result.setStatus("1");
                    result.setPosition("测试工程师");
                    result.setDepartment(new String[]{"1", "2"});
                    
                    Map<String, Object> extattr = new HashMap<>();
                    extattr.put("工号", "T001");
                    result.setExtattr(extattr);
                    
                    return result;
                }
                return WecomUserDetailResult.failure("60111", "userid not found");
            }

            @Override
            public boolean validateConfig(String corpId, String corpSecret, String agentId) {
                return "test_corp_id".equals(corpId) && "test_corp_secret".equals(corpSecret);
            }
        };
    }
}
