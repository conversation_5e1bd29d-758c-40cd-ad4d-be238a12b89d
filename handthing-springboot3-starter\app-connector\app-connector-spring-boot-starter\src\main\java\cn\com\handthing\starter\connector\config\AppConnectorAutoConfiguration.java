package cn.com.handthing.starter.connector.config;

import cn.com.handthing.starter.cache.CacheService;
import cn.com.handthing.starter.connector.auth.AuthService;
import cn.com.handthing.starter.connector.auth.DefaultAuthService;
import cn.com.handthing.starter.connector.token.TokenManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.web.client.RestTemplate;

/**
 * App Connector 自动配置类
 * <p>
 * 根据配置条件装配相应的Bean，注册AuthService、TokenManager等核心服务，
 * 支持按需装配平台连接器，依赖CacheService进行Token管理。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@AutoConfiguration
@EnableConfigurationProperties(AppConnectorProperties.class)
@ConditionalOnProperty(prefix = "handthing.connector", name = "enabled", havingValue = "true", matchIfMissing = true)
@ConditionalOnClass(CacheService.class)
@ComponentScan(basePackages = "cn.com.handthing.starter.connector")
public class AppConnectorAutoConfiguration {

    /**
     * 创建RestTemplate Bean（如果不存在）
     *
     * @return RestTemplate实例
     */
    @Bean
    @ConditionalOnMissingBean(RestTemplate.class)
    public RestTemplate restTemplate() {
        log.info("Creating RestTemplate for App Connector");
        return new RestTemplate();
    }

    /**
     * 创建默认认证服务
     *
     * @return 认证服务实例
     */
    @Bean
    @ConditionalOnMissingBean(AuthService.class)
    public AuthService authService() {
        log.info("Creating default AuthService");
        return new DefaultAuthService();
    }

    /**
     * 创建Token管理器
     *
     * @param cacheService 缓存服务
     * @return Token管理器实例
     */
    @Bean
    @ConditionalOnMissingBean(TokenManager.class)
    public TokenManager tokenManager(CacheService cacheService) {
        String tokenCacheName = "app-connector-tokens";
        log.info("Creating TokenManager with cache service: {}, token cache name: {}",
                cacheService.getClass().getSimpleName(), tokenCacheName);
        return new TokenManager(cacheService, tokenCacheName);
    }

    /**
     * 配置信息Bean
     *
     * @param properties 配置属性
     * @return 配置信息
     */
    @Bean
    public AppConnectorConfigInfo appConnectorConfigInfo(AppConnectorProperties properties) {
        AppConnectorConfigInfo configInfo = new AppConnectorConfigInfo();
        configInfo.setEnabled(properties.isEnabled());
        configInfo.setRedirectUriPrefix(properties.getRedirectUriPrefix());
        configInfo.setTokenCacheName(properties.getTokenCacheName());
        configInfo.setEnableUnifiedCallback(properties.isEnableUnifiedCallback());
        configInfo.setCallbackPathPrefix(properties.getCallbackPathPrefix());
        configInfo.setEnableEventPublishing(properties.isEnableEventPublishing());
        configInfo.setEnabledPlatforms(properties.getEnabledPlatformConfigs().keySet());
        
        log.info("App Connector configuration: enabled={}, platforms={}, callback={}", 
                configInfo.isEnabled(), 
                configInfo.getEnabledPlatforms(),
                configInfo.isEnableUnifiedCallback());
        
        return configInfo;
    }

    /**
     * 配置信息类
     */
    public static class AppConnectorConfigInfo {
        private boolean enabled;
        private String redirectUriPrefix;
        private String tokenCacheName;
        private boolean enableUnifiedCallback;
        private String callbackPathPrefix;
        private boolean enableEventPublishing;
        private java.util.Set<String> enabledPlatforms;

        // Getters and Setters
        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public String getRedirectUriPrefix() {
            return redirectUriPrefix;
        }

        public void setRedirectUriPrefix(String redirectUriPrefix) {
            this.redirectUriPrefix = redirectUriPrefix;
        }

        public String getTokenCacheName() {
            return tokenCacheName;
        }

        public void setTokenCacheName(String tokenCacheName) {
            this.tokenCacheName = tokenCacheName;
        }

        public boolean isEnableUnifiedCallback() {
            return enableUnifiedCallback;
        }

        public void setEnableUnifiedCallback(boolean enableUnifiedCallback) {
            this.enableUnifiedCallback = enableUnifiedCallback;
        }

        public String getCallbackPathPrefix() {
            return callbackPathPrefix;
        }

        public void setCallbackPathPrefix(String callbackPathPrefix) {
            this.callbackPathPrefix = callbackPathPrefix;
        }

        public boolean isEnableEventPublishing() {
            return enableEventPublishing;
        }

        public void setEnableEventPublishing(boolean enableEventPublishing) {
            this.enableEventPublishing = enableEventPublishing;
        }

        public java.util.Set<String> getEnabledPlatforms() {
            return enabledPlatforms;
        }

        public void setEnabledPlatforms(java.util.Set<String> enabledPlatforms) {
            this.enabledPlatforms = enabledPlatforms;
        }
    }
}
