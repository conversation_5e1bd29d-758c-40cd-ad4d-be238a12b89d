package cn.com.handthing.starter.auth.thirdparty;

import cn.com.handthing.starter.auth.core.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * 飞书认证提供者
 * <p>
 * 实现基于飞书OAuth2的认证逻辑，支持网页授权、移动端授权和小程序授权。
 * 提供完整的飞书认证流程，包括授权码验证、用户信息获取、自动注册等。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class FeishuAuthenticationProvider implements AuthenticationProvider {

    private final FeishuUserService feishuUserService;
    private final FeishuApiService feishuApiService;
    private final JwtTokenProvider jwtTokenProvider;

    @Override
    public GrantType getSupportedGrantType() {
        return GrantType.FEISHU;
    }

    @Override
    public boolean supports(AuthenticationRequest request) {
        return request instanceof FeishuAuthenticationRequest && 
               request.getGrantType() == GrantType.FEISHU;
    }

    @Override
    public AuthenticationResponse authenticate(AuthenticationRequest request) throws AuthenticationException {
        log.debug("Starting Feishu authentication for request: {}", request);

        if (!(request instanceof FeishuAuthenticationRequest)) {
            throw new AuthenticationException("INVALID_REQUEST", "Invalid authentication request type");
        }

        FeishuAuthenticationRequest feishuRequest = (FeishuAuthenticationRequest) request;

        try {
            // 预处理
            preAuthenticate(feishuRequest);

            // 获取应用访问令牌
            FeishuApiService.FeishuAppTokenResult appTokenResult = feishuApiService.getAppAccessToken(
                    feishuRequest.getAppId(), feishuRequest.getAppSecret());
            
            if (!appTokenResult.isSuccess()) {
                throw new AuthenticationException("FEISHU_APP_TOKEN_ERROR", 
                        "获取飞书应用令牌失败: " + appTokenResult.getErrorMessage());
            }

            // 获取用户访问令牌
            FeishuApiService.FeishuUserTokenResult userTokenResult = feishuApiService.getUserAccessToken(
                    appTokenResult.getAppAccessToken(), feishuRequest.getCode());
            
            if (!userTokenResult.isSuccess()) {
                throw new InvalidCredentialsException("飞书授权码无效: " + userTokenResult.getErrorMessage());
            }

            // 获取用户信息
            FeishuApiService.FeishuUserInfoResult userInfo = feishuApiService.getUserInfo(userTokenResult.getAccessToken());
            if (!userInfo.isSuccess()) {
                throw new AuthenticationException("FEISHU_API_ERROR", "获取用户信息失败: " + userInfo.getErrorMessage());
            }

            // 查找或创建用户
            UserInfo systemUserInfo = findOrCreateUser(feishuRequest, userInfo);

            // 验证用户状态
            validateUserStatus(systemUserInfo);

            // 生成令牌
            String jwtAccessToken = generateAccessToken(systemUserInfo, feishuRequest);
            String refreshToken = generateRefreshToken(systemUserInfo, feishuRequest);
            Long expiresIn = 7200L; // 2小时

            // 更新登录信息
            updateLoginInfo(systemUserInfo, feishuRequest);

            // 清除登录失败记录
            feishuUserService.clearLoginFailures(userInfo.getUserId(), feishuRequest.getAppId());

            // 创建成功响应
            FeishuAuthenticationResponse response;
            if (feishuRequest.getAutoRegister() && feishuUserService.isFirstLogin(systemUserInfo)) {
                response = FeishuAuthenticationResponse.successForNewUser(jwtAccessToken, refreshToken, expiresIn, systemUserInfo);
            } else {
                response = FeishuAuthenticationResponse.success(jwtAccessToken, refreshToken, expiresIn, systemUserInfo);
            }

            // 设置额外信息
            setAdditionalInfo(response, systemUserInfo, feishuRequest, userInfo, appTokenResult);

            log.info("Feishu authentication successful for user: {}, appId: {}", userInfo.getUserId(), feishuRequest.getAppId());
            return response;

        } catch (AuthenticationException e) {
            // 记录登录失败
            recordLoginFailure(feishuRequest, e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Feishu authentication error", e);
            recordLoginFailure(feishuRequest, "System error");
            throw new AuthenticationException("AUTHENTICATION_ERROR", "Authentication failed", e);
        }
    }

    @Override
    public boolean validateToken(String token) {
        return jwtTokenProvider.validateToken(token);
    }

    @Override
    public AuthenticationResponse refreshToken(String refreshToken) throws AuthenticationException {
        try {
            JwtClaims claims = jwtTokenProvider.parseToken(refreshToken);
            
            // 验证是否为刷新令牌
            if (!"refresh_token".equals(claims.getTokenType())) {
                throw new AuthenticationException("INVALID_REFRESH_TOKEN", "Invalid refresh token type");
            }

            // 查找用户
            Optional<UserInfo> userOptional = feishuUserService.findById(claims.getSubject());
            if (userOptional.isEmpty()) {
                throw new AuthenticationException("USER_NOT_FOUND", "User not found");
            }

            UserInfo userInfo = userOptional.get();

            // 验证用户状态
            validateUserStatus(userInfo);

            // 生成新的访问令牌
            String newAccessToken = generateAccessToken(userInfo, null);
            Long expiresIn = 7200L;

            return FeishuAuthenticationResponse.success(newAccessToken, refreshToken, expiresIn, userInfo);

        } catch (AuthenticationException e) {
            throw e;
        } catch (Exception e) {
            log.error("Refresh token error", e);
            throw new AuthenticationException("REFRESH_TOKEN_ERROR", "Failed to refresh token", e);
        }
    }

    @Override
    public String getProviderName() {
        return "Feishu Authentication Provider";
    }

    @Override
    public boolean isAvailable() {
        return true;
    }

    @Override
    public int getPriority() {
        return 5;
    }

    /**
     * 预处理认证请求
     *
     * @param request 飞书认证请求
     * @throws AuthenticationException 认证异常
     */
    private void preAuthenticate(FeishuAuthenticationRequest request) throws AuthenticationException {
        // 验证请求参数
        if (!request.isValid()) {
            throw new AuthenticationException("INVALID_REQUEST", "Invalid authentication request");
        }

        // 验证飞书配置
        if (!feishuApiService.validateConfig(request.getAppId(), request.getAppSecret())) {
            throw new AuthenticationException("INVALID_FEISHU_CONFIG", "Invalid Feishu configuration");
        }
    }

    /**
     * 查找或创建用户
     *
     * @param request  飞书认证请求
     * @param userInfo 飞书用户信息
     * @return 用户信息
     * @throws AuthenticationException 认证异常
     */
    private UserInfo findOrCreateUser(FeishuAuthenticationRequest request, 
                                     FeishuApiService.FeishuUserInfoResult userInfo) throws AuthenticationException {
        
        String feishuUserId = userInfo.getUserId();
        String unionId = userInfo.getUnionId();

        // 优先根据UnionID查找用户（如果存在）
        Optional<UserInfo> userOptional = Optional.empty();
        if (unionId != null && !unionId.trim().isEmpty()) {
            userOptional = feishuUserService.findByFeishuUnionId(unionId);
        }

        // 如果UnionID没找到，根据飞书用户ID查找
        if (userOptional.isEmpty()) {
            userOptional = feishuUserService.findByFeishuUserId(feishuUserId, request.getAppId());
        }

        // 尝试根据邮箱或手机号查找现有用户
        if (userOptional.isEmpty()) {
            if (userInfo.getEmail() != null) {
                userOptional = feishuUserService.findByEmail(userInfo.getEmail());
                if (userOptional.isPresent()) {
                    // 绑定飞书用户
                    UserInfo existingUser = userOptional.get();
                    feishuUserService.bindFeishuUser(existingUser.getUserId(), feishuUserId, unionId, request.getAppId());
                    return existingUser;
                }
            }

            if (userInfo.getMobile() != null) {
                userOptional = feishuUserService.findByMobile(userInfo.getMobile());
                if (userOptional.isPresent()) {
                    // 绑定飞书用户
                    UserInfo existingUser = userOptional.get();
                    feishuUserService.bindFeishuUser(existingUser.getUserId(), feishuUserId, unionId, request.getAppId());
                    return existingUser;
                }
            }
        }

        if (userOptional.isPresent()) {
            return userOptional.get();
        }

        // 用户不存在
        if (!request.getAutoRegister()) {
            throw AuthenticationFailedException.userNotFound(feishuUserId);
        }

        // 自动注册新用户
        try {
            UserInfo newUser = feishuUserService.createUser(userInfo, request.getAppId());
            log.info("Auto-registered new user for feishu userId: {}, appId: {}", feishuUserId, request.getAppId());
            return newUser;

        } catch (Exception e) {
            log.error("Failed to create user for feishu userId: {}, appId: {}", feishuUserId, request.getAppId(), e);
            throw new AuthenticationException("REGISTRATION_FAILED", "Failed to create user", e);
        }
    }

    /**
     * 验证用户状态
     *
     * @param userInfo 用户信息
     * @throws AuthenticationException 认证异常
     */
    private void validateUserStatus(UserInfo userInfo) throws AuthenticationException {
        // 检查用户是否有效
        if (!feishuUserService.isUserValid(userInfo)) {
            throw AuthenticationFailedException.userDisabled(userInfo.getUsername());
        }

        // 检查用户是否被锁定
        if (feishuUserService.isUserLocked(userInfo)) {
            throw AuthenticationFailedException.userLocked(userInfo.getUsername());
        }

        // 检查用户是否被禁用
        if (feishuUserService.isUserDisabled(userInfo)) {
            throw AuthenticationFailedException.userDisabled(userInfo.getUsername());
        }

        // 检查账户是否过期
        if (feishuUserService.isAccountExpired(userInfo)) {
            throw new AuthenticationException("ACCOUNT_EXPIRED", "Account has expired");
        }
    }

    /**
     * 生成访问令牌
     *
     * @param userInfo 用户信息
     * @param request  认证请求
     * @return 访问令牌
     */
    private String generateAccessToken(UserInfo userInfo, FeishuAuthenticationRequest request) {
        JwtClaims claims = JwtClaims.builder()
                .subject(userInfo.getUserId())
                .username(userInfo.getUsername())
                .nickname(userInfo.getNickname())
                .email(userInfo.getEmail())
                .phone(userInfo.getPhone())
                .roles(userInfo.getRoles())
                .permissions(userInfo.getPermissions())
                .grantType(GrantType.FEISHU.getCode())
                .tokenType("access_token")
                .build();

        if (request != null) {
            claims.setClientId(request.getClientId());
            claims.setScope(request.getScope());
            claims.setIpAddress(request.getIpAddress());
            claims.setUserAgent(request.getUserAgent());
            claims.setDeviceId(request.getDeviceId());
            
            // 添加飞书相关信息
            claims.addCustomClaim("app_id", request.getAppId());
            claims.addCustomClaim("auth_type", request.getAuthType());
            claims.addCustomClaim("locale", request.getLocale());
        }

        return jwtTokenProvider.generateAccessToken(claims);
    }

    /**
     * 生成刷新令牌
     *
     * @param userInfo 用户信息
     * @param request  认证请求
     * @return 刷新令牌
     */
    private String generateRefreshToken(UserInfo userInfo, FeishuAuthenticationRequest request) {
        JwtClaims claims = JwtClaims.builder()
                .subject(userInfo.getUserId())
                .username(userInfo.getUsername())
                .grantType(GrantType.FEISHU.getCode())
                .tokenType("refresh_token")
                .build();

        if (request != null) {
            claims.setClientId(request.getClientId());
            claims.addCustomClaim("app_id", request.getAppId());
        }

        return jwtTokenProvider.generateRefreshToken(claims);
    }

    /**
     * 更新登录信息
     *
     * @param userInfo 用户信息
     * @param request  认证请求
     */
    private void updateLoginInfo(UserInfo userInfo, FeishuAuthenticationRequest request) {
        feishuUserService.updateLastLoginInfo(
                userInfo.getUserId(),
                LocalDateTime.now(),
                request.getIpAddress(),
                request.getUserAgent()
        );
    }

    /**
     * 设置额外信息
     *
     * @param response        认证响应
     * @param userInfo        用户信息
     * @param request         认证请求
     * @param feishuUser      飞书用户信息
     * @param appTokenResult  应用令牌结果
     */
    private void setAdditionalInfo(FeishuAuthenticationResponse response, UserInfo userInfo, 
                                  FeishuAuthenticationRequest request, FeishuApiService.FeishuUserInfoResult feishuUser,
                                  FeishuApiService.FeishuAppTokenResult appTokenResult) {
        response.setFirstLogin(feishuUserService.isFirstLogin(userInfo));
        response.setLastLoginTime(userInfo.getLastLoginTime());
        response.setLastLoginIp(userInfo.getLastLoginIp());
        
        // 设置飞书相关信息
        response.setFeishuUserInfo(feishuUser.getUserId(), feishuUser.getName(), 
                feishuUser.getAvatar(), request.getAppId(), appTokenResult.getTenantKey());
    }

    /**
     * 记录登录失败
     *
     * @param request 认证请求
     * @param reason  失败原因
     */
    private void recordLoginFailure(FeishuAuthenticationRequest request, String reason) {
        try {
            // 这里使用飞书用户标识记录失败
            String identifier = request.getAppId() + ":" + request.getCode();
            feishuUserService.recordLoginFailure(identifier, request.getAppId(), request.getIpAddress(), reason);
        } catch (Exception e) {
            log.error("Failed to record login failure", e);
        }
    }
}
