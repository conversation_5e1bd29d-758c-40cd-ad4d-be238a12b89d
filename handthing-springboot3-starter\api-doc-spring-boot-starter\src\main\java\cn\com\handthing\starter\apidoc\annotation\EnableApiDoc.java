package cn.com.handthing.starter.apidoc.annotation;

import cn.com.handthing.starter.apidoc.configuration.ApiDocAutoConfiguration;
import org.springframework.context.annotation.Import;

import java.lang.annotation.*;

/**
 * 启用 HandThing API 文档功能。
 * <p>
 * 在 Spring Boot 启动类上添加此注解，即可为项目集成基于 OpenAPI 3.0 的 API 文档。
 *
 * <AUTHOR>
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
@Import(ApiDocAutoConfiguration.class) // 关键：导入自动配置类
public @interface EnableApiDoc {
}