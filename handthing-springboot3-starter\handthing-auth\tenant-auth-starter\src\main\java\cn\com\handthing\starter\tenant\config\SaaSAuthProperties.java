package cn.com.handthing.starter.tenant.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.time.Duration;

/**
 * SaaS认证配置属性
 * <p>
 * 配置多租户SaaS认证系统的相关参数，包括租户管理、缓存配置等。
 * </p>
 *
 * <AUTHOR>
 * @since V3.0.0
 */
@Data
@ConfigurationProperties(prefix = "handthing.auth.saas")
public class SaaSAuthProperties {

    /**
     * 是否启用SaaS多租户认证
     */
    private boolean enabled = true;

    /**
     * 默认租户ID
     * 当无法解析租户ID时使用的默认值
     */
    private String defaultTenantId = "default";

    /**
     * 是否要求必须有租户
     * 如果为true，当无法解析租户ID时会返回错误
     */
    private boolean tenantRequired = false;

    /**
     * 租户解析器配置
     */
    private TenantResolverProperties resolver = new TenantResolverProperties();

    /**
     * 缓存配置
     */
    private CacheConfig cache = new CacheConfig();

    /**
     * 过滤器配置
     */
    private FilterConfig filter = new FilterConfig();

    /**
     * 验证配置
     */
    private ValidationConfig validation = new ValidationConfig();

    /**
     * 监控配置
     */
    private MonitoringConfig monitoring = new MonitoringConfig();

    /**
     * 缓存配置
     */
    @Data
    public static class CacheConfig {

        /**
         * 是否启用缓存
         */
        private boolean enabled = true;

        /**
         * 缓存名称
         */
        private String cacheName = "tenant-config";

        /**
         * 缓存TTL（生存时间）
         */
        private Duration ttl = Duration.ofMinutes(5);

        /**
         * 最大缓存条目数
         */
        private long maxSize = 1000;

        /**
         * 是否启用缓存统计
         */
        private boolean enableStats = true;
    }

    /**
     * 过滤器配置
     */
    @Data
    public static class FilterConfig {

        /**
         * 是否启用租户解析过滤器
         */
        private boolean enabled = true;

        /**
         * 过滤器顺序
         */
        private int order = -200;

        /**
         * 排除路径列表
         * 这些路径不会进行租户解析
         */
        private String[] excludePaths = {
                "/actuator/**",
                "/error",
                "/favicon.ico",
                "/static/**",
                "/public/**"
        };

        /**
         * 包含路径列表
         * 如果指定，只有这些路径会进行租户解析
         */
        private String[] includePaths = {};

        /**
         * 是否记录租户解析日志
         */
        private boolean logResolution = true;
    }

    /**
     * 验证配置
     */
    @Data
    public static class ValidationConfig {

        /**
         * 是否启用租户ID验证
         */
        private boolean enabled = true;

        /**
         * 租户ID最小长度
         */
        private int minLength = 1;

        /**
         * 租户ID最大长度
         */
        private int maxLength = 64;

        /**
         * 租户ID正则表达式
         */
        private String pattern = "^[a-zA-Z0-9][a-zA-Z0-9_-]*[a-zA-Z0-9]$|^[a-zA-Z0-9]$";

        /**
         * 是否允许空租户ID
         */
        private boolean allowEmpty = false;

        /**
         * 禁用的租户ID列表
         */
        private String[] blacklist = {"admin", "root", "system", "api", "www"};
    }

    /**
     * 监控配置
     */
    @Data
    public static class MonitoringConfig {

        /**
         * 是否启用监控
         */
        private boolean enabled = true;

        /**
         * 是否启用指标收集
         */
        private boolean enableMetrics = true;

        /**
         * 是否启用健康检查
         */
        private boolean enableHealthCheck = true;

        /**
         * 是否启用事件发布
         */
        private boolean enableEvents = true;

        /**
         * 监控数据保留时间
         */
        private Duration retentionPeriod = Duration.ofDays(7);
    }

    /**
     * 检查是否启用了多租户功能
     *
     * @return 如果启用返回true，否则返回false
     */
    public boolean isMultiTenantEnabled() {
        return enabled && resolver.hasEnabledStrategy();
    }

    /**
     * 检查是否需要严格的租户验证
     *
     * @return 如果需要严格验证返回true，否则返回false
     */
    public boolean isStrictTenantValidation() {
        return tenantRequired && validation.enabled;
    }

    /**
     * 获取有效的默认租户ID
     *
     * @return 默认租户ID，如果未配置则返回"default"
     */
    public String getEffectiveDefaultTenantId() {
        return defaultTenantId != null && !defaultTenantId.trim().isEmpty() 
               ? defaultTenantId.trim() 
               : "default";
    }

    /**
     * 检查指定的租户ID是否在黑名单中
     *
     * @param tenantId 租户ID
     * @return 如果在黑名单中返回true，否则返回false
     */
    public boolean isBlacklistedTenantId(String tenantId) {
        if (tenantId == null || validation.blacklist == null) {
            return false;
        }
        
        for (String blacklisted : validation.blacklist) {
            if (blacklisted.equalsIgnoreCase(tenantId)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 验证租户ID格式
     *
     * @param tenantId 租户ID
     * @return 如果格式有效返回true，否则返回false
     */
    public boolean isValidTenantIdFormat(String tenantId) {
        if (!validation.enabled) {
            return true;
        }
        
        if (tenantId == null) {
            return validation.allowEmpty;
        }
        
        if (tenantId.length() < validation.minLength || tenantId.length() > validation.maxLength) {
            return false;
        }
        
        if (validation.pattern != null && !tenantId.matches(validation.pattern)) {
            return false;
        }
        
        return !isBlacklistedTenantId(tenantId);
    }

    /**
     * 获取配置摘要
     *
     * @return 配置摘要字符串
     */
    public String getConfigSummary() {
        StringBuilder sb = new StringBuilder();
        sb.append("SaaSAuthProperties{");
        sb.append("enabled=").append(enabled);
        sb.append(", defaultTenant='").append(defaultTenantId).append("'");
        sb.append(", tenantRequired=").append(tenantRequired);
        sb.append(", multiTenant=").append(isMultiTenantEnabled());
        sb.append(", cache=").append(cache.enabled);
        sb.append(", filter=").append(filter.enabled);
        sb.append(", validation=").append(validation.enabled);
        sb.append(", monitoring=").append(monitoring.enabled);
        sb.append("}");
        return sb.toString();
    }

    @Override
    public String toString() {
        return getConfigSummary();
    }
}
