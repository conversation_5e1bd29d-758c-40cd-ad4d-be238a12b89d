# HandThing DataLayer Spring Boot Starter 系列

[![License](https://img.shields.io/badge/license-Apache%202-blue.svg)](https://www.apache.org/licenses/LICENSE-2.0)
[![Spring Boot](https://img.shields.io/badge/Spring%20Boot-3.x-brightgreen.svg)](https://spring.io/projects/spring-boot)
[![Java](https://img.shields.io/badge/Java-17+-orange.svg)](https://openjdk.java.net/)
[![MyBatis-Plus](https://img.shields.io/badge/MyBatis--Plus-3.5.5-blue.svg)](https://baomidou.com/)

> 🚀 **企业级数据层解决方案** - 模块化、可插拔、高性能的Spring Boot数据层框架

## 📖 项目概述

HandThing DataLayer Spring Boot Starter 系列是一个基于 **核心 + 插件** 架构设计的企业级数据层解决方案。通过模块化设计，您可以根据业务需求灵活组合不同的功能模块，实现从简单CRUD到复杂多租户、数据权限控制的完整数据层能力。

### 🎯 核心特性

- **🔧 零侵入** - 基于注解和自动配置，无需修改现有业务代码
- **🧩 模块化** - 核心 + 插件架构，按需引入功能模块
- **⚡ 高性能** - 池化ID生成、权限缓存、SQL拦截器优化
- **🔒 企业级** - 多租户隔离、数据权限控制、审计日志
- **🛡️ 类型安全** - 完整的泛型设计和类型检查
- **📊 可观测** - 完整的日志记录和配置验证

### 🏗️ 架构设计

```
┌─────────────────────────────────────────────────────────────┐
│                    Your Application                         │
│  ┌─────────────────┐  ┌─────────────────┐                  │
│  │   Tenant Plugin │  │ DataAuth Plugin │  (可选插件)      │
│  └─────────────────┘  └─────────────────┘                  │
│  ┌─────────────────────────────────────────┐                │
│  │        DataLayer Core Module            │  (核心模块)    │
│  └─────────────────────────────────────────┘                │
│  ┌─────────────────────────────────────────┐                │
│  │         ID Generator Module             │  (基础模块)    │
│  └─────────────────────────────────────────┘                │
└─────────────────────────────────────────────────────────────┘
```

## 📦 模块介绍

### 🔑 基础模块

| 模块 | 描述 | 状态 |
|------|------|------|
| **id-spring-boot-starter** | 分布式ID生成器，支持@IdSetter注解 | ✅ 已完成 |

### 🏛️ 核心模块

| 模块 | 描述 | 状态 |
|------|------|------|
| **datalayer-spring-boot-starter** | 基础持久化能力，集成MyBatis-Plus | ✅ 已完成 |

### 🔌 插件模块

| 模块 | 描述 | 状态 |
|------|------|------|
| **tenant-datalayer-spring-boot-starter** | 多租户数据隔离插件 | ✅ 已完成 |
| **dataauth-datalayer-spring-boot-starter** | 数据权限控制插件 | ✅ 已完成 |

## 🚀 快速开始

### 1️⃣ 添加依赖

**基础使用（仅核心功能）：**
```xml
<dependency>
    <groupId>cn.com.handthing.springboot3.starter</groupId>
    <artifactId>datalayer-spring-boot-starter</artifactId>
    <version>1.0.0-SNAPSHOT</version>
</dependency>
```

**多租户应用：**
```xml
<!-- 核心模块 -->
<dependency>
    <groupId>cn.com.handthing.springboot3.starter</groupId>
    <artifactId>datalayer-spring-boot-starter</artifactId>
    <version>1.0.0-SNAPSHOT</version>
</dependency>
<!-- 租户插件 -->
<dependency>
    <groupId>cn.com.handthing.springboot3.starter</groupId>
    <artifactId>tenant-datalayer-spring-boot-starter</artifactId>
    <version>1.0.0-SNAPSHOT</version>
</dependency>
```

**完整功能（核心+租户+权限）：**
```xml
<!-- 核心模块 -->
<dependency>
    <groupId>cn.com.handthing.springboot3.starter</groupId>
    <artifactId>datalayer-spring-boot-starter</artifactId>
    <version>1.0.0-SNAPSHOT</version>
</dependency>
<!-- 租户插件 -->
<dependency>
    <groupId>cn.com.handthing.springboot3.starter</groupId>
    <artifactId>tenant-datalayer-spring-boot-starter</artifactId>
    <version>1.0.0-SNAPSHOT</version>
</dependency>
<!-- 权限插件 -->
<dependency>
    <groupId>cn.com.handthing.springboot3.starter</groupId>
    <artifactId>dataauth-datalayer-spring-boot-starter</artifactId>
    <version>1.0.0-SNAPSHOT</version>
</dependency>
```

### 2️⃣ 配置文件

```yaml
handthing:
  id:
    snowflake:
      worker-id: 1
      datacenter-id: 1
  datalayer:
    enabled: true
    tenant:
      enabled: true
      tenant-id-column: tenant_id
      default-tenant-id: default
    dataauth:
      enabled: true
      aop-enabled: true
      strict-mode: false
```

### 3️⃣ 创建实体类

```java
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("products")
public class Product extends TenantBaseEntity {
    @IdSetter  // 自动生成ID
    @TableId
    private Long id;
    
    @IdSetter  // 自动生成产品编号
    private String productNo;
    
    private String name;
    private BigDecimal price;
}
```

### 4️⃣ 创建Service

```java
@Service
public class ProductService extends BaseServiceImpl<ProductMapper, Product> {
    
    @DataPermission(type = DataPermissionType.DEPT)
    public List<Product> findDeptProducts() {
        return list(); // 自动添加部门权限条件
    }
    
    @DataPermission(type = DataPermissionType.USER, field = "create_by")
    public List<Product> findMyProducts() {
        return list(); // 自动添加用户权限条件
    }
}
```

### 5️⃣ 使用

```java
@RestController
public class ProductController {
    
    @Autowired
    private ProductService productService;
    
    @PostMapping("/products")
    public Product createProduct(@RequestBody Product product) {
        // ID、租户ID、审计字段会自动填充
        return productService.save(product) ? product : null;
    }
    
    @GetMapping("/products")
    public List<Product> getProducts() {
        // 自动应用租户隔离和数据权限
        return productService.findDeptProducts();
    }
}
```

## 🎯 使用场景

### 📋 场景1：简单应用
- **需求**：基础CRUD、ID生成、审计字段
- **模块**：仅引入 `datalayer-spring-boot-starter`
- **特性**：ID自动生成、审计字段自动填充、逻辑删除、乐观锁、分页

### 🏢 场景2：SaaS应用
- **需求**：多租户数据隔离
- **模块**：`datalayer-spring-boot-starter` + `tenant-datalayer-spring-boot-starter`
- **特性**：在场景1基础上，增加透明的租户数据隔离

### 🔐 场景3：企业管理系统
- **需求**：数据权限控制
- **模块**：`datalayer-spring-boot-starter` + `dataauth-datalayer-spring-boot-starter`
- **特性**：在场景1基础上，增加基于注解的数据权限控制

### 🏛️ 场景4：复杂企业应用
- **需求**：多租户 + 数据权限
- **模块**：全部模块
- **特性**：完整的企业级数据层能力

## 📊 性能特性

- **🚀 高性能ID生成**：池化技术，支持10万+/秒的ID生成速度
- **⚡ 智能SQL拦截**：零性能损耗的权限条件注入
- **💾 权限缓存**：可配置的权限结果缓存，减少重复计算
- **🔄 连接池优化**：与主流连接池完美集成

## 🛡️ 安全特性

- **🔒 SQL注入防护**：完整的参数转义和验证
- **🏠 租户隔离**：严格的数据隔离，防止跨租户数据泄露
- **👥 权限控制**：细粒度的数据权限控制
- **📝 审计日志**：完整的数据操作审计记录

## 📚 文档导航

- [📖 API文档](./API.md) - 详细的API接口说明
- [⚙️ 配置参考](./CONFIGURATION.md) - 完整的配置项说明
- [💡 使用示例](./EXAMPLES.md) - 丰富的使用示例
- [🚀 快速开始](./QUICK_START.md) - 详细的入门指南

## 🤝 贡献指南

我们欢迎任何形式的贡献！请查看 [贡献指南](./CONTRIBUTING.md) 了解如何参与项目开发。

## 📄 许可证

本项目采用 [Apache License 2.0](./LICENSE) 许可证。

## 👥 作者

- **HandThing** - *项目创建者和主要维护者*

---

⭐ 如果这个项目对您有帮助，请给我们一个Star！
