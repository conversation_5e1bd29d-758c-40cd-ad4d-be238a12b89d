# 企业微信连接器 (WeCom Connector)

## 概述

企业微信连接器提供了与企业微信开放平台的集成能力，支持企业内部应用和第三方应用的开发。通过统一的API接口，可以轻松实现用户认证、消息发送、通讯录管理等功能。

## 特性

- ✅ **OAuth2.0认证** - 支持企业微信用户授权登录
- ✅ **消息发送** - 支持文本、图片、文件、卡片等多种消息类型
- ✅ **通讯录管理** - 获取部门信息、用户信息、组织架构
- ✅ **应用管理** - 支持企业内部应用和第三方应用
- ✅ **Webhook回调** - 支持企业微信事件回调处理
- ✅ **多环境支持** - 支持开发、测试、生产环境配置

## 快速开始

### 1. 添加依赖

```xml
<dependency>
    <groupId>cn.com.handthing.springboot3.starter</groupId>
    <artifactId>wecom-connector</artifactId>
    <version>1.0.0-SNAPSHOT</version>
</dependency>
```

### 2. 配置文件

```yaml
handthing:
  connector:
    wecom:
      enabled: true
      corp-id: "your-corp-id"
      corp-secret: "your-corp-secret"
      agent-id: "your-agent-id"
      third-party-mode: false
      webhook-enabled: true
      webhook-token: "your-webhook-token"
      webhook-encoding-aes-key: "your-encoding-aes-key"
      redirect-uri: "http://your-domain.com/callback/wecom"
```

### 3. 基本使用

```java
@RestController
public class WeComController {
    
    @Autowired
    private WeComService weComService;
    
    @Autowired
    private AuthService authService;
    
    // 获取授权URL
    @GetMapping("/wecom/auth")
    public String getAuthUrl() {
        return authService.getAuthorizationUrl(
            PlatformType.WECOM, 
            "state", 
            "http://your-domain.com/callback/wecom"
        );
    }
    
    // 发送消息
    @PostMapping("/wecom/message")
    public Map<String, Object> sendMessage(@RequestParam String accessToken,
                                          @RequestParam String userId,
                                          @RequestParam String content) {
        return weComService.message().sendTextMessage(accessToken, userId, content);
    }
    
    // 获取用户信息
    @GetMapping("/wecom/user/{userId}")
    public Map<String, Object> getUserInfo(@RequestParam String accessToken,
                                          @PathVariable String userId) {
        return weComService.user().getUserInfo(accessToken, userId);
    }
}
```

## 配置说明

| 配置项 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| `enabled` | Boolean | 是 | false | 是否启用企业微信连接器 |
| `corp-id` | String | 是 | - | 企业ID |
| `corp-secret` | String | 是 | - | 企业应用密钥 |
| `agent-id` | String | 是 | - | 应用ID |
| `third-party-mode` | Boolean | 否 | false | 是否为第三方应用模式 |
| `webhook-enabled` | Boolean | 否 | false | 是否启用Webhook回调 |
| `webhook-token` | String | 否 | - | Webhook验证Token |
| `webhook-encoding-aes-key` | String | 否 | - | Webhook消息加密密钥 |
| `redirect-uri` | String | 否 | - | OAuth回调地址 |

## API文档

### 消息API (WeComMessageApi)

#### 发送文本消息
```java
Map<String, Object> sendTextMessage(String accessToken, String userId, String content)
```

#### 发送图片消息
```java
Map<String, Object> sendImageMessage(String accessToken, String userId, String mediaId)
```

#### 发送文件消息
```java
Map<String, Object> sendFileMessage(String accessToken, String userId, String mediaId)
```

#### 发送卡片消息
```java
Map<String, Object> sendCardMessage(String accessToken, String userId, Map<String, Object> cardContent)
```

### 用户API (WeComUserApi)

#### 获取用户信息
```java
Map<String, Object> getUserInfo(String accessToken, String userId)
```

#### 获取用户列表
```java
Map<String, Object> getUserList(String accessToken, String departmentId, Boolean fetchChild)
```

#### 获取部门列表
```java
Map<String, Object> getDepartmentList(String accessToken, String departmentId)
```

## 常见问题

### Q: 如何获取企业微信的CorpId和CorpSecret？
A: 登录企业微信管理后台，在"我的企业" -> "企业信息"中可以查看CorpId，在"应用管理"中创建应用后可以获取CorpSecret。

### Q: 第三方应用模式和企业内部应用有什么区别？
A: 企业内部应用只能在本企业内使用，第三方应用可以被多个企业安装使用。配置上主要区别在于认证流程和权限范围。

### Q: 如何处理Webhook回调？
A: 启用webhook-enabled配置，然后实现相应的回调处理器来处理企业微信推送的事件消息。

## 更多信息

- [企业微信开发文档](https://developer.work.weixin.qq.com/)
- [OAuth2.0授权流程](https://developer.work.weixin.qq.com/document/path/91335)
- [消息推送API](https://developer.work.weixin.qq.com/document/path/90236)
