# HandThing Admin 整体模块架构设计和技术架构设计文档

## 1. 整体架构概述

### 1.1 架构设计原则
- **领域驱动设计(DDD)**: 基于业务领域进行模块划分
- **分层架构**: 清晰的分层结构，职责分离
- **微服务就绪**: 模块化设计支持后续微服务拆分
- **高内聚低耦合**: 模块内部高内聚，模块间低耦合
- **可扩展性**: 支持水平扩展和垂直扩展

### 1.2 系统架构图

```
┌─────────────────────────────────────────────────────────────┐
│                        前端层 (Frontend)                      │
├─────────────────────────────────────────────────────────────┤
│  Vue 3.0 + TypeScript + Vite 6 + Ant Design Vue 4         │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                      API网关层 (Gateway)                     │
├─────────────────────────────────────────────────────────────┤
│  Spring Cloud Gateway + 限流 + 熔断 + 认证                   │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                      应用服务层 (Services)                    │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │ 认证授权服务 │ │ 租户管理服务 │ │ 用户管理服务 │ │ 系统管理 │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ │ 服务    │ │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ └─────────┘ │
│  │ 工作流服务   │ │ 消息中心服务 │ │ 集成服务     │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
│  ┌─────────────┐                                           │
│  │ 运营监控服务 │                                           │
│  └─────────────┘                                           │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                      数据存储层 (Storage)                     │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │   MySQL     │ │    Redis    │ │  RabbitMQ   │ │ 文件存储 │ │
│  │   (主库)     │ │   (缓存)     │ │  (消息队列)  │ │ (OSS/S3)│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 2. 业务模块架构设计

### 2.1 业务模块划分

#### 核心业务模块
```
handthing-admin/
├── handthing-admin-common/           # 公共模块
├── handthing-admin-auth/             # 认证授权模块
├── handthing-admin-tenant/           # 租户管理模块
├── handthing-admin-user/             # 用户管理模块
├── handthing-admin-system/           # 系统管理模块
├── handthing-admin-workflow/         # 工作流模块
├── handthing-admin-message/          # 消息中心模块
├── handthing-admin-integration/      # 应用集成模块
├── handthing-admin-operation/        # 运营监控模块
└── handthing-admin-web/              # Web启动模块
```

#### 模块职责说明

| 模块名称 | 核心职责 | 主要功能 |
|---------|---------|---------|
| **common** | 公共基础设施 | 工具类、常量、异常、DTO |
| **auth** | 认证授权 | 登录认证、权限控制、Token管理 |
| **tenant** | 租户管理 | 租户CRUD、套餐管理、资源配额 |
| **user** | 用户管理 | 用户CRUD、角色分配、组织架构 |
| **system** | 系统管理 | 菜单管理、字典管理、配置管理 |
| **workflow** | 工作流 | 流程设计、审批管理、任务调度 |
| **message** | 消息中心 | 消息模板、通知发送、文件存储 |
| **integration** | 应用集成 | 第三方集成、API管理、数据同步 |
| **operation** | 运营监控 | 审计日志、系统监控、计费管理 |
| **web** | Web启动 | 控制器、配置、应用启动 |

### 2.2 模块依赖关系

```
                    handthing-admin-web
                           │
        ┌──────────────────┼──────────────────┐
        │                  │                  │
        ▼                  ▼                  ▼
handthing-admin-auth  handthing-admin-tenant  handthing-admin-user
        │                  │                  │
        └──────────────────┼──────────────────┘
                           │
        ┌──────────────────┼──────────────────┐
        │                  │                  │
        ▼                  ▼                  ▼
handthing-admin-system handthing-admin-workflow handthing-admin-message
        │                  │                  │
        └──────────────────┼──────────────────┘
                           │
        ┌──────────────────┼──────────────────┐
        │                  │                  │
        ▼                  ▼                  ▼
handthing-admin-integration handthing-admin-operation handthing-admin-common
        │                  │                  │
        └──────────────────┼──────────────────┘
                           │
                           ▼
                handthing-springboot3-starter
```

### 2.3 DDD分层架构

每个业务模块内部采用DDD分层架构：

```
{module}/
├── {module}-api/                     # 接口层 (Interface Layer)
│   ├── dto/                          # 数据传输对象
│   ├── request/                      # 请求对象
│   ├── response/                     # 响应对象
│   └── service/                      # 接口定义
├── {module}-domain/                  # 领域层 (Domain Layer)
│   ├── entity/                       # 实体对象
│   ├── valueobject/                  # 值对象
│   ├── repository/                   # 仓储接口
│   ├── service/                      # 领域服务
│   └── event/                        # 领域事件
├── {module}-application/             # 应用层 (Application Layer)
│   ├── service/                      # 应用服务
│   ├── assembler/                    # 对象转换器
│   ├── command/                      # 命令对象
│   └── handler/                      # 事件处理器
└── {module}-infrastructure/          # 基础设施层 (Infrastructure Layer)
    ├── repository/                   # 仓储实现
    ├── mapper/                       # MyBatis Mapper
    ├── config/                       # 配置类
    └── external/                     # 外部服务
```

## 3. 技术架构设计

### 3.1 技术栈选型

#### 后端技术栈
```yaml
基础框架:
  - Spring Boot: 3.2+
  - Spring Security: 6.x
  - Spring Data JPA: 3.x
  
依赖管理:
  - Maven: 3.9+
  - starter-parent: handthing-springboot3-starter统一管理
  
数据层:
  - datalayer-spring-boot-starter: MyBatis-Plus集成
  - tenant-datalayer-spring-boot-starter: 多租户数据隔离
  - dataauth-datalayer-spring-boot-starter: 数据权限控制
  - MySQL: 8.0+ (主数据库)
  - Redis: 7.x (缓存)
  
认证授权:
  - handthing-auth: 统一认证框架
  - JWT: Token认证
  - OAuth2.0: 第三方登录
  - SAML/OIDC: 企业身份源
  
缓存:
  - level-cache-spring-boot-starter: 多级缓存
  - Caffeine: L1本地缓存
  - Redis: L2分布式缓存
  
工具组件:
  - id-spring-boot-starter: 分布式ID生成
  - crypto-spring-boot-starter: 加密解密
  - webclient-spring-boot-starter: HTTP客户端
  - handthing-core: 核心工具类(替代hutool)
  
消息队列:
  - RabbitMQ: 3.12+
  
任务调度:
  - XXL-JOB: 2.4+
  
工作流:
  - Flowable: 7.0+
  
文件存储:
  - MinIO: 8.5+
  - 阿里云OSS
  - AWS S3
  
API文档:
  - knife4j-spring-boot-starter: API文档生成
```

#### 前端技术栈
```yaml
核心框架:
  - Vue: 3.0+
  - TypeScript: 5.x
  - Vite: 6.x
  
UI组件:
  - Ant Design Vue: 4.x
  
状态管理:
  - Pinia: Vue 3官方状态管理
  
路由:
  - Vue Router: 4.x
  
HTTP客户端:
  - Axios: 1.x
  
工具库:
  - Lodash-es: 实用工具函数
  
图表:
  - ECharts: 5.x
  
流程设计:
  - BPMN.js: 工作流设计器
  
代码规范:
  - ESLint: 代码检查
  - Prettier: 代码格式化
  
包管理:
  - pnpm: 高效包管理
```

### 3.2 数据架构设计

#### 数据库设计策略
```yaml
多租户策略:
  - 逻辑隔离: 基于tenant_id字段
  - 自动注入: tenant-datalayer-spring-boot-starter自动处理
  
数据权限策略:
  - 行级权限: 基于部门层级
  - 列级权限: 敏感字段控制
  - 自动过滤: dataauth-datalayer-spring-boot-starter自动处理
  
缓存策略:
  - L1缓存: Caffeine本地缓存
  - L2缓存: Redis分布式缓存
  - 缓存一致性: 基于事件的缓存更新
  
分库分表策略:
  - 日志表: 按月分表
  - 监控数据: 按天分表
  - 大数据量表: 基于租户ID分库
```

#### 核心数据模型
```yaml
租户相关:
  - tenant_info: 租户基本信息
  - tenant_package: 租户套餐配置
  - tenant_subscription: 租户订阅记录
  
用户权限:
  - user_info: 用户基本信息
  - role_info: 角色信息
  - permission_info: 权限信息
  - user_role: 用户角色关联
  - role_permission: 角色权限关联
  
组织架构:
  - department_info: 部门信息
  - position_info: 职位信息
  - user_department: 用户部门关联
  
系统管理:
  - menu_info: 菜单信息
  - dict_type: 字典类型
  - dict_data: 字典数据
  - system_config: 系统配置
  
工作流:
  - workflow_definition: 流程定义
  - workflow_instance: 流程实例
  - workflow_task: 工作任务
  
消息中心:
  - message_template: 消息模板
  - message_record: 消息记录
  - notification: 通知信息
  
审计监控:
  - audit_log: 审计日志
  - system_monitor: 系统监控
  - alert_rule: 告警规则
```

### 3.3 安全架构设计

#### 认证安全
```yaml
多因素认证:
  - 密码认证: 基于用户名密码
  - 短信认证: 基于手机验证码
  - 邮箱认证: 基于邮箱验证码
  - OAuth2.0: 第三方平台登录
  - SAML: 企业身份源集成
  - OIDC: OpenID Connect
  - MFA: 多因素认证
  
Token管理:
  - JWT: 无状态Token
  - 双Token: Access Token + Refresh Token
  - Token刷新: 自动刷新机制
  - Token撤销: 强制下线功能
```

#### 权限控制
```yaml
权限模型:
  - RBAC: 基于角色的访问控制
  - 菜单权限: 控制页面访问
  - 操作权限: 控制按钮功能
  - 数据权限: 控制数据范围
  
数据权限:
  - 全部数据: 超级管理员
  - 本部门数据: 部门管理员
  - 本部门及下级: 高级管理员
  - 仅本人数据: 普通用户
  - 自定义规则: 灵活配置
```

#### 数据安全
```yaml
传输安全:
  - HTTPS: TLS 1.3加密传输
  - API签名: 接口签名验证
  
存储安全:
  - 字段加密: 敏感数据加密存储
  - 密钥管理: crypto-spring-boot-starter
  - 数据脱敏: 敏感信息脱敏显示
  
备份安全:
  - 定期备份: 自动化数据备份
  - 异地备份: 跨地域备份
  - 加密备份: 备份数据加密
```

### 3.4 部署架构设计

#### 容器化部署
```yaml
容器技术:
  - Docker: 应用容器化
  - Kubernetes: 容器编排
  - Helm: 部署管理
  
镜像管理:
  - Harbor: 私有镜像仓库
  - 多阶段构建: 优化镜像大小
  - 安全扫描: 镜像安全检查
```

#### 高可用设计
```yaml
应用高可用:
  - 多副本部署: 3个副本
  - 负载均衡: Nginx/Ingress
  - 健康检查: 自动故障检测
  - 自动扩缩容: HPA水平扩展
  
数据库高可用:
  - 主从复制: MySQL主从
  - 读写分离: 读写分离
  - 故障转移: 自动切换
  - 数据备份: 定期备份
  
缓存高可用:
  - Redis集群: Cluster模式
  - 数据分片: 自动分片
  - 故障转移: 自动切换
  - 持久化: RDB+AOF
```

#### 监控体系
```yaml
应用监控:
  - Micrometer: 指标收集
  - Prometheus: 指标存储
  - Grafana: 可视化展示
  - AlertManager: 告警管理
  
日志监控:
  - ELK Stack: 日志收集分析
  - Filebeat: 日志采集
  - Logstash: 日志处理
  - Elasticsearch: 日志存储
  - Kibana: 日志查询
  
链路追踪:
  - SkyWalking: 分布式追踪
  - Jaeger: 链路分析
```

## 4. 集成架构设计

### 4.1 第三方集成
```yaml
身份认证集成:
  - 企业微信: OAuth2.0
  - 钉钉: OAuth2.0
  - 飞书: OAuth2.0
  - LDAP: 企业目录
  - AD域: Windows域
  
支付集成:
  - 支付宝: 支付接口
  - 微信支付: 支付接口
  - 银联: 支付接口
  
存储集成:
  - 阿里云OSS: 对象存储
  - 腾讯云COS: 对象存储
  - AWS S3: 对象存储
  - MinIO: 私有对象存储
  
通知集成:
  - 短信: 阿里云SMS、腾讯云SMS
  - 邮件: SMTP、阿里云邮件
  - 推送: 极光推送、个推
```

### 4.2 API网关设计
```yaml
网关功能:
  - 路由转发: 请求路由
  - 负载均衡: 流量分发
  - 限流熔断: 流量控制
  - 认证授权: 统一认证
  - 日志记录: 访问日志
  - 监控统计: 接口监控
  
网关技术:
  - Spring Cloud Gateway: 网关框架
  - Redis: 限流存储
  - Sentinel: 熔断降级
```

---

**© 2024 HandThing. All rights reserved.**
