package cn.com.handthing.starter.connector.dingtalk.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 钉钉访问令牌响应模型
 * <p>
 * 钉钉获取访问令牌API的响应数据结构
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DingTalkAccessTokenResponse {

    /**
     * 访问令牌
     */
    @JsonProperty("accessToken")
    private String accessToken;

    /**
     * 刷新令牌
     */
    @JsonProperty("refreshToken")
    private String refreshToken;

    /**
     * 过期时间（秒）
     */
    @JsonProperty("expireIn")
    private Long expiresIn;

    /**
     * 授权范围
     */
    @JsonProperty("scope")
    private String scope;

    /**
     * 企业CorpId
     */
    @JsonProperty("corpId")
    private String corpId;

    /**
     * 错误码
     */
    @JsonProperty("code")
    private String code;

    /**
     * 错误信息
     */
    @JsonProperty("message")
    private String message;

    /**
     * 检查响应是否成功
     *
     * @return 如果成功返回true，否则返回false
     */
    public boolean isSuccess() {
        return accessToken != null && !accessToken.trim().isEmpty();
    }

    /**
     * 获取错误描述
     *
     * @return 错误描述
     */
    public String getErrorDescription() {
        if (isSuccess()) {
            return "Success";
        }
        return String.format("Error %s: %s", code, message);
    }
}
