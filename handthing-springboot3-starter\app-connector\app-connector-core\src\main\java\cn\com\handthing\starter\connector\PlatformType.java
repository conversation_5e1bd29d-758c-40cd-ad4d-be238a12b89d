package cn.com.handthing.starter.connector;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 平台类型枚举
 * <p>
 * 定义支持的第三方平台类型，包含平台名称、代码等属性
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Getter
@RequiredArgsConstructor
public enum PlatformType {

    /**
     * 企业微信
     */
    WECOM("wecom", "企业微信", "WeCom"),

    /**
     * 钉钉
     */
    DINGTALK("dingtalk", "钉钉", "DingTalk"),

    /**
     * 抖音
     */
    DOUYIN("douyin", "抖音", "Douyin"),

    /**
     * 支付宝
     */
    ALIPAY("alipay", "支付宝", "Alipay"),

    /**
     * 微信
     */
    WECHAT("wechat", "微信", "WeChat"),

    /**
     * 飞书
     */
    FEISHU("feishu", "飞书", "<PERSON>ish<PERSON>"),

    /**
     * 快手
     */
    KUAISHOU("kuaishou", "快手", "<PERSON><PERSON><PERSON>"),

    /**
     * 小红书
     */
    XIAOHONGSHU("xiaohongshu", "小红书", "XiaoHongShu"),

    /**
     * 今日头条
     */
    TOUTIAO("toutiao", "今日头条", "Toutiao"),

    /**
     * Bilibili
     */
    BILIBILI("bilibili", "Bilibili", "Bilibili");

    /**
     * 平台代码（用于配置和URL路径）
     */
    private final String code;

    /**
     * 平台中文名称
     */
    private final String displayName;

    /**
     * 平台英文名称
     */
    private final String englishName;

    /**
     * 根据代码查找平台类型
     *
     * @param code 平台代码
     * @return 平台类型，如果未找到则返回null
     */
    public static PlatformType fromCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return null;
        }
        
        for (PlatformType type : values()) {
            if (type.code.equalsIgnoreCase(code.trim())) {
                return type;
            }
        }
        return null;
    }

    /**
     * 检查是否为有效的平台代码
     *
     * @param code 平台代码
     * @return 如果有效返回true，否则返回false
     */
    public static boolean isValidCode(String code) {
        return fromCode(code) != null;
    }

    @Override
    public String toString() {
        return String.format("%s(%s)", displayName, code);
    }
}
