package cn.com.handthing.starter.httpclient;

import org.springframework.lang.NonNull;

/**
 * 统一的、与具体实现无关的HTTP客户端接口。
 * <p>
 * 这是用户与本 starter 交互的唯一入口点。通过注入此接口，用户可以发起一个HTTP请求。
 * <p>
 * 使用示例:
 * <pre>{@code
 * @Autowired
 * private HandthingHttpClient httpClient;
 *
 * public Mono<User> getUser(String id) {
 * return httpClient.get("/api/users/{id}", id)
 * .retrieve()
 * .body(User.class)
 * .mono();
 * }
 * }</pre>
 */
public interface HandthingHttpClient {

    /**
     * 准备一个 GET 请求。
     *
     * @param uri           请求的URI模板，可以使用花括号作为占位符，例如 "/users/{id}"。
     * @param uriVariables  用于替换URI模板中占位符的可变参数。
     * @return 一个请求执行器 ({@link RequestExecutor})，用于进一步配置和执行请求。
     */
    @NonNull
    RequestExecutor get(@NonNull String uri, Object... uriVariables);

    /**
     * 准备一个 POST 请求。
     *
     * @param uri           请求的URI模板。
     * @param uriVariables  URI模板变量。
     * @return 一个请求执行器 ({@link RequestExecutor})。
     */
    @NonNull
    RequestExecutor post(@NonNull String uri, Object... uriVariables);

    /**
     * 准备一个 PUT 请求。
     *
     * @param uri           请求的URI模板。
     * @param uriVariables  URI模板变量。
     * @return 一个请求执行器 ({@link RequestExecutor})。
     */
    @NonNull
    RequestExecutor put(@NonNull String uri, Object... uriVariables);

    /**
     * 准备一个 DELETE 请求。
     *
     * @param uri           请求的URI模板。
     * @param uriVariables  URI模板变量。
     * @return 一个请求执行器 ({@link RequestExecutor})。
     */
    @NonNull
    RequestExecutor delete(@NonNull String uri, Object... uriVariables);

}