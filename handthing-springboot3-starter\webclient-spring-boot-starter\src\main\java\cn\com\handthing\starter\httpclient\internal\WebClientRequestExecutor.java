package cn.com.handthing.starter.httpclient.internal;

import cn.com.handthing.starter.httpclient.RequestExecutor;
import cn.com.handthing.starter.httpclient.ResponseSpec;
import cn.com.handthing.starter.httpclient.config.EndpointConfig;
import cn.com.handthing.starter.httpclient.crypto.RequestEncryptor;
import cn.com.handthing.starter.httpclient.crypto.ResponseDecryptor;
import cn.com.handthing.starter.httpclient.exception.HandthingHttpException;
import cn.com.handthing.starter.httpclient.filter.WebClientCryptoFilter;
import cn.com.handthing.starter.httpclient.filter.WebClientLoggingFilter;
import cn.com.handthing.starter.httpclient.filter.WebClientRetryFilter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;

/**
 * RequestExecutor 接口的 WebClient 实现。
 *
 * <AUTHOR>
 * @since V1.0.0
 */
public final class WebClientRequestExecutor implements RequestExecutor {

    private static final Logger log = LoggerFactory.getLogger(WebClientRequestExecutor.class);

    private final EndpointConfigResolver configResolver;
    private final RequestEncryptor<?> requestEncryptor;
    private final ResponseDecryptor responseDecryptor;
    private final HttpMethod httpMethod;
    private final String uriTemplate;
    private final Object[] uriVariables;

    private final HttpHeaders headers = new HttpHeaders();
    private Object body;

    public WebClientRequestExecutor(
            EndpointConfigResolver configResolver,
            RequestEncryptor<?> requestEncryptor,
            ResponseDecryptor responseDecryptor,
            HttpMethod httpMethod,
            String uriTemplate,
            Object... uriVariables) {
        this.configResolver = configResolver;
        this.requestEncryptor = requestEncryptor;
        this.responseDecryptor = responseDecryptor;
        this.httpMethod = httpMethod;
        this.uriTemplate = uriTemplate;
        this.uriVariables = uriVariables;
    }

    @Override
    @NonNull
    public RequestExecutor header(@NonNull String name, @NonNull String value) {
        headers.add(name, value);
        return this;
    }

    @Override
    @NonNull
    public RequestExecutor body(@Nullable Object body) {
        this.body = body;
        return this;
    }

    @Override
    @NonNull
    public ResponseSpec retrieve() {
        try {
            // 构建完整的 URI
            URI uri = buildUri();
            
            // 解析端点配置
            EndpointConfig config = configResolver.resolve(uri.toString());
            
            // 创建配置好的 WebClient
            WebClient webClient = createConfiguredWebClient(config);
            
            // 构建请求
            WebClient.RequestBodySpec requestSpec = webClient
                    .method(httpMethod)
                    .uri(uri)
                    .headers(httpHeaders -> httpHeaders.addAll(headers));
            
            // 设置请求体（如果有）
            WebClient.RequestHeadersSpec<?> headersSpec;
            if (body != null && (httpMethod == HttpMethod.POST || httpMethod == HttpMethod.PUT || httpMethod == HttpMethod.PATCH)) {
                headersSpec = requestSpec.bodyValue(body);
            } else {
                headersSpec = requestSpec;
            }
            
            // 返回 WebClient 响应规范
            return new WebClientResponseSpec(headersSpec.retrieve(), responseDecryptor);
            
        } catch (Exception e) {
            log.error("Failed to execute HTTP request: {} {}", httpMethod, uriTemplate, e);
            throw new HandthingHttpException("Failed to execute HTTP request", e);
        }
    }

    private URI buildUri() {
        try {
            if (uriVariables != null && uriVariables.length > 0) {
                return UriComponentsBuilder.fromUriString(uriTemplate)
                        .buildAndExpand(uriVariables)
                        .toUri();
            } else {
                return UriComponentsBuilder.fromUriString(uriTemplate)
                        .build()
                        .toUri();
            }
        } catch (Exception e) {
            throw new HandthingHttpException("Failed to build URI from template: " + uriTemplate, e);
        }
    }

    private WebClient createConfiguredWebClient(EndpointConfig config) {
        WebClient.Builder builder = WebClient.builder();
        
        // 配置超时
        if (config.getConnectTimeout() != null || config.getReadTimeout() != null) {
            builder = builder.codecs(configurer -> {
                if (config.getReadTimeout() != null) {
                    configurer.defaultCodecs().maxInMemorySize(16 * 1024 * 1024); // 16MB
                }
            });
        }
        
        // 配置过滤器
        configureFilters(builder, config);
        
        return builder.build();
    }

    private void configureFilters(WebClient.Builder builder, EndpointConfig config) {
        // 添加日志过滤器
        if (config.getLogging() != null && config.getLogging().isEnabled()) {
            builder.filter(new WebClientLoggingFilter(config.getLogging()));
        }
        
        // 添加重试过滤器
        if (config.getRetry() != null && config.getRetry().isEnabled()) {
            builder.filter(new WebClientRetryFilter(config.getRetry()));
        }
        
        // 添加加密过滤器
        if (config.getCrypto() != null && config.getCrypto().isEnabled()) {
            builder.filter(new WebClientCryptoFilter(config.getCrypto(), requestEncryptor, responseDecryptor));
        }
    }
}
