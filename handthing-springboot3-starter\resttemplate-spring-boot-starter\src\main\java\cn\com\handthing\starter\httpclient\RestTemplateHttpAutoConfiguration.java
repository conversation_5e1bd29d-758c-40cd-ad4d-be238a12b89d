package cn.com.handthing.starter.httpclient;

import cn.com.handthing.starter.httpclient.crypto.RequestEncryptor;
import cn.com.handthing.starter.httpclient.crypto.ResponseDecryptor;
import cn.com.handthing.starter.httpclient.internal.EndpointConfigResolver;
import cn.com.handthing.starter.httpclient.internal.RestTemplateHandthingHttpClient;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.web.client.RestTemplate;

/**
 * RestTemplate HTTP 客户端的自动配置类。
 * <p>
 * 当类路径下存在 RestTemplate 并且没有更高优先级的 HandthingHttpClient Bean 时，此配置将生效。
 * 
 * <AUTHOR>
 * @since V1.0.0
 */
@AutoConfiguration(after = HttpCoreAutoConfiguration.class)
@ConditionalOnClass(RestTemplate.class)
public class RestTemplateHttpAutoConfiguration {

    /**
     * 创建并注册实现了统一 Fluent API 的 RestTemplate 客户端 Bean。
     * <p>
     * 使用 @ConditionalOnMissingBean(HandthingHttpClient.class) 确保了
     * 如果 webclient-starter 存在并已经注册了它的实现，那么此 Bean 将不会被创建，
     * 从而实现了 WebClient 优先的策略。
     *
     * @param configResolver    由核心模块提供的配置解析器。
     * @param requestEncryptor  由核心模块或用户提供的请求加密器。
     * @param responseDecryptor 由核心模块或用户提供的响应解密器。
     * @return 一个实现了 {@link HandthingHttpClient} 接口的实例。
     */
    @Bean
    @ConditionalOnMissingBean(HandthingHttpClient.class)
    public HandthingHttpClient handthingRestTemplateHttpClient(
            EndpointConfigResolver configResolver,
            @org.springframework.beans.factory.annotation.Qualifier("handthingNoOpRequestEncryptor") RequestEncryptor<?> requestEncryptor,
            @org.springframework.beans.factory.annotation.Qualifier("handthingNoOpResponseDecryptor") ResponseDecryptor responseDecryptor) {

        return new RestTemplateHandthingHttpClient(configResolver, requestEncryptor, responseDecryptor);
    }
}
