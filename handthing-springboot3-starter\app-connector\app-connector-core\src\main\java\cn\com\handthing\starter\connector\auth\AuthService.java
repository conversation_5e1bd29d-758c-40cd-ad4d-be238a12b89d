package cn.com.handthing.starter.connector.auth;

import cn.com.handthing.starter.connector.PlatformType;
import cn.com.handthing.starter.connector.exception.AuthException;

import java.util.List;

/**
 * 统一认证服务接口
 * <p>
 * 管理多个AuthProvider实例，提供统一的认证入口方法。
 * 支持按平台类型路由到对应的AuthProvider。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
public interface AuthService {

    /**
     * 获取授权URL
     *
     * @param platform    平台类型
     * @param state       自定义状态参数
     * @param redirectUri 回调地址（可选）
     * @return 授权URL
     * @throws AuthException 如果平台不支持或生成URL失败
     */
    String getAuthorizationUrl(PlatformType platform, String state, String redirectUri) throws AuthException;

    /**
     * 获取授权URL（使用默认回调地址）
     *
     * @param platform 平台类型
     * @param state    自定义状态参数
     * @return 授权URL
     * @throws AuthException 如果平台不支持或生成URL失败
     */
    default String getAuthorizationUrl(PlatformType platform, String state) throws AuthException {
        return getAuthorizationUrl(platform, state, null);
    }

    /**
     * 使用授权码交换访问令牌
     *
     * @param platform    平台类型
     * @param code        授权码
     * @param state       状态参数
     * @param redirectUri 回调地址
     * @return 访问令牌信息
     * @throws AuthException 如果平台不支持或交换失败
     */
    UnifiedAccessToken exchangeToken(PlatformType platform, String code, String state, String redirectUri) throws AuthException;

    /**
     * 刷新访问令牌
     *
     * @param platform     平台类型
     * @param refreshToken 刷新令牌
     * @return 新的访问令牌信息
     * @throws AuthException 如果平台不支持或刷新失败
     */
    UnifiedAccessToken refreshToken(PlatformType platform, String refreshToken) throws AuthException;

    /**
     * 获取用户信息
     *
     * @param platform    平台类型
     * @param accessToken 访问令牌
     * @return 用户信息
     * @throws AuthException 如果平台不支持或获取失败
     */
    UnifiedUserInfo getUserInfo(PlatformType platform, String accessToken) throws AuthException;

    /**
     * 验证访问令牌是否有效
     *
     * @param platform    平台类型
     * @param accessToken 访问令牌
     * @return 如果令牌有效返回true，否则返回false
     * @throws AuthException 如果验证过程中发生错误
     */
    boolean validateToken(PlatformType platform, String accessToken) throws AuthException;

    /**
     * 撤销访问令牌
     *
     * @param platform    平台类型
     * @param accessToken 访问令牌
     * @throws AuthException 如果平台不支持或撤销失败
     */
    void revokeToken(PlatformType platform, String accessToken) throws AuthException;

    /**
     * 检查平台是否支持
     *
     * @param platform 平台类型
     * @return 如果支持返回true，否则返回false
     */
    boolean isPlatformSupported(PlatformType platform);

    /**
     * 获取所有支持的平台类型
     *
     * @return 支持的平台类型列表
     */
    List<PlatformType> getSupportedPlatforms();

    /**
     * 检查平台是否支持刷新令牌
     *
     * @param platform 平台类型
     * @return 如果支持返回true，否则返回false
     * @throws AuthException 如果平台不支持
     */
    boolean supportsRefreshToken(PlatformType platform) throws AuthException;

    /**
     * 检查平台是否支持令牌撤销
     *
     * @param platform 平台类型
     * @return 如果支持返回true，否则返回false
     * @throws AuthException 如果平台不支持
     */
    boolean supportsTokenRevocation(PlatformType platform) throws AuthException;

    /**
     * 获取平台的默认授权范围
     *
     * @param platform 平台类型
     * @return 默认授权范围
     * @throws AuthException 如果平台不支持
     */
    String getDefaultScope(PlatformType platform) throws AuthException;
}
