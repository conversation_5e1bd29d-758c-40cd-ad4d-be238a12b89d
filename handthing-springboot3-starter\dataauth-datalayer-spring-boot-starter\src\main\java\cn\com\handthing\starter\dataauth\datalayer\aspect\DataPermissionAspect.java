package cn.com.handthing.starter.dataauth.datalayer.aspect;

import cn.com.handthing.starter.dataauth.datalayer.annotation.DataPermission;
import cn.com.handthing.starter.dataauth.datalayer.annotation.IgnoreDataPermission;
import cn.com.handthing.starter.dataauth.datalayer.context.DataPermissionContext;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * 数据权限AOP拦截器
 * <p>
 * 通过AOP拦截带有@DataPermission注解的方法，设置数据权限上下文。
 * 在方法执行前设置权限配置，在方法执行后清理上下文。
 * </p>
 * 
 * <h3>功能特性：</h3>
 * <ul>
 *   <li>自动拦截@DataPermission注解的方法</li>
 *   <li>支持@IgnoreDataPermission注解跳过权限检查</li>
 *   <li>设置和清理权限上下文</li>
 *   <li>支持方法级和类级注解</li>
 * </ul>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Aspect
@Component
public class DataPermissionAspect {
    
    /**
     * 定义切点：所有带有@DataPermission注解的方法
     */
    @Pointcut("@annotation(cn.com.handthing.starter.dataauth.datalayer.annotation.DataPermission)")
    public void dataPermissionMethodPointcut() {
    }
    
    /**
     * 定义切点：所有在带有@DataPermission注解的类中的方法
     */
    @Pointcut("@within(cn.com.handthing.starter.dataauth.datalayer.annotation.DataPermission)")
    public void dataPermissionClassPointcut() {
    }
    
    /**
     * 定义切点：所有带有@IgnoreDataPermission注解的方法
     */
    @Pointcut("@annotation(cn.com.handthing.starter.dataauth.datalayer.annotation.IgnoreDataPermission)")
    public void ignoreDataPermissionMethodPointcut() {
    }
    
    /**
     * 定义切点：所有在带有@IgnoreDataPermission注解的类中的方法
     */
    @Pointcut("@within(cn.com.handthing.starter.dataauth.datalayer.annotation.IgnoreDataPermission)")
    public void ignoreDataPermissionClassPointcut() {
    }
    
    /**
     * 环绕通知：处理数据权限
     */
    @Around("(dataPermissionMethodPointcut() || dataPermissionClassPointcut()) && " +
            "!(ignoreDataPermissionMethodPointcut() || ignoreDataPermissionClassPointcut())")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        Class<?> targetClass = joinPoint.getTarget().getClass();
        
        // 检查是否应该忽略权限检查
        if (shouldIgnorePermission(method, targetClass)) {
            log.debug("Ignoring data permission for method: {}", getMethodSignature(method));
            return joinPoint.proceed();
        }
        
        // 获取权限配置
        DataPermission permission = getDataPermission(method, targetClass);
        if (permission == null) {
            log.debug("No data permission configuration found for method: {}", getMethodSignature(method));
            return joinPoint.proceed();
        }
        
        // 检查权限是否启用
        if (!isPermissionEnabled(permission)) {
            log.debug("Data permission is disabled for method: {}", getMethodSignature(method));
            return joinPoint.proceed();
        }
        
        // 设置权限上下文
        DataPermissionContext context = createPermissionContext(permission, method, joinPoint);
        DataPermissionContext.setCurrentContext(context);
        
        try {
            log.debug("Applied data permission context for method: {}, type: {}", 
                    getMethodSignature(method), permission.type());
            
            // 执行目标方法
            return joinPoint.proceed();
            
        } finally {
            // 清理权限上下文
            DataPermissionContext.clearContext();
            log.debug("Cleared data permission context for method: {}", getMethodSignature(method));
        }
    }
    
    /**
     * 检查是否应该忽略权限检查
     * 
     * @param method      方法
     * @param targetClass 目标类
     * @return 如果应该忽略返回true，否则返回false
     */
    protected boolean shouldIgnorePermission(Method method, Class<?> targetClass) {
        // 检查方法级@IgnoreDataPermission注解
        IgnoreDataPermission methodIgnore = AnnotationUtils.findAnnotation(method, IgnoreDataPermission.class);
        if (methodIgnore != null) {
            return shouldIgnoreByAnnotation(methodIgnore);
        }
        
        // 检查类级@IgnoreDataPermission注解
        IgnoreDataPermission classIgnore = AnnotationUtils.findAnnotation(targetClass, IgnoreDataPermission.class);
        if (classIgnore != null) {
            return shouldIgnoreByAnnotation(classIgnore);
        }
        
        return false;
    }
    
    /**
     * 根据@IgnoreDataPermission注解判断是否忽略
     * 
     * @param ignore 忽略注解
     * @return 如果应该忽略返回true，否则返回false
     */
    protected boolean shouldIgnoreByAnnotation(IgnoreDataPermission ignore) {
        // 检查是否仅对超级管理员生效
        if (ignore.onlyForSuperAdmin()) {
            boolean isSuperAdmin = checkSuperAdmin();
            if (!isSuperAdmin) {
                log.debug("IgnoreDataPermission is only for super admin, but current user is not super admin");
                return false;
            }
        }
        
        // 检查条件表达式
        String condition = ignore.condition();
        if (condition != null && !condition.trim().isEmpty()) {
            boolean conditionResult = evaluateCondition(condition);
            if (!conditionResult) {
                log.debug("IgnoreDataPermission condition not met: {}", condition);
                return false;
            }
        }
        
        // 记录忽略日志
        if (ignore.logIgnore()) {
            String reason = ignore.reason();
            if (reason != null && !reason.trim().isEmpty()) {
                log.info("Ignoring data permission check: {}", reason);
            } else {
                log.info("Ignoring data permission check");
            }
        }
        
        return true;
    }
    
    /**
     * 获取数据权限配置
     * 
     * @param method      方法
     * @param targetClass 目标类
     * @return 权限配置
     */
    protected DataPermission getDataPermission(Method method, Class<?> targetClass) {
        // 优先使用方法级注解
        DataPermission methodPermission = AnnotationUtils.findAnnotation(method, DataPermission.class);
        if (methodPermission != null) {
            return methodPermission;
        }
        
        // 使用类级注解
        return AnnotationUtils.findAnnotation(targetClass, DataPermission.class);
    }
    
    /**
     * 检查权限是否启用
     * 
     * @param permission 权限配置
     * @return 如果启用返回true，否则返回false
     */
    protected boolean isPermissionEnabled(DataPermission permission) {
        String enabled = permission.enabled();
        if (enabled == null || enabled.trim().isEmpty() || "true".equalsIgnoreCase(enabled)) {
            return true;
        }
        
        if ("false".equalsIgnoreCase(enabled)) {
            return false;
        }
        
        // TODO: 支持SpEL表达式
        return evaluateCondition(enabled);
    }
    
    /**
     * 创建权限上下文
     * 
     * @param permission 权限配置
     * @param method     方法
     * @param joinPoint  连接点
     * @return 权限上下文
     */
    protected DataPermissionContext createPermissionContext(DataPermission permission, Method method, 
                                                           ProceedingJoinPoint joinPoint) {
        DataPermissionContext context = DataPermissionContext.create(permission);
        
        // 设置方法信息
        context.setMethodSignature(getMethodSignature(method));
        
        // 设置用户信息
        DataPermissionContext.UserInfo userInfo = getCurrentUserInfo();
        if (userInfo != null) {
            context.setUserInfo(userInfo);
        }
        
        // 设置方法参数
        Object[] args = joinPoint.getArgs();
        String[] paramNames = ((MethodSignature) joinPoint.getSignature()).getParameterNames();
        if (args != null && paramNames != null) {
            for (int i = 0; i < Math.min(args.length, paramNames.length); i++) {
                if (args[i] != null) {
                    context.addParameter(paramNames[i], args[i]);
                }
            }
        }
        
        return context;
    }
    
    /**
     * 获取方法签名
     * 
     * @param method 方法
     * @return 方法签名
     */
    protected String getMethodSignature(Method method) {
        return method.getDeclaringClass().getName() + "." + method.getName();
    }
    
    /**
     * 检查当前用户是否为超级管理员
     * 
     * @return 如果是超级管理员返回true，否则返回false
     */
    protected boolean checkSuperAdmin() {
        // TODO: 实现超级管理员检查逻辑
        return false;
    }
    
    /**
     * 评估条件表达式
     * 
     * @param condition 条件表达式
     * @return 评估结果
     */
    protected boolean evaluateCondition(String condition) {
        // TODO: 实现SpEL表达式评估
        // 这里可以集成Spring的SpEL表达式解析器
        return true;
    }
    
    /**
     * 获取当前用户信息
     * 
     * @return 用户信息
     */
    protected DataPermissionContext.UserInfo getCurrentUserInfo() {
        // TODO: 从AuthContextHolder或其他用户上下文获取用户信息
        DataPermissionContext.UserInfo userInfo = new DataPermissionContext.UserInfo();
        userInfo.setUserId("test-user");
        userInfo.setUsername("Test User");
        userInfo.setDeptId("test-dept");
        userInfo.setSuperAdmin(false);
        return userInfo;
    }
}
