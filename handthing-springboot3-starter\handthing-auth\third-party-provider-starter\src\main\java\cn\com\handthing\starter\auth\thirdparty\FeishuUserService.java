package cn.com.handthing.starter.auth.thirdparty;

import cn.com.handthing.starter.auth.core.UserInfo;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * 飞书用户服务接口
 * <p>
 * 定义飞书认证相关的用户操作接口，包括用户查找、创建、绑定等。
 * 业务系统需要实现此接口来提供具体的用户数据访问逻辑。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
public interface FeishuUserService {

    /**
     * 根据飞书用户ID查找用户
     *
     * @param feishuUserId 飞书用户ID
     * @param appId        应用ID
     * @return 用户信息，如果未找到返回空
     */
    Optional<UserInfo> findByFeishuUserId(String feishuUserId, String appId);

    /**
     * 根据飞书UnionID查找用户
     *
     * @param unionId 飞书UnionID
     * @return 用户信息，如果未找到返回空
     */
    Optional<UserInfo> findByFeishuUnionId(String unionId);

    /**
     * 根据用户ID查找用户
     *
     * @param userId 用户ID
     * @return 用户信息，如果未找到返回空
     */
    Optional<UserInfo> findById(String userId);

    /**
     * 根据手机号查找用户
     *
     * @param mobile 手机号
     * @return 用户信息，如果未找到返回空
     */
    Optional<UserInfo> findByMobile(String mobile);

    /**
     * 根据邮箱查找用户
     *
     * @param email 邮箱
     * @return 用户信息，如果未找到返回空
     */
    Optional<UserInfo> findByEmail(String email);

    /**
     * 创建新用户（基于飞书信息）
     *
     * @param feishuUserInfo 飞书用户信息
     * @param appId          应用ID
     * @return 创建的用户信息
     */
    UserInfo createUser(FeishuApiService.FeishuUserInfoResult feishuUserInfo, String appId);

    /**
     * 绑定飞书用户
     *
     * @param userId       用户ID
     * @param feishuUserId 飞书用户ID
     * @param unionId      飞书UnionID
     * @param appId        应用ID
     * @return 如果绑定成功返回true，否则返回false
     */
    boolean bindFeishuUser(String userId, String feishuUserId, String unionId, String appId);

    /**
     * 解绑飞书用户
     *
     * @param userId 用户ID
     * @param appId  应用ID
     * @return 如果解绑成功返回true，否则返回false
     */
    boolean unbindFeishuUser(String userId, String appId);

    /**
     * 检查用户状态是否有效
     *
     * @param userInfo 用户信息
     * @return 如果用户状态有效返回true，否则返回false
     */
    boolean isUserValid(UserInfo userInfo);

    /**
     * 检查用户是否被锁定
     *
     * @param userInfo 用户信息
     * @return 如果用户被锁定返回true，否则返回false
     */
    boolean isUserLocked(UserInfo userInfo);

    /**
     * 检查用户是否被禁用
     *
     * @param userInfo 用户信息
     * @return 如果用户被禁用返回true，否则返回false
     */
    boolean isUserDisabled(UserInfo userInfo);

    /**
     * 检查账户是否过期
     *
     * @param userInfo 用户信息
     * @return 如果账户过期返回true，否则返回false
     */
    boolean isAccountExpired(UserInfo userInfo);

    /**
     * 更新用户最后登录信息
     *
     * @param userId    用户ID
     * @param loginTime 登录时间
     * @param ipAddress IP地址
     * @param userAgent 用户代理
     */
    void updateLastLoginInfo(String userId, LocalDateTime loginTime, String ipAddress, String userAgent);

    /**
     * 记录登录失败
     *
     * @param feishuUserId 飞书用户ID
     * @param appId        应用ID
     * @param ipAddress    IP地址
     * @param reason       失败原因
     */
    void recordLoginFailure(String feishuUserId, String appId, String ipAddress, String reason);

    /**
     * 清除登录失败记录
     *
     * @param feishuUserId 飞书用户ID
     * @param appId        应用ID
     */
    void clearLoginFailures(String feishuUserId, String appId);

    /**
     * 获取登录失败次数
     *
     * @param feishuUserId 飞书用户ID
     * @param appId        应用ID
     * @return 登录失败次数
     */
    int getLoginFailureCount(String feishuUserId, String appId);

    /**
     * 锁定用户
     *
     * @param userId    用户ID
     * @param reason    锁定原因
     * @param lockUntil 锁定到期时间
     */
    void lockUser(String userId, String reason, LocalDateTime lockUntil);

    /**
     * 解锁用户
     *
     * @param userId 用户ID
     */
    void unlockUser(String userId);

    /**
     * 检查是否为首次登录
     *
     * @param userInfo 用户信息
     * @return 如果是首次登录返回true，否则返回false
     */
    default boolean isFirstLogin(UserInfo userInfo) {
        return userInfo.getLastLoginTime() == null;
    }

    /**
     * 检查飞书用户是否已绑定
     *
     * @param feishuUserId 飞书用户ID
     * @param appId        应用ID
     * @return 如果已绑定返回true，否则返回false
     */
    default boolean isFeishuUserBound(String feishuUserId, String appId) {
        return findByFeishuUserId(feishuUserId, appId).isPresent();
    }

    /**
     * 生成用户ID
     *
     * @param feishuUserId 飞书用户ID
     * @param appId        应用ID
     * @return 用户ID
     */
    default String generateUserId(String feishuUserId, String appId) {
        return "feishu_" + appId + "_" + feishuUserId + "_" + System.currentTimeMillis();
    }

    /**
     * 生成默认用户名
     *
     * @param feishuUserId 飞书用户ID
     * @param appId        应用ID
     * @return 默认用户名
     */
    default String generateDefaultUsername(String feishuUserId, String appId) {
        return "feishu_" + feishuUserId;
    }

    /**
     * 获取默认角色
     *
     * @return 默认角色列表
     */
    default java.util.List<String> getDefaultRoles() {
        return java.util.List.of("USER", "FEISHU_USER");
    }

    /**
     * 获取默认权限
     *
     * @return 默认权限列表
     */
    default java.util.List<String> getDefaultPermissions() {
        return java.util.List.of("READ", "FEISHU_ACCESS");
    }

    /**
     * 验证飞书用户ID格式
     *
     * @param feishuUserId 飞书用户ID
     * @return 如果格式正确返回true，否则返回false
     */
    default boolean isValidFeishuUserId(String feishuUserId) {
        return feishuUserId != null && feishuUserId.trim().length() > 0 && feishuUserId.length() <= 64;
    }

    /**
     * 验证应用ID格式
     *
     * @param appId 应用ID
     * @return 如果格式正确返回true，否则返回false
     */
    default boolean isValidAppId(String appId) {
        return appId != null && appId.trim().length() > 0 && appId.length() <= 64;
    }

    /**
     * 构建飞书用户唯一标识
     *
     * @param feishuUserId 飞书用户ID
     * @param appId        应用ID
     * @return 唯一标识
     */
    default String buildFeishuUserKey(String feishuUserId, String appId) {
        return appId + ":" + feishuUserId;
    }

    /**
     * 根据员工类型获取描述
     *
     * @param employeeType 员工类型代码
     * @return 员工类型描述
     */
    default String getEmployeeTypeDescription(String employeeType) {
        if ("1".equals(employeeType)) {
            return "正式员工";
        } else if ("2".equals(employeeType)) {
            return "实习生";
        } else if ("3".equals(employeeType)) {
            return "外包";
        } else if ("4".equals(employeeType)) {
            return "劳务";
        } else if ("5".equals(employeeType)) {
            return "顾问";
        } else {
            return "其他";
        }
    }

    /**
     * 根据状态代码获取状态描述
     *
     * @param status 状态代码
     * @return 状态描述
     */
    default String getStatusDescription(String status) {
        if ("1".equals(status)) {
            return "激活";
        } else if ("2".equals(status)) {
            return "未激活";
        } else if ("3".equals(status)) {
            return "停用";
        } else if ("4".equals(status)) {
            return "删除";
        } else {
            return "未知";
        }
    }

    /**
     * 构建部门信息字符串
     *
     * @param departmentIds 部门ID数组
     * @return 部门信息字符串
     */
    default String buildDepartmentInfo(String[] departmentIds) {
        if (departmentIds == null || departmentIds.length == 0) {
            return null;
        }
        return String.join(",", departmentIds);
    }
}
