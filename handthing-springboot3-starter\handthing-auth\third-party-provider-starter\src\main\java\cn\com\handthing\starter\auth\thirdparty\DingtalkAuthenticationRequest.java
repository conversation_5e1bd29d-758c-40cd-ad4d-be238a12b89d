package cn.com.handthing.starter.auth.thirdparty;

import cn.com.handthing.starter.auth.core.AuthenticationRequest;
import cn.com.handthing.starter.auth.core.GrantType;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 钉钉认证请求
 * <p>
 * 用于钉钉OAuth2认证的请求对象，包含授权码、应用信息等认证信息。
 * 支持钉钉扫码登录和网页授权登录。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DingtalkAuthenticationRequest extends AuthenticationRequest {

    /**
     * 钉钉授权码
     */
    private String code;

    /**
     * 应用Key
     */
    private String appKey;

    /**
     * 应用密钥
     */
    private String appSecret;

    /**
     * 企业ID（可选）
     */
    private String corpId;

    /**
     * 重定向URI
     */
    private String redirectUri;

    /**
     * 状态参数
     */
    private String state;

    /**
     * 授权类型（web-网页授权，scan-扫码登录）
     */
    private String authType = "web";

    /**
     * 是否自动注册（如果用户不存在）
     */
    private Boolean autoRegister = true;

    /**
     * 默认构造函数
     */
    public DingtalkAuthenticationRequest() {
        super(GrantType.DINGTALK);
    }

    /**
     * 构造函数
     *
     * @param code   授权码
     * @param appKey 应用Key
     */
    public DingtalkAuthenticationRequest(String code, String appKey) {
        super(GrantType.DINGTALK);
        this.code = code;
        this.appKey = appKey;
    }

    /**
     * 构造函数
     *
     * @param code      授权码
     * @param appKey    应用Key
     * @param appSecret 应用密钥
     */
    public DingtalkAuthenticationRequest(String code, String appKey, String appSecret) {
        super(GrantType.DINGTALK);
        this.code = code;
        this.appKey = appKey;
        this.appSecret = appSecret;
    }

    @Override
    public String getAuthenticationIdentifier() {
        return appKey + ":" + corpId;
    }

    @Override
    public Object getCredentials() {
        return code;
    }

    @Override
    public boolean isValid() {
        return super.isValid() && 
               code != null && !code.trim().isEmpty() &&
               appKey != null && !appKey.trim().isEmpty();
    }

    /**
     * 判断是否为网页授权类型
     *
     * @return 如果是网页授权返回true，否则返回false
     */
    public boolean isWebAuth() {
        return "web".equals(authType);
    }

    /**
     * 判断是否为扫码登录类型
     *
     * @return 如果是扫码登录返回true，否则返回false
     */
    public boolean isScanAuth() {
        return "scan".equals(authType);
    }

    /**
     * 获取完整的应用标识
     *
     * @return 应用标识
     */
    public String getAppIdentifier() {
        return appKey + "_" + corpId;
    }

    /**
     * 创建钉钉认证请求
     *
     * @param code   授权码
     * @param appKey 应用Key
     * @return 钉钉认证请求
     */
    public static DingtalkAuthenticationRequest of(String code, String appKey) {
        return new DingtalkAuthenticationRequest(code, appKey);
    }

    /**
     * 创建网页授权的钉钉认证请求
     *
     * @param code        授权码
     * @param appKey      应用Key
     * @param appSecret   应用密钥
     * @param redirectUri 重定向URI
     * @return 钉钉认证请求
     */
    public static DingtalkAuthenticationRequest forWebAuth(String code, String appKey, 
                                                          String appSecret, String redirectUri) {
        DingtalkAuthenticationRequest request = new DingtalkAuthenticationRequest(code, appKey, appSecret);
        request.setAuthType("web");
        request.setRedirectUri(redirectUri);
        return request;
    }

    /**
     * 创建扫码登录的钉钉认证请求
     *
     * @param code      授权码
     * @param appKey    应用Key
     * @param appSecret 应用密钥
     * @return 钉钉认证请求
     */
    public static DingtalkAuthenticationRequest forScanAuth(String code, String appKey, String appSecret) {
        DingtalkAuthenticationRequest request = new DingtalkAuthenticationRequest(code, appKey, appSecret);
        request.setAuthType("scan");
        return request;
    }

    /**
     * 创建自动注册的钉钉认证请求
     *
     * @param code   授权码
     * @param appKey 应用Key
     * @return 钉钉认证请求
     */
    public static DingtalkAuthenticationRequest withAutoRegister(String code, String appKey) {
        DingtalkAuthenticationRequest request = new DingtalkAuthenticationRequest(code, appKey);
        request.setAutoRegister(true);
        return request;
    }

    @Override
    public String toString() {
        return String.format("DingtalkAuthenticationRequest{appKey='%s', corpId='%s', authType='%s', autoRegister=%s}",
                appKey, corpId, authType, autoRegister);
    }
}
