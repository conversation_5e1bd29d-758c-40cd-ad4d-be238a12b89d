package cn.com.handthing.starter.auth.thirdparty;

import cn.com.handthing.starter.auth.core.UserInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 默认飞书用户服务实现
 * <p>
 * 提供基于内存的飞书用户服务实现，主要用于测试和演示。
 * 生产环境建议实现自己的FeishuUserService来对接实际的用户数据源。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Service
@ConditionalOnMissingBean(FeishuUserService.class)
public class DefaultFeishuUserService implements FeishuUserService {

    /**
     * 用户存储（按用户ID索引）
     */
    private final Map<String, UserInfo> usersById = new ConcurrentHashMap<>();

    /**
     * 飞书用户ID映射（飞书用户ID -> 用户ID）
     */
    private final Map<String, String> feishuUserMapping = new ConcurrentHashMap<>();

    /**
     * 飞书UnionID映射（UnionID -> 用户ID）
     */
    private final Map<String, String> feishuUnionIdMapping = new ConcurrentHashMap<>();

    /**
     * 手机号映射（手机号 -> 用户ID）
     */
    private final Map<String, String> mobileMapping = new ConcurrentHashMap<>();

    /**
     * 邮箱映射（邮箱 -> 用户ID）
     */
    private final Map<String, String> emailMapping = new ConcurrentHashMap<>();

    /**
     * 登录失败记录
     */
    private final Map<String, LoginFailureRecord> loginFailures = new ConcurrentHashMap<>();

    /**
     * 用户ID生成器
     */
    private final AtomicLong userIdGenerator = new AtomicLong(5000);

    @Override
    public Optional<UserInfo> findByFeishuUserId(String feishuUserId, String appId) {
        String key = buildFeishuUserKey(feishuUserId, appId);
        String userId = feishuUserMapping.get(key);
        return userId != null ? Optional.ofNullable(usersById.get(userId)) : Optional.empty();
    }

    @Override
    public Optional<UserInfo> findByFeishuUnionId(String unionId) {
        String userId = feishuUnionIdMapping.get(unionId);
        return userId != null ? Optional.ofNullable(usersById.get(userId)) : Optional.empty();
    }

    @Override
    public Optional<UserInfo> findById(String userId) {
        return Optional.ofNullable(usersById.get(userId));
    }

    @Override
    public Optional<UserInfo> findByMobile(String mobile) {
        String userId = mobileMapping.get(mobile);
        return userId != null ? Optional.ofNullable(usersById.get(userId)) : Optional.empty();
    }

    @Override
    public Optional<UserInfo> findByEmail(String email) {
        String userId = emailMapping.get(email);
        return userId != null ? Optional.ofNullable(usersById.get(userId)) : Optional.empty();
    }

    @Override
    public UserInfo createUser(FeishuApiService.FeishuUserInfoResult feishuUserInfo, String appId) {
        // 验证参数
        if (!isValidFeishuUserId(feishuUserInfo.getUserId())) {
            throw new RuntimeException("Invalid feishu user id: " + feishuUserInfo.getUserId());
        }

        if (!isValidAppId(appId)) {
            throw new RuntimeException("Invalid app id: " + appId);
        }

        // 检查是否已存在
        String feishuKey = buildFeishuUserKey(feishuUserInfo.getUserId(), appId);
        if (feishuUserMapping.containsKey(feishuKey)) {
            throw new RuntimeException("Feishu user already exists: " + feishuKey);
        }

        // 创建用户
        String userId = String.valueOf(userIdGenerator.incrementAndGet());
        String username = generateDefaultUsername(feishuUserInfo.getUserId(), appId);
        String nickname = feishuUserInfo.getName() != null ? feishuUserInfo.getName() : 
                         generateDefaultNickname(feishuUserInfo.getUserId());

        UserInfo userInfo = UserInfo.builder()
                .userId(userId)
                .username(username)
                .nickname(nickname)
                .phone(feishuUserInfo.getMobile())
                .email(feishuUserInfo.getEmail())
                .avatar(feishuUserInfo.getAvatar())
                .roles(getDefaultRoles())
                .permissions(getDefaultPermissions())
                .status(1)
                .enabled(true)
                .accountNonExpired(true)
                .accountNonLocked(true)
                .credentialsNonExpired(true)
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .build();

        // 添加飞书相关属性
        userInfo.addAttribute("feishu_user_id", feishuUserInfo.getUserId());
        userInfo.addAttribute("feishu_union_id", feishuUserInfo.getUnionId());
        userInfo.addAttribute("feishu_open_id", feishuUserInfo.getOpenId());
        userInfo.addAttribute("app_id", appId);
        userInfo.addAttribute("feishu_name", feishuUserInfo.getName());
        userInfo.addAttribute("feishu_en_name", feishuUserInfo.getEnName());
        userInfo.addAttribute("feishu_nickname", feishuUserInfo.getNickname());
        userInfo.addAttribute("feishu_status", feishuUserInfo.getStatus());
        userInfo.addAttribute("feishu_status_desc", getStatusDescription(feishuUserInfo.getStatus()));
        userInfo.addAttribute("feishu_employee_type", feishuUserInfo.getEmployeeType());
        userInfo.addAttribute("feishu_employee_type_desc", getEmployeeTypeDescription(feishuUserInfo.getEmployeeType()));
        userInfo.addAttribute("feishu_tenant_key", feishuUserInfo.getTenantKey());
        userInfo.addAttribute("feishu_department_ids", feishuUserInfo.getDepartmentIds());
        userInfo.addAttribute("feishu_department_info", buildDepartmentInfo(feishuUserInfo.getDepartmentIds()));

        if (feishuUserInfo.getCustomAttrs() != null) {
            userInfo.addAttribute("feishu_custom_attrs", feishuUserInfo.getCustomAttrs());
        }

        // 存储用户
        usersById.put(userId, userInfo);
        feishuUserMapping.put(feishuKey, userId);

        // 如果有UnionID，也建立映射
        if (feishuUserInfo.getUnionId() != null && !feishuUserInfo.getUnionId().trim().isEmpty()) {
            feishuUnionIdMapping.put(feishuUserInfo.getUnionId(), userId);
        }

        if (feishuUserInfo.getMobile() != null) {
            mobileMapping.put(feishuUserInfo.getMobile(), userId);
        }

        if (feishuUserInfo.getEmail() != null) {
            emailMapping.put(feishuUserInfo.getEmail(), userId);
        }

        log.info("Created new feishu user: userId={}, feishuUserId={}, appId={}, name={}", 
                userId, feishuUserInfo.getUserId(), appId, feishuUserInfo.getName());
        return userInfo;
    }

    @Override
    public boolean bindFeishuUser(String userId, String feishuUserId, String unionId, String appId) {
        UserInfo userInfo = usersById.get(userId);
        if (userInfo == null) {
            log.warn("User not found for binding: {}", userId);
            return false;
        }

        String feishuKey = buildFeishuUserKey(feishuUserId, appId);
        if (feishuUserMapping.containsKey(feishuKey)) {
            log.warn("Feishu user already bound: {}", feishuKey);
            return false;
        }

        // 绑定飞书用户
        feishuUserMapping.put(feishuKey, userId);
        userInfo.addAttribute("feishu_user_id", feishuUserId);
        userInfo.addAttribute("app_id", appId);
        
        if (unionId != null && !unionId.trim().isEmpty()) {
            feishuUnionIdMapping.put(unionId, userId);
            userInfo.addAttribute("feishu_union_id", unionId);
        }
        
        userInfo.setUpdateTime(LocalDateTime.now());

        log.info("Bound feishu user: userId={}, feishuUserId={}, unionId={}, appId={}", userId, feishuUserId, unionId, appId);
        return true;
    }

    @Override
    public boolean unbindFeishuUser(String userId, String appId) {
        UserInfo userInfo = usersById.get(userId);
        if (userInfo == null) {
            return false;
        }

        String feishuUserId = userInfo.getAttribute("feishu_user_id", String.class);
        String unionId = userInfo.getAttribute("feishu_union_id", String.class);
        
        if (feishuUserId == null) {
            return false;
        }

        String feishuKey = buildFeishuUserKey(feishuUserId, appId);
        feishuUserMapping.remove(feishuKey);
        
        if (unionId != null) {
            feishuUnionIdMapping.remove(unionId);
        }
        
        userInfo.getAttributes().remove("feishu_user_id");
        userInfo.getAttributes().remove("feishu_union_id");
        userInfo.getAttributes().remove("feishu_open_id");
        userInfo.getAttributes().remove("app_id");
        userInfo.setUpdateTime(LocalDateTime.now());

        log.info("Unbound feishu user: userId={}, feishuUserId={}, unionId={}, appId={}", userId, feishuUserId, unionId, appId);
        return true;
    }

    @Override
    public boolean isUserValid(UserInfo userInfo) {
        return userInfo != null && Boolean.TRUE.equals(userInfo.getEnabled());
    }

    @Override
    public boolean isUserLocked(UserInfo userInfo) {
        return userInfo != null && Boolean.FALSE.equals(userInfo.getAccountNonLocked());
    }

    @Override
    public boolean isUserDisabled(UserInfo userInfo) {
        return userInfo != null && Boolean.FALSE.equals(userInfo.getEnabled());
    }

    @Override
    public boolean isAccountExpired(UserInfo userInfo) {
        return userInfo != null && Boolean.FALSE.equals(userInfo.getAccountNonExpired());
    }

    @Override
    public void updateLastLoginInfo(String userId, LocalDateTime loginTime, String ipAddress, String userAgent) {
        UserInfo userInfo = usersById.get(userId);
        if (userInfo != null) {
            userInfo.setLastLoginTime(loginTime);
            userInfo.setLastLoginIp(ipAddress);
            userInfo.setUpdateTime(LocalDateTime.now());
            userInfo.addAttribute("lastUserAgent", userAgent);
            log.debug("Updated last login info for user: {}", userId);
        }
    }

    @Override
    public void recordLoginFailure(String feishuUserId, String appId, String ipAddress, String reason) {
        String key = buildFeishuUserKey(feishuUserId, appId);
        LoginFailureRecord record = loginFailures.computeIfAbsent(key, k -> new LoginFailureRecord());
        record.incrementFailureCount();
        record.setLastFailureTime(LocalDateTime.now());
        record.setLastFailureIp(ipAddress);
        record.setLastFailureReason(reason);

        log.debug("Recorded login failure for feishu user: {}, count: {}", key, record.getFailureCount());
    }

    @Override
    public void clearLoginFailures(String feishuUserId, String appId) {
        String key = buildFeishuUserKey(feishuUserId, appId);
        loginFailures.remove(key);
        log.debug("Cleared login failures for feishu user: {}", key);
    }

    @Override
    public int getLoginFailureCount(String feishuUserId, String appId) {
        String key = buildFeishuUserKey(feishuUserId, appId);
        LoginFailureRecord record = loginFailures.get(key);
        return record != null ? record.getFailureCount() : 0;
    }

    @Override
    public void lockUser(String userId, String reason, LocalDateTime lockUntil) {
        UserInfo userInfo = usersById.get(userId);
        if (userInfo != null) {
            userInfo.setAccountNonLocked(false);
            userInfo.setUpdateTime(LocalDateTime.now());
            userInfo.addAttribute("lockReason", reason);
            userInfo.addAttribute("lockUntil", lockUntil);
            log.info("Locked user: {}, reason: {}, until: {}", userId, reason, lockUntil);
        }
    }

    @Override
    public void unlockUser(String userId) {
        UserInfo userInfo = usersById.get(userId);
        if (userInfo != null) {
            userInfo.setAccountNonLocked(true);
            userInfo.setUpdateTime(LocalDateTime.now());
            userInfo.getAttributes().remove("lockReason");
            userInfo.getAttributes().remove("lockUntil");
            log.info("Unlocked user: {}", userId);
        }
    }

    /**
     * 生成默认昵称
     *
     * @param feishuUserId 飞书用户ID
     * @return 默认昵称
     */
    private String generateDefaultNickname(String feishuUserId) {
        return "飞书用户" + feishuUserId.substring(Math.max(0, feishuUserId.length() - 4));
    }

    /**
     * 登录失败记录
     */
    private static class LoginFailureRecord {
        private int failureCount = 0;
        private LocalDateTime lastFailureTime;
        private String lastFailureIp;
        private String lastFailureReason;

        public void incrementFailureCount() {
            this.failureCount++;
        }

        public int getFailureCount() {
            return failureCount;
        }

        public LocalDateTime getLastFailureTime() {
            return lastFailureTime;
        }

        public void setLastFailureTime(LocalDateTime lastFailureTime) {
            this.lastFailureTime = lastFailureTime;
        }

        public String getLastFailureIp() {
            return lastFailureIp;
        }

        public void setLastFailureIp(String lastFailureIp) {
            this.lastFailureIp = lastFailureIp;
        }

        public String getLastFailureReason() {
            return lastFailureReason;
        }

        public void setLastFailureReason(String lastFailureReason) {
            this.lastFailureReason = lastFailureReason;
        }
    }

    /**
     * 获取统计信息
     *
     * @return 统计信息
     */
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new java.util.HashMap<>();
        stats.put("totalUsers", usersById.size());
        stats.put("feishuUsers", feishuUserMapping.size());
        stats.put("feishuUnionIdUsers", feishuUnionIdMapping.size());
        stats.put("mobileUsers", mobileMapping.size());
        stats.put("emailUsers", emailMapping.size());
        stats.put("loginFailures", loginFailures.size());
        
        long enabledUsers = usersById.values().stream()
                .filter(user -> Boolean.TRUE.equals(user.getEnabled()))
                .count();
        stats.put("enabledUsers", enabledUsers);
        
        return stats;
    }
}
