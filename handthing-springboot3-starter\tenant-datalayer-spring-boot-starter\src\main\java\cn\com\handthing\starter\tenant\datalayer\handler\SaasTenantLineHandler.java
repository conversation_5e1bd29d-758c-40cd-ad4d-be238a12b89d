package cn.com.handthing.starter.tenant.datalayer.handler;

import cn.com.handthing.starter.tenant.context.TenantContextHolder;
import cn.com.handthing.starter.tenant.datalayer.config.TenantDatalayerProperties;
import com.baomidou.mybatisplus.extension.plugins.handler.TenantLineHandler;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.StringValue;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Set;

/**
 * SaaS租户行处理器
 * <p>
 * 实现MyBatis-Plus的TenantLineHandler接口，自动在SQL中添加租户条件。
 * 支持忽略表配置和动态租户ID获取。
 * </p>
 * 
 * <h3>功能特性：</h3>
 * <ul>
 *   <li>自动SQL租户条件注入</li>
 *   <li>支持忽略表配置</li>
 *   <li>动态租户ID获取</li>
 *   <li>租户上下文集成</li>
 * </ul>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Component
public class SaasTenantLineHandler implements TenantLineHandler {
    
    /**
     * 租户数据层配置
     */
    @Autowired
    private TenantDatalayerProperties properties;
    
    /**
     * 获取租户ID
     * <p>
     * 从TenantContextHolder获取当前租户ID
     * </p>
     * 
     * @return 租户ID表达式
     */
    @Override
    public Expression getTenantId() {
        try {
            // 从租户上下文获取当前租户ID
            String tenantId = getCurrentTenantId();
            
            if (tenantId == null || tenantId.trim().isEmpty()) {
                log.warn("No tenant ID found in context, using default tenant");
                tenantId = getDefaultTenantId();
            }
            
            log.debug("Using tenant ID for SQL: {}", tenantId);
            return new StringValue(tenantId);
            
        } catch (Exception e) {
            log.error("Failed to get tenant ID, using default tenant", e);
            return new StringValue(getDefaultTenantId());
        }
    }
    
    /**
     * 获取租户字段名
     * 
     * @return 租户字段名
     */
    @Override
    public String getTenantIdColumn() {
        return properties.getTenantIdColumn();
    }
    
    /**
     * 判断是否忽略租户处理
     * <p>
     * 根据配置的忽略表列表判断是否需要跳过租户处理
     * </p>
     * 
     * @param tableName 表名
     * @return 如果需要忽略返回true，否则返回false
     */
    @Override
    public boolean ignoreTable(String tableName) {
        if (tableName == null || tableName.trim().isEmpty()) {
            return true;
        }
        
        // 检查是否在忽略表列表中
        Set<String> ignoreTables = properties.getIgnoreTables();
        if (ignoreTables != null && ignoreTables.contains(tableName.toLowerCase())) {
            log.debug("Ignoring tenant processing for table: {}", tableName);
            return true;
        }
        
        // 检查表名前缀
        Set<String> ignoreTablePrefixes = properties.getIgnoreTablePrefixes();
        if (ignoreTablePrefixes != null) {
            String lowerTableName = tableName.toLowerCase();
            for (String prefix : ignoreTablePrefixes) {
                if (lowerTableName.startsWith(prefix.toLowerCase())) {
                    log.debug("Ignoring tenant processing for table with prefix: {} -> {}", prefix, tableName);
                    return true;
                }
            }
        }
        
        // 检查系统表
        if (isSystemTable(tableName)) {
            log.debug("Ignoring tenant processing for system table: {}", tableName);
            return true;
        }
        
        log.debug("Applying tenant processing for table: {}", tableName);
        return false;
    }
    
    /**
     * 从租户上下文获取当前租户ID
     * 
     * @return 当前租户ID，如果不存在返回null
     */
    protected String getCurrentTenantId() {
        try {
            return TenantContextHolder.getTenantId();
        } catch (Exception e) {
            log.debug("Failed to get tenant ID from TenantContextHolder", e);
            return null;
        }
    }
    
    /**
     * 获取默认租户ID
     * 
     * @return 默认租户ID
     */
    protected String getDefaultTenantId() {
        String defaultTenantId = properties.getDefaultTenantId();
        return defaultTenantId != null ? defaultTenantId : "default";
    }
    
    /**
     * 判断是否为系统表
     * <p>
     * 系统表通常不需要租户隔离
     * </p>
     * 
     * @param tableName 表名
     * @return 如果是系统表返回true，否则返回false
     */
    protected boolean isSystemTable(String tableName) {
        if (tableName == null) {
            return false;
        }
        
        String lowerTableName = tableName.toLowerCase();
        
        // 常见的系统表前缀
        String[] systemPrefixes = {
                "sys_", "system_", "config_", "dict_", "menu_", "role_", "permission_",
                "tenant_", "user_", "auth_", "log_", "monitor_", "schedule_"
        };
        
        for (String prefix : systemPrefixes) {
            if (lowerTableName.startsWith(prefix)) {
                return true;
            }
        }
        
        // 常见的系统表名
        String[] systemTables = {
                "users", "roles", "permissions", "menus", "configs", "dictionaries",
                "tenants", "organizations", "departments", "logs", "audit_logs"
        };
        
        for (String systemTable : systemTables) {
            if (lowerTableName.equals(systemTable)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 检查租户上下文是否可用
     * 
     * @return 如果可用返回true，否则返回false
     */
    public boolean isTenantContextAvailable() {
        try {
            String tenantId = getCurrentTenantId();
            return tenantId != null && !tenantId.trim().isEmpty();
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 获取当前租户信息摘要
     * 
     * @return 租户信息摘要
     */
    public String getCurrentTenantSummary() {
        try {
            String tenantId = getCurrentTenantId();
            if (tenantId == null || tenantId.trim().isEmpty()) {
                return "No tenant context";
            }
            
            return String.format("Tenant{id='%s', column='%s'}", tenantId, getTenantIdColumn());
        } catch (Exception e) {
            return "Tenant context error: " + e.getMessage();
        }
    }
    
    /**
     * 验证租户配置
     * 
     * @return 验证结果，如果配置有效返回空字符串，否则返回错误信息
     */
    public String validateConfiguration() {
        StringBuilder errors = new StringBuilder();
        
        if (properties == null) {
            errors.append("TenantDatalayerProperties is null; ");
        } else {
            if (properties.getTenantIdColumn() == null || properties.getTenantIdColumn().trim().isEmpty()) {
                errors.append("Tenant ID column is not configured; ");
            }
        }
        
        if (!isTenantContextAvailable()) {
            errors.append("Tenant context is not available; ");
        }
        
        return errors.toString();
    }
    
    /**
     * 强制设置租户ID（用于特殊场景）
     * <p>
     * 注意：此方法会绕过正常的租户上下文，仅在特殊情况下使用
     * </p>
     * 
     * @param tenantId 租户ID
     * @return 租户ID表达式
     */
    public Expression createTenantIdExpression(String tenantId) {
        if (tenantId == null || tenantId.trim().isEmpty()) {
            tenantId = getDefaultTenantId();
        }
        return new StringValue(tenantId);
    }
    
    /**
     * 检查表是否需要租户处理
     * <p>
     * 提供给外部调用的公共方法
     * </p>
     * 
     * @param tableName 表名
     * @return 如果需要租户处理返回true，否则返回false
     */
    public boolean needsTenantProcessing(String tableName) {
        return !ignoreTable(tableName);
    }
    
    /**
     * 获取忽略表的统计信息
     * 
     * @return 忽略表统计信息
     */
    public String getIgnoreTablesSummary() {
        StringBuilder sb = new StringBuilder();
        sb.append("Ignore Tables Summary: ");
        
        Set<String> ignoreTables = properties.getIgnoreTables();
        if (ignoreTables != null && !ignoreTables.isEmpty()) {
            sb.append("tables=").append(ignoreTables.size()).append(" ");
        }
        
        Set<String> ignoreTablePrefixes = properties.getIgnoreTablePrefixes();
        if (ignoreTablePrefixes != null && !ignoreTablePrefixes.isEmpty()) {
            sb.append("prefixes=").append(ignoreTablePrefixes.size()).append(" ");
        }
        
        return sb.toString();
    }
}
