<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - HandThing认证测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <div class="card shadow mt-5">
                    <div class="card-header bg-primary text-white text-center">
                        <h4 class="mb-0">
                            <i class="bi bi-shield-check"></i>
                            HandThing认证测试
                        </h4>
                    </div>
                    <div class="card-body p-4">
                        <!-- 错误信息显示 -->
                        <div th:if="${error}" class="alert alert-danger">
                            <i class="bi bi-exclamation-triangle"></i>
                            <span th:text="${error}">错误信息</span>
                        </div>

                        <!-- 登出成功信息 -->
                        <div th:if="${param.logout}" class="alert alert-success">
                            <i class="bi bi-check-circle"></i>
                            您已成功登出
                        </div>

                        <!-- 认证方式选择 -->
                        <ul class="nav nav-tabs" id="authTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="password-tab" data-bs-toggle="tab" 
                                        data-bs-target="#password" type="button" role="tab">
                                    <i class="bi bi-key"></i> 密码登录
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="sms-tab" data-bs-toggle="tab" 
                                        data-bs-target="#sms" type="button" role="tab">
                                    <i class="bi bi-phone"></i> 短信登录
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="wecom-tab" data-bs-toggle="tab"
                                        data-bs-target="#wecom" type="button" role="tab">
                                    <i class="bi bi-wechat"></i> 企业微信
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="dingtalk-tab" data-bs-toggle="tab"
                                        data-bs-target="#dingtalk" type="button" role="tab">
                                    <i class="bi bi-chat-dots"></i> 钉钉
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="wechat-tab" data-bs-toggle="tab"
                                        data-bs-target="#wechat" type="button" role="tab">
                                    <i class="bi bi-wechat"></i> 微信
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="feishu-tab" data-bs-toggle="tab"
                                        data-bs-target="#feishu" type="button" role="tab">
                                    <i class="bi bi-chat-square-text"></i> 飞书
                                </button>
                            </li>
                        </ul>

                        <div class="tab-content mt-3" id="authTabContent">
                            <!-- 密码登录 -->
                            <div class="tab-pane fade show active" id="password" role="tabpanel">
                                <form id="passwordForm">
                                    <div class="mb-3">
                                        <label for="username" class="form-label">用户名</label>
                                        <input type="text" class="form-control" id="username" name="username" 
                                               placeholder="请输入用户名" required>
                                        <div class="form-text">
                                            测试账号：admin, user, test
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="password" class="form-label">密码</label>
                                        <input type="password" class="form-control" id="password" name="password" 
                                               placeholder="请输入密码" required>
                                        <div class="form-text">
                                            对应密码：admin123, user123, test123
                                        </div>
                                    </div>
                                    <div class="mb-3 form-check">
                                        <input type="checkbox" class="form-check-input" id="rememberMe" name="rememberMe">
                                        <label class="form-check-label" for="rememberMe">记住我</label>
                                    </div>
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="bi bi-box-arrow-in-right"></i> 登录
                                    </button>
                                </form>
                            </div>

                            <!-- 短信登录 -->
                            <div class="tab-pane fade" id="sms" role="tabpanel">
                                <form id="smsForm">
                                    <div class="mb-3">
                                        <label for="phone" class="form-label">手机号</label>
                                        <input type="tel" class="form-control" id="phone" name="phone" 
                                               placeholder="请输入手机号" pattern="^1[3-9]\d{9}$" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="smsCode" class="form-label">验证码</label>
                                        <div class="input-group">
                                            <input type="text" class="form-control" id="smsCode" name="smsCode" 
                                                   placeholder="请输入验证码" required>
                                            <button type="button" class="btn btn-outline-secondary" id="sendSmsBtn">
                                                发送验证码
                                            </button>
                                        </div>
                                    </div>
                                    <div class="mb-3 form-check">
                                        <input type="checkbox" class="form-check-input" id="autoRegister" name="autoRegister" checked>
                                        <label class="form-check-label" for="autoRegister">自动注册新用户</label>
                                    </div>
                                    <button type="submit" class="btn btn-success w-100">
                                        <i class="bi bi-phone"></i> 短信登录
                                    </button>
                                </form>
                            </div>

                            <!-- 企业微信登录 -->
                            <div class="tab-pane fade" id="wecom" role="tabpanel">
                                <div class="text-center">
                                    <div class="mb-3">
                                        <i class="bi bi-wechat text-success" style="font-size: 4rem;"></i>
                                    </div>
                                    <p class="text-muted mb-3">使用企业微信账号登录</p>
                                    <button type="button" class="btn btn-success w-100" id="wecomLoginBtn">
                                        <i class="bi bi-qr-code"></i> 企业微信登录
                                    </button>
                                    <div class="mt-3">
                                        <small class="text-muted">
                                            支持扫码登录和网页授权登录
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <!-- 钉钉登录 -->
                            <div class="tab-pane fade" id="dingtalk" role="tabpanel">
                                <div class="text-center">
                                    <div class="mb-3">
                                        <i class="bi bi-chat-dots text-primary" style="font-size: 4rem;"></i>
                                    </div>
                                    <p class="text-muted mb-3">使用钉钉账号登录</p>
                                    <button type="button" class="btn btn-primary w-100" id="dingtalkLoginBtn">
                                        <i class="bi bi-qr-code"></i> 钉钉登录
                                    </button>
                                    <div class="mt-3">
                                        <small class="text-muted">
                                            支持扫码登录和网页授权登录
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <!-- 微信登录 -->
                            <div class="tab-pane fade" id="wechat" role="tabpanel">
                                <div class="text-center">
                                    <div class="mb-3">
                                        <i class="bi bi-wechat text-success" style="font-size: 4rem;"></i>
                                    </div>
                                    <p class="text-muted mb-3">使用微信账号登录</p>
                                    <button type="button" class="btn btn-success w-100" id="wechatLoginBtn">
                                        <i class="bi bi-qr-code"></i> 微信登录
                                    </button>
                                    <div class="mt-3">
                                        <small class="text-muted">
                                            支持公众号授权和开放平台扫码登录
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <!-- 飞书登录 -->
                            <div class="tab-pane fade" id="feishu" role="tabpanel">
                                <div class="text-center">
                                    <div class="mb-3">
                                        <i class="bi bi-chat-square-text text-info" style="font-size: 4rem;"></i>
                                    </div>
                                    <p class="text-muted mb-3">使用飞书账号登录</p>
                                    <button type="button" class="btn btn-info w-100" id="feishuLoginBtn">
                                        <i class="bi bi-qr-code"></i> 飞书登录
                                    </button>
                                    <div class="mt-3">
                                        <small class="text-muted">
                                            支持网页授权和移动端授权
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="text-center mt-4">
                            <a href="/" class="text-decoration-none">
                                <i class="bi bi-arrow-left"></i> 返回首页
                            </a>
                        </div>
                    </div>
                </div>

                <!-- API测试区域 -->
                <div class="card shadow mt-3">
                    <div class="card-header">
                        <h6 class="mb-0">API测试</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <button class="btn btn-outline-info btn-sm w-100 mb-2" onclick="testGrantTypes()">
                                    获取支持的认证方式
                                </button>
                            </div>
                            <div class="col-md-4">
                                <button class="btn btn-outline-info btn-sm w-100 mb-2" onclick="testSmsStats()">
                                    短信统计信息
                                </button>
                            </div>
                            <div class="col-md-4">
                                <button class="btn btn-outline-info btn-sm w-100 mb-2" onclick="testWecomStats()">
                                    企业微信统计
                                </button>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-outline-info btn-sm w-100 mb-2" onclick="testDingtalkStats()">
                                    钉钉统计信息
                                </button>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-outline-info btn-sm w-100 mb-2" onclick="testWechatStats()">
                                    微信统计信息
                                </button>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-outline-info btn-sm w-100 mb-2" onclick="testFeishuStats()">
                                    飞书统计信息
                                </button>
                            </div>
                        </div>
                        <div id="apiResult" class="mt-2" style="display: none;">
                            <pre class="bg-light p-2 rounded small"></pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 密码登录
        document.getElementById('passwordForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const formData = new FormData(this);
            const data = {
                grant_type: 'password',
                username: formData.get('username'),
                password: formData.get('password'),
                remember_me: formData.get('rememberMe') === 'on'
            };
            
            login(data);
        });

        // 短信登录
        document.getElementById('smsForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const formData = new FormData(this);
            const data = {
                grant_type: 'sms_code',
                phone: formData.get('phone'),
                sms_code: formData.get('smsCode'),
                auto_register: formData.get('autoRegister') === 'on'
            };
            
            login(data);
        });

        // 发送短信验证码
        document.getElementById('sendSmsBtn').addEventListener('click', function() {
            const phone = document.getElementById('phone').value;
            if (!phone) {
                alert('请先输入手机号');
                return;
            }

            const btn = this;
            btn.disabled = true;
            btn.textContent = '发送中...';

            fetch('/auth/sms/send', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    phone: phone,
                    code_type: 'login'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('验证码发送成功！（测试环境，请查看控制台日志）');
                    startCountdown(btn);
                } else {
                    alert('发送失败：' + data.error_description);
                    btn.disabled = false;
                    btn.textContent = '发送验证码';
                }
            })
            .catch(error => {
                alert('发送失败：' + error.message);
                btn.disabled = false;
                btn.textContent = '发送验证码';
            });
        });

        // 企业微信登录
        document.getElementById('wecomLoginBtn').addEventListener('click', function() {
            // 生成企业微信授权URL
            fetch('/auth/wecom/auth-url', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    corp_id: 'test_corp_id',
                    agent_id: 'test_agent_id',
                    redirect_uri: window.location.origin + '/wecom/callback',
                    auth_type: 'web'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 跳转到企业微信授权页面
                    window.location.href = data.auth_url;
                } else {
                    alert('生成授权URL失败：' + data.error_description);
                }
            })
            .catch(error => {
                alert('企业微信登录失败：' + error.message);
            });
        });

        // 钉钉登录
        document.getElementById('dingtalkLoginBtn').addEventListener('click', function() {
            // 生成钉钉授权URL
            fetch('/auth/dingtalk/auth-url', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    app_key: 'test_app_key',
                    redirect_uri: window.location.origin + '/dingtalk/callback',
                    auth_type: 'web'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 跳转到钉钉授权页面
                    window.location.href = data.auth_url;
                } else {
                    alert('生成授权URL失败：' + data.error_description);
                }
            })
            .catch(error => {
                alert('钉钉登录失败：' + error.message);
            });
        });

        // 微信登录
        document.getElementById('wechatLoginBtn').addEventListener('click', function() {
            // 生成微信授权URL
            fetch('/auth/wechat/auth-url', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    app_id: 'test_app_id',
                    redirect_uri: window.location.origin + '/wechat/callback',
                    auth_type: 'web',
                    scope: 'snsapi_userinfo'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 跳转到微信授权页面
                    window.location.href = data.auth_url;
                } else {
                    alert('生成授权URL失败：' + data.error_description);
                }
            })
            .catch(error => {
                alert('微信登录失败：' + error.message);
            });
        });

        // 飞书登录
        document.getElementById('feishuLoginBtn').addEventListener('click', function() {
            // 生成飞书授权URL
            fetch('/auth/feishu/auth-url', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    app_id: 'test_app_id',
                    redirect_uri: window.location.origin + '/feishu/callback',
                    auth_type: 'web'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 跳转到飞书授权页面
                    window.location.href = data.auth_url;
                } else {
                    alert('生成授权URL失败：' + data.error_description);
                }
            })
            .catch(error => {
                alert('飞书登录失败：' + error.message);
            });
        });

        // 通用登录函数
        function login(data) {
            fetch('/auth/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 保存JWT token到localStorage
                    if (data.access_token) {
                        localStorage.setItem('access_token', data.access_token);
                        console.log('JWT token saved:', data.access_token);
                    }

                    // 保存refresh token（如果有）
                    if (data.refresh_token) {
                        localStorage.setItem('refresh_token', data.refresh_token);
                    }

                    // 保存用户信息（如果有）
                    if (data.user_info) {
                        localStorage.setItem('user_info', JSON.stringify(data.user_info));
                    }

                    alert('登录成功！');
                    window.location.href = '/';
                } else {
                    alert('登录失败：' + data.error_description);
                }
            })
            .catch(error => {
                alert('登录失败：' + error.message);
            });
        }

        // 倒计时
        function startCountdown(btn) {
            let count = 60;
            const timer = setInterval(() => {
                btn.textContent = `${count}秒后重发`;
                count--;
                if (count < 0) {
                    clearInterval(timer);
                    btn.disabled = false;
                    btn.textContent = '发送验证码';
                }
            }, 1000);
        }

        // API测试函数
        function testGrantTypes() {
            fetch('/auth/grant-types')
                .then(response => response.json())
                .then(data => showApiResult(data))
                .catch(error => showApiResult({error: error.message}));
        }

        function testSmsStats() {
            fetch('/auth/sms/stats?phone=13800138000')
                .then(response => response.json())
                .then(data => showApiResult(data))
                .catch(error => showApiResult({error: error.message}));
        }

        function testWecomStats() {
            fetch('/auth/wecom/stats')
                .then(response => response.json())
                .then(data => showApiResult(data))
                .catch(error => showApiResult({error: error.message}));
        }

        function testDingtalkStats() {
            fetch('/auth/dingtalk/stats')
                .then(response => response.json())
                .then(data => showApiResult(data))
                .catch(error => showApiResult({error: error.message}));
        }

        function testWechatStats() {
            fetch('/auth/wechat/stats')
                .then(response => response.json())
                .then(data => showApiResult(data))
                .catch(error => showApiResult({error: error.message}));
        }

        function testFeishuStats() {
            fetch('/auth/feishu/stats')
                .then(response => response.json())
                .then(data => showApiResult(data))
                .catch(error => showApiResult({error: error.message}));
        }

        // 通用API调用函数，自动包含JWT token
        function apiCall(url, options = {}) {
            const token = localStorage.getItem('access_token');
            const headers = {
                'Content-Type': 'application/json',
                ...options.headers
            };

            // 如果有token，添加Authorization头
            if (token) {
                headers['Authorization'] = `Bearer ${token}`;
            }

            return fetch(url, {
                ...options,
                headers: headers
            });
        }

        function showApiResult(data) {
            const resultDiv = document.getElementById('apiResult');
            const pre = resultDiv.querySelector('pre');
            pre.textContent = JSON.stringify(data, null, 2);
            resultDiv.style.display = 'block';
        }
    </script>
</body>
</html>
