package cn.com.handthing.starter.connector.bilibili.config;

import cn.com.handthing.starter.connector.config.PlatformConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * Bilibili连接器配置
 * <p>
 * 支持Bilibili开放平台的配置，包括AppKey、AppSecret等认证信息。
 * 支持视频内容创作、直播互动等功能的配置。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ConfigurationProperties(prefix = "handthing.connector.bilibili")
public class BilibiliConfig extends PlatformConfig {

    /**
     * Bilibili应用Key (App Key)
     */
    private String appKey;

    /**
     * Bilibili应用密钥 (App Secret)
     */
    private String appSecret;

    /**
     * 应用类型
     * personal: 个人创作者
     * enterprise: 企业用户
     * mcn: MCN机构
     */
    private String appType = "personal";

    /**
     * 是否为MCN机构模式
     */
    private boolean mcnMode = false;

    /**
     * 是否启用视频上传功能
     */
    private boolean videoUploadEnabled = true;

    /**
     * 是否启用直播功能
     */
    private boolean liveEnabled = false;

    /**
     * 是否启用数据分析功能
     */
    private boolean analyticsEnabled = true;

    /**
     * 是否启用社区互动功能
     */
    private boolean communityEnabled = true;

    /**
     * Bilibili API基础URL
     */
    private String apiBaseUrl = "https://api.bilibili.com";

    /**
     * 是否使用测试环境
     */
    private boolean testMode = false;

    /**
     * 测试环境API基础URL
     */
    private String testApiBaseUrl = "https://api-test.bilibili.com";

    /**
     * 授权范围
     */
    private String scope = "user_info,video.upload,community.read,analytics.read";

    /**
     * 获取实际使用的API基础URL
     *
     * @return API基础URL
     */
    public String getEffectiveApiBaseUrl() {
        return testMode ? testApiBaseUrl : apiBaseUrl;
    }

    /**
     * 验证配置是否有效
     *
     * @return 如果配置有效返回true，否则返回false
     */
    @Override
    public boolean isValid() {
        if (!super.isValid()) {
            return false;
        }

        // 检查必需的配置项
        if (appKey == null || appKey.trim().isEmpty()) {
            return false;
        }

        if (appSecret == null || appSecret.trim().isEmpty()) {
            return false;
        }

        return true;
    }

    /**
     * 获取配置描述信息
     *
     * @return 配置描述
     */
    public String getConfigDescription() {
        return String.format("Bilibili Config - AppKey: %s, AppType: %s, MCN: %s, Test: %s",
                appKey, appType, mcnMode, testMode);
    }

    /**
     * 是否为个人创作者
     *
     * @return 如果是个人创作者返回true，否则返回false
     */
    public boolean isPersonalCreator() {
        return "personal".equals(appType);
    }

    /**
     * 是否为企业用户
     *
     * @return 如果是企业用户返回true，否则返回false
     */
    public boolean isEnterpriseUser() {
        return "enterprise".equals(appType);
    }

    /**
     * 是否为MCN机构
     *
     * @return 如果是MCN机构返回true，否则返回false
     */
    public boolean isMcnInstitution() {
        return "mcn".equals(appType) || mcnMode;
    }

    /**
     * 是否启用了视频上传功能
     *
     * @return 如果启用了视频上传功能返回true，否则返回false
     */
    public boolean isVideoUploadAvailable() {
        return videoUploadEnabled && scope != null && scope.contains("video.upload");
    }

    /**
     * 是否启用了直播功能
     *
     * @return 如果启用了直播功能返回true，否则返回false
     */
    public boolean isLiveAvailable() {
        return liveEnabled && scope != null && scope.contains("live");
    }

    /**
     * 是否启用了数据分析功能
     *
     * @return 如果启用了数据分析功能返回true，否则返回false
     */
    public boolean isAnalyticsAvailable() {
        return analyticsEnabled && scope != null && scope.contains("analytics.read");
    }

    /**
     * 是否启用了社区互动功能
     *
     * @return 如果启用了社区互动功能返回true，否则返回false
     */
    public boolean isCommunityAvailable() {
        return communityEnabled && scope != null && scope.contains("community.read");
    }
}
