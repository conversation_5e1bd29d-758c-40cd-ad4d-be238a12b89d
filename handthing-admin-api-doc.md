# HandThing Admin API接口文档

## 1. 接口概述

### 1.1 基础信息
- **API版本**: v1
- **基础URL**: `https://api.handthing.com/api/v1`
- **协议**: HTTPS
- **数据格式**: JSON
- **字符编码**: UTF-8

### 1.2 认证方式
- **认证类型**: <PERSON><PERSON> (JWT)
- **请求头**: `Authorization: Bearer {access_token}`
- **Token有效期**: 2小时 (可配置)
- **刷新机制**: 使用refresh_token刷新

### 1.3 通用响应格式
```json
{
    "code": 200,
    "message": "success",
    "data": {},
    "timestamp": 1640995200000,
    "traceId": "abc123def456"
}
```

### 1.4 错误码说明
| 错误码 | 说明 | 描述 |
|--------|------|------|
| 200 | 成功 | 请求成功 |
| 400 | 参数错误 | 请求参数有误 |
| 401 | 未认证 | 需要登录认证 |
| 403 | 无权限 | 权限不足 |
| 404 | 资源不存在 | 请求的资源不存在 |
| 500 | 服务器错误 | 内部服务器错误 |

## 2. 认证授权API

### 2.1 用户登录
```http
POST /api/v1/auth/login
Content-Type: application/json

{
    "username": "admin",
    "password": "123456",
    "captcha": "abcd",
    "captchaKey": "uuid-key",
    "loginType": "password"
}
```

**响应示例:**
```json
{
    "code": 200,
    "message": "登录成功",
    "data": {
        "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "expiresIn": 7200,
        "tokenType": "Bearer",
        "userInfo": {
            "id": 1,
            "username": "admin",
            "nickname": "管理员",
            "avatar": "https://example.com/avatar.jpg",
            "roles": ["admin"],
            "permissions": ["user:read", "user:write"]
        }
    }
}
```

### 2.2 刷新Token
```http
POST /api/v1/auth/refresh
Authorization: Bearer {refresh_token}
```

**响应示例:**
```json
{
    "code": 200,
    "message": "刷新成功",
    "data": {
        "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "expiresIn": 7200
    }
}
```

### 2.3 用户登出
```http
POST /api/v1/auth/logout
Authorization: Bearer {access_token}
```

**响应示例:**
```json
{
    "code": 200,
    "message": "登出成功",
    "data": null
}
```

### 2.4 获取验证码
```http
GET /api/v1/auth/captcha
```

**响应示例:**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "captchaKey": "uuid-key",
        "captchaImage": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..."
    }
}
```

### 2.5 发送短信验证码
```http
POST /api/v1/auth/sms/send
Content-Type: application/json

{
    "phone": "***********",
    "type": "login"
}
```

**响应示例:**
```json
{
    "code": 200,
    "message": "验证码发送成功",
    "data": null
}
```

### 2.6 第三方登录授权URL
```http
GET /api/v1/auth/oauth/wecom/authorize?redirectUri=https://example.com/callback
```

**响应示例:**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "authorizeUrl": "https://open.weixin.qq.com/connect/oauth2/authorize?...",
        "state": "random-state"
    }
}
```

### 2.7 第三方登录回调
```http
POST /api/v1/auth/oauth/wecom/callback
Content-Type: application/json

{
    "code": "authorization_code",
    "state": "random-state"
}
```

### 2.8 获取当前用户信息
```http
GET /api/v1/auth/userinfo
Authorization: Bearer {access_token}
```

**响应示例:**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "id": 1,
        "username": "admin",
        "nickname": "管理员",
        "email": "<EMAIL>",
        "phone": "***********",
        "avatar": "https://example.com/avatar.jpg",
        "status": 1,
        "roles": [
            {
                "id": 1,
                "roleCode": "admin",
                "roleName": "超级管理员"
            }
        ],
        "permissions": ["user:read", "user:write", "role:read"],
        "departments": [
            {
                "id": 1,
                "deptName": "总经理办公室",
                "isMain": true
            }
        ]
    }
}
```

### 2.9 获取用户菜单
```http
GET /api/v1/auth/menus
Authorization: Bearer {access_token}
```

**响应示例:**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": [
        {
            "id": 1,
            "name": "系统管理",
            "path": "/system",
            "component": "Layout",
            "icon": "system",
            "sort": 1,
            "children": [
                {
                    "id": 2,
                    "name": "用户管理",
                    "path": "/system/user",
                    "component": "system/user/index",
                    "icon": "user",
                    "sort": 1
                }
            ]
        }
    ]
}
```

## 3. 租户管理API

### 3.1 租户列表
```http
GET /api/v1/tenant/tenants?page=1&size=10&keyword=测试&status=1
Authorization: Bearer {access_token}
```

**响应示例:**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "records": [
            {
                "id": 1,
                "tenantCode": "T001",
                "tenantName": "测试租户",
                "companyName": "测试公司",
                "contactName": "张三",
                "contactPhone": "***********",
                "contactEmail": "<EMAIL>",
                "packageName": "企业版",
                "status": 1,
                "expireTime": "2024-12-31 23:59:59",
                "createTime": "2024-01-01 00:00:00"
            }
        ],
        "total": 1,
        "size": 10,
        "current": 1,
        "pages": 1
    }
}
```

### 3.2 创建租户
```http
POST /api/v1/tenant/tenants
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "tenantName": "新租户",
    "companyName": "新公司",
    "contactName": "李四",
    "contactPhone": "13900139000",
    "contactEmail": "<EMAIL>",
    "packageId": 1,
    "expireTime": "2024-12-31 23:59:59",
    "adminUsername": "admin",
    "adminPassword": "123456"
}
```

**响应示例:**
```json
{
    "code": 200,
    "message": "创建成功",
    "data": {
        "id": 2,
        "tenantCode": "T002"
    }
}
```

### 3.3 更新租户
```http
PUT /api/v1/tenant/tenants/1
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "tenantName": "更新后的租户名称",
    "status": 1
}
```

### 3.4 删除租户
```http
DELETE /api/v1/tenant/tenants/1
Authorization: Bearer {access_token}
```

### 3.5 套餐列表
```http
GET /api/v1/tenant/packages?page=1&size=10
Authorization: Bearer {access_token}
```

**响应示例:**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "records": [
            {
                "id": 1,
                "packageName": "企业版",
                "packageCode": "enterprise",
                "description": "企业版套餐",
                "priceMonthly": 999.00,
                "priceYearly": 9999.00,
                "maxUsers": 1000,
                "maxStorage": 107374182400,
                "maxApiCalls": 1000000,
                "permissions": ["user:read", "user:write"],
                "status": 1,
                "createTime": "2024-01-01 00:00:00"
            }
        ],
        "total": 1,
        "size": 10,
        "current": 1,
        "pages": 1
    }
}
```

### 3.6 创建套餐
```http
POST /api/v1/tenant/packages
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "packageName": "标准版",
    "packageCode": "standard",
    "description": "标准版套餐",
    "priceMonthly": 499.00,
    "priceYearly": 4999.00,
    "maxUsers": 100,
    "maxStorage": 10737418240,
    "maxApiCalls": 100000,
    "permissions": ["user:read"]
}
```

## 4. 用户管理API

### 4.1 用户列表
```http
GET /api/v1/user/users?page=1&size=10&keyword=张三&deptId=1&status=1
Authorization: Bearer {access_token}
```

**响应示例:**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "records": [
            {
                "id": 1,
                "username": "zhangsan",
                "nickname": "张三",
                "email": "<EMAIL>",
                "phone": "***********",
                "avatar": "https://example.com/avatar.jpg",
                "status": 1,
                "deptName": "技术部",
                "positionName": "高级工程师",
                "roles": ["developer"],
                "lastLoginTime": "2024-01-01 10:00:00",
                "createTime": "2024-01-01 00:00:00"
            }
        ],
        "total": 1,
        "size": 10,
        "current": 1,
        "pages": 1
    }
}
```

### 4.2 创建用户
```http
POST /api/v1/user/users
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "username": "lisi",
    "nickname": "李四",
    "email": "<EMAIL>",
    "phone": "13900139000",
    "password": "123456",
    "deptId": 1,
    "positionId": 1,
    "roleIds": [1, 2]
}
```

### 4.3 更新用户
```http
PUT /api/v1/user/users/1
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "nickname": "张三三",
    "email": "<EMAIL>",
    "deptId": 2,
    "status": 1
}
```

### 4.4 删除用户
```http
DELETE /api/v1/user/users/1
Authorization: Bearer {access_token}
```

### 4.5 批量导入用户
```http
POST /api/v1/user/users/batch-import
Authorization: Bearer {access_token}
Content-Type: multipart/form-data

file: users.xlsx
```

**响应示例:**
```json
{
    "code": 200,
    "message": "导入完成",
    "data": {
        "successCount": 10,
        "failCount": 2,
        "failList": [
            {
                "row": 3,
                "username": "duplicate",
                "error": "用户名已存在"
            }
        ]
    }
}
```

### 4.6 重置密码
```http
PUT /api/v1/user/users/1/reset-password
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "newPassword": "newpassword123"
}
```

### 4.7 分配角色
```http
PUT /api/v1/user/users/1/roles
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "roleIds": [1, 2, 3]
}
```

## 5. 角色权限API

### 5.1 角色列表
```http
GET /api/v1/user/roles?page=1&size=10&keyword=管理员
Authorization: Bearer {access_token}
```

**响应示例:**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "records": [
            {
                "id": 1,
                "roleCode": "admin",
                "roleName": "超级管理员",
                "description": "系统超级管理员",
                "dataScope": 1,
                "status": 1,
                "userCount": 5,
                "createTime": "2024-01-01 00:00:00"
            }
        ],
        "total": 1,
        "size": 10,
        "current": 1,
        "pages": 1
    }
}
```

### 5.2 创建角色
```http
POST /api/v1/user/roles
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "roleCode": "manager",
    "roleName": "部门经理",
    "description": "部门经理角色",
    "dataScope": 2
}
```

### 5.3 更新角色
```http
PUT /api/v1/user/roles/1
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "roleName": "高级管理员",
    "description": "高级管理员角色"
}
```

### 5.4 删除角色
```http
DELETE /api/v1/user/roles/1
Authorization: Bearer {access_token}
```

### 5.5 权限树
```http
GET /api/v1/user/permissions/tree
Authorization: Bearer {access_token}
```

**响应示例:**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": [
        {
            "id": 1,
            "permissionCode": "system",
            "permissionName": "系统管理",
            "permissionType": 1,
            "children": [
                {
                    "id": 2,
                    "permissionCode": "user:read",
                    "permissionName": "用户查看",
                    "permissionType": 3
                }
            ]
        }
    ]
}
```

### 5.6 分配权限
```http
PUT /api/v1/user/roles/1/permissions
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "permissionIds": [1, 2, 3, 4, 5]
}
```

### 5.7 复制角色
```http
POST /api/v1/user/roles/1/copy
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "newRoleName": "新角色名称"
}
```

## 6. 组织架构API

### 6.1 部门树
```http
GET /api/v1/user/departments/tree
Authorization: Bearer {access_token}
```

**响应示例:**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": [
        {
            "id": 1,
            "deptCode": "ROOT",
            "deptName": "总公司",
            "parentId": 0,
            "leaderName": "张总",
            "phone": "010-12345678",
            "email": "<EMAIL>",
            "sort": 1,
            "status": 1,
            "userCount": 100,
            "children": [
                {
                    "id": 2,
                    "deptCode": "IT",
                    "deptName": "技术部",
                    "parentId": 1,
                    "leaderName": "李经理",
                    "userCount": 20,
                    "children": []
                }
            ]
        }
    ]
}
```

### 6.2 创建部门
```http
POST /api/v1/user/departments
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "deptCode": "SALES",
    "deptName": "销售部",
    "parentId": 1,
    "leaderId": 5,
    "phone": "010-87654321",
    "email": "<EMAIL>",
    "sort": 2
}
```

### 6.3 更新部门
```http
PUT /api/v1/user/departments/1
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "deptName": "技术研发部",
    "leaderId": 6
}
```

### 6.4 删除部门
```http
DELETE /api/v1/user/departments/1
Authorization: Bearer {access_token}
```

### 6.5 职位列表
```http
GET /api/v1/user/positions?page=1&size=10&keyword=工程师
Authorization: Bearer {access_token}
```

**响应示例:**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "records": [
            {
                "id": 1,
                "positionCode": "SE",
                "positionName": "高级软件工程师",
                "description": "负责系统开发和维护",
                "levelRank": 5,
                "status": 1,
                "userCount": 10,
                "createTime": "2024-01-01 00:00:00"
            }
        ],
        "total": 1,
        "size": 10,
        "current": 1,
        "pages": 1
    }
}
```

### 6.6 创建职位
```http
POST /api/v1/user/positions
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "positionCode": "PM",
    "positionName": "项目经理",
    "description": "负责项目管理",
    "levelRank": 6
}
```

## 7. 系统管理API

### 7.1 菜单树
```http
GET /api/v1/system/menus/tree
Authorization: Bearer {access_token}
```

**响应示例:**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": [
        {
            "id": 1,
            "menuCode": "system",
            "menuName": "系统管理",
            "parentId": 0,
            "menuType": 1,
            "path": "/system",
            "component": "Layout",
            "icon": "system",
            "sort": 1,
            "visible": 1,
            "status": 1,
            "children": [
                {
                    "id": 2,
                    "menuCode": "user",
                    "menuName": "用户管理",
                    "parentId": 1,
                    "menuType": 2,
                    "path": "/system/user",
                    "component": "system/user/index",
                    "icon": "user",
                    "sort": 1,
                    "visible": 1,
                    "status": 1,
                    "children": []
                }
            ]
        }
    ]
}
```

### 7.2 创建菜单
```http
POST /api/v1/system/menus
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "menuCode": "role",
    "menuName": "角色管理",
    "parentId": 1,
    "menuType": 2,
    "path": "/system/role",
    "component": "system/role/index",
    "icon": "role",
    "sort": 2,
    "visible": 1
}
```

### 7.3 字典类型列表
```http
GET /api/v1/system/dict/types?page=1&size=10&keyword=状态
Authorization: Bearer {access_token}
```

### 7.4 字典数据列表
```http
GET /api/v1/system/dict/data?dictTypeId=1&page=1&size=10
Authorization: Bearer {access_token}
```

### 7.5 系统配置列表
```http
GET /api/v1/system/configs?category=system
Authorization: Bearer {access_token}
```

### 7.6 更新系统配置
```http
PUT /api/v1/system/configs/system.title
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "configValue": "HandThing Admin 管理系统"
}
```

## 8. 工作流API

### 8.1 流程定义列表
```http
GET /api/v1/workflow/definitions?page=1&size=10&keyword=请假&category=hr
Authorization: Bearer {access_token}
```

### 8.2 部署流程
```http
POST /api/v1/workflow/definitions/deploy
Authorization: Bearer {access_token}
Content-Type: multipart/form-data

file: leave-process.bpmn
name: 请假流程
category: hr
```

### 8.3 启动流程
```http
POST /api/v1/workflow/instances/start
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "processDefinitionKey": "leave-process",
    "businessKey": "LEAVE-2024-001",
    "variables": {
        "days": 3,
        "reason": "个人事务"
    }
}
```

### 8.4 待办任务列表
```http
GET /api/v1/workflow/tasks/todo?page=1&size=10
Authorization: Bearer {access_token}
```

### 8.5 完成任务
```http
POST /api/v1/workflow/tasks/task123/complete
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "variables": {
        "approved": true
    },
    "comment": "同意请假申请"
}
```

## 9. 消息中心API

### 9.1 消息模板列表
```http
GET /api/v1/message/templates?page=1&size=10&type=sms
Authorization: Bearer {access_token}
```

### 9.2 发送消息
```http
POST /api/v1/message/send
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "templateCode": "welcome",
    "receivers": ["***********", "<EMAIL>"],
    "variables": {
        "username": "张三",
        "company": "测试公司"
    }
}
```

### 9.3 文件上传
```http
POST /api/v1/message/files/upload
Authorization: Bearer {access_token}
Content-Type: multipart/form-data

file: document.pdf
category: document
```

## 10. 运营监控API

### 10.1 审计日志列表
```http
GET /api/v1/operation/audit/logs?page=1&size=10&startTime=2024-01-01&endTime=2024-01-31
Authorization: Bearer {access_token}
```

### 10.2 系统监控数据
```http
GET /api/v1/operation/monitor/metrics?type=cpu&startTime=2024-01-01&endTime=2024-01-02
Authorization: Bearer {access_token}
```

### 10.3 告警规则列表
```http
GET /api/v1/operation/alert/rules?page=1&size=10
Authorization: Bearer {access_token}
```

### 10.4 计费记录列表
```http
GET /api/v1/operation/billing/records?page=1&size=10&tenantId=1
Authorization: Bearer {access_token}
```

---

**© 2024 HandThing. All rights reserved.**
