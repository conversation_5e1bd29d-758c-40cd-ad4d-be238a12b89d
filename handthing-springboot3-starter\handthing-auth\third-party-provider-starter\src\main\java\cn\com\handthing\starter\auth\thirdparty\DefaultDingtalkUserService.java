package cn.com.handthing.starter.auth.thirdparty;

import cn.com.handthing.starter.auth.core.UserInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 默认钉钉用户服务实现
 * <p>
 * 提供基于内存的钉钉用户服务实现，主要用于测试和演示。
 * 生产环境建议实现自己的DingtalkUserService来对接实际的用户数据源。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Service
@ConditionalOnMissingBean(DingtalkUserService.class)
public class DefaultDingtalkUserService implements DingtalkUserService {

    /**
     * 用户存储（按用户ID索引）
     */
    private final Map<String, UserInfo> usersById = new ConcurrentHashMap<>();

    /**
     * 钉钉用户映射（钉钉用户ID -> 用户ID）
     */
    private final Map<String, String> dingtalkUserMapping = new ConcurrentHashMap<>();

    /**
     * 手机号映射（手机号 -> 用户ID）
     */
    private final Map<String, String> mobileMapping = new ConcurrentHashMap<>();

    /**
     * 邮箱映射（邮箱 -> 用户ID）
     */
    private final Map<String, String> emailMapping = new ConcurrentHashMap<>();

    /**
     * 登录失败记录
     */
    private final Map<String, LoginFailureRecord> loginFailures = new ConcurrentHashMap<>();

    /**
     * 用户ID生成器
     */
    private final AtomicLong userIdGenerator = new AtomicLong(3000);

    @Override
    public Optional<UserInfo> findByDingtalkUserId(String dingtalkUserId, String appKey) {
        String key = buildDingtalkUserKey(dingtalkUserId, appKey);
        String userId = dingtalkUserMapping.get(key);
        return userId != null ? Optional.ofNullable(usersById.get(userId)) : Optional.empty();
    }

    @Override
    public Optional<UserInfo> findById(String userId) {
        return Optional.ofNullable(usersById.get(userId));
    }

    @Override
    public Optional<UserInfo> findByMobile(String mobile) {
        String userId = mobileMapping.get(mobile);
        return userId != null ? Optional.ofNullable(usersById.get(userId)) : Optional.empty();
    }

    @Override
    public Optional<UserInfo> findByEmail(String email) {
        String userId = emailMapping.get(email);
        return userId != null ? Optional.ofNullable(usersById.get(userId)) : Optional.empty();
    }

    @Override
    public UserInfo createUser(DingtalkApiService.DingtalkUserDetailResult dingtalkUserDetail, String appKey) {
        // 验证参数
        if (!isValidDingtalkUserId(dingtalkUserDetail.getUserId())) {
            throw new RuntimeException("Invalid dingtalk user id: " + dingtalkUserDetail.getUserId());
        }

        if (!isValidAppKey(appKey)) {
            throw new RuntimeException("Invalid app key: " + appKey);
        }

        // 检查是否已存在
        String dingtalkKey = buildDingtalkUserKey(dingtalkUserDetail.getUserId(), appKey);
        if (dingtalkUserMapping.containsKey(dingtalkKey)) {
            throw new RuntimeException("Dingtalk user already exists: " + dingtalkKey);
        }

        // 创建用户
        String userId = String.valueOf(userIdGenerator.incrementAndGet());
        String username = generateDefaultUsername(dingtalkUserDetail.getUserId(), appKey);
        String nickname = dingtalkUserDetail.getName() != null ? dingtalkUserDetail.getName() : 
                         generateDefaultNickname(dingtalkUserDetail.getUserId());

        UserInfo userInfo = UserInfo.builder()
                .userId(userId)
                .username(username)
                .nickname(nickname)
                .phone(dingtalkUserDetail.getMobile())
                .email(dingtalkUserDetail.getEmail())
                .avatar(dingtalkUserDetail.getAvatar())
                .roles(getDefaultRoles())
                .permissions(getDefaultPermissions())
                .status(1)
                .enabled(true)
                .accountNonExpired(true)
                .accountNonLocked(true)
                .credentialsNonExpired(true)
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .build();

        // 添加钉钉相关属性
        userInfo.addAttribute("dingtalk_user_id", dingtalkUserDetail.getUserId());
        userInfo.addAttribute("app_key", appKey);
        userInfo.addAttribute("dingtalk_name", dingtalkUserDetail.getName());
        userInfo.addAttribute("dingtalk_job_number", dingtalkUserDetail.getJobNumber());
        userInfo.addAttribute("dingtalk_title", dingtalkUserDetail.getTitle());
        userInfo.addAttribute("dingtalk_work_place", dingtalkUserDetail.getWorkPlace());
        userInfo.addAttribute("dingtalk_remark", dingtalkUserDetail.getRemark());
        userInfo.addAttribute("dingtalk_dept_id_list", dingtalkUserDetail.getDeptIdList());
        userInfo.addAttribute("dingtalk_dept_order_list", dingtalkUserDetail.getDeptOrderList());

        if (dingtalkUserDetail.getExtension() != null) {
            userInfo.addAttribute("dingtalk_extension", dingtalkUserDetail.getExtension());
        }

        // 存储用户
        usersById.put(userId, userInfo);
        dingtalkUserMapping.put(dingtalkKey, userId);

        if (dingtalkUserDetail.getMobile() != null) {
            mobileMapping.put(dingtalkUserDetail.getMobile(), userId);
        }

        if (dingtalkUserDetail.getEmail() != null) {
            emailMapping.put(dingtalkUserDetail.getEmail(), userId);
        }

        log.info("Created new dingtalk user: userId={}, dingtalkUserId={}, appKey={}, name={}", 
                userId, dingtalkUserDetail.getUserId(), appKey, dingtalkUserDetail.getName());
        return userInfo;
    }

    @Override
    public boolean bindDingtalkUser(String userId, String dingtalkUserId, String appKey) {
        UserInfo userInfo = usersById.get(userId);
        if (userInfo == null) {
            log.warn("User not found for binding: {}", userId);
            return false;
        }

        String dingtalkKey = buildDingtalkUserKey(dingtalkUserId, appKey);
        if (dingtalkUserMapping.containsKey(dingtalkKey)) {
            log.warn("Dingtalk user already bound: {}", dingtalkKey);
            return false;
        }

        // 绑定钉钉用户
        dingtalkUserMapping.put(dingtalkKey, userId);
        userInfo.addAttribute("dingtalk_user_id", dingtalkUserId);
        userInfo.addAttribute("app_key", appKey);
        userInfo.setUpdateTime(LocalDateTime.now());

        log.info("Bound dingtalk user: userId={}, dingtalkUserId={}, appKey={}", userId, dingtalkUserId, appKey);
        return true;
    }

    @Override
    public boolean unbindDingtalkUser(String userId, String appKey) {
        UserInfo userInfo = usersById.get(userId);
        if (userInfo == null) {
            return false;
        }

        String dingtalkUserId = userInfo.getAttribute("dingtalk_user_id", String.class);
        if (dingtalkUserId == null) {
            return false;
        }

        String dingtalkKey = buildDingtalkUserKey(dingtalkUserId, appKey);
        dingtalkUserMapping.remove(dingtalkKey);
        
        userInfo.getAttributes().remove("dingtalk_user_id");
        userInfo.getAttributes().remove("app_key");
        userInfo.setUpdateTime(LocalDateTime.now());

        log.info("Unbound dingtalk user: userId={}, dingtalkUserId={}, appKey={}", userId, dingtalkUserId, appKey);
        return true;
    }

    @Override
    public boolean isUserValid(UserInfo userInfo) {
        return userInfo != null && Boolean.TRUE.equals(userInfo.getEnabled());
    }

    @Override
    public boolean isUserLocked(UserInfo userInfo) {
        return userInfo != null && Boolean.FALSE.equals(userInfo.getAccountNonLocked());
    }

    @Override
    public boolean isUserDisabled(UserInfo userInfo) {
        return userInfo != null && Boolean.FALSE.equals(userInfo.getEnabled());
    }

    @Override
    public boolean isAccountExpired(UserInfo userInfo) {
        return userInfo != null && Boolean.FALSE.equals(userInfo.getAccountNonExpired());
    }

    @Override
    public void updateLastLoginInfo(String userId, LocalDateTime loginTime, String ipAddress, String userAgent) {
        UserInfo userInfo = usersById.get(userId);
        if (userInfo != null) {
            userInfo.setLastLoginTime(loginTime);
            userInfo.setLastLoginIp(ipAddress);
            userInfo.setUpdateTime(LocalDateTime.now());
            userInfo.addAttribute("lastUserAgent", userAgent);
            log.debug("Updated last login info for user: {}", userId);
        }
    }

    @Override
    public void recordLoginFailure(String dingtalkUserId, String appKey, String ipAddress, String reason) {
        String key = buildDingtalkUserKey(dingtalkUserId, appKey);
        LoginFailureRecord record = loginFailures.computeIfAbsent(key, k -> new LoginFailureRecord());
        record.incrementFailureCount();
        record.setLastFailureTime(LocalDateTime.now());
        record.setLastFailureIp(ipAddress);
        record.setLastFailureReason(reason);

        log.debug("Recorded login failure for dingtalk user: {}, count: {}", key, record.getFailureCount());
    }

    @Override
    public void clearLoginFailures(String dingtalkUserId, String appKey) {
        String key = buildDingtalkUserKey(dingtalkUserId, appKey);
        loginFailures.remove(key);
        log.debug("Cleared login failures for dingtalk user: {}", key);
    }

    @Override
    public int getLoginFailureCount(String dingtalkUserId, String appKey) {
        String key = buildDingtalkUserKey(dingtalkUserId, appKey);
        LoginFailureRecord record = loginFailures.get(key);
        return record != null ? record.getFailureCount() : 0;
    }

    @Override
    public void lockUser(String userId, String reason, LocalDateTime lockUntil) {
        UserInfo userInfo = usersById.get(userId);
        if (userInfo != null) {
            userInfo.setAccountNonLocked(false);
            userInfo.setUpdateTime(LocalDateTime.now());
            userInfo.addAttribute("lockReason", reason);
            userInfo.addAttribute("lockUntil", lockUntil);
            log.info("Locked user: {}, reason: {}, until: {}", userId, reason, lockUntil);
        }
    }

    @Override
    public void unlockUser(String userId) {
        UserInfo userInfo = usersById.get(userId);
        if (userInfo != null) {
            userInfo.setAccountNonLocked(true);
            userInfo.setUpdateTime(LocalDateTime.now());
            userInfo.getAttributes().remove("lockReason");
            userInfo.getAttributes().remove("lockUntil");
            log.info("Unlocked user: {}", userId);
        }
    }

    /**
     * 生成默认昵称
     *
     * @param dingtalkUserId 钉钉用户ID
     * @return 默认昵称
     */
    private String generateDefaultNickname(String dingtalkUserId) {
        return "钉钉用户" + dingtalkUserId.substring(Math.max(0, dingtalkUserId.length() - 4));
    }

    /**
     * 登录失败记录
     */
    private static class LoginFailureRecord {
        private int failureCount = 0;
        private LocalDateTime lastFailureTime;
        private String lastFailureIp;
        private String lastFailureReason;

        public void incrementFailureCount() {
            this.failureCount++;
        }

        public int getFailureCount() {
            return failureCount;
        }

        public LocalDateTime getLastFailureTime() {
            return lastFailureTime;
        }

        public void setLastFailureTime(LocalDateTime lastFailureTime) {
            this.lastFailureTime = lastFailureTime;
        }

        public String getLastFailureIp() {
            return lastFailureIp;
        }

        public void setLastFailureIp(String lastFailureIp) {
            this.lastFailureIp = lastFailureIp;
        }

        public String getLastFailureReason() {
            return lastFailureReason;
        }

        public void setLastFailureReason(String lastFailureReason) {
            this.lastFailureReason = lastFailureReason;
        }
    }

    /**
     * 获取统计信息
     *
     * @return 统计信息
     */
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new java.util.HashMap<>();
        stats.put("totalUsers", usersById.size());
        stats.put("dingtalkUsers", dingtalkUserMapping.size());
        stats.put("mobileUsers", mobileMapping.size());
        stats.put("emailUsers", emailMapping.size());
        stats.put("loginFailures", loginFailures.size());
        
        long enabledUsers = usersById.values().stream()
                .filter(user -> Boolean.TRUE.equals(user.getEnabled()))
                .count();
        stats.put("enabledUsers", enabledUsers);
        
        return stats;
    }
}
