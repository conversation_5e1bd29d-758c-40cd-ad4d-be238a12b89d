{"groups": [{"name": "handthing.datalayer.tenant", "type": "cn.com.handthing.starter.tenant.datalayer.config.TenantDatalayerProperties", "sourceType": "cn.com.handthing.starter.tenant.datalayer.config.TenantDatalayerProperties"}, {"name": "handthing.datalayer.tenant.data-source", "type": "cn.com.handthing.starter.tenant.datalayer.config.TenantDatalayerProperties$DataSource", "sourceType": "cn.com.handthing.starter.tenant.datalayer.config.TenantDatalayerProperties", "sourceMethod": "public cn.com.handthing.starter.tenant.datalayer.config.TenantDatalayerProperties.DataSource getDataSource() "}], "properties": [{"name": "handthing.datalayer.tenant.auto-fill-enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用租户ID自动填充", "sourceType": "cn.com.handthing.starter.tenant.datalayer.config.TenantDatalayerProperties"}, {"name": "handthing.datalayer.tenant.data-source.cache-enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用数据源缓存", "sourceType": "cn.com.handthing.starter.tenant.datalayer.config.TenantDatalayerProperties$DataSource"}, {"name": "handthing.datalayer.tenant.data-source.cache-expire-seconds", "type": "java.lang.Long", "description": "数据源缓存过期时间（秒）", "sourceType": "cn.com.handthing.starter.tenant.datalayer.config.TenantDatalayerProperties$DataSource"}, {"name": "handthing.datalayer.tenant.data-source.data-source-prefix", "type": "java.lang.String", "description": "数据源前缀", "sourceType": "cn.com.handthing.starter.tenant.datalayer.config.TenantDatalayerProperties$DataSource"}, {"name": "handthing.datalayer.tenant.data-source.default-data-source", "type": "java.lang.String", "description": "默认数据源名称", "sourceType": "cn.com.handthing.starter.tenant.datalayer.config.TenantDatalayerProperties$DataSource"}, {"name": "handthing.datalayer.tenant.data-source.multi-data-source-enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用多数据源", "sourceType": "cn.com.handthing.starter.tenant.datalayer.config.TenantDatalayerProperties$DataSource"}, {"name": "handthing.datalayer.tenant.data-source.routing-strategy", "type": "java.lang.String", "description": "数据源路由策略", "sourceType": "cn.com.handthing.starter.tenant.datalayer.config.TenantDatalayerProperties$DataSource"}, {"name": "handthing.datalayer.tenant.default-tenant-id", "type": "java.lang.String", "description": "默认租户ID", "sourceType": "cn.com.handthing.starter.tenant.datalayer.config.TenantDatalayerProperties"}, {"name": "handthing.datalayer.tenant.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用租户功能", "sourceType": "cn.com.handthing.starter.tenant.datalayer.config.TenantDatalayerProperties"}, {"name": "handthing.datalayer.tenant.ignore-system-tables", "type": "java.lang.Bo<PERSON>an", "description": "是否忽略系统表 <p> 如果为true，系统表（如用户表、角色表等）不会自动添加租户条件 </p>", "sourceType": "cn.com.handthing.starter.tenant.datalayer.config.TenantDatalayerProperties"}, {"name": "handthing.datalayer.tenant.ignore-table-prefixes", "type": "java.util.Set<java.lang.String>", "description": "忽略租户处理的表名前缀集合 <p> 以这些前缀开头的表不会自动添加租户条件 </p>", "sourceType": "cn.com.handthing.starter.tenant.datalayer.config.TenantDatalayerProperties"}, {"name": "handthing.datalayer.tenant.ignore-tables", "type": "java.util.Set<java.lang.String>", "description": "忽略租户处理的表名集合 <p> 这些表不会自动添加租户条件 </p>", "sourceType": "cn.com.handthing.starter.tenant.datalayer.config.TenantDatalayerProperties"}, {"name": "handthing.datalayer.tenant.isolation-strategy", "type": "cn.com.handthing.starter.tenant.datalayer.config.TenantDatalayerProperties$IsolationStrategy", "description": "租户隔离策略", "sourceType": "cn.com.handthing.starter.tenant.datalayer.config.TenantDatalayerProperties"}, {"name": "handthing.datalayer.tenant.strict-mode", "type": "java.lang.Bo<PERSON>an", "description": "是否启用严格模式 <p> 严格模式下，租户相关的错误会抛出异常而不是记录日志 </p>", "sourceType": "cn.com.handthing.starter.tenant.datalayer.config.TenantDatalayerProperties"}, {"name": "handthing.datalayer.tenant.tenant-id-column", "type": "java.lang.String", "description": "租户ID字段名", "sourceType": "cn.com.handthing.starter.tenant.datalayer.config.TenantDatalayerProperties"}, {"name": "handthing.datalayer.tenant.validate-consistency", "type": "java.lang.Bo<PERSON>an", "description": "是否验证租户一致性 <p> 验证实体的租户ID是否与当前上下文的租户ID一致 </p>", "sourceType": "cn.com.handthing.starter.tenant.datalayer.config.TenantDatalayerProperties"}], "hints": [], "ignored": {"properties": []}}