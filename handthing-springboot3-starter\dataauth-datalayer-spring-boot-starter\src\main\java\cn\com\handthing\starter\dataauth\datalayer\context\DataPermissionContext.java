package cn.com.handthing.starter.dataauth.datalayer.context;

import cn.com.handthing.starter.dataauth.datalayer.annotation.DataPermission;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 数据权限上下文
 * <p>
 * 使用ThreadLocal存储当前线程的数据权限配置信息。
 * 在AOP拦截器中设置权限配置，在数据权限处理器中获取并应用。
 * </p>
 * 
 * <h3>功能特性：</h3>
 * <ul>
 *   <li>线程安全的权限上下文存储</li>
 *   <li>支持权限配置的动态设置和获取</li>
 *   <li>支持权限参数的传递</li>
 *   <li>自动清理上下文防止内存泄漏</li>
 * </ul>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Data
public class DataPermissionContext {
    
    /**
     * ThreadLocal存储权限上下文
     */
    private static final ThreadLocal<DataPermissionContext> CONTEXT_HOLDER = new ThreadLocal<>();
    
    /**
     * 是否启用权限检查
     */
    private boolean enabled = true;
    
    /**
     * 权限配置
     */
    private DataPermission permission;
    
    /**
     * 方法签名
     */
    private String methodSignature;
    
    /**
     * Mapper方法ID
     */
    private String mappedStatementId;
    
    /**
     * 权限参数
     */
    private Map<String, Object> parameters = new ConcurrentHashMap<>();
    
    /**
     * 用户信息
     */
    private UserInfo userInfo;
    
    /**
     * 创建时间
     */
    private long createTime = System.currentTimeMillis();
    
    /**
     * 获取当前线程的权限上下文
     * 
     * @return 权限上下文，如果不存在返回null
     */
    public static DataPermissionContext getCurrentContext() {
        return CONTEXT_HOLDER.get();
    }
    
    /**
     * 设置当前线程的权限上下文
     * 
     * @param context 权限上下文
     */
    public static void setCurrentContext(DataPermissionContext context) {
        if (context != null) {
            CONTEXT_HOLDER.set(context);
            log.debug("Set data permission context: {}", context.getContextSummary());
        } else {
            CONTEXT_HOLDER.remove();
            log.debug("Removed data permission context");
        }
    }
    
    /**
     * 清除当前线程的权限上下文
     */
    public static void clearContext() {
        DataPermissionContext context = CONTEXT_HOLDER.get();
        if (context != null) {
            log.debug("Clearing data permission context: {}", context.getContextSummary());
        }
        CONTEXT_HOLDER.remove();
    }
    
    /**
     * 检查是否存在权限上下文
     * 
     * @return 如果存在返回true，否则返回false
     */
    public static boolean hasContext() {
        return CONTEXT_HOLDER.get() != null;
    }
    
    /**
     * 创建权限上下文
     * 
     * @param permission 权限配置
     * @return 权限上下文
     */
    public static DataPermissionContext create(DataPermission permission) {
        DataPermissionContext context = new DataPermissionContext();
        context.setPermission(permission);
        context.setEnabled(true);
        return context;
    }
    
    /**
     * 创建禁用的权限上下文
     * 
     * @return 禁用的权限上下文
     */
    public static DataPermissionContext createDisabled() {
        DataPermissionContext context = new DataPermissionContext();
        context.setEnabled(false);
        return context;
    }
    
    /**
     * 添加权限参数
     * 
     * @param key   参数名
     * @param value 参数值
     */
    public void addParameter(String key, Object value) {
        if (key != null && value != null) {
            parameters.put(key, value);
        }
    }
    
    /**
     * 获取权限参数
     * 
     * @param key 参数名
     * @return 参数值
     */
    public Object getParameter(String key) {
        return parameters.get(key);
    }
    
    /**
     * 获取权限参数（指定类型）
     * 
     * @param key  参数名
     * @param type 参数类型
     * @param <T>  参数类型
     * @return 参数值
     */
    @SuppressWarnings("unchecked")
    public <T> T getParameter(String key, Class<T> type) {
        Object value = parameters.get(key);
        if (value != null && type.isAssignableFrom(value.getClass())) {
            return (T) value;
        }
        return null;
    }
    
    /**
     * 检查是否包含指定参数
     * 
     * @param key 参数名
     * @return 如果包含返回true，否则返回false
     */
    public boolean hasParameter(String key) {
        return parameters.containsKey(key);
    }
    
    /**
     * 移除权限参数
     * 
     * @param key 参数名
     * @return 被移除的参数值
     */
    public Object removeParameter(String key) {
        return parameters.remove(key);
    }
    
    /**
     * 清空所有权限参数
     */
    public void clearParameters() {
        parameters.clear();
    }
    
    /**
     * 获取上下文摘要信息
     * 
     * @return 摘要信息
     */
    public String getContextSummary() {
        StringBuilder sb = new StringBuilder();
        sb.append("DataPermissionContext{");
        sb.append("enabled=").append(enabled);
        
        if (permission != null) {
            sb.append(", type=").append(permission.type());
            sb.append(", field='").append(permission.field()).append("'");
            sb.append(", scope=").append(permission.scope());
        }
        
        if (methodSignature != null) {
            sb.append(", method='").append(methodSignature).append("'");
        }
        
        if (mappedStatementId != null) {
            sb.append(", mapper='").append(mappedStatementId).append("'");
        }
        
        if (!parameters.isEmpty()) {
            sb.append(", params=").append(parameters.size());
        }
        
        if (userInfo != null) {
            sb.append(", user='").append(userInfo.getUserId()).append("'");
        }
        
        sb.append(", age=").append(System.currentTimeMillis() - createTime).append("ms");
        sb.append("}");
        
        return sb.toString();
    }
    
    /**
     * 检查上下文是否有效
     * 
     * @return 如果有效返回true，否则返回false
     */
    public boolean isValid() {
        // 检查上下文是否过期（超过5分钟）
        long age = System.currentTimeMillis() - createTime;
        if (age > 300000) { // 5分钟
            log.warn("Data permission context is expired: age={}ms", age);
            return false;
        }
        
        return enabled;
    }
    
    /**
     * 复制上下文
     * 
     * @return 复制的上下文
     */
    public DataPermissionContext copy() {
        DataPermissionContext copy = new DataPermissionContext();
        copy.setEnabled(this.enabled);
        copy.setPermission(this.permission);
        copy.setMethodSignature(this.methodSignature);
        copy.setMappedStatementId(this.mappedStatementId);
        copy.setUserInfo(this.userInfo);
        copy.getParameters().putAll(this.parameters);
        return copy;
    }
    
    /**
     * 用户信息内部类
     */
    @Data
    public static class UserInfo {
        /**
         * 用户ID
         */
        private String userId;
        
        /**
         * 用户名
         */
        private String username;
        
        /**
         * 部门ID
         */
        private String deptId;
        
        /**
         * 部门名称
         */
        private String deptName;
        
        /**
         * 组织ID
         */
        private String orgId;
        
        /**
         * 组织名称
         */
        private String orgName;
        
        /**
         * 角色列表
         */
        private java.util.List<String> roles;
        
        /**
         * 是否为超级管理员
         */
        private boolean superAdmin;
        
        /**
         * 扩展属性
         */
        private Map<String, Object> attributes = new ConcurrentHashMap<>();
        
        /**
         * 添加扩展属性
         * 
         * @param key   属性名
         * @param value 属性值
         */
        public void addAttribute(String key, Object value) {
            if (key != null && value != null) {
                attributes.put(key, value);
            }
        }
        
        /**
         * 获取扩展属性
         * 
         * @param key 属性名
         * @return 属性值
         */
        public Object getAttribute(String key) {
            return attributes.get(key);
        }
        
        /**
         * 获取扩展属性（指定类型）
         * 
         * @param key  属性名
         * @param type 属性类型
         * @param <T>  属性类型
         * @return 属性值
         */
        @SuppressWarnings("unchecked")
        public <T> T getAttribute(String key, Class<T> type) {
            Object value = attributes.get(key);
            if (value != null && type.isAssignableFrom(value.getClass())) {
                return (T) value;
            }
            return null;
        }
        
        /**
         * 检查是否具有指定角色
         * 
         * @param role 角色名
         * @return 如果具有返回true，否则返回false
         */
        public boolean hasRole(String role) {
            return roles != null && roles.contains(role);
        }
        
        /**
         * 检查是否具有任意指定角色
         * 
         * @param roles 角色列表
         * @return 如果具有任意角色返回true，否则返回false
         */
        public boolean hasAnyRole(java.util.Collection<String> roles) {
            if (this.roles == null || this.roles.isEmpty() || roles == null || roles.isEmpty()) {
                return false;
            }
            
            return this.roles.stream().anyMatch(roles::contains);
        }
    }
}
