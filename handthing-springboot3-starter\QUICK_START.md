# 🚀 HandThing DataLayer 快速开始指南

本指南将帮助您在5分钟内快速上手 HandThing DataLayer Spring Boot Starter 系列。

## 📋 前置条件

- **Java 17+**
- **Spring Boot 3.x**
- **Maven 3.6+** 或 **Gradle 7.0+**
- **MySQL 8.0+** 或其他支持的数据库

## 🎯 选择您的场景

根据您的业务需求，选择合适的模块组合：

### 📦 场景1：基础应用
**适用于**：简单的CRUD应用，需要ID生成和审计功能
```xml
<dependency>
    <groupId>cn.com.handthing.springboot3.starter</groupId>
    <artifactId>datalayer-spring-boot-starter</artifactId>
    <version>1.0.0-SNAPSHOT</version>
</dependency>
```

### 🏢 场景2：多租户SaaS应用
**适用于**：需要租户数据隔离的SaaS应用
```xml
<!-- 核心模块 -->
<dependency>
    <groupId>cn.com.handthing.springboot3.starter</groupId>
    <artifactId>datalayer-spring-boot-starter</artifactId>
    <version>1.0.0-SNAPSHOT</version>
</dependency>
<!-- 租户插件 -->
<dependency>
    <groupId>cn.com.handthing.springboot3.starter</groupId>
    <artifactId>tenant-datalayer-spring-boot-starter</artifactId>
    <version>1.0.0-SNAPSHOT</version>
</dependency>
```

### 🔐 场景3：权限控制应用
**适用于**：需要数据权限控制的企业应用
```xml
<!-- 核心模块 -->
<dependency>
    <groupId>cn.com.handthing.springboot3.starter</groupId>
    <artifactId>datalayer-spring-boot-starter</artifactId>
    <version>1.0.0-SNAPSHOT</version>
</dependency>
<!-- 权限插件 -->
<dependency>
    <groupId>cn.com.handthing.springboot3.starter</groupId>
    <artifactId>dataauth-datalayer-spring-boot-starter</artifactId>
    <version>1.0.0-SNAPSHOT</version>
</dependency>
```

### 🏛️ 场景4：完整企业应用
**适用于**：需要多租户+数据权限的复杂企业应用
```xml
<!-- 核心模块 -->
<dependency>
    <groupId>cn.com.handthing.springboot3.starter</groupId>
    <artifactId>datalayer-spring-boot-starter</artifactId>
    <version>1.0.0-SNAPSHOT</version>
</dependency>
<!-- 租户插件 -->
<dependency>
    <groupId>cn.com.handthing.springboot3.starter</groupId>
    <artifactId>tenant-datalayer-spring-boot-starter</artifactId>
    <version>1.0.0-SNAPSHOT</version>
</dependency>
<!-- 权限插件 -->
<dependency>
    <groupId>cn.com.handthing.springboot3.starter</groupId>
    <artifactId>dataauth-datalayer-spring-boot-starter</artifactId>
    <version>1.0.0-SNAPSHOT</version>
</dependency>
```

## ⚙️ 配置文件

### 基础配置 (application.yml)

```yaml
spring:
  datasource:
    url: *************************************************************************************************************
    username: your_username
    password: your_password
    driver-class-name: com.mysql.cj.jdbc.Driver

# HandThing DataLayer 配置
handthing:
  id:
    snowflake:
      worker-id: 1        # 工作节点ID (0-31)
      datacenter-id: 1    # 数据中心ID (0-31)
      pool:
        enabled: true     # 启用ID池化
        size: 200         # 池大小
        threshold: 50     # 补充阈值
  datalayer:
    enabled: true
```

### 多租户配置

```yaml
handthing:
  datalayer:
    tenant:
      enabled: true
      tenant-id-column: tenant_id
      default-tenant-id: default
      strict-mode: false
      validate-consistency: true
      ignore-tables:
        - users
        - roles
        - permissions
      ignore-table-prefixes:
        - sys_
        - config_
```

### 数据权限配置

```yaml
handthing:
  datalayer:
    dataauth:
      enabled: true
      aop-enabled: true
      strict-mode: false
      default-failure-strategy: FILTER
      cache-enabled: true
      super-admin:
        enabled: true
        roles:
          - SUPER_ADMIN
          - SYSTEM_ADMIN
      provider:
        dept:
          enabled: true
          default-field: dept_id
        user:
          enabled: true
          default-field: create_by
```

## 📝 创建实体类

### 基础实体

```java
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("products")
public class Product extends BaseEntity {
    @IdSetter  // 自动生成Long类型ID (雪花算法)
    @TableId
    private Long id;
    
    @IdSetter  // 自动生成String类型编号 (UUID)
    private String productNo;
    
    private String name;
    private BigDecimal price;
    private String description;
}
```

### 多租户实体

```java
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("orders")
public class Order extends TenantBaseEntity {  // 继承TenantBaseEntity
    @IdSetter
    @TableId
    private Long id;
    
    @IdSetter
    private String orderNo;
    
    private Long productId;
    private Integer quantity;
    private BigDecimal totalAmount;
}
```

## 🗄️ 创建数据库表

### 基础表结构

```sql
CREATE TABLE products (
    id BIGINT PRIMARY KEY,
    product_no VARCHAR(64) NOT NULL UNIQUE,
    name VARCHAR(255) NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    description TEXT,
    create_by VARCHAR(64),
    create_time DATETIME,
    update_by VARCHAR(64),
    update_time DATETIME,
    version BIGINT DEFAULT 1,
    deleted INT DEFAULT 0
);
```

### 多租户表结构

```sql
CREATE TABLE orders (
    id BIGINT PRIMARY KEY,
    order_no VARCHAR(64) NOT NULL,
    product_id BIGINT NOT NULL,
    quantity INT NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    tenant_id VARCHAR(64) NOT NULL,  -- 租户字段
    create_by VARCHAR(64),
    create_time DATETIME,
    update_by VARCHAR(64),
    update_time DATETIME,
    version BIGINT DEFAULT 1,
    deleted INT DEFAULT 0,
    INDEX idx_tenant_id (tenant_id)
);
```

## 🔧 创建Mapper

```java
@Mapper
public interface ProductMapper extends BaseMapper<Product> {
    // 继承BaseMapper即可获得基础CRUD方法
    // 可以添加自定义查询方法
}

@Mapper
public interface OrderMapper extends BaseMapper<Order> {
    // 多租户Mapper，查询会自动添加租户条件
}
```

## 🏗️ 创建Service

### 基础Service

```java
@Service
public class ProductService extends BaseServiceImpl<ProductMapper, Product> {
    
    public Product createProduct(Product product) {
        // ID、审计字段会自动填充
        save(product);
        return product;
    }
    
    public List<Product> findAllProducts() {
        return list();
    }
    
    public Product findById(Long id) {
        return getById(id);
    }
}
```

### 带权限控制的Service

```java
@Service
public class OrderService extends BaseServiceImpl<OrderMapper, Order> {
    
    @DataPermission(type = DataPermissionType.DEPT)
    public List<Order> findDeptOrders() {
        return list(); // 自动添加部门权限条件
    }
    
    @DataPermission(type = DataPermissionType.USER, field = "create_by")
    public List<Order> findMyOrders() {
        return list(); // 只返回当前用户创建的订单
    }
    
    @IgnoreDataPermission(reason = "管理员查看所有订单")
    public List<Order> findAllOrders() {
        return list(); // 跳过权限检查
    }
}
```

## 🌐 创建Controller

```java
@RestController
@RequestMapping("/api/products")
public class ProductController {
    
    @Autowired
    private ProductService productService;
    
    @PostMapping
    public ResponseEntity<Product> createProduct(@RequestBody Product product) {
        Product created = productService.createProduct(product);
        return ResponseEntity.ok(created);
    }
    
    @GetMapping
    public ResponseEntity<List<Product>> getProducts() {
        List<Product> products = productService.findAllProducts();
        return ResponseEntity.ok(products);
    }
    
    @GetMapping("/{id}")
    public ResponseEntity<Product> getProduct(@PathVariable Long id) {
        Product product = productService.findById(id);
        return ResponseEntity.ok(product);
    }
}

@RestController
@RequestMapping("/api/orders")
public class OrderController {
    
    @Autowired
    private OrderService orderService;
    
    @GetMapping("/dept")
    public ResponseEntity<List<Order>> getDeptOrders() {
        // 自动应用部门权限过滤
        List<Order> orders = orderService.findDeptOrders();
        return ResponseEntity.ok(orders);
    }
    
    @GetMapping("/my")
    public ResponseEntity<List<Order>> getMyOrders() {
        // 只返回当前用户的订单
        List<Order> orders = orderService.findMyOrders();
        return ResponseEntity.ok(orders);
    }
}
```

## 🧪 测试

### 创建测试类

```java
@SpringBootTest
class ProductServiceTest {
    
    @Autowired
    private ProductService productService;
    
    @Test
    void testCreateProduct() {
        Product product = new Product();
        product.setName("测试产品");
        product.setPrice(new BigDecimal("99.99"));
        
        Product created = productService.createProduct(product);
        
        // 验证ID自动生成
        assertThat(created.getId()).isNotNull();
        assertThat(created.getProductNo()).isNotNull();
        
        // 验证审计字段自动填充
        assertThat(created.getCreateTime()).isNotNull();
        assertThat(created.getUpdateTime()).isNotNull();
    }
}
```

## 🚀 启动应用

```java
@SpringBootApplication
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
```

启动应用后，您将看到类似的日志输出：

```
=== HandThing DataLayer Configuration ===
Enabled: true
Logic Delete:
  Field: deleted
  Deleted Value: 1
  Not Deleted Value: 0
Pagination:
  Default Page Size: 10
  Max Page Size: 1000
==========================================

=== HandThing Tenant DataLayer Configuration ===
Enabled: true
Tenant Configuration:
  Tenant ID Column: tenant_id
  Default Tenant ID: default
===============================================

=== HandThing DataAuth DataLayer Configuration ===
Enabled: true
Data Permission Configuration:
  AOP Enabled: true
  Strict Mode: false
================================================
```

## 🎉 验证功能

### 1. 测试ID自动生成

```bash
curl -X POST http://localhost:8080/api/products \
  -H "Content-Type: application/json" \
  -d '{"name":"测试产品","price":99.99}'
```

响应：
```json
{
  "id": 1420070400000000001,
  "productNo": "a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6",
  "name": "测试产品",
  "price": 99.99,
  "createTime": "2025-01-01T10:00:00",
  "updateTime": "2025-01-01T10:00:00",
  "version": 1,
  "deleted": 0
}
```

### 2. 测试多租户隔离

设置租户上下文后，查询只会返回当前租户的数据。

### 3. 测试数据权限

使用不同权限的用户访问，会自动应用相应的数据过滤条件。

## 📚 下一步

- 查看 [API文档](./API.md) 了解详细的API使用方法
- 查看 [配置参考](./CONFIGURATION.md) 了解所有配置选项
- 查看 [使用示例](./EXAMPLES.md) 了解更多使用场景

## ❓ 常见问题

### Q: ID生成器不工作？
A: 确保实体类继承了BaseEntity或TenantBaseEntity，并在ID字段上添加@IdSetter注解。

### Q: 租户隔离不生效？
A: 检查实体类是否继承了TenantBaseEntity，并确保租户上下文已正确设置。

### Q: 数据权限不生效？
A: 确保在Service方法上添加了@DataPermission注解，并且用户上下文信息可用。

---

🎉 恭喜！您已经成功完成了 HandThing DataLayer 的快速入门。
