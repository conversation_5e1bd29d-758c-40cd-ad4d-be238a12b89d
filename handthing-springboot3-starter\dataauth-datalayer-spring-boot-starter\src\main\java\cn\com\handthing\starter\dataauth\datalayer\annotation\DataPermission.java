package cn.com.handthing.starter.dataauth.datalayer.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 数据权限注解
 * <p>
 * 通过在方法上声明@DataPermission注解，实现数据权限的自动控制。
 * 支持多种权限策略，包括部门权限、用户权限、角色权限等。
 * </p>
 * 
 * <h3>使用示例：</h3>
 * <pre>
 * &#64;DataPermission(type = DataPermissionType.DEPT)
 * public List&lt;User&gt; findUsers() {
 *     return userMapper.selectList(null);
 * }
 * 
 * &#64;DataPermission(type = DataPermissionType.USER, field = "create_by")
 * public List&lt;Order&gt; findMyOrders() {
 *     return orderMapper.selectList(null);
 * }
 * 
 * &#64;DataPermission(type = DataPermissionType.CUSTOM, expression = "dept_id IN (#{deptIds})")
 * public List&lt;Product&gt; findProducts() {
 *     return productMapper.selectList(null);
 * }
 * </pre>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD, ElementType.TYPE})
@Documented
public @interface DataPermission {
    
    /**
     * 数据权限类型
     * 
     * @return 权限类型，默认为DEPT（部门权限）
     */
    DataPermissionType type() default DataPermissionType.DEPT;
    
    /**
     * 权限字段名
     * <p>
     * 指定用于权限控制的数据库字段名，如dept_id、user_id等。
     * 如果为空，则使用权限类型的默认字段。
     * </p>
     * 
     * @return 权限字段名
     */
    String field() default "";
    
    /**
     * 权限表别名
     * <p>
     * 当SQL涉及多表查询时，指定权限字段所在表的别名。
     * </p>
     * 
     * @return 表别名
     */
    String alias() default "";
    
    /**
     * 自定义权限表达式
     * <p>
     * 当权限类型为CUSTOM时，使用此表达式作为权限条件。
     * 支持SpEL表达式和参数占位符。
     * </p>
     * 
     * @return 自定义权限表达式
     */
    String expression() default "";
    
    /**
     * 是否启用权限控制
     * <p>
     * 可以通过此属性动态控制是否启用权限，支持SpEL表达式。
     * </p>
     * 
     * @return 是否启用，默认为true
     */
    String enabled() default "true";
    
    /**
     * 权限失败时的处理策略
     * 
     * @return 失败处理策略，默认为FILTER（过滤数据）
     */
    FailureStrategy onFailure() default FailureStrategy.FILTER;
    
    /**
     * 是否忽略超级管理员
     * <p>
     * 如果为true，超级管理员将跳过权限检查。
     * </p>
     * 
     * @return 是否忽略超级管理员，默认为true
     */
    boolean ignoreSuperAdmin() default true;
    
    /**
     * 权限范围
     * <p>
     * 定义权限的作用范围，如当前部门、当前部门及子部门等。
     * </p>
     * 
     * @return 权限范围，默认为CURRENT（当前范围）
     */
    PermissionScope scope() default PermissionScope.CURRENT;
    
    /**
     * 权限缓存时间（秒）
     * <p>
     * 权限数据的缓存时间，0表示不缓存，-1表示永久缓存。
     * </p>
     * 
     * @return 缓存时间，默认为300秒（5分钟）
     */
    int cacheSeconds() default 300;
    
    /**
     * 数据权限类型枚举
     */
    enum DataPermissionType {
        /**
         * 部门权限：根据用户所属部门控制数据访问
         */
        DEPT,
        
        /**
         * 用户权限：只能访问自己创建的数据
         */
        USER,
        
        /**
         * 角色权限：根据用户角色控制数据访问
         */
        ROLE,
        
        /**
         * 组织权限：根据用户所属组织控制数据访问
         */
        ORG,
        
        /**
         * 自定义权限：使用自定义表达式控制数据访问
         */
        CUSTOM,
        
        /**
         * 无权限：跳过权限检查
         */
        NONE
    }
    
    /**
     * 权限失败处理策略枚举
     */
    enum FailureStrategy {
        /**
         * 过滤数据：返回空结果或过滤后的结果
         */
        FILTER,
        
        /**
         * 抛出异常：抛出权限不足异常
         */
        EXCEPTION,
        
        /**
         * 记录日志：记录权限检查失败日志，继续执行
         */
        LOG,
        
        /**
         * 忽略：忽略权限检查，正常执行
         */
        IGNORE
    }
    
    /**
     * 权限范围枚举
     */
    enum PermissionScope {
        /**
         * 当前范围：只能访问当前部门/组织的数据
         */
        CURRENT,
        
        /**
         * 当前及子级：可以访问当前部门/组织及其子级的数据
         */
        CURRENT_AND_CHILDREN,
        
        /**
         * 全部：可以访问所有数据（相当于无权限限制）
         */
        ALL,
        
        /**
         * 自定义：使用自定义逻辑确定权限范围
         */
        CUSTOM
    }
}
