package cn.com.handthing.starter.connector.toutiao.config;

import cn.com.handthing.starter.connector.auth.AuthService;
import cn.com.handthing.starter.connector.auth.DefaultAuthService;
import cn.com.handthing.starter.connector.token.TokenManager;
import cn.com.handthing.starter.connector.toutiao.ToutiaoAuthProvider;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;

import jakarta.annotation.PostConstruct;

/**
 * 今日头条连接器自动配置
 * <p>
 * 自动配置今日头条连接器相关的Bean，包括配置类、认证提供者、服务类等。
 * 当启用今日头条连接器时，自动注册到认证服务和Token管理器中。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@RequiredArgsConstructor
@AutoConfiguration
@EnableConfigurationProperties(ToutiaoConfig.class)
@ConditionalOnProperty(prefix = "handthing.connector.toutiao", name = "enabled", havingValue = "true")
@ConditionalOnClass(AuthService.class)
@ComponentScan(basePackages = "cn.com.handthing.starter.connector.toutiao")
public class ToutiaoAutoConfiguration {

    private final AuthService authService;
    private final ToutiaoAuthProvider toutiaoAuthProvider;
    private final TokenManager tokenManager;
    private final ToutiaoConfig toutiaoConfig;

    /**
     * 注册今日头条认证提供者到认证服务
     */
    @PostConstruct
    public void registerToutiaoAuthProvider() {
        if (authService instanceof DefaultAuthService) {
            DefaultAuthService defaultAuthService = (DefaultAuthService) authService;
            defaultAuthService.registerAuthProvider(toutiaoAuthProvider);
            tokenManager.registerAuthProvider(toutiaoAuthProvider);
            
            log.info("Registered Toutiao auth provider - ClientKey: {}, AppType: {}, Enterprise: {}, Valid: {}", 
                    toutiaoConfig.getClientKey(), 
                    toutiaoConfig.getAppType(),
                    toutiaoConfig.isEnterpriseMode(),
                    toutiaoConfig.isValid());
        } else {
            log.warn("AuthService is not DefaultAuthService, cannot register Toutiao auth provider");
        }
    }

    /**
     * 配置信息日志输出
     */
    @PostConstruct
    public void logConfiguration() {
        log.info("Toutiao configuration: enabled=true, valid={}, clientKey={}, appType={}, enterpriseMode={}, sandboxMode={}, contentPublishEnabled={}, analyticsEnabled={}",
                toutiaoConfig.isValid(),
                toutiaoConfig.getClientKey(),
                toutiaoConfig.getAppType(),
                toutiaoConfig.isEnterpriseMode(),
                toutiaoConfig.isSandboxMode(),
                toutiaoConfig.isContentPublishAvailable(),
                toutiaoConfig.isAnalyticsAvailable());
    }
}
