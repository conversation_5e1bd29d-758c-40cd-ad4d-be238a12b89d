package cn.com.handthing.starter.tenant.config;

/**
 * 租户配置键枚举
 * <p>
 * 定义所有支持的租户配置项，包含配置项的类型、默认值、描述等元信息。
 * 用于标准化租户配置的管理和使用。
 * </p>
 *
 * <AUTHOR>
 * @since V3.0.0
 */
public enum TenantConfigKey {

    // ========== 基础配置 ==========
    
    /**
     * 租户名称
     */
    TENANT_NAME("tenant.name", TenantConfigType.STRING, null, "租户显示名称"),

    /**
     * 租户状态
     */
    TENANT_STATUS("tenant.status", TenantConfigType.STRING, "ACTIVE", "租户状态：ACTIVE, DISABLED, SUSPENDED等"),

    /**
     * 租户描述
     */
    TENANT_DESCRIPTION("tenant.description", TenantConfigType.STRING, null, "租户描述信息"),

    // ========== 认证配置 ==========

    /**
     * 默认认证类型
     */
    AUTH_DEFAULT_GRANT_TYPE("auth.default.grant.type", TenantConfigType.STRING, "password", "默认认证类型"),

    /**
     * 是否启用多认证提供者
     */
    AUTH_MULTI_PROVIDER_ENABLED("auth.multi.provider.enabled", TenantConfigType.BOOLEAN, "true", "是否启用多认证提供者"),

    // ========== JWT配置 ==========

    /**
     * JWT密钥
     */
    JWT_SECRET("jwt.secret", TenantConfigType.PASSWORD, null, "JWT签名密钥"),

    /**
     * JWT签发者
     */
    JWT_ISSUER("jwt.issuer", TenantConfigType.STRING, "handthing-auth", "JWT签发者"),

    /**
     * JWT受众
     */
    JWT_AUDIENCE("jwt.audience", TenantConfigType.STRING, "handthing-app", "JWT受众"),

    /**
     * 访问令牌过期时间（秒）
     */
    JWT_ACCESS_TOKEN_EXPIRATION("jwt.access.token.expiration", TenantConfigType.INTEGER, "7200", "访问令牌过期时间（秒）"),

    /**
     * 刷新令牌过期时间（秒）
     */
    JWT_REFRESH_TOKEN_EXPIRATION("jwt.refresh.token.expiration", TenantConfigType.INTEGER, "604800", "刷新令牌过期时间（秒）"),

    // ========== 密码认证配置 ==========

    /**
     * 是否启用密码认证
     */
    PASSWORD_AUTH_ENABLED("password.auth.enabled", TenantConfigType.BOOLEAN, "true", "是否启用密码认证"),

    /**
     * 密码最小长度
     */
    PASSWORD_MIN_LENGTH("password.min.length", TenantConfigType.INTEGER, "6", "密码最小长度"),

    /**
     * 密码最大长度
     */
    PASSWORD_MAX_LENGTH("password.max.length", TenantConfigType.INTEGER, "32", "密码最大长度"),

    /**
     * 是否要求密码包含数字
     */
    PASSWORD_REQUIRE_DIGIT("password.require.digit", TenantConfigType.BOOLEAN, "false", "是否要求密码包含数字"),

    /**
     * 是否要求密码包含字母
     */
    PASSWORD_REQUIRE_LETTER("password.require.letter", TenantConfigType.BOOLEAN, "false", "是否要求密码包含字母"),

    /**
     * 是否要求密码包含特殊字符
     */
    PASSWORD_REQUIRE_SPECIAL("password.require.special", TenantConfigType.BOOLEAN, "false", "是否要求密码包含特殊字符"),

    // ========== 短信认证配置 ==========

    /**
     * 是否启用短信认证
     */
    SMS_AUTH_ENABLED("sms.auth.enabled", TenantConfigType.BOOLEAN, "true", "是否启用短信认证"),

    /**
     * 短信验证码长度
     */
    SMS_CODE_LENGTH("sms.code.length", TenantConfigType.INTEGER, "6", "短信验证码长度"),

    /**
     * 短信验证码过期时间（秒）
     */
    SMS_CODE_EXPIRATION("sms.code.expiration", TenantConfigType.INTEGER, "300", "短信验证码过期时间（秒）"),

    /**
     * 短信发送间隔（秒）
     */
    SMS_SEND_INTERVAL("sms.send.interval", TenantConfigType.INTEGER, "60", "短信发送间隔（秒）"),

    /**
     * 每日最大发送次数
     */
    SMS_MAX_SEND_COUNT("sms.max.send.count", TenantConfigType.INTEGER, "10", "每日最大发送次数"),

    /**
     * 短信服务提供商
     */
    SMS_PROVIDER("sms.provider", TenantConfigType.STRING, "default", "短信服务提供商"),

    /**
     * 短信API密钥
     */
    SMS_API_KEY("sms.api.key", TenantConfigType.PASSWORD, null, "短信API密钥"),

    /**
     * 短信API密钥ID
     */
    SMS_API_SECRET("sms.api.secret", TenantConfigType.PASSWORD, null, "短信API密钥"),

    // ========== 企业微信认证配置 ==========

    /**
     * 是否启用企业微信认证
     */
    WECOM_AUTH_ENABLED("wecom.auth.enabled", TenantConfigType.BOOLEAN, "false", "是否启用企业微信认证"),

    /**
     * 企业微信企业ID
     */
    WECOM_CORP_ID("wecom.corp.id", TenantConfigType.STRING, null, "企业微信企业ID"),

    /**
     * 企业微信应用ID
     */
    WECOM_AGENT_ID("wecom.agent.id", TenantConfigType.STRING, null, "企业微信应用ID"),

    /**
     * 企业微信应用密钥
     */
    WECOM_CORP_SECRET("wecom.corp.secret", TenantConfigType.PASSWORD, null, "企业微信应用密钥"),

    // ========== 钉钉认证配置 ==========

    /**
     * 是否启用钉钉认证
     */
    DINGTALK_AUTH_ENABLED("dingtalk.auth.enabled", TenantConfigType.BOOLEAN, "false", "是否启用钉钉认证"),

    /**
     * 钉钉应用Key
     */
    DINGTALK_APP_KEY("dingtalk.app.key", TenantConfigType.STRING, null, "钉钉应用Key"),

    /**
     * 钉钉应用密钥
     */
    DINGTALK_APP_SECRET("dingtalk.app.secret", TenantConfigType.PASSWORD, null, "钉钉应用密钥"),

    /**
     * 钉钉企业ID
     */
    DINGTALK_CORP_ID("dingtalk.corp.id", TenantConfigType.STRING, null, "钉钉企业ID"),

    // ========== 微信认证配置 ==========

    /**
     * 是否启用微信认证
     */
    WECHAT_AUTH_ENABLED("wechat.auth.enabled", TenantConfigType.BOOLEAN, "false", "是否启用微信认证"),

    /**
     * 微信应用ID
     */
    WECHAT_APP_ID("wechat.app.id", TenantConfigType.STRING, null, "微信应用ID"),

    /**
     * 微信应用密钥
     */
    WECHAT_APP_SECRET("wechat.app.secret", TenantConfigType.PASSWORD, null, "微信应用密钥"),

    // ========== 飞书认证配置 ==========

    /**
     * 是否启用飞书认证
     */
    FEISHU_AUTH_ENABLED("feishu.auth.enabled", TenantConfigType.BOOLEAN, "false", "是否启用飞书认证"),

    /**
     * 飞书应用ID
     */
    FEISHU_APP_ID("feishu.app.id", TenantConfigType.STRING, null, "飞书应用ID"),

    /**
     * 飞书应用密钥
     */
    FEISHU_APP_SECRET("feishu.app.secret", TenantConfigType.PASSWORD, null, "飞书应用密钥"),

    // ========== 缓存配置 ==========

    /**
     * 是否启用缓存
     */
    CACHE_ENABLED("cache.enabled", TenantConfigType.BOOLEAN, "true", "是否启用缓存"),

    /**
     * 缓存类型
     */
    CACHE_TYPE("cache.type", TenantConfigType.STRING, "caffeine", "缓存类型：caffeine, redis"),

    /**
     * 缓存TTL（秒）
     */
    CACHE_TTL("cache.ttl", TenantConfigType.INTEGER, "300", "缓存生存时间（秒）"),

    // ========== 安全配置 ==========

    /**
     * 登录失败最大次数
     */
    SECURITY_MAX_LOGIN_ATTEMPTS("security.max.login.attempts", TenantConfigType.INTEGER, "5", "登录失败最大次数"),

    /**
     * 账户锁定时间（秒）
     */
    SECURITY_LOCKOUT_DURATION("security.lockout.duration", TenantConfigType.INTEGER, "1800", "账户锁定时间（秒）"),

    /**
     * 是否启用IP白名单
     */
    SECURITY_IP_WHITELIST_ENABLED("security.ip.whitelist.enabled", TenantConfigType.BOOLEAN, "false", "是否启用IP白名单"),

    /**
     * IP白名单
     */
    SECURITY_IP_WHITELIST("security.ip.whitelist", TenantConfigType.JSON_ARRAY, "[]", "IP白名单列表");

    /**
     * 配置键
     */
    private final String key;

    /**
     * 配置类型
     */
    private final TenantConfigType type;

    /**
     * 默认值
     */
    private final String defaultValue;

    /**
     * 配置描述
     */
    private final String description;

    TenantConfigKey(String key, TenantConfigType type, String defaultValue, String description) {
        this.key = key;
        this.type = type;
        this.defaultValue = defaultValue;
        this.description = description;
    }

    /**
     * 获取配置键
     *
     * @return 配置键
     */
    public String getKey() {
        return key;
    }

    /**
     * 获取配置类型
     *
     * @return 配置类型
     */
    public TenantConfigType getType() {
        return type;
    }

    /**
     * 获取默认值
     *
     * @return 默认值
     */
    public String getDefaultValue() {
        return defaultValue;
    }

    /**
     * 获取配置描述
     *
     * @return 配置描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * 检查是否为敏感配置
     *
     * @return 如果是敏感配置返回true，否则返回false
     */
    public boolean isSensitive() {
        return type.isSensitive();
    }

    /**
     * 根据配置键查找枚举值
     *
     * @param key 配置键
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static TenantConfigKey fromKey(String key) {
        if (key == null || key.trim().isEmpty()) {
            return null;
        }

        for (TenantConfigKey configKey : values()) {
            if (configKey.key.equals(key.trim())) {
                return configKey;
            }
        }

        return null;
    }

    /**
     * 获取所有认证相关的配置键
     *
     * @return 认证相关的配置键数组
     */
    public static TenantConfigKey[] getAuthConfigKeys() {
        return new TenantConfigKey[]{
            AUTH_DEFAULT_GRANT_TYPE, AUTH_MULTI_PROVIDER_ENABLED,
            JWT_SECRET, JWT_ISSUER, JWT_AUDIENCE, JWT_ACCESS_TOKEN_EXPIRATION, JWT_REFRESH_TOKEN_EXPIRATION,
            PASSWORD_AUTH_ENABLED, SMS_AUTH_ENABLED,
            WECOM_AUTH_ENABLED, DINGTALK_AUTH_ENABLED, WECHAT_AUTH_ENABLED, FEISHU_AUTH_ENABLED
        };
    }

    /**
     * 获取所有敏感配置键
     *
     * @return 敏感配置键数组
     */
    public static TenantConfigKey[] getSensitiveConfigKeys() {
        return new TenantConfigKey[]{
            JWT_SECRET, SMS_API_KEY, SMS_API_SECRET,
            WECOM_CORP_SECRET, DINGTALK_APP_SECRET, WECHAT_APP_SECRET, FEISHU_APP_SECRET
        };
    }

    @Override
    public String toString() {
        return key;
    }
}
