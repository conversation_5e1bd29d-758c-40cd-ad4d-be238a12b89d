-- HandThing多租户认证系统数据库表结构
-- 版本: V3.0.0
-- 作者: HandThing
-- 创建时间: 2025-07-29

-- ========================================
-- 租户配置表
-- ========================================
CREATE TABLE IF NOT EXISTS tenant_configs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    tenant_id VARCHAR(64) NOT NULL,
    config_key VARCHAR(128) NOT NULL,
    config_value TEXT,
    config_type VARCHAR(32) DEFAULT 'STRING',
    description VARCHAR(255),
    enabled BOOLEAN DEFAULT TRUE,
    sensitive BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(64),
    updated_by VARCHAR(64),
    version BIGINT DEFAULT 0
);

-- 创建唯一索引
CREATE UNIQUE INDEX IF NOT EXISTS uk_tenant_config ON tenant_configs (tenant_id, config_key);

-- 创建普通索引
CREATE INDEX IF NOT EXISTS idx_tenant_id ON tenant_configs (tenant_id);
CREATE INDEX IF NOT EXISTS idx_config_key ON tenant_configs (config_key);
CREATE INDEX IF NOT EXISTS idx_enabled ON tenant_configs (enabled);

-- ========================================
-- 租户信息表（可选）
-- ========================================
CREATE TABLE IF NOT EXISTS tenants (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    tenant_id VARCHAR(64) UNIQUE NOT NULL,
    tenant_name VARCHAR(128) NOT NULL,
    tenant_code VARCHAR(64),
    status VARCHAR(32) DEFAULT 'ACTIVE',
    description TEXT,
    contact_email VARCHAR(255),
    contact_phone VARCHAR(32),
    contact_person VARCHAR(128),
    domain VARCHAR(255),
    logo_url VARCHAR(512),
    theme_config VARCHAR(2000),
    feature_flags VARCHAR(2000),
    quota_config VARCHAR(2000),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(64),
    updated_by VARCHAR(64),
    version BIGINT DEFAULT 0
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_tenant_status ON tenants (status);
CREATE INDEX IF NOT EXISTS idx_tenant_domain ON tenants (domain);
