package cn.com.handthing.starter.knife4j.configuration;

import cn.com.handthing.starter.apidoc.properties.ApiDocProperties;
import cn.com.handthing.starter.knife4j.properties.Knife4jProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.provisioning.InMemoryUserDetailsManager;
import org.springframework.security.web.SecurityFilterChain;

import static org.springframework.security.config.Customizer.withDefaults;

/**
 * Knife4j 安全配置类
 *
 * <p>继承 api-doc 的安全配置，并扩展 Knife4j 特有的功能。
 * <p>当 knife4j 认证启用时，会覆盖 api-doc 的认证配置，提供统一的认证体验。
 * <p>由于 Knife4jProperties 继承了 ApiDocProperties，所以认证配置也是继承的。
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Configuration
@EnableWebSecurity
@ConditionalOnProperty(prefix = "handthing.api-doc.auth", name = "enabled", havingValue = "true", matchIfMissing = false)
@Order(1) // 设置高优先级，确保在其他安全配置之前执行
public class Knife4jSecurityConfiguration {

    private static final Logger log = LoggerFactory.getLogger(Knife4jSecurityConfiguration.class);

    public Knife4jSecurityConfiguration() {
        log.info("Knife4jSecurityConfiguration is being created!");
    }

    // 定义需要认证的路径，包含 api-doc 和 knife4j 的所有路径
    private static final String[] PROTECTED_URLS = {
            // Knife4j 特有路径
            "/doc.html",
            "/doc.html/**",
            // 共同路径
            "/swagger-ui.html",
            "/swagger-ui/**",
            "/v3/api-docs/**",
            "/swagger-resources/**",
            "/webjars/**"
    };

    @Bean
    public InMemoryUserDetailsManager unifiedUserDetailsManager(Knife4jProperties knife4jProperties) {
        // 使用继承的认证配置（Knife4jProperties 继承了 ApiDocProperties 的 auth 配置）
        ApiDocProperties.Auth auth = knife4jProperties.getAuth();

        // 创建用户，同时拥有 KNIFE4J_USER 和 DOC_USER 角色
        UserDetails user = User.builder()
                .username(auth.getUsername())
                .password("{noop}" + auth.getPassword())
                .roles("KNIFE4J_USER", "DOC_USER") // 同时拥有两种角色，确保兼容性
                .build();

        InMemoryUserDetailsManager userDetailsManager = new InMemoryUserDetailsManager(user);

        log.info("Knife4j security configured with inherited user: {}", auth.getUsername());
        return userDetailsManager;
    }

    @Bean
    public SecurityFilterChain unifiedSecurityFilterChain(HttpSecurity http, InMemoryUserDetailsManager userDetailsManager) throws Exception {
        http
            .authorizeHttpRequests(authorize -> authorize
                .requestMatchers(PROTECTED_URLS).authenticated() // 所有文档相关路径需要认证
                .anyRequest().permitAll() // 其他所有请求都允许
            )
            .httpBasic(withDefaults()) // 启用 HTTP Basic 认证
            .formLogin(withDefaults()) // 启用表单登录
            .csrf(AbstractHttpConfigurer::disable) // 关闭 CSRF
            .userDetailsService(userDetailsManager); // 设置用户详情服务

        return http.build();
    }
}