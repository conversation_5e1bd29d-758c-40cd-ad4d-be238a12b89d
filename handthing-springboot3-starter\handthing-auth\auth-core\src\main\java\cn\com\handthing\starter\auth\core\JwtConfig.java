package cn.com.handthing.starter.auth.core;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.time.Duration;

/**
 * JWT配置类
 * <p>
 * 定义JWT令牌的相关配置，包括密钥、过期时间、签发者等信息。
 * 支持访问令牌和刷新令牌的不同配置。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
@ConfigurationProperties(prefix = "handthing.auth.jwt")
public class JwtConfig {

    /**
     * 是否启用JWT
     */
    private boolean enabled = true;

    /**
     * JWT密钥
     */
    private String secret = "handthing-auth-default-secret-key-please-change-in-production";

    /**
     * 签发者
     */
    private String issuer = "handthing-auth";

    /**
     * 受众
     */
    private String audience = "handthing-app";

    /**
     * 访问令牌过期时间
     */
    private Duration accessTokenExpiration = Duration.ofHours(2);

    /**
     * 刷新令牌过期时间
     */
    private Duration refreshTokenExpiration = Duration.ofDays(7);

    /**
     * 令牌前缀
     */
    private String tokenPrefix = "Bearer ";

    /**
     * 请求头名称
     */
    private String headerName = "Authorization";

    /**
     * 请求参数名称
     */
    private String parameterName = "access_token";

    /**
     * 是否允许在URL参数中传递令牌
     */
    private boolean allowUrlParameter = false;

    /**
     * 是否在响应头中返回令牌
     */
    private boolean includeInResponseHeader = false;

    /**
     * 响应头名称
     */
    private String responseHeaderName = "X-Auth-Token";

    /**
     * 时钟偏移量（秒）
     */
    private long clockSkew = 60;

    /**
     * 是否验证签发者
     */
    private boolean validateIssuer = true;

    /**
     * 是否验证受众
     */
    private boolean validateAudience = false;

    /**
     * 是否验证过期时间
     */
    private boolean validateExpiration = true;

    /**
     * 是否验证生效时间
     */
    private boolean validateNotBefore = true;

    /**
     * 获取访问令牌过期时间（秒）
     *
     * @return 过期时间（秒）
     */
    public long getAccessTokenExpirationSeconds() {
        return accessTokenExpiration != null ? accessTokenExpiration.getSeconds() : 7200;
    }

    /**
     * 获取刷新令牌过期时间（秒）
     *
     * @return 过期时间（秒）
     */
    public long getRefreshTokenExpirationSeconds() {
        return refreshTokenExpiration != null ? refreshTokenExpiration.getSeconds() : 604800;
    }

    /**
     * 验证配置是否有效
     *
     * @return 如果配置有效返回true，否则返回false
     */
    public boolean isValid() {
        return secret != null && !secret.trim().isEmpty() &&
               issuer != null && !issuer.trim().isEmpty() &&
               accessTokenExpiration != null && !accessTokenExpiration.isNegative() &&
               refreshTokenExpiration != null && !refreshTokenExpiration.isNegative();
    }

    /**
     * 获取完整的令牌前缀
     *
     * @return 令牌前缀
     */
    public String getFullTokenPrefix() {
        return tokenPrefix != null ? tokenPrefix : "Bearer ";
    }

    /**
     * 从令牌字符串中提取纯令牌
     *
     * @param tokenString 令牌字符串
     * @return 纯令牌
     */
    public String extractToken(String tokenString) {
        if (tokenString == null || tokenString.trim().isEmpty()) {
            return null;
        }

        String prefix = getFullTokenPrefix();
        if (tokenString.startsWith(prefix)) {
            return tokenString.substring(prefix.length()).trim();
        }

        return tokenString.trim();
    }

    /**
     * 为令牌添加前缀
     *
     * @param token 纯令牌
     * @return 带前缀的令牌
     */
    public String addTokenPrefix(String token) {
        if (token == null || token.trim().isEmpty()) {
            return null;
        }

        return getFullTokenPrefix() + token;
    }

    @Override
    public String toString() {
        return String.format("JwtConfig{enabled=%s, issuer='%s', audience='%s', accessTokenExpiration=%s, refreshTokenExpiration=%s}",
                enabled, issuer, audience, accessTokenExpiration, refreshTokenExpiration);
    }
}
