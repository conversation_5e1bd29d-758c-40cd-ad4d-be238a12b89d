# App Connector Spring Boot Starter 任务清单

**项目名称**: `app-connector-spring-boot-starter`  
**作者**: HandThing  
**版本**: V1.0.0  
**基于**: `http-client-spring-boot-starter`

## 📋 总体架构

### 多模块Maven项目结构
```
app-connector/
├── app-connector-core/                    # 核心模块
├── app-connector-spring-boot-starter/     # 自动配置模块
├── wecom-connector/                       # 企业微信连接器
├── dingtalk-connector/                    # 钉钉连接器
├── douyin-connector/                      # 抖音连接器
├── alipay-connector/                      # 支付宝连接器
└── pom.xml                               # 父POM
```

## 🎯 开发优先级与步骤

### 阶段一：核心基础设施 (优先级：高) ✅
1. **创建Maven多模块项目结构** ✅
2. **开发 app-connector-core 模块** ✅
3. **开发 app-connector-spring-boot-starter 模块** ✅

### 阶段二：平台连接器实现 (优先级：中) ✅
4. **开发 wecom-connector 模块** (作为示例实现) ✅
5. **开发 dingtalk-connector 模块** ✅
6. **开发 douyin-connector 模块** ✅
7. **开发 alipay-connector 模块** ✅
8. **平台连接器实现完成** ✅

### 阶段三：测试与完善 (优先级：中) ✅
6. **编写单元测试** ✅
7. **在test-app中进行集成测试** ✅
8. **创建综合测试应用和API接口** ✅

---

## 📦 模块详细任务清单

### 1. app-connector-core 模块

#### 1.1 认证授权框架 ✅
- [x] **AuthProvider.java** - 认证提供者策略接口 ✅
  - 定义统一的认证方法：getAuthorizationUrl(), exchangeToken(), refreshToken()
  - 支持自定义state参数和回调处理

- [x] **AuthService.java** - 统一认证服务 ✅
  - 管理多个AuthProvider实例
  - 提供统一的认证入口方法
  - 支持按平台类型路由到对应的AuthProvider

- [x] **UnifiedAccessToken.java** - 统一访问令牌模型 ✅
  - 包含字段：accessToken, refreshToken, expiresIn, tokenType, scope
  - 支持序列化/反序列化

- [x] **UnifiedUserInfo.java** - 统一用户信息模型 ✅
  - 包含字段：openId, unionId, nickname, avatar, email, mobile
  - 支持平台特定字段的扩展

#### 1.2 平台枚举与配置 ✅
- [x] **PlatformType.java** - 平台类型枚举 ✅
  - 定义：WECOM, DINGTALK, DOUYIN, ALIPAY, WECHAT等
  - 包含平台名称、代码等属性

- [x] **PlatformConfig.java** - 平台配置基类 ✅
  - 定义通用配置字段：enabled, clientId, clientSecret, redirectUri
  - 支持子类扩展特定配置

- [x] **~~TokenStoreType.java~~** - ~~Token存储类型枚举~~ ✅ (已移除，使用缓存配置)
  - ~~定义：MEMORY, REDIS~~
  - ~~支持扩展其他存储类型~~

#### 1.3 异常体系 ✅
- [x] **ConnectorException.java** - 基础异常类 ✅
  - 包含错误码、错误消息、平台类型等信息
  - 支持异常链

- [x] **AuthException.java** - 认证异常 ✅
  - 继承ConnectorException
  - 专门处理认证相关错误

- [x] **ApiCallException.java** - API调用异常 ✅
  - 继承ConnectorException
  - 处理API调用失败、限流等错误

#### 1.4 事件模型 ✅
- [x] **ConnectorEvent.java** - 基础事件类 ✅
  - 继承ApplicationEvent
  - 包含平台类型、时间戳等基础信息

- [x] **UserAuthorizedEvent.java** - 用户授权成功事件 ✅
  - 继承ConnectorEvent
  - 包含用户信息、访问令牌等

### 2. app-connector-spring-boot-starter 模块 ✅

#### 2.1 依赖集成 ✅
- [x] **添加缓存依赖** - 集成现有缓存基础设施 ✅
  - 依赖 `cache-spring-boot-starter` 核心抽象
  - 支持用户选择具体缓存实现（caffeine、redis、memory、level）
  - 复用成熟的缓存架构和配置

#### 2.2 配置与自动装配 ✅
- [x] **AppConnectorProperties.java** - 配置属性类 ✅
  - 使用@ConfigurationProperties绑定配置
  - 支持多平台配置：wecom, dingtalk, douyin, alipay等
  - 包含全局配置：redirectUriPrefix等（移除tokenStoreType）
  - 集成handthing.cache配置体系

- [x] **AppConnectorAutoConfiguration.java** - 自动配置类 ✅
  - 根据配置条件装配相应的Bean
  - 注册AuthService、TokenManager等核心服务
  - 支持按需装配平台连接器
  - 依赖CacheService进行Token管理

#### 2.3 回调处理 ✅
- [x] **CallbackController.java** - 统一回调处理器 ✅
  - 提供统一回调端点：/connector/callback/{platform}
  - 自动处理code和state参数
  - 委托给相应的AuthProvider完成Token交换
  - 发布UserAuthorizedEvent事件

#### 2.4 Token管理（基于缓存服务） ✅
- [x] **TokenManager.java** - Token管理器 ✅
  - 基于CacheService实现Token存储和管理
  - 使用专门的缓存区域："app-connector-tokens"
  - 利用CacheService的TTL功能自动管理Token过期
  - 支持自动刷新过期Token
  - 提供Token缓存和检索功能
  - 支持所有缓存后端（内存、Caffeine、Redis、多级等）

### 3. wecom-connector 模块 (示例实现) ✅

#### 3.1 认证实现 ✅
- [x] **WeComAuthProvider.java** - 企业微信认证提供者 ✅
  - 实现AuthProvider接口
  - 处理企业微信OAuth2.0流程
  - 支持网页授权和扫码登录

- [x] **WeComConfig.java** - 企业微信配置 ✅
  - 继承PlatformConfig
  - 包含corpId, agentId, secret等企微特有配置

#### 3.2 API服务 ✅
- [x] **WeComService.java** - 企业微信服务主入口 ✅
  - 聚合各个API服务
  - 提供统一的服务访问入口

- [x] **WeComMessageApi.java** - 消息API ✅
  - 发送文本、图片、文件等消息
  - 支持群发和单发

- [x] **WeComContactApi.java** - 通讯录API ✅ (基础结构)
  - 获取部门列表、用户列表
  - 支持用户信息查询

- [x] **WeComCustomerApi.java** - 客户联系API ✅ (基础结构)
  - 获取客户列表
  - 管理客户标签

#### 3.3 模型类 ✅
- [x] **WeComUser.java** - 企业微信用户模型 ✅
- [x] **WeComMessage.java** - 企业微信消息模型 ✅
- [x] **WeComDepartment.java** - 企业微信部门模型 ✅

### 4. 其他平台连接器模块

#### 4.1 dingtalk-connector 模块
- [ ] **DingTalkAuthProvider.java** - 钉钉认证提供者
- [ ] **DingTalkService.java** - 钉钉服务主入口
- [ ] **DingTalkMessageApi.java** - 工作通知API
- [ ] **DingTalkContactApi.java** - 通讯录API
- [ ] **DingTalkTodoApi.java** - 待办事项API
- [ ] **DingTalkUser.java** - 钉钉用户模型
- [ ] **DingTalkMessage.java** - 钉钉消息模型
- [ ] **DingTalkTodo.java** - 钉钉待办模型

#### 4.2 douyin-connector 模块
- [ ] **DouyinAuthProvider.java** - 抖音认证提供者
- [ ] **DouyinService.java** - 抖音服务主入口
- [ ] **DouyinUserApi.java** - 用户API
- [ ] **DouyinVideoApi.java** - 视频管理API
- [ ] **DouyinPoiApi.java** - POI搜索API
- [ ] **DouyinUser.java** - 抖音用户模型
- [ ] **DouyinVideo.java** - 抖音视频模型
- [ ] **DouyinPoi.java** - 抖音POI模型

#### 4.3 alipay-connector 模块
- [ ] **AlipayAuthProvider.java** - 支付宝认证提供者
- [ ] **AlipayService.java** - 支付宝服务主入口
- [ ] **AlipayPaymentApi.java** - 支付API
- [ ] **AlipayUserApi.java** - 用户授权API
- [ ] **AlipayMiniProgramApi.java** - 小程序能力API
- [ ] **AlipayUser.java** - 支付宝用户模型
- [ ] **AlipayPayment.java** - 支付宝支付模型
- [ ] **AlipayMiniProgram.java** - 支付宝小程序模型

### 5. 测试与文档

#### 5.1 单元测试
- [ ] **核心模块单元测试** - 测试AuthService、TokenManager等核心功能
- [ ] **平台连接器单元测试** - 测试各平台的认证和API调用
- [ ] **自动配置测试** - 测试Spring Boot自动配置功能

#### 5.2 集成测试
- [ ] **test-app集成测试** - 在测试应用中验证完整功能
- [ ] **多平台集成测试** - 测试多个平台同时使用的场景

#### 5.3 文档
- [ ] **README.md** - 项目说明和快速开始指南
- [ ] **API文档** - 详细的API使用文档
- [ ] **配置说明** - 各平台配置参数说明

---

## ⚙️ 技术要求

### 代码规范
- **作者**: HandThing
- **版本**: V1.0.0
- **JDK版本**: JDK17及以上
- **代码风格**: Spring命名规范
- **代码简化**: 使用Lombok

### 依赖优先级
1. handthing-core工具类
2. 现有的handthing starter（如cache-spring-boot-starter）
3. JDK标准库
4. Spring框架
5. Apache Commons
6. 自定义实现

### 缓存集成要求
- **必须依赖** `cache-spring-boot-starter` 进行Token存储
- **禁止重复实现** 缓存功能，复用现有缓存架构
- **支持多后端** 通过配置选择缓存实现（内存、Caffeine、Redis、多级）
- **统一配置** 使用 handthing.cache 配置体系或者完成现有的handthing-core工具类

### 设计模式
- **策略模式**: AuthProvider实现
- **工厂模式**: 服务创建
- **观察者模式**: 事件发布订阅
- **模板方法模式**: API调用模板

### 质量要求
- **编译**: 必须通过Maven编译打包
- **测试**: 核心功能需要单元测试
- **注释**: 类和方法需要完整注释
- **异常**: 统一的异常处理机制

---

## 📝 配置说明

### 缓存集成配置示例

基于现有的 `cache-spring-boot-starter`，Token存储配置示例：

#### 使用Redis缓存（推荐用于生产环境）
```yaml
handthing:
  # 缓存配置
  cache:
    enabled: true
    type: redis
    default-ttl: 2h
    specs:
      app-connector-tokens:  # Token专用缓存区域
        ttl: 2h
        max-size: 10000
    redis:
      key-prefix: "handthing:connector:tokens:"
      default-ttl: 2h

  # 应用连接器配置
  connector:
    redirect-uri-prefix: "https://your.domain.com/auth"
    wecom:
      enabled: true
      corp-id: "your-wecom-corp-id"
      agent-id: "your-wecom-agent-id"
      secret: "your-wecom-secret"
```

#### 使用Caffeine缓存（推荐用于单机环境）
```yaml
handthing:
  cache:
    enabled: true
    type: caffeine
    specs:
      app-connector-tokens:
        ttl: 2h
        spec: "maximumSize=10000,expireAfterWrite=2h,recordStats"
```

#### 使用多级缓存（推荐用于高性能场景）
```yaml
handthing:
  cache:
    enabled: true
    type: level
    specs:
      app-connector-tokens:
        ttl: 2h
        level:
          l1-spec: "maximumSize=1000,expireAfterWrite=30m"  # 本地缓存
          l2-ttl: 2h  # Redis缓存
```

### 依赖配置

用户需要根据选择的缓存类型添加相应依赖：

```xml
<!-- 基础依赖 -->
<dependency>
    <groupId>cn.com.handthing.springboot3.starter</groupId>
    <artifactId>app-connector-spring-boot-starter</artifactId>
    <version>${revision}</version>
</dependency>

<!-- 选择缓存实现 -->
<!-- Redis缓存 -->
<dependency>
    <groupId>cn.com.handthing.springboot3.starter</groupId>
    <artifactId>redis-cache-spring-boot-starter</artifactId>
    <version>${revision}</version>
</dependency>

<!-- 或者 Caffeine缓存 -->
<dependency>
    <groupId>cn.com.handthing.springboot3.starter</groupId>
    <artifactId>caffeine-cache-spring-boot-starter</artifactId>
    <version>${revision}</version>
</dependency>

<!-- 或者 多级缓存 -->
<dependency>
    <groupId>cn.com.handthing.springboot3.starter</groupId>
    <artifactId>level-cache-spring-boot-starter</artifactId>
    <version>${revision}</version>
</dependency>
```

---

## 🚀 开始开发

请确认是否开始按照此任务清单进行开发？建议从**阶段一：核心基础设施**开始，首先创建Maven多模块项目结构和app-connector-core模块。

### 优化后的优势

通过集成现有的缓存 starter，我们获得了以下优势：

1. **统一架构** - 复用成熟的缓存抽象和实现
2. **灵活选择** - 支持内存、Caffeine、Redis、多级等多种缓存后端
3. **配置统一** - 使用统一的 handthing.cache 配置体系
4. **功能完整** - 自动获得TTL、统计、监控等功能
5. **减少维护** - 避免重复实现缓存功能
6. **更好扩展** - 未来新增缓存类型时自动支持
