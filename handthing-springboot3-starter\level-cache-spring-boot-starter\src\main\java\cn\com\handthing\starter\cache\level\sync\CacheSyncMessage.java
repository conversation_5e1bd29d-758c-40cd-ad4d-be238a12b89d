package cn.com.handthing.starter.cache.level.sync;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 缓存同步消息
 * <p>
 * 用于多级缓存之间的同步通信
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CacheSyncMessage implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 消息ID
     */
    private String messageId;

    /**
     * 发送者ID
     */
    private String senderId;

    /**
     * 缓存名称
     */
    private String cacheName;

    /**
     * 缓存键
     */
    private String key;

    /**
     * 操作类型
     */
    private OperationType operation;

    /**
     * 消息发送时间
     */
    private LocalDateTime timestamp;

    /**
     * 操作类型枚举
     */
    public enum OperationType {
        /**
         * 删除操作
         */
        EVICT,
        /**
         * 清空操作
         */
        CLEAR,
        /**
         * 更新操作
         */
        PUT
    }

    /**
     * 创建删除消息
     *
     * @param senderId  发送者ID
     * @param cacheName 缓存名称
     * @param key       缓存键
     * @return 删除消息
     */
    public static CacheSyncMessage evict(String senderId, String cacheName, String key) {
        return CacheSyncMessage.builder()
                .messageId(generateMessageId())
                .senderId(senderId)
                .cacheName(cacheName)
                .key(key)
                .operation(OperationType.EVICT)
                .timestamp(LocalDateTime.now())
                .build();
    }

    /**
     * 创建清空消息
     *
     * @param senderId  发送者ID
     * @param cacheName 缓存名称
     * @return 清空消息
     */
    public static CacheSyncMessage clear(String senderId, String cacheName) {
        return CacheSyncMessage.builder()
                .messageId(generateMessageId())
                .senderId(senderId)
                .cacheName(cacheName)
                .operation(OperationType.CLEAR)
                .timestamp(LocalDateTime.now())
                .build();
    }

    /**
     * 创建更新消息
     *
     * @param senderId  发送者ID
     * @param cacheName 缓存名称
     * @param key       缓存键
     * @return 更新消息
     */
    public static CacheSyncMessage put(String senderId, String cacheName, String key) {
        return CacheSyncMessage.builder()
                .messageId(generateMessageId())
                .senderId(senderId)
                .cacheName(cacheName)
                .key(key)
                .operation(OperationType.PUT)
                .timestamp(LocalDateTime.now())
                .build();
    }

    /**
     * 生成消息ID
     *
     * @return 消息ID
     */
    private static String generateMessageId() {
        return System.currentTimeMillis() + "-" + Thread.currentThread().getId();
    }
}