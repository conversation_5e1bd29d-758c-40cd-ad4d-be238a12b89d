<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>cn.com.handthing.springboot3.starter</groupId>
        <artifactId>starter-parent</artifactId>
        <version>${revision}</version>
        <relativePath>../starter-parent</relativePath>
    </parent>

    <artifactId>app-connector</artifactId>
    <packaging>pom</packaging>

    <name>HandThing :: App Connector</name>
    <description>企业级第三方应用连接器，统一和简化对企业微信、钉钉、飞书、微信、支付宝、抖音等主流开放平台的集成</description>

    <!--
      App Connector 多模块聚合器
      包含核心模块、自动配置模块和各平台连接器模块
    -->
    <modules>
        <module>app-connector-core</module>
        <module>app-connector-spring-boot-starter</module>
        <module>wecom-connector</module>
        <module>dingtalk-connector</module>
        <module>douyin-connector</module>
        <module>alipay-connector</module>
        <module>wechat-connector</module>
        <module>feishu-connector</module>
        <module>toutiao-connector</module>
        <module>xiaohongshu-connector</module>
        <module>bilibili-connector</module>
    </modules>

    <properties>
        <!-- App Connector 特定属性 -->
        <app-connector.version>${revision}</app-connector.version>
    </properties>

    <!-- 依赖管理 -->
    <dependencyManagement>
        <dependencies>
            <!-- App Connector 核心模块 -->
            <dependency>
                <groupId>cn.com.handthing.springboot3.starter</groupId>
                <artifactId>app-connector-core</artifactId>
                <version>${app-connector.version}</version>
            </dependency>

            <!-- App Connector 自动配置 -->
            <dependency>
                <groupId>cn.com.handthing.springboot3.starter</groupId>
                <artifactId>app-connector-spring-boot-starter</artifactId>
                <version>${app-connector.version}</version>
            </dependency>

            <!-- 平台连接器模块 -->
            <dependency>
                <groupId>cn.com.handthing.springboot3.starter</groupId>
                <artifactId>wecom-connector</artifactId>
                <version>${app-connector.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.com.handthing.springboot3.starter</groupId>
                <artifactId>dingtalk-connector</artifactId>
                <version>${app-connector.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.com.handthing.springboot3.starter</groupId>
                <artifactId>douyin-connector</artifactId>
                <version>${app-connector.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.com.handthing.springboot3.starter</groupId>
                <artifactId>alipay-connector</artifactId>
                <version>${app-connector.version}</version>
            </dependency>

            <!-- 缓存依赖 -->
            <dependency>
                <groupId>cn.com.handthing.springboot3.starter</groupId>
                <artifactId>cache-spring-boot-starter</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- HTTP客户端依赖 -->
            <dependency>
                <groupId>cn.com.handthing.springboot3.starter</groupId>
                <artifactId>http-client-spring-boot-starter</artifactId>
                <version>${revision}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

</project>
