{"groups": [{"name": "handthing.auth.third-party", "type": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties"}, {"name": "handthing.auth.third-party.alipay", "type": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties$AlipayConfig", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties", "sourceMethod": "public cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties.AlipayConfig getAlipay() "}, {"name": "handthing.auth.third-party.common", "type": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties$CommonConfig", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties", "sourceMethod": "public cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties.CommonConfig getCommon() "}, {"name": "handthing.auth.third-party.dingtalk", "type": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties$DingtalkConfig", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties", "sourceMethod": "public cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties.DingtalkConfig getDingtalk() "}, {"name": "handthing.auth.third-party.douyin", "type": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties$DouyinConfig", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties", "sourceMethod": "public cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties.DouyinConfig getDouyin() "}, {"name": "handthing.auth.third-party.feishu", "type": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties$FeishuConfig", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties", "sourceMethod": "public cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties.FeishuConfig getFeishu() "}, {"name": "handthing.auth.third-party.wechat", "type": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties$WechatConfig", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties", "sourceMethod": "public cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties.WechatConfig getWechat() "}, {"name": "handthing.auth.third-party.wecom", "type": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties$WecomConfig", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties", "sourceMethod": "public cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties.WecomConfig getWecom() "}], "properties": [{"name": "handthing.auth.third-party.alipay.api-timeout", "type": "java.time.Duration", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties$AlipayConfig"}, {"name": "handthing.auth.third-party.alipay.app-id", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties$AlipayConfig"}, {"name": "handthing.auth.third-party.alipay.auto-register", "type": "java.lang.Bo<PERSON>an", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties$AlipayConfig"}, {"name": "handthing.auth.third-party.alipay.enabled", "type": "java.lang.Bo<PERSON>an", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties$AlipayConfig"}, {"name": "handthing.auth.third-party.alipay.endpoints-enabled", "type": "java.lang.Bo<PERSON>an", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties$AlipayConfig"}, {"name": "handthing.auth.third-party.alipay.private-key", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties$AlipayConfig"}, {"name": "handthing.auth.third-party.alipay.public-key", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties$AlipayConfig"}, {"name": "handthing.auth.third-party.alipay.redirect-uri", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties$AlipayConfig"}, {"name": "handthing.auth.third-party.common.access-token-expiration", "type": "java.time.Duration", "description": "访问令牌过期时间", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties$CommonConfig"}, {"name": "handthing.auth.third-party.common.account-lock-duration", "type": "java.time.Duration", "description": "账户锁定持续时间", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties$CommonConfig"}, {"name": "handthing.auth.third-party.common.attribute-mapping", "type": "java.util.Map<java.lang.String,java.lang.String>", "description": "自定义属性映射", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties$CommonConfig"}, {"name": "handthing.auth.third-party.common.max-login-attempts", "type": "java.lang.Integer", "description": "最大登录尝试次数", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties$CommonConfig"}, {"name": "handthing.auth.third-party.common.refresh-token-expiration", "type": "java.time.Duration", "description": "刷新令牌过期时间", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties$CommonConfig"}, {"name": "handthing.auth.third-party.common.token-cache-enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用令牌缓存", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties$CommonConfig"}, {"name": "handthing.auth.third-party.common.token-cache-expiration", "type": "java.time.Duration", "description": "令牌缓存过期时间", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties$CommonConfig"}, {"name": "handthing.auth.third-party.dingtalk.api-timeout", "type": "java.time.Duration", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties$DingtalkConfig"}, {"name": "handthing.auth.third-party.dingtalk.app-key", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties$DingtalkConfig"}, {"name": "handthing.auth.third-party.dingtalk.app-secret", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties$DingtalkConfig"}, {"name": "handthing.auth.third-party.dingtalk.auto-register", "type": "java.lang.Bo<PERSON>an", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties$DingtalkConfig"}, {"name": "handthing.auth.third-party.dingtalk.enabled", "type": "java.lang.Bo<PERSON>an", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties$DingtalkConfig"}, {"name": "handthing.auth.third-party.dingtalk.endpoints-enabled", "type": "java.lang.Bo<PERSON>an", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties$DingtalkConfig"}, {"name": "handthing.auth.third-party.dingtalk.redirect-uri", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties$DingtalkConfig"}, {"name": "handthing.auth.third-party.douyin.api-timeout", "type": "java.time.Duration", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties$DouyinConfig"}, {"name": "handthing.auth.third-party.douyin.auto-register", "type": "java.lang.Bo<PERSON>an", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties$DouyinConfig"}, {"name": "handthing.auth.third-party.douyin.client-key", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties$DouyinConfig"}, {"name": "handthing.auth.third-party.douyin.client-secret", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties$DouyinConfig"}, {"name": "handthing.auth.third-party.douyin.enabled", "type": "java.lang.Bo<PERSON>an", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties$DouyinConfig"}, {"name": "handthing.auth.third-party.douyin.endpoints-enabled", "type": "java.lang.Bo<PERSON>an", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties$DouyinConfig"}, {"name": "handthing.auth.third-party.douyin.redirect-uri", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties$DouyinConfig"}, {"name": "handthing.auth.third-party.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用第三方认证", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties"}, {"name": "handthing.auth.third-party.feishu.api-timeout", "type": "java.time.Duration", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties$FeishuConfig"}, {"name": "handthing.auth.third-party.feishu.app-id", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties$FeishuConfig"}, {"name": "handthing.auth.third-party.feishu.app-secret", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties$FeishuConfig"}, {"name": "handthing.auth.third-party.feishu.auto-register", "type": "java.lang.Bo<PERSON>an", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties$FeishuConfig"}, {"name": "handthing.auth.third-party.feishu.enabled", "type": "java.lang.Bo<PERSON>an", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties$FeishuConfig"}, {"name": "handthing.auth.third-party.feishu.endpoints-enabled", "type": "java.lang.Bo<PERSON>an", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties$FeishuConfig"}, {"name": "handthing.auth.third-party.feishu.redirect-uri", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties$FeishuConfig"}, {"name": "handthing.auth.third-party.wechat.api-timeout", "type": "java.time.Duration", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties$WechatConfig"}, {"name": "handthing.auth.third-party.wechat.app-id", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties$WechatConfig"}, {"name": "handthing.auth.third-party.wechat.app-secret", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties$WechatConfig"}, {"name": "handthing.auth.third-party.wechat.auto-register", "type": "java.lang.Bo<PERSON>an", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties$WechatConfig"}, {"name": "handthing.auth.third-party.wechat.enabled", "type": "java.lang.Bo<PERSON>an", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties$WechatConfig"}, {"name": "handthing.auth.third-party.wechat.endpoints-enabled", "type": "java.lang.Bo<PERSON>an", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties$WechatConfig"}, {"name": "handthing.auth.third-party.wechat.redirect-uri", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties$WechatConfig"}, {"name": "handthing.auth.third-party.wecom.agent-id", "type": "java.lang.String", "description": "应用ID", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties$WecomConfig"}, {"name": "handthing.auth.third-party.wecom.agent-secret", "type": "java.lang.String", "description": "应用密钥", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties$WecomConfig"}, {"name": "handthing.auth.third-party.wecom.api-timeout", "type": "java.time.Duration", "description": "API超时时间", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties$WecomConfig"}, {"name": "handthing.auth.third-party.wecom.auto-register", "type": "java.lang.Bo<PERSON>an", "description": "是否启用自动注册", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties$WecomConfig"}, {"name": "handthing.auth.third-party.wecom.corp-id", "type": "java.lang.String", "description": "企业ID", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties$WecomConfig"}, {"name": "handthing.auth.third-party.wecom.corp-secret", "type": "java.lang.String", "description": "企业密钥", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties$WecomConfig"}, {"name": "handthing.auth.third-party.wecom.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用企业微信认证", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties$WecomConfig"}, {"name": "handthing.auth.third-party.wecom.endpoints-enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用API端点", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties$WecomConfig"}, {"name": "handthing.auth.third-party.wecom.redirect-uri", "type": "java.lang.String", "description": "重定向URI", "sourceType": "cn.com.handthing.starter.auth.thirdparty.ThirdPartyAuthProperties$WecomConfig"}], "hints": [], "ignored": {"properties": []}}