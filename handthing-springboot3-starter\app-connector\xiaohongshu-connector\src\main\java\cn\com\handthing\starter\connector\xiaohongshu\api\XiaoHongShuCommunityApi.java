package cn.com.handthing.starter.connector.xiaohongshu.api;

import cn.com.handthing.starter.connector.xiaohongshu.config.XiaoHongShuConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.HashMap;
import java.util.Map;

/**
 * 小红书社区API
 * <p>
 * 提供小红书社区互动相关的API功能，包括关注管理、评论互动、数据统计等。
 * 支持个人开发者、企业开发者和品牌方的社区运营功能。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "handthing.connector.xiaohongshu", name = "enabled", havingValue = "true")
public class XiaoHongShuCommunityApi {

    private final XiaoHongShuConfig config;
    private final RestTemplate restTemplate;

    /**
     * 获取粉丝列表
     *
     * @param accessToken 访问令牌
     * @param page        页码
     * @param pageSize    每页大小
     * @return 粉丝列表
     */
    public Map<String, Object> getFollowersList(String accessToken, Integer page, Integer pageSize) {
        try {
            log.debug("Getting XiaoHongShu followers list, page: {}", page);

            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(config.getEffectiveApiBaseUrl() + "/api/user/followers")
                    .queryParam("access_token", accessToken)
                    .queryParam("page", page != null ? page : 1)
                    .queryParam("page_size", pageSize != null ? pageSize : 20);

            String url = builder.build().toUriString();

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Void> request = new HttpEntity<>(headers);

            Map<String, Object> response = restTemplate.exchange(url, HttpMethod.GET, request, Map.class).getBody();
            log.debug("XiaoHongShu followers list retrieved, response: {}", response);

            return response;

        } catch (Exception e) {
            log.error("Failed to get XiaoHongShu followers list", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", -1);
            errorResponse.put("message", "Failed to get followers list: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 获取关注列表
     *
     * @param accessToken 访问令牌
     * @param page        页码
     * @param pageSize    每页大小
     * @return 关注列表
     */
    public Map<String, Object> getFollowingList(String accessToken, Integer page, Integer pageSize) {
        try {
            log.debug("Getting XiaoHongShu following list, page: {}", page);

            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(config.getEffectiveApiBaseUrl() + "/api/user/following")
                    .queryParam("access_token", accessToken)
                    .queryParam("page", page != null ? page : 1)
                    .queryParam("page_size", pageSize != null ? pageSize : 20);

            String url = builder.build().toUriString();

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Void> request = new HttpEntity<>(headers);

            Map<String, Object> response = restTemplate.exchange(url, HttpMethod.GET, request, Map.class).getBody();
            log.debug("XiaoHongShu following list retrieved, response: {}", response);

            return response;

        } catch (Exception e) {
            log.error("Failed to get XiaoHongShu following list", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", -1);
            errorResponse.put("message", "Failed to get following list: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 获取笔记评论
     *
     * @param accessToken 访问令牌
     * @param noteId      笔记ID
     * @param page        页码
     * @param pageSize    每页大小
     * @return 评论列表
     */
    public Map<String, Object> getNoteComments(String accessToken, String noteId, Integer page, Integer pageSize) {
        try {
            log.debug("Getting XiaoHongShu note comments for note: {}", noteId);

            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(config.getEffectiveApiBaseUrl() + "/api/note/comments")
                    .queryParam("access_token", accessToken)
                    .queryParam("note_id", noteId)
                    .queryParam("page", page != null ? page : 1)
                    .queryParam("page_size", pageSize != null ? pageSize : 20);

            String url = builder.build().toUriString();

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Void> request = new HttpEntity<>(headers);

            Map<String, Object> response = restTemplate.exchange(url, HttpMethod.GET, request, Map.class).getBody();
            log.debug("XiaoHongShu note comments retrieved, response: {}", response);

            return response;

        } catch (Exception e) {
            log.error("Failed to get XiaoHongShu note comments", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", -1);
            errorResponse.put("message", "Failed to get note comments: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 回复评论
     *
     * @param accessToken 访问令牌
     * @param commentId   评论ID
     * @param content     回复内容
     * @return 回复结果
     */
    public Map<String, Object> replyComment(String accessToken, String commentId, String content) {
        try {
            log.debug("Replying to XiaoHongShu comment: {}", commentId);

            String url = config.getEffectiveApiBaseUrl() + "/api/comment/reply";

            Map<String, Object> replyData = new HashMap<>();
            replyData.put("access_token", accessToken);
            replyData.put("comment_id", commentId);
            replyData.put("content", content);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(replyData, headers);

            Map<String, Object> response = restTemplate.postForObject(url, request, Map.class);
            log.debug("XiaoHongShu comment replied, response: {}", response);

            return response;

        } catch (Exception e) {
            log.error("Failed to reply XiaoHongShu comment", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", -1);
            errorResponse.put("message", "Failed to reply comment: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 获取笔记数据统计
     *
     * @param accessToken 访问令牌
     * @param noteId      笔记ID
     * @return 数据统计
     */
    public Map<String, Object> getNoteStats(String accessToken, String noteId) {
        try {
            log.debug("Getting XiaoHongShu note stats for note: {}", noteId);

            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(config.getEffectiveApiBaseUrl() + "/api/note/stats")
                    .queryParam("access_token", accessToken)
                    .queryParam("note_id", noteId);

            String url = builder.build().toUriString();

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Void> request = new HttpEntity<>(headers);

            Map<String, Object> response = restTemplate.exchange(url, HttpMethod.GET, request, Map.class).getBody();
            log.debug("XiaoHongShu note stats retrieved, response: {}", response);

            return response;

        } catch (Exception e) {
            log.error("Failed to get XiaoHongShu note stats", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", -1);
            errorResponse.put("message", "Failed to get note stats: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 获取用户数据统计
     *
     * @param accessToken 访问令牌
     * @return 用户数据统计
     */
    public Map<String, Object> getUserStats(String accessToken) {
        try {
            log.debug("Getting XiaoHongShu user stats");

            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(config.getEffectiveApiBaseUrl() + "/api/user/stats")
                    .queryParam("access_token", accessToken);

            String url = builder.build().toUriString();

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Void> request = new HttpEntity<>(headers);

            Map<String, Object> response = restTemplate.exchange(url, HttpMethod.GET, request, Map.class).getBody();
            log.debug("XiaoHongShu user stats retrieved, response: {}", response);

            return response;

        } catch (Exception e) {
            log.error("Failed to get XiaoHongShu user stats", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", -1);
            errorResponse.put("message", "Failed to get user stats: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 搜索笔记
     *
     * @param accessToken 访问令牌
     * @param keyword     搜索关键词
     * @param page        页码
     * @param pageSize    每页大小
     * @return 搜索结果
     */
    public Map<String, Object> searchNotes(String accessToken, String keyword, Integer page, Integer pageSize) {
        try {
            log.debug("Searching XiaoHongShu notes with keyword: {}", keyword);

            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(config.getEffectiveApiBaseUrl() + "/api/note/search")
                    .queryParam("access_token", accessToken)
                    .queryParam("keyword", keyword)
                    .queryParam("page", page != null ? page : 1)
                    .queryParam("page_size", pageSize != null ? pageSize : 20);

            String url = builder.build().toUriString();

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Void> request = new HttpEntity<>(headers);

            Map<String, Object> response = restTemplate.exchange(url, HttpMethod.GET, request, Map.class).getBody();
            log.debug("XiaoHongShu note search completed, response: {}", response);

            return response;

        } catch (Exception e) {
            log.error("Failed to search XiaoHongShu notes", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", -1);
            errorResponse.put("message", "Failed to search notes: " + e.getMessage());
            return errorResponse;
        }
    }
}
