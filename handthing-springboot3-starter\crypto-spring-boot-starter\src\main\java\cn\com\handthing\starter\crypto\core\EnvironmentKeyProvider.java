package cn.com.handthing.starter.crypto.core;

import cn.com.handthing.starter.crypto.exception.CryptoException;
import cn.com.handthing.starter.crypto.properties.CryptoProperties.KeyStoreConfig;
import org.springframework.util.Assert;
import java.util.Base64;

/**
 * 从环境变量加载密钥的 KeyProvider。
 *
 * <AUTHOR>
 * @since V1.0.0
 */
class EnvironmentKeyProvider extends AbstractKeyProvider {

    private final byte[] keyBytes;

    public EnvironmentKeyProvider(KeyStoreConfig config) {
        super(config);
        Assert.hasText(config.getEnvVariable(), "env-variable must not be empty for KeyStoreType.ENV");
        String keyValue = System.getenv(config.getEnvVariable());
        if (keyValue == null) {
            throw new CryptoException("Environment variable '" + config.getEnvVariable() + "' is not set.");
        }
        try {
            this.keyBytes = Base64.getDecoder().decode(keyValue);
            this.ready = true;
        } catch (IllegalArgumentException e) {
            throw new CryptoException("Invalid Base64 encoded key in environment variable '" + config.getEnvVariable() + "'", e);
        }
    }

    @Override
    public byte[] getKeyBytes() {
        return this.keyBytes;
    }
}