package cn.com.handthing.starter.auth;

import cn.com.handthing.starter.auth.core.JwtConfig;
import cn.com.handthing.starter.auth.core.JwtTokenProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * JWT自动配置
 * <p>
 * 负责JWT相关组件的自动配置，包括JWT配置、令牌提供者、令牌验证器等。
 * 提供JWT令牌的生成、解析、验证等核心功能。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Configuration
@ConditionalOnProperty(prefix = "handthing.auth.jwt", name = "enabled", havingValue = "true", matchIfMissing = true)
@EnableConfigurationProperties(JwtConfig.class)
public class JwtAutoConfiguration {

    /**
     * JWT配置
     *
     * @return JWT配置
     */
    @Bean
    @ConditionalOnMissingBean
    public JwtConfig jwtConfig() {
        JwtConfig config = new JwtConfig();
        log.info("Creating JwtConfig with issuer: {}, access token expiration: {}s, refresh token expiration: {}s",
                config.getIssuer(), config.getAccessTokenExpirationSeconds(), config.getRefreshTokenExpirationSeconds());
        
        // 验证配置
        if (!config.isValid()) {
            log.warn("JWT configuration is invalid, please check your settings");
        }
        
        return config;
    }

    /**
     * JWT令牌提供者
     *
     * @param jwtConfig JWT配置
     * @return JWT令牌提供者
     */
    @Bean
    @ConditionalOnMissingBean
    public JwtTokenProvider jwtTokenProvider(JwtConfig jwtConfig) {
        log.info("Creating JwtTokenProvider");
        return new JwtTokenProvider(jwtConfig);
    }

    /**
     * JWT令牌验证器
     *
     * @param jwtTokenProvider JWT令牌提供者
     * @return JWT令牌验证器
     */
    @Bean
    @ConditionalOnMissingBean
    public JwtTokenValidator jwtTokenValidator(JwtTokenProvider jwtTokenProvider) {
        log.info("Creating JwtTokenValidator");
        return new JwtTokenValidator(jwtTokenProvider);
    }

    /**
     * JWT令牌验证器
     */
    public static class JwtTokenValidator {
        
        private final JwtTokenProvider jwtTokenProvider;

        public JwtTokenValidator(JwtTokenProvider jwtTokenProvider) {
            this.jwtTokenProvider = jwtTokenProvider;
        }

        /**
         * 验证令牌
         *
         * @param token 访问令牌
         * @return 如果令牌有效返回true，否则返回false
         */
        public boolean validateToken(String token) {
            return jwtTokenProvider.validateToken(token);
        }

        /**
         * 解析令牌
         *
         * @param token 访问令牌
         * @return JWT声明
         */
        public cn.com.handthing.starter.auth.core.JwtClaims parseToken(String token) {
            return jwtTokenProvider.parseToken(token);
        }

        /**
         * 获取令牌剩余有效时间
         *
         * @param token 访问令牌
         * @return 剩余有效时间（秒）
         */
        public long getTokenRemainingTime(String token) {
            return jwtTokenProvider.getTokenRemainingTime(token);
        }
    }
}
