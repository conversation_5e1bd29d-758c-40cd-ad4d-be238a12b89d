<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${title}">HandThing Test Application</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .header p {
            margin: 10px 0 0 0;
            font-size: 1.2em;
            opacity: 0.9;
        }
        .content {
            padding: 30px;
        }
        .section {
            margin-bottom: 30px;
        }
        .section h2 {
            color: #4facfe;
            border-bottom: 2px solid #4facfe;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .api-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .api-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .api-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .api-card h3 {
            margin: 0 0 15px 0;
            color: #495057;
        }
        .api-link {
            display: inline-block;
            background: #4facfe;
            color: white;
            padding: 8px 16px;
            text-decoration: none;
            border-radius: 5px;
            margin: 5px 5px 5px 0;
            font-size: 0.9em;
            transition: background 0.2s;
        }
        .api-link:hover {
            background: #3d8bfe;
            color: white;
            text-decoration: none;
        }
        .platform-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .platform-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            transition: transform 0.2s;
        }
        .platform-card:hover {
            transform: scale(1.05);
        }
        .platform-card h4 {
            margin: 0 0 10px 0;
            font-size: 1.2em;
        }
        .platform-card p {
            margin: 0;
            opacity: 0.9;
            font-size: 0.9em;
        }
        .footer {
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            color: #6c757d;
            border-top: 1px solid #e9ecef;
        }
        .timestamp {
            font-size: 0.9em;
            color: #6c757d;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 th:text="${title}">HandThing Test Application</h1>
            <p th:text="${description}">App Connector 综合测试应用</p>
        </div>
        
        <div class="content">
            <div class="section">
                <h2>🚀 支持的平台</h2>
                <div class="platform-grid">
                    <div class="platform-card">
                        <h4>企业微信</h4>
                        <p>WeCom Connector</p>
                    </div>
                    <div class="platform-card">
                        <h4>钉钉</h4>
                        <p>DingTalk Connector</p>
                    </div>
                    <div class="platform-card">
                        <h4>抖音</h4>
                        <p>Douyin Connector</p>
                    </div>
                    <div class="platform-card">
                        <h4>支付宝</h4>
                        <p>Alipay Connector</p>
                    </div>
                    <div class="platform-card">
                        <h4>微信</h4>
                        <p>WeChat Connector</p>
                    </div>
                </div>
            </div>
            
            <div class="section">
                <h2>🧪 测试接口</h2>
                <div class="api-grid">
                    <div class="api-card">
                        <h3>App Connector 测试</h3>
                        <a href="/test/app-connector/platforms" class="api-link">平台列表</a>
                        <a href="/test/app-connector/token/status" class="api-link">Token状态</a>
                        <a href="/test/app-connector/config/test" class="api-link">配置测试</a>
                    </div>
                    
                    <div class="api-card">
                        <h3>平台服务测试</h3>
                        <a href="/test/platform-services/status" class="api-link">服务状态</a>
                        <a href="/test/platform-services/wecom/test" class="api-link">企业微信</a>
                        <a href="/test/platform-services/dingtalk/test" class="api-link">钉钉</a>
                        <a href="/test/platform-services/douyin/test" class="api-link">抖音</a>
                        <a href="/test/platform-services/alipay/test" class="api-link">支付宝</a>
                        <a href="/test/platform-services/wechat/test" class="api-link">微信</a>
                    </div>
                    
                    <div class="api-card">
                        <h3>基础信息</h3>
                        <a href="/health" class="api-link">健康检查</a>
                        <a href="/info" class="api-link">应用信息</a>
                        <a href="/api/list" class="api-link">API列表</a>
                    </div>
                    
                    <div class="api-card">
                        <h3>监控端点</h3>
                        <a href="/actuator/health" class="api-link">Actuator健康</a>
                        <a href="/actuator/info" class="api-link">Actuator信息</a>
                        <a href="/actuator/metrics" class="api-link">指标监控</a>
                    </div>
                </div>
            </div>
            
            <div class="section">
                <h2>📖 使用说明</h2>
                <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #4facfe;">
                    <h4>快速开始：</h4>
                    <ol>
                        <li>点击上方的测试接口链接查看各个功能模块的状态</li>
                        <li>使用 <code>/test/app-connector/platforms</code> 查看支持的平台列表</li>
                        <li>使用 <code>/test/platform-services/status</code> 查看所有平台服务状态</li>
                        <li>通过 <code>/test/app-connector/auth/{platform}</code> 生成平台授权URL进行OAuth测试</li>
                    </ol>
                    
                    <h4>注意事项：</h4>
                    <ul>
                        <li>当前使用的是测试配置，真实环境需要配置实际的平台参数</li>
                        <li>OAuth回调测试需要真实的授权码才能完成完整流程</li>
                        <li>所有接口返回JSON格式数据，便于API测试和集成</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>© 2024 HandThing. All rights reserved.</p>
            <p class="timestamp" th:text="'页面生成时间: ' + ${timestamp}">页面生成时间: 2024-01-01 00:00:00</p>
        </div>
    </div>
</body>
</html>
