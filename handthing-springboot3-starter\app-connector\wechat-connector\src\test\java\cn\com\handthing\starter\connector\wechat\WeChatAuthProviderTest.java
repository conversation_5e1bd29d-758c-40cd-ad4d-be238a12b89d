package cn.com.handthing.starter.connector.wechat;

import cn.com.handthing.starter.connector.PlatformType;
import cn.com.handthing.starter.connector.auth.UnifiedAccessToken;
import cn.com.handthing.starter.connector.auth.UnifiedUserInfo;
import cn.com.handthing.starter.connector.exception.AuthException;
import cn.com.handthing.starter.connector.wechat.config.WeChatConfig;
import cn.com.handthing.starter.connector.wechat.model.WeChatAccessTokenResponse;
import cn.com.handthing.starter.connector.wechat.model.WeChatUserInfo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 微信认证提供者测试
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@ExtendWith(MockitoExtension.class)
class WeChatAuthProviderTest {

    @Mock
    private WeChatConfig config;

    @Mock
    private RestTemplate restTemplate;

    private WeChatAuthProvider authProvider;

    @BeforeEach
    void setUp() {
        authProvider = new WeChatAuthProvider(config, restTemplate);
    }

    @Test
    void testGetPlatformType() {
        assertEquals(PlatformType.WECHAT, authProvider.getPlatformType());
    }

    @Test
    void testGetAuthorizationUrl() {
        // 准备测试数据
        when(config.getAppId()).thenReturn("test-app-id");
        when(config.getRedirectUri()).thenReturn("http://localhost:8080/callback");
        when(config.isMiniProgramMode()).thenReturn(false);

        // 执行测试
        String authUrl = authProvider.getAuthorizationUrl("test-state", null);

        // 验证结果
        assertNotNull(authUrl);
        assertTrue(authUrl.contains("test-app-id"));
        assertTrue(authUrl.contains("test-state"));
        assertTrue(authUrl.contains("snsapi_userinfo"));
    }

    @Test
    void testGetAuthorizationUrlMiniProgram() {
        // 准备测试数据
        when(config.getAppId()).thenReturn("test-app-id");
        when(config.getRedirectUri()).thenReturn("http://localhost:8080/callback");
        when(config.isMiniProgramMode()).thenReturn(true);

        // 执行测试
        String authUrl = authProvider.getAuthorizationUrl("test-state", null);

        // 验证结果
        assertNotNull(authUrl);
        assertTrue(authUrl.contains("snsapi_base"));
    }

    @Test
    void testGetAuthorizationUrlWithoutRedirectUri() {
        // 准备测试数据
        when(config.getAppId()).thenReturn("test-app-id");
        when(config.getRedirectUri()).thenReturn(null);

        // 执行测试并验证异常
        assertThrows(AuthException.class, () -> {
            authProvider.getAuthorizationUrl("test-state", null);
        });
    }

    @Test
    void testExchangeCodeForToken() {
        // 准备测试数据
        when(config.getAppId()).thenReturn("test-app-id");
        when(config.getAppSecret()).thenReturn("test-app-secret");
        when(config.getEffectiveApiBaseUrl()).thenReturn("https://api.weixin.qq.com");

        WeChatAccessTokenResponse tokenResponse = new WeChatAccessTokenResponse();
        tokenResponse.setAccessToken("test-access-token");
        tokenResponse.setRefreshToken("test-refresh-token");
        tokenResponse.setExpiresIn(7200);
        tokenResponse.setScope("snsapi_userinfo");
        tokenResponse.setOpenid("test-openid");
        tokenResponse.setUnionid("test-unionid");

        when(restTemplate.getForEntity(anyString(), eq(WeChatAccessTokenResponse.class)))
                .thenReturn(ResponseEntity.ok(tokenResponse));

        // 执行测试
        UnifiedAccessToken result = authProvider.exchangeToken("test-code", "test-state", null);

        // 验证结果
        assertNotNull(result);
        assertEquals("test-access-token", result.getAccessToken());
        assertEquals("test-refresh-token", result.getRefreshToken());
        assertEquals(7200, result.getExpiresIn());
        assertEquals("test-openid", result.getUserId());
        assertEquals("test-openid", result.getExtraInfo().get("openId"));
        assertEquals("test-unionid", result.getExtraInfo().get("unionId"));
        assertEquals(PlatformType.WECHAT, result.getPlatform());
    }

    @Test
    void testExchangeCodeForTokenWithError() {
        // 准备测试数据
        when(config.getAppId()).thenReturn("test-app-id");
        when(config.getAppSecret()).thenReturn("test-app-secret");
        when(config.getEffectiveApiBaseUrl()).thenReturn("https://api.weixin.qq.com");

        WeChatAccessTokenResponse tokenResponse = new WeChatAccessTokenResponse();
        tokenResponse.setErrcode(40013);
        tokenResponse.setErrmsg("invalid appid");

        when(restTemplate.getForEntity(anyString(), eq(WeChatAccessTokenResponse.class)))
                .thenReturn(ResponseEntity.ok(tokenResponse));

        // 执行测试并验证异常
        assertThrows(AuthException.class, () -> {
            authProvider.exchangeToken("test-code", "test-state", null);
        });
    }

    @Test
    void testRefreshToken() {
        // 准备测试数据
        when(config.getAppId()).thenReturn("test-app-id");
        when(config.getEffectiveApiBaseUrl()).thenReturn("https://api.weixin.qq.com");

        WeChatAccessTokenResponse tokenResponse = new WeChatAccessTokenResponse();
        tokenResponse.setAccessToken("new-access-token");
        tokenResponse.setRefreshToken("new-refresh-token");
        tokenResponse.setExpiresIn(7200);
        tokenResponse.setScope("snsapi_userinfo");
        tokenResponse.setOpenid("test-openid");

        when(restTemplate.getForEntity(anyString(), eq(WeChatAccessTokenResponse.class)))
                .thenReturn(ResponseEntity.ok(tokenResponse));

        // 执行测试
        UnifiedAccessToken result = authProvider.refreshToken("test-refresh-token");

        // 验证结果
        assertNotNull(result);
        assertEquals("new-access-token", result.getAccessToken());
        assertEquals("new-refresh-token", result.getRefreshToken());
    }

    @Test
    void testGetUserInfo() {
        // 准备测试数据
        when(config.getEffectiveApiBaseUrl()).thenReturn("https://api.weixin.qq.com");
        when(config.isMiniProgramMode()).thenReturn(false);

        WeChatUserInfo userInfo = new WeChatUserInfo();
        userInfo.setOpenid("test-openid");
        userInfo.setUnionid("test-unionid");
        userInfo.setNickname("测试用户");
        userInfo.setHeadimgurl("http://example.com/avatar.jpg");
        userInfo.setSex(1);
        userInfo.setCountry("中国");
        userInfo.setProvince("广东");
        userInfo.setCity("深圳");

        when(restTemplate.getForEntity(anyString(), eq(WeChatUserInfo.class)))
                .thenReturn(ResponseEntity.ok(userInfo));

        // 执行测试
        UnifiedUserInfo result = authProvider.getUserInfo("test-access-token", "test-openid");

        // 验证结果
        assertNotNull(result);
        assertEquals("test-openid", result.getOpenId());
        assertEquals("test-unionid", result.getUnionId());
        assertEquals("测试用户", result.getNickname());
        assertEquals("http://example.com/avatar.jpg", result.getAvatar());
        assertEquals("male", result.getGender());
        assertEquals("中国", result.getCountry());
        assertEquals("广东", result.getProvince());
        assertEquals("深圳", result.getCity());
        assertEquals(PlatformType.WECHAT, result.getPlatform());
    }

    @Test
    void testGetUserInfoMiniProgram() {
        // 准备测试数据
        when(config.isMiniProgramMode()).thenReturn(true);

        // 执行测试
        UnifiedUserInfo result = authProvider.getUserInfo("test-access-token", "test-openid");

        // 验证结果
        assertNotNull(result);
        assertEquals("test-openid", result.getOpenId());
        assertEquals(PlatformType.WECHAT, result.getPlatform());
        
        // 小程序模式下不获取详细用户信息
        verify(restTemplate, never()).getForEntity(anyString(), eq(WeChatUserInfo.class));
    }

    @Test
    void testValidateToken() {
        // 准备测试数据
        when(config.getEffectiveApiBaseUrl()).thenReturn("https://api.weixin.qq.com");

        Map<String, Object> response = new HashMap<>();
        response.put("errcode", 0);
        response.put("errmsg", "ok");

        when(restTemplate.getForEntity(anyString(), eq(Map.class)))
                .thenReturn(ResponseEntity.ok(response));

        // 执行测试
        boolean result = authProvider.validateToken("test-access-token", "test-openid");

        // 验证结果
        assertTrue(result);
    }

    @Test
    void testValidateTokenInvalid() {
        // 准备测试数据
        when(config.getEffectiveApiBaseUrl()).thenReturn("https://api.weixin.qq.com");

        Map<String, Object> response = new HashMap<>();
        response.put("errcode", 40001);
        response.put("errmsg", "invalid credential");

        when(restTemplate.getForEntity(anyString(), eq(Map.class)))
                .thenReturn(ResponseEntity.ok(response));

        // 执行测试
        boolean result = authProvider.validateToken("test-access-token", "test-openid");

        // 验证结果
        assertFalse(result);
    }
}
