package cn.com.handthing.starter.connector.bilibili.config;

import cn.com.handthing.starter.connector.auth.AuthService;
import cn.com.handthing.starter.connector.auth.DefaultAuthService;
import cn.com.handthing.starter.connector.token.TokenManager;
import cn.com.handthing.starter.connector.bilibili.BilibiliAuthProvider;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;

import jakarta.annotation.PostConstruct;

/**
 * Bilibili连接器自动配置
 * <p>
 * 自动配置Bilibili连接器相关的Bean，包括配置类、认证提供者、服务类等。
 * 当启用Bilibili连接器时，自动注册到认证服务和Token管理器中。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@RequiredArgsConstructor
@AutoConfiguration
@EnableConfigurationProperties(BilibiliConfig.class)
@ConditionalOnProperty(prefix = "handthing.connector.bilibili", name = "enabled", havingValue = "true")
@ConditionalOnClass(AuthService.class)
@ComponentScan(basePackages = "cn.com.handthing.starter.connector.bilibili")
public class BilibiliAutoConfiguration {

    private final AuthService authService;
    private final BilibiliAuthProvider bilibiliAuthProvider;
    private final TokenManager tokenManager;
    private final BilibiliConfig bilibiliConfig;

    /**
     * 注册Bilibili认证提供者到认证服务
     */
    @PostConstruct
    public void registerBilibiliAuthProvider() {
        if (authService instanceof DefaultAuthService) {
            DefaultAuthService defaultAuthService = (DefaultAuthService) authService;
            defaultAuthService.registerAuthProvider(bilibiliAuthProvider);
            tokenManager.registerAuthProvider(bilibiliAuthProvider);
            
            log.info("Registered Bilibili auth provider - AppKey: {}, AppType: {}, MCN: {}, Valid: {}", 
                    bilibiliConfig.getAppKey(), 
                    bilibiliConfig.getAppType(),
                    bilibiliConfig.isMcnInstitution(),
                    bilibiliConfig.isValid());
        } else {
            log.warn("AuthService is not DefaultAuthService, cannot register Bilibili auth provider");
        }
    }

    /**
     * 配置信息日志输出
     */
    @PostConstruct
    public void logConfiguration() {
        log.info("Bilibili configuration: enabled=true, valid={}, appKey={}, appType={}, mcnMode={}, testMode={}, videoUploadEnabled={}, liveEnabled={}",
                bilibiliConfig.isValid(),
                bilibiliConfig.getAppKey(),
                bilibiliConfig.getAppType(),
                bilibiliConfig.isMcnInstitution(),
                bilibiliConfig.isTestMode(),
                bilibiliConfig.isVideoUploadAvailable(),
                bilibiliConfig.isLiveAvailable());
    }
}
