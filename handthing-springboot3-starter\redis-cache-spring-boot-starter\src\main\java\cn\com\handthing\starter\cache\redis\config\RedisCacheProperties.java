package cn.com.handthing.starter.cache.redis.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * Redis缓存配置属性
 * <p>
 * 基于Redis分布式缓存的配置
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
@ConfigurationProperties(prefix = "handthing.cache.redis")
public class RedisCacheProperties {

    /**
     * 是否启用Redis缓存
     */
    private boolean enabled = true;

    /**
     * 缓存键前缀
     */
    private String keyPrefix = "handthing:cache:";

    /**
     * 默认过期时间
     */
    private Duration defaultTtl = Duration.ofMinutes(30);

    /**
     * 是否允许缓存null值
     */
    private boolean allowNullValues = true;

    /**
     * 是否使用键前缀
     */
    private boolean useKeyPrefix = true;

    /**
     * 序列化配置
     */
    private Serialization serialization = new Serialization();

    /**
     * 各个缓存区域的独立配置
     */
    private Map<String, RedisCacheSpec> caches = new HashMap<>();

    /**
     * 序列化配置
     */
    @Data
    public static class Serialization {

        /**
         * 键序列化类型
         */
        private SerializationType keyType = SerializationType.STRING;

        /**
         * 值序列化类型
         */
        private SerializationType valueType = SerializationType.JSON;

        /**
         * 哈希键序列化类型
         */
        private SerializationType hashKeyType = SerializationType.STRING;

        /**
         * 哈希值序列化类型
         */
        private SerializationType hashValueType = SerializationType.JSON;

        /**
         * 序列化类型枚举
         */
        public enum SerializationType {
            /**
             * JDK序列化
             */
            JDK,
            /**
             * 字符串序列化
             */
            STRING,
            /**
             * JSON序列化
             */
            JSON
        }
    }

    /**
     * Redis缓存区域配置
     */
    @Data
    public static class RedisCacheSpec {

        /**
         * 过期时间
         */
        private Duration ttl;

        /**
         * 键前缀
         */
        private String keyPrefix;

        /**
         * 是否允许null值
         */
        private Boolean allowNullValues;

        /**
         * 是否使用键前缀
         */
        private Boolean useKeyPrefix;

        /**
         * 序列化配置
         */
        private Serialization serialization;

        /**
         * 获取有效的TTL
         *
         * @param defaultTtl 默认TTL
         * @return 有效的TTL
         */
        public Duration getEffectiveTtl(Duration defaultTtl) {
            return ttl != null ? ttl : defaultTtl;
        }

        /**
         * 获取有效的键前缀
         *
         * @param defaultKeyPrefix 默认键前缀
         * @return 有效的键前缀
         */
        public String getEffectiveKeyPrefix(String defaultKeyPrefix) {
            return keyPrefix != null ? keyPrefix : defaultKeyPrefix;
        }

        /**
         * 获取有效的allowNullValues
         *
         * @param defaultAllowNullValues 默认allowNullValues
         * @return 有效的allowNullValues
         */
        public boolean getEffectiveAllowNullValues(boolean defaultAllowNullValues) {
            return allowNullValues != null ? allowNullValues : defaultAllowNullValues;
        }

        /**
         * 获取有效的useKeyPrefix
         *
         * @param defaultUseKeyPrefix 默认useKeyPrefix
         * @return 有效的useKeyPrefix
         */
        public boolean getEffectiveUseKeyPrefix(boolean defaultUseKeyPrefix) {
            return useKeyPrefix != null ? useKeyPrefix : defaultUseKeyPrefix;
        }

        /**
         * 获取有效的序列化配置
         *
         * @param defaultSerialization 默认序列化配置
         * @return 有效的序列化配置
         */
        public Serialization getEffectiveSerialization(Serialization defaultSerialization) {
            return serialization != null ? serialization : defaultSerialization;
        }
    }
}