package cn.com.handthing.starter.auth.core;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户信息模型
 * <p>
 * 定义用户的基本信息结构，包含用户ID、用户名、角色、权限等信息。
 * 支持扩展属性，可以根据业务需要添加自定义字段。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserInfo {

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 头像URL
     */
    private String avatar;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 性别（1-男，2-女，0-未知）
     */
    private Integer gender;

    /**
     * 生日
     */
    private LocalDateTime birthday;

    /**
     * 部门ID
     */
    private String departmentId;

    /**
     * 部门名称
     */
    private String departmentName;

    /**
     * 职位
     */
    private String position;

    /**
     * 角色列表
     */
    private List<String> roles;

    /**
     * 权限列表
     */
    private List<String> permissions;

    /**
     * 用户状态（1-正常，2-锁定，3-禁用）
     */
    private Integer status;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 账户是否未过期
     */
    private Boolean accountNonExpired;

    /**
     * 账户是否未锁定
     */
    private Boolean accountNonLocked;

    /**
     * 凭证是否未过期
     */
    private Boolean credentialsNonExpired;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 最后登录时间
     */
    private LocalDateTime lastLoginTime;

    /**
     * 最后登录IP
     */
    private String lastLoginIp;

    /**
     * 扩展属性
     */
    private Map<String, Object> attributes;

    /**
     * 添加扩展属性
     *
     * @param key   属性键
     * @param value 属性值
     */
    public void addAttribute(String key, Object value) {
        if (attributes == null) {
            attributes = new HashMap<>();
        }
        attributes.put(key, value);
    }

    /**
     * 获取扩展属性
     *
     * @param key 属性键
     * @return 属性值
     */
    public Object getAttribute(String key) {
        return attributes != null ? attributes.get(key) : null;
    }

    /**
     * 获取扩展属性（指定类型）
     *
     * @param key   属性键
     * @param clazz 属性类型
     * @param <T>   泛型类型
     * @return 属性值
     */
    @SuppressWarnings("unchecked")
    public <T> T getAttribute(String key, Class<T> clazz) {
        Object value = getAttribute(key);
        if (value != null && clazz.isInstance(value)) {
            return (T) value;
        }
        return null;
    }

    /**
     * 判断用户是否有效
     *
     * @return 如果用户有效返回true，否则返回false
     */
    public boolean isValid() {
        return userId != null && username != null && 
               Boolean.TRUE.equals(enabled) && 
               Boolean.TRUE.equals(accountNonExpired) && 
               Boolean.TRUE.equals(accountNonLocked) && 
               Boolean.TRUE.equals(credentialsNonExpired);
    }

    /**
     * 判断用户是否有指定角色
     *
     * @param role 角色名称
     * @return 如果有指定角色返回true，否则返回false
     */
    public boolean hasRole(String role) {
        return roles != null && roles.contains(role);
    }

    /**
     * 判断用户是否有指定权限
     *
     * @param permission 权限名称
     * @return 如果有指定权限返回true，否则返回false
     */
    public boolean hasPermission(String permission) {
        return permissions != null && permissions.contains(permission);
    }

    /**
     * 获取性别描述
     *
     * @return 性别描述
     */
    public String getGenderDescription() {
        if (gender == null) {
            return "未知";
        }
        switch (gender) {
            case 1:
                return "男";
            case 2:
                return "女";
            default:
                return "未知";
        }
    }

    /**
     * 获取状态描述
     *
     * @return 状态描述
     */
    public String getStatusDescription() {
        if (status == null) {
            return "未知";
        }
        switch (status) {
            case 1:
                return "正常";
            case 2:
                return "锁定";
            case 3:
                return "禁用";
            default:
                return "未知";
        }
    }

    /**
     * 获取显示名称
     * <p>
     * 优先返回昵称，如果昵称为空则返回用户名
     * </p>
     *
     * @return 显示名称
     */
    public String getDisplayName() {
        if (nickname != null && !nickname.trim().isEmpty()) {
            return nickname;
        }
        return username;
    }

    @Override
    public String toString() {
        return String.format("UserInfo{userId='%s', username='%s', nickname='%s', email='%s', phone='%s', enabled=%s}",
                userId, username, nickname, email, phone, enabled);
    }
}
