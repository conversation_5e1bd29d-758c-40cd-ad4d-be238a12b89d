package cn.com.handthing.starter.auth;

import cn.com.handthing.starter.auth.core.JwtConfig;
import cn.com.handthing.starter.auth.core.JwtTokenProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;

/**
 * 认证自动配置
 * <p>
 * HandThing认证框架的核心自动配置类，负责初始化认证相关的Bean和配置。
 * 提供认证管理器、JWT支持、Web端点等核心功能的自动装配。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@AutoConfiguration
@ConditionalOnProperty(prefix = "handthing.auth", name = "enabled", havingValue = "true", matchIfMissing = true)
@EnableConfigurationProperties({AuthProperties.class, JwtConfig.class})
@ComponentScan(basePackages = "cn.com.handthing.starter.auth")
@Import({JwtAutoConfiguration.class, SecurityAutoConfiguration.class})
public class AuthAutoConfiguration {

    /**
     * 认证管理器
     *
     * @param authenticationProviders 认证提供者列表
     * @param authProperties          认证配置属性
     * @param authenticationBus       认证总线
     * @return 认证管理器
     */
    @Bean
    @ConditionalOnMissingBean
    public AuthenticationManager authenticationManager(
            java.util.List<cn.com.handthing.starter.auth.core.AuthenticationProvider> authenticationProviders,
            AuthProperties authProperties,
            AuthenticationBus authenticationBus) {
        
        log.info("Creating AuthenticationManager with {} providers", authenticationProviders.size());
        return new AuthenticationManager(authenticationProviders, authProperties, authenticationBus);
    }

    /**
     * 认证总线
     *
     * @param eventPublisher Spring事件发布器
     * @param authProperties 认证配置属性
     * @return 认证总线
     */
    @Bean
    @ConditionalOnMissingBean
    public AuthenticationBus authenticationBus(
            org.springframework.context.ApplicationEventPublisher eventPublisher,
            AuthProperties authProperties) {
        
        log.info("Creating AuthenticationBus with events enabled: {}", authProperties.isEventsEnabled());
        return new AuthenticationBus(eventPublisher, authProperties);
    }

    /**
     * 提供者注册表
     *
     * @param authenticationProviders 认证提供者列表
     * @return 提供者注册表
     */
    @Bean
    @ConditionalOnMissingBean
    public ProviderRegistry providerRegistry(
            java.util.List<cn.com.handthing.starter.auth.core.AuthenticationProvider> authenticationProviders) {
        
        log.info("Creating ProviderRegistry with {} providers", authenticationProviders.size());
        return new ProviderRegistry(authenticationProviders);
    }

    /**
     * 认证控制器
     *
     * @param authenticationManager 认证管理器
     * @param providerRegistry      提供者注册表
     * @param authProperties        认证配置属性
     * @param jwtTokenProvider      JWT令牌提供者
     * @return 认证控制器
     */
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(prefix = "handthing.auth.web", name = "endpoints-enabled", havingValue = "true", matchIfMissing = true)
    public AuthenticationController authenticationController(
            AuthenticationManager authenticationManager,
            ProviderRegistry providerRegistry,
            AuthProperties authProperties,
            JwtTokenProvider jwtTokenProvider) {
        
        log.info("Creating AuthenticationController with endpoints enabled");
        return new AuthenticationController(authenticationManager, providerRegistry, authProperties, jwtTokenProvider);
    }

    /**
     * 认证过滤器
     *
     * @param jwtTokenProvider JWT令牌提供者
     * @param authProperties   认证配置属性
     * @return 认证过滤器
     */
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(prefix = "handthing.auth.web", name = "filter-enabled", havingValue = "true", matchIfMissing = true)
    public AuthenticationFilter authenticationFilter(
            JwtTokenProvider jwtTokenProvider,
            AuthProperties authProperties) {
        
        log.info("Creating AuthenticationFilter with order: {}", authProperties.getWeb().getFilterOrder());
        return new AuthenticationFilter(jwtTokenProvider, authProperties);
    }

    /**
     * 认证拦截器
     *
     * @param authProperties 认证配置属性
     * @return 认证拦截器
     */
    @Bean
    @ConditionalOnMissingBean
    public AuthenticationInterceptor authenticationInterceptor(AuthProperties authProperties) {
        log.info("Creating AuthenticationInterceptor");
        return new AuthenticationInterceptor(authProperties);
    }

    /**
     * Web MVC配置
     *
     * @param authenticationInterceptor 认证拦截器
     * @param authProperties            认证配置属性
     * @return Web MVC配置
     */
    @Bean
    @ConditionalOnMissingBean
    public AuthWebMvcConfigurer authWebMvcConfigurer(
            AuthenticationInterceptor authenticationInterceptor,
            AuthProperties authProperties) {
        
        log.info("Creating AuthWebMvcConfigurer");
        return new AuthWebMvcConfigurer(authenticationInterceptor, authProperties);
    }

    /**
     * 认证事件监听器
     *
     * @param authProperties 认证配置属性
     * @return 认证事件监听器
     */
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(prefix = "handthing.auth", name = "events-enabled", havingValue = "true", matchIfMissing = true)
    public AuthenticationEventListener authenticationEventListener(AuthProperties authProperties) {
        log.info("Creating AuthenticationEventListener");
        return new AuthenticationEventListener(authProperties);
    }

    /**
     * 认证指标收集器
     *
     * @param authProperties 认证配置属性
     * @return 认证指标收集器
     */
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(prefix = "handthing.auth", name = "metrics-enabled", havingValue = "true")
    public AuthenticationMetrics authenticationMetrics(AuthProperties authProperties) {
        log.info("Creating AuthenticationMetrics");
        return new AuthenticationMetrics(authProperties);
    }

    /**
     * 认证健康检查
     *
     * @param providerRegistry 提供者注册表
     * @param authProperties   认证配置属性
     * @return 认证健康检查
     */
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnClass(name = "org.springframework.boot.actuator.health.HealthIndicator")
    public AuthenticationHealthIndicator authenticationHealthIndicator(
            ProviderRegistry providerRegistry,
            AuthProperties authProperties) {

        log.info("Creating AuthenticationHealthIndicator");
        return new AuthenticationHealthIndicator(providerRegistry, authProperties);
    }

    /**
     * 初始化完成后的日志输出
     */
    @Bean
    public AuthConfigurationLogger authConfigurationLogger(
            AuthProperties authProperties,
            ProviderRegistry providerRegistry) {
        
        return new AuthConfigurationLogger(authProperties, providerRegistry);
    }

    /**
     * 认证配置日志记录器
     */
    public static class AuthConfigurationLogger {
        
        public AuthConfigurationLogger(AuthProperties authProperties, ProviderRegistry providerRegistry) {
            log.info("=== HandThing Auth Configuration ===");
            log.info("Auth enabled: {}", authProperties.isEnabled());
            log.info("Default grant type: {}", authProperties.getDefaultGrantType());
            log.info("Multi-provider enabled: {}", authProperties.isMultiProviderEnabled());
            log.info("Cache enabled: {}", authProperties.isCacheEnabled());
            log.info("Events enabled: {}", authProperties.isEventsEnabled());
            log.info("Metrics enabled: {}", authProperties.isMetricsEnabled());
            log.info("Web endpoints enabled: {}", authProperties.getWeb().isEndpointsEnabled());
            log.info("Authentication filter enabled: {}", authProperties.getWeb().isFilterEnabled());
            log.info("Registered providers: {}", providerRegistry.getProviderNames());
            log.info("Supported grant types: {}", providerRegistry.getSupportedGrantTypes());
            log.info("=== HandThing Auth Configuration Complete ===");
        }
    }
}
