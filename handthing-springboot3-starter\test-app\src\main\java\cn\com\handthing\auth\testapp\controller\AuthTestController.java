package cn.com.handthing.auth.testapp.controller;

import cn.com.handthing.starter.auth.core.UserContext;
import cn.com.handthing.starter.auth.core.UserContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.HashMap;
import java.util.Map;

/**
 * 认证测试控制器
 * <p>
 * 提供认证功能的测试页面和API，包括登录页面、用户信息展示等。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Controller
public class AuthTestController {

    /**
     * 认证首页
     */
    @GetMapping("/auth-home")
    public String index(Model model) {
        UserContext userContext = UserContextHolder.getContext();
        if (userContext != null && userContext.isAuthenticated()) {
            model.addAttribute("authenticated", true);
            model.addAttribute("userInfo", userContext.getUserInfo());
        } else {
            model.addAttribute("authenticated", false);
        }
        return "index";
    }

    /**
     * 登录页面
     */
    @GetMapping("/login")
    public String loginPage(Model model, @RequestParam(required = false) String error) {
        if (error != null) {
            model.addAttribute("error", error);
        }
        return "login";
    }

    /**
     * 用户信息页面
     */
    @GetMapping("/profile")
    public String profile(Model model) {
        UserContext userContext = UserContextHolder.getContext();
        if (userContext == null || !userContext.isAuthenticated()) {
            return "redirect:/login";
        }
        
        model.addAttribute("userInfo", userContext.getUserInfo());
        model.addAttribute("userContext", userContext);
        return "profile";
    }

    /**
     * 获取当前用户信息（API）
     */
    @GetMapping("/api/current-user")
    @ResponseBody
    public Map<String, Object> getCurrentUser() {
        Map<String, Object> result = new HashMap<>();
        
        UserContext userContext = UserContextHolder.getContext();
        if (userContext != null && userContext.isAuthenticated()) {
            result.put("authenticated", true);
            result.put("userInfo", userContext.getUserInfo());
            result.put("grantType", userContext.getGrantType());
            result.put("authenticationTime", userContext.getAuthenticationTime());
            result.put("ipAddress", userContext.getIpAddress());
        } else {
            result.put("authenticated", false);
        }
        
        return result;
    }

    /**
     * 企业微信授权回调页面
     */
    @GetMapping("/wecom/callback")
    public String wecomCallback(@RequestParam(required = false) String code,
                               @RequestParam(required = false) String state,
                               @RequestParam(required = false) String error,
                               Model model) {
        if (error != null) {
            model.addAttribute("error", "企业微信授权失败: " + error);
            return "login";
        }

        if (code != null) {
            model.addAttribute("code", code);
            model.addAttribute("state", state);
            return "wecom-callback";
        }

        return "redirect:/login";
    }

    /**
     * 钉钉授权回调页面
     */
    @GetMapping("/dingtalk/callback")
    public String dingtalkCallback(@RequestParam(required = false) String code,
                                  @RequestParam(required = false) String state,
                                  @RequestParam(required = false) String error,
                                  Model model) {
        if (error != null) {
            model.addAttribute("error", "钉钉授权失败: " + error);
            return "login";
        }

        if (code != null) {
            model.addAttribute("code", code);
            model.addAttribute("state", state);
            return "dingtalk-callback";
        }

        return "redirect:/login";
    }

    /**
     * 微信授权回调页面
     */
    @GetMapping("/wechat/callback")
    public String wechatCallback(@RequestParam(required = false) String code,
                                @RequestParam(required = false) String state,
                                @RequestParam(required = false) String error,
                                Model model) {
        if (error != null) {
            model.addAttribute("error", "微信授权失败: " + error);
            return "login";
        }

        if (code != null) {
            model.addAttribute("code", code);
            model.addAttribute("state", state);
            return "wechat-callback";
        }

        return "redirect:/login";
    }

    /**
     * 飞书授权回调页面
     */
    @GetMapping("/feishu/callback")
    public String feishuCallback(@RequestParam(required = false) String code,
                                @RequestParam(required = false) String state,
                                @RequestParam(required = false) String error,
                                Model model) {
        if (error != null) {
            model.addAttribute("error", "飞书授权失败: " + error);
            return "login";
        }

        if (code != null) {
            model.addAttribute("code", code);
            model.addAttribute("state", state);
            return "feishu-callback";
        }

        return "redirect:/login";
    }

    /**
     * 测试受保护的资源
     */
    @GetMapping("/protected")
    @ResponseBody
    public Map<String, Object> protectedResource() {
        Map<String, Object> result = new HashMap<>();
        
        UserContext userContext = UserContextHolder.getContext();
        if (userContext != null && userContext.isAuthenticated()) {
            result.put("success", true);
            result.put("message", "访问受保护资源成功");
            result.put("userId", userContext.getUserInfo().getUserId());
            result.put("username", userContext.getUserInfo().getUsername());
            result.put("roles", userContext.getUserInfo().getRoles());
            result.put("permissions", userContext.getUserInfo().getPermissions());
        } else {
            result.put("success", false);
            result.put("message", "未认证，无法访问受保护资源");
        }
        
        return result;
    }

    /**
     * 测试管理员资源
     */
    @GetMapping("/admin")
    @ResponseBody
    public Map<String, Object> adminResource() {
        Map<String, Object> result = new HashMap<>();
        
        UserContext userContext = UserContextHolder.getContext();
        if (userContext == null || !userContext.isAuthenticated()) {
            result.put("success", false);
            result.put("message", "未认证，无法访问管理员资源");
            return result;
        }
        
        // 检查是否有管理员角色
        if (userContext.getUserInfo().getRoles().contains("ADMIN")) {
            result.put("success", true);
            result.put("message", "访问管理员资源成功");
            result.put("userId", userContext.getUserInfo().getUserId());
            result.put("username", userContext.getUserInfo().getUsername());
        } else {
            result.put("success", false);
            result.put("message", "权限不足，无法访问管理员资源");
        }
        
        return result;
    }

    /**
     * 登出处理
     */
    @PostMapping("/logout")
    public String logout() {
        // 清除用户上下文
        UserContextHolder.clearContext();
        return "redirect:/login?logout=true";
    }
}
