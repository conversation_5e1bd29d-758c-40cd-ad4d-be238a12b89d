HandThing DataLayer Spring Boot Starter 系列
详细设计说明书 (V2.0 模块化重构版)
| 文档版本 | V2.0 |
| 状态 | 定稿 (Final) |
| 作者 | HandThing, Gemini |
| 创建日期 | 2025-07-29 |

1. 概述 (Overview)
   1.1. 文档目的
   本文档旨在为datalayer-starter系列项目提供全面的技术设计蓝图。该系列项目在之前persistence-starter的基础上进行了彻底的模块化重构，旨在提供一个以核心数据层为基础，可按需装配租户隔离、数据权限等高级功能的高度解耦、可插拔的持久化解决方案。

1.2. 核心设计理念：核心 + 插件
datalayer-spring-boot-starter (核心): 提供基于MyBatis-Plus的基础CRUD、自动填充、ID生成、逻辑删除和乐观锁等通用能力。它是所有数据操作的基础。

*-datalayer-spring-boot-starter (插件): 作为独立的能力增强模块，可以像插件一样即插即用。例如，引入tenant-datalayer-starter即可为系统无缝增加多租户数据隔离能力。

配置驱动: 所有核心功能和插件功能均可通过application.yml进行精细化配置和开关。

无缝集成: 所有模块深度集成auth-starter提供的上下文信息 (AuthContextHolder, TenantContextHolder)，实现智能化操作。

2. 整体架构与模块依赖
   graph TD
   subgraph 主应用 (Your Application)
   A -- 可选依赖 --> B;
   A -- 可选依赖 --> C;
   A -- 必须依赖 --> D;
   end

   subgraph 可插拔能力层 (Pluggable Capabilities)
   B["tenant-datalayer-spring-boot-starter"];
   C["dataauth-datalayer-spring-boot-starter"];
   end

   subgraph 核心数据层 (Core DataLayer)
   D["datalayer-spring-boot-starter"];
   end

   subgraph 基础依赖 (Base Dependencies)
   E["id-spring-boot-starter"];
   F["mybatis-plus-boot-starter"];
   G["auth-starter (提供上下文)"];
   end

   B --> D;
   C --> D;
   D --> E;
   D --> F;
   D -- 弱依赖/概念依赖 --> G;
   B -- 弱依赖/概念依赖 --> G;
   C -- 弱依赖/概念依赖 --> G;



依赖关系说明:

id-starter 作为最底层，提供ID生成能力。

datalayer-starter 作为核心，依赖id-starter和mybatis-plus，提供了所有基础持久化能力。

tenant-datalayer-starter 和 dataauth-datalayer-starter 作为插件，都依赖于datalayer-starter，它们为核心功能添加了额外的拦截器和逻辑。

主应用 必须依赖datalayer-starter，然后根据业务需求，自由选择是否引入tenant和dataauth插件。

3. 模块详细设计
   3.1. datalayer-spring-boot-starter (核心模块)
   职责: 提供基础、通用的持久化能力。

核心组件:

BaseEntity: 不再包含tenantId。只保留id(无类型，由具体实体定义)、createBy, createTime, updateBy, updateTime, version, deleted。

BaseMapper<T> & BaseService<M, T>: 继承自MyBatis-Plus，提供基础CRUD。

DatalayerMetaObjectHandler:

集成id-starter，在insertFill时为@IdSetter注解的字段自动注入ID。

从AuthContextHolder获取用户信息，自动填充createBy, updateBy字段。

自动填充createTime, updateTime。

自动配置:

自动装配MybatisPlusInterceptor，并内置PaginationInnerInterceptor (分页) 和OptimisticLockerInnerInterceptor (乐观锁)。

自动装配DatalayerMetaObjectHandler。

配置 (application.yml):

handthing:
datalayer:
# 逻辑删除等基础配置
logic-delete:
enabled: true
deleted-value: 1
not-deleted-value: 0


3.2. tenant-datalayer-spring-boot-starter (租户隔离插件)
职责: 为核心数据层增加透明的租户数据隔离能力。

核心组件:

TenantBaseEntity: public class TenantBaseEntity extends BaseEntity，在此类中新增private Long tenantId;字段。需要多租户能力的实体应继承此类。

SaasTenantLineHandler: 实现MyBatis-Plus的TenantLineHandler接口，getTenantId()方法从TenantContextHolder获取租户ID。

自动配置 (TenantDatalayerAutoConfiguration):

使用@ConditionalOnProperty(name = "handthing.datalayer.tenant.enabled", havingValue = "true")进行开关控制。

向已有的MybatisPlusInterceptor Bean中添加TenantLineInnerInterceptor。

通过AOP或@Primary方式增强DatalayerMetaObjectHandler，使其在insertFill时也能自动填充tenantId。

配置 (application.yml):

handthing:
datalayer:
tenant:
enabled: true
ignore-tables:
- "sys_tenant"


3.3. dataauth-datalayer-spring-boot-starter (数据权限插件)
职责: 提供基于注解的方法级数据权限过滤。

核心组件:

@DataPermission注解: 与之前设计相同，用于标记需要权限过滤的Mapper方法。

DataPermissionRuleProvider接口: 与之前设计相同，由主应用提供具体的SQL规则生成逻辑。

DataPermissionInterceptor: MyBatis-Plus的InnerInterceptor实现，负责动态拼接SQL。

自动配置 (DataAuthDatalayerAutoConfiguration):

使用@ConditionalOnProperty(name = "handthing.datalayer.data-auth.enabled", havingValue = "true")进行开关控制。

向已有的MybatisPlusInterceptor Bean中添加DataPermissionInterceptor。

配置 (application.yml):

handthing:
datalayer:
data-auth:
enabled: true


4. 使用示例
   场景1: 简单应用 (无需多租户和数据权限)
   依赖: 只引入 datalayer-spring-boot-starter。

实体: public class Product extends BaseEntity { ... }

效果: 拥有ID自动生成、创建/更新人自动填充、逻辑删除、乐观锁、分页等所有基础能力。

场景2: SaaS应用 (需要多租户)
依赖: 引入 datalayer-spring-boot-starter 和 tenant-datalayer-spring-boot-starter。

实体: public class Order extends TenantBaseEntity { ... } (继承租户基类)

配置: 启用handthing.datalayer.tenant.enabled=true。

效果: 在场景1的基础上，所有对Order表的查询和操作都会自动带上WHERE tenant_id = ?条件。

场景3: 复杂后台管理系统 (需要数据权限)
依赖: 引入 datalayer-spring-boot-starter 和 dataauth-datalayer-spring-boot-starter (也可以同时引入tenant-starter)。

Mapper:

@Mapper
public interface OrderMapper extends BaseMapper<Order> {
@DataPermission // 标记此方法
Page<Order> findMyOrders(Page<Order> page);
}


配置: 启用handthing.datalayer.data-auth.enabled=true。

主应用实现: 提供一个DataPermissionRuleProvider的Bean。

效果: 调用findMyOrders方法时，会自动根据当前用户的角色和权限拼接SQL，实现数据范围的过滤。