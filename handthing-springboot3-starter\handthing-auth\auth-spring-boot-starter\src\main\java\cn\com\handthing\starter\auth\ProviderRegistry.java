package cn.com.handthing.starter.auth;

import cn.com.handthing.starter.auth.core.AuthenticationProvider;
import cn.com.handthing.starter.auth.core.GrantType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 提供者注册表
 * <p>
 * 管理和注册所有的认证提供者，提供提供者的查找、注册、注销等功能。
 * 支持动态注册和管理多个认证提供者。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Component
public class ProviderRegistry {

    /**
     * 提供者映射表（按授权类型）
     */
    private final Map<GrantType, AuthenticationProvider> providersByGrantType = new ConcurrentHashMap<>();

    /**
     * 提供者映射表（按名称）
     */
    private final Map<String, AuthenticationProvider> providersByName = new ConcurrentHashMap<>();

    /**
     * 所有提供者列表
     */
    private final List<AuthenticationProvider> allProviders = new ArrayList<>();

    /**
     * 注册的提供者列表（来自Spring容器）
     */
    private final List<AuthenticationProvider> springProviders;

    /**
     * 构造函数
     *
     * @param springProviders Spring容器中的认证提供者
     */
    public ProviderRegistry(List<AuthenticationProvider> springProviders) {
        this.springProviders = springProviders != null ? springProviders : new ArrayList<>();
    }

    /**
     * 初始化注册表
     */
    @PostConstruct
    public void initialize() {
        log.info("Initializing provider registry with {} providers", springProviders.size());

        // 注册所有Spring容器中的提供者
        for (AuthenticationProvider provider : springProviders) {
            registerProvider(provider);
        }

        log.info("Provider registry initialized with {} providers: {}", 
                allProviders.size(), getProviderNames());
    }

    /**
     * 注册认证提供者
     *
     * @param provider 认证提供者
     */
    public void registerProvider(AuthenticationProvider provider) {
        if (provider == null) {
            log.warn("Attempted to register null provider");
            return;
        }

        GrantType grantType = provider.getSupportedGrantType();
        String providerName = provider.getProviderName();

        // 检查是否已存在相同授权类型的提供者
        if (providersByGrantType.containsKey(grantType)) {
            AuthenticationProvider existingProvider = providersByGrantType.get(grantType);
            log.warn("Provider for grant type {} already exists: {}, replacing with: {}", 
                    grantType, existingProvider.getProviderName(), providerName);
            
            // 移除旧提供者
            unregisterProvider(existingProvider);
        }

        // 注册新提供者
        providersByGrantType.put(grantType, provider);
        providersByName.put(providerName, provider);
        allProviders.add(provider);

        log.info("Registered authentication provider: {} for grant type: {}", 
                providerName, grantType);
    }

    /**
     * 注销认证提供者
     *
     * @param provider 认证提供者
     */
    public void unregisterProvider(AuthenticationProvider provider) {
        if (provider == null) {
            return;
        }

        GrantType grantType = provider.getSupportedGrantType();
        String providerName = provider.getProviderName();

        providersByGrantType.remove(grantType);
        providersByName.remove(providerName);
        allProviders.remove(provider);

        log.info("Unregistered authentication provider: {} for grant type: {}", 
                providerName, grantType);
    }

    /**
     * 根据授权类型获取提供者
     *
     * @param grantType 授权类型
     * @return 认证提供者，如果未找到返回null
     */
    public AuthenticationProvider getProviderByGrantType(GrantType grantType) {
        return providersByGrantType.get(grantType);
    }

    /**
     * 根据名称获取提供者
     *
     * @param providerName 提供者名称
     * @return 认证提供者，如果未找到返回null
     */
    public AuthenticationProvider getProviderByName(String providerName) {
        return providersByName.get(providerName);
    }

    /**
     * 获取所有提供者
     *
     * @return 提供者列表
     */
    public List<AuthenticationProvider> getAllProviders() {
        return new ArrayList<>(allProviders);
    }

    /**
     * 获取可用的提供者
     *
     * @return 可用的提供者列表
     */
    public List<AuthenticationProvider> getAvailableProviders() {
        return allProviders.stream()
                .filter(AuthenticationProvider::isAvailable)
                .toList();
    }

    /**
     * 获取支持的授权类型
     *
     * @return 授权类型列表
     */
    public List<GrantType> getSupportedGrantTypes() {
        return allProviders.stream()
                .filter(AuthenticationProvider::isAvailable)
                .map(AuthenticationProvider::getSupportedGrantType)
                .distinct()
                .toList();
    }

    /**
     * 获取提供者名称列表
     *
     * @return 提供者名称列表
     */
    public List<String> getProviderNames() {
        return allProviders.stream()
                .map(AuthenticationProvider::getProviderName)
                .toList();
    }

    /**
     * 判断是否支持指定的授权类型
     *
     * @param grantType 授权类型
     * @return 如果支持返回true，否则返回false
     */
    public boolean supportsGrantType(GrantType grantType) {
        AuthenticationProvider provider = providersByGrantType.get(grantType);
        return provider != null && provider.isAvailable();
    }

    /**
     * 判断是否存在指定名称的提供者
     *
     * @param providerName 提供者名称
     * @return 如果存在返回true，否则返回false
     */
    public boolean hasProvider(String providerName) {
        return providersByName.containsKey(providerName);
    }

    /**
     * 获取提供者数量
     *
     * @return 提供者数量
     */
    public int getProviderCount() {
        return allProviders.size();
    }

    /**
     * 获取可用提供者数量
     *
     * @return 可用提供者数量
     */
    public int getAvailableProviderCount() {
        return (int) allProviders.stream()
                .filter(AuthenticationProvider::isAvailable)
                .count();
    }

    /**
     * 获取注册表状态信息
     *
     * @return 状态信息
     */
    public Map<String, Object> getRegistryStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("totalProviders", getProviderCount());
        status.put("availableProviders", getAvailableProviderCount());
        status.put("supportedGrantTypes", getSupportedGrantTypes().size());
        status.put("providerNames", getProviderNames());
        
        Map<String, Object> providerDetails = new HashMap<>();
        for (AuthenticationProvider provider : allProviders) {
            Map<String, Object> details = new HashMap<>();
            details.put("grantType", provider.getSupportedGrantType());
            details.put("available", provider.isAvailable());
            details.put("priority", provider.getPriority());
            details.put("description", provider.getProviderDescription());
            providerDetails.put(provider.getProviderName(), details);
        }
        status.put("providers", providerDetails);
        
        return status;
    }

    /**
     * 清空注册表
     */
    public void clear() {
        providersByGrantType.clear();
        providersByName.clear();
        allProviders.clear();
        log.info("Provider registry cleared");
    }

    @Override
    public String toString() {
        return String.format("ProviderRegistry{totalProviders=%d, availableProviders=%d, supportedGrantTypes=%d}",
                getProviderCount(), getAvailableProviderCount(), getSupportedGrantTypes().size());
    }
}
