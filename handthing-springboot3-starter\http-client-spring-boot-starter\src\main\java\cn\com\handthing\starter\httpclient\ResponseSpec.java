package cn.com.handthing.starter.httpclient;

import org.springframework.http.HttpStatus;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.lang.NonNull;
import reactor.core.publisher.Mono;

import java.util.function.Function;
import java.util.function.Predicate;

/**
 * 响应规格处理器接口。
 * <p>
 * 这是 Fluent API 的第三步，在请求已经发送并收到响应后调用。
 * 它允许用户根据响应的状态码定义自定义的错误处理逻辑，并指定如何处理响应体。
 */
public interface ResponseSpec {

    /**
     * 添加一个基于HTTP状态码的异常处理函数。
     * <p>
     * 这是一种强大的机制，可以用来覆盖默认的4xx/5xx异常处理。
     * 例如，你可以将一个特定的404 Not Found错误转换为一个自定义的 {@code UserNotFoundException}。
     * <p>
     * 示例:
     * <pre>{@code
     * .onStatus(
     * status -> status == HttpStatus.NOT_FOUND,
     * response -> Mono.error(new UserNotFoundException("User not found"))
     * )
     * }</pre>
     *
     * @param statusPredicate 一个断言 (Predicate)，用于判断收到的HTTP状态码是否匹配此处理规则。
     * @param exceptionFunction 一个函数 (Function)，当断言为真时，它接收原始的 {@link ClientHttpResponse} 并返回一个包含异常的 {@code Mono}。
     * @return 当前响应规格处理器实例，用于链式调用。
     */
    @NonNull
    ResponseSpec onStatus(@NonNull Predicate<HttpStatus> statusPredicate, @NonNull Function<ClientHttpResponse, Mono<? extends Throwable>> exceptionFunction);

    /**
     * 指定期望的响应体类型，并准备进行最终的消费。
     *
     * @param type 期望将响应体解码成的目标Java类型。
     * @param <T>  响应体的泛型类型。
     * @return 一个响应体规格处理器 ({@link BodySpec})，用于最终获取响应体。
     */
    @NonNull
    <T> BodySpec<T> body(@NonNull Class<T> type);

}