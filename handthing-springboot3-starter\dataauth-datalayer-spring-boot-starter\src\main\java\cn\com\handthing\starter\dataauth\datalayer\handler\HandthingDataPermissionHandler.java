package cn.com.handthing.starter.dataauth.datalayer.handler;

import cn.com.handthing.starter.dataauth.datalayer.annotation.DataPermission;
import cn.com.handthing.starter.dataauth.datalayer.context.DataPermissionContext;
import cn.com.handthing.starter.dataauth.datalayer.provider.DataPermissionProvider;
import com.baomidou.mybatisplus.extension.plugins.handler.DataPermissionHandler;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.operators.conditional.AndExpression;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 数据权限处理器
 * <p>
 * 实现MyBatis-Plus的DataPermissionHandler接口，自动在SQL中添加数据权限条件。
 * 根据@DataPermission注解配置和用户权限信息，动态生成权限过滤条件。
 * </p>
 * 
 * <h3>功能特性：</h3>
 * <ul>
 *   <li>自动SQL权限条件注入</li>
 *   <li>支持多种权限类型</li>
 *   <li>支持自定义权限表达式</li>
 *   <li>支持权限范围控制</li>
 * </ul>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Component
public class HandthingDataPermissionHandler implements DataPermissionHandler {
    
    /**
     * 数据权限提供者
     */
    @Autowired
    private List<DataPermissionProvider> permissionProviders;
    
    /**
     * 获取数据权限SQL片段
     * <p>
     * 根据当前权限上下文生成相应的SQL权限条件
     * </p>
     * 
     * @param where       原始WHERE条件
     * @param mappedStatementId Mapper方法ID
     * @return 增强后的WHERE条件
     */
    @Override
    public Expression getSqlSegment(Expression where, String mappedStatementId) {
        try {
            // 获取当前权限上下文
            DataPermissionContext context = DataPermissionContext.getCurrentContext();
            if (context == null || !context.isEnabled()) {
                log.debug("Data permission context is disabled, skipping permission check");
                return where;
            }
            
            // 获取权限配置
            DataPermission permission = context.getPermission();
            if (permission == null || permission.type() == DataPermission.DataPermissionType.NONE) {
                log.debug("No data permission configuration found, skipping permission check");
                return where;
            }
            
            // 检查是否忽略超级管理员
            if (permission.ignoreSuperAdmin() && isSuperAdmin()) {
                log.debug("Current user is super admin, skipping permission check");
                return where;
            }
            
            // 生成权限条件
            String permissionCondition = generatePermissionCondition(permission, mappedStatementId);
            if (permissionCondition == null || permissionCondition.trim().isEmpty()) {
                log.debug("No permission condition generated, skipping permission check");
                return where;
            }
            
            // 解析权限条件为Expression
            Expression permissionExpression = CCJSqlParserUtil.parseCondExpression(permissionCondition);
            
            // 合并原有WHERE条件和权限条件
            if (where == null) {
                log.debug("Applied data permission condition: {}", permissionCondition);
                return permissionExpression;
            } else {
                Expression combinedExpression = new AndExpression(where, permissionExpression);
                log.debug("Combined original WHERE with permission condition: {}", permissionCondition);
                return combinedExpression;
            }
            
        } catch (Exception e) {
            log.error("Failed to apply data permission for {}: {}", mappedStatementId, e.getMessage(), e);
            
            // 根据失败策略处理
            DataPermissionContext context = DataPermissionContext.getCurrentContext();
            if (context != null && context.getPermission() != null) {
                DataPermission.FailureStrategy strategy = context.getPermission().onFailure();
                switch (strategy) {
                    case EXCEPTION:
                        throw new RuntimeException("Data permission check failed", e);
                    case LOG:
                        log.error("Data permission check failed, continuing execution", e);
                        break;
                    case FILTER:
                        // 返回一个永远为false的条件，过滤所有数据
                        try {
                            return CCJSqlParserUtil.parseCondExpression("1 = 0");
                        } catch (Exception parseException) {
                            log.error("Failed to create filter condition", parseException);
                            return where;
                        }
                    case IGNORE:
                    default:
                        log.debug("Ignoring data permission check failure");
                        break;
                }
            }
            
            return where;
        }
    }
    
    /**
     * 生成权限条件
     * 
     * @param permission 权限配置
     * @param mappedStatementId Mapper方法ID
     * @return 权限条件SQL片段
     */
    protected String generatePermissionCondition(DataPermission permission, String mappedStatementId) {
        try {
            // 如果是自定义权限，直接使用表达式
            if (permission.type() == DataPermission.DataPermissionType.CUSTOM) {
                return processCustomExpression(permission.expression());
            }
            
            // 查找对应的权限提供者
            DataPermissionProvider provider = findPermissionProvider(permission.type());
            if (provider == null) {
                log.warn("No permission provider found for type: {}", permission.type());
                return null;
            }
            
            // 生成权限条件
            return provider.generatePermissionCondition(permission, mappedStatementId);
            
        } catch (Exception e) {
            log.error("Failed to generate permission condition for type: {}", permission.type(), e);
            return null;
        }
    }
    
    /**
     * 处理自定义权限表达式
     * 
     * @param expression 自定义表达式
     * @return 处理后的SQL条件
     */
    protected String processCustomExpression(String expression) {
        if (expression == null || expression.trim().isEmpty()) {
            return null;
        }
        
        // TODO: 实现SpEL表达式解析和参数替换
        // 这里可以集成Spring的SpEL表达式解析器
        // 或者实现简单的参数占位符替换
        
        return expression;
    }
    
    /**
     * 查找权限提供者
     * 
     * @param type 权限类型
     * @return 权限提供者
     */
    protected DataPermissionProvider findPermissionProvider(DataPermission.DataPermissionType type) {
        if (permissionProviders == null || permissionProviders.isEmpty()) {
            return null;
        }
        
        return permissionProviders.stream()
                .filter(provider -> provider.supports(type))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * 检查当前用户是否为超级管理员
     * 
     * @return 如果是超级管理员返回true，否则返回false
     */
    protected boolean isSuperAdmin() {
        try {
            // TODO: 实现超级管理员检查逻辑
            // 可以从AuthContextHolder或其他用户上下文获取用户信息
            // 然后检查用户是否具有超级管理员角色
            
            return false; // 默认返回false
        } catch (Exception e) {
            log.debug("Failed to check super admin status", e);
            return false;
        }
    }
    
    /**
     * 获取当前用户ID
     * 
     * @return 当前用户ID
     */
    protected String getCurrentUserId() {
        try {
            // TODO: 从AuthContextHolder或其他用户上下文获取当前用户ID
            return null;
        } catch (Exception e) {
            log.debug("Failed to get current user ID", e);
            return null;
        }
    }
    
    /**
     * 获取当前用户部门ID
     * 
     * @return 当前用户部门ID
     */
    protected String getCurrentDeptId() {
        try {
            // TODO: 从用户上下文获取当前用户的部门ID
            return null;
        } catch (Exception e) {
            log.debug("Failed to get current dept ID", e);
            return null;
        }
    }
    
    /**
     * 获取当前用户角色列表
     * 
     * @return 当前用户角色列表
     */
    protected List<String> getCurrentRoles() {
        try {
            // TODO: 从用户上下文获取当前用户的角色列表
            return List.of();
        } catch (Exception e) {
            log.debug("Failed to get current roles", e);
            return List.of();
        }
    }
    
    /**
     * 验证权限配置
     * 
     * @param permission 权限配置
     * @return 验证结果，如果配置有效返回空字符串，否则返回错误信息
     */
    public String validatePermissionConfig(DataPermission permission) {
        if (permission == null) {
            return "Permission configuration is null";
        }
        
        StringBuilder errors = new StringBuilder();
        
        // 验证自定义权限表达式
        if (permission.type() == DataPermission.DataPermissionType.CUSTOM) {
            if (permission.expression() == null || permission.expression().trim().isEmpty()) {
                errors.append("Custom permission expression cannot be empty; ");
            }
        }
        
        // 验证权限提供者
        if (permission.type() != DataPermission.DataPermissionType.CUSTOM 
                && permission.type() != DataPermission.DataPermissionType.NONE) {
            DataPermissionProvider provider = findPermissionProvider(permission.type());
            if (provider == null) {
                errors.append("No permission provider found for type: ").append(permission.type()).append("; ");
            }
        }
        
        return errors.toString();
    }
    
    /**
     * 获取权限处理器状态信息
     * 
     * @return 状态信息
     */
    public String getStatusInfo() {
        StringBuilder sb = new StringBuilder();
        sb.append("DataPermissionHandler{");
        sb.append("providers=").append(permissionProviders != null ? permissionProviders.size() : 0);
        sb.append(", context=").append(DataPermissionContext.getCurrentContext() != null ? "available" : "null");
        sb.append(", superAdmin=").append(isSuperAdmin());
        sb.append("}");
        return sb.toString();
    }
}
