package cn.com.handthing.starter.connector.feishu.config;

import cn.com.handthing.starter.connector.auth.AuthService;
import cn.com.handthing.starter.connector.auth.DefaultAuthService;
import cn.com.handthing.starter.connector.token.TokenManager;
import cn.com.handthing.starter.connector.feishu.FeishuAuthProvider;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;

import jakarta.annotation.PostConstruct;

/**
 * 飞书连接器自动配置
 * <p>
 * 自动配置飞书连接器相关的Bean，包括配置类、认证提供者、服务类等。
 * 当启用飞书连接器时，自动注册到认证服务和Token管理器中。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@RequiredArgsConstructor
@AutoConfiguration
@EnableConfigurationProperties(FeishuConfig.class)
@ConditionalOnProperty(prefix = "handthing.connector.feishu", name = "enabled", havingValue = "true")
@ConditionalOnClass(AuthService.class)
@ComponentScan(basePackages = "cn.com.handthing.starter.connector.feishu")
public class FeishuAutoConfiguration {

    private final AuthService authService;
    private final FeishuAuthProvider feishuAuthProvider;
    private final TokenManager tokenManager;
    private final FeishuConfig feishuConfig;

    /**
     * 注册飞书认证提供者到认证服务
     */
    @PostConstruct
    public void registerFeishuAuthProvider() {
        if (authService instanceof DefaultAuthService) {
            DefaultAuthService defaultAuthService = (DefaultAuthService) authService;
            defaultAuthService.registerAuthProvider(feishuAuthProvider);
            tokenManager.registerAuthProvider(feishuAuthProvider);
            
            log.info("Registered Feishu auth provider - AppId: {}, AppType: {}, ThirdParty: {}, Valid: {}", 
                    feishuConfig.getAppId(), 
                    feishuConfig.getAppType(),
                    feishuConfig.isThirdPartyApp(),
                    feishuConfig.isValid());
        } else {
            log.warn("AuthService is not DefaultAuthService, cannot register Feishu auth provider");
        }
    }

    /**
     * 配置信息日志输出
     */
    @PostConstruct
    public void logConfiguration() {
        log.info("Feishu configuration: enabled=true, valid={}, appId={}, appType={}, thirdPartyMode={}, botEnabled={}, larkMode={}, encryptMessage={}",
                feishuConfig.isValid(),
                feishuConfig.getAppId(),
                feishuConfig.getAppType(),
                feishuConfig.isThirdPartyApp(),
                feishuConfig.isBotAvailable(),
                feishuConfig.isLarkMode(),
                feishuConfig.isEncryptionEnabled());
    }
}
