package cn.com.handthing.starter.connector.wecom.config;

import cn.com.handthing.starter.connector.auth.AuthService;
import cn.com.handthing.starter.connector.auth.DefaultAuthService;
import cn.com.handthing.starter.connector.token.TokenManager;
import cn.com.handthing.starter.connector.wecom.WeComAuthProvider;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.web.client.RestTemplate;

import jakarta.annotation.PostConstruct;

/**
 * 企业微信自动配置类
 * <p>
 * 当企业微信配置启用时，自动装配企业微信相关的Bean，
 * 并注册到统一的认证服务中。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@RequiredArgsConstructor
@AutoConfiguration
@EnableConfigurationProperties(WeComConfig.class)
@ConditionalOnProperty(prefix = "handthing.connector.wecom", name = "enabled", havingValue = "true")
@ConditionalOnClass(AuthService.class)
@ComponentScan(basePackages = "cn.com.handthing.starter.connector.wecom")
public class WeComAutoConfiguration {



    private final AuthService authService;
    private final WeComAuthProvider weComAuthProvider;
    private final TokenManager tokenManager;
    private final WeComConfig weComConfig;

    /**
     * 注册企业微信认证提供者到认证服务
     */
    @PostConstruct
    public void registerWeComAuthProvider() {
        if (authService instanceof DefaultAuthService) {
            DefaultAuthService defaultAuthService = (DefaultAuthService) authService;
            defaultAuthService.registerAuthProvider(weComAuthProvider);
            tokenManager.registerAuthProvider(weComAuthProvider);

            log.info("Registered WeCom auth provider - CorpId: {}, AgentId: {}, Valid: {}",
                    weComConfig.getCorpId(),
                    weComConfig.getAgentId(),
                    weComConfig.isValid());
        } else {
            log.warn("AuthService is not DefaultAuthService, cannot register WeCom auth provider");
        }
    }

    /**
     * 企业微信配置信息Bean
     *
     * @param weComConfig 企业微信配置
     * @return 配置信息
     */
    @Bean
    public WeComConfigInfo weComConfigInfo(WeComConfig weComConfig) {
        WeComConfigInfo configInfo = new WeComConfigInfo();
        configInfo.setEnabled(weComConfig.isEnabled());
        configInfo.setValid(weComConfig.isValid());
        configInfo.setCorpId(weComConfig.getCorpId());
        configInfo.setAgentId(weComConfig.getAgentId());
        configInfo.setThirdPartyMode(weComConfig.isThirdPartyMode());
        configInfo.setCallbackEnabled(weComConfig.isCallbackEnabled());
        configInfo.setApiBaseUrl(weComConfig.getEffectiveApiBaseUrl());
        configInfo.setAuthUrl(weComConfig.getEffectiveAuthUrl());
        
        log.info("WeCom configuration: enabled={}, valid={}, corpId={}, agentId={}, thirdPartyMode={}", 
                configInfo.isEnabled(), 
                configInfo.isValid(),
                configInfo.getCorpId(),
                configInfo.getAgentId(),
                configInfo.isThirdPartyMode());
        
        return configInfo;
    }

    /**
     * 企业微信配置信息类
     */
    public static class WeComConfigInfo {
        private boolean enabled;
        private boolean valid;
        private String corpId;
        private String agentId;
        private boolean thirdPartyMode;
        private boolean callbackEnabled;
        private String apiBaseUrl;
        private String authUrl;

        // Getters and Setters
        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public boolean isValid() {
            return valid;
        }

        public void setValid(boolean valid) {
            this.valid = valid;
        }

        public String getCorpId() {
            return corpId;
        }

        public void setCorpId(String corpId) {
            this.corpId = corpId;
        }

        public String getAgentId() {
            return agentId;
        }

        public void setAgentId(String agentId) {
            this.agentId = agentId;
        }

        public boolean isThirdPartyMode() {
            return thirdPartyMode;
        }

        public void setThirdPartyMode(boolean thirdPartyMode) {
            this.thirdPartyMode = thirdPartyMode;
        }

        public boolean isCallbackEnabled() {
            return callbackEnabled;
        }

        public void setCallbackEnabled(boolean callbackEnabled) {
            this.callbackEnabled = callbackEnabled;
        }

        public String getApiBaseUrl() {
            return apiBaseUrl;
        }

        public void setApiBaseUrl(String apiBaseUrl) {
            this.apiBaseUrl = apiBaseUrl;
        }

        public String getAuthUrl() {
            return authUrl;
        }

        public void setAuthUrl(String authUrl) {
            this.authUrl = authUrl;
        }

        @Override
        public String toString() {
            return String.format("WeComConfigInfo{enabled=%s, valid=%s, corpId='%s', agentId='%s', thirdPartyMode=%s, callbackEnabled=%s, apiBaseUrl='%s', authUrl='%s'}",
                    enabled, valid, corpId, agentId, thirdPartyMode, callbackEnabled, apiBaseUrl, authUrl);
        }
    }
}
