package cn.com.handthing.starter.cache.level;

import cn.com.handthing.starter.cache.CacheService;
import cn.com.handthing.starter.cache.level.config.LevelCacheProperties;
import cn.com.handthing.starter.cache.level.sync.CacheSyncService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.autoconfigure.AutoConfigurations;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.test.context.runner.ApplicationContextRunner;
import org.springframework.cache.CacheManager;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * LevelCacheAutoConfiguration 单元测试
 *
 * <AUTHOR>
 * @since V1.0.0
 */
class LevelCacheAutoConfigurationTest {

    private final ApplicationContextRunner contextRunner = new ApplicationContextRunner()
            .withConfiguration(AutoConfigurations.of(
                    LevelCacheAutoConfiguration.class,
                    RedisAutoConfiguration.class
            ));

    @Test
    void testAutoConfigurationEnabled() {
        contextRunner
                .withPropertyValues(
                        "handthing.cache.type=level",
                        "handthing.cache.level.enabled=true"
                )
                .withBean("l1CacheManager", CacheManager.class, () -> org.mockito.Mockito.mock(CacheManager.class))
                .withBean("l2CacheManager", CacheManager.class, () -> org.mockito.Mockito.mock(CacheManager.class))
                .run(context -> {
                    assertThat(context).hasSingleBean(LevelCacheProperties.class);
                    assertThat(context).hasSingleBean(LevelCacheManager.class);
                    assertThat(context).hasSingleBean(CacheService.class);
                });
    }

    @Test
    void testAutoConfigurationDisabled() {
        contextRunner
                .withPropertyValues(
                        "handthing.cache.type=redis", // 不是level类型
                        "handthing.cache.level.enabled=false"
                )
                .run(context -> {
                    assertThat(context).doesNotHaveBean(LevelCacheManager.class);
                    assertThat(context).doesNotHaveBean(LevelCacheService.class);
                });
    }

    @Test
    void testPropertiesBinding() {
        contextRunner
                .withPropertyValues(
                        "handthing.cache.type=level",
                        "handthing.cache.level.enabled=true",
                        "handthing.cache.level.l1.type=caffeine",
                        "handthing.cache.level.l2.type=redis",
                        "handthing.cache.level.l2.key-prefix=test:",
                        "handthing.cache.level.sync.topic=test-topic"
                )
                .withBean("l1CacheManager", CacheManager.class, () -> org.mockito.Mockito.mock(CacheManager.class))
                .withBean("l2CacheManager", CacheManager.class, () -> org.mockito.Mockito.mock(CacheManager.class))
                .run(context -> {
                    LevelCacheProperties properties = context.getBean(LevelCacheProperties.class);
                    assertThat(properties.isEnabled()).isTrue();
                    assertThat(properties.getL1().getType()).isEqualTo("caffeine");
                    assertThat(properties.getL2().getType()).isEqualTo("redis");
                    assertThat(properties.getL2().getKeyPrefix()).isEqualTo("test:");
                    assertThat(properties.getSync().getTopic()).isEqualTo("test-topic");
                });
    }
}