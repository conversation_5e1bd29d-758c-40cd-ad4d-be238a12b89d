package cn.com.handthing.starter.connector.wechat.api;

import cn.com.handthing.starter.connector.wechat.config.WeChatConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 微信用户API
 * <p>
 * 提供微信用户管理相关的API功能，包括用户信息获取、用户列表管理等。
 * 支持公众号的用户管理功能。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "handthing.connector.wechat", name = "enabled", havingValue = "true")
public class WeChatUserApi {

    private final WeChatConfig config;
    private final RestTemplate restTemplate;

    /**
     * 获取用户基本信息
     *
     * @param accessToken 访问令牌
     * @param openId      用户openid
     * @param lang        语言（zh_CN, zh_TW, en）
     * @return 用户信息
     */
    public Map<String, Object> getUserInfo(String accessToken, String openId, String lang) {
        try {
            log.debug("Getting WeChat user info for openId: {}", openId);

            String url = UriComponentsBuilder.fromHttpUrl(config.getEffectiveApiBaseUrl() + "/cgi-bin/user/info")
                    .queryParam("access_token", accessToken)
                    .queryParam("openid", openId)
                    .queryParam("lang", lang != null ? lang : "zh_CN")
                    .build()
                    .toUriString();

            Map<String, Object> response = restTemplate.getForObject(url, Map.class);
            log.debug("WeChat user info retrieved, response: {}", response);

            return response;

        } catch (Exception e) {
            log.error("Failed to get WeChat user info", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("errcode", -1);
            errorResponse.put("errmsg", "Failed to get user info: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 获取用户基本信息（默认中文）
     *
     * @param accessToken 访问令牌
     * @param openId      用户openid
     * @return 用户信息
     */
    public Map<String, Object> getUserInfo(String accessToken, String openId) {
        return getUserInfo(accessToken, openId, "zh_CN");
    }

    /**
     * 批量获取用户基本信息
     *
     * @param accessToken 访问令牌
     * @param openIds     用户openid列表
     * @param lang        语言
     * @return 用户信息列表
     */
    public Map<String, Object> batchGetUserInfo(String accessToken, List<String> openIds, String lang) {
        try {
            log.debug("Batch getting WeChat user info for {} users", openIds.size());

            String url = config.getEffectiveApiBaseUrl() + "/cgi-bin/user/info/batchget?access_token=" + accessToken;

            Map<String, Object> requestBody = new HashMap<>();
            List<Map<String, String>> userList = openIds.stream()
                    .map(openId -> {
                        Map<String, String> user = new HashMap<>();
                        user.put("openid", openId);
                        user.put("lang", lang != null ? lang : "zh_CN");
                        return user;
                    })
                    .toList();
            requestBody.put("user_list", userList);

            Map<String, Object> response = restTemplate.postForObject(url, requestBody, Map.class);
            log.debug("WeChat batch user info retrieved, response: {}", response);

            return response;

        } catch (Exception e) {
            log.error("Failed to batch get WeChat user info", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("errcode", -1);
            errorResponse.put("errmsg", "Failed to batch get user info: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 获取用户列表
     *
     * @param accessToken 访问令牌
     * @param nextOpenId  拉取列表的第一个用户的OPENID，不填默认从头开始拉取
     * @return 用户列表
     */
    public Map<String, Object> getUserList(String accessToken, String nextOpenId) {
        try {
            log.debug("Getting WeChat user list, nextOpenId: {}", nextOpenId);

            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(config.getEffectiveApiBaseUrl() + "/cgi-bin/user/get")
                    .queryParam("access_token", accessToken);
            
            if (nextOpenId != null && !nextOpenId.trim().isEmpty()) {
                builder.queryParam("next_openid", nextOpenId);
            }

            String url = builder.build().toUriString();
            Map<String, Object> response = restTemplate.getForObject(url, Map.class);
            log.debug("WeChat user list retrieved, response: {}", response);

            return response;

        } catch (Exception e) {
            log.error("Failed to get WeChat user list", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("errcode", -1);
            errorResponse.put("errmsg", "Failed to get user list: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 获取用户列表（从头开始）
     *
     * @param accessToken 访问令牌
     * @return 用户列表
     */
    public Map<String, Object> getUserList(String accessToken) {
        return getUserList(accessToken, null);
    }

    /**
     * 设置用户备注名
     *
     * @param accessToken 访问令牌
     * @param openId      用户openid
     * @param remark      新的备注名，长度必须小于30字符
     * @return 操作结果
     */
    public Map<String, Object> updateUserRemark(String accessToken, String openId, String remark) {
        try {
            log.debug("Updating WeChat user remark for openId: {}", openId);

            String url = config.getEffectiveApiBaseUrl() + "/cgi-bin/user/info/updateremark?access_token=" + accessToken;

            Map<String, String> requestBody = new HashMap<>();
            requestBody.put("openid", openId);
            requestBody.put("remark", remark);

            Map<String, Object> response = restTemplate.postForObject(url, requestBody, Map.class);
            log.debug("WeChat user remark updated, response: {}", response);

            return response;

        } catch (Exception e) {
            log.error("Failed to update WeChat user remark", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("errcode", -1);
            errorResponse.put("errmsg", "Failed to update user remark: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 获取公众号的黑名单列表
     *
     * @param accessToken 访问令牌
     * @param beginOpenId 拉取列表的第一个用户的OPENID，不填默认从头开始拉取
     * @return 黑名单列表
     */
    public Map<String, Object> getBlackList(String accessToken, String beginOpenId) {
        try {
            log.debug("Getting WeChat black list, beginOpenId: {}", beginOpenId);

            String url = config.getEffectiveApiBaseUrl() + "/cgi-bin/tags/members/getblacklist?access_token=" + accessToken;

            Map<String, String> requestBody = new HashMap<>();
            if (beginOpenId != null && !beginOpenId.trim().isEmpty()) {
                requestBody.put("begin_openid", beginOpenId);
            }

            Map<String, Object> response = restTemplate.postForObject(url, requestBody, Map.class);
            log.debug("WeChat black list retrieved, response: {}", response);

            return response;

        } catch (Exception e) {
            log.error("Failed to get WeChat black list", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("errcode", -1);
            errorResponse.put("errmsg", "Failed to get black list: " + e.getMessage());
            return errorResponse;
        }
    }
}
