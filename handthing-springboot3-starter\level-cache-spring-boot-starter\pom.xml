<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.com.handthing.springboot3.starter</groupId>
        <artifactId>starter-parent</artifactId>
        <version>${revision}</version>
        <relativePath>../starter-parent</relativePath>
    </parent>

    <artifactId>level-cache-spring-boot-starter</artifactId>
    <name>HandThing :: Level Cache Starter</name>
    <description>多级缓存实现，支持L1(本地)+L2(远程)架构和缓存同步</description>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <!-- 缓存核心模块 -->
        <dependency>
            <groupId>cn.com.handthing.springboot3.starter</groupId>
            <artifactId>cache-spring-boot-starter</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- Caffeine缓存模块 (L1缓存) -->
        <dependency>
            <groupId>cn.com.handthing.springboot3.starter</groupId>
            <artifactId>caffeine-cache-spring-boot-starter</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- Redis缓存模块 (L2缓存) -->
        <dependency>
            <groupId>cn.com.handthing.springboot3.starter</groupId>
            <artifactId>redis-cache-spring-boot-starter</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- Spring Boot 自动配置 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-autoconfigure</artifactId>
        </dependency>

        <!-- 配置处理器 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- Spring Context 缓存抽象 -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>

        <!-- Spring Data Redis (用于Pub/Sub) -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>

        <!-- 参数校验 -->
        <dependency>
            <groupId>jakarta.validation</groupId>
            <artifactId>jakarta.validation-api</artifactId>
        </dependency>

        <!-- Jakarta Annotation -->
        <dependency>
            <groupId>jakarta.annotation</groupId>
            <artifactId>jakarta.annotation-api</artifactId>
        </dependency>

        <!-- HandThing Core 工具类 -->
        <dependency>
            <groupId>cn.com.handthing.springboot3.starter</groupId>
            <artifactId>handthing-core</artifactId>
        </dependency>

        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- 测试依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- 嵌入式 Redis 用于测试 -->
        <dependency>
            <groupId>it.ozimov</groupId>
            <artifactId>embedded-redis</artifactId>
            <version>0.7.3</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

</project>