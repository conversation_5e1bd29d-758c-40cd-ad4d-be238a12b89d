package cn.com.handthing.starter.httpclient.interceptor;

import cn.com.handthing.starter.httpclient.config.CryptoProperties;
import cn.com.handthing.starter.httpclient.crypto.RequestEncryptor;
import cn.com.handthing.starter.httpclient.crypto.ResponseDecryptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;

/**
 * RestTemplate 加密拦截器
 * 
 * <AUTHOR>
 * @since V1.0.0
 */
public class CryptoInterceptor implements ClientHttpRequestInterceptor {

    private static final Logger log = LoggerFactory.getLogger(CryptoInterceptor.class);
    
    private final CryptoProperties cryptoProperties;
    private final RequestEncryptor<?> requestEncryptor;
    private final ResponseDecryptor responseDecryptor;

    public CryptoInterceptor(CryptoProperties cryptoProperties, 
                           RequestEncryptor<?> requestEncryptor,
                           ResponseDecryptor responseDecryptor) {
        this.cryptoProperties = cryptoProperties;
        this.requestEncryptor = requestEncryptor;
        this.responseDecryptor = responseDecryptor;
    }

    @Override
    public ClientHttpResponse intercept(
            HttpRequest request, 
            byte[] body, 
            ClientHttpRequestExecution execution) throws IOException {
        
        // 加密请求体
        byte[] encryptedBody = encryptRequestBody(request, body);
        
        // 执行请求
        ClientHttpResponse response = execution.execute(request, encryptedBody);
        
        // 解密响应体
        return new DecryptingClientHttpResponse(response);
    }

    @SuppressWarnings("unchecked")
    private byte[] encryptRequestBody(HttpRequest request, byte[] body) {
        if (body == null || body.length == 0) {
            return body;
        }

        try {
            // 将 byte[] 转换为 String 进行加密
            String bodyStr = new String(body, StandardCharsets.UTF_8);
            Object encrypted = ((RequestEncryptor<Object>) requestEncryptor).encrypt(request, bodyStr);
            
            if (encrypted instanceof String) {
                log.debug("Request body encrypted");
                return ((String) encrypted).getBytes(StandardCharsets.UTF_8);
            } else if (encrypted instanceof byte[]) {
                log.debug("Request body encrypted");
                return (byte[]) encrypted;
            }
        } catch (Exception e) {
            log.warn("Failed to encrypt request body, using original", e);
        }

        return body;
    }

    /**
     * 解密响应包装器
     */
    private class DecryptingClientHttpResponse implements ClientHttpResponse {
        private final ClientHttpResponse response;
        private byte[] decryptedBody;

        public DecryptingClientHttpResponse(ClientHttpResponse response) {
            this.response = response;
        }

        @Override
        public org.springframework.http.HttpStatusCode getStatusCode() throws IOException {
            return response.getStatusCode();
        }

        @Override
        public String getStatusText() throws IOException {
            return response.getStatusText();
        }

        @Override
        public void close() {
            response.close();
        }

        @Override
        public InputStream getBody() throws IOException {
            if (decryptedBody == null) {
                decryptedBody = decryptResponseBody();
            }
            return new ByteArrayInputStream(decryptedBody);
        }

        @Override
        public org.springframework.http.HttpHeaders getHeaders() {
            return response.getHeaders();
        }

        private byte[] decryptResponseBody() throws IOException {
            byte[] originalBody = response.getBody().readAllBytes();
            
            if (originalBody == null || originalBody.length == 0) {
                return originalBody;
            }

            try {
                InputStream decryptedStream = responseDecryptor.decrypt(response);
                byte[] decrypted = decryptedStream.readAllBytes();
                log.debug("Response body decrypted");
                return decrypted;
            } catch (Exception e) {
                log.warn("Failed to decrypt response body, using original", e);
                return originalBody;
            }
        }
    }
}
