# HandThing Admin 详细设计文档

## 1. 接口设计规范

### 1.1 RESTful API设计原则
- **资源导向**: URL表示资源，HTTP方法表示操作
- **统一接口**: 统一的请求响应格式
- **无状态**: 每个请求包含完整信息
- **分层系统**: 清晰的分层架构
- **可缓存**: 支持缓存机制

### 1.2 URL设计规范
```
基础格式: /api/v{version}/{module}/{resource}
示例:
- GET /api/v1/auth/login          # 用户登录
- GET /api/v1/tenant/tenants      # 获取租户列表
- POST /api/v1/user/users         # 创建用户
- PUT /api/v1/user/users/{id}     # 更新用户
- DELETE /api/v1/user/users/{id}  # 删除用户
```

### 1.3 HTTP方法使用规范
| 方法 | 用途 | 示例 |
|------|------|------|
| GET | 查询资源 | GET /api/v1/users |
| POST | 创建资源 | POST /api/v1/users |
| PUT | 更新资源(全量) | PUT /api/v1/users/{id} |
| PATCH | 更新资源(部分) | PATCH /api/v1/users/{id} |
| DELETE | 删除资源 | DELETE /api/v1/users/{id} |

### 1.4 统一响应格式

> code：只有三个值 200、401、500，其他500 具体的错误编码在 error 中体现


```json
{
    "code": 200,
    "message": "success",
    "data": {},
    "timestamp": 1640995200000,
    "traceId": "abc123def456"
}
```

### 1.5 分页响应格式
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "records": [],
        "total": 100,
        "size": 10,
        "current": 1,
        "pages": 10
    },
    "timestamp": 1640995200000,
    "traceId": "abc123def456"
}
```

### 1.6 错误响应格式
```json
{
    "code": 500,
    "message": "请求失败",
    "error": {
        "errorCode": "100000",
        "errorMessage": "参数校验异常：用户名称不能为空！"
    },
    "timestamp": 1640995200000,
    "traceId": "abc123def456"
}
```

```json
{
    "code": 500,
    "message": "请求失败",
    "error": {
        "errorCode": "100001",
        "errorMessage": "用户名称已存在！"
    },
    "timestamp": 1640995200000,
    "traceId": "abc123def456"
}
```

### 1.7 没有权限响应格式
```json
{
    "code": 401,
    "message": "暂无权限",
    "timestamp": 1640995200000,
    "traceId": "abc123def456"
}
```

## 2. 认证授权模块接口设计

### 2.1 用户认证接口

#### 2.1.1 用户登录
```yaml
接口路径: POST /api/v1/auth/login
接口描述: 用户登录认证，支持多种登录方式
请求参数:
  Content-Type: application/json
  Body:
    username: string, required, 用户名
    password: string, optional, 密码(密码登录时必填)
    phone: string, optional, 手机号(短信登录时必填)
    smsCode: string, optional, 短信验证码(短信登录时必填)
    email: string, optional, 邮箱(邮箱登录时必填)
    emailCode: string, optional, 邮箱验证码(邮箱登录时必填)
    captcha: string, optional, 图形验证码
    captchaKey: string, optional, 验证码key
    loginType: string, required, 登录类型(password/sms/email/oauth)
    platform: string, optional, 第三方平台(oauth登录时必填)
    
响应数据:
  accessToken: string, 访问令牌
  refreshToken: string, 刷新令牌
  expiresIn: number, 过期时间(秒)
  tokenType: string, 令牌类型(Bearer)
  userInfo:
    id: number, 用户ID
    username: string, 用户名
    nickname: string, 昵称
    avatar: string, 头像URL
    roles: array, 角色列表
    permissions: array, 权限列表
```

#### 2.1.2 刷新Token
```yaml
接口路径: POST /api/v1/auth/refresh
接口描述: 刷新访问令牌
请求头:
  Authorization: Bearer {refresh_token}
  
响应数据:
  accessToken: string, 新的访问令牌
  refreshToken: string, 新的刷新令牌
  expiresIn: number, 过期时间(秒)
```

#### 2.1.3 用户登出
```yaml
接口路径: POST /api/v1/auth/logout
接口描述: 用户登出，使Token失效
请求头:
  Authorization: Bearer {access_token}
  
响应数据:
  message: string, 登出成功消息
```

#### 2.1.4 获取验证码
```yaml
接口路径: GET /api/v1/auth/captcha
接口描述: 获取图形验证码
响应数据:
  captchaKey: string, 验证码key
  captchaImage: string, 验证码图片(base64)
```

#### 2.1.5 发送短信验证码
```yaml
接口路径: POST /api/v1/auth/sms/send
接口描述: 发送短信验证码
请求参数:
  phone: string, required, 手机号
  type: string, required, 验证码类型(login/register/reset)
  
响应数据:
  message: string, 发送成功消息
```

### 2.2 第三方登录接口

#### 2.2.1 获取授权URL
```yaml
接口路径: GET /api/v1/auth/oauth/{platform}/authorize
接口描述: 获取第三方平台授权URL
路径参数:
  platform: string, 平台标识(wecom/dingtalk/feishu)
请求参数:
  redirectUri: string, optional, 回调地址
  
响应数据:
  authorizeUrl: string, 授权URL
  state: string, 状态参数
```

#### 2.2.2 第三方登录回调
```yaml
接口路径: POST /api/v1/auth/oauth/{platform}/callback
接口描述: 处理第三方平台登录回调
路径参数:
  platform: string, 平台标识
请求参数:
  code: string, required, 授权码
  state: string, required, 状态参数
  
响应数据:
  accessToken: string, 访问令牌
  refreshToken: string, 刷新令牌
  expiresIn: number, 过期时间
  userInfo: object, 用户信息
```

### 2.3 权限管理接口

#### 2.3.1 获取当前用户信息
```yaml
接口路径: GET /api/v1/auth/userinfo
接口描述: 获取当前登录用户信息
请求头:
  Authorization: Bearer {access_token}
  
响应数据:
  id: number, 用户ID
  username: string, 用户名
  nickname: string, 昵称
  email: string, 邮箱
  phone: string, 手机号
  avatar: string, 头像URL
  status: number, 状态
  roles: array, 角色列表
  permissions: array, 权限列表
  departments: array, 部门列表
```

#### 2.3.2 获取用户菜单
```yaml
接口路径: GET /api/v1/auth/menus
接口描述: 获取当前用户可访问的菜单
请求头:
  Authorization: Bearer {access_token}
  
响应数据:
  - id: number, 菜单ID
    name: string, 菜单名称
    path: string, 路由路径
    component: string, 组件路径
    icon: string, 图标
    sort: number, 排序
    children: array, 子菜单
```

## 3. 租户管理模块接口设计

### 3.1 租户管理接口

#### 3.1.1 租户列表
```yaml
接口路径: GET /api/v1/tenant/tenants
接口描述: 获取租户列表
请求参数:
  page: number, optional, 页码(默认1)
  size: number, optional, 页大小(默认10)
  keyword: string, optional, 搜索关键词
  status: number, optional, 状态筛选
  packageId: number, optional, 套餐筛选
  
响应数据:
  records:
    - id: number, 租户ID
      tenantCode: string, 租户编码
      tenantName: string, 租户名称
      companyName: string, 公司名称
      contactName: string, 联系人
      contactPhone: string, 联系电话
      contactEmail: string, 联系邮箱
      packageName: string, 套餐名称
      status: number, 状态
      expireTime: string, 到期时间
      createTime: string, 创建时间
  total: number, 总数
  size: number, 页大小
  current: number, 当前页
  pages: number, 总页数
```

#### 3.1.2 创建租户
```yaml
接口路径: POST /api/v1/tenant/tenants
接口描述: 创建新租户
请求参数:
  tenantName: string, required, 租户名称
  companyName: string, optional, 公司名称
  contactName: string, required, 联系人姓名
  contactPhone: string, required, 联系电话
  contactEmail: string, optional, 联系邮箱
  packageId: number, required, 套餐ID
  expireTime: string, optional, 到期时间
  adminUsername: string, required, 管理员用户名
  adminPassword: string, required, 管理员密码
  
响应数据:
  id: number, 租户ID
  tenantCode: string, 租户编码
  message: string, 创建成功消息
```

#### 3.1.3 更新租户
```yaml
接口路径: PUT /api/v1/tenant/tenants/{id}
接口描述: 更新租户信息
路径参数:
  id: number, 租户ID
请求参数:
  tenantName: string, optional, 租户名称
  companyName: string, optional, 公司名称
  contactName: string, optional, 联系人姓名
  contactPhone: string, optional, 联系电话
  contactEmail: string, optional, 联系邮箱
  packageId: number, optional, 套餐ID
  status: number, optional, 状态
  expireTime: string, optional, 到期时间
  
响应数据:
  message: string, 更新成功消息
```

#### 3.1.4 删除租户
```yaml
接口路径: DELETE /api/v1/tenant/tenants/{id}
接口描述: 删除租户(软删除)
路径参数:
  id: number, 租户ID
  
响应数据:
  message: string, 删除成功消息
```

### 3.2 套餐管理接口

#### 3.2.1 套餐列表
```yaml
接口路径: GET /api/v1/tenant/packages
接口描述: 获取套餐列表
请求参数:
  page: number, optional, 页码
  size: number, optional, 页大小
  status: number, optional, 状态筛选
  
响应数据:
  records:
    - id: number, 套餐ID
      packageName: string, 套餐名称
      packageCode: string, 套餐编码
      description: string, 套餐描述
      priceMonthly: number, 月付价格
      priceYearly: number, 年付价格
      maxUsers: number, 最大用户数
      maxStorage: number, 最大存储空间
      maxApiCalls: number, 最大API调用次数
      permissions: array, 权限列表
      status: number, 状态
      createTime: string, 创建时间
```

#### 3.2.2 创建套餐
```yaml
接口路径: POST /api/v1/tenant/packages
接口描述: 创建套餐
请求参数:
  packageName: string, required, 套餐名称
  packageCode: string, required, 套餐编码
  description: string, optional, 套餐描述
  priceMonthly: number, optional, 月付价格
  priceYearly: number, optional, 年付价格
  maxUsers: number, required, 最大用户数
  maxStorage: number, required, 最大存储空间
  maxApiCalls: number, required, 最大API调用次数
  permissions: array, required, 权限列表
  
响应数据:
  id: number, 套餐ID
  message: string, 创建成功消息
```

## 4. 用户管理模块接口设计

### 4.1 用户管理接口

#### 4.1.1 用户列表
```yaml
接口路径: GET /api/v1/user/users
接口描述: 获取用户列表
请求参数:
  page: number, optional, 页码
  size: number, optional, 页大小
  keyword: string, optional, 搜索关键词(用户名/姓名/手机号)
  deptId: number, optional, 部门ID筛选
  status: number, optional, 状态筛选
  roleId: number, optional, 角色ID筛选
  
响应数据:
  records:
    - id: number, 用户ID
      username: string, 用户名
      nickname: string, 昵称
      email: string, 邮箱
      phone: string, 手机号
      avatar: string, 头像URL
      status: number, 状态
      deptName: string, 部门名称
      positionName: string, 职位名称
      roles: array, 角色列表
      lastLoginTime: string, 最后登录时间
      createTime: string, 创建时间
```

#### 4.1.2 创建用户
```yaml
接口路径: POST /api/v1/user/users
接口描述: 创建用户
请求参数:
  username: string, required, 用户名
  nickname: string, required, 昵称
  email: string, optional, 邮箱
  phone: string, optional, 手机号
  password: string, required, 密码
  deptId: number, required, 部门ID
  positionId: number, optional, 职位ID
  roleIds: array, optional, 角色ID列表
  
响应数据:
  id: number, 用户ID
  message: string, 创建成功消息
```

#### 4.1.3 更新用户
```yaml
接口路径: PUT /api/v1/user/users/{id}
接口描述: 更新用户信息
路径参数:
  id: number, 用户ID
请求参数:
  nickname: string, optional, 昵称
  email: string, optional, 邮箱
  phone: string, optional, 手机号
  deptId: number, optional, 部门ID
  positionId: number, optional, 职位ID
  status: number, optional, 状态
  
响应数据:
  message: string, 更新成功消息
```

#### 4.1.4 批量导入用户
```yaml
接口路径: POST /api/v1/user/users/batch-import
接口描述: 批量导入用户
请求参数:
  Content-Type: multipart/form-data
  file: file, required, Excel文件
  
响应数据:
  successCount: number, 成功导入数量
  failCount: number, 失败数量
  failList: array, 失败记录列表
  message: string, 导入结果消息
```

#### 4.1.5 重置密码
```yaml
接口路径: PUT /api/v1/user/users/{id}/reset-password
接口描述: 重置用户密码
路径参数:
  id: number, 用户ID
请求参数:
  newPassword: string, required, 新密码
  
响应数据:
  message: string, 重置成功消息
```

### 4.2 角色管理接口

#### 4.2.1 角色列表
```yaml
接口路径: GET /api/v1/user/roles
接口描述: 获取角色列表
请求参数:
  page: number, optional, 页码
  size: number, optional, 页大小
  keyword: string, optional, 搜索关键词
  status: number, optional, 状态筛选
  
响应数据:
  records:
    - id: number, 角色ID
      roleCode: string, 角色编码
      roleName: string, 角色名称
      description: string, 角色描述
      dataScope: number, 数据权限范围
      status: number, 状态
      userCount: number, 用户数量
      createTime: string, 创建时间
```

#### 4.2.2 创建角色
```yaml
接口路径: POST /api/v1/user/roles
接口描述: 创建角色
请求参数:
  roleCode: string, required, 角色编码
  roleName: string, required, 角色名称
  description: string, optional, 角色描述
  dataScope: number, required, 数据权限范围
  
响应数据:
  id: number, 角色ID
  message: string, 创建成功消息
```

#### 4.2.3 分配权限
```yaml
接口路径: PUT /api/v1/user/roles/{id}/permissions
接口描述: 为角色分配权限
路径参数:
  id: number, 角色ID
请求参数:
  permissionIds: array, required, 权限ID列表
  
响应数据:
  message: string, 分配成功消息
```

### 4.3 组织架构接口

#### 4.3.1 部门树
```yaml
接口路径: GET /api/v1/user/departments/tree
接口描述: 获取部门树结构
响应数据:
  - id: number, 部门ID
    deptCode: string, 部门编码
    deptName: string, 部门名称
    parentId: number, 父部门ID
    leaderName: string, 负责人姓名
    phone: string, 联系电话
    email: string, 邮箱
    sort: number, 排序
    status: number, 状态
    userCount: number, 用户数量
    children: array, 子部门
```

#### 4.3.2 创建部门
```yaml
接口路径: POST /api/v1/user/departments
接口描述: 创建部门
请求参数:
  deptCode: string, required, 部门编码
  deptName: string, required, 部门名称
  parentId: number, required, 父部门ID
  leaderId: number, optional, 负责人ID
  phone: string, optional, 联系电话
  email: string, optional, 邮箱
  sort: number, optional, 排序
  
响应数据:
  id: number, 部门ID
  message: string, 创建成功消息
```

#### 4.3.3 职位列表
```yaml
接口路径: GET /api/v1/user/positions
接口描述: 获取职位列表
请求参数:
  page: number, optional, 页码
  size: number, optional, 页大小
  keyword: string, optional, 搜索关键词
  
响应数据:
  records:
    - id: number, 职位ID
      positionCode: string, 职位编码
      positionName: string, 职位名称
      description: string, 职位描述
      levelRank: number, 职级
      status: number, 状态
      userCount: number, 用户数量
      createTime: string, 创建时间
```

## 5. 系统管理模块接口设计

### 5.1 菜单管理接口

#### 5.1.1 菜单树
```yaml
接口路径: GET /api/v1/system/menus/tree
接口描述: 获取菜单树结构
响应数据:
  - id: number, 菜单ID
    menuCode: string, 菜单编码
    menuName: string, 菜单名称
    parentId: number, 父菜单ID
    menuType: number, 菜单类型(1目录,2菜单,3按钮)
    path: string, 路由路径
    component: string, 组件路径
    icon: string, 图标
    sort: number, 排序
    visible: number, 是否可见
    status: number, 状态
    children: array, 子菜单
```

#### 5.1.2 创建菜单
```yaml
接口路径: POST /api/v1/system/menus
接口描述: 创建菜单
请求参数:
  menuCode: string, required, 菜单编码
  menuName: string, required, 菜单名称
  parentId: number, required, 父菜单ID
  menuType: number, required, 菜单类型
  path: string, optional, 路由路径
  component: string, optional, 组件路径
  icon: string, optional, 图标
  sort: number, optional, 排序
  visible: number, optional, 是否可见

响应数据:
  id: number, 菜单ID
  message: string, 创建成功消息
```

### 5.2 字典管理接口

#### 5.2.1 字典类型列表
```yaml
接口路径: GET /api/v1/system/dict/types
接口描述: 获取字典类型列表
请求参数:
  page: number, optional, 页码
  size: number, optional, 页大小
  keyword: string, optional, 搜索关键词

响应数据:
  records:
    - id: number, 字典类型ID
      dictCode: string, 字典编码
      dictName: string, 字典名称
      description: string, 描述
      status: number, 状态
      dataCount: number, 数据项数量
      createTime: string, 创建时间
```

#### 5.2.2 字典数据列表
```yaml
接口路径: GET /api/v1/system/dict/data
接口描述: 获取字典数据列表
请求参数:
  dictTypeId: number, required, 字典类型ID
  page: number, optional, 页码
  size: number, optional, 页大小

响应数据:
  records:
    - id: number, 字典数据ID
      dictLabel: string, 字典标签
      dictValue: string, 字典值
      sort: number, 排序
      status: number, 状态
      createTime: string, 创建时间
```

### 5.3 系统配置接口

#### 5.3.1 系统配置列表
```yaml
接口路径: GET /api/v1/system/configs
接口描述: 获取系统配置列表
请求参数:
  category: string, optional, 配置分类

响应数据:
  - configKey: string, 配置键
    configValue: string, 配置值
    configName: string, 配置名称
    configType: string, 配置类型
    category: string, 配置分类
    description: string, 描述
    updateTime: string, 更新时间
```

#### 5.3.2 更新系统配置
```yaml
接口路径: PUT /api/v1/system/configs/{key}
接口描述: 更新系统配置
路径参数:
  key: string, 配置键
请求参数:
  configValue: string, required, 配置值

响应数据:
  message: string, 更新成功消息
```

## 6. 工作流模块接口设计

### 6.1 流程定义接口

#### 6.1.1 流程定义列表
```yaml
接口路径: GET /api/v1/workflow/definitions
接口描述: 获取流程定义列表
请求参数:
  page: number, optional, 页码
  size: number, optional, 页大小
  keyword: string, optional, 搜索关键词
  category: string, optional, 流程分类

响应数据:
  records:
    - id: string, 流程定义ID
      key: string, 流程键
      name: string, 流程名称
      category: string, 流程分类
      version: number, 版本号
      description: string, 描述
      status: string, 状态
      deployTime: string, 部署时间
```

#### 6.1.2 部署流程
```yaml
接口路径: POST /api/v1/workflow/definitions/deploy
接口描述: 部署流程定义
请求参数:
  Content-Type: multipart/form-data
  file: file, required, BPMN文件
  name: string, required, 流程名称
  category: string, optional, 流程分类

响应数据:
  deploymentId: string, 部署ID
  processDefinitionId: string, 流程定义ID
  message: string, 部署成功消息
```

### 6.2 流程实例接口

#### 6.2.1 启动流程
```yaml
接口路径: POST /api/v1/workflow/instances/start
接口描述: 启动流程实例
请求参数:
  processDefinitionKey: string, required, 流程定义键
  businessKey: string, optional, 业务键
  variables: object, optional, 流程变量

响应数据:
  processInstanceId: string, 流程实例ID
  businessKey: string, 业务键
  message: string, 启动成功消息
```

#### 6.2.2 流程实例列表
```yaml
接口路径: GET /api/v1/workflow/instances
接口描述: 获取流程实例列表
请求参数:
  page: number, optional, 页码
  size: number, optional, 页大小
  processDefinitionKey: string, optional, 流程定义键
  businessKey: string, optional, 业务键
  status: string, optional, 状态

响应数据:
  records:
    - id: string, 流程实例ID
      processDefinitionName: string, 流程定义名称
      businessKey: string, 业务键
      startUserId: string, 发起人ID
      startUserName: string, 发起人姓名
      startTime: string, 开始时间
      endTime: string, 结束时间
      status: string, 状态
```

### 6.3 任务管理接口

#### 6.3.1 待办任务列表
```yaml
接口路径: GET /api/v1/workflow/tasks/todo
接口描述: 获取当前用户待办任务列表
请求参数:
  page: number, optional, 页码
  size: number, optional, 页大小
  processDefinitionKey: string, optional, 流程定义键

响应数据:
  records:
    - id: string, 任务ID
      name: string, 任务名称
      processInstanceId: string, 流程实例ID
      processDefinitionName: string, 流程定义名称
      businessKey: string, 业务键
      assignee: string, 处理人
      createTime: string, 创建时间
      dueDate: string, 到期时间
      priority: number, 优先级
```

#### 6.3.2 完成任务
```yaml
接口路径: POST /api/v1/workflow/tasks/{taskId}/complete
接口描述: 完成任务
路径参数:
  taskId: string, 任务ID
请求参数:
  variables: object, optional, 任务变量
  comment: string, optional, 审批意见

响应数据:
  message: string, 完成成功消息
```

## 7. 消息中心模块接口设计

### 7.1 消息模板接口

#### 7.1.1 消息模板列表
```yaml
接口路径: GET /api/v1/message/templates
接口描述: 获取消息模板列表
请求参数:
  page: number, optional, 页码
  size: number, optional, 页大小
  type: string, optional, 模板类型(sms/email/push)

响应数据:
  records:
    - id: number, 模板ID
      templateCode: string, 模板编码
      templateName: string, 模板名称
      templateType: string, 模板类型
      title: string, 标题
      content: string, 内容
      status: number, 状态
      createTime: string, 创建时间
```

#### 7.1.2 创建消息模板
```yaml
接口路径: POST /api/v1/message/templates
接口描述: 创建消息模板
请求参数:
  templateCode: string, required, 模板编码
  templateName: string, required, 模板名称
  templateType: string, required, 模板类型
  title: string, optional, 标题
  content: string, required, 内容

响应数据:
  id: number, 模板ID
  message: string, 创建成功消息
```

### 7.2 消息发送接口

#### 7.2.1 发送消息
```yaml
接口路径: POST /api/v1/message/send
接口描述: 发送消息
请求参数:
  templateCode: string, required, 模板编码
  receivers: array, required, 接收人列表
  variables: object, optional, 模板变量
  sendTime: string, optional, 发送时间(定时发送)

响应数据:
  messageId: string, 消息ID
  message: string, 发送成功消息
```

#### 7.2.2 消息发送记录
```yaml
接口路径: GET /api/v1/message/records
接口描述: 获取消息发送记录
请求参数:
  page: number, optional, 页码
  size: number, optional, 页大小
  templateCode: string, optional, 模板编码
  status: string, optional, 发送状态
  startTime: string, optional, 开始时间
  endTime: string, optional, 结束时间

响应数据:
  records:
    - id: string, 记录ID
      templateName: string, 模板名称
      receiver: string, 接收人
      title: string, 标题
      content: string, 内容
      status: string, 发送状态
      sendTime: string, 发送时间
      errorMsg: string, 错误信息
```

### 7.3 文件存储接口

#### 7.3.1 文件上传
```yaml
接口路径: POST /api/v1/message/files/upload
接口描述: 上传文件
请求参数:
  Content-Type: multipart/form-data
  file: file, required, 文件
  category: string, optional, 文件分类

响应数据:
  fileId: string, 文件ID
  fileName: string, 文件名
  fileUrl: string, 文件URL
  fileSize: number, 文件大小
  message: string, 上传成功消息
```

#### 7.3.2 文件列表
```yaml
接口路径: GET /api/v1/message/files
接口描述: 获取文件列表
请求参数:
  page: number, optional, 页码
  size: number, optional, 页大小
  category: string, optional, 文件分类
  keyword: string, optional, 搜索关键词

响应数据:
  records:
    - id: string, 文件ID
      fileName: string, 文件名
      fileUrl: string, 文件URL
      fileSize: number, 文件大小
      fileType: string, 文件类型
      category: string, 文件分类
      uploadUser: string, 上传人
      uploadTime: string, 上传时间
```

---

**© 2024 HandThing. All rights reserved.**
