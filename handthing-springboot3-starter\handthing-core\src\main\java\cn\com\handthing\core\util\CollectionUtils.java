package cn.com.handthing.core.util;

import java.util.Collection;
import java.util.Map;

/**
 * 集合工具类
 *
 * <AUTHOR>
 * @since V1.0.0
 */
public final class CollectionUtils {

    private CollectionUtils() {}

    /**
     * 检查集合是否为空。
     * @param coll 待检查的集合
     * @return 如果为 null 或 size() == 0 则返回 true
     */
    public static boolean isEmpty(final Collection<?> coll) {
        return coll == null || coll.isEmpty();
    }
    
    /**
     * 检查集合是否不为空。
     * @param coll 待检查的集合
     * @return 如果不为空则返回 true
     */
    public static boolean isNotEmpty(final Collection<?> coll) {
        return !isEmpty(coll);
    }

    /**
     * 检查 Map 是否为空。
     * @param map 待检查的 Map
     * @return 如果为 null 或 size() == 0 则返回 true
     */
    public static boolean isEmpty(final Map<?, ?> map) {
        return map == null || map.isEmpty();
    }
    
    /**
     * 检查 Map 是否不为空。
     * @param map 待检查的 Map
     * @return 如果不为空则返回 true
     */
    public static boolean isNotEmpty(final Map<?, ?> map) {
        return !isEmpty(map);
    }
}