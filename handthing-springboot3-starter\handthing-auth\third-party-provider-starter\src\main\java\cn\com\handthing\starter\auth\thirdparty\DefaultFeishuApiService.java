package cn.com.handthing.starter.auth.thirdparty;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * 默认飞书API服务实现
 * <p>
 * 提供飞书API调用的默认实现，包括获取访问令牌、用户信息等功能。
 * 使用WebClient进行HTTP调用，支持超时和错误处理。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnMissingBean(FeishuApiService.class)
public class DefaultFeishuApiService implements FeishuApiService {

    private final WebClient webClient;
    private final ObjectMapper objectMapper;

    /**
     * 飞书API基础URL
     */
    private static final String FEISHU_API_BASE_URL = "https://open.feishu.cn";

    /**
     * 获取应用访问令牌URL
     */
    private static final String GET_APP_TOKEN_URL = FEISHU_API_BASE_URL + "/open-apis/auth/v3/app_access_token/internal";

    /**
     * 获取用户访问令牌URL
     */
    private static final String GET_USER_TOKEN_URL = FEISHU_API_BASE_URL + "/open-apis/authen/v1/access_token";

    /**
     * 获取用户信息URL
     */
    private static final String GET_USER_INFO_URL = FEISHU_API_BASE_URL + "/open-apis/authen/v1/user_info";

    /**
     * 刷新用户访问令牌URL
     */
    private static final String REFRESH_USER_TOKEN_URL = FEISHU_API_BASE_URL + "/open-apis/authen/v1/refresh_access_token";

    /**
     * 默认构造函数
     */
    public DefaultFeishuApiService() {
        this.webClient = WebClient.builder()
                .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(1024 * 1024))
                .build();
        this.objectMapper = new ObjectMapper();
    }

    @Override
    public FeishuAppTokenResult getAppAccessToken(String appId, String appSecret) {
        try {
            log.debug("Getting app access token for appId: {}", appId);

            Map<String, String> requestBody = Map.of(
                    "app_id", appId,
                    "app_secret", appSecret
            );

            String response = webClient.post()
                    .uri(GET_APP_TOKEN_URL)
                    .header("Content-Type", "application/json")
                    .bodyValue(requestBody)
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(10))
                    .block();

            JsonNode jsonNode = objectMapper.readTree(response);
            int code = jsonNode.get("code").asInt();

            if (code == 0) {
                String appAccessToken = jsonNode.get("app_access_token").asText();
                long expire = jsonNode.get("expire").asLong();
                String tenantKey = jsonNode.has("tenant_key") ? jsonNode.get("tenant_key").asText() : null;
                
                log.debug("App access token obtained successfully for appId: {}", appId);
                return FeishuAppTokenResult.success(appAccessToken, expire, tenantKey);
            } else {
                String msg = jsonNode.get("msg").asText();
                log.warn("Failed to get app access token for appId: {}, code: {}, msg: {}", appId, code, msg);
                return FeishuAppTokenResult.failure(String.valueOf(code), msg);
            }

        } catch (Exception e) {
            log.error("Error getting app access token for appId: {}", appId, e);
            return FeishuAppTokenResult.failure("NETWORK_ERROR", "网络请求失败: " + e.getMessage());
        }
    }

    @Override
    public FeishuUserTokenResult getUserAccessToken(String appAccessToken, String code) {
        try {
            log.debug("Getting user access token by code");

            Map<String, String> requestBody = Map.of(
                    "grant_type", "authorization_code",
                    "code", code
            );

            String response = webClient.post()
                    .uri(GET_USER_TOKEN_URL)
                    .header("Authorization", "Bearer " + appAccessToken)
                    .header("Content-Type", "application/json")
                    .bodyValue(requestBody)
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(10))
                    .block();

            JsonNode jsonNode = objectMapper.readTree(response);
            int responseCode = jsonNode.get("code").asInt();

            if (responseCode == 0) {
                JsonNode data = jsonNode.get("data");
                String accessToken = data.get("access_token").asText();
                String refreshToken = data.get("refresh_token").asText();
                long expiresIn = data.get("expires_in").asLong();
                String tokenType = data.get("token_type").asText();
                String scope = data.has("scope") ? data.get("scope").asText() : null;

                log.debug("User access token obtained successfully");
                return FeishuUserTokenResult.success(accessToken, refreshToken, expiresIn, tokenType, scope);
            } else {
                String msg = jsonNode.get("msg").asText();
                log.warn("Failed to get user access token, code: {}, msg: {}", responseCode, msg);
                return FeishuUserTokenResult.failure(String.valueOf(responseCode), msg);
            }

        } catch (Exception e) {
            log.error("Error getting user access token", e);
            return FeishuUserTokenResult.failure("NETWORK_ERROR", "网络请求失败: " + e.getMessage());
        }
    }

    @Override
    public FeishuUserInfoResult getUserInfo(String userAccessToken) {
        try {
            log.debug("Getting user info");

            String response = webClient.get()
                    .uri(GET_USER_INFO_URL)
                    .header("Authorization", "Bearer " + userAccessToken)
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(10))
                    .block();

            JsonNode jsonNode = objectMapper.readTree(response);
            int code = jsonNode.get("code").asInt();

            if (code == 0) {
                JsonNode data = jsonNode.get("data");
                FeishuUserInfoResult result = FeishuUserInfoResult.success();
                
                result.setUserId(getTextValue(data, "user_id"));
                result.setUnionId(getTextValue(data, "union_id"));
                result.setOpenId(getTextValue(data, "open_id"));
                result.setName(getTextValue(data, "name"));
                result.setEnName(getTextValue(data, "en_name"));
                result.setNickname(getTextValue(data, "nickname"));
                result.setEmail(getTextValue(data, "email"));
                result.setMobile(getTextValue(data, "mobile"));
                result.setAvatar(getTextValue(data, "avatar_url"));
                result.setStatus(getTextValue(data, "status"));
                result.setEmployeeType(getTextValue(data, "employee_type"));
                result.setTenantKey(getTextValue(data, "tenant_key"));

                // 处理部门信息
                if (data.has("department_ids")) {
                    JsonNode deptNode = data.get("department_ids");
                    if (deptNode.isArray()) {
                        String[] deptIds = new String[deptNode.size()];
                        for (int i = 0; i < deptNode.size(); i++) {
                            deptIds[i] = deptNode.get(i).asText();
                        }
                        result.setDepartmentIds(deptIds);
                    }
                }

                // 处理自定义属性
                if (data.has("custom_attrs")) {
                    JsonNode customNode = data.get("custom_attrs");
                    Map<String, Object> customAttrs = objectMapper.convertValue(customNode, Map.class);
                    result.setCustomAttrs(customAttrs);
                }

                log.debug("User info obtained successfully");
                return result;
            } else {
                String msg = jsonNode.get("msg").asText();
                log.warn("Failed to get user info, code: {}, msg: {}", code, msg);
                return FeishuUserInfoResult.failure(String.valueOf(code), msg);
            }

        } catch (Exception e) {
            log.error("Error getting user info", e);
            return FeishuUserInfoResult.failure("NETWORK_ERROR", "网络请求失败: " + e.getMessage());
        }
    }

    @Override
    public FeishuUserTokenResult refreshUserAccessToken(String appAccessToken, String refreshToken) {
        try {
            log.debug("Refreshing user access token");

            Map<String, String> requestBody = Map.of(
                    "grant_type", "refresh_token",
                    "refresh_token", refreshToken
            );

            String response = webClient.post()
                    .uri(REFRESH_USER_TOKEN_URL)
                    .header("Authorization", "Bearer " + appAccessToken)
                    .header("Content-Type", "application/json")
                    .bodyValue(requestBody)
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(10))
                    .block();

            JsonNode jsonNode = objectMapper.readTree(response);
            int code = jsonNode.get("code").asInt();

            if (code == 0) {
                JsonNode data = jsonNode.get("data");
                String accessToken = data.get("access_token").asText();
                String newRefreshToken = data.get("refresh_token").asText();
                long expiresIn = data.get("expires_in").asLong();
                String tokenType = data.get("token_type").asText();
                String scope = data.has("scope") ? data.get("scope").asText() : null;

                log.debug("User access token refreshed successfully");
                return FeishuUserTokenResult.success(accessToken, newRefreshToken, expiresIn, tokenType, scope);
            } else {
                String msg = jsonNode.get("msg").asText();
                log.warn("Failed to refresh user access token, code: {}, msg: {}", code, msg);
                return FeishuUserTokenResult.failure(String.valueOf(code), msg);
            }

        } catch (Exception e) {
            log.error("Error refreshing user access token", e);
            return FeishuUserTokenResult.failure("NETWORK_ERROR", "网络请求失败: " + e.getMessage());
        }
    }

    @Override
    public boolean validateConfig(String appId, String appSecret) {
        try {
            log.debug("Validating feishu config for appId: {}", appId);

            FeishuAppTokenResult tokenResult = getAppAccessToken(appId, appSecret);
            if (!tokenResult.isSuccess()) {
                log.warn("Invalid feishu config: failed to get app access token");
                return false;
            }

            log.debug("Feishu config validation successful");
            return true;

        } catch (Exception e) {
            log.error("Error validating feishu config", e);
            return false;
        }
    }

    /**
     * 安全地获取JSON节点的文本值
     *
     * @param jsonNode JSON节点
     * @param fieldName 字段名
     * @return 文本值，如果不存在返回null
     */
    private String getTextValue(JsonNode jsonNode, String fieldName) {
        return jsonNode.has(fieldName) ? jsonNode.get(fieldName).asText() : null;
    }

    /**
     * 创建模拟的飞书API服务（用于测试）
     *
     * @return 模拟的API服务
     */
    public static FeishuApiService createMockService() {
        return new FeishuApiService() {
            @Override
            public FeishuAppTokenResult getAppAccessToken(String appId, String appSecret) {
                // 模拟成功响应
                if ("test_app_id".equals(appId) && "test_app_secret".equals(appSecret)) {
                    return FeishuAppTokenResult.success("mock_app_token_" + System.currentTimeMillis(), 7200L, "test_tenant_key");
                }
                return FeishuAppTokenResult.failure("10013", "invalid app_id or app_secret");
            }

            @Override
            public FeishuUserTokenResult getUserAccessToken(String appAccessToken, String code) {
                // 模拟成功响应
                if (appAccessToken.startsWith("mock_app_token") && "test_code".equals(code)) {
                    return FeishuUserTokenResult.success("mock_user_token_" + System.currentTimeMillis(), 
                            "mock_refresh_token_" + System.currentTimeMillis(), 7200L, "Bearer", "user:email");
                }
                return FeishuUserTokenResult.failure("10014", "invalid code");
            }

            @Override
            public FeishuUserInfoResult getUserInfo(String userAccessToken) {
                // 模拟成功响应
                if (userAccessToken.startsWith("mock_user_token")) {
                    FeishuUserInfoResult result = FeishuUserInfoResult.success();
                    result.setUserId("test_user_id");
                    result.setUnionId("test_union_id");
                    result.setOpenId("test_open_id");
                    result.setName("测试用户");
                    result.setEnName("Test User");
                    result.setNickname("测试");
                    result.setEmail("<EMAIL>");
                    result.setMobile("13800138000");
                    result.setAvatar("http://example.com/avatar.jpg");
                    result.setStatus("1");
                    result.setEmployeeType("1");
                    result.setTenantKey("test_tenant_key");
                    result.setDepartmentIds(new String[]{"dept_001", "dept_002"});
                    
                    Map<String, Object> customAttrs = new HashMap<>();
                    customAttrs.put("工号", "F001");
                    result.setCustomAttrs(customAttrs);
                    
                    return result;
                }
                return FeishuUserInfoResult.failure("99991663", "invalid user access token");
            }

            @Override
            public FeishuUserTokenResult refreshUserAccessToken(String appAccessToken, String refreshToken) {
                // 模拟成功响应
                if (appAccessToken.startsWith("mock_app_token") && refreshToken.startsWith("mock_refresh_token")) {
                    return FeishuUserTokenResult.success("mock_user_token_" + System.currentTimeMillis(), 
                            refreshToken, 7200L, "Bearer", "user:email");
                }
                return FeishuUserTokenResult.failure("10015", "invalid refresh token");
            }

            @Override
            public boolean validateConfig(String appId, String appSecret) {
                return "test_app_id".equals(appId) && "test_app_secret".equals(appSecret);
            }
        };
    }
}
