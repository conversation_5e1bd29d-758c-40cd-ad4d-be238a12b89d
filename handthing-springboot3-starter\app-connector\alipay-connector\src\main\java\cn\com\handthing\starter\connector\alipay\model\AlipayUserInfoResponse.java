package cn.com.handthing.starter.connector.alipay.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 支付宝用户信息响应模型
 * <p>
 * 支付宝获取用户信息API的响应数据结构
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class AlipayUserInfoResponse {

    /**
     * 用户信息分享响应
     */
    @JsonProperty("alipay_user_info_share_response")
    private UserInfo alipayUserInfoShareResponse;

    /**
     * 错误响应
     */
    @JsonProperty("error_response")
    private ErrorResponse errorResponse;

    /**
     * 签名
     */
    @JsonProperty("sign")
    private String sign;

    /**
     * 检查响应是否成功
     *
     * @return 如果成功返回true，否则返回false
     */
    public boolean isSuccess() {
        return alipayUserInfoShareResponse != null && 
               alipayUserInfoShareResponse.getUserId() != null;
    }

    /**
     * 获取错误描述
     *
     * @return 错误描述
     */
    public String getErrorDescription() {
        if (isSuccess()) {
            return "Success";
        }
        if (errorResponse != null) {
            return String.format("Error %s: %s", errorResponse.getCode(), errorResponse.getMsg());
        }
        return "Unknown error";
    }

    /**
     * 用户信息
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class UserInfo {

        /**
         * 用户ID
         */
        @JsonProperty("user_id")
        private String userId;

        /**
         * 用户昵称
         */
        @JsonProperty("nick_name")
        private String nickName;

        /**
         * 用户头像
         */
        @JsonProperty("avatar")
        private String avatar;

        /**
         * 用户性别（M-男，F-女）
         */
        @JsonProperty("gender")
        private String gender;

        /**
         * 用户所在省份
         */
        @JsonProperty("province")
        private String province;

        /**
         * 用户所在城市
         */
        @JsonProperty("city")
        private String city;

        /**
         * 用户类型（1-个人用户，2-企业用户）
         */
        @JsonProperty("user_type")
        private String userType;

        /**
         * 用户状态（Q-快速注册用户，T-已认证用户，B-被冻结账户，W-已注册，待激活的账户）
         */
        @JsonProperty("user_status")
        private String userStatus;

        /**
         * 是否通过实名认证（T-是，F-否）
         */
        @JsonProperty("is_certified")
        private String isCertified;

        /**
         * 是否是学生（T-是，F-否）
         */
        @JsonProperty("is_student_certified")
        private String isStudentCertified;

        /**
         * 获取显示名称
         *
         * @return 显示名称
         */
        public String getDisplayName() {
            if (nickName != null && !nickName.trim().isEmpty()) {
                return nickName;
            }
            return userId;
        }

        /**
         * 获取性别描述
         *
         * @return 性别描述
         */
        public String getGenderDescription() {
            if (gender == null) {
                return "未知";
            }
            switch (gender.toUpperCase()) {
                case "M":
                    return "男";
                case "F":
                    return "女";
                default:
                    return "未知";
            }
        }

        /**
         * 获取用户类型描述
         *
         * @return 用户类型描述
         */
        public String getUserTypeDescription() {
            if (userType == null) {
                return "未知";
            }
            switch (userType) {
                case "1":
                    return "个人用户";
                case "2":
                    return "企业用户";
                default:
                    return "未知类型";
            }
        }

        /**
         * 获取用户状态描述
         *
         * @return 用户状态描述
         */
        public String getUserStatusDescription() {
            if (userStatus == null) {
                return "未知";
            }
            switch (userStatus.toUpperCase()) {
                case "Q":
                    return "快速注册用户";
                case "T":
                    return "已认证用户";
                case "B":
                    return "被冻结账户";
                case "W":
                    return "已注册，待激活";
                default:
                    return "未知状态";
            }
        }

        /**
         * 检查是否已实名认证
         *
         * @return 如果已实名认证返回true，否则返回false
         */
        public boolean isCertified() {
            return "T".equalsIgnoreCase(isCertified);
        }

        /**
         * 检查是否为学生
         *
         * @return 如果是学生返回true，否则返回false
         */
        public boolean isStudent() {
            return "T".equalsIgnoreCase(isStudentCertified);
        }
    }

    /**
     * 错误响应数据
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ErrorResponse {

        /**
         * 错误码
         */
        @JsonProperty("code")
        private String code;

        /**
         * 错误信息
         */
        @JsonProperty("msg")
        private String msg;

        /**
         * 子错误码
         */
        @JsonProperty("sub_code")
        private String subCode;

        /**
         * 子错误信息
         */
        @JsonProperty("sub_msg")
        private String subMsg;
    }
}
