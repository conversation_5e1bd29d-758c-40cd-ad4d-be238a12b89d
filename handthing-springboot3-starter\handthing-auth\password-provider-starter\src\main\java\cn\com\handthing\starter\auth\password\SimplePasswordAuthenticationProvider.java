package cn.com.handthing.starter.auth.password;

import cn.com.handthing.starter.auth.core.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 简单密码认证提供者
 * <p>
 * 实现基于用户名密码的认证逻辑，提供基础的密码认证功能。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SimplePasswordAuthenticationProvider implements AuthenticationProvider {

    private final JwtTokenProvider jwtTokenProvider;

    @Override
    public GrantType getSupportedGrantType() {
        return GrantType.PASSWORD;
    }

    @Override
    public boolean supports(AuthenticationRequest request) {
        return request.getGrantType() == GrantType.PASSWORD;
    }

    @Override
    public AuthenticationResponse authenticate(AuthenticationRequest request) throws AuthenticationException {
        log.debug("Starting password authentication");

        try {
            // 从请求中获取用户名和密码
            String username = (String) request.getExtraParam("username");
            String password = (String) request.getExtraParam("password");

            if (username == null || password == null) {
                throw new InvalidCredentialsException("用户名或密码不能为空");
            }

            // 简单的用户验证逻辑（实际应用中应该查询数据库）
            if (!isValidUser(username, password)) {
                throw new InvalidCredentialsException("用户名或密码错误");
            }

            // 创建用户信息
            UserInfo userInfo = createUserInfo(username);

            // 生成JWT令牌
            JwtClaims claims = JwtClaims.builder()
                    .subject(userInfo.getUserId())
                    .username(userInfo.getUsername())
                    .nickname(userInfo.getNickname())
                    .roles(userInfo.getRoles())
                    .permissions(userInfo.getPermissions())
                    .grantType(GrantType.PASSWORD.getCode())
                    .clientId(request.getClientId())
                    .scope(request.getScope())
                    .ipAddress(request.getIpAddress())
                    .userAgent(request.getUserAgent())
                    .build();

            String accessToken = jwtTokenProvider.generateAccessToken(claims);
            String refreshToken = jwtTokenProvider.generateRefreshToken(claims);

            // 创建成功响应
            AuthenticationResponse response = AuthenticationResponse.success(accessToken, refreshToken, 7200L);
            response.setUserId(userInfo.getUserId());
            response.setUsername(userInfo.getUsername());

            log.info("Password authentication successful for user: {}", username);
            return response;

        } catch (AuthenticationException e) {
            log.warn("Password authentication failed: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Password authentication error", e);
            throw new AuthenticationException("AUTHENTICATION_ERROR", "认证失败", e);
        }
    }

    @Override
    public boolean validateToken(String token) {
        return jwtTokenProvider.validateToken(token);
    }

    @Override
    public AuthenticationResponse refreshToken(String refreshToken) throws AuthenticationException {
        try {
            JwtClaims claims = jwtTokenProvider.parseToken(refreshToken);
            
            if (!"refresh_token".equals(claims.getTokenType())) {
                throw new AuthenticationException("INVALID_REFRESH_TOKEN", "无效的刷新令牌");
            }

            // 生成新的访问令牌
            String newAccessToken = jwtTokenProvider.generateAccessToken(claims);
            
            return AuthenticationResponse.success(newAccessToken, refreshToken, 7200L);

        } catch (Exception e) {
            log.error("Refresh token error", e);
            throw new AuthenticationException("REFRESH_TOKEN_ERROR", "刷新令牌失败", e);
        }
    }

    @Override
    public String getProviderName() {
        return "Simple Password Provider";
    }

    @Override
    public boolean isAvailable() {
        return true;
    }

    @Override
    public int getPriority() {
        return 0;
    }

    /**
     * 验证用户凭证
     *
     * @param username 用户名
     * @param password 密码
     * @return 如果验证通过返回true，否则返回false
     */
    private boolean isValidUser(String username, String password) {
        // 简单的硬编码验证逻辑，实际应用中应该查询数据库
        return ("admin".equals(username) && "admin123".equals(password)) ||
               ("user".equals(username) && "user123".equals(password)) ||
               ("test".equals(username) && "test123".equals(password));
    }

    /**
     * 创建用户信息
     *
     * @param username 用户名
     * @return 用户信息
     */
    private UserInfo createUserInfo(String username) {
        UserInfo.UserInfoBuilder builder = UserInfo.builder()
                .username(username)
                .nickname(username)
                .enabled(true)
                .accountNonExpired(true)
                .accountNonLocked(true)
                .credentialsNonExpired(true)
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now());

        // 根据用户名设置不同的角色和权限
        switch (username) {
            case "admin":
                builder.userId("1")
                       .email("<EMAIL>")
                       .roles(List.of("ADMIN"))
                       .permissions(List.of("*"));
                break;
            case "user":
                builder.userId("2")
                       .email("<EMAIL>")
                       .roles(List.of("USER"))
                       .permissions(List.of("READ"));
                break;
            case "test":
                builder.userId("3")
                       .email("<EMAIL>")
                       .roles(List.of("USER"))
                       .permissions(List.of("READ", "WRITE"));
                break;
            default:
                builder.userId(String.valueOf(username.hashCode()))
                       .roles(List.of("USER"))
                       .permissions(List.of("READ"));
                break;
        }

        return builder.build();
    }
}
