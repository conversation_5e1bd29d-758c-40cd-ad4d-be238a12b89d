package cn.com.handthing.starter.crypto.core;

import cn.com.handthing.starter.crypto.properties.CryptoProperties.KeyStoreConfig;
import cn.com.handthing.starter.crypto.exception.CryptoException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import uk.org.webcompere.systemstubs.environment.EnvironmentVariables;
import uk.org.webcompere.systemstubs.jupiter.SystemStub;
import uk.org.webcompere.systemstubs.jupiter.SystemStubsExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

/**
 * EnvironmentKeyProvider 的单元测试。
 * 使用 System-Stubs 库来模拟环境变量。
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@ExtendWith(SystemStubsExtension.class)
@DisplayName("EnvironmentKeyProvider Tests")
class EnvironmentKeyProviderTest {

    @SystemStub
    private EnvironmentVariables environmentVariables;

    private static final String ENV_VAR_NAME = "MY_TEST_CRYPTO_KEY";
    private static final String VALID_BASE64_KEY = "AAECAwQFBgcICQoLDA0ODw==";

    @Test
    @DisplayName("应能成功从环境变量加载密钥")
    void shouldLoadKeyFromEnvironmentVariable() throws Exception {
        environmentVariables.set(ENV_VAR_NAME, VALID_BASE64_KEY);

        KeyStoreConfig config = new KeyStoreConfig();
        config.setEnvVariable(ENV_VAR_NAME);

        EnvironmentKeyProvider provider = new EnvironmentKeyProvider(config);
        provider.checkReady();
        assertThat(provider.getKeyBytes()).hasSize(16);
    }

    @Test
    @DisplayName("当环境变量未设置时应抛出异常")
    void shouldThrowExceptionWhenEnvVarIsNotSet() {
        KeyStoreConfig config = new KeyStoreConfig();
        config.setEnvVariable("NON_EXISTENT_VAR");

        assertThatThrownBy(() -> new EnvironmentKeyProvider(config))
                .isInstanceOf(CryptoException.class)
                .hasMessageContaining("is not set");
    }

    @Test
    @DisplayName("当环境变量名称为空时应抛出异常")
    void shouldThrowExceptionWhenEnvVarNameIsEmpty() {
        KeyStoreConfig configNull = new KeyStoreConfig();
        configNull.setEnvVariable(null);

        KeyStoreConfig configEmpty = new KeyStoreConfig();
        configEmpty.setEnvVariable("");

        assertThatThrownBy(() -> new EnvironmentKeyProvider(configNull))
                .isInstanceOf(IllegalArgumentException.class);
        assertThatThrownBy(() -> new EnvironmentKeyProvider(configEmpty))
                .isInstanceOf(IllegalArgumentException.class);
    }
}