package cn.com.handthing.starter.httpclient.internal;

import cn.com.handthing.starter.httpclient.HandthingHttpClient;
import cn.com.handthing.starter.httpclient.RequestExecutor;
import cn.com.handthing.starter.httpclient.crypto.RequestEncryptor;
import cn.com.handthing.starter.httpclient.crypto.ResponseDecryptor;
import org.springframework.http.HttpMethod;
import org.springframework.lang.NonNull;

/**
 * HandthingHttpClient 接口的 RestTemplate 实现。
 * 
 * <AUTHOR>
 * @since V1.0.0
 */
public final class RestTemplateHandthingHttpClient implements HandthingHttpClient {

    private final EndpointConfigResolver configResolver;
    private final RequestEncryptor<?> requestEncryptor;
    private final ResponseDecryptor responseDecryptor;

    public RestTemplateHandthingHttpClient(
            EndpointConfigResolver configResolver,
            RequestEncryptor<?> requestEncryptor,
            ResponseDecryptor responseDecryptor) {
        this.configResolver = configResolver;
        this.requestEncryptor = requestEncryptor;
        this.responseDecryptor = responseDecryptor;
    }

    @Override
    @NonNull
    public RequestExecutor get(@NonNull String uri, Object... uriVariables) {
        return createRequestExecutor(HttpMethod.GET, uri, uriVariables);
    }

    @Override
    @NonNull
    public RequestExecutor post(@NonNull String uri, Object... uriVariables) {
        return createRequestExecutor(HttpMethod.POST, uri, uriVariables);
    }

    @Override
    @NonNull
    public RequestExecutor put(@NonNull String uri, Object... uriVariables) {
        return createRequestExecutor(HttpMethod.PUT, uri, uriVariables);
    }

    @Override
    @NonNull
    public RequestExecutor delete(@NonNull String uri, Object... uriVariables) {
        return createRequestExecutor(HttpMethod.DELETE, uri, uriVariables);
    }

    private RequestExecutor createRequestExecutor(HttpMethod method, String uri, Object... uriVariables) {
        return new RestTemplateRequestExecutor(
            configResolver, requestEncryptor, responseDecryptor,
            method, uri, uriVariables
        );
    }
}
