package cn.com.handthing.starter.cache;

import cn.com.handthing.starter.cache.config.CacheProperties;
import org.junit.jupiter.api.Test;
import org.springframework.boot.autoconfigure.AutoConfigurations;
import org.springframework.boot.test.context.runner.ApplicationContextRunner;
import org.springframework.cache.CacheManager;
import org.springframework.cache.concurrent.ConcurrentMapCacheManager;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * 条件装配测试
 * 验证缓存自动配置的条件装配是否正常工作
 *
 * <AUTHOR>
 * @since V1.0.0
 */
public class ConditionalConfigurationTest {

    private final ApplicationContextRunner contextRunner = new ApplicationContextRunner()
            .withConfiguration(AutoConfigurations.of(CacheAutoConfiguration.class));

    @Test
    public void testCacheServiceCreatedWhenCacheManagerPresent() {
        contextRunner
                .withBean(CacheManager.class, () -> new ConcurrentMapCacheManager())
                .run(context -> {
                    assertThat(context).hasSingleBean(CacheService.class);
                    assertThat(context).hasSingleBean(CacheProperties.class);
                });
    }

    @Test
    public void testCacheServiceNotCreatedWhenCacheManagerAbsent() {
        contextRunner
                .run(context -> {
                    assertThat(context).doesNotHaveBean(CacheService.class);
                    // CacheProperties should still be created
                    assertThat(context).hasSingleBean(CacheProperties.class);
                });
    }

    @Test
    public void testCacheDisabledByProperty() {
        contextRunner
                .withBean(CacheManager.class, () -> new ConcurrentMapCacheManager())
                .withPropertyValues("handthing.cache.enabled=false")
                .run(context -> {
                    assertThat(context).doesNotHaveBean(CacheService.class);
                    assertThat(context).doesNotHaveBean(CacheProperties.class);
                });
    }

    @Test
    public void testCacheEnabledByDefault() {
        contextRunner
                .withBean(CacheManager.class, () -> new ConcurrentMapCacheManager())
                .run(context -> {
                    assertThat(context).hasSingleBean(CacheService.class);
                    assertThat(context).hasSingleBean(CacheProperties.class);
                });
    }
}