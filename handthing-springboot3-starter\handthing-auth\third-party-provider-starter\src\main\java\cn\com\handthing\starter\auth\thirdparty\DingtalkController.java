package cn.com.handthing.starter.auth.thirdparty;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 钉钉控制器
 * <p>
 * 提供钉钉认证相关的REST API端点，包括授权URL生成、配置验证等功能。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@RestController
@RequestMapping("${handthing.auth.web.auth-path:/auth}/dingtalk")
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "handthing.auth.third-party.dingtalk", name = "endpoints-enabled", havingValue = "true", matchIfMissing = true)
public class DingtalkController {

    private final DingtalkApiService dingtalkApiService;
    private final DingtalkUserService dingtalkUserService;

    /**
     * 生成钉钉授权URL
     *
     * @param request 授权请求
     * @return 授权URL
     */
    @PostMapping("/auth-url")
    public ResponseEntity<Map<String, Object>> generateAuthUrl(@RequestBody Map<String, Object> request) {
        try {
            String appKey = (String) request.get("app_key");
            String redirectUri = (String) request.get("redirect_uri");
            String state = (String) request.getOrDefault("state", "");
            String authType = (String) request.getOrDefault("auth_type", "web");

            // 验证参数
            if (appKey == null || appKey.trim().isEmpty()) {
                Map<String, Object> response = buildErrorResponse("INVALID_APP_KEY", "应用Key不能为空");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
            }

            if (redirectUri == null || redirectUri.trim().isEmpty()) {
                Map<String, Object> response = buildErrorResponse("INVALID_REDIRECT_URI", "重定向URI不能为空");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
            }

            // 生成授权URL
            String authUrl = buildAuthUrl(appKey, redirectUri, state, authType);

            Map<String, Object> response = buildSuccessResponse("授权URL生成成功");
            response.put("auth_url", authUrl);
            response.put("app_key", appKey);
            response.put("auth_type", authType);

            log.info("Generated dingtalk auth URL for appKey: {}", appKey);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Generate auth URL error", e);
            Map<String, Object> response = buildErrorResponse("AUTH_URL_ERROR", "生成授权URL失败");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 验证钉钉配置
     *
     * @param request 验证请求
     * @return 验证结果
     */
    @PostMapping("/validate-config")
    public ResponseEntity<Map<String, Object>> validateConfig(@RequestBody Map<String, Object> request) {
        try {
            String appKey = (String) request.get("app_key");
            String appSecret = (String) request.get("app_secret");

            // 验证参数
            if (appKey == null || appKey.trim().isEmpty()) {
                Map<String, Object> response = buildErrorResponse("INVALID_APP_KEY", "应用Key不能为空");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
            }

            if (appSecret == null || appSecret.trim().isEmpty()) {
                Map<String, Object> response = buildErrorResponse("INVALID_APP_SECRET", "应用密钥不能为空");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
            }

            // 验证配置
            boolean isValid = dingtalkApiService.validateConfig(appKey, appSecret);

            if (isValid) {
                Map<String, Object> response = buildSuccessResponse("钉钉配置验证成功");
                response.put("valid", true);
                response.put("app_key", appKey);

                log.info("Dingtalk config validation successful for appKey: {}", appKey);
                return ResponseEntity.ok(response);
            } else {
                Map<String, Object> response = buildErrorResponse("INVALID_CONFIG", "钉钉配置验证失败");
                response.put("valid", false);
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
            }

        } catch (Exception e) {
            log.error("Validate config error", e);
            Map<String, Object> response = buildErrorResponse("CONFIG_VALIDATE_ERROR", "配置验证失败");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 获取钉钉用户信息
     *
     * @param request 用户信息请求
     * @return 用户信息
     */
    @PostMapping("/user-info")
    public ResponseEntity<Map<String, Object>> getUserInfo(@RequestBody Map<String, Object> request) {
        try {
            String appKey = (String) request.get("app_key");
            String appSecret = (String) request.get("app_secret");
            String code = (String) request.get("code");

            // 验证参数
            if (appKey == null || appSecret == null || code == null) {
                Map<String, Object> response = buildErrorResponse("INVALID_PARAMS", "参数不完整");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
            }

            // 获取访问令牌
            DingtalkApiService.DingtalkTokenResult tokenResult = dingtalkApiService.getAccessToken(appKey, appSecret);
            if (!tokenResult.isSuccess()) {
                Map<String, Object> response = buildErrorResponse("TOKEN_ERROR", "获取访问令牌失败: " + tokenResult.getErrorMessage());
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
            }

            // 获取用户信息
            DingtalkApiService.DingtalkUserResult userResult = dingtalkApiService.getUserInfoByCode(tokenResult.getAccessToken(), code);
            if (!userResult.isSuccess()) {
                Map<String, Object> response = buildErrorResponse("USER_INFO_ERROR", "获取用户信息失败: " + userResult.getErrorMessage());
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
            }

            // 获取用户详细信息
            DingtalkApiService.DingtalkUserDetailResult userDetail = dingtalkApiService.getUserDetail(tokenResult.getAccessToken(), userResult.getUserId());
            if (!userDetail.isSuccess()) {
                Map<String, Object> response = buildErrorResponse("USER_DETAIL_ERROR", "获取用户详细信息失败: " + userDetail.getErrorMessage());
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
            }

            // 构建响应
            Map<String, Object> response = buildSuccessResponse("获取用户信息成功");
            response.put("user_id", userDetail.getUserId());
            response.put("name", userDetail.getName());
            response.put("mobile", userDetail.getMobile());
            response.put("email", userDetail.getEmail());
            response.put("avatar", userDetail.getAvatar());
            response.put("job_number", userDetail.getJobNumber());
            response.put("title", userDetail.getTitle());
            response.put("work_place", userDetail.getWorkPlace());
            response.put("dept_id_list", userDetail.getDeptIdList());

            log.info("Retrieved dingtalk user info: userId={}, name={}", userDetail.getUserId(), userDetail.getName());
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Get user info error", e);
            Map<String, Object> response = buildErrorResponse("USER_INFO_ERROR", "获取用户信息失败");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 获取钉钉统计信息
     *
     * @return 统计信息
     */
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getStats() {
        try {
            Map<String, Object> stats = new HashMap<>();
            
            if (dingtalkUserService instanceof DefaultDingtalkUserService) {
                DefaultDingtalkUserService defaultService = (DefaultDingtalkUserService) dingtalkUserService;
                stats = defaultService.getStatistics();
            } else {
                stats.put("message", "Statistics not available for custom implementation");
            }

            Map<String, Object> response = buildSuccessResponse("获取统计信息成功");
            response.put("statistics", stats);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Get stats error", e);
            Map<String, Object> response = buildErrorResponse("STATS_ERROR", "获取统计信息失败");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 构建钉钉授权URL
     *
     * @param appKey      应用Key
     * @param redirectUri 重定向URI
     * @param state       状态参数
     * @param authType    授权类型
     * @return 授权URL
     */
    private String buildAuthUrl(String appKey, String redirectUri, String state, String authType) {
        StringBuilder urlBuilder = new StringBuilder();
        
        if ("scan".equals(authType)) {
            // 扫码登录URL
            urlBuilder.append("https://oapi.dingtalk.com/connect/qrconnect");
            urlBuilder.append("?appid=").append(appKey);
            urlBuilder.append("&response_type=code");
            urlBuilder.append("&scope=snsapi_login");
        } else {
            // 网页授权URL
            urlBuilder.append("https://oapi.dingtalk.com/connect/oauth2/sns_authorize");
            urlBuilder.append("?appid=").append(appKey);
            urlBuilder.append("&response_type=code");
            urlBuilder.append("&scope=snsapi_auth");
        }
        
        urlBuilder.append("&redirect_uri=").append(java.net.URLEncoder.encode(redirectUri, java.nio.charset.StandardCharsets.UTF_8));
        
        if (state != null && !state.trim().isEmpty()) {
            urlBuilder.append("&state=").append(state);
        }
        
        return urlBuilder.toString();
    }

    /**
     * 构建成功响应
     *
     * @param message 消息
     * @return 响应Map
     */
    private Map<String, Object> buildSuccessResponse(String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", message);
        response.put("timestamp", LocalDateTime.now());
        return response;
    }

    /**
     * 构建错误响应
     *
     * @param errorCode        错误代码
     * @param errorDescription 错误描述
     * @return 响应Map
     */
    private Map<String, Object> buildErrorResponse(String errorCode, String errorDescription) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("error", errorCode);
        response.put("error_description", errorDescription);
        response.put("timestamp", LocalDateTime.now());
        return response;
    }
}
