{"groups": [{"name": "handthing.cache.redis", "type": "cn.com.handthing.starter.cache.redis.config.RedisCacheProperties", "sourceType": "cn.com.handthing.starter.cache.redis.config.RedisCacheProperties"}, {"name": "handthing.cache.redis.serialization", "type": "cn.com.handthing.starter.cache.redis.config.RedisCacheProperties$Serialization", "sourceType": "cn.com.handthing.starter.cache.redis.config.RedisCacheProperties", "sourceMethod": "public cn.com.handthing.starter.cache.redis.config.RedisCacheProperties.Serialization getSerialization() "}], "properties": [{"name": "handthing.cache.redis.allow-null-values", "type": "java.lang.Bo<PERSON>an", "description": "是否允许缓存null值", "sourceType": "cn.com.handthing.starter.cache.redis.config.RedisCacheProperties"}, {"name": "handthing.cache.redis.caches", "type": "java.util.Map<java.lang.String,cn.com.handthing.starter.cache.redis.config.RedisCacheProperties$RedisCacheSpec>", "description": "各个缓存区域的独立配置", "sourceType": "cn.com.handthing.starter.cache.redis.config.RedisCacheProperties"}, {"name": "handthing.cache.redis.default-ttl", "type": "java.time.Duration", "description": "默认过期时间", "sourceType": "cn.com.handthing.starter.cache.redis.config.RedisCacheProperties"}, {"name": "handthing.cache.redis.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用Redis缓存", "sourceType": "cn.com.handthing.starter.cache.redis.config.RedisCacheProperties"}, {"name": "handthing.cache.redis.key-prefix", "type": "java.lang.String", "description": "缓存键前缀", "sourceType": "cn.com.handthing.starter.cache.redis.config.RedisCacheProperties"}, {"name": "handthing.cache.redis.serialization.hash-key-type", "type": "cn.com.handthing.starter.cache.redis.config.RedisCacheProperties$Serialization$SerializationType", "description": "哈希键序列化类型", "sourceType": "cn.com.handthing.starter.cache.redis.config.RedisCacheProperties$Serialization"}, {"name": "handthing.cache.redis.serialization.hash-value-type", "type": "cn.com.handthing.starter.cache.redis.config.RedisCacheProperties$Serialization$SerializationType", "description": "哈希值序列化类型", "sourceType": "cn.com.handthing.starter.cache.redis.config.RedisCacheProperties$Serialization"}, {"name": "handthing.cache.redis.serialization.key-type", "type": "cn.com.handthing.starter.cache.redis.config.RedisCacheProperties$Serialization$SerializationType", "description": "键序列化类型", "sourceType": "cn.com.handthing.starter.cache.redis.config.RedisCacheProperties$Serialization"}, {"name": "handthing.cache.redis.serialization.value-type", "type": "cn.com.handthing.starter.cache.redis.config.RedisCacheProperties$Serialization$SerializationType", "description": "值序列化类型", "sourceType": "cn.com.handthing.starter.cache.redis.config.RedisCacheProperties$Serialization"}, {"name": "handthing.cache.redis.use-key-prefix", "type": "java.lang.Bo<PERSON>an", "description": "是否使用键前缀", "sourceType": "cn.com.handthing.starter.cache.redis.config.RedisCacheProperties"}], "hints": [], "ignored": {"properties": []}}