<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人信息 - HandThing认证测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="bi bi-shield-check"></i>
                HandThing认证测试
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    欢迎，<span th:text="${userInfo.nickname}">用户</span>
                </span>
                <a class="btn btn-outline-light btn-sm" href="/">首页</a>
                <form class="d-inline" method="post" action="/logout">
                    <button type="submit" class="btn btn-outline-light btn-sm ms-2">登出</button>
                </form>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-md-8 mx-auto">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="bi bi-person-circle"></i>
                            个人信息
                        </h4>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h5>基本信息</h5>
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>用户ID:</strong></td>
                                        <td th:text="${userInfo.userId}">-</td>
                                    </tr>
                                    <tr>
                                        <td><strong>用户名:</strong></td>
                                        <td th:text="${userInfo.username}">-</td>
                                    </tr>
                                    <tr>
                                        <td><strong>昵称:</strong></td>
                                        <td th:text="${userInfo.nickname}">-</td>
                                    </tr>
                                    <tr>
                                        <td><strong>邮箱:</strong></td>
                                        <td th:text="${userInfo.email ?: '未设置'}">-</td>
                                    </tr>
                                    <tr>
                                        <td><strong>手机号:</strong></td>
                                        <td th:text="${userInfo.phone ?: '未设置'}">-</td>
                                    </tr>
                                    <tr>
                                        <td><strong>状态:</strong></td>
                                        <td>
                                            <span th:if="${userInfo.enabled}" class="badge bg-success">启用</span>
                                            <span th:unless="${userInfo.enabled}" class="badge bg-danger">禁用</span>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h5>权限信息</h5>
                                <div class="mb-3">
                                    <strong>角色:</strong><br>
                                    <span th:each="role : ${userInfo.roles}">
                                        <span class="badge bg-primary me-1" th:text="${role}">角色</span>
                                    </span>
                                </div>
                                <div class="mb-3">
                                    <strong>权限:</strong><br>
                                    <span th:each="permission : ${userInfo.permissions}">
                                        <span class="badge bg-secondary me-1" th:text="${permission}">权限</span>
                                    </span>
                                </div>
                                <div class="mb-3">
                                    <strong>账户状态:</strong><br>
                                    <span th:if="${userInfo.accountNonExpired}" class="badge bg-success">未过期</span>
                                    <span th:unless="${userInfo.accountNonExpired}" class="badge bg-danger">已过期</span>
                                    
                                    <span th:if="${userInfo.accountNonLocked}" class="badge bg-success">未锁定</span>
                                    <span th:unless="${userInfo.accountNonLocked}" class="badge bg-danger">已锁定</span>
                                    
                                    <span th:if="${userInfo.credentialsNonExpired}" class="badge bg-success">凭证有效</span>
                                    <span th:unless="${userInfo.credentialsNonExpired}" class="badge bg-danger">凭证过期</span>
                                </div>
                            </div>
                        </div>

                        <hr>

                        <div class="row">
                            <div class="col-md-6">
                                <h5>登录信息</h5>
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>认证方式:</strong></td>
                                        <td>
                                            <span th:switch="${userContext.grantType}">
                                                <span th:case="'password'" class="badge bg-primary">
                                                    <i class="bi bi-key"></i> 密码认证
                                                </span>
                                                <span th:case="'sms_code'" class="badge bg-success">
                                                    <i class="bi bi-phone"></i> 短信认证
                                                </span>
                                                <span th:case="'wecom'" class="badge bg-warning">
                                                    <i class="bi bi-wechat"></i> 企业微信
                                                </span>
                                                <span th:case="*" class="badge bg-secondary" th:text="${userContext.grantType}">其他</span>
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>认证时间:</strong></td>
                                        <td th:text="${userContext.authenticationTime}">-</td>
                                    </tr>
                                    <tr>
                                        <td><strong>登录IP:</strong></td>
                                        <td th:text="${userContext.ipAddress}">-</td>
                                    </tr>
                                    <tr>
                                        <td><strong>最后登录:</strong></td>
                                        <td th:text="${userInfo.lastLoginTime ?: '首次登录'}">-</td>
                                    </tr>
                                    <tr>
                                        <td><strong>最后登录IP:</strong></td>
                                        <td th:text="${userInfo.lastLoginIp ?: '-'}">-</td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h5>时间信息</h5>
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>创建时间:</strong></td>
                                        <td th:text="${userInfo.createTime}">-</td>
                                    </tr>
                                    <tr>
                                        <td><strong>更新时间:</strong></td>
                                        <td th:text="${userInfo.updateTime}">-</td>
                                    </tr>
                                </table>

                                <h5>扩展属性</h5>
                                <div th:if="${userInfo.attributes and !userInfo.attributes.isEmpty()}">
                                    <div th:each="attr : ${userInfo.attributes}" class="mb-1">
                                        <small>
                                            <strong th:text="${attr.key}">属性名</strong>: 
                                            <span th:text="${attr.value}">属性值</span>
                                        </small>
                                    </div>
                                </div>
                                <div th:unless="${userInfo.attributes and !userInfo.attributes.isEmpty()}">
                                    <small class="text-muted">无扩展属性</small>
                                </div>
                            </div>
                        </div>

                        <hr>

                        <div class="row">
                            <div class="col-12">
                                <h5>功能测试</h5>
                                <div class="row">
                                    <div class="col-md-3">
                                        <button class="btn btn-outline-primary w-100 mb-2" onclick="testProtectedResource()">
                                            <i class="bi bi-shield-lock"></i>
                                            受保护资源
                                        </button>
                                    </div>
                                    <div class="col-md-3">
                                        <button class="btn btn-outline-warning w-100 mb-2" onclick="testAdminResource()">
                                            <i class="bi bi-person-gear"></i>
                                            管理员资源
                                        </button>
                                    </div>
                                    <div class="col-md-3">
                                        <button class="btn btn-outline-info w-100 mb-2" onclick="refreshToken()">
                                            <i class="bi bi-arrow-clockwise"></i>
                                            刷新令牌
                                        </button>
                                    </div>
                                    <div class="col-md-3">
                                        <button class="btn btn-outline-success w-100 mb-2" onclick="getUserInfo()">
                                            <i class="bi bi-person-circle"></i>
                                            获取用户信息
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 测试结果显示区域 -->
                        <div id="testResult" class="mt-3" style="display: none;">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">测试结果</h6>
                                </div>
                                <div class="card-body">
                                    <pre id="testResultContent" class="bg-light p-3 rounded small"></pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function showResult(data) {
            document.getElementById('testResultContent').textContent = JSON.stringify(data, null, 2);
            document.getElementById('testResult').style.display = 'block';
        }

        function testProtectedResource() {
            fetch('/protected')
                .then(response => response.json())
                .then(data => showResult(data))
                .catch(error => showResult({error: error.message}));
        }

        function testAdminResource() {
            fetch('/admin')
                .then(response => response.json())
                .then(data => showResult(data))
                .catch(error => showResult({error: error.message}));
        }

        function refreshToken() {
            fetch('/auth/refresh', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => showResult(data))
            .catch(error => showResult({error: error.message}));
        }

        function getUserInfo() {
            fetch('/auth/userinfo')
                .then(response => response.json())
                .then(data => showResult(data))
                .catch(error => showResult({error: error.message}));
        }
    </script>
</body>
</html>
