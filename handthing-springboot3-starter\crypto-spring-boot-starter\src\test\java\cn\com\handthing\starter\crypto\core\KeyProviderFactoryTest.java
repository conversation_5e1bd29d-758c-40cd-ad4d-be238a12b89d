package cn.com.handthing.starter.crypto.core;

import cn.com.handthing.starter.crypto.exception.CryptoException;
import cn.com.handthing.starter.crypto.properties.CryptoProperties.KeyStoreConfig;
import cn.com.handthing.starter.crypto.properties.CryptoProperties.KeyStoreType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationContext;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

/**
 * KeyProviderFactory 的单元测试。
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("KeyProviderFactory Tests")
public class KeyProviderFactoryTest {

    @Mock
    private ApplicationContext applicationContext;

    private KeyProviderFactory keyProviderFactory;

    @BeforeEach
    void setUp() {
        keyProviderFactory = new KeyProviderFactory(applicationContext);
    }

    @Test
    @DisplayName("当类型为 CONFIG 时应创建 ConfigKeyProvider")
    void shouldCreateConfigKeyProvider() {
        KeyStoreConfig config = new KeyStoreConfig();
        config.setType(KeyStoreType.CONFIG);
        config.setKeyValue("AAECAwQFBgcICQoLDA0ODw==");

        KeyProvider provider = keyProviderFactory.create(config);
        assertThat(provider).isInstanceOf(ConfigKeyProvider.class);
    }

    @Test
    @DisplayName("当类型为 FILE 时应抛出异常（文件不存在）")
    void shouldCreateFileKeyProvider() {
        KeyStoreConfig config = new KeyStoreConfig();
        config.setType(KeyStoreType.FILE);
        config.setPath("/tmp/dummy.key");

        assertThatThrownBy(() -> keyProviderFactory.create(config))
                .isInstanceOf(CryptoException.class)
                .hasMessageContaining("Failed to read key from file");
    }

    @Test
    @DisplayName("当类型为 CLASSPATH 时应创建 ClasspathKeyProvider")
    void shouldCreateClasspathKeyProvider() {
        KeyStoreConfig config = new KeyStoreConfig();
        config.setType(KeyStoreType.CLASSPATH);
        config.setPath("keys/test.key");

        KeyProvider provider = keyProviderFactory.create(config);
        assertThat(provider).isInstanceOf(ClasspathKeyProvider.class);
    }

    @Test
    @DisplayName("当类型为 ENV 时应创建 EnvironmentKeyProvider")
    void shouldCreateEnvironmentKeyProvider() {
        // 设置环境变量
        System.setProperty("MY_APP_KEY", "dGVzdC1rZXktZm9yLXVuaXQtdGVzdA==");

        try {
            KeyStoreConfig config = new KeyStoreConfig();
            config.setType(KeyStoreType.ENV);
            config.setEnvVariable("MY_APP_KEY");

            assertThatThrownBy(() -> keyProviderFactory.create(config))
                    .isInstanceOf(CryptoException.class)
                    .hasMessageContaining("Environment variable");
        } finally {
            System.clearProperty("MY_APP_KEY");
        }
    }

    @Test
    @DisplayName("当类型为 VAULT 时应创建 VaultKeyProvider")
    void shouldCreateVaultKeyProvider() {
        KeyStoreConfig config = new KeyStoreConfig();
        config.setType(KeyStoreType.VAULT);

        assertThatThrownBy(() -> keyProviderFactory.create(config))
                .isInstanceOf(CryptoException.class)
                .hasMessageContaining("Vault config is missing");
    }

    @Test
    @DisplayName("当 KeyStoreConfig 为 null 时应抛出异常")
    void shouldThrowExceptionWhenConfigIsNull() {
        assertThatThrownBy(() -> keyProviderFactory.create(null))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("KeyStoreConfig cannot be null");
    }
}