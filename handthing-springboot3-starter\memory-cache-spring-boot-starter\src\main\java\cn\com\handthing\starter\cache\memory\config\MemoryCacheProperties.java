package cn.com.handthing.starter.cache.memory.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * 内存缓存配置属性
 * <p>
 * 基于ConcurrentHashMap的内存缓存配置
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
@ConfigurationProperties(prefix = "handthing.cache.memory")
public class MemoryCacheProperties {

    /**
     * 是否启用内存缓存
     */
    private boolean enabled = true;

    /**
     * 默认最大缓存条目数
     */
    private long defaultMaxSize = 1000L;

    /**
     * 默认过期时间
     */
    private Duration defaultTtl = Duration.ofMinutes(30);

    /**
     * 清理过期缓存的间隔时间
     */
    private Duration cleanupInterval = Duration.ofMinutes(5);

    /**
     * 是否启用统计信息
     */
    private boolean enableStats = true;

    /**
     * 各个缓存区域的独立配置
     */
    private Map<String, MemoryCacheSpec> caches = new HashMap<>();

    /**
     * 内存缓存区域配置
     */
    @Data
    public static class MemoryCacheSpec {

        /**
         * 最大缓存条目数
         */
        private Long maxSize;

        /**
         * 过期时间
         */
        private Duration ttl;

        /**
         * 初始容量
         */
        private Integer initialCapacity;

        /**
         * 是否启用统计信息
         */
        private Boolean enableStats;

        /**
         * 获取有效的最大缓存条目数
         *
         * @param defaultMaxSize 默认最大缓存条目数
         * @return 有效的最大缓存条目数
         */
        public long getEffectiveMaxSize(long defaultMaxSize) {
            return maxSize != null ? maxSize : defaultMaxSize;
        }

        /**
         * 获取有效的TTL
         *
         * @param defaultTtl 默认TTL
         * @return 有效的TTL
         */
        public Duration getEffectiveTtl(Duration defaultTtl) {
            return ttl != null ? ttl : defaultTtl;
        }

        /**
         * 获取有效的初始容量
         *
         * @return 有效的初始容量
         */
        public int getEffectiveInitialCapacity() {
            return initialCapacity != null ? initialCapacity : 16;
        }

        /**
         * 获取有效的统计信息启用状态
         *
         * @param defaultEnableStats 默认统计信息启用状态
         * @return 有效的统计信息启用状态
         */
        public boolean getEffectiveEnableStats(boolean defaultEnableStats) {
            return enableStats != null ? enableStats : defaultEnableStats;
        }
    }
}