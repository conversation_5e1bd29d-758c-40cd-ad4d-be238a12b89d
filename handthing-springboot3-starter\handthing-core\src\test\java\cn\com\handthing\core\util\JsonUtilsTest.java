package cn.com.handthing.core.util;

import com.fasterxml.jackson.core.type.TypeReference;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * JsonUtils 工具类单元测试
 * 
 * <AUTHOR>
 * @since V1.0.0
 */
@DisplayName("JsonUtils 工具类测试")
class JsonUtilsTest {

    private TestUser testUser;
    private List<TestUser> userList;
    private Set<TestUser> userSet;
    private Map<String, TestUser> userMap;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        testUser = new TestUser(
            1L, 
            "张三", 
            25, 
            5000.50, 
            95.5f, 
            new BigDecimal("10000.99"),
            LocalDateTime.of(2023, 1, 1, 10, 30, 0),
            LocalDate.of(1998, 5, 15),
            true
        );

        TestUser user2 = new TestUser(2L, "李四", 30);
        TestUser user3 = new TestUser(3L, "王五", 28);

        userList = Arrays.asList(testUser, user2, user3);
        userSet = new LinkedHashSet<>(userList);
        
        userMap = new HashMap<>();
        userMap.put("user1", testUser);
        userMap.put("user2", user2);
        userMap.put("user3", user3);
    }

    @Test
    @DisplayName("测试基本对象序列化")
    void testToJson() {
        String json = JsonUtils.toJson(testUser);
        assertNotNull(json);
        assertTrue(json.contains("\"id\":\"1\""));  // Long 类型应该序列化为字符串
        assertTrue(json.contains("\"name\":\"张三\""));
        assertTrue(json.contains("\"salary\":\"5000.5\""));  // Double 类型应该序列化为字符串
        assertTrue(json.contains("\"createTime\":\"2023-01-01 10:30:00\""));  // 时间格式
        assertTrue(json.contains("\"birthDate\":\"1998-05-15\""));  // 日期格式
        System.out.println("序列化结果: " + json);
    }

    @Test
    @DisplayName("测试格式化序列化")
    void testToPrettyJson() {
        String prettyJson = JsonUtils.toPrettyJson(testUser);
        assertNotNull(prettyJson);
        assertTrue(prettyJson.contains("\n"));  // 格式化后应该包含换行符
        System.out.println("格式化序列化结果:\n" + prettyJson);
    }

    @Test
    @DisplayName("测试基本对象反序列化")
    void testFromJson() {
        String json = JsonUtils.toJson(testUser);
        TestUser deserializedUser = JsonUtils.fromJson(json, TestUser.class);
        
        assertNotNull(deserializedUser);
        assertEquals(testUser.getId(), deserializedUser.getId());
        assertEquals(testUser.getName(), deserializedUser.getName());
        assertEquals(testUser.getAge(), deserializedUser.getAge());
        assertEquals(testUser.getCreateTime(), deserializedUser.getCreateTime());
        assertEquals(testUser.getBirthDate(), deserializedUser.getBirthDate());
    }

    @Test
    @DisplayName("测试 null 值处理")
    void testNullHandling() {
        assertNull(JsonUtils.toJson(null));
        assertNull(JsonUtils.toPrettyJson(null));
        assertNull(JsonUtils.fromJson(null, TestUser.class));
        assertNull(JsonUtils.fromJson("", TestUser.class));
        assertNull(JsonUtils.fromJson("   ", TestUser.class));
    }

    @Test
    @DisplayName("测试 List 序列化")
    void testToJsonList() {
        String json = JsonUtils.toJsonList(userList);
        assertNotNull(json);
        assertTrue(json.startsWith("["));
        assertTrue(json.endsWith("]"));
        assertTrue(json.contains("\"name\":\"张三\""));
        assertTrue(json.contains("\"name\":\"李四\""));
        System.out.println("List 序列化结果: " + json);
    }

    @Test
    @DisplayName("测试 List 反序列化")
    void testFromJsonToList() {
        String json = JsonUtils.toJsonList(userList);
        List<TestUser> deserializedList = JsonUtils.fromJsonToList(json, TestUser.class);
        
        assertNotNull(deserializedList);
        assertEquals(3, deserializedList.size());
        assertEquals("张三", deserializedList.get(0).getName());
        assertEquals("李四", deserializedList.get(1).getName());
        assertEquals("王五", deserializedList.get(2).getName());
    }

    @Test
    @DisplayName("测试 List TypeReference 反序列化")
    void testFromJsonToListWithTypeReference() {
        String json = JsonUtils.toJsonList(userList);
        List<TestUser> deserializedList = JsonUtils.fromJsonToList(json, new TypeReference<List<TestUser>>() {});
        
        assertNotNull(deserializedList);
        assertEquals(3, deserializedList.size());
        assertEquals("张三", deserializedList.get(0).getName());
    }

    @Test
    @DisplayName("测试 Set 序列化")
    void testToJsonSet() {
        String json = JsonUtils.toJsonSet(userSet);
        assertNotNull(json);
        assertTrue(json.startsWith("["));
        assertTrue(json.endsWith("]"));
        System.out.println("Set 序列化结果: " + json);
    }

    @Test
    @DisplayName("测试 Set 反序列化")
    void testFromJsonToSet() {
        String json = JsonUtils.toJsonSet(userSet);
        Set<TestUser> deserializedSet = JsonUtils.fromJsonToSet(json, TestUser.class);
        
        assertNotNull(deserializedSet);
        assertEquals(3, deserializedSet.size());
        assertTrue(deserializedSet.stream().anyMatch(u -> "张三".equals(u.getName())));
    }

    @Test
    @DisplayName("测试 Set TypeReference 反序列化")
    void testFromJsonToSetWithTypeReference() {
        String json = JsonUtils.toJsonSet(userSet);
        Set<TestUser> deserializedSet = JsonUtils.fromJsonToSet(json, new TypeReference<Set<TestUser>>() {});
        
        assertNotNull(deserializedSet);
        assertEquals(3, deserializedSet.size());
    }

    @Test
    @DisplayName("测试 Map 序列化")
    void testToJsonMap() {
        String json = JsonUtils.toJsonMap(userMap);
        assertNotNull(json);
        assertTrue(json.startsWith("{"));
        assertTrue(json.endsWith("}"));
        assertTrue(json.contains("\"user1\""));
        System.out.println("Map 序列化结果: " + json);
    }

    @Test
    @DisplayName("测试 Map 反序列化")
    void testFromJsonToMap() {
        String json = JsonUtils.toJsonMap(userMap);
        Map<String, TestUser> deserializedMap = JsonUtils.fromJsonToMap(json, String.class, TestUser.class);
        
        assertNotNull(deserializedMap);
        assertEquals(3, deserializedMap.size());
        assertEquals("张三", deserializedMap.get("user1").getName());
        assertEquals("李四", deserializedMap.get("user2").getName());
    }

    @Test
    @DisplayName("测试 Map TypeReference 反序列化")
    void testFromJsonToMapWithTypeReference() {
        String json = JsonUtils.toJsonMap(userMap);
        Map<String, TestUser> deserializedMap = JsonUtils.fromJsonToMap(json, new TypeReference<Map<String, TestUser>>() {});

        assertNotNull(deserializedMap);
        assertEquals(3, deserializedMap.size());
        assertEquals("张三", deserializedMap.get("user1").getName());
    }

    @Test
    @DisplayName("测试 List 转 Set")
    void testListToSet() {
        // 测试正常转换
        Set<TestUser> convertedSet = JsonUtils.listToSet(userList);
        assertNotNull(convertedSet);
        assertEquals(3, convertedSet.size());
        assertTrue(convertedSet instanceof LinkedHashSet);  // 应该保持插入顺序

        // 测试去重功能
        List<String> listWithDuplicates = Arrays.asList("a", "b", "a", "c", "b");
        Set<String> uniqueSet = JsonUtils.listToSet(listWithDuplicates);
        assertEquals(3, uniqueSet.size());
        assertTrue(uniqueSet.contains("a"));
        assertTrue(uniqueSet.contains("b"));
        assertTrue(uniqueSet.contains("c"));

        // 测试 null 处理
        assertNull(JsonUtils.listToSet(null));
    }

    @Test
    @DisplayName("测试 Set 转 List")
    void testSetToList() {
        List<TestUser> convertedList = JsonUtils.setToList(userSet);
        assertNotNull(convertedList);
        assertEquals(3, convertedList.size());
        assertTrue(convertedList instanceof ArrayList);

        // 测试 null 处理
        assertNull(JsonUtils.setToList(null));
    }

    @Test
    @DisplayName("测试数组转 List")
    void testArrayToList() {
        String[] array = {"a", "b", "c"};
        List<String> convertedList = JsonUtils.arrayToList(array);
        assertNotNull(convertedList);
        assertEquals(3, convertedList.size());
        assertEquals("a", convertedList.get(0));
        assertEquals("b", convertedList.get(1));
        assertEquals("c", convertedList.get(2));

        // 测试 null 处理
        assertNull(JsonUtils.arrayToList(null));
    }

    @Test
    @DisplayName("测试 List 转数组")
    void testListToArray() {
        List<String> stringList = Arrays.asList("a", "b", "c");
        String[] array = JsonUtils.listToArray(stringList, String.class);
        assertNotNull(array);
        assertEquals(3, array.length);
        assertEquals("a", array[0]);
        assertEquals("b", array[1]);
        assertEquals("c", array[2]);

        // 测试 null 处理
        assertNull(JsonUtils.listToArray(null, String.class));
        assertNull(JsonUtils.listToArray(stringList, null));
    }

    @Test
    @DisplayName("测试过滤 List 中的 null 元素")
    void testFilterNullFromList() {
        List<String> listWithNulls = Arrays.asList("a", null, "b", null, "c");
        List<String> filteredList = JsonUtils.filterNullFromList(listWithNulls);
        assertNotNull(filteredList);
        assertEquals(3, filteredList.size());
        assertEquals("a", filteredList.get(0));
        assertEquals("b", filteredList.get(1));
        assertEquals("c", filteredList.get(2));

        // 测试 null 处理
        assertNull(JsonUtils.filterNullFromList(null));

        // 测试全为 null 的情况
        List<String> allNullList = Arrays.asList(null, null, null);
        List<String> emptyList = JsonUtils.filterNullFromList(allNullList);
        assertNotNull(emptyList);
        assertTrue(emptyList.isEmpty());
    }

    @Test
    @DisplayName("测试过滤 Set 中的 null 元素")
    void testFilterNullFromSet() {
        Set<String> setWithNulls = new LinkedHashSet<>(Arrays.asList("a", null, "b", null, "c"));
        Set<String> filteredSet = JsonUtils.filterNullFromSet(setWithNulls);
        assertNotNull(filteredSet);
        assertEquals(3, filteredSet.size());
        assertTrue(filteredSet.contains("a"));
        assertTrue(filteredSet.contains("b"));
        assertTrue(filteredSet.contains("c"));

        // 测试 null 处理
        assertNull(JsonUtils.filterNullFromSet(null));
    }

    @Test
    @DisplayName("测试复杂泛型反序列化")
    void testComplexGenericDeserialization() {
        // 测试 List<Map<String, Object>>
        List<Map<String, Object>> complexList = new ArrayList<>();
        Map<String, Object> item1 = new HashMap<>();
        item1.put("id", 1L);
        item1.put("name", "test");
        item1.put("active", true);
        complexList.add(item1);

        String json = JsonUtils.toJson(complexList);
        List<Map<String, Object>> deserializedList = JsonUtils.fromJson(json, new TypeReference<List<Map<String, Object>>>() {});

        assertNotNull(deserializedList);
        assertEquals(1, deserializedList.size());
        Map<String, Object> deserializedItem = deserializedList.get(0);
        assertEquals("1", deserializedItem.get("id"));  // Long 序列化为字符串
        assertEquals("test", deserializedItem.get("name"));
        assertEquals(true, deserializedItem.get("active"));
    }

    @Test
    @DisplayName("测试异常情况处理")
    void testExceptionHandling() {
        // 测试无效 JSON
        TestUser result = JsonUtils.fromJson("{invalid json}", TestUser.class);
        assertNull(result);

        List<TestUser> listResult = JsonUtils.fromJsonToList("{invalid json}", TestUser.class);
        assertNull(listResult);

        Set<TestUser> setResult = JsonUtils.fromJsonToSet("{invalid json}", TestUser.class);
        assertNull(setResult);

        Map<String, TestUser> mapResult = JsonUtils.fromJsonToMap("{invalid json}", String.class, TestUser.class);
        assertNull(mapResult);
    }
}
