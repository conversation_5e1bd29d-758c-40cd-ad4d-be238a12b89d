package cn.com.handthing.starter.auth.thirdparty;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * 第三方认证配置属性
 * <p>
 * 定义第三方认证相关的配置属性，包括企业微信、钉钉、微信等第三方平台的配置。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
@ConfigurationProperties(prefix = "handthing.auth.third-party")
public class ThirdPartyAuthProperties {

    /**
     * 是否启用第三方认证
     */
    private boolean enabled = true;

    /**
     * 企业微信配置
     */
    private WecomConfig wecom = new WecomConfig();

    /**
     * 钉钉配置
     */
    private DingtalkConfig dingtalk = new DingtalkConfig();

    /**
     * 微信配置
     */
    private WechatConfig wechat = new WechatConfig();

    /**
     * 飞书配置
     */
    private FeishuConfig feishu = new FeishuConfig();

    /**
     * 抖音配置
     */
    private DouyinConfig douyin = new DouyinConfig();

    /**
     * 支付宝配置
     */
    private AlipayConfig alipay = new AlipayConfig();

    /**
     * 通用配置
     */
    private CommonConfig common = new CommonConfig();

    /**
     * 企业微信配置
     */
    @Data
    public static class WecomConfig {
        /**
         * 是否启用企业微信认证
         */
        private boolean enabled = true;

        /**
         * 是否启用API端点
         */
        private boolean endpointsEnabled = true;

        /**
         * 企业ID
         */
        private String corpId;

        /**
         * 企业密钥
         */
        private String corpSecret;

        /**
         * 应用ID
         */
        private String agentId;

        /**
         * 应用密钥
         */
        private String agentSecret;

        /**
         * 重定向URI
         */
        private String redirectUri;

        /**
         * API超时时间
         */
        private Duration apiTimeout = Duration.ofSeconds(10);

        /**
         * 是否启用自动注册
         */
        private boolean autoRegister = true;
    }

    /**
     * 钉钉配置
     */
    @Data
    public static class DingtalkConfig {
        private boolean enabled = false;
        private boolean endpointsEnabled = true;
        private String appKey;
        private String appSecret;
        private String redirectUri;
        private Duration apiTimeout = Duration.ofSeconds(10);
        private boolean autoRegister = true;
    }

    /**
     * 微信配置
     */
    @Data
    public static class WechatConfig {
        private boolean enabled = false;
        private boolean endpointsEnabled = true;
        private String appId;
        private String appSecret;
        private String redirectUri;
        private Duration apiTimeout = Duration.ofSeconds(10);
        private boolean autoRegister = true;
    }

    /**
     * 飞书配置
     */
    @Data
    public static class FeishuConfig {
        private boolean enabled = false;
        private boolean endpointsEnabled = true;
        private String appId;
        private String appSecret;
        private String redirectUri;
        private Duration apiTimeout = Duration.ofSeconds(10);
        private boolean autoRegister = true;
    }

    /**
     * 抖音配置
     */
    @Data
    public static class DouyinConfig {
        private boolean enabled = false;
        private boolean endpointsEnabled = true;
        private String clientKey;
        private String clientSecret;
        private String redirectUri;
        private Duration apiTimeout = Duration.ofSeconds(10);
        private boolean autoRegister = true;
    }

    /**
     * 支付宝配置
     */
    @Data
    public static class AlipayConfig {
        private boolean enabled = false;
        private boolean endpointsEnabled = true;
        private String appId;
        private String privateKey;
        private String publicKey;
        private String redirectUri;
        private Duration apiTimeout = Duration.ofSeconds(10);
        private boolean autoRegister = true;
    }

    /**
     * 通用配置
     */
    @Data
    public static class CommonConfig {
        /**
         * 访问令牌过期时间
         */
        private Duration accessTokenExpiration = Duration.ofHours(2);

        /**
         * 刷新令牌过期时间
         */
        private Duration refreshTokenExpiration = Duration.ofDays(7);

        /**
         * 是否启用令牌缓存
         */
        private boolean tokenCacheEnabled = true;

        /**
         * 令牌缓存过期时间
         */
        private Duration tokenCacheExpiration = Duration.ofMinutes(30);

        /**
         * 最大登录尝试次数
         */
        private int maxLoginAttempts = 5;

        /**
         * 账户锁定持续时间
         */
        private Duration accountLockDuration = Duration.ofMinutes(15);

        /**
         * 自定义属性映射
         */
        private Map<String, String> attributeMapping = new HashMap<>();
    }

    /**
     * 获取访问令牌过期时间（秒）
     *
     * @return 过期时间（秒）
     */
    public long getAccessTokenExpirationSeconds() {
        return common.accessTokenExpiration != null ? common.accessTokenExpiration.getSeconds() : 7200;
    }

    /**
     * 获取刷新令牌过期时间（秒）
     *
     * @return 过期时间（秒）
     */
    public long getRefreshTokenExpirationSeconds() {
        return common.refreshTokenExpiration != null ? common.refreshTokenExpiration.getSeconds() : 604800;
    }

    /**
     * 验证配置是否有效
     *
     * @return 如果配置有效返回true，否则返回false
     */
    public boolean isValid() {
        return enabled && 
               common.accessTokenExpiration != null && !common.accessTokenExpiration.isNegative() &&
               common.refreshTokenExpiration != null && !common.refreshTokenExpiration.isNegative() &&
               common.maxLoginAttempts > 0;
    }

    @Override
    public String toString() {
        return String.format("ThirdPartyAuthProperties{enabled=%s, wecom.enabled=%s, dingtalk.enabled=%s, wechat.enabled=%s}",
                enabled, wecom.enabled, dingtalk.enabled, wechat.enabled);
    }
}
