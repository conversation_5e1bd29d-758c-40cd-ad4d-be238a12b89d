package cn.com.handthing.starter.crypto.configuration;

import cn.com.handthing.starter.crypto.tool.CryptoToolRunner;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.autoconfigure.AutoConfigurations;
import org.springframework.boot.test.context.runner.ApplicationContextRunner;
import static org.assertj.core.api.Assertions.assertThat;

/**
 * CryptoToolConfiguration 的单元测试。
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@DisplayName("Crypto Tool Configuration Tests")
class CryptoToolConfigurationTest {
    
    private final ApplicationContextRunner contextRunner = new ApplicationContextRunner()
            .withConfiguration(AutoConfigurations.of(CryptoToolConfiguration.class));

    @Test
    @DisplayName("当指定了生成密钥的算法时，应注册 CryptoToolRunner Bean")
    void shouldRegisterToolRunnerWhenPropertyIsSet() {
        this.contextRunner
                .withPropertyValues("handthing.crypto.tool.generate-key.algorithm=AES")
                .run(context -> {
                    assertThat(context).hasBean("cryptoToolRunner");
                    assertThat(context.getBean("cryptoToolRunner")).isInstanceOf(CryptoToolRunner.class);
                });
    }

    @Test
    @DisplayName("当未指定生成密钥的算法时，不应注册 CryptoToolRunner Bean")
    void shouldNotRegisterToolRunnerWhenPropertyIsNotSet() {
        this.contextRunner.run(context -> {
            assertThat(context).doesNotHaveBean("cryptoToolRunner");
        });
    }
}