package cn.com.handthing.starter.auth.core;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * JWT声明模型
 * <p>
 * 定义JWT令牌中包含的声明信息，包括标准声明和自定义声明。
 * 支持用户信息、权限信息、会话信息等的封装。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class JwtClaims {

    /**
     * 主题（用户ID）
     */
    private String subject;

    /**
     * 签发者
     */
    private String issuer;

    /**
     * 受众
     */
    private String audience;

    /**
     * 过期时间
     */
    private LocalDateTime expiration;

    /**
     * 生效时间
     */
    private LocalDateTime notBefore;

    /**
     * 签发时间
     */
    private LocalDateTime issuedAt;

    /**
     * JWT ID
     */
    private String jwtId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 角色列表
     */
    private List<String> roles;

    /**
     * 权限列表
     */
    private List<String> permissions;

    /**
     * 授权类型
     */
    private String grantType;

    /**
     * 客户端ID
     */
    private String clientId;

    /**
     * 授权范围
     */
    private String scope;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 设备ID
     */
    private String deviceId;

    /**
     * IP地址
     */
    private String ipAddress;

    /**
     * 用户代理
     */
    private String userAgent;

    /**
     * 令牌类型
     */
    private String tokenType;

    /**
     * 自定义声明
     */
    private Map<String, Object> customClaims;

    /**
     * 添加自定义声明
     *
     * @param key   声明键
     * @param value 声明值
     */
    public void addCustomClaim(String key, Object value) {
        if (customClaims == null) {
            customClaims = new HashMap<>();
        }
        customClaims.put(key, value);
    }

    /**
     * 获取自定义声明
     *
     * @param key 声明键
     * @return 声明值
     */
    public Object getCustomClaim(String key) {
        return customClaims != null ? customClaims.get(key) : null;
    }

    /**
     * 获取自定义声明（指定类型）
     *
     * @param key   声明键
     * @param clazz 声明类型
     * @param <T>   泛型类型
     * @return 声明值
     */
    @SuppressWarnings("unchecked")
    public <T> T getCustomClaim(String key, Class<T> clazz) {
        Object value = getCustomClaim(key);
        if (value != null && clazz.isInstance(value)) {
            return (T) value;
        }
        return null;
    }

    /**
     * 判断是否过期
     *
     * @return 如果过期返回true，否则返回false
     */
    public boolean isExpired() {
        return expiration != null && LocalDateTime.now().isAfter(expiration);
    }

    /**
     * 判断是否生效
     *
     * @return 如果生效返回true，否则返回false
     */
    public boolean isActive() {
        LocalDateTime now = LocalDateTime.now();
        return (notBefore == null || !now.isBefore(notBefore)) && !isExpired();
    }

    /**
     * 获取剩余有效时间（秒）
     *
     * @return 剩余有效时间，如果已过期返回0
     */
    public long getRemainingSeconds() {
        if (expiration == null || isExpired()) {
            return 0;
        }
        return java.time.Duration.between(LocalDateTime.now(), expiration).getSeconds();
    }

    /**
     * 转换为Map
     *
     * @return 声明Map
     */
    public Map<String, Object> toMap() {
        Map<String, Object> claims = new HashMap<>();
        
        if (subject != null) claims.put("sub", subject);
        if (issuer != null) claims.put("iss", issuer);
        if (audience != null) claims.put("aud", audience);
        if (expiration != null) claims.put("exp", expiration);
        if (notBefore != null) claims.put("nbf", notBefore);
        if (issuedAt != null) claims.put("iat", issuedAt);
        if (jwtId != null) claims.put("jti", jwtId);
        
        if (username != null) claims.put("username", username);
        if (nickname != null) claims.put("nickname", nickname);
        if (email != null) claims.put("email", email);
        if (phone != null) claims.put("phone", phone);
        if (roles != null) claims.put("roles", roles);
        if (permissions != null) claims.put("permissions", permissions);
        if (grantType != null) claims.put("grant_type", grantType);
        if (clientId != null) claims.put("client_id", clientId);
        if (scope != null) claims.put("scope", scope);
        if (sessionId != null) claims.put("session_id", sessionId);
        if (deviceId != null) claims.put("device_id", deviceId);
        if (ipAddress != null) claims.put("ip_address", ipAddress);
        if (userAgent != null) claims.put("user_agent", userAgent);
        if (tokenType != null) claims.put("token_type", tokenType);
        
        if (customClaims != null) {
            claims.putAll(customClaims);
        }
        
        return claims;
    }

    /**
     * 从Map创建JwtClaims
     *
     * @param claimsMap 声明Map
     * @return JwtClaims对象
     */
    @SuppressWarnings("unchecked")
    public static JwtClaims fromMap(Map<String, Object> claimsMap) {
        JwtClaimsBuilder builder = JwtClaims.builder();
        
        if (claimsMap.containsKey("sub")) builder.subject((String) claimsMap.get("sub"));
        if (claimsMap.containsKey("iss")) builder.issuer((String) claimsMap.get("iss"));
        if (claimsMap.containsKey("aud")) builder.audience((String) claimsMap.get("aud"));
        if (claimsMap.containsKey("exp")) builder.expiration((LocalDateTime) claimsMap.get("exp"));
        if (claimsMap.containsKey("nbf")) builder.notBefore((LocalDateTime) claimsMap.get("nbf"));
        if (claimsMap.containsKey("iat")) builder.issuedAt((LocalDateTime) claimsMap.get("iat"));
        if (claimsMap.containsKey("jti")) builder.jwtId((String) claimsMap.get("jti"));
        
        if (claimsMap.containsKey("username")) builder.username((String) claimsMap.get("username"));
        if (claimsMap.containsKey("nickname")) builder.nickname((String) claimsMap.get("nickname"));
        if (claimsMap.containsKey("email")) builder.email((String) claimsMap.get("email"));
        if (claimsMap.containsKey("phone")) builder.phone((String) claimsMap.get("phone"));
        if (claimsMap.containsKey("roles")) builder.roles((List<String>) claimsMap.get("roles"));
        if (claimsMap.containsKey("permissions")) builder.permissions((List<String>) claimsMap.get("permissions"));
        if (claimsMap.containsKey("grant_type")) builder.grantType((String) claimsMap.get("grant_type"));
        if (claimsMap.containsKey("client_id")) builder.clientId((String) claimsMap.get("client_id"));
        if (claimsMap.containsKey("scope")) builder.scope((String) claimsMap.get("scope"));
        if (claimsMap.containsKey("session_id")) builder.sessionId((String) claimsMap.get("session_id"));
        if (claimsMap.containsKey("device_id")) builder.deviceId((String) claimsMap.get("device_id"));
        if (claimsMap.containsKey("ip_address")) builder.ipAddress((String) claimsMap.get("ip_address"));
        if (claimsMap.containsKey("user_agent")) builder.userAgent((String) claimsMap.get("user_agent"));
        if (claimsMap.containsKey("token_type")) builder.tokenType((String) claimsMap.get("token_type"));
        
        // 处理自定义声明
        Map<String, Object> customClaims = new HashMap<>();
        for (Map.Entry<String, Object> entry : claimsMap.entrySet()) {
            String key = entry.getKey();
            if (!isStandardClaim(key)) {
                customClaims.put(key, entry.getValue());
            }
        }
        if (!customClaims.isEmpty()) {
            builder.customClaims(customClaims);
        }
        
        return builder.build();
    }

    /**
     * 判断是否为标准声明
     *
     * @param claimName 声明名称
     * @return 如果是标准声明返回true，否则返回false
     */
    private static boolean isStandardClaim(String claimName) {
        return "sub".equals(claimName) || "iss".equals(claimName) || "aud".equals(claimName) ||
               "exp".equals(claimName) || "nbf".equals(claimName) || "iat".equals(claimName) ||
               "jti".equals(claimName) || "username".equals(claimName) || "nickname".equals(claimName) ||
               "email".equals(claimName) || "phone".equals(claimName) || "roles".equals(claimName) ||
               "permissions".equals(claimName) || "grant_type".equals(claimName) || "client_id".equals(claimName) ||
               "scope".equals(claimName) || "session_id".equals(claimName) || "device_id".equals(claimName) ||
               "ip_address".equals(claimName) || "user_agent".equals(claimName) || "token_type".equals(claimName);
    }

    @Override
    public String toString() {
        return String.format("JwtClaims{subject='%s', username='%s', expiration=%s, roles=%s}",
                subject, username, expiration, roles);
    }
}
