package cn.com.handthing.starter.connector.exception;

import cn.com.handthing.starter.connector.PlatformType;
import lombok.Getter;

/**
 * 连接器基础异常类
 * <p>
 * 所有连接器相关异常的基类，包含错误码、错误消息、平台类型等信息，
 * 支持异常链传递。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Getter
public class ConnectorException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    /**
     * 错误码
     */
    private final String errorCode;

    /**
     * 平台类型
     */
    private final PlatformType platform;

    /**
     * 额外的错误信息
     */
    private final Object errorData;

    /**
     * 构造函数
     *
     * @param message 错误消息
     */
    public ConnectorException(String message) {
        super(message);
        this.errorCode = null;
        this.platform = null;
        this.errorData = null;
    }

    /**
     * 构造函数
     *
     * @param message 错误消息
     * @param cause   原因异常
     */
    public ConnectorException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = null;
        this.platform = null;
        this.errorData = null;
    }

    /**
     * 构造函数
     *
     * @param message   错误消息
     * @param errorCode 错误码
     */
    public ConnectorException(String message, String errorCode) {
        super(message);
        this.errorCode = errorCode;
        this.platform = null;
        this.errorData = null;
    }

    /**
     * 构造函数
     *
     * @param message   错误消息
     * @param errorCode 错误码
     * @param cause     原因异常
     */
    public ConnectorException(String message, String errorCode, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.platform = null;
        this.errorData = null;
    }

    /**
     * 构造函数
     *
     * @param message   错误消息
     * @param errorCode 错误码
     * @param platform  平台类型
     */
    public ConnectorException(String message, String errorCode, PlatformType platform) {
        super(message);
        this.errorCode = errorCode;
        this.platform = platform;
        this.errorData = null;
    }

    /**
     * 构造函数
     *
     * @param message   错误消息
     * @param errorCode 错误码
     * @param platform  平台类型
     * @param cause     原因异常
     */
    public ConnectorException(String message, String errorCode, PlatformType platform, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.platform = platform;
        this.errorData = null;
    }

    /**
     * 完整构造函数
     *
     * @param message   错误消息
     * @param errorCode 错误码
     * @param platform  平台类型
     * @param errorData 额外错误信息
     * @param cause     原因异常
     */
    public ConnectorException(String message, String errorCode, PlatformType platform, Object errorData, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.platform = platform;
        this.errorData = errorData;
    }

    /**
     * 获取格式化的错误信息
     *
     * @return 格式化的错误信息
     */
    public String getFormattedMessage() {
        StringBuilder sb = new StringBuilder();
        
        if (platform != null) {
            sb.append("[").append(platform.getDisplayName()).append("] ");
        }
        
        if (errorCode != null) {
            sb.append("[").append(errorCode).append("] ");
        }
        
        sb.append(getMessage());
        
        return sb.toString();
    }

    @Override
    public String toString() {
        return getClass().getSimpleName() + ": " + getFormattedMessage();
    }
}
