package cn.com.handthing.starter.connector.douyin.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 抖音用户信息响应模型
 * <p>
 * 抖音获取用户信息API的响应数据结构
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DouyinUserInfoResponse {

    /**
     * 错误码
     */
    @JsonProperty("error_code")
    private Integer errorCode;

    /**
     * 错误描述
     */
    @JsonProperty("description")
    private String description;

    /**
     * 用户数据
     */
    @JsonProperty("data")
    private UserData data;

    /**
     * 检查响应是否成功
     *
     * @return 如果成功返回true，否则返回false
     */
    public boolean isSuccess() {
        return errorCode == null || errorCode == 0;
    }

    /**
     * 获取错误描述
     *
     * @return 错误描述
     */
    public String getErrorDescription() {
        if (isSuccess()) {
            return "Success";
        }
        return String.format("Error %d: %s", errorCode, description);
    }

    /**
     * 用户数据
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class UserData {

        /**
         * 用户OpenID
         */
        @JsonProperty("open_id")
        private String openId;

        /**
         * 用户UnionID
         */
        @JsonProperty("union_id")
        private String unionId;

        /**
         * 用户昵称
         */
        @JsonProperty("nickname")
        private String nickname;

        /**
         * 用户头像
         */
        @JsonProperty("avatar")
        private String avatar;

        /**
         * 用户大头像
         */
        @JsonProperty("avatar_larger")
        private String avatarLarger;

        /**
         * 用户性别（0-未知，1-男，2-女）
         */
        @JsonProperty("gender")
        private Integer gender;

        /**
         * 用户所在国家
         */
        @JsonProperty("country")
        private String country;

        /**
         * 用户所在省份
         */
        @JsonProperty("province")
        private String province;

        /**
         * 用户所在城市
         */
        @JsonProperty("city")
        private String city;

        /**
         * 用户头像验证码
         */
        @JsonProperty("captcha")
        private String captcha;

        /**
         * 企业号角色
         */
        @JsonProperty("e_account_role")
        private String eAccountRole;

        /**
         * 获取显示名称
         *
         * @return 显示名称
         */
        public String getDisplayName() {
            if (nickname != null && !nickname.trim().isEmpty()) {
                return nickname;
            }
            return openId;
        }

        /**
         * 获取性别描述
         *
         * @return 性别描述
         */
        public String getGenderDescription() {
            if (gender == null) {
                return "未知";
            }
            switch (gender) {
                case 1:
                    return "男";
                case 2:
                    return "女";
                default:
                    return "未知";
            }
        }
    }
}
