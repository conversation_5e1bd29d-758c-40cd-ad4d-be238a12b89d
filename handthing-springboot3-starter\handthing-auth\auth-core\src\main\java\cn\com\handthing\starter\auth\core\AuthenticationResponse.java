package cn.com.handthing.starter.auth.core;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 认证响应抽象类
 * <p>
 * 定义认证响应的基础结构，包含认证结果、令牌信息、用户信息等通用字段。
 * 各种具体的认证方式可以继承此类并添加特定的响应数据。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
public abstract class AuthenticationResponse {

    /**
     * 认证是否成功
     */
    private boolean success;

    /**
     * 访问令牌
     */
    private String accessToken;

    /**
     * 刷新令牌
     */
    private String refreshToken;

    /**
     * 令牌类型（通常为Bearer）
     */
    private String tokenType = "Bearer";

    /**
     * 令牌过期时间（秒）
     */
    private Long expiresIn;

    /**
     * 授权范围
     */
    private String scope;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 错误代码
     */
    private String errorCode;

    /**
     * 错误描述
     */
    private String errorDescription;

    /**
     * 响应时间戳
     */
    private LocalDateTime timestamp;

    /**
     * 扩展数据
     */
    private Map<String, Object> extraData;

    /**
     * 默认构造函数
     */
    protected AuthenticationResponse() {
        this.timestamp = LocalDateTime.now();
        this.extraData = new HashMap<>();
    }

    /**
     * 成功响应构造函数
     *
     * @param accessToken  访问令牌
     * @param refreshToken 刷新令牌
     * @param expiresIn    过期时间
     */
    protected AuthenticationResponse(String accessToken, String refreshToken, Long expiresIn) {
        this();
        this.success = true;
        this.accessToken = accessToken;
        this.refreshToken = refreshToken;
        this.expiresIn = expiresIn;
    }

    /**
     * 失败响应构造函数
     *
     * @param errorCode        错误代码
     * @param errorDescription 错误描述
     */
    protected AuthenticationResponse(String errorCode, String errorDescription) {
        this();
        this.success = false;
        this.errorCode = errorCode;
        this.errorDescription = errorDescription;
    }

    /**
     * 创建成功响应
     *
     * @param accessToken  访问令牌
     * @param refreshToken 刷新令牌
     * @param expiresIn    过期时间
     * @return 认证响应
     */
    public static AuthenticationResponse success(String accessToken, String refreshToken, Long expiresIn) {
        return new DefaultAuthenticationResponse(accessToken, refreshToken, expiresIn);
    }

    /**
     * 创建失败响应
     *
     * @param errorCode        错误代码
     * @param errorDescription 错误描述
     * @return 认证响应
     */
    public static AuthenticationResponse failure(String errorCode, String errorDescription) {
        return new DefaultAuthenticationResponse(errorCode, errorDescription);
    }

    /**
     * 添加扩展数据
     *
     * @param key   数据键
     * @param value 数据值
     */
    public void addExtraData(String key, Object value) {
        if (extraData == null) {
            extraData = new HashMap<>();
        }
        extraData.put(key, value);
    }

    /**
     * 获取扩展数据
     *
     * @param key 数据键
     * @return 数据值
     */
    public Object getExtraData(String key) {
        return extraData != null ? extraData.get(key) : null;
    }

    /**
     * 获取扩展数据（指定类型）
     *
     * @param key   数据键
     * @param clazz 数据类型
     * @param <T>   泛型类型
     * @return 数据值
     */
    @SuppressWarnings("unchecked")
    public <T> T getExtraData(String key, Class<T> clazz) {
        Object value = getExtraData(key);
        if (value != null && clazz.isInstance(value)) {
            return (T) value;
        }
        return null;
    }

    /**
     * 判断是否为成功响应
     *
     * @return 如果成功返回true，否则返回false
     */
    public boolean isSuccess() {
        return success && accessToken != null && !accessToken.trim().isEmpty();
    }

    /**
     * 判断是否为失败响应
     *
     * @return 如果失败返回true，否则返回false
     */
    public boolean isFailure() {
        return !isSuccess();
    }

    /**
     * 获取完整的错误信息
     *
     * @return 错误信息
     */
    public String getFullErrorMessage() {
        if (errorCode == null && errorDescription == null) {
            return null;
        }
        
        StringBuilder sb = new StringBuilder();
        if (errorCode != null) {
            sb.append("[").append(errorCode).append("]");
        }
        if (errorDescription != null) {
            if (sb.length() > 0) {
                sb.append(" ");
            }
            sb.append(errorDescription);
        }
        
        return sb.toString();
    }

    @Override
    public String toString() {
        if (isSuccess()) {
            return String.format("AuthenticationResponse{success=true, userId='%s', username='%s', tokenType='%s', expiresIn=%d}",
                    userId, username, tokenType, expiresIn);
        } else {
            return String.format("AuthenticationResponse{success=false, errorCode='%s', errorDescription='%s'}",
                    errorCode, errorDescription);
        }
    }

    /**
     * 默认认证响应实现
     */
    private static class DefaultAuthenticationResponse extends AuthenticationResponse {
        
        public DefaultAuthenticationResponse(String accessToken, String refreshToken, Long expiresIn) {
            super(accessToken, refreshToken, expiresIn);
        }
        
        public DefaultAuthenticationResponse(String errorCode, String errorDescription) {
            super(errorCode, errorDescription);
        }
    }
}
