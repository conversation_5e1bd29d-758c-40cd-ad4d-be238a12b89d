{"groups": [{"name": "handthing.connector.bilibili", "type": "cn.com.handthing.starter.connector.bilibili.config.BilibiliConfig", "sourceType": "cn.com.handthing.starter.connector.bilibili.config.BilibiliConfig"}], "properties": [{"name": "handthing.connector.bilibili.analytics-enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用数据分析功能", "sourceType": "cn.com.handthing.starter.connector.bilibili.config.BilibiliConfig"}, {"name": "handthing.connector.bilibili.api-base-url", "type": "java.lang.String", "description": "Bilibili API基础URL", "sourceType": "cn.com.handthing.starter.connector.bilibili.config.BilibiliConfig"}, {"name": "handthing.connector.bilibili.app-key", "type": "java.lang.String", "description": "Bilibili应用Key (App Key)", "sourceType": "cn.com.handthing.starter.connector.bilibili.config.BilibiliConfig"}, {"name": "handthing.connector.bilibili.app-secret", "type": "java.lang.String", "description": "Bilibili应用密钥 (App Secret)", "sourceType": "cn.com.handthing.starter.connector.bilibili.config.BilibiliConfig"}, {"name": "handthing.connector.bilibili.app-type", "type": "java.lang.String", "description": "应用类型 personal: 个人创作者 enterprise: 企业用户 mcn: MCN机构", "sourceType": "cn.com.handthing.starter.connector.bilibili.config.BilibiliConfig"}, {"name": "handthing.connector.bilibili.auth-url", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.bilibili.config.BilibiliConfig"}, {"name": "handthing.connector.bilibili.client-id", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.bilibili.config.BilibiliConfig"}, {"name": "handthing.connector.bilibili.client-secret", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.bilibili.config.BilibiliConfig"}, {"name": "handthing.connector.bilibili.community-enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用社区互动功能", "sourceType": "cn.com.handthing.starter.connector.bilibili.config.BilibiliConfig"}, {"name": "handthing.connector.bilibili.connect-timeout", "type": "java.lang.Integer", "sourceType": "cn.com.handthing.starter.connector.bilibili.config.BilibiliConfig"}, {"name": "handthing.connector.bilibili.enabled", "type": "java.lang.Bo<PERSON>an", "sourceType": "cn.com.handthing.starter.connector.bilibili.config.BilibiliConfig"}, {"name": "handthing.connector.bilibili.extra-config", "type": "java.util.Map<java.lang.String,java.lang.Object>", "sourceType": "cn.com.handthing.starter.connector.bilibili.config.BilibiliConfig"}, {"name": "handthing.connector.bilibili.live-enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用直播功能", "sourceType": "cn.com.handthing.starter.connector.bilibili.config.BilibiliConfig"}, {"name": "handthing.connector.bilibili.max-retries", "type": "java.lang.Integer", "sourceType": "cn.com.handthing.starter.connector.bilibili.config.BilibiliConfig"}, {"name": "handthing.connector.bilibili.mcn-mode", "type": "java.lang.Bo<PERSON>an", "description": "是否为MCN机构模式", "sourceType": "cn.com.handthing.starter.connector.bilibili.config.BilibiliConfig"}, {"name": "handthing.connector.bilibili.read-timeout", "type": "java.lang.Integer", "sourceType": "cn.com.handthing.starter.connector.bilibili.config.BilibiliConfig"}, {"name": "handthing.connector.bilibili.redirect-uri", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.bilibili.config.BilibiliConfig"}, {"name": "handthing.connector.bilibili.sandbox", "type": "java.lang.Bo<PERSON>an", "sourceType": "cn.com.handthing.starter.connector.bilibili.config.BilibiliConfig"}, {"name": "handthing.connector.bilibili.scope", "type": "java.lang.String", "description": "授权范围", "sourceType": "cn.com.handthing.starter.connector.bilibili.config.BilibiliConfig"}, {"name": "handthing.connector.bilibili.test-api-base-url", "type": "java.lang.String", "description": "测试环境API基础URL", "sourceType": "cn.com.handthing.starter.connector.bilibili.config.BilibiliConfig"}, {"name": "handthing.connector.bilibili.test-mode", "type": "java.lang.Bo<PERSON>an", "description": "是否使用测试环境", "sourceType": "cn.com.handthing.starter.connector.bilibili.config.BilibiliConfig"}, {"name": "handthing.connector.bilibili.token-url", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.bilibili.config.BilibiliConfig"}, {"name": "handthing.connector.bilibili.user-info-url", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.bilibili.config.BilibiliConfig"}, {"name": "handthing.connector.bilibili.video-upload-enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用视频上传功能", "sourceType": "cn.com.handthing.starter.connector.bilibili.config.BilibiliConfig"}], "hints": [], "ignored": {"properties": []}}