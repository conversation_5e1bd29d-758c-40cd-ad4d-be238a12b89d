package cn.com.handthing.starter.connector.douyin.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 抖音访问令牌响应模型
 * <p>
 * 抖音获取访问令牌API的响应数据结构
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DouyinAccessTokenResponse {

    /**
     * 错误码
     */
    @JsonProperty("error_code")
    private Integer errorCode;

    /**
     * 错误描述
     */
    @JsonProperty("description")
    private String description;

    /**
     * 访问令牌
     */
    @JsonProperty("access_token")
    private String accessToken;

    /**
     * 刷新令牌
     */
    @JsonProperty("refresh_token")
    private String refreshToken;

    /**
     * 过期时间（秒）
     */
    @JsonProperty("expires_in")
    private Long expiresIn;

    /**
     * 刷新令牌过期时间（秒）
     */
    @JsonProperty("refresh_expires_in")
    private Long refreshExpiresIn;

    /**
     * 授权范围
     */
    @JsonProperty("scope")
    private String scope;

    /**
     * 用户OpenID
     */
    @JsonProperty("open_id")
    private String openId;

    /**
     * 检查响应是否成功
     *
     * @return 如果成功返回true，否则返回false
     */
    public boolean isSuccess() {
        return errorCode == null || errorCode == 0;
    }

    /**
     * 获取错误描述
     *
     * @return 错误描述
     */
    public String getErrorDescription() {
        if (isSuccess()) {
            return "Success";
        }
        return String.format("Error %d: %s", errorCode, description);
    }
}
