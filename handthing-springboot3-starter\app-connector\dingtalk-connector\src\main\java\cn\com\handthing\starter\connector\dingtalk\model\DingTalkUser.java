package cn.com.handthing.starter.connector.dingtalk.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 钉钉用户模型
 * <p>
 * 钉钉用户信息的数据结构
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DingTalkUser {

    /**
     * 用户ID
     */
    @JsonProperty("userid")
    private String userId;

    /**
     * 用户在当前开放应用内的唯一标识
     */
    @JsonProperty("openId")
    private String openId;

    /**
     * 用户在当前开发者企业账号范围内的唯一标识
     */
    @JsonProperty("unionId")
    private String unionId;

    /**
     * 用户姓名
     */
    @JsonProperty("name")
    private String name;

    /**
     * 用户昵称
     */
    @JsonProperty("nick")
    private String nick;

    /**
     * 用户头像
     */
    @JsonProperty("avatar")
    private String avatar;

    /**
     * 用户邮箱
     */
    @JsonProperty("email")
    private String email;

    /**
     * 用户手机号
     */
    @JsonProperty("mobile")
    private String mobile;

    /**
     * 用户职位
     */
    @JsonProperty("title")
    private String title;

    /**
     * 用户工号
     */
    @JsonProperty("jobNumber")
    private String jobNumber;

    /**
     * 用户所在部门ID列表
     */
    @JsonProperty("deptIdList")
    private Long[] deptIdList;

    /**
     * 用户主部门ID
     */
    @JsonProperty("mainDeptId")
    private Long mainDeptId;

    /**
     * 用户状态（1-正常，0-停用）
     */
    @JsonProperty("active")
    private Boolean active;

    /**
     * 是否为管理员
     */
    @JsonProperty("admin")
    private Boolean admin;

    /**
     * 是否为老板
     */
    @JsonProperty("boss")
    private Boolean boss;

    /**
     * 是否为部门负责人
     */
    @JsonProperty("leader")
    private Boolean leader;

    /**
     * 入职时间
     */
    @JsonProperty("hiredDate")
    private Long hiredDate;

    /**
     * 检查用户是否激活
     *
     * @return 如果用户激活返回true，否则返回false
     */
    public boolean isActive() {
        return active != null && active;
    }

    /**
     * 检查用户是否为管理员
     *
     * @return 如果是管理员返回true，否则返回false
     */
    public boolean isAdmin() {
        return admin != null && admin;
    }

    /**
     * 检查用户是否为老板
     *
     * @return 如果是老板返回true，否则返回false
     */
    public boolean isBoss() {
        return boss != null && boss;
    }

    /**
     * 检查用户是否为部门负责人
     *
     * @return 如果是部门负责人返回true，否则返回false
     */
    public boolean isLeader() {
        return leader != null && leader;
    }

    /**
     * 获取显示名称
     *
     * @return 显示名称
     */
    public String getDisplayName() {
        if (name != null && !name.trim().isEmpty()) {
            return name;
        }
        if (nick != null && !nick.trim().isEmpty()) {
            return nick;
        }
        return userId;
    }

    /**
     * 获取用户状态描述
     *
     * @return 状态描述
     */
    public String getStatusDescription() {
        if (active == null) {
            return "未知";
        }
        return active ? "正常" : "停用";
    }
}
