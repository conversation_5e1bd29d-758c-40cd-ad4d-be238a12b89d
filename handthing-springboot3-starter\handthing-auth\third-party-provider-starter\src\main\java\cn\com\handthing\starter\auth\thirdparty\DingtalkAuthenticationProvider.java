package cn.com.handthing.starter.auth.thirdparty;

import cn.com.handthing.starter.auth.core.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * 钉钉认证提供者
 * <p>
 * 实现基于钉钉OAuth2的认证逻辑，支持扫码登录和网页授权登录。
 * 提供完整的钉钉认证流程，包括授权码验证、用户信息获取、自动注册等。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DingtalkAuthenticationProvider implements AuthenticationProvider {

    private final DingtalkUserService dingtalkUserService;
    private final DingtalkApiService dingtalkApiService;
    private final JwtTokenProvider jwtTokenProvider;

    @Override
    public GrantType getSupportedGrantType() {
        return GrantType.DINGTALK;
    }

    @Override
    public boolean supports(AuthenticationRequest request) {
        return request instanceof DingtalkAuthenticationRequest && 
               request.getGrantType() == GrantType.DINGTALK;
    }

    @Override
    public AuthenticationResponse authenticate(AuthenticationRequest request) throws AuthenticationException {
        log.debug("Starting Dingtalk authentication for request: {}", request);

        if (!(request instanceof DingtalkAuthenticationRequest)) {
            throw new AuthenticationException("INVALID_REQUEST", "Invalid authentication request type");
        }

        DingtalkAuthenticationRequest dingtalkRequest = (DingtalkAuthenticationRequest) request;

        try {
            // 预处理
            preAuthenticate(dingtalkRequest);

            // 获取钉钉访问令牌
            String accessToken = getDingtalkAccessToken(dingtalkRequest);

            // 根据授权码获取用户信息
            DingtalkApiService.DingtalkUserResult userResult = dingtalkApiService.getUserInfoByCode(accessToken, dingtalkRequest.getCode());
            if (!userResult.isSuccess()) {
                throw new InvalidCredentialsException("钉钉授权码无效: " + userResult.getErrorMessage());
            }

            // 获取用户详细信息
            DingtalkApiService.DingtalkUserDetailResult userDetail = dingtalkApiService.getUserDetail(accessToken, userResult.getUserId());
            if (!userDetail.isSuccess()) {
                throw new AuthenticationException("DINGTALK_API_ERROR", "获取用户详细信息失败: " + userDetail.getErrorMessage());
            }

            // 查找或创建用户
            UserInfo userInfo = findOrCreateUser(dingtalkRequest, userDetail);

            // 验证用户状态
            validateUserStatus(userInfo);

            // 生成令牌
            String jwtAccessToken = generateAccessToken(userInfo, dingtalkRequest);
            String refreshToken = generateRefreshToken(userInfo, dingtalkRequest);
            Long expiresIn = 7200L; // 2小时

            // 更新登录信息
            updateLoginInfo(userInfo, dingtalkRequest);

            // 清除登录失败记录
            dingtalkUserService.clearLoginFailures(userResult.getUserId(), dingtalkRequest.getAppKey());

            // 创建成功响应
            DingtalkAuthenticationResponse response;
            if (dingtalkRequest.getAutoRegister() && dingtalkUserService.isFirstLogin(userInfo)) {
                response = DingtalkAuthenticationResponse.successForNewUser(jwtAccessToken, refreshToken, expiresIn, userInfo);
            } else {
                response = DingtalkAuthenticationResponse.success(jwtAccessToken, refreshToken, expiresIn, userInfo);
            }

            // 设置额外信息
            setAdditionalInfo(response, userInfo, dingtalkRequest, userDetail);

            log.info("Dingtalk authentication successful for user: {}, appKey: {}", userResult.getUserId(), dingtalkRequest.getAppKey());
            return response;

        } catch (AuthenticationException e) {
            // 记录登录失败
            recordLoginFailure(dingtalkRequest, e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Dingtalk authentication error", e);
            recordLoginFailure(dingtalkRequest, "System error");
            throw new AuthenticationException("AUTHENTICATION_ERROR", "Authentication failed", e);
        }
    }

    @Override
    public boolean validateToken(String token) {
        return jwtTokenProvider.validateToken(token);
    }

    @Override
    public AuthenticationResponse refreshToken(String refreshToken) throws AuthenticationException {
        try {
            JwtClaims claims = jwtTokenProvider.parseToken(refreshToken);
            
            // 验证是否为刷新令牌
            if (!"refresh_token".equals(claims.getTokenType())) {
                throw new AuthenticationException("INVALID_REFRESH_TOKEN", "Invalid refresh token type");
            }

            // 查找用户
            Optional<UserInfo> userOptional = dingtalkUserService.findById(claims.getSubject());
            if (userOptional.isEmpty()) {
                throw new AuthenticationException("USER_NOT_FOUND", "User not found");
            }

            UserInfo userInfo = userOptional.get();

            // 验证用户状态
            validateUserStatus(userInfo);

            // 生成新的访问令牌
            String newAccessToken = generateAccessToken(userInfo, null);
            Long expiresIn = 7200L;

            return DingtalkAuthenticationResponse.success(newAccessToken, refreshToken, expiresIn, userInfo);

        } catch (AuthenticationException e) {
            throw e;
        } catch (Exception e) {
            log.error("Refresh token error", e);
            throw new AuthenticationException("REFRESH_TOKEN_ERROR", "Failed to refresh token", e);
        }
    }

    @Override
    public String getProviderName() {
        return "Dingtalk Authentication Provider";
    }

    @Override
    public boolean isAvailable() {
        return true;
    }

    @Override
    public int getPriority() {
        return 3;
    }

    /**
     * 预处理认证请求
     *
     * @param request 钉钉认证请求
     * @throws AuthenticationException 认证异常
     */
    private void preAuthenticate(DingtalkAuthenticationRequest request) throws AuthenticationException {
        // 验证请求参数
        if (!request.isValid()) {
            throw new AuthenticationException("INVALID_REQUEST", "Invalid authentication request");
        }

        // 验证钉钉配置
        if (!dingtalkApiService.validateConfig(request.getAppKey(), request.getAppSecret())) {
            throw new AuthenticationException("INVALID_DINGTALK_CONFIG", "Invalid Dingtalk configuration");
        }
    }

    /**
     * 获取钉钉访问令牌
     *
     * @param request 钉钉认证请求
     * @return 访问令牌
     * @throws AuthenticationException 认证异常
     */
    private String getDingtalkAccessToken(DingtalkAuthenticationRequest request) throws AuthenticationException {
        DingtalkApiService.DingtalkTokenResult tokenResult = dingtalkApiService.getAccessToken(
                request.getAppKey(), request.getAppSecret());
        
        if (!tokenResult.isSuccess()) {
            throw new AuthenticationException("DINGTALK_TOKEN_ERROR", 
                    "获取钉钉访问令牌失败: " + tokenResult.getErrorMessage());
        }

        return tokenResult.getAccessToken();
    }

    /**
     * 查找或创建用户
     *
     * @param request    钉钉认证请求
     * @param userDetail 钉钉用户详细信息
     * @return 用户信息
     * @throws AuthenticationException 认证异常
     */
    private UserInfo findOrCreateUser(DingtalkAuthenticationRequest request, 
                                     DingtalkApiService.DingtalkUserDetailResult userDetail) throws AuthenticationException {
        
        Optional<UserInfo> userOptional = dingtalkUserService.findByDingtalkUserId(userDetail.getUserId(), request.getAppKey());

        if (userOptional.isPresent()) {
            return userOptional.get();
        }

        // 尝试根据手机号或邮箱查找现有用户
        if (userDetail.getMobile() != null) {
            userOptional = dingtalkUserService.findByMobile(userDetail.getMobile());
            if (userOptional.isPresent()) {
                // 绑定钉钉用户
                UserInfo existingUser = userOptional.get();
                dingtalkUserService.bindDingtalkUser(existingUser.getUserId(), userDetail.getUserId(), request.getAppKey());
                return existingUser;
            }
        }

        if (userDetail.getEmail() != null) {
            userOptional = dingtalkUserService.findByEmail(userDetail.getEmail());
            if (userOptional.isPresent()) {
                // 绑定钉钉用户
                UserInfo existingUser = userOptional.get();
                dingtalkUserService.bindDingtalkUser(existingUser.getUserId(), userDetail.getUserId(), request.getAppKey());
                return existingUser;
            }
        }

        // 用户不存在
        if (!request.getAutoRegister()) {
            throw AuthenticationFailedException.userNotFound(userDetail.getUserId());
        }

        // 自动注册新用户
        try {
            UserInfo newUser = dingtalkUserService.createUser(userDetail, request.getAppKey());
            log.info("Auto-registered new user for dingtalk userId: {}, appKey: {}", 
                    userDetail.getUserId(), request.getAppKey());
            return newUser;

        } catch (Exception e) {
            log.error("Failed to create user for dingtalk userId: {}, appKey: {}", 
                    userDetail.getUserId(), request.getAppKey(), e);
            throw new AuthenticationException("REGISTRATION_FAILED", "Failed to create user", e);
        }
    }

    /**
     * 验证用户状态
     *
     * @param userInfo 用户信息
     * @throws AuthenticationException 认证异常
     */
    private void validateUserStatus(UserInfo userInfo) throws AuthenticationException {
        // 检查用户是否有效
        if (!dingtalkUserService.isUserValid(userInfo)) {
            throw AuthenticationFailedException.userDisabled(userInfo.getUsername());
        }

        // 检查用户是否被锁定
        if (dingtalkUserService.isUserLocked(userInfo)) {
            throw AuthenticationFailedException.userLocked(userInfo.getUsername());
        }

        // 检查用户是否被禁用
        if (dingtalkUserService.isUserDisabled(userInfo)) {
            throw AuthenticationFailedException.userDisabled(userInfo.getUsername());
        }

        // 检查账户是否过期
        if (dingtalkUserService.isAccountExpired(userInfo)) {
            throw new AuthenticationException("ACCOUNT_EXPIRED", "Account has expired");
        }
    }

    /**
     * 生成访问令牌
     *
     * @param userInfo 用户信息
     * @param request  认证请求
     * @return 访问令牌
     */
    private String generateAccessToken(UserInfo userInfo, DingtalkAuthenticationRequest request) {
        JwtClaims claims = JwtClaims.builder()
                .subject(userInfo.getUserId())
                .username(userInfo.getUsername())
                .nickname(userInfo.getNickname())
                .email(userInfo.getEmail())
                .phone(userInfo.getPhone())
                .roles(userInfo.getRoles())
                .permissions(userInfo.getPermissions())
                .grantType(GrantType.DINGTALK.getCode())
                .tokenType("access_token")
                .build();

        if (request != null) {
            claims.setClientId(request.getClientId());
            claims.setScope(request.getScope());
            claims.setIpAddress(request.getIpAddress());
            claims.setUserAgent(request.getUserAgent());
            claims.setDeviceId(request.getDeviceId());
            
            // 添加钉钉相关信息
            claims.addCustomClaim("app_key", request.getAppKey());
            claims.addCustomClaim("corp_id", request.getCorpId());
        }

        return jwtTokenProvider.generateAccessToken(claims);
    }

    /**
     * 生成刷新令牌
     *
     * @param userInfo 用户信息
     * @param request  认证请求
     * @return 刷新令牌
     */
    private String generateRefreshToken(UserInfo userInfo, DingtalkAuthenticationRequest request) {
        JwtClaims claims = JwtClaims.builder()
                .subject(userInfo.getUserId())
                .username(userInfo.getUsername())
                .grantType(GrantType.DINGTALK.getCode())
                .tokenType("refresh_token")
                .build();

        if (request != null) {
            claims.setClientId(request.getClientId());
            claims.addCustomClaim("app_key", request.getAppKey());
        }

        return jwtTokenProvider.generateRefreshToken(claims);
    }

    /**
     * 更新登录信息
     *
     * @param userInfo 用户信息
     * @param request  认证请求
     */
    private void updateLoginInfo(UserInfo userInfo, DingtalkAuthenticationRequest request) {
        dingtalkUserService.updateLastLoginInfo(
                userInfo.getUserId(),
                LocalDateTime.now(),
                request.getIpAddress(),
                request.getUserAgent()
        );
    }

    /**
     * 设置额外信息
     *
     * @param response   认证响应
     * @param userInfo   用户信息
     * @param request    认证请求
     * @param userDetail 钉钉用户详细信息
     */
    private void setAdditionalInfo(DingtalkAuthenticationResponse response, UserInfo userInfo, 
                                  DingtalkAuthenticationRequest request, DingtalkApiService.DingtalkUserDetailResult userDetail) {
        response.setFirstLogin(dingtalkUserService.isFirstLogin(userInfo));
        response.setLastLoginTime(userInfo.getLastLoginTime());
        response.setLastLoginIp(userInfo.getLastLoginIp());
        
        // 设置钉钉相关信息
        response.setDingtalkUserInfo(userDetail.getUserId(), userDetail.getName(), userDetail.getAvatar(), 
                request.getAppKey(), request.getCorpId());
    }

    /**
     * 记录登录失败
     *
     * @param request 认证请求
     * @param reason  失败原因
     */
    private void recordLoginFailure(DingtalkAuthenticationRequest request, String reason) {
        try {
            // 这里使用钉钉用户标识记录失败
            String identifier = request.getAppKey() + ":" + request.getCode();
            dingtalkUserService.recordLoginFailure(identifier, request.getAppKey(), request.getIpAddress(), reason);
        } catch (Exception e) {
            log.error("Failed to record login failure", e);
        }
    }
}
