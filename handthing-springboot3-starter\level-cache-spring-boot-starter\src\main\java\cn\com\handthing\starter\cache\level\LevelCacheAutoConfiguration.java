package cn.com.handthing.starter.cache.level;

import cn.com.handthing.starter.cache.CacheService;
import cn.com.handthing.starter.cache.level.config.LevelCacheProperties;
import cn.com.handthing.starter.cache.level.sync.CacheSyncService;
// import cn.com.handthing.starter.cache.level.health.LevelCacheHealthIndicator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
// import org.springframework.boot.actuator.health.HealthIndicator;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cache.CacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;

import java.net.InetAddress;
import java.util.UUID;

/**
 * 多级缓存自动配置类
 * <p>
 * 当缓存类型配置为level时，自动配置多级缓存管理器和服务
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@AutoConfiguration
@EnableConfigurationProperties(LevelCacheProperties.class)
@ConditionalOnProperty(prefix = "handthing.cache", name = "type", havingValue = "level")
public class LevelCacheAutoConfiguration {

    /**
     * 创建Redis消息监听容器（用于缓存同步）
     *
     * @param connectionFactory Redis连接工厂
     * @return Redis消息监听容器
     */
    @Bean
    @ConditionalOnClass(RedisConnectionFactory.class)
    @ConditionalOnProperty(prefix = "handthing.cache.level.sync", name = "enabled", havingValue = "true", matchIfMissing = true)
    public RedisMessageListenerContainer redisMessageListenerContainer(RedisConnectionFactory connectionFactory) {
        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(connectionFactory);
        log.info("Created RedisMessageListenerContainer for cache sync");
        return container;
    }

    /**
     * 创建缓存同步服务
     *
     * @param properties                    多级缓存配置属性
     * @param redisTemplate                Redis模板
     * @param messageListenerContainer     消息监听容器
     * @return 缓存同步服务
     */
    @Bean
    @ConditionalOnClass({RedisTemplate.class, RedisMessageListenerContainer.class})
    @ConditionalOnProperty(prefix = "handthing.cache.level.sync", name = "enabled", havingValue = "true", matchIfMissing = true)
    public CacheSyncService cacheSyncService(LevelCacheProperties properties,
                                           RedisTemplate<String, Object> redisTemplate,
                                           RedisMessageListenerContainer messageListenerContainer) {
        String instanceId = generateInstanceId();
        log.info("Creating CacheSyncService with instanceId: {}", instanceId);
        return new CacheSyncService(properties, redisTemplate, messageListenerContainer, instanceId);
    }

    /**
     * 创建多级缓存管理器
     *
     * @param properties      多级缓存配置属性
     * @param l1CacheManager  L1缓存管理器
     * @param l2CacheManager  L2缓存管理器
     * @param syncService     同步服务（可选）
     * @return 多级缓存管理器
     */
    @Bean
    @Primary
    @ConditionalOnMissingBean(name = "levelCacheManager")
    public LevelCacheManager levelCacheManager(LevelCacheProperties properties,
                                              @Qualifier("l1CacheManager") CacheManager l1CacheManager,
                                              @Qualifier("l2CacheManager") CacheManager l2CacheManager,
                                              @Qualifier("cacheSyncService") CacheSyncService syncService) {
        log.info("Creating LevelCacheManager with L1: {}, L2: {}, sync: {}", 
                l1CacheManager.getClass().getSimpleName(),
                l2CacheManager.getClass().getSimpleName(),
                syncService != null ? "enabled" : "disabled");
        return new LevelCacheManager(properties, l1CacheManager, l2CacheManager, syncService);
    }

    /**
     * 创建多级缓存服务
     *
     * @param cacheManager 多级缓存管理器
     * @return 多级缓存服务
     */
    @Bean
    @Primary
    @ConditionalOnMissingBean(CacheService.class)
    public CacheService levelCacheService(LevelCacheManager cacheManager) {
        log.info("Creating LevelCacheService with CacheManager: {}", 
                cacheManager.getClass().getSimpleName());
        return new LevelCacheService(cacheManager);
    }

    /*
     * 创建多级缓存健康检查指示器
     *
     * @param cacheService 多级缓存服务
     * @param cacheManager 多级缓存管理器
     * @return 健康检查指示器
     */
    /*
    @Bean
    @ConditionalOnClass(HealthIndicator.class)
    @ConditionalOnMissingBean(name = "levelCacheHealthIndicator")
    @ConditionalOnProperty(prefix = "management.health.levelcache", name = "enabled", havingValue = "true", matchIfMissing = true)
    public HealthIndicator levelCacheHealthIndicator(LevelCacheService cacheService, LevelCacheManager cacheManager) {
        log.info("Creating LevelCacheHealthIndicator");
        return new LevelCacheHealthIndicator(cacheService, cacheManager);
    }
    */

    /**
     * 生成实例ID
     *
     * @return 实例ID
     */
    private String generateInstanceId() {
        try {
            String hostName = InetAddress.getLocalHost().getHostName();
            String uuid = UUID.randomUUID().toString().substring(0, 8);
            return hostName + "-" + uuid;
        } catch (Exception e) {
            log.warn("Failed to get hostname, using UUID as instance ID", e);
            return UUID.randomUUID().toString();
        }
    }
}