package cn.com.handthing.starter.connector.douyin.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 抖音视频模型
 * <p>
 * 抖音视频信息的数据结构
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DouyinVideo {

    /**
     * 视频ID
     */
    @JsonProperty("item_id")
    private String itemId;

    /**
     * 视频标题
     */
    @JsonProperty("title")
    private String title;

    /**
     * 视频封面
     */
    @JsonProperty("cover")
    private String cover;

    /**
     * 视频播放地址
     */
    @JsonProperty("video_url")
    private String videoUrl;

    /**
     * 视频时长（秒）
     */
    @JsonProperty("duration")
    private Integer duration;

    /**
     * 创建时间
     */
    @JsonProperty("create_time")
    private Long createTime;

    /**
     * 是否置顶
     */
    @JsonProperty("is_top")
    private Boolean isTop;

    /**
     * 视频状态
     */
    @JsonProperty("video_status")
    private Integer videoStatus;

    /**
     * 分享URL
     */
    @JsonProperty("share_url")
    private String shareUrl;

    /**
     * 统计数据
     */
    @JsonProperty("statistics")
    private VideoStatistics statistics;

    /**
     * 检查视频是否可用
     *
     * @return 如果视频可用返回true，否则返回false
     */
    public boolean isAvailable() {
        return videoStatus != null && videoStatus == 1;
    }

    /**
     * 检查视频是否置顶
     *
     * @return 如果视频置顶返回true，否则返回false
     */
    public boolean isTop() {
        return isTop != null && isTop;
    }

    /**
     * 获取视频状态描述
     *
     * @return 状态描述
     */
    public String getVideoStatusDescription() {
        if (videoStatus == null) {
            return "未知";
        }
        switch (videoStatus) {
            case 1:
                return "已发布";
            case 2:
                return "不适宜公开";
            case 4:
                return "审核中";
            default:
                return "未知状态";
        }
    }

    /**
     * 视频统计数据
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class VideoStatistics {

        /**
         * 点赞数
         */
        @JsonProperty("digg_count")
        private Long diggCount;

        /**
         * 下载数
         */
        @JsonProperty("download_count")
        private Long downloadCount;

        /**
         * 转发数
         */
        @JsonProperty("forward_count")
        private Long forwardCount;

        /**
         * 播放数
         */
        @JsonProperty("play_count")
        private Long playCount;

        /**
         * 分享数
         */
        @JsonProperty("share_count")
        private Long shareCount;

        /**
         * 评论数
         */
        @JsonProperty("comment_count")
        private Long commentCount;

        /**
         * 获取总互动数
         *
         * @return 总互动数
         */
        public Long getTotalInteraction() {
            long total = 0;
            if (diggCount != null) total += diggCount;
            if (commentCount != null) total += commentCount;
            if (shareCount != null) total += shareCount;
            if (forwardCount != null) total += forwardCount;
            return total;
        }

        /**
         * 获取互动率（相对于播放数）
         *
         * @return 互动率（百分比）
         */
        public Double getInteractionRate() {
            if (playCount == null || playCount == 0) {
                return 0.0;
            }
            return (getTotalInteraction().doubleValue() / playCount.doubleValue()) * 100;
        }
    }
}
