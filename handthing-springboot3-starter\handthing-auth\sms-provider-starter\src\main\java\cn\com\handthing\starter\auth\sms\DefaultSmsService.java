package cn.com.handthing.starter.auth.sms;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 默认短信服务实现
 * <p>
 * 提供基于内存的短信服务实现，主要用于测试和演示。
 * 生产环境建议实现自己的SmsService来对接实际的短信服务商。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Service
@ConditionalOnMissingBean(SmsService.class)
public class DefaultSmsService implements SmsService {

    /**
     * 验证码存储（内存）
     */
    private final Map<String, SmsCodeInfo> codeStorage = new ConcurrentHashMap<>();

    /**
     * 发送历史记录
     */
    private final Map<String, java.util.List<SendRecord>> sendHistory = new ConcurrentHashMap<>();

    /**
     * 随机数生成器
     */
    private final Random random = new Random();

    @Override
    public SmsResult sendSmsCode(String phone, String code, String codeType, String template) {
        try {
            // 检查发送频率限制
            if (!checkSendLimit(phone)) {
                return SmsResult.failure("发送过于频繁，请稍后再试");
            }

            // 模拟短信发送（实际应用中应该调用短信服务商API）
            String message = template.replace("{code}", code);
            log.info("Sending SMS to {}: {}", phone, message);

            // 存储验证码
            LocalDateTime expireTime = LocalDateTime.now().plusMinutes(5);
            storeSmsCode(phone, code, codeType, expireTime);

            // 记录发送历史
            recordSendHistory(phone, codeType, true);

            // 模拟成功响应
            String requestId = "SMS_" + System.currentTimeMillis();
            log.info("SMS sent successfully to {}, requestId: {}", phone, requestId);
            
            return SmsResult.success(requestId);

        } catch (Exception e) {
            log.error("Failed to send SMS to {}", phone, e);
            recordSendHistory(phone, codeType, false);
            return SmsResult.failure("短信发送失败: " + e.getMessage());
        }
    }

    @Override
    public boolean verifySmsCode(String phone, String code, String codeType) {
        try {
            SmsCodeInfo codeInfo = getSmsCode(phone, codeType);
            
            if (codeInfo == null) {
                log.debug("SMS code not found for phone: {}, codeType: {}", phone, codeType);
                return false;
            }

            if (codeInfo.isUsed()) {
                log.debug("SMS code already used for phone: {}", phone);
                return false;
            }

            if (codeInfo.isExpired()) {
                log.debug("SMS code expired for phone: {}", phone);
                return false;
            }

            if (!code.equals(codeInfo.getCode())) {
                log.debug("SMS code mismatch for phone: {}", phone);
                return false;
            }

            // 标记为已使用
            codeInfo.setUsed(true);
            log.debug("SMS code verified successfully for phone: {}", phone);
            return true;

        } catch (Exception e) {
            log.error("Failed to verify SMS code for phone: {}", phone, e);
            return false;
        }
    }

    @Override
    public String generateSmsCode(int length) {
        StringBuilder code = new StringBuilder();
        for (int i = 0; i < length; i++) {
            code.append(random.nextInt(10));
        }
        return code.toString();
    }

    @Override
    public void storeSmsCode(String phone, String code, String codeType, LocalDateTime expireTime) {
        String key = buildCodeKey(phone, codeType);
        SmsCodeInfo codeInfo = new SmsCodeInfo(code, codeType, LocalDateTime.now(), expireTime);
        codeStorage.put(key, codeInfo);
        log.debug("Stored SMS code for phone: {}, codeType: {}, expireTime: {}", phone, codeType, expireTime);
    }

    @Override
    public SmsCodeInfo getSmsCode(String phone, String codeType) {
        String key = buildCodeKey(phone, codeType);
        return codeStorage.get(key);
    }

    @Override
    public void deleteSmsCode(String phone, String codeType) {
        String key = buildCodeKey(phone, codeType);
        codeStorage.remove(key);
        log.debug("Deleted SMS code for phone: {}, codeType: {}", phone, codeType);
    }

    @Override
    public boolean checkSendLimit(String phone) {
        // 检查1分钟内是否已发送
        java.util.List<SendRecord> records = sendHistory.get(phone);
        if (records != null) {
            LocalDateTime oneMinuteAgo = LocalDateTime.now().minusMinutes(1);
            long recentCount = records.stream()
                    .filter(record -> record.getSendTime().isAfter(oneMinuteAgo))
                    .count();
            
            if (recentCount > 0) {
                log.debug("Send limit exceeded for phone: {}, recent count: {}", phone, recentCount);
                return false;
            }
        }

        // 检查今日发送次数
        int todayCount = getTodaySendCount(phone);
        if (todayCount >= 10) { // 每日最多10次
            log.debug("Daily send limit exceeded for phone: {}, today count: {}", phone, todayCount);
            return false;
        }

        return true;
    }

    @Override
    public void recordSendHistory(String phone, String codeType, boolean success) {
        sendHistory.computeIfAbsent(phone, k -> new java.util.ArrayList<>())
                   .add(new SendRecord(codeType, LocalDateTime.now(), success));
    }

    @Override
    public int getTodaySendCount(String phone) {
        java.util.List<SendRecord> records = sendHistory.get(phone);
        if (records == null) {
            return 0;
        }

        LocalDateTime startOfDay = LocalDateTime.now().toLocalDate().atStartOfDay();
        return (int) records.stream()
                .filter(record -> record.getSendTime().isAfter(startOfDay))
                .filter(SendRecord::isSuccess)
                .count();
    }

    /**
     * 构建验证码存储键
     *
     * @param phone    手机号
     * @param codeType 验证码类型
     * @return 存储键
     */
    private String buildCodeKey(String phone, String codeType) {
        return phone + ":" + codeType;
    }

    /**
     * 发送记录
     */
    private static class SendRecord {
        private final String codeType;
        private final LocalDateTime sendTime;
        private final boolean success;

        public SendRecord(String codeType, LocalDateTime sendTime, boolean success) {
            this.codeType = codeType;
            this.sendTime = sendTime;
            this.success = success;
        }

        public String getCodeType() {
            return codeType;
        }

        public LocalDateTime getSendTime() {
            return sendTime;
        }

        public boolean isSuccess() {
            return success;
        }
    }

    /**
     * 清理过期的验证码（定时任务可以调用）
     */
    public void cleanExpiredCodes() {
        LocalDateTime now = LocalDateTime.now();
        codeStorage.entrySet().removeIf(entry -> entry.getValue().getExpireTime().isBefore(now));
        log.debug("Cleaned expired SMS codes, remaining: {}", codeStorage.size());
    }

    /**
     * 获取统计信息
     *
     * @return 统计信息
     */
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new java.util.HashMap<>();
        stats.put("totalCodes", codeStorage.size());
        stats.put("totalPhones", sendHistory.size());
        
        int totalSends = sendHistory.values().stream()
                .mapToInt(java.util.List::size)
                .sum();
        stats.put("totalSends", totalSends);
        
        return stats;
    }
}
