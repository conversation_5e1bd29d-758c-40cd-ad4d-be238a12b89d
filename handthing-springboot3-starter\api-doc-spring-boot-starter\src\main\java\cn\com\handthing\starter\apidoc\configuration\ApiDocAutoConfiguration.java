package cn.com.handthing.starter.apidoc.configuration;

import cn.com.handthing.starter.apidoc.properties.ApiDocProperties;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.OpenAPI;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.BeanFactoryAware;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.AutoConfigurationPackages;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * API 文档自动配置类。
 */
@Configuration
@ConditionalOnWebApplication(type = ConditionalOnWebApplication.Type.SERVLET)
@ConditionalOnProperty(prefix = "handthing.api-doc", name = "enabled", havingValue = "true", matchIfMissing = true)
@EnableConfigurationProperties(ApiDocProperties.class)
@Import(ApiDocSecurityConfiguration.class)
// 实现 BeanFactoryAware 接口以获取 BeanFactory
public class ApiDocAutoConfiguration implements BeanFactoryAware {

    private static final Logger log = LoggerFactory.getLogger(ApiDocAutoConfiguration.class);

    private BeanFactory beanFactory;

    @Value("${spring.application.name:default}")
    private String applicationName;

    @Override
    public void setBeanFactory(BeanFactory beanFactory) throws BeansException {
        this.beanFactory = beanFactory;
    }

    @Bean
    public OpenAPI customOpenAPI(ApiDocProperties properties) {
        return new OpenAPI()
                .info(new Info()
                        .title(properties.getTitle())
                        .version(properties.getVersion())
                        .description(properties.getDescription()));
    }

    @Bean
    public GroupedOpenApi defaultGroup() {
        return GroupedOpenApi.builder()
                .group(applicationName)
                .packagesToScan(getPackagesToScan()) // 调用改进后的方法
                .build();
    }

    /**
     * 动态获取 @SpringBootApplication 所在的主类包名。
     * 这是实现通用 Starter 的标准、可靠方式。
     *
     * @return 主应用程序的包名数组
     */
    private String[] getPackagesToScan() {
        if (this.beanFactory == null) {
            log.warn("BeanFactory is not available. Cannot determine auto-configuration packages. Scanning all packages.");
            return new String[0]; // 返回空数组，springdoc 会扫描所有包
        }
        
        // 从 BeanFactory 中获取由 Spring Boot 自动注册的包列表
        List<String> packages = AutoConfigurationPackages.get(this.beanFactory);
        
        if (CollectionUtils.isEmpty(packages)) {
            log.warn("Could not find auto-configuration packages. Scanning all packages.");
            return new String[0];
        }

        log.info("API Doc Starter will scan the following base packages: {}", packages);
        return packages.toArray(new String[0]);
    }
}