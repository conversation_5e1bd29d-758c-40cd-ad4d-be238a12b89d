package cn.com.handthing.starter.crypto.core;

import cn.com.handthing.starter.crypto.exception.CryptoException;
import cn.com.handthing.starter.crypto.properties.CryptoProperties.KeyStoreConfig;
import org.springframework.util.Assert;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;

/**
 * 从文件系统加载原始密钥字节的 KeyProvider。
 *
 * <AUTHOR>
 * @since V1.0.0
 */
class FileKeyProvider extends AbstractKeyProvider {

    private final byte[] keyBytes;

    public FileKeyProvider(KeyStoreConfig config) {
        super(config);
        Assert.hasText(config.getPath(), "path must not be empty for KeyStoreType.FILE");
        try {
            this.keyBytes = Files.readAllBytes(Paths.get(config.getPath()));
            this.ready = true;
        } catch (IOException e) {
            throw new CryptoException("Failed to read key from file path: " + config.getPath(), e);
        }
    }

    @Override
    public byte[] getKeyBytes() {
        return this.keyBytes;
    }
}