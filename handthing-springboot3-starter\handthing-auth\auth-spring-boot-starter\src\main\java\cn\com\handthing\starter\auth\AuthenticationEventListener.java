package cn.com.handthing.starter.auth;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;

/**
 * 认证事件监听器
 * <p>
 * 监听认证过程中的各种事件，提供日志记录、指标收集等功能。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@RequiredArgsConstructor
public class AuthenticationEventListener {

    private final AuthProperties authProperties;

    /**
     * 监听认证开始事件
     *
     * @param event 认证开始事件
     */
    @EventListener
    public void handleAuthenticationStarted(AuthenticationBus.AuthenticationStartedEvent event) {
        if (authProperties.isLoggingEnabled()) {
            log.info("Authentication started: {}", event.getContext().getDescription());
        }
    }

    /**
     * 监听认证成功事件
     *
     * @param event 认证成功事件
     */
    @EventListener
    public void handleAuthenticationSuccess(AuthenticationBus.AuthenticationSuccessEvent event) {
        if (authProperties.isLoggingEnabled()) {
            log.info("Authentication successful: {}", event.getContext().getDescription());
        }
    }

    /**
     * 监听认证失败事件
     *
     * @param event 认证失败事件
     */
    @EventListener
    public void handleAuthenticationFailed(AuthenticationBus.AuthenticationFailedEvent event) {
        if (authProperties.isLoggingEnabled()) {
            log.warn("Authentication failed: {}", event.getContext().getDescription());
        }
    }

    /**
     * 监听认证错误事件
     *
     * @param event 认证错误事件
     */
    @EventListener
    public void handleAuthenticationError(AuthenticationBus.AuthenticationErrorEvent event) {
        if (authProperties.isLoggingEnabled()) {
            log.error("Authentication error: {}", event.getContext().getDescription());
        }
    }

    /**
     * 监听认证完成事件
     *
     * @param event 认证完成事件
     */
    @EventListener
    public void handleAuthenticationCompleted(AuthenticationBus.AuthenticationCompletedEvent event) {
        if (authProperties.isLoggingEnabled()) {
            log.debug("Authentication completed: {}", event.getContext().getDescription());
        }
    }
}
