<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>飞书授权 - HandThing认证测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card shadow mt-5">
                    <div class="card-header bg-info text-white text-center">
                        <h4 class="mb-0">
                            <i class="bi bi-chat-square-text"></i>
                            飞书授权
                        </h4>
                    </div>
                    <div class="card-body text-center">
                        <div id="loading" class="mb-3">
                            <div class="spinner-border text-info" role="status">
                                <span class="visually-hidden">处理中...</span>
                            </div>
                            <p class="mt-2">正在处理飞书授权...</p>
                        </div>

                        <div id="result" style="display: none;">
                            <div id="success" class="alert alert-success" style="display: none;">
                                <i class="bi bi-check-circle"></i>
                                <strong>授权成功！</strong>
                                <p class="mb-0">正在跳转到首页...</p>
                            </div>

                            <div id="error" class="alert alert-danger" style="display: none;">
                                <i class="bi bi-exclamation-triangle"></i>
                                <strong>授权失败</strong>
                                <p id="errorMessage" class="mb-0"></p>
                            </div>
                        </div>

                        <div class="mt-3">
                            <a href="/login" class="btn btn-outline-primary">
                                <i class="bi bi-arrow-left"></i> 返回登录
                            </a>
                        </div>
                    </div>
                </div>

                <!-- 调试信息 -->
                <div class="card shadow mt-3">
                    <div class="card-header">
                        <h6 class="mb-0">调试信息</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">
                                <strong>授权码:</strong>
                            </div>
                            <div class="col-6">
                                <code th:text="${code ?: '无'}">-</code>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-6">
                                <strong>状态参数:</strong>
                            </div>
                            <div class="col-6">
                                <code th:text="${state ?: '无'}">-</code>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-12">
                                <div id="debugInfo">
                                    <pre class="bg-light p-2 rounded small"></pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script th:inline="javascript">
        // 获取URL参数
        const urlParams = new URLSearchParams(window.location.search);
        const code = /*[[${code}]]*/ urlParams.get('code');
        const state = /*[[${state}]]*/ urlParams.get('state');

        // 显示调试信息
        const debugInfo = {
            code: code,
            state: state,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent
        };
        document.querySelector('#debugInfo pre').textContent = JSON.stringify(debugInfo, null, 2);

        // 如果有授权码，进行登录
        if (code) {
            // 延迟1秒显示处理效果
            setTimeout(() => {
                performFeishuLogin(code, state);
            }, 1000);
        } else {
            showError('未获取到飞书授权码');
        }

        function performFeishuLogin(code, state) {
            const loginData = {
                grant_type: 'feishu',
                code: code,
                app_id: 'test_app_id',
                app_secret: 'test_app_secret',
                auto_register: true,
                state: state
            };

            fetch('/auth/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(loginData)
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('result').style.display = 'block';

                if (data.success) {
                    document.getElementById('success').style.display = 'block';
                    // 2秒后跳转到首页
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 2000);
                } else {
                    showError(data.error_description || '飞书登录失败');
                }
            })
            .catch(error => {
                showError('网络请求失败: ' + error.message);
            });
        }

        function showError(message) {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('result').style.display = 'block';
            document.getElementById('error').style.display = 'block';
            document.getElementById('errorMessage').textContent = message;
        }
    </script>
</body>
</html>
