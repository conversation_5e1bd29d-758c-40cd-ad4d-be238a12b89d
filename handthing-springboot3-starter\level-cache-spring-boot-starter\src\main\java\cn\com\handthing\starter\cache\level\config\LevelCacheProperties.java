package cn.com.handthing.starter.cache.level.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.time.Duration;

/**
 * 多级缓存配置属性
 * <p>
 * 配置L1(本地缓存)和L2(远程缓存)的多级缓存架构
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
@ConfigurationProperties(prefix = "handthing.cache.level")
public class LevelCacheProperties {

    /**
     * 是否启用多级缓存
     */
    private boolean enabled = true;

    /**
     * L1缓存配置
     */
    private L1Config l1 = new L1Config();

    /**
     * L2缓存配置
     */
    private L2Config l2 = new L2Config();

    /**
     * 缓存同步配置
     */
    private SyncConfig sync = new SyncConfig();

    /**
     * 一致性策略
     */
    private ConsistencyStrategy consistencyStrategy = ConsistencyStrategy.EVENTUAL;

    /**
     * L1缓存配置
     */
    @Data
    public static class L1Config {

        /**
         * L1缓存类型 (caffeine, memory)
         */
        private String type = "caffeine";

        /**
         * 是否启用L1缓存
         */
        private boolean enabled = true;

        /**
         * L1缓存最大条目数
         */
        private long maxSize = 1000L;

        /**
         * L1缓存过期时间
         */
        private Duration ttl = Duration.ofMinutes(10);

        /**
         * Caffeine配置规格字符串
         */
        private String caffeineSpec = "initialCapacity=50,maximumSize=1000,expireAfterWrite=10m";
    }

    /**
     * L2缓存配置
     */
    @Data
    public static class L2Config {

        /**
         * L2缓存类型 (redis)
         */
        private String type = "redis";

        /**
         * 是否启用L2缓存
         */
        private boolean enabled = true;

        /**
         * L2缓存过期时间
         */
        private Duration ttl = Duration.ofMinutes(30);

        /**
         * Redis键前缀
         */
        private String keyPrefix = "level-cache:";
    }

    /**
     * 缓存同步配置
     */
    @Data
    public static class SyncConfig {

        /**
         * 是否启用缓存同步
         */
        private boolean enabled = true;

        /**
         * 同步主题名称
         */
        private String topic = "cache-sync";

        /**
         * 同步消息超时时间
         */
        private Duration timeout = Duration.ofSeconds(5);

        /**
         * 是否启用消息去重
         */
        private boolean enableDeduplication = true;

        /**
         * 消息去重窗口时间
         */
        private Duration deduplicationWindow = Duration.ofSeconds(10);
    }

    /**
     * 一致性策略枚举
     */
    public enum ConsistencyStrategy {
        /**
         * 最终一致性 - 异步同步，性能最好
         */
        EVENTUAL,

        /**
         * 强一致性 - 同步操作，一致性最好
         */
        STRONG,

        /**
         * 会话一致性 - 同一会话内保证一致性
         */
        SESSION
    }
}