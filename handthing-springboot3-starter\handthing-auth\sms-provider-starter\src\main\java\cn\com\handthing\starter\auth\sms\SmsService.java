package cn.com.handthing.starter.auth.sms;

import java.time.LocalDateTime;

/**
 * 短信服务接口
 * <p>
 * 定义短信发送和验证码管理的核心接口。
 * 业务系统需要实现此接口来提供具体的短信发送和验证码存储逻辑。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
public interface SmsService {

    /**
     * 发送短信验证码
     *
     * @param phone    手机号
     * @param code     验证码
     * @param codeType 验证码类型（login-登录，register-注册）
     * @param template 短信模板
     * @return 发送结果
     */
    SmsResult sendSmsCode(String phone, String code, String codeType, String template);

    /**
     * 验证短信验证码
     *
     * @param phone    手机号
     * @param code     验证码
     * @param codeType 验证码类型
     * @return 如果验证通过返回true，否则返回false
     */
    boolean verifySmsCode(String phone, String code, String codeType);

    /**
     * 生成短信验证码
     *
     * @param length 验证码长度
     * @return 验证码
     */
    String generateSmsCode(int length);

    /**
     * 存储短信验证码
     *
     * @param phone      手机号
     * @param code       验证码
     * @param codeType   验证码类型
     * @param expireTime 过期时间
     */
    void storeSmsCode(String phone, String code, String codeType, LocalDateTime expireTime);

    /**
     * 获取短信验证码
     *
     * @param phone    手机号
     * @param codeType 验证码类型
     * @return 验证码信息，如果不存在返回null
     */
    SmsCodeInfo getSmsCode(String phone, String codeType);

    /**
     * 删除短信验证码
     *
     * @param phone    手机号
     * @param codeType 验证码类型
     */
    void deleteSmsCode(String phone, String codeType);

    /**
     * 检查发送频率限制
     *
     * @param phone 手机号
     * @return 如果可以发送返回true，否则返回false
     */
    boolean checkSendLimit(String phone);

    /**
     * 记录发送历史
     *
     * @param phone    手机号
     * @param codeType 验证码类型
     * @param success  是否发送成功
     */
    void recordSendHistory(String phone, String codeType, boolean success);

    /**
     * 获取今日发送次数
     *
     * @param phone 手机号
     * @return 今日发送次数
     */
    int getTodaySendCount(String phone);

    /**
     * 获取短信模板
     *
     * @param codeType 验证码类型
     * @return 短信模板
     */
    default String getSmsTemplate(String codeType) {
        switch (codeType) {
            case "login":
                return "您的登录验证码是：{code}，有效期5分钟，请勿泄露给他人。";
            case "register":
                return "您的注册验证码是：{code}，有效期5分钟，请勿泄露给他人。";
            default:
                return "您的验证码是：{code}，有效期5分钟，请勿泄露给他人。";
        }
    }

    /**
     * 短信发送结果
     */
    class SmsResult {
        private boolean success;
        private String message;
        private String requestId;

        public SmsResult(boolean success, String message) {
            this.success = success;
            this.message = message;
        }

        public SmsResult(boolean success, String message, String requestId) {
            this.success = success;
            this.message = message;
            this.requestId = requestId;
        }

        public boolean isSuccess() {
            return success;
        }

        public String getMessage() {
            return message;
        }

        public String getRequestId() {
            return requestId;
        }

        public static SmsResult success() {
            return new SmsResult(true, "发送成功");
        }

        public static SmsResult success(String requestId) {
            return new SmsResult(true, "发送成功", requestId);
        }

        public static SmsResult failure(String message) {
            return new SmsResult(false, message);
        }

        @Override
        public String toString() {
            return String.format("SmsResult{success=%s, message='%s', requestId='%s'}", 
                    success, message, requestId);
        }
    }

    /**
     * 短信验证码信息
     */
    class SmsCodeInfo {
        private String code;
        private String codeType;
        private LocalDateTime createTime;
        private LocalDateTime expireTime;
        private boolean used;

        public SmsCodeInfo(String code, String codeType, LocalDateTime createTime, LocalDateTime expireTime) {
            this.code = code;
            this.codeType = codeType;
            this.createTime = createTime;
            this.expireTime = expireTime;
            this.used = false;
        }

        public String getCode() {
            return code;
        }

        public String getCodeType() {
            return codeType;
        }

        public LocalDateTime getCreateTime() {
            return createTime;
        }

        public LocalDateTime getExpireTime() {
            return expireTime;
        }

        public boolean isUsed() {
            return used;
        }

        public void setUsed(boolean used) {
            this.used = used;
        }

        public boolean isExpired() {
            return LocalDateTime.now().isAfter(expireTime);
        }

        public boolean isValid() {
            return !used && !isExpired();
        }

        @Override
        public String toString() {
            return String.format("SmsCodeInfo{code='%s', codeType='%s', createTime=%s, expireTime=%s, used=%s}", 
                    code, codeType, createTime, expireTime, used);
        }
    }
}
