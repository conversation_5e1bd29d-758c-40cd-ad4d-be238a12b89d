package cn.com.handthing.starter.cache.redis;

import cn.com.handthing.starter.cache.CacheService;
import cn.com.handthing.starter.cache.redis.config.RedisCacheProperties;
import org.junit.jupiter.api.Test;
import org.springframework.boot.autoconfigure.AutoConfigurations;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.test.context.runner.ApplicationContextRunner;
import org.springframework.cache.CacheManager;
import org.springframework.data.redis.core.RedisTemplate;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * RedisCacheAutoConfiguration 单元测试
 *
 * <AUTHOR>
 * @since V1.0.0
 */
class RedisCacheAutoConfigurationTest {

    private final ApplicationContextRunner contextRunner = new ApplicationContextRunner()
            .withConfiguration(AutoConfigurations.of(
                    RedisAutoConfiguration.class,
                    RedisCacheAutoConfiguration.class
            ));

    @Test
    void testAutoConfiguration_Enabled() {
        contextRunner
                .withPropertyValues(
                        "handthing.cache.type=redis",
                        "spring.data.redis.host=localhost",
                        "spring.data.redis.port=6379"
                )
                .run(context -> {
                    assertThat(context).hasSingleBean(RedisCacheProperties.class);
                    assertThat(context).hasSingleBean(CacheManager.class);
                    assertThat(context).hasSingleBean(CacheService.class);
                    assertThat(context).hasBean("redisTemplate");
                    assertThat(context).getBean(CacheManager.class)
                            .isInstanceOf(RedisCacheManager.class);
                    assertThat(context).getBean(CacheService.class)
                            .isInstanceOf(RedisCacheService.class);
                });
    }

    @Test
    void testAutoConfiguration_Disabled() {
        contextRunner
                .withPropertyValues("handthing.cache.type=caffeine")
                .run(context -> {
                    assertThat(context).doesNotHaveBean(RedisCacheManager.class);
                    assertThat(context).doesNotHaveBean(RedisCacheService.class);
                });
    }

    @Test
    void testRedisCacheProperties_Binding() {
        contextRunner
                .withPropertyValues(
                        "handthing.cache.type=redis",
                        "handthing.cache.redis.key-prefix=myapp:",
                        "handthing.cache.redis.default-ttl=PT15M",
                        "handthing.cache.redis.allow-null-values=false",
                        "handthing.cache.redis.use-key-prefix=true",
                        "handthing.cache.redis.serialization.key-type=STRING",
                        "handthing.cache.redis.serialization.value-type=JSON",
                        "handthing.cache.redis.caches.users.ttl=PT5M",
                        "handthing.cache.redis.caches.users.key-prefix=users:",
                        "spring.data.redis.host=localhost",
                        "spring.data.redis.port=6379"
                )
                .run(context -> {
                    RedisCacheProperties properties = context.getBean(RedisCacheProperties.class);
                    assertThat(properties.getKeyPrefix()).isEqualTo("myapp:");
                    assertThat(properties.getDefaultTtl().toMinutes()).isEqualTo(15);
                    assertThat(properties.isAllowNullValues()).isFalse();
                    assertThat(properties.isUseKeyPrefix()).isTrue();
                    assertThat(properties.getSerialization().getKeyType())
                            .isEqualTo(RedisCacheProperties.Serialization.SerializationType.STRING);
                    assertThat(properties.getSerialization().getValueType())
                            .isEqualTo(RedisCacheProperties.Serialization.SerializationType.JSON);
                    
                    RedisCacheProperties.RedisCacheSpec userSpec = properties.getCaches().get("users");
                    assertThat(userSpec).isNotNull();
                    assertThat(userSpec.getTtl().toMinutes()).isEqualTo(5);
                    assertThat(userSpec.getKeyPrefix()).isEqualTo("users:");
                });
    }

    @Test
    void testRedisCacheManager_Creation() {
        contextRunner
                .withPropertyValues(
                        "handthing.cache.type=redis",
                        "spring.data.redis.host=localhost",
                        "spring.data.redis.port=6379"
                )
                .run(context -> {
                    CacheManager cacheManager = context.getBean(CacheManager.class);
                    assertThat(cacheManager).isInstanceOf(RedisCacheManager.class);
                    
                    // 测试缓存创建（不依赖真实Redis连接）
                    assertThat(cacheManager.getCacheNames()).isEmpty();
                });
    }

    @Test
    void testRedisTemplate_Creation() {
        contextRunner
                .withPropertyValues(
                        "handthing.cache.type=redis",
                        "spring.data.redis.host=localhost",
                        "spring.data.redis.port=6379"
                )
                .run(context -> {
                    RedisTemplate<String, Object> redisTemplate = context.getBean("redisTemplate", RedisTemplate.class);
                    assertThat(redisTemplate).isNotNull();
                    assertThat(redisTemplate.getKeySerializer()).isNotNull();
                    assertThat(redisTemplate.getValueSerializer()).isNotNull();
                });
    }

    @Test
    void testRedisCacheService_Creation() {
        contextRunner
                .withPropertyValues(
                        "handthing.cache.type=redis",
                        "spring.data.redis.host=localhost",
                        "spring.data.redis.port=6379"
                )
                .run(context -> {
                    CacheService cacheService = context.getBean(CacheService.class);
                    assertThat(cacheService).isInstanceOf(RedisCacheService.class);
                });
    }
}