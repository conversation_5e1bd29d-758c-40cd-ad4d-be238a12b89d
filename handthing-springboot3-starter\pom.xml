<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>cn.com.handthing.springboot3</groupId>
    <artifactId>handthing-springboot3-starters</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>

    <name>HandThing :: Spring Boot 3 Starters</name>
    <description>所有 HandThing Spring Boot 3 Starter 项目的聚合器</description>

    <properties>
        <!--
          全局唯一的版本号 (Single Source of Truth).
          未来发布时, 只需修改此一处。
        -->
        <revision>1.0.0-SNAPSHOT</revision>
    </properties>

    <!--
      聚合所有模块, mvn 命令可在此目录下执行以构建整个项目。
    -->
    <modules>
        <module>starter-parent</module>
        <module>handthing-core</module>
        <!-- 加解密相关 -->
        <module>crypto-spring-boot-starter</module>
<!--        &lt;!&ndash; 抽象层 &ndash;&gt;-->
<!--        <module>message-spring-boot-starter</module>-->
<!--        &lt;!&ndash; 适配层 &ndash;&gt;-->
<!--        <module>wechat-message-spring-boot-starter</module>-->
<!--        <module>wecom-message-spring-boot-starter</module>-->
<!--        <module>dingtalk-message-spring-boot-starter</module>-->
<!--        <module>feishu-message-spring-boot-starter</module>-->

        <!-- 功能性 Starter -->
        <!-- <module>verification-code-spring-boot-starter</module> -->

         <module>distributed-log-spring-boot-starter</module>
         <module>api-doc-spring-boot-starter</module>
         <module>knife4j-spring-boot-starter</module>

        <module>http-client-spring-boot-starter</module>
        <module>resttemplate-spring-boot-starter</module>
        <module>webclient-spring-boot-starter</module>
        <!-- 缓存系列 Starter -->
        <module>cache-spring-boot-starter</module>
        <module>memory-cache-spring-boot-starter</module>
        <module>caffeine-cache-spring-boot-starter</module>
        <module>redis-cache-spring-boot-starter</module>
        <module>level-cache-spring-boot-starter</module>

        <!-- App Connector 系列 -->
        <module>app-connector</module>
        <!-- ID生成器系列 -->
        <module>id-spring-boot-starter</module>

        <!-- 数据层系列 -->
        <module>datalayer-spring-boot-starter</module>
        <module>tenant-datalayer-spring-boot-starter</module>
        <module>dataauth-datalayer-spring-boot-starter</module>

        <!-- Auth 认证授权系列 -->
        <module>handthing-auth</module>

        <!-- 测试应用 -->
        <module>test-app</module>

    </modules>

</project>