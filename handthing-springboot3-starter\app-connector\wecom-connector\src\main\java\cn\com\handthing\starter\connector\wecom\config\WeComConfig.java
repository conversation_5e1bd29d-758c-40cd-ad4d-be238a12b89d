package cn.com.handthing.starter.connector.wecom.config;

import cn.com.handthing.starter.connector.config.PlatformConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import jakarta.validation.constraints.NotBlank;

/**
 * 企业微信配置类
 * <p>
 * 继承PlatformConfig，包含企业微信特有的配置项，
 * 如企业ID、应用ID、应用密钥等。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ConfigurationProperties(prefix = "handthing.connector.wecom")
public class WeComConfig extends PlatformConfig {

    /**
     * 企业ID
     */
    @NotBlank(message = "WeCom corp ID cannot be blank")
    private String corpId;

    /**
     * 应用ID
     */
    @NotBlank(message = "WeCom agent ID cannot be blank")
    private String agentId;

    /**
     * 应用密钥
     */
    @NotBlank(message = "WeCom secret cannot be blank")
    private String secret;

    /**
     * 企业微信API基础URL
     */
    private String apiBaseUrl = "https://qyapi.weixin.qq.com";

    /**
     * 企业微信授权URL
     */
    private String authUrl = "https://open.weixin.qq.com/connect/oauth2/authorize";

    /**
     * 是否启用企业微信自建应用
     */
    private boolean selfBuiltApp = true;

    /**
     * 是否启用企业微信第三方应用
     */
    private boolean thirdPartyApp = false;

    /**
     * 第三方应用的SuiteId（仅第三方应用需要）
     */
    private String suiteId;

    /**
     * 第三方应用的SuiteSecret（仅第三方应用需要）
     */
    private String suiteSecret;

    /**
     * 企业微信服务商的CorpId（仅第三方应用需要）
     */
    private String providerCorpId;

    /**
     * 企业微信服务商的Secret（仅第三方应用需要）
     */
    private String providerSecret;

    /**
     * 消息推送的Token
     */
    private String token;

    /**
     * 消息推送的EncodingAESKey
     */
    private String encodingAESKey;

    /**
     * 是否启用消息推送
     */
    private boolean enableCallback = false;

    /**
     * 消息推送回调URL
     */
    private String callbackUrl;

    @Override
    public boolean isValid() {
        // 企业微信特有的验证逻辑，不依赖父类的clientId和clientSecret
        boolean baseValid = isEnabled() &&
                corpId != null && !corpId.trim().isEmpty() &&
                agentId != null && !agentId.trim().isEmpty() &&
                secret != null && !secret.trim().isEmpty();

        if (!baseValid) {
            return false;
        }

        // 如果启用第三方应用，需要额外验证第三方应用相关配置
        if (thirdPartyApp) {
            return suiteId != null && !suiteId.trim().isEmpty() &&
                   suiteSecret != null && !suiteSecret.trim().isEmpty();
        }

        return true;
    }

    /**
     * 获取有效的API基础URL
     *
     * @return API基础URL
     */
    public String getEffectiveApiBaseUrl() {
        return getEffectiveApiBaseUrl(apiBaseUrl);
    }

    /**
     * 获取有效的授权URL
     *
     * @return 授权URL
     */
    public String getEffectiveAuthUrl() {
        return getEffectiveAuthUrl(authUrl);
    }

    /**
     * 检查是否为第三方应用模式
     *
     * @return 如果是第三方应用返回true，否则返回false
     */
    public boolean isThirdPartyMode() {
        return thirdPartyApp && suiteId != null && !suiteId.trim().isEmpty();
    }

    /**
     * 检查是否启用消息推送
     *
     * @return 如果启用消息推送返回true，否则返回false
     */
    public boolean isCallbackEnabled() {
        return enableCallback && 
               token != null && !token.trim().isEmpty() &&
               encodingAESKey != null && !encodingAESKey.trim().isEmpty();
    }

    /**
     * 获取Token URL
     *
     * @return Token获取URL
     */
    public String getTokenUrl() {
        if (isThirdPartyMode()) {
            return getEffectiveApiBaseUrl() + "/cgi-bin/service/get_suite_token";
        } else {
            return getEffectiveApiBaseUrl() + "/cgi-bin/gettoken";
        }
    }

    /**
     * 获取用户信息URL
     *
     * @return 用户信息获取URL
     */
    public String getUserInfoUrl() {
        return getEffectiveApiBaseUrl() + "/cgi-bin/user/getuserinfo";
    }

    /**
     * 获取用户详情URL
     *
     * @return 用户详情获取URL
     */
    public String getUserDetailUrl() {
        return getEffectiveApiBaseUrl() + "/cgi-bin/user/get";
    }

    /**
     * 获取部门列表URL
     *
     * @return 部门列表获取URL
     */
    public String getDepartmentListUrl() {
        return getEffectiveApiBaseUrl() + "/cgi-bin/department/list";
    }

    /**
     * 获取发送消息URL
     *
     * @return 发送消息URL
     */
    public String getSendMessageUrl() {
        return getEffectiveApiBaseUrl() + "/cgi-bin/message/send";
    }

    /**
     * 获取客户联系相关URL前缀
     *
     * @return 客户联系URL前缀
     */
    public String getExternalContactUrlPrefix() {
        return getEffectiveApiBaseUrl() + "/cgi-bin/externalcontact";
    }
}
