package cn.com.handthing.starter.auth.thirdparty;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * 默认钉钉API服务实现
 * <p>
 * 提供钉钉API调用的默认实现，包括获取访问令牌、用户信息等功能。
 * 使用WebClient进行HTTP调用，支持超时和错误处理。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnMissingBean(DingtalkApiService.class)
public class DefaultDingtalkApiService implements DingtalkApiService {

    private final WebClient webClient;
    private final ObjectMapper objectMapper;

    /**
     * 钉钉API基础URL
     */
    private static final String DINGTALK_API_BASE_URL = "https://oapi.dingtalk.com";

    /**
     * 获取访问令牌URL
     */
    private static final String GET_TOKEN_URL = DINGTALK_API_BASE_URL + "/gettoken";

    /**
     * 获取用户信息URL
     */
    private static final String GET_USER_INFO_URL = DINGTALK_API_BASE_URL + "/sns/getuserinfo_bycode";

    /**
     * 获取用户详细信息URL
     */
    private static final String GET_USER_DETAIL_URL = DINGTALK_API_BASE_URL + "/topapi/v2/user/get";

    /**
     * 默认构造函数
     */
    public DefaultDingtalkApiService() {
        this.webClient = WebClient.builder()
                .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(1024 * 1024))
                .build();
        this.objectMapper = new ObjectMapper();
    }

    @Override
    public DingtalkTokenResult getAccessToken(String appKey, String appSecret) {
        try {
            log.debug("Getting access token for appKey: {}", appKey);

            String response = webClient.get()
                    .uri(GET_TOKEN_URL + "?appkey={appKey}&appsecret={appSecret}", appKey, appSecret)
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(10))
                    .block();

            JsonNode jsonNode = objectMapper.readTree(response);
            int errcode = jsonNode.get("errcode").asInt();

            if (errcode == 0) {
                String accessToken = jsonNode.get("access_token").asText();
                long expiresIn = jsonNode.get("expires_in").asLong();
                
                log.debug("Access token obtained successfully for appKey: {}", appKey);
                return DingtalkTokenResult.success(accessToken, expiresIn);
            } else {
                String errmsg = jsonNode.get("errmsg").asText();
                log.warn("Failed to get access token for appKey: {}, errcode: {}, errmsg: {}", appKey, errcode, errmsg);
                return DingtalkTokenResult.failure(String.valueOf(errcode), errmsg);
            }

        } catch (Exception e) {
            log.error("Error getting access token for appKey: {}", appKey, e);
            return DingtalkTokenResult.failure("NETWORK_ERROR", "网络请求失败: " + e.getMessage());
        }
    }

    @Override
    public DingtalkUserResult getUserInfoByCode(String accessToken, String code) {
        try {
            log.debug("Getting user info by code");

            String response = webClient.post()
                    .uri(GET_USER_INFO_URL + "?access_token={accessToken}", accessToken)
                    .bodyValue(Map.of("tmp_auth_code", code))
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(10))
                    .block();

            JsonNode jsonNode = objectMapper.readTree(response);
            int errcode = jsonNode.get("errcode").asInt();

            if (errcode == 0) {
                JsonNode userInfo = jsonNode.get("user_info");
                String userId = userInfo.has("openid") ? userInfo.get("openid").asText() : null;
                String deviceId = userInfo.has("device_id") ? userInfo.get("device_id").asText() : null;
                String sysLevel = userInfo.has("sys_level") ? userInfo.get("sys_level").asText() : "1";

                log.debug("User info obtained successfully, userId: {}", userId);
                return DingtalkUserResult.success(userId, deviceId, sysLevel);
            } else {
                String errmsg = jsonNode.get("errmsg").asText();
                log.warn("Failed to get user info by code, errcode: {}, errmsg: {}", errcode, errmsg);
                return DingtalkUserResult.failure(String.valueOf(errcode), errmsg);
            }

        } catch (Exception e) {
            log.error("Error getting user info by code", e);
            return DingtalkUserResult.failure("NETWORK_ERROR", "网络请求失败: " + e.getMessage());
        }
    }

    @Override
    public DingtalkUserDetailResult getUserDetail(String accessToken, String userId) {
        try {
            log.debug("Getting user detail for userId: {}", userId);

            Map<String, Object> requestBody = Map.of("userid", userId);

            String response = webClient.post()
                    .uri(GET_USER_DETAIL_URL + "?access_token={accessToken}", accessToken)
                    .bodyValue(requestBody)
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(10))
                    .block();

            JsonNode jsonNode = objectMapper.readTree(response);
            int errcode = jsonNode.get("errcode").asInt();

            if (errcode == 0) {
                JsonNode result = jsonNode.get("result");
                DingtalkUserDetailResult userDetail = DingtalkUserDetailResult.success();
                
                userDetail.setUserId(getTextValue(result, "userid"));
                userDetail.setName(getTextValue(result, "name"));
                userDetail.setMobile(getTextValue(result, "mobile"));
                userDetail.setEmail(getTextValue(result, "email"));
                userDetail.setAvatar(getTextValue(result, "avatar"));
                userDetail.setJobNumber(getTextValue(result, "job_number"));
                userDetail.setTitle(getTextValue(result, "title"));
                userDetail.setWorkPlace(getTextValue(result, "work_place"));
                userDetail.setRemark(getTextValue(result, "remark"));

                // 处理部门信息
                if (result.has("dept_id_list")) {
                    JsonNode deptNode = result.get("dept_id_list");
                    if (deptNode.isArray()) {
                        String[] deptIds = new String[deptNode.size()];
                        for (int i = 0; i < deptNode.size(); i++) {
                            deptIds[i] = deptNode.get(i).asText();
                        }
                        userDetail.setDeptIdList(deptIds);
                    }
                }

                // 处理部门排序
                if (result.has("dept_order_list")) {
                    JsonNode orderNode = result.get("dept_order_list");
                    if (orderNode.isArray()) {
                        String[] deptOrders = new String[orderNode.size()];
                        for (int i = 0; i < orderNode.size(); i++) {
                            deptOrders[i] = orderNode.get(i).asText();
                        }
                        userDetail.setDeptOrderList(deptOrders);
                    }
                }

                // 处理扩展信息
                if (result.has("extension")) {
                    JsonNode extNode = result.get("extension");
                    Map<String, Object> extension = objectMapper.convertValue(extNode, Map.class);
                    userDetail.setExtension(extension);
                }

                log.debug("User detail obtained successfully for userId: {}", userId);
                return userDetail;
            } else {
                String errmsg = jsonNode.get("errmsg").asText();
                log.warn("Failed to get user detail for userId: {}, errcode: {}, errmsg: {}", userId, errcode, errmsg);
                return DingtalkUserDetailResult.failure(String.valueOf(errcode), errmsg);
            }

        } catch (Exception e) {
            log.error("Error getting user detail for userId: {}", userId, e);
            return DingtalkUserDetailResult.failure("NETWORK_ERROR", "网络请求失败: " + e.getMessage());
        }
    }

    @Override
    public boolean validateConfig(String appKey, String appSecret) {
        try {
            log.debug("Validating dingtalk config for appKey: {}", appKey);

            DingtalkTokenResult tokenResult = getAccessToken(appKey, appSecret);
            if (!tokenResult.isSuccess()) {
                log.warn("Invalid dingtalk config: failed to get access token");
                return false;
            }

            log.debug("Dingtalk config validation successful");
            return true;

        } catch (Exception e) {
            log.error("Error validating dingtalk config", e);
            return false;
        }
    }

    /**
     * 安全地获取JSON节点的文本值
     *
     * @param jsonNode JSON节点
     * @param fieldName 字段名
     * @return 文本值，如果不存在返回null
     */
    private String getTextValue(JsonNode jsonNode, String fieldName) {
        return jsonNode.has(fieldName) ? jsonNode.get(fieldName).asText() : null;
    }

    /**
     * 创建模拟的钉钉API服务（用于测试）
     *
     * @return 模拟的API服务
     */
    public static DingtalkApiService createMockService() {
        return new DingtalkApiService() {
            @Override
            public DingtalkTokenResult getAccessToken(String appKey, String appSecret) {
                // 模拟成功响应
                if ("test_app_key".equals(appKey) && "test_app_secret".equals(appSecret)) {
                    return DingtalkTokenResult.success("mock_access_token_" + System.currentTimeMillis(), 7200L);
                }
                return DingtalkTokenResult.failure("40014", "invalid appkey");
            }

            @Override
            public DingtalkUserResult getUserInfoByCode(String accessToken, String code) {
                // 模拟成功响应
                if (accessToken.startsWith("mock_access_token") && "test_code".equals(code)) {
                    return DingtalkUserResult.success("test_user_id", "test_device_id", "1");
                }
                return DingtalkUserResult.failure("40078", "invalid code");
            }

            @Override
            public DingtalkUserDetailResult getUserDetail(String accessToken, String userId) {
                // 模拟成功响应
                if (accessToken.startsWith("mock_access_token") && "test_user_id".equals(userId)) {
                    DingtalkUserDetailResult result = DingtalkUserDetailResult.success();
                    result.setUserId("test_user_id");
                    result.setName("测试用户");
                    result.setMobile("13800138000");
                    result.setEmail("<EMAIL>");
                    result.setAvatar("http://example.com/avatar.jpg");
                    result.setJobNumber("D001");
                    result.setTitle("测试工程师");
                    result.setWorkPlace("杭州");
                    result.setDeptIdList(new String[]{"1", "2"});
                    
                    Map<String, Object> extension = new HashMap<>();
                    extension.put("工号", "D001");
                    result.setExtension(extension);
                    
                    return result;
                }
                return DingtalkUserDetailResult.failure("60121", "user not exist");
            }

            @Override
            public boolean validateConfig(String appKey, String appSecret) {
                return "test_app_key".equals(appKey) && "test_app_secret".equals(appSecret);
            }
        };
    }
}
