D:\code\ai-project\handthing-springboot3-starter\app-connector\dingtalk-connector\src\main\java\cn\com\handthing\starter\connector\dingtalk\api\DingTalkContactApi.java
D:\code\ai-project\handthing-springboot3-starter\app-connector\dingtalk-connector\src\main\java\cn\com\handthing\starter\connector\dingtalk\api\DingTalkMessageApi.java
D:\code\ai-project\handthing-springboot3-starter\app-connector\dingtalk-connector\src\main\java\cn\com\handthing\starter\connector\dingtalk\api\DingTalkTodoApi.java
D:\code\ai-project\handthing-springboot3-starter\app-connector\dingtalk-connector\src\main\java\cn\com\handthing\starter\connector\dingtalk\config\DingTalkAutoConfiguration.java
D:\code\ai-project\handthing-springboot3-starter\app-connector\dingtalk-connector\src\main\java\cn\com\handthing\starter\connector\dingtalk\config\DingTalkConfig.java
D:\code\ai-project\handthing-springboot3-starter\app-connector\dingtalk-connector\src\main\java\cn\com\handthing\starter\connector\dingtalk\DingTalkAuthProvider.java
D:\code\ai-project\handthing-springboot3-starter\app-connector\dingtalk-connector\src\main\java\cn\com\handthing\starter\connector\dingtalk\DingTalkService.java
D:\code\ai-project\handthing-springboot3-starter\app-connector\dingtalk-connector\src\main\java\cn\com\handthing\starter\connector\dingtalk\model\DingTalkAccessTokenResponse.java
D:\code\ai-project\handthing-springboot3-starter\app-connector\dingtalk-connector\src\main\java\cn\com\handthing\starter\connector\dingtalk\model\DingTalkMessage.java
D:\code\ai-project\handthing-springboot3-starter\app-connector\dingtalk-connector\src\main\java\cn\com\handthing\starter\connector\dingtalk\model\DingTalkTodo.java
D:\code\ai-project\handthing-springboot3-starter\app-connector\dingtalk-connector\src\main\java\cn\com\handthing\starter\connector\dingtalk\model\DingTalkUser.java
D:\code\ai-project\handthing-springboot3-starter\app-connector\dingtalk-connector\src\main\java\cn\com\handthing\starter\connector\dingtalk\model\DingTalkUserInfoResponse.java
