package cn.com.handthing.starter.connector.bilibili.api;

import cn.com.handthing.starter.connector.bilibili.config.BilibiliConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Bilibili视频API
 * <p>
 * 提供Bilibili视频相关的API功能，包括视频上传、视频管理、视频数据统计等。
 * 支持个人创作者、企业用户和MCN机构的视频创作功能。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "handthing.connector.bilibili", name = "enabled", havingValue = "true")
public class BilibiliVideoApi {

    private final BilibiliConfig config;
    private final RestTemplate restTemplate;

    /**
     * 上传视频
     *
     * @param accessToken 访问令牌
     * @param title       视频标题
     * @param description 视频描述
     * @param videoUrl    视频文件URL
     * @param cover       封面图片URL
     * @param tags        标签列表
     * @return 上传结果
     */
    public Map<String, Object> uploadVideo(String accessToken, String title, String description, 
                                           String videoUrl, String cover, List<String> tags) {
        try {
            log.debug("Uploading Bilibili video: {}", title);

            String url = config.getEffectiveApiBaseUrl() + "/x/vu/web/add";

            Map<String, Object> video = new HashMap<>();
            video.put("title", title);
            video.put("desc", description);
            video.put("filename", videoUrl);
            video.put("cover", cover);
            video.put("tag", String.join(",", tags));
            video.put("access_token", accessToken);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + accessToken);

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(video, headers);

            Map<String, Object> response = restTemplate.postForObject(url, request, Map.class);
            log.debug("Bilibili video uploaded, response: {}", response);

            return response;

        } catch (Exception e) {
            log.error("Failed to upload Bilibili video", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", -1);
            errorResponse.put("message", "Failed to upload video: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 获取视频列表
     *
     * @param accessToken 访问令牌
     * @param page        页码
     * @param pageSize    每页大小
     * @return 视频列表
     */
    public Map<String, Object> getVideoList(String accessToken, Integer page, Integer pageSize) {
        try {
            log.debug("Getting Bilibili video list, page: {}", page);

            String url = config.getEffectiveApiBaseUrl() + "/x/space/arc/search";

            Map<String, Object> params = new HashMap<>();
            params.put("access_token", accessToken);
            params.put("pn", page != null ? page : 1);
            params.put("ps", pageSize != null ? pageSize : 20);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + accessToken);

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(params, headers);

            Map<String, Object> response = restTemplate.postForObject(url, request, Map.class);
            log.debug("Bilibili video list retrieved, response: {}", response);

            return response;

        } catch (Exception e) {
            log.error("Failed to get Bilibili video list", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", -1);
            errorResponse.put("message", "Failed to get video list: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 获取视频详情
     *
     * @param accessToken 访问令牌
     * @param bvid        视频BV号
     * @return 视频详情
     */
    public Map<String, Object> getVideoDetail(String accessToken, String bvid) {
        try {
            log.debug("Getting Bilibili video detail: {}", bvid);

            String url = config.getEffectiveApiBaseUrl() + "/x/web-interface/view";

            Map<String, Object> params = new HashMap<>();
            params.put("access_token", accessToken);
            params.put("bvid", bvid);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + accessToken);

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(params, headers);

            Map<String, Object> response = restTemplate.postForObject(url, request, Map.class);
            log.debug("Bilibili video detail retrieved, response: {}", response);

            return response;

        } catch (Exception e) {
            log.error("Failed to get Bilibili video detail", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", -1);
            errorResponse.put("message", "Failed to get video detail: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 删除视频
     *
     * @param accessToken 访问令牌
     * @param aid         视频AV号
     * @return 删除结果
     */
    public Map<String, Object> deleteVideo(String accessToken, Long aid) {
        try {
            log.debug("Deleting Bilibili video: {}", aid);

            String url = config.getEffectiveApiBaseUrl() + "/x/vu/web/del";

            Map<String, Object> deleteData = new HashMap<>();
            deleteData.put("access_token", accessToken);
            deleteData.put("aid", aid);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + accessToken);

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(deleteData, headers);

            Map<String, Object> response = restTemplate.postForObject(url, request, Map.class);
            log.debug("Bilibili video deleted, response: {}", response);

            return response;

        } catch (Exception e) {
            log.error("Failed to delete Bilibili video", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", -1);
            errorResponse.put("message", "Failed to delete video: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 获取视频统计数据
     *
     * @param accessToken 访问令牌
     * @param bvid        视频BV号
     * @return 统计数据
     */
    public Map<String, Object> getVideoStats(String accessToken, String bvid) {
        try {
            log.debug("Getting Bilibili video stats: {}", bvid);

            String url = config.getEffectiveApiBaseUrl() + "/x/web-interface/archive/stat";

            Map<String, Object> params = new HashMap<>();
            params.put("access_token", accessToken);
            params.put("bvid", bvid);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + accessToken);

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(params, headers);

            Map<String, Object> response = restTemplate.postForObject(url, request, Map.class);
            log.debug("Bilibili video stats retrieved, response: {}", response);

            return response;

        } catch (Exception e) {
            log.error("Failed to get Bilibili video stats", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", -1);
            errorResponse.put("message", "Failed to get video stats: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 编辑视频信息
     *
     * @param accessToken 访问令牌
     * @param aid         视频AV号
     * @param title       新标题
     * @param description 新描述
     * @param tags        新标签列表
     * @return 编辑结果
     */
    public Map<String, Object> editVideo(String accessToken, Long aid, String title, String description, List<String> tags) {
        try {
            log.debug("Editing Bilibili video: {}", aid);

            String url = config.getEffectiveApiBaseUrl() + "/x/vu/web/edit";

            Map<String, Object> editData = new HashMap<>();
            editData.put("access_token", accessToken);
            editData.put("aid", aid);
            editData.put("title", title);
            editData.put("desc", description);
            editData.put("tag", String.join(",", tags));

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + accessToken);

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(editData, headers);

            Map<String, Object> response = restTemplate.postForObject(url, request, Map.class);
            log.debug("Bilibili video edited, response: {}", response);

            return response;

        } catch (Exception e) {
            log.error("Failed to edit Bilibili video", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", -1);
            errorResponse.put("message", "Failed to edit video: " + e.getMessage());
            return errorResponse;
        }
    }
}
