package cn.com.handthing.starter.httpclient.internal;

import cn.com.handthing.starter.httpclient.HandthingHttpClient;
import cn.com.handthing.starter.httpclient.RequestExecutor;
import cn.com.handthing.starter.httpclient.crypto.NoOpCryptoProvider;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * WebClientHandthingHttpClient 单元测试
 * 
 * <AUTHOR>
 * @since V1.0.0
 */
@ExtendWith(MockitoExtension.class)
class WebClientHandthingHttpClientTest {

    @Mock
    private EndpointConfigResolver configResolver;

    private HandthingHttpClient httpClient;

    @BeforeEach
    void setUp() {
        NoOpCryptoProvider noOpCryptoProvider = new NoOpCryptoProvider();
        httpClient = new WebClientHandthingHttpClient(
                configResolver, 
                noOpCryptoProvider, 
                noOpCryptoProvider
        );
    }

    @Test
    void testGetRequest() {
        // When
        RequestExecutor executor = httpClient.get("/api/users/{id}", 123);

        // Then
        assertThat(executor).isNotNull();
        assertThat(executor).isInstanceOf(WebClientRequestExecutor.class);
    }

    @Test
    void testPostRequest() {
        // When
        RequestExecutor executor = httpClient.post("/api/users");

        // Then
        assertThat(executor).isNotNull();
        assertThat(executor).isInstanceOf(WebClientRequestExecutor.class);
    }

    @Test
    void testPutRequest() {
        // When
        RequestExecutor executor = httpClient.put("/api/users/{id}", 123);

        // Then
        assertThat(executor).isNotNull();
        assertThat(executor).isInstanceOf(WebClientRequestExecutor.class);
    }

    @Test
    void testDeleteRequest() {
        // When
        RequestExecutor executor = httpClient.delete("/api/users/{id}", 123);

        // Then
        assertThat(executor).isNotNull();
        assertThat(executor).isInstanceOf(WebClientRequestExecutor.class);
    }

    @Test
    void testFluentApiChaining() {
        // When
        RequestExecutor executor = httpClient.post("/api/users")
                .header("Content-Type", "application/json")
                .header("Authorization", "Bearer token")
                .body("{\"name\":\"test\"}");

        // Then
        assertThat(executor).isNotNull();
        assertThat(executor).isInstanceOf(WebClientRequestExecutor.class);
    }
}
