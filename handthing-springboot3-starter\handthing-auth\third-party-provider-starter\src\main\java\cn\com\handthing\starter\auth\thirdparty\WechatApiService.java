package cn.com.handthing.starter.auth.thirdparty;

/**
 * 微信API服务接口
 * <p>
 * 定义微信API调用的核心接口，包括获取访问令牌、用户信息等。
 * 业务系统可以实现此接口来对接微信的具体API。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
public interface WechatApiService {

    /**
     * 根据授权码获取访问令牌
     *
     * @param appId     应用ID
     * @param appSecret 应用密钥
     * @param code      授权码
     * @return 访问令牌结果
     */
    WechatTokenResult getAccessToken(String appId, String appSecret, String code);

    /**
     * 刷新访问令牌
     *
     * @param appId        应用ID
     * @param refreshToken 刷新令牌
     * @return 访问令牌结果
     */
    WechatTokenResult refreshAccessToken(String appId, String refreshToken);

    /**
     * 获取用户信息
     *
     * @param accessToken 访问令牌
     * @param openId      用户OpenID
     * @param lang        语言设置
     * @return 用户信息结果
     */
    WechatUserResult getUserInfo(String accessToken, String openId, String lang);

    /**
     * 验证访问令牌是否有效
     *
     * @param accessToken 访问令牌
     * @param openId      用户OpenID
     * @return 如果有效返回true，否则返回false
     */
    boolean validateAccessToken(String accessToken, String openId);

    /**
     * 验证微信应用配置
     *
     * @param appId     应用ID
     * @param appSecret 应用密钥
     * @return 如果配置有效返回true，否则返回false
     */
    boolean validateConfig(String appId, String appSecret);

    /**
     * 微信令牌结果
     */
    class WechatTokenResult {
        private boolean success;
        private String accessToken;
        private String refreshToken;
        private Long expiresIn;
        private String openId;
        private String scope;
        private String errorCode;
        private String errorMessage;

        public WechatTokenResult(boolean success, String accessToken, String refreshToken, 
                                Long expiresIn, String openId, String scope) {
            this.success = success;
            this.accessToken = accessToken;
            this.refreshToken = refreshToken;
            this.expiresIn = expiresIn;
            this.openId = openId;
            this.scope = scope;
        }

        public WechatTokenResult(boolean success, String errorCode, String errorMessage) {
            this.success = success;
            this.errorCode = errorCode;
            this.errorMessage = errorMessage;
        }

        public boolean isSuccess() {
            return success;
        }

        public String getAccessToken() {
            return accessToken;
        }

        public String getRefreshToken() {
            return refreshToken;
        }

        public Long getExpiresIn() {
            return expiresIn;
        }

        public String getOpenId() {
            return openId;
        }

        public String getScope() {
            return scope;
        }

        public String getErrorCode() {
            return errorCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public static WechatTokenResult success(String accessToken, String refreshToken, 
                                              Long expiresIn, String openId, String scope) {
            return new WechatTokenResult(true, accessToken, refreshToken, expiresIn, openId, scope);
        }

        public static WechatTokenResult failure(String errorCode, String errorMessage) {
            return new WechatTokenResult(false, errorCode, errorMessage);
        }

        @Override
        public String toString() {
            return String.format("WechatTokenResult{success=%s, openId='%s', scope='%s', expiresIn=%d, errorCode='%s', errorMessage='%s'}",
                    success, openId, scope, expiresIn, errorCode, errorMessage);
        }
    }

    /**
     * 微信用户结果
     */
    class WechatUserResult {
        private boolean success;
        private String openId;
        private String unionId;
        private String nickname;
        private String sex;
        private String province;
        private String city;
        private String country;
        private String headImgUrl;
        private String[] privilege;
        private String errorCode;
        private String errorMessage;

        public WechatUserResult(boolean success) {
            this.success = success;
        }

        public WechatUserResult(boolean success, String errorCode, String errorMessage) {
            this.success = success;
            this.errorCode = errorCode;
            this.errorMessage = errorMessage;
        }

        public boolean isSuccess() {
            return success;
        }

        public String getOpenId() {
            return openId;
        }

        public void setOpenId(String openId) {
            this.openId = openId;
        }

        public String getUnionId() {
            return unionId;
        }

        public void setUnionId(String unionId) {
            this.unionId = unionId;
        }

        public String getNickname() {
            return nickname;
        }

        public void setNickname(String nickname) {
            this.nickname = nickname;
        }

        public String getSex() {
            return sex;
        }

        public void setSex(String sex) {
            this.sex = sex;
        }

        public String getProvince() {
            return province;
        }

        public void setProvince(String province) {
            this.province = province;
        }

        public String getCity() {
            return city;
        }

        public void setCity(String city) {
            this.city = city;
        }

        public String getCountry() {
            return country;
        }

        public void setCountry(String country) {
            this.country = country;
        }

        public String getHeadImgUrl() {
            return headImgUrl;
        }

        public void setHeadImgUrl(String headImgUrl) {
            this.headImgUrl = headImgUrl;
        }

        public String[] getPrivilege() {
            return privilege;
        }

        public void setPrivilege(String[] privilege) {
            this.privilege = privilege;
        }

        public String getErrorCode() {
            return errorCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public static WechatUserResult success() {
            return new WechatUserResult(true);
        }

        public static WechatUserResult failure(String errorCode, String errorMessage) {
            return new WechatUserResult(false, errorCode, errorMessage);
        }

        @Override
        public String toString() {
            return String.format("WechatUserResult{success=%s, openId='%s', unionId='%s', nickname='%s', errorCode='%s', errorMessage='%s'}",
                    success, openId, unionId, nickname, errorCode, errorMessage);
        }
    }
}
