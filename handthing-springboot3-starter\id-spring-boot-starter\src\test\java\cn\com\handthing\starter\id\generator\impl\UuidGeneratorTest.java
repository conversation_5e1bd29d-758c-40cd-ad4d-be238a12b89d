package cn.com.handthing.starter.id.generator.impl;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.HashSet;
import java.util.Set;
import java.util.regex.Pattern;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * UUID生成器测试
 *
 * <AUTHOR>
 * @since V1.0.0
 */
class UuidGeneratorTest {
    
    private static final Pattern UUID_PATTERN = Pattern.compile("^[0-9a-fA-F]{32}$");
    private static final Pattern UUID_WITH_HYPHENS_PATTERN = 
            Pattern.compile("^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$");
    
    @Test
    @DisplayName("应该生成32位无连字符的小写UUID")
    void shouldGenerateDefaultUuid() {
        // Given
        UuidGenerator generator = new UuidGenerator();
        
        // When
        String uuid = generator.generate();
        
        // Then
        assertThat(uuid).isNotNull();
        assertThat(uuid).hasSize(32);
        assertThat(uuid).matches(UUID_PATTERN);
        assertThat(uuid).isLowerCase();
        assertThat(uuid).doesNotContain("-");
    }
    
    @Test
    @DisplayName("应该生成包含连字符的UUID")
    void shouldGenerateUuidWithHyphens() {
        // Given
        UuidGenerator generator = new UuidGenerator(true, false);
        
        // When
        String uuid = generator.generate();
        
        // Then
        assertThat(uuid).isNotNull();
        assertThat(uuid).hasSize(36);
        assertThat(uuid).matches(UUID_WITH_HYPHENS_PATTERN);
        assertThat(uuid).contains("-");
    }
    
    @Test
    @DisplayName("应该生成大写UUID")
    void shouldGenerateUpperCaseUuid() {
        // Given
        UuidGenerator generator = new UuidGenerator(false, true);
        
        // When
        String uuid = generator.generate();
        
        // Then
        assertThat(uuid).isNotNull();
        assertThat(uuid).hasSize(32);
        assertThat(uuid).isUpperCase();
        assertThat(uuid).doesNotContain("-");
    }
    
    @Test
    @DisplayName("应该生成包含连字符的大写UUID")
    void shouldGenerateUpperCaseUuidWithHyphens() {
        // Given
        UuidGenerator generator = new UuidGenerator(true, true);
        
        // When
        String uuid = generator.generate();
        
        // Then
        assertThat(uuid).isNotNull();
        assertThat(uuid).hasSize(36);
        assertThat(uuid).matches(UUID_WITH_HYPHENS_PATTERN);
        assertThat(uuid).isUpperCase();
        assertThat(uuid).contains("-");
    }
    
    @Test
    @DisplayName("应该生成唯一的UUID")
    void shouldGenerateUniqueUuids() {
        // Given
        UuidGenerator generator = new UuidGenerator();
        Set<String> uuids = new HashSet<>();
        int count = 1000;
        
        // When
        for (int i = 0; i < count; i++) {
            String uuid = generator.generate();
            uuids.add(uuid);
        }
        
        // Then
        assertThat(uuids).hasSize(count);
    }
    
    @Test
    @DisplayName("应该返回正确的ID类型")
    void shouldReturnCorrectIdType() {
        // Given
        UuidGenerator generator = new UuidGenerator();
        
        // When & Then
        assertThat(generator.getIdType()).isEqualTo(String.class);
    }
    
    @Test
    @DisplayName("应该返回正确的生成器名称")
    void shouldReturnCorrectGeneratorName() {
        // Given
        UuidGenerator generator = new UuidGenerator();
        
        // When & Then
        assertThat(generator.getName()).isEqualTo("UuidGenerator");
    }
    
    @Test
    @DisplayName("应该总是可用")
    void shouldAlwaysBeAvailable() {
        // Given
        UuidGenerator generator = new UuidGenerator();
        
        // When & Then
        assertThat(generator.isAvailable()).isTrue();
    }
    
    @Test
    @DisplayName("预热应该成功")
    void shouldWarmUpSuccessfully() {
        // Given
        UuidGenerator generator = new UuidGenerator();
        
        // When & Then
        // 应该不抛出异常
        generator.warmUp();
    }
    
    @Test
    @DisplayName("静态工厂方法应该工作正常")
    void shouldWorkWithStaticFactoryMethods() {
        // When
        UuidGenerator withHyphens = UuidGenerator.withHyphens();
        UuidGenerator upperCase = UuidGenerator.upperCase();
        UuidGenerator withHyphensUpperCase = UuidGenerator.withHyphensUpperCase();
        
        // Then
        String uuid1 = withHyphens.generate();
        assertThat(uuid1).hasSize(36).contains("-");
        
        String uuid2 = upperCase.generate();
        assertThat(uuid2).hasSize(32).isUpperCase().doesNotContain("-");
        
        String uuid3 = withHyphensUpperCase.generate();
        assertThat(uuid3).hasSize(36).isUpperCase().contains("-");
    }
    
    @Test
    @DisplayName("UUID验证应该工作正常")
    void shouldValidateUuidCorrectly() {
        // Given
        UuidGenerator generator = new UuidGenerator();
        String validUuid = generator.generate();
        String validUuidWithHyphens = UuidGenerator.withHyphens().generate();
        
        // When & Then
        assertThat(UuidGenerator.isValidUuid(validUuid)).isTrue();
        assertThat(UuidGenerator.isValidUuid(validUuidWithHyphens)).isTrue();
        assertThat(UuidGenerator.isValidUuid("invalid")).isFalse();
        assertThat(UuidGenerator.isValidUuid(null)).isFalse();
        assertThat(UuidGenerator.isValidUuid("")).isFalse();
    }
    
    @Test
    @DisplayName("配置信息应该正确")
    void shouldReturnCorrectConfigInfo() {
        // Given
        UuidGenerator generator1 = new UuidGenerator();
        UuidGenerator generator2 = new UuidGenerator(true, true);
        
        // When & Then
        assertThat(generator1.getConfigInfo()).contains("includeHyphens=false", "upperCase=false");
        assertThat(generator2.getConfigInfo()).contains("includeHyphens=true", "upperCase=true");
    }
    
    @Test
    @DisplayName("toString应该返回配置信息")
    void shouldReturnConfigInfoInToString() {
        // Given
        UuidGenerator generator = new UuidGenerator(true, false);
        
        // When & Then
        assertThat(generator.toString()).isEqualTo(generator.getConfigInfo());
    }
}
