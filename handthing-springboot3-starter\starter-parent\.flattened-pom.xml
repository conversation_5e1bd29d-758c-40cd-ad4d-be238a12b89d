<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-parent</artifactId>
    <version>3.5.3</version>
    <relativePath></relativePath>
  </parent>
  <groupId>cn.com.handthing.springboot3.starter</groupId>
  <artifactId>starter-parent</artifactId>
  <version>1.0.0-SNAPSHOT</version>
  <packaging>pom</packaging>
  <name>HandThing :: Starter Parent</name>
  <description>所有 HandThing Spring Boot 3 Starter 的父依赖</description>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0</url>
    </license>
  </licenses>
  <properties>
    <maven.compiler.target>${java.version}</maven.compiler.target>
    <java.version>17</java.version>
    <maven.compiler.source>${java.version}</maven.compiler.source>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.version>1.0.0-SNAPSHOT</project.version>
    <flatten-maven-plugin.version>1.6.0</flatten-maven-plugin.version>
    <revision>1.0.0-SNAPSHOT</revision>
  </properties>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-dependencies</artifactId>
        <version>2025.0.0</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>cn.com.handthing.springboot3.starter</groupId>
        <artifactId>handthing-core</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.com.handthing.springboot3.starter</groupId>
        <artifactId>distributed-log-spring-boot-starter</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.com.handthing.springboot3.starter</groupId>
        <artifactId>api-doc-spring-boot-starter</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.com.handthing.springboot3.starter</groupId>
        <artifactId>auth-core</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.com.handthing.springboot3.starter</groupId>
        <artifactId>auth-spring-boot-starter</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.com.handthing.springboot3.starter</groupId>
        <artifactId>password-provider-starter</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.com.handthing.springboot3.starter</groupId>
        <artifactId>sms-provider-starter</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.com.handthing.springboot3.starter</groupId>
        <artifactId>third-party-provider-starter</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.com.handthing.springboot3.starter</groupId>
        <artifactId>tenant-auth-starter</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.com.handthing.springboot3.starter</groupId>
        <artifactId>cache-spring-boot-starter</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.com.handthing.springboot3.starter</groupId>
        <artifactId>memory-cache-spring-boot-starter</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.com.handthing.springboot3.starter</groupId>
        <artifactId>caffeine-cache-spring-boot-starter</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.com.handthing.springboot3.starter</groupId>
        <artifactId>redis-cache-spring-boot-starter</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.com.handthing.springboot3.starter</groupId>
        <artifactId>level-cache-spring-boot-starter</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.github.ben-manes.caffeine</groupId>
        <artifactId>caffeine</artifactId>
        <version>3.1.8</version>
      </dependency>
      <dependency>
        <groupId>io.github.resilience4j</groupId>
        <artifactId>resilience4j-circuitbreaker</artifactId>
        <version>2.2.0</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.retry</groupId>
        <artifactId>spring-retry</artifactId>
        <version>2.0.5</version>
      </dependency>
      <dependency>
        <groupId>net.logstash.logback</groupId>
        <artifactId>logstash-logback-encoder</artifactId>
        <version>7.4</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.janino</groupId>
        <artifactId>janino</artifactId>
        <version>3.1.12</version>
      </dependency>
      <dependency>
        <groupId>org.springdoc</groupId>
        <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
        <version>2.5.0</version>
      </dependency>
      <dependency>
        <groupId>org.bouncycastle</groupId>
        <artifactId>bcprov-jdk18on</artifactId>
        <version>1.78.1</version>
      </dependency>
      <dependency>
        <groupId>org.bouncycastle</groupId>
        <artifactId>bcpkix-jdk18on</artifactId>
        <version>1.78.1</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>junit-jupiter</artifactId>
        <version>1.20.1</version>
      </dependency>
      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-boot-starter</artifactId>
        <version>3.5.5</version>
      </dependency>
      <dependency>
        <groupId>cn.com.handthing.springboot3.starter</groupId>
        <artifactId>id-spring-boot-starter</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.com.handthing.springboot3.starter</groupId>
        <artifactId>datalayer-spring-boot-starter</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.com.handthing.springboot3.starter</groupId>
        <artifactId>tenant-datalayer-spring-boot-starter</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.com.handthing.springboot3.starter</groupId>
        <artifactId>dataauth-datalayer-spring-boot-starter</artifactId>
        <version>${project.version}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <build>
    <plugins>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>flatten-maven-plugin</artifactId>
        <version>${flatten-maven-plugin.version}</version>
        <executions>
          <execution>
            <id>flatten</id>
            <phase>process-resources</phase>
            <goals>
              <goal>flatten</goal>
            </goals>
          </execution>
          <execution>
            <id>flatten.clean</id>
            <phase>clean</phase>
            <goals>
              <goal>clean</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <updatePomFile>true</updatePomFile>
          <flattenMode>resolveCiFriendliesOnly</flattenMode>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
