package cn.com.handthing.starter.httpclient.filter;

import cn.com.handthing.starter.httpclient.config.RetryProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.web.reactive.function.client.ClientRequest;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.ExchangeFilterFunction;
import org.springframework.web.reactive.function.client.ExchangeFunction;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

/**
 * WebClient 重试过滤器
 * 
 * <AUTHOR>
 * @since V1.0.0
 */
public class WebClientRetryFilter implements ExchangeFilterFunction {

    private static final Logger log = LoggerFactory.getLogger(WebClientRetryFilter.class);
    
    private final RetryProperties retryProperties;

    public WebClientRetryFilter(RetryProperties retryProperties) {
        this.retryProperties = retryProperties;
    }

    @Override
    public Mono<ClientResponse> filter(ClientRequest request, ExchangeFunction next) {
        if (!retryProperties.isEnabled()) {
            return next.exchange(request);
        }

        return next.exchange(request)
                .flatMap(response -> {
                    // 检查是否需要重试（5xx 错误）
                    if (response.statusCode().is5xxServerError()) {
                        return Mono.error(new WebClientResponseException(
                                "Server error: " + response.statusCode(),
                                response.statusCode().value(),
                                HttpStatus.valueOf(response.statusCode().value()).getReasonPhrase(),
                                response.headers().asHttpHeaders(),
                                null,
                                null
                        ));
                    }
                    return Mono.just(response);
                })
                .retryWhen(createRetrySpec(request))
                .doOnNext(response -> {
                    if (log.isDebugEnabled()) {
                        log.debug("Request succeeded: {} {}", request.method(), request.url());
                    }
                });
    }

    private Retry createRetrySpec(ClientRequest request) {
        return Retry.backoff(
                        retryProperties.getMaxAttempts() - 1, // 减1因为第一次不算重试
                        retryProperties.getDelay()
                )
                .maxBackoff(retryProperties.getMaxDelay())
                .filter(this::shouldRetry)
                .doBeforeRetry(retrySignal -> {
                    log.warn("Retrying request: {} {} (attempt {}/{})", 
                            request.method(), 
                            request.url(),
                            retrySignal.totalRetries() + 1,
                            retryProperties.getMaxAttempts());
                })
                .onRetryExhaustedThrow((retryBackoffSpec, retrySignal) -> {
                    log.error("Retry exhausted for request: {} {} after {} attempts", 
                            request.method(), 
                            request.url(),
                            retrySignal.totalRetries() + 1);
                    return retrySignal.failure();
                });
    }

    private boolean shouldRetry(Throwable throwable) {
        // 重试条件：
        // 1. WebClientResponseException 且状态码为 5xx
        // 2. 连接异常
        // 3. 超时异常
        
        if (throwable instanceof WebClientResponseException) {
            WebClientResponseException ex = (WebClientResponseException) throwable;
            return ex.getStatusCode().is5xxServerError();
        }
        
        // 检查是否是连接相关异常
        String message = throwable.getMessage();
        if (message != null) {
            String lowerMessage = message.toLowerCase();
            return lowerMessage.contains("connection") ||
                   lowerMessage.contains("timeout") ||
                   lowerMessage.contains("refused") ||
                   lowerMessage.contains("reset");
        }
        
        return false;
    }
}
