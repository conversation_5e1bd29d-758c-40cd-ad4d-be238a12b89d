# HandThing 多租户认证系统部署指南

## 概述

本文档提供了 HandThing 多租户认证系统在不同环境下的部署指南，包括开发环境、测试环境和生产环境的配置和部署步骤。

## 环境要求

### 基础环境
- **Java**: JDK 17 或更高版本
- **Spring Boot**: 3.5.3 或更高版本
- **Maven**: 3.6.0 或更高版本
- **数据库**: MySQL 8.0+ / PostgreSQL 13+ / H2（仅用于开发测试）

### 推荐配置
- **CPU**: 2核心或更多
- **内存**: 4GB 或更多
- **存储**: 20GB 或更多
- **网络**: 稳定的网络连接

## 快速部署

### 1. 添加依赖

在你的 Spring Boot 项目中添加依赖：

```xml
<dependency>
    <groupId>cn.com.handthing.springboot3.starter</groupId>
    <artifactId>tenant-auth-starter</artifactId>
    <version>1.0.0-SNAPSHOT</version>
</dependency>
```

### 2. 基础配置

创建 `application.yml` 配置文件：

```yaml
# 基础配置
spring:
  application:
    name: your-app-name
  
  # 数据库配置
  datasource:
    url: **************************************************************************************************************************
    username: ${DB_USERNAME:your_username}
    username: ${DB_PASSWORD:your_password}
    driver-class-name: com.mysql.cj.jdbc.Driver
    
    # 连接池配置
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      max-lifetime: 1800000
      connection-timeout: 30000
      pool-name: HikariCP-TenantAuth
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
        use_sql_comments: false
    open-in-view: false
  
  # SQL初始化配置
  sql:
    init:
      mode: always
      schema-locations: classpath:schema.sql
      data-locations: classpath:data.sql
      continue-on-error: false

# HandThing 多租户配置
handthing:
  auth:
    saas:
      enabled: true
      default-tenant: default
      tenant-required: false
      multi-tenant: true
      cache: true
      
      filter:
        enabled: true
        order: -200
        exclude-paths:
          - "/actuator/**"
          - "/error"
          - "/favicon.ico"
          - "/static/**"
          - "/public/**"
          - "/login"
          - "/auth/login"
      
      resolver:
        http-header:
          enabled: true
          primary-header: X-Tenant-ID
          fallback-headers:
            - Tenant-ID
            - X-Tenant
            - Tenant
          case-sensitive: false
          order: 20
      
      validation: true
      monitoring: true

# 日志配置
logging:
  level:
    cn.com.handthing.starter.tenant: INFO
    org.springframework.web.filter: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{tenantId:-}] %logger{36} - %msg%n"
```

### 3. 数据库初始化

创建数据库表结构文件 `src/main/resources/schema.sql`：

```sql
-- 租户信息表
CREATE TABLE IF NOT EXISTS tenants (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    tenant_id VARCHAR(64) UNIQUE NOT NULL,
    tenant_name VARCHAR(128) NOT NULL,
    tenant_code VARCHAR(64),
    status VARCHAR(32) DEFAULT 'ACTIVE',
    description TEXT,
    contact_email VARCHAR(255),
    contact_phone VARCHAR(32),
    contact_person VARCHAR(128),
    domain VARCHAR(255),
    logo_url VARCHAR(512),
    theme_config VARCHAR(2000),
    feature_flags VARCHAR(2000),
    quota_config VARCHAR(2000),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by VARCHAR(64),
    updated_by VARCHAR(64),
    version BIGINT DEFAULT 0
);

-- 租户配置表
CREATE TABLE IF NOT EXISTS tenant_configs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    tenant_id VARCHAR(64) NOT NULL,
    config_key VARCHAR(128) NOT NULL,
    config_value TEXT,
    config_type VARCHAR(32) DEFAULT 'STRING',
    description VARCHAR(255),
    enabled BOOLEAN DEFAULT TRUE,
    sensitive BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by VARCHAR(64),
    updated_by VARCHAR(64),
    version BIGINT DEFAULT 0
);

-- 创建索引
CREATE UNIQUE INDEX IF NOT EXISTS uk_tenant_config ON tenant_configs (tenant_id, config_key);
CREATE INDEX IF NOT EXISTS idx_tenant_id ON tenant_configs (tenant_id);
CREATE INDEX IF NOT EXISTS idx_config_key ON tenant_configs (config_key);
CREATE INDEX IF NOT EXISTS idx_enabled ON tenant_configs (enabled);
CREATE INDEX IF NOT EXISTS idx_tenant_status ON tenants (status);
CREATE INDEX IF NOT EXISTS idx_tenant_domain ON tenants (domain);
```

创建初始数据文件 `src/main/resources/data.sql`：

```sql
-- 插入默认租户
INSERT IGNORE INTO tenants (tenant_id, tenant_name, status, description, created_by) VALUES 
('default', '默认租户', 'ACTIVE', '系统默认租户，用于单租户模式或作为后备租户', 'system');

-- 插入默认租户的基础配置
INSERT IGNORE INTO tenant_configs (tenant_id, config_key, config_value, config_type, description, created_by) VALUES 
('default', 'tenant.name', '默认租户', 'STRING', '租户显示名称', 'system'),
('default', 'tenant.status', 'ACTIVE', 'STRING', '租户状态', 'system'),
('default', 'jwt.secret', 'handthing-auth-default-secret-key-2024', 'PASSWORD', 'JWT签名密钥', 'system'),
('default', 'jwt.access.token.expiration', '7200', 'INTEGER', '访问令牌过期时间（秒）', 'system'),
('default', 'password.auth.enabled', 'true', 'BOOLEAN', '是否启用密码认证', 'system'),
('default', 'cache.enabled', 'true', 'BOOLEAN', '是否启用缓存', 'system');
```

### 4. 启动应用

```bash
mvn spring-boot:run
```

## 环境配置

### 开发环境

开发环境配置文件 `application-dev.yml`：

```yaml
spring:
  profiles:
    active: dev
  
  # 使用H2内存数据库
  datasource:
    url: jdbc:h2:mem:testdb
    username: sa
    password: 
    driver-class-name: org.h2.Driver
  
  # H2控制台
  h2:
    console:
      enabled: true
      path: /h2-console
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true

# 开发环境日志
logging:
  level:
    cn.com.handthing.starter.tenant: DEBUG
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE

# 多租户配置
handthing:
  auth:
    saas:
      filter:
        exclude-paths:
          - "/actuator/**"
          - "/error"
          - "/favicon.ico"
          - "/static/**"
          - "/public/**"
          - "/h2-console/**"
          - "/*.html"
```

### 测试环境

测试环境配置文件 `application-test.yml`：

```yaml
spring:
  profiles:
    active: test
  
  # 使用MySQL数据库
  datasource:
    url: **********************************************************************************************************************************
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
    
    hikari:
      maximum-pool-size: 10
      minimum-idle: 2
  
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false

# 测试环境日志
logging:
  level:
    cn.com.handthing.starter.tenant: INFO
    org.springframework.web.filter: INFO
  file:
    name: logs/tenant-auth-test.log

# 缓存配置
spring:
  cache:
    type: caffeine
    caffeine:
      spec: maximumSize=500,expireAfterWrite=10m
```

### 生产环境

生产环境配置文件 `application-prod.yml`：

```yaml
spring:
  profiles:
    active: prod
  
  # 生产数据库配置
  datasource:
    url: *********************************************************************************************************************************
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
    
    hikari:
      maximum-pool-size: 50
      minimum-idle: 10
      idle-timeout: 300000
      max-lifetime: 1800000
      connection-timeout: 30000
      leak-detection-threshold: 60000
  
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true

# 生产环境日志
logging:
  level:
    cn.com.handthing.starter.tenant: WARN
    org.springframework.web.filter: WARN
    org.hibernate: WARN
  file:
    name: logs/tenant-auth-prod.log
    max-size: 100MB
    max-history: 30

# 生产缓存配置
spring:
  cache:
    type: caffeine
    caffeine:
      spec: maximumSize=10000,expireAfterWrite=30m

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true

# 安全配置
handthing:
  auth:
    saas:
      tenant-required: true
      validation: true
      monitoring: true
```

## Docker 部署

### 1. Dockerfile

创建 `Dockerfile`：

```dockerfile
FROM openjdk:17-jre-slim

# 设置工作目录
WORKDIR /app

# 复制jar包
COPY target/your-app-name-*.jar app.jar

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 创建日志目录
RUN mkdir -p /app/logs

# 暴露端口
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:8080/actuator/health || exit 1

# 启动应用
ENTRYPOINT ["java", "-jar", "-Dspring.profiles.active=prod", "app.jar"]
```

### 2. Docker Compose

创建 `docker-compose.yml`：

```yaml
version: '3.8'

services:
  # 应用服务
  tenant-auth-app:
    build: .
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - DB_USERNAME=tenant_auth
      - DB_PASSWORD=your_secure_password
      - JAVA_OPTS=-Xmx2g -Xms1g
    depends_on:
      - mysql
      - redis
    volumes:
      - ./logs:/app/logs
    networks:
      - tenant-auth-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # MySQL数据库
  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=root_password
      - MYSQL_DATABASE=tenant_auth_prod
      - MYSQL_USER=tenant_auth
      - MYSQL_PASSWORD=your_secure_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - tenant-auth-network
    restart: unless-stopped
    command: --default-authentication-plugin=mysql_native_password

  # Redis缓存
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - tenant-auth-network
    restart: unless-stopped
    command: redis-server --appendonly yes

volumes:
  mysql_data:
  redis_data:

networks:
  tenant-auth-network:
    driver: bridge
```

### 3. 构建和部署

```bash
# 构建应用
mvn clean package -DskipTests

# 构建Docker镜像
docker build -t tenant-auth-app:latest .

# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f tenant-auth-app

# 停止服务
docker-compose down
```

## Kubernetes 部署

### 1. ConfigMap

创建 `k8s/configmap.yaml`：

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: tenant-auth-config
  namespace: default
data:
  application.yml: |
    spring:
      profiles:
        active: k8s
      datasource:
        url: *********************************************************************************************************************************
        username: ${DB_USERNAME}
        password: ${DB_PASSWORD}
        hikari:
          maximum-pool-size: 20
          minimum-idle: 5
      jpa:
        hibernate:
          ddl-auto: update
        show-sql: false
    handthing:
      auth:
        saas:
          enabled: true
          default-tenant: default
          multi-tenant: true
          cache: true
    logging:
      level:
        cn.com.handthing.starter.tenant: INFO
```

### 2. Secret

创建 `k8s/secret.yaml`：

```yaml
apiVersion: v1
kind: Secret
metadata:
  name: tenant-auth-secret
  namespace: default
type: Opaque
data:
  DB_USERNAME: dGVuYW50X2F1dGg=  # base64 encoded
  DB_PASSWORD: eW91cl9zZWN1cmVfcGFzc3dvcmQ=  # base64 encoded
```

### 3. Deployment

创建 `k8s/deployment.yaml`：

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tenant-auth-app
  namespace: default
spec:
  replicas: 3
  selector:
    matchLabels:
      app: tenant-auth-app
  template:
    metadata:
      labels:
        app: tenant-auth-app
    spec:
      containers:
      - name: tenant-auth-app
        image: tenant-auth-app:latest
        ports:
        - containerPort: 8080
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "k8s"
        - name: DB_USERNAME
          valueFrom:
            secretKeyRef:
              name: tenant-auth-secret
              key: DB_USERNAME
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: tenant-auth-secret
              key: DB_PASSWORD
        volumeMounts:
        - name: config-volume
          mountPath: /app/config
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
      volumes:
      - name: config-volume
        configMap:
          name: tenant-auth-config
```

### 4. Service

创建 `k8s/service.yaml`：

```yaml
apiVersion: v1
kind: Service
metadata:
  name: tenant-auth-service
  namespace: default
spec:
  selector:
    app: tenant-auth-app
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8080
  type: LoadBalancer
```

### 5. 部署到Kubernetes

```bash
# 应用配置
kubectl apply -f k8s/configmap.yaml
kubectl apply -f k8s/secret.yaml
kubectl apply -f k8s/deployment.yaml
kubectl apply -f k8s/service.yaml

# 查看部署状态
kubectl get pods
kubectl get services

# 查看日志
kubectl logs -f deployment/tenant-auth-app

# 扩容
kubectl scale deployment tenant-auth-app --replicas=5
```

## 监控和运维

### 1. 健康检查

应用提供了多个健康检查端点：

```bash
# 基础健康检查
curl http://localhost:8080/actuator/health

# 租户健康检查
curl -H "X-Tenant-ID: demo" http://localhost:8080/api/tenant/health

# 详细健康信息
curl http://localhost:8080/actuator/health/db
```

### 2. 指标监控

配置 Prometheus 监控：

```yaml
# prometheus.yml
scrape_configs:
  - job_name: 'tenant-auth-app'
    static_configs:
      - targets: ['localhost:8080']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 15s
```

### 3. 日志收集

使用 ELK Stack 收集日志：

```yaml
# logstash.conf
input {
  file {
    path => "/app/logs/*.log"
    start_position => "beginning"
  }
}

filter {
  grok {
    match => { "message" => "%{TIMESTAMP_ISO8601:timestamp} \[%{DATA:thread}\] %{LOGLEVEL:level} \[%{DATA:tenant_id}\] %{DATA:logger} - %{GREEDYDATA:message}" }
  }
}

output {
  elasticsearch {
    hosts => ["elasticsearch:9200"]
    index => "tenant-auth-logs-%{+YYYY.MM.dd}"
  }
}
```

## 故障排除

### 常见问题

1. **数据库连接失败**
   ```bash
   # 检查数据库连接
   telnet mysql-server 3306
   
   # 检查数据库用户权限
   mysql -u tenant_auth -p -h mysql-server
   ```

2. **租户解析失败**
   ```bash
   # 检查HTTP头部
   curl -H "X-Tenant-ID: demo" -v http://localhost:8080/api/tenant/current
   
   # 查看过滤器日志
   kubectl logs -f deployment/tenant-auth-app | grep TenantResolverFilter
   ```

3. **配置加载失败**
   ```bash
   # 检查配置文件
   kubectl describe configmap tenant-auth-config
   
   # 检查环境变量
   kubectl exec -it pod/tenant-auth-app-xxx -- env | grep DB_
   ```

### 性能调优

1. **JVM参数优化**
   ```bash
   JAVA_OPTS="-Xmx4g -Xms2g -XX:+UseG1GC -XX:MaxGCPauseMillis=200"
   ```

2. **数据库连接池调优**
   ```yaml
   spring:
     datasource:
       hikari:
         maximum-pool-size: 50
         minimum-idle: 10
         idle-timeout: 300000
   ```

3. **缓存配置优化**
   ```yaml
   spring:
     cache:
       caffeine:
         spec: maximumSize=10000,expireAfterWrite=30m
   ```

## 安全建议

### 1. 数据库安全
- 使用强密码
- 限制数据库访问IP
- 启用SSL连接
- 定期备份数据

### 2. 应用安全
- 使用HTTPS
- 配置防火墙规则
- 定期更新依赖
- 监控异常访问

### 3. 容器安全
- 使用非root用户运行
- 扫描镜像漏洞
- 限制容器权限
- 定期更新基础镜像

## 备份和恢复

### 1. 数据库备份

```bash
# MySQL备份
mysqldump -u tenant_auth -p tenant_auth_prod > backup_$(date +%Y%m%d_%H%M%S).sql

# 自动备份脚本
#!/bin/bash
BACKUP_DIR="/backup/mysql"
DATE=$(date +%Y%m%d_%H%M%S)
mysqldump -u tenant_auth -p$DB_PASSWORD tenant_auth_prod > $BACKUP_DIR/tenant_auth_$DATE.sql
gzip $BACKUP_DIR/tenant_auth_$DATE.sql

# 保留最近30天的备份
find $BACKUP_DIR -name "tenant_auth_*.sql.gz" -mtime +30 -delete
```

### 2. 配置备份

```bash
# 备份Kubernetes配置
kubectl get configmap tenant-auth-config -o yaml > config-backup.yaml
kubectl get secret tenant-auth-secret -o yaml > secret-backup.yaml

# 备份应用配置
cp application-prod.yml backup/application-prod-$(date +%Y%m%d).yml
```

### 3. 数据恢复

```bash
# MySQL恢复
mysql -u tenant_auth -p tenant_auth_prod < backup_20250729_120000.sql

# Kubernetes配置恢复
kubectl apply -f config-backup.yaml
kubectl apply -f secret-backup.yaml
```

---

更多部署相关问题，请参考 [HandThing 官方文档](https://docs.handthing.com) 或提交 Issue。
