package cn.com.handthing.starter.cache.level.sync;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * CacheSyncMessage 单元测试
 *
 * <AUTHOR>
 * @since V1.0.0
 */
class CacheSyncMessageTest {

    @Test
    void testEvictMessage() {
        // Given
        String senderId = "sender-123";
        String cacheName = "testCache";
        String key = "testKey";

        // When
        CacheSyncMessage message = CacheSyncMessage.evict(senderId, cacheName, key);

        // Then
        assertNotNull(message);
        assertEquals(senderId, message.getSenderId());
        assertEquals(cacheName, message.getCacheName());
        assertEquals(key, message.getKey());
        assertEquals(CacheSyncMessage.OperationType.EVICT, message.getOperation());
        assertNotNull(message.getMessageId());
        assertNotNull(message.getTimestamp());
    }

    @Test
    void testClearMessage() {
        // Given
        String senderId = "sender-123";
        String cacheName = "testCache";

        // When
        CacheSyncMessage message = CacheSyncMessage.clear(senderId, cacheName);

        // Then
        assertNotNull(message);
        assertEquals(senderId, message.getSenderId());
        assertEquals(cacheName, message.getCacheName());
        assertNull(message.getKey());
        assertEquals(CacheSyncMessage.OperationType.CLEAR, message.getOperation());
        assertNotNull(message.getMessageId());
        assertNotNull(message.getTimestamp());
    }

    @Test
    void testPutMessage() {
        // Given
        String senderId = "sender-123";
        String cacheName = "testCache";
        String key = "testKey";

        // When
        CacheSyncMessage message = CacheSyncMessage.put(senderId, cacheName, key);

        // Then
        assertNotNull(message);
        assertEquals(senderId, message.getSenderId());
        assertEquals(cacheName, message.getCacheName());
        assertEquals(key, message.getKey());
        assertEquals(CacheSyncMessage.OperationType.PUT, message.getOperation());
        assertNotNull(message.getMessageId());
        assertNotNull(message.getTimestamp());
    }

    @Test
    void testMessageIdUniqueness() {
        // Given
        String senderId = "sender-123";
        String cacheName = "testCache";
        String key = "testKey";

        // When
        CacheSyncMessage message1 = CacheSyncMessage.evict(senderId, cacheName, key);
        CacheSyncMessage message2 = CacheSyncMessage.evict(senderId, cacheName, key);

        // Then
        assertNotEquals(message1.getMessageId(), message2.getMessageId());
    }

    @Test
    void testSerializable() {
        // Given
        CacheSyncMessage message = CacheSyncMessage.evict("sender", "cache", "key");

        // Then
        assertTrue(message instanceof java.io.Serializable);
    }

    @Test
    void testBuilderPattern() {
        // Given
        String messageId = "msg-123";
        String senderId = "sender-123";
        String cacheName = "testCache";
        String key = "testKey";
        CacheSyncMessage.OperationType operation = CacheSyncMessage.OperationType.EVICT;

        // When
        CacheSyncMessage message = CacheSyncMessage.builder()
                .messageId(messageId)
                .senderId(senderId)
                .cacheName(cacheName)
                .key(key)
                .operation(operation)
                .build();

        // Then
        assertEquals(messageId, message.getMessageId());
        assertEquals(senderId, message.getSenderId());
        assertEquals(cacheName, message.getCacheName());
        assertEquals(key, message.getKey());
        assertEquals(operation, message.getOperation());
    }

    @Test
    void testEqualsAndHashCode() {
        // Given
        CacheSyncMessage message1 = CacheSyncMessage.builder()
                .messageId("msg-123")
                .senderId("sender")
                .cacheName("cache")
                .key("key")
                .operation(CacheSyncMessage.OperationType.EVICT)
                .build();

        CacheSyncMessage message2 = CacheSyncMessage.builder()
                .messageId("msg-123")
                .senderId("sender")
                .cacheName("cache")
                .key("key")
                .operation(CacheSyncMessage.OperationType.EVICT)
                .build();

        CacheSyncMessage message3 = CacheSyncMessage.builder()
                .messageId("msg-456")
                .senderId("sender")
                .cacheName("cache")
                .key("key")
                .operation(CacheSyncMessage.OperationType.EVICT)
                .build();

        // Then
        assertEquals(message1, message2);
        assertEquals(message1.hashCode(), message2.hashCode());
        assertNotEquals(message1, message3);
        assertNotEquals(message1.hashCode(), message3.hashCode());
    }
}