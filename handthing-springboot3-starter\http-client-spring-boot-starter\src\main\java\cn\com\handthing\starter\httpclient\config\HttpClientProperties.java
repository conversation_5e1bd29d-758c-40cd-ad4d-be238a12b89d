package cn.com.handthing.starter.httpclient.config;

import jakarta.validation.Valid;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * HTTP客户端starter的顶层配置属性类。
 * <p>
 * 绑定到 'handthing.http-client' 前缀。
 */
@ConfigurationProperties(prefix = "handthing.http-client")
@Validated // 开启对此Bean及其嵌套对象的JSR-303验证
public class HttpClientProperties {

    /**
     * 是否启用整个HTTP客户端starter。
     */
    private boolean enabled = true;

    /**
     * 选择HTTP客户端的实现类型。
     */
    private ClientType clientType = ClientType.AUTO;

    /**
     * 全局默认配置。
     * 当一个请求URL没有在 'endpoints' 映射中找到特定配置时，将使用此配置。
     */
    @Valid // 级联验证
    private EndpointConfig defaultConfig = new EndpointConfig();

    /**
     * 针对特定URL模式的端点配置映射。
     * Key是Ant风格的URL匹配模式 (例如: "/api/v1/**", "https://example.com/payment/**")。
     * Value是该模式对应的具体配置。
     */
    private Map<String, @Valid EndpointConfig> endpoints = new HashMap<>();

    // --- Standard Getters and Setters, equals, hashCode, toString ---

    public HttpClientProperties() {
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public ClientType getClientType() {
        return clientType;
    }

    public void setClientType(ClientType clientType) {
        this.clientType = clientType;
    }

    public EndpointConfig getDefaultConfig() {
        return defaultConfig;
    }

    public void setDefaultConfig(EndpointConfig defaultConfig) {
        this.defaultConfig = defaultConfig;
    }

    public Map<String, EndpointConfig> getEndpoints() {
        return endpoints;
    }

    public void setEndpoints(Map<String, EndpointConfig> endpoints) {
        this.endpoints = endpoints;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        HttpClientProperties that = (HttpClientProperties) o;
        return enabled == that.enabled && clientType == that.clientType && Objects.equals(defaultConfig, that.defaultConfig) && Objects.equals(endpoints, that.endpoints);
    }

    @Override
    public int hashCode() {
        return Objects.hash(enabled, clientType, defaultConfig, endpoints);
    }

    @Override
    public String toString() {
        return "HttpClientProperties{" +
                "enabled=" + enabled +
                ", clientType=" + clientType +
                ", defaultConfig=" + defaultConfig +
                ", endpoints=" + endpoints +
                '}';
    }
}