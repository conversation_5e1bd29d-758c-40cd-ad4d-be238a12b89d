package cn.com.handthing.starter.cache;

import cn.com.handthing.starter.cache.config.CacheProperties;
import cn.com.handthing.starter.cache.impl.DefaultCacheService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.autoconfigure.AutoConfigurations;
import org.springframework.boot.test.context.runner.ApplicationContextRunner;
import org.springframework.cache.CacheManager;
import org.springframework.cache.concurrent.ConcurrentMapCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * CacheAutoConfiguration 单元测试
 *
 * <AUTHOR>
 * @since V1.0.0
 */
class CacheAutoConfigurationTest {

    private final ApplicationContextRunner contextRunner = new ApplicationContextRunner()
            .withConfiguration(AutoConfigurations.of(CacheAutoConfiguration.class))
            .withUserConfiguration(TestCacheManagerConfiguration.class);

    @Test
    void testAutoConfiguration_Enabled() {
        contextRunner
                .withPropertyValues("handthing.cache.enabled=true")
                .run(context -> {
                    assertThat(context).hasSingleBean(CacheProperties.class);
                    assertThat(context).hasSingleBean(CacheService.class);
                    assertThat(context).getBean(CacheService.class)
                            .isInstanceOf(DefaultCacheService.class);
                });
    }

    @Test
    void testAutoConfiguration_Disabled() {
        contextRunner
                .withPropertyValues("handthing.cache.enabled=false")
                .run(context -> {
                    assertThat(context).doesNotHaveBean(CacheService.class);
                });
    }

    @Test
    void testAutoConfiguration_DefaultEnabled() {
        contextRunner
                .run(context -> {
                    assertThat(context).hasSingleBean(CacheService.class);
                });
    }

    @Test
    void testCacheProperties_Binding() {
        contextRunner
                .withPropertyValues(
                        "handthing.cache.type=caffeine",
                        "handthing.cache.allow-null-values=false",
                        "handthing.cache.default-ttl=PT10M",
                        "handthing.cache.specs.users.ttl=PT5M",
                        "handthing.cache.specs.users.max-size=1000"
                )
                .run(context -> {
                    CacheProperties properties = context.getBean(CacheProperties.class);
                    assertThat(properties.getType()).isEqualTo("caffeine");
                    assertThat(properties.isAllowNullValues()).isFalse();
                    assertThat(properties.getDefaultTtl().toMinutes()).isEqualTo(10);
                    
                    CacheProperties.CacheSpec userSpec = properties.getSpecs().get("users");
                    assertThat(userSpec).isNotNull();
                    assertThat(userSpec.getTtl().toMinutes()).isEqualTo(5);
                    assertThat(userSpec.getMaxSize()).isEqualTo(1000L);
                });
    }

    @Configuration
    static class TestCacheManagerConfiguration {
        
        @Bean
        public CacheManager cacheManager() {
            return new ConcurrentMapCacheManager();
        }
    }
}