package cn.com.handthing.starter.httpclient.exception;

/**
 * 表示在建立到目标服务器的连接时发生超时的异常。
 * <p>
 * 这是一种特定的 {@link HttpRequestException}。
 */
public class HttpConnectTimeoutException extends HttpRequestException {

    /**
     * 使用指定的详细消息和原因构造一个新的连接超时异常。
     *
     * @param message 详细消息。
     * @param cause   原因，通常是底层客户端抛出的与连接超时相关的原生异常。
     */
    public HttpConnectTimeoutException(String message, Throwable cause) {
        super(message, cause);
    }
}