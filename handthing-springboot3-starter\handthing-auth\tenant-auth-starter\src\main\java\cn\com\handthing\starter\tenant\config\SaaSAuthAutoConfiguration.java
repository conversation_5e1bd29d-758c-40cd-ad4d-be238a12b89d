package cn.com.handthing.starter.tenant.config;

import cn.com.handthing.starter.tenant.filter.TenantResolverFilter;
import cn.com.handthing.starter.tenant.resolver.HttpHeaderTenantResolver;
import cn.com.handthing.starter.tenant.resolver.RequestPathTenantResolver;
import cn.com.handthing.starter.tenant.resolver.SubdomainTenantResolver;
import cn.com.handthing.starter.tenant.resolver.TenantResolver;
import cn.com.handthing.starter.tenant.service.TenantConfigService;
import cn.com.handthing.starter.tenant.service.impl.TenantConfigServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;

/**
 * SaaS认证自动配置
 * <p>
 * 自动装配TenantResolverFilter，确保高优先级；
 * 自动装配TenantConfigService，集成cache-starter；
 * 根据配置条件装配不同的TenantResolver实现；
 * 配置租户相关的Bean和属性。
 * </p>
 *
 * <AUTHOR>
 * @since V3.0.0
 */
@AutoConfiguration
@ConditionalOnProperty(prefix = "handthing.auth.saas", name = "enabled", havingValue = "true", matchIfMissing = true)
@EnableConfigurationProperties({SaaSAuthProperties.class, TenantResolverProperties.class})
@EnableJpaRepositories(basePackages = "cn.com.handthing.starter.tenant.repository")
@EntityScan(basePackages = "cn.com.handthing.starter.tenant.config")
@ComponentScan(basePackages = "cn.com.handthing.starter.tenant")
public class SaaSAuthAutoConfiguration {

    private static final Logger log = LoggerFactory.getLogger(SaaSAuthAutoConfiguration.class);

    /**
     * 租户解析器配置
     */
    @Configuration
    @ConditionalOnWebApplication(type = ConditionalOnWebApplication.Type.SERVLET)
    static class TenantResolverConfiguration {

        /**
         * 子域名租户解析器
         */
        @Bean
        @ConditionalOnProperty(prefix = "handthing.auth.tenant.resolver.subdomain", name = "enabled", havingValue = "true")
        @ConditionalOnMissingBean(name = "subdomainTenantResolver")
        public SubdomainTenantResolver subdomainTenantResolver(TenantResolverProperties properties) {
            TenantResolverProperties.SubdomainConfig config = properties.getSubdomain();
            
            SubdomainTenantResolver resolver;
            if (config.getBaseDomain() != null) {
                resolver = new SubdomainTenantResolver(config.getBaseDomain());
            } else if (config.getPattern() != null) {
                resolver = SubdomainTenantResolver.fromPattern(config.getPattern());
            } else {
                resolver = new SubdomainTenantResolver();
            }
            
            log.info("Configured SubdomainTenantResolver: pattern={}, baseDomain={}, order={}", 
                     config.getPattern(), config.getBaseDomain(), config.getOrder());
            
            return resolver;
        }

        /**
         * HTTP头租户解析器
         */
        @Bean
        @ConditionalOnProperty(prefix = "handthing.auth.tenant.resolver.header", name = "enabled", havingValue = "true", matchIfMissing = true)
        @ConditionalOnMissingBean(name = "httpHeaderTenantResolver")
        public HttpHeaderTenantResolver httpHeaderTenantResolver(TenantResolverProperties properties) {
            TenantResolverProperties.HeaderConfig config = properties.getHeader();
            
            HttpHeaderTenantResolver resolver = new HttpHeaderTenantResolver(
                    config.getName(), 
                    config.getFallbackNames(), 
                    config.isCaseSensitive()
            );
            
            log.info("Configured HttpHeaderTenantResolver: primary={}, fallback={}, caseSensitive={}, order={}", 
                     config.getName(), config.getFallbackNames(), config.isCaseSensitive(), config.getOrder());
            
            return resolver;
        }

        /**
         * URL路径租户解析器
         */
        @Bean
        @ConditionalOnProperty(prefix = "handthing.auth.tenant.resolver.path", name = "enabled", havingValue = "true")
        @ConditionalOnMissingBean(name = "requestPathTenantResolver")
        public RequestPathTenantResolver requestPathTenantResolver(TenantResolverProperties properties) {
            TenantResolverProperties.PathConfig config = properties.getPath();
            
            RequestPathTenantResolver resolver;
            if (config.getPattern() != null) {
                resolver = RequestPathTenantResolver.fromPattern(config.getPattern());
            } else {
                resolver = RequestPathTenantResolver.forPrefix(config.getPrefix(), config.isRemovePrefix());
            }
            
            log.info("Configured RequestPathTenantResolver: pattern={}, prefix={}, removePrefix={}, order={}", 
                     config.getPattern(), config.getPrefix(), config.isRemovePrefix(), config.getOrder());
            
            return resolver;
        }

        /**
         * 租户解析器列表
         */
        @Bean
        @ConditionalOnMissingBean
        public List<TenantResolver> tenantResolvers(List<TenantResolver> resolvers, TenantResolverProperties properties) {
            // 按优先级排序
            List<TenantResolver> sortedResolvers = new ArrayList<>(resolvers);
            sortedResolvers.sort(Comparator.comparingInt(TenantResolver::getOrder));
            
            log.info("Configured {} tenant resolvers: {}", 
                     sortedResolvers.size(), 
                     sortedResolvers.stream().map(TenantResolver::getName).toArray());
            
            return sortedResolvers;
        }
    }

    /**
     * 租户配置服务配置
     */
    @Configuration
    static class TenantConfigServiceConfiguration {

        /**
         * 租户配置服务
         */
        @Bean
        @ConditionalOnMissingBean
        public TenantConfigService tenantConfigService(
                cn.com.handthing.starter.tenant.repository.TenantConfigRepository tenantConfigRepository,
                ObjectMapper objectMapper) {
            
            TenantConfigServiceImpl service = new TenantConfigServiceImpl(tenantConfigRepository, objectMapper);
            
            log.info("Configured TenantConfigService with cache support");
            
            return service;
        }

        /**
         * JSON对象映射器
         */
        @Bean
        @ConditionalOnMissingBean
        public ObjectMapper objectMapper() {
            ObjectMapper mapper = new ObjectMapper();
            mapper.findAndRegisterModules();
            return mapper;
        }
    }

    /**
     * 租户解析过滤器配置
     */
    @Configuration
    @ConditionalOnWebApplication(type = ConditionalOnWebApplication.Type.SERVLET)
    @ConditionalOnProperty(prefix = "handthing.auth.saas.filter", name = "enabled", havingValue = "true", matchIfMissing = true)
    static class TenantResolverFilterConfiguration {

        /**
         * 租户解析过滤器注册
         */
        @Bean
        @ConditionalOnMissingBean
        public FilterRegistrationBean<TenantResolverFilter> tenantResolverFilterRegistration(
                List<TenantResolver> tenantResolvers,
                TenantConfigService tenantConfigService,
                SaaSAuthProperties properties) {
            
            TenantResolverFilter filter = new TenantResolverFilter(
                    tenantResolvers,
                    tenantConfigService,
                    properties.getEffectiveDefaultTenantId(),
                    properties.isTenantRequired(),
                    properties.getFilter().getOrder(),
                    Arrays.asList(properties.getFilter().getExcludePaths())
            );

            FilterRegistrationBean<TenantResolverFilter> registration = new FilterRegistrationBean<>(filter);
            registration.setOrder(properties.getFilter().getOrder());
            registration.setName("tenantResolverFilter");
            
            // 设置URL模式
            if (properties.getFilter().getIncludePaths().length > 0) {
                registration.addUrlPatterns(properties.getFilter().getIncludePaths());
            } else {
                registration.addUrlPatterns("/*");
            }
            
            log.info("Configured TenantResolverFilter: order={}, defaultTenant={}, required={}, resolvers={}", 
                     properties.getFilter().getOrder(),
                     properties.getEffectiveDefaultTenantId(),
                     properties.isTenantRequired(),
                     tenantResolvers.size());
            
            return registration;
        }
    }

    /**
     * 缓存配置
     */
    @Configuration
    @ConditionalOnProperty(prefix = "handthing.auth.saas.cache", name = "enabled", havingValue = "true", matchIfMissing = true)
    static class TenantCacheConfiguration {

        /**
         * 租户配置缓存管理器
         * 这里可以根据需要配置特定的缓存管理器
         */
        // 暂时注释掉，因为需要依赖cache-starter的具体实现
        /*
        @Bean
        @ConditionalOnMissingBean(name = "tenantConfigCacheManager")
        public CacheManager tenantConfigCacheManager(SaaSAuthProperties properties) {
            // 配置租户配置专用的缓存管理器
            return new ConcurrentMapCacheManager(properties.getCache().getCacheName());
        }
        */
    }

    /**
     * 监控配置
     */
    @Configuration
    @ConditionalOnProperty(prefix = "handthing.auth.saas.monitoring", name = "enabled", havingValue = "true", matchIfMissing = true)
    static class TenantMonitoringConfiguration {

        /**
         * 租户健康检查指示器
         */
        // 暂时注释掉，因为需要依赖actuator
        /*
        @Bean
        @ConditionalOnClass(HealthIndicator.class)
        @ConditionalOnProperty(prefix = "handthing.auth.saas.monitoring", name = "enable-health-check", havingValue = "true", matchIfMissing = true)
        public TenantHealthIndicator tenantHealthIndicator(TenantConfigService tenantConfigService) {
            return new TenantHealthIndicator(tenantConfigService);
        }
        */
    }

    /**
     * 初始化日志
     */
    public SaaSAuthAutoConfiguration(SaaSAuthProperties properties) {
        log.info("SaaS Auth Auto Configuration initialized: {}", properties.getConfigSummary());
    }
}
