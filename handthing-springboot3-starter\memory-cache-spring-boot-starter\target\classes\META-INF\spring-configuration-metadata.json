{"groups": [{"name": "handthing.cache.memory", "type": "cn.com.handthing.starter.cache.memory.config.MemoryCacheProperties", "sourceType": "cn.com.handthing.starter.cache.memory.config.MemoryCacheProperties"}], "properties": [{"name": "handthing.cache.memory.caches", "type": "java.util.Map<java.lang.String,cn.com.handthing.starter.cache.memory.config.MemoryCacheProperties$MemoryCacheSpec>", "description": "各个缓存区域的独立配置", "sourceType": "cn.com.handthing.starter.cache.memory.config.MemoryCacheProperties"}, {"name": "handthing.cache.memory.cleanup-interval", "type": "java.time.Duration", "description": "清理过期缓存的间隔时间", "sourceType": "cn.com.handthing.starter.cache.memory.config.MemoryCacheProperties"}, {"name": "handthing.cache.memory.default-max-size", "type": "java.lang.Long", "description": "默认最大缓存条目数", "sourceType": "cn.com.handthing.starter.cache.memory.config.MemoryCacheProperties"}, {"name": "handthing.cache.memory.default-ttl", "type": "java.time.Duration", "description": "默认过期时间", "sourceType": "cn.com.handthing.starter.cache.memory.config.MemoryCacheProperties"}, {"name": "handthing.cache.memory.enable-stats", "type": "java.lang.Bo<PERSON>an", "description": "是否启用统计信息", "sourceType": "cn.com.handthing.starter.cache.memory.config.MemoryCacheProperties"}, {"name": "handthing.cache.memory.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用内存缓存", "sourceType": "cn.com.handthing.starter.cache.memory.config.MemoryCacheProperties"}], "hints": [], "ignored": {"properties": []}}