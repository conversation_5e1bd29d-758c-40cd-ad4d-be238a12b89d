package cn.com.handthing.starter.connector.bilibili;

import cn.com.handthing.starter.connector.PlatformType;
import cn.com.handthing.starter.connector.auth.AuthProvider;
import cn.com.handthing.starter.connector.auth.UnifiedAccessToken;
import cn.com.handthing.starter.connector.auth.UnifiedUserInfo;
import cn.com.handthing.starter.connector.exception.AuthException;
import cn.com.handthing.starter.connector.bilibili.config.BilibiliConfig;
import cn.com.handthing.starter.connector.bilibili.model.BilibiliAccessTokenResponse;
import cn.com.handthing.starter.connector.bilibili.model.BilibiliUserInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * Bilibili认证提供者
 * <p>
 * 实现Bilibili OAuth2.0认证流程，支持个人创作者、企业用户和MCN机构的认证。
 * 提供统一的认证接口，处理授权码交换、Token刷新等操作。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "handthing.connector.bilibili", name = "enabled", havingValue = "true")
public class BilibiliAuthProvider implements AuthProvider {

    private final BilibiliConfig config;
    private final RestTemplate restTemplate;

    @Override
    public PlatformType getPlatformType() {
        return PlatformType.BILIBILI;
    }

    @Override
    public String getAuthorizationUrl(String state, String redirectUri) {
        try {
            log.debug("Generating Bilibili authorization URL with state: {}, redirectUri: {}", state, redirectUri);

            // 使用配置的回调地址或传入的地址
            String effectiveRedirectUri = redirectUri != null ? redirectUri : config.getRedirectUri();
            if (effectiveRedirectUri == null || effectiveRedirectUri.trim().isEmpty()) {
                throw new AuthException("Redirect URI is required for Bilibili authorization");
            }

            // 构建Bilibili授权URL
            return UriComponentsBuilder.fromHttpUrl(config.getEffectiveApiBaseUrl() + "/oauth2/authorize")
                    .queryParam("client_id", config.getAppKey())
                    .queryParam("response_type", "code")
                    .queryParam("scope", config.getScope())
                    .queryParam("redirect_uri", URLEncoder.encode(effectiveRedirectUri, StandardCharsets.UTF_8))
                    .queryParam("state", state)
                    .build()
                    .toUriString();

        } catch (Exception e) {
            log.error("Failed to generate Bilibili authorization URL", e);
            throw new AuthException("Failed to generate authorization URL", e);
        }
    }

    @Override
    public UnifiedAccessToken exchangeToken(String code, String state, String redirectUri) {
        try {
            log.debug("Exchanging Bilibili authorization code for access token: {}", code);

            // 构建获取access_token的URL
            String tokenUrl = config.getEffectiveApiBaseUrl() + "/oauth2/access_token";

            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("client_id", config.getAppKey());
            requestBody.put("client_secret", config.getAppSecret());
            requestBody.put("code", code);
            requestBody.put("grant_type", "authorization_code");

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);

            // 发送请求获取access_token
            ResponseEntity<BilibiliAccessTokenResponse> response = restTemplate.postForEntity(
                    tokenUrl, request, BilibiliAccessTokenResponse.class);

            BilibiliAccessTokenResponse tokenResponse = response.getBody();
            if (tokenResponse == null) {
                throw new AuthException("Empty response from Bilibili token endpoint");
            }

            if (!tokenResponse.isSuccess()) {
                throw new AuthException("Bilibili token exchange failed: " + tokenResponse.getErrorMessage());
            }

            // 转换为统一的访问令牌格式
            Map<String, Object> extraInfo = new HashMap<>();
            extraInfo.put("mid", tokenResponse.getData().getMid());
            extraInfo.put("scope", tokenResponse.getData().getScope());
            
            return UnifiedAccessToken.builder()
                    .accessToken(tokenResponse.getData().getAccessToken())
                    .refreshToken(tokenResponse.getData().getRefreshToken())
                    .expiresIn(tokenResponse.getData().getExpiresIn())
                    .scope(tokenResponse.getData().getScope())
                    .platform(PlatformType.BILIBILI)
                    .userId(String.valueOf(tokenResponse.getData().getMid()))
                    .extraInfo(extraInfo)
                    .createdAt(LocalDateTime.now())
                    .build();

        } catch (Exception e) {
            log.error("Failed to exchange Bilibili authorization code for token", e);
            throw new AuthException("Failed to exchange code for token", e);
        }
    }

    @Override
    public UnifiedAccessToken refreshToken(String refreshToken) {
        try {
            log.debug("Refreshing Bilibili access token");

            // 构建刷新token的URL
            String refreshUrl = config.getEffectiveApiBaseUrl() + "/oauth2/refresh_token";

            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("client_id", config.getAppKey());
            requestBody.put("client_secret", config.getAppSecret());
            requestBody.put("refresh_token", refreshToken);
            requestBody.put("grant_type", "refresh_token");

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);

            // 发送请求刷新token
            ResponseEntity<BilibiliAccessTokenResponse> response = restTemplate.postForEntity(
                    refreshUrl, request, BilibiliAccessTokenResponse.class);

            BilibiliAccessTokenResponse tokenResponse = response.getBody();
            if (tokenResponse == null) {
                throw new AuthException("Empty response from Bilibili refresh token endpoint");
            }

            if (!tokenResponse.isSuccess()) {
                throw new AuthException("Bilibili token refresh failed: " + tokenResponse.getErrorMessage());
            }

            // 转换为统一的访问令牌格式
            Map<String, Object> extraInfo = new HashMap<>();
            extraInfo.put("mid", tokenResponse.getData().getMid());
            extraInfo.put("scope", tokenResponse.getData().getScope());
            
            return UnifiedAccessToken.builder()
                    .accessToken(tokenResponse.getData().getAccessToken())
                    .refreshToken(tokenResponse.getData().getRefreshToken())
                    .expiresIn(tokenResponse.getData().getExpiresIn())
                    .scope(tokenResponse.getData().getScope())
                    .platform(PlatformType.BILIBILI)
                    .userId(String.valueOf(tokenResponse.getData().getMid()))
                    .extraInfo(extraInfo)
                    .createdAt(LocalDateTime.now())
                    .build();

        } catch (Exception e) {
            log.error("Failed to refresh Bilibili access token", e);
            throw new AuthException("Failed to refresh token", e);
        }
    }

    @Override
    public UnifiedUserInfo getUserInfo(String accessToken) {
        try {
            log.debug("Getting Bilibili user info with access token");

            // 构建获取用户信息的URL
            String userInfoUrl = UriComponentsBuilder.fromHttpUrl(config.getEffectiveApiBaseUrl() + "/x/web-interface/nav")
                    .queryParam("access_token", accessToken)
                    .build()
                    .toUriString();

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", "Bearer " + accessToken);

            HttpEntity<Void> request = new HttpEntity<>(headers);

            // 发送请求获取用户信息
            ResponseEntity<BilibiliUserInfo> response = restTemplate.exchange(
                    userInfoUrl, HttpMethod.GET, request, BilibiliUserInfo.class);

            BilibiliUserInfo userInfo = response.getBody();
            if (userInfo == null) {
                throw new AuthException("Empty response from Bilibili user info endpoint");
            }

            if (!userInfo.isSuccess()) {
                throw new AuthException("Bilibili get user info failed: " + userInfo.getErrorMessage());
            }

            // 转换为统一的用户信息格式
            return UnifiedUserInfo.builder()
                    .openId(String.valueOf(userInfo.getData().getMid()))
                    .unionId(String.valueOf(userInfo.getData().getMid()))
                    .nickname(userInfo.getData().getUname())
                    .avatar(userInfo.getData().getFace())
                    .gender(userInfo.getData().getSex())
                    .platform(PlatformType.BILIBILI)
                    .build();

        } catch (Exception e) {
            log.error("Failed to get Bilibili user info", e);
            throw new AuthException("Failed to get user info", e);
        }
    }

    @Override
    public boolean validateToken(String accessToken) {
        try {
            log.debug("Validating Bilibili access token");

            // 通过获取用户信息来验证token
            getUserInfo(accessToken);
            return true;

        } catch (Exception e) {
            log.error("Failed to validate Bilibili access token", e);
            return false;
        }
    }
}
