package cn.com.handthing.starter.datalayer.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * 基础Mapper接口
 * <p>
 * 继承MyBatis-Plus的BaseMapper，提供基础的CRUD操作。
 * 所有Mapper接口都应该继承此接口。
 * </p>
 * 
 * <h3>使用示例：</h3>
 * <pre>
 * &#64;Mapper
 * public interface ProductMapper extends BaseMapper&lt;Product&gt; {
 *     // 自定义查询方法
 *     List&lt;Product&gt; selectByCategory(String category);
 * }
 * </pre>
 *
 * @param <T> 实体类型
 * <AUTHOR>
 * @since V1.0.0
 */
public interface BaseMapper<T> extends com.baomidou.mybatisplus.core.mapper.BaseMapper<T> {
    
    /**
     * 根据ID查询（增强版）
     * <p>
     * 相比原生selectById，增加了空值检查和日志记录
     * </p>
     *
     * @param id 主键ID
     * @return 实体对象，如果不存在返回null
     */
    default T selectByIdEnhanced(Serializable id) {
        if (id == null) {
            return null;
        }
        return selectById(id);
    }
    
    /**
     * 批量查询（增强版）
     * <p>
     * 相比原生selectBatchIds，增加了空值检查和去重处理
     * </p>
     *
     * @param idList ID列表
     * @return 实体列表
     */
    default List<T> selectBatchIdsEnhanced(Collection<? extends Serializable> idList) {
        if (idList == null || idList.isEmpty()) {
            return List.of();
        }
        
        // 去重处理
        Collection<? extends Serializable> distinctIds = idList.stream()
                .distinct()
                .toList();
        
        return selectBatchIds(distinctIds);
    }
    
    /**
     * 分页查询（增强版）
     * <p>
     * 提供更友好的分页查询接口
     * </p>
     *
     * @param page         分页对象
     * @param queryWrapper 查询条件
     * @return 分页结果
     */
    default IPage<T> selectPageEnhanced(IPage<T> page, Wrapper<T> queryWrapper) {
        if (page == null) {
            throw new IllegalArgumentException("Page cannot be null");
        }
        return selectPage(page, queryWrapper);
    }
    
    /**
     * 统计查询（增强版）
     * <p>
     * 提供更安全的统计查询
     * </p>
     *
     * @param queryWrapper 查询条件
     * @return 统计结果
     */
    default Long selectCountEnhanced(Wrapper<T> queryWrapper) {
        Long count = selectCount(queryWrapper);
        return count != null ? count : 0L;
    }
    
    /**
     * 检查记录是否存在
     *
     * @param id 主键ID
     * @return 如果存在返回true，否则返回false
     */
    default boolean exists(Serializable id) {
        if (id == null) {
            return false;
        }
        return selectById(id) != null;
    }
    
    /**
     * 检查记录是否存在
     *
     * @param queryWrapper 查询条件
     * @return 如果存在返回true，否则返回false
     */
    default boolean exists(Wrapper<T> queryWrapper) {
        return selectCountEnhanced(queryWrapper) > 0;
    }
    
    /**
     * 查询第一条记录
     *
     * @param queryWrapper 查询条件
     * @return 第一条记录，如果不存在返回null
     */
    default T selectFirst(Wrapper<T> queryWrapper) {
        List<T> list = selectList(queryWrapper);
        return list.isEmpty() ? null : list.get(0);
    }
    
    /**
     * 安全删除（检查记录是否存在）
     *
     * @param id 主键ID
     * @return 删除的记录数
     */
    default int deleteByIdSafely(Serializable id) {
        if (id == null || !exists(id)) {
            return 0;
        }
        return deleteById(id);
    }
    
    /**
     * 批量安全删除
     *
     * @param idList ID列表
     * @return 删除的记录数
     */
    default int deleteBatchIdsSafely(Collection<? extends Serializable> idList) {
        if (idList == null || idList.isEmpty()) {
            return 0;
        }
        
        // 过滤存在的ID
        List<? extends Serializable> existingIds = idList.stream()
                .filter(this::exists)
                .toList();
        
        if (existingIds.isEmpty()) {
            return 0;
        }
        
        return deleteBatchIds(existingIds);
    }
    
    /**
     * 安全更新（检查记录是否存在）
     *
     * @param entity 实体对象
     * @return 更新的记录数
     */
    default int updateByIdSafely(T entity) {
        if (entity == null) {
            return 0;
        }
        return updateById(entity);
    }
    
    /**
     * 条件更新（增强版）
     *
     * @param entity        实体对象
     * @param updateWrapper 更新条件
     * @return 更新的记录数
     */
    default int updateEnhanced(T entity, Wrapper<T> updateWrapper) {
        if (entity == null) {
            return 0;
        }
        return update(entity, updateWrapper);
    }
    
    /**
     * 插入或更新
     * <p>
     * 如果记录存在则更新，否则插入
     * </p>
     *
     * @param entity 实体对象
     * @return 影响的记录数
     */
    default int insertOrUpdate(T entity) {
        if (entity == null) {
            return 0;
        }
        
        // 这里需要子类实现具体的判断逻辑
        // 默认使用MyBatis-Plus的insertOrUpdate方法
        // 注意：这需要实体有主键字段
        try {
            return insert(entity);
        } catch (Exception e) {
            // 如果插入失败（可能是主键冲突），尝试更新
            return updateById(entity);
        }
    }
}
