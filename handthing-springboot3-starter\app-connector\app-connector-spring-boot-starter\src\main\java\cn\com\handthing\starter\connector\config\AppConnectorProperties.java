package cn.com.handthing.starter.connector.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import jakarta.validation.constraints.NotBlank;
import java.util.HashMap;
import java.util.Map;

/**
 * App Connector 配置属性类
 * <p>
 * 使用@ConfigurationProperties绑定配置，支持多平台配置和全局配置。
 * 集成handthing.cache配置体系，移除了tokenStoreType配置。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
@ConfigurationProperties(prefix = "handthing.connector")
public class AppConnectorProperties {

    /**
     * 是否启用App Connector
     */
    private boolean enabled = true;

    /**
     * 统一回调地址前缀
     * 最终回调地址格式：{redirectUriPrefix}/connector/callback/{platform}
     * 例如：https://your.domain.com/auth/connector/callback/wecom
     */
    @NotBlank(message = "Redirect URI prefix cannot be blank")
    private String redirectUriPrefix;

    /**
     * Token缓存区域名称
     */
    private String tokenCacheName = "app-connector-tokens";

    /**
     * 是否启用统一回调处理器
     */
    private boolean enableUnifiedCallback = true;

    /**
     * 回调处理器路径前缀
     */
    private String callbackPathPrefix = "/connector/callback";

    /**
     * 是否启用事件发布
     */
    private boolean enableEventPublishing = true;

    /**
     * 默认连接超时时间（毫秒）
     */
    private Integer defaultConnectTimeout = 10000;

    /**
     * 默认读取超时时间（毫秒）
     */
    private Integer defaultReadTimeout = 30000;

    /**
     * 默认最大重试次数
     */
    private Integer defaultMaxRetries = 3;

    /**
     * 企业微信配置
     */
    private WeComConfig wecom = new WeComConfig();

    /**
     * 钉钉配置
     */
    private DingTalkConfig dingtalk = new DingTalkConfig();

    /**
     * 抖音配置
     */
    private DouyinConfig douyin = new DouyinConfig();

    /**
     * 支付宝配置
     */
    private AlipayConfig alipay = new AlipayConfig();

    /**
     * 微信配置
     */
    private WeChatConfig wechat = new WeChatConfig();

    /**
     * 飞书配置
     */
    private FeishuConfig feishu = new FeishuConfig();

    /**
     * 企业微信配置
     */
    @Data
    public static class WeComConfig extends PlatformConfig {
        /**
         * 企业ID
         */
        private String corpId;

        /**
         * 应用ID
         */
        private String agentId;

        /**
         * 应用密钥
         */
        private String secret;

        @Override
        public boolean isValid() {
            return super.isValid() 
                    && corpId != null && !corpId.trim().isEmpty()
                    && agentId != null && !agentId.trim().isEmpty()
                    && secret != null && !secret.trim().isEmpty();
        }
    }

    /**
     * 钉钉配置
     */
    @Data
    public static class DingTalkConfig extends PlatformConfig {
        /**
         * 应用Key
         */
        private String appKey;

        /**
         * 应用密钥
         */
        private String appSecret;

        @Override
        public boolean isValid() {
            return super.isValid() 
                    && appKey != null && !appKey.trim().isEmpty()
                    && appSecret != null && !appSecret.trim().isEmpty();
        }
    }

    /**
     * 抖音配置
     */
    @Data
    public static class DouyinConfig extends PlatformConfig {
        /**
         * 客户端Key
         */
        private String clientKey;

        /**
         * 客户端密钥
         */
        private String clientSecret;

        @Override
        public boolean isValid() {
            return super.isValid() 
                    && clientKey != null && !clientKey.trim().isEmpty()
                    && clientSecret != null && !clientSecret.trim().isEmpty();
        }
    }

    /**
     * 支付宝配置
     */
    @Data
    public static class AlipayConfig extends PlatformConfig {
        /**
         * 应用ID
         */
        private String appId;

        /**
         * 应用私钥
         */
        private String privateKey;

        /**
         * 支付宝公钥
         */
        private String alipayPublicKey;

        /**
         * 签名类型
         */
        private String signType = "RSA2";

        @Override
        public boolean isValid() {
            return super.isValid() 
                    && appId != null && !appId.trim().isEmpty()
                    && privateKey != null && !privateKey.trim().isEmpty()
                    && alipayPublicKey != null && !alipayPublicKey.trim().isEmpty();
        }
    }

    /**
     * 微信配置
     */
    @Data
    public static class WeChatConfig extends PlatformConfig {
        /**
         * 应用ID
         */
        private String appId;

        /**
         * 应用密钥
         */
        private String appSecret;

        @Override
        public boolean isValid() {
            return super.isValid() 
                    && appId != null && !appId.trim().isEmpty()
                    && appSecret != null && !appSecret.trim().isEmpty();
        }
    }

    /**
     * 飞书配置
     */
    @Data
    public static class FeishuConfig extends PlatformConfig {
        /**
         * 应用ID
         */
        private String appId;

        /**
         * 应用密钥
         */
        private String appSecret;

        @Override
        public boolean isValid() {
            return super.isValid() 
                    && appId != null && !appId.trim().isEmpty()
                    && appSecret != null && !appSecret.trim().isEmpty();
        }
    }

    /**
     * 获取所有平台配置
     *
     * @return 平台配置映射
     */
    public Map<String, PlatformConfig> getAllPlatformConfigs() {
        Map<String, PlatformConfig> configs = new HashMap<>();
        configs.put("wecom", wecom);
        configs.put("dingtalk", dingtalk);
        configs.put("douyin", douyin);
        configs.put("alipay", alipay);
        configs.put("wechat", wechat);
        configs.put("feishu", feishu);
        return configs;
    }

    /**
     * 获取启用的平台配置
     *
     * @return 启用的平台配置映射
     */
    public Map<String, PlatformConfig> getEnabledPlatformConfigs() {
        Map<String, PlatformConfig> enabledConfigs = new HashMap<>();
        getAllPlatformConfigs().forEach((key, config) -> {
            if (config.isEnabled() && config.isValid()) {
                enabledConfigs.put(key, config);
            }
        });
        return enabledConfigs;
    }

    /**
     * 获取完整的回调URL
     *
     * @param platform 平台代码
     * @return 完整的回调URL
     */
    public String getCallbackUrl(String platform) {
        return redirectUriPrefix + callbackPathPrefix + "/" + platform;
    }
}
