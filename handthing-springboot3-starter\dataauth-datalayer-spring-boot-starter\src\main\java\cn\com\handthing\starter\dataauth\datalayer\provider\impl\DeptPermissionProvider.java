package cn.com.handthing.starter.dataauth.datalayer.provider.impl;

import cn.com.handthing.starter.dataauth.datalayer.annotation.DataPermission;
import cn.com.handthing.starter.dataauth.datalayer.context.DataPermissionContext;
import cn.com.handthing.starter.dataauth.datalayer.provider.DataPermissionProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 部门权限提供者
 * <p>
 * 实现基于部门的数据权限控制。根据用户所属部门和权限范围，
 * 生成相应的SQL权限条件。
 * </p>
 * 
 * <h3>支持的权限范围：</h3>
 * <ul>
 *   <li>CURRENT - 只能访问当前部门的数据</li>
 *   <li>CURRENT_AND_CHILDREN - 可以访问当前部门及子部门的数据</li>
 *   <li>ALL - 可以访问所有部门的数据</li>
 * </ul>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Component
public class DeptPermissionProvider implements DataPermissionProvider {
    
    /**
     * 默认部门字段名
     */
    private static final String DEFAULT_DEPT_FIELD = "dept_id";
    
    @Override
    public boolean supports(DataPermission.DataPermissionType type) {
        return DataPermission.DataPermissionType.DEPT == type;
    }
    
    @Override
    public String generatePermissionCondition(DataPermission permission, String mappedStatementId) {
        try {
            // 获取当前用户信息
            DataPermissionContext.UserInfo userInfo = getCurrentUserInfo();
            if (userInfo == null) {
                log.warn("No user info found for dept permission check");
                return "1 = 0"; // 无用户信息时拒绝访问
            }
            
            // 获取部门字段名
            String deptField = getDeptField(permission);
            
            // 根据权限范围生成条件
            return generateConditionByScope(permission, deptField, userInfo);
            
        } catch (Exception e) {
            log.error("Failed to generate dept permission condition", e);
            return "1 = 0"; // 出错时拒绝访问
        }
    }
    
    /**
     * 根据权限范围生成条件
     * 
     * @param permission 权限配置
     * @param deptField  部门字段名
     * @param userInfo   用户信息
     * @return SQL条件
     */
    protected String generateConditionByScope(DataPermission permission, String deptField, 
                                            DataPermissionContext.UserInfo userInfo) {
        
        DataPermission.PermissionScope scope = permission.scope();
        String userDeptId = userInfo.getDeptId();
        
        if (userDeptId == null || userDeptId.trim().isEmpty()) {
            log.warn("User dept ID is empty for user: {}", userInfo.getUserId());
            return "1 = 0"; // 无部门信息时拒绝访问
        }
        
        switch (scope) {
            case CURRENT:
                return generateCurrentDeptCondition(deptField, userDeptId, permission.alias());
                
            case CURRENT_AND_CHILDREN:
                return generateCurrentAndChildrenDeptCondition(deptField, userDeptId, permission.alias());
                
            case ALL:
                return null; // 无限制
                
            case CUSTOM:
                return generateCustomDeptCondition(permission, userInfo);
                
            default:
                log.warn("Unsupported permission scope: {}", scope);
                return generateCurrentDeptCondition(deptField, userDeptId, permission.alias());
        }
    }
    
    /**
     * 生成当前部门条件
     * 
     * @param deptField 部门字段名
     * @param deptId    部门ID
     * @param alias     表别名
     * @return SQL条件
     */
    protected String generateCurrentDeptCondition(String deptField, String deptId, String alias) {
        String fullFieldName = buildFullFieldName(deptField, alias);
        return String.format("%s = '%s'", fullFieldName, escapeSqlValue(deptId));
    }
    
    /**
     * 生成当前部门及子部门条件
     * 
     * @param deptField 部门字段名
     * @param deptId    部门ID
     * @param alias     表别名
     * @return SQL条件
     */
    protected String generateCurrentAndChildrenDeptCondition(String deptField, String deptId, String alias) {
        // 获取当前部门及所有子部门ID列表
        List<String> deptIds = getCurrentAndChildrenDeptIds(deptId);
        
        if (deptIds.isEmpty()) {
            return generateCurrentDeptCondition(deptField, deptId, alias);
        }
        
        String fullFieldName = buildFullFieldName(deptField, alias);
        String deptIdList = deptIds.stream()
                .map(this::escapeSqlValue)
                .map(id -> "'" + id + "'")
                .collect(Collectors.joining(", "));
        
        return String.format("%s IN (%s)", fullFieldName, deptIdList);
    }
    
    /**
     * 生成自定义部门条件
     * 
     * @param permission 权限配置
     * @param userInfo   用户信息
     * @return SQL条件
     */
    protected String generateCustomDeptCondition(DataPermission permission, 
                                               DataPermissionContext.UserInfo userInfo) {
        // TODO: 实现自定义部门权限逻辑
        // 可以根据用户的特殊权限配置生成条件
        log.debug("Custom dept permission not implemented, falling back to current dept");
        return generateCurrentDeptCondition(getDeptField(permission), userInfo.getDeptId(), permission.alias());
    }
    
    /**
     * 获取部门字段名
     * 
     * @param permission 权限配置
     * @return 部门字段名
     */
    protected String getDeptField(DataPermission permission) {
        String field = permission.field();
        return (field == null || field.trim().isEmpty()) ? DEFAULT_DEPT_FIELD : field.trim();
    }
    
    /**
     * 构建完整字段名（包含表别名）
     * 
     * @param fieldName 字段名
     * @param alias     表别名
     * @return 完整字段名
     */
    protected String buildFullFieldName(String fieldName, String alias) {
        if (alias == null || alias.trim().isEmpty()) {
            return fieldName;
        }
        return alias.trim() + "." + fieldName;
    }
    
    /**
     * 转义SQL值
     * 
     * @param value 原始值
     * @return 转义后的值
     */
    protected String escapeSqlValue(String value) {
        if (value == null) {
            return "";
        }
        // 简单的SQL注入防护，实际项目中应该使用更完善的转义方法
        return value.replace("'", "''");
    }
    
    /**
     * 获取当前用户信息
     * 
     * @return 用户信息
     */
    protected DataPermissionContext.UserInfo getCurrentUserInfo() {
        DataPermissionContext context = DataPermissionContext.getCurrentContext();
        if (context != null) {
            return context.getUserInfo();
        }
        
        // TODO: 从AuthContextHolder或其他用户上下文获取用户信息
        return null;
    }
    
    /**
     * 获取当前部门及所有子部门ID列表
     * 
     * @param deptId 当前部门ID
     * @return 部门ID列表
     */
    protected List<String> getCurrentAndChildrenDeptIds(String deptId) {
        // TODO: 实现部门层级查询逻辑
        // 这里应该查询数据库获取部门的层级关系
        // 返回当前部门及所有子部门的ID列表
        
        // 临时实现：只返回当前部门ID
        return List.of(deptId);
    }
    
    @Override
    public String validatePermissionConfig(DataPermission permission) {
        StringBuilder errors = new StringBuilder();
        
        // 验证部门字段配置
        String deptField = getDeptField(permission);
        if (deptField.isEmpty()) {
            errors.append("Dept field cannot be empty; ");
        }
        
        // 验证权限范围
        if (permission.scope() == DataPermission.PermissionScope.CUSTOM) {
            if (permission.expression() == null || permission.expression().trim().isEmpty()) {
                errors.append("Custom scope requires expression; ");
            }
        }
        
        return errors.toString();
    }
    
    @Override
    public boolean isAvailable() {
        // 检查是否有用户上下文
        DataPermissionContext context = DataPermissionContext.getCurrentContext();
        if (context == null) {
            return false;
        }
        
        DataPermissionContext.UserInfo userInfo = context.getUserInfo();
        return userInfo != null && userInfo.getDeptId() != null;
    }
    
    @Override
    public String getDescription() {
        return "Department-based data permission provider that controls data access based on user's department";
    }
    
    @Override
    public int getOrder() {
        return 100; // 部门权限的优先级
    }
}
