package cn.com.handthing.starter.connector.bilibili.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * Bilibili用户信息
 * <p>
 * Bilibili OAuth2.0认证获取用户信息的响应数据模型。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
public class BilibiliUserInfo {

    /**
     * 状态码，0表示成功
     */
    @JsonProperty("code")
    private Integer code;

    /**
     * 响应消息
     */
    @JsonProperty("message")
    private String message;

    /**
     * 响应数据
     */
    @JsonProperty("data")
    private UserData data;

    /**
     * 检查响应是否成功
     *
     * @return 如果成功返回true，否则返回false
     */
    public boolean isSuccess() {
        return code != null && code == 0;
    }

    /**
     * 获取错误信息
     *
     * @return 错误信息
     */
    public String getErrorMessage() {
        if (isSuccess()) {
            return null;
        }
        return String.format("Code %d: %s", code, message);
    }

    /**
     * 用户数据
     */
    @Data
    public static class UserData {

        /**
         * 用户ID (Member ID)
         */
        @JsonProperty("mid")
        private Long mid;

        /**
         * 用户名
         */
        @JsonProperty("uname")
        private String uname;

        /**
         * 用户头像URL
         */
        @JsonProperty("face")
        private String face;

        /**
         * 用户性别，1-男，2-女，0-保密
         */
        @JsonProperty("sex")
        private Integer sex;

        /**
         * 用户等级
         */
        @JsonProperty("level")
        private Integer level;

        /**
         * 是否为VIP
         */
        @JsonProperty("vip")
        private VipInfo vip;

        /**
         * 用户签名
         */
        @JsonProperty("sign")
        private String sign;

        /**
         * 硬币数量
         */
        @JsonProperty("coins")
        private Integer coins;

        /**
         * 关注数量
         */
        @JsonProperty("following")
        private Integer following;

        /**
         * 粉丝数量
         */
        @JsonProperty("follower")
        private Integer follower;

        /**
         * 获取性别描述
         *
         * @return 性别描述
         */
        public String getSexDescription() {
            if (sex == null) {
                return "保密";
            }
            switch (sex) {
                case 1:
                    return "男";
                case 2:
                    return "女";
                default:
                    return "保密";
            }
        }

        /**
         * VIP信息
         */
        @Data
        public static class VipInfo {

            /**
             * VIP类型，0-非VIP，1-月度VIP，2-年度VIP
             */
            @JsonProperty("type")
            private Integer type;

            /**
             * VIP状态，0-非VIP，1-VIP
             */
            @JsonProperty("status")
            private Integer status;

            /**
             * VIP到期时间戳
             */
            @JsonProperty("due_date")
            private Long dueDate;

            /**
             * 是否为VIP
             *
             * @return 如果是VIP返回true，否则返回false
             */
            public boolean isVip() {
                return status != null && status == 1;
            }

            /**
             * 获取VIP类型描述
             *
             * @return VIP类型描述
             */
            public String getTypeDescription() {
                if (type == null) {
                    return "非VIP";
                }
                switch (type) {
                    case 1:
                        return "月度VIP";
                    case 2:
                        return "年度VIP";
                    default:
                        return "非VIP";
                }
            }
        }
    }
}
