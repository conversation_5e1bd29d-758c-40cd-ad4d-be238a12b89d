# HandThing Auth 认证授权体系 - 任务清单

## 项目概述
基于"认证总线"(Authentication Bus)设计理念，创建支持多种登录方式的认证授权体系。

## 模块结构

### 1. auth-core (核心抽象模块)

#### 1.1 认证核心接口
- [ ] `AuthenticationProvider` - 认证提供者接口
- [ ] `AuthenticationRequest` - 认证请求抽象类
- [ ] `AuthenticationResponse` - 认证响应抽象类
- [ ] `AuthenticationContext` - 认证上下文
- [ ] `GrantType` - 授权类型枚举

#### 1.2 用户上下文
- [ ] `UserContext` - 用户上下文接口
- [ ] `UserInfo` - 用户信息模型
- [ ] `UserContextHolder` - 用户上下文持有者
- [ ] `UserContextFilter` - 用户上下文过滤器

#### 1.3 JWT支持
- [ ] `JwtTokenProvider` - JWT令牌提供者
- [ ] `JwtTokenValidator` - JWT令牌验证器
- [ ] `JwtConfig` - JWT配置类
- [ ] `JwtClaims` - JWT声明模型

#### 1.4 异常处理
- [ ] `AuthenticationException` - 认证异常基类
- [ ] `InvalidCredentialsException` - 无效凭证异常
- [ ] `TokenExpiredException` - 令牌过期异常
- [ ] `AuthenticationFailedException` - 认证失败异常

#### 1.5 工具类
- [ ] `AuthenticationUtils` - 认证工具类
- [ ] `PasswordEncoder` - 密码编码器
- [ ] `SecurityUtils` - 安全工具类

### 2. auth-spring-boot-starter (核心编排器)

#### 2.1 自动配置
- [ ] `AuthAutoConfiguration` - 认证自动配置
- [ ] `JwtAutoConfiguration` - JWT自动配置
- [ ] `SecurityAutoConfiguration` - 安全自动配置

#### 2.2 认证管理器
- [ ] `AuthenticationManager` - 认证管理器
- [ ] `AuthenticationBus` - 认证总线
- [ ] `ProviderRegistry` - 提供者注册表

#### 2.3 Web支持
- [ ] `AuthenticationController` - 认证控制器
- [ ] `AuthenticationFilter` - 认证过滤器
- [ ] `AuthenticationInterceptor` - 认证拦截器

#### 2.4 配置属性
- [ ] `AuthProperties` - 认证配置属性
- [ ] `JwtProperties` - JWT配置属性
- [ ] `SecurityProperties` - 安全配置属性

### 3. password-provider-starter (密码认证提供者)

#### 3.1 密码认证
- [ ] `PasswordAuthenticationProvider` - 密码认证提供者
- [ ] `PasswordAuthenticationRequest` - 密码认证请求
- [ ] `PasswordAuthenticationResponse` - 密码认证响应

#### 3.2 用户服务
- [ ] `UserService` - 用户服务接口
- [ ] `DefaultUserService` - 默认用户服务实现
- [ ] `UserRepository` - 用户仓储接口

#### 3.3 密码处理
- [ ] `PasswordValidator` - 密码验证器
- [ ] `PasswordPolicy` - 密码策略
- [ ] `BCryptPasswordEncoder` - BCrypt密码编码器

#### 3.4 配置
- [ ] `PasswordProviderAutoConfiguration` - 密码提供者自动配置
- [ ] `PasswordProperties` - 密码配置属性

### 4. sms-provider-starter (短信认证提供者)

#### 4.1 短信认证
- [ ] `SmsAuthenticationProvider` - 短信认证提供者
- [ ] `SmsAuthenticationRequest` - 短信认证请求
- [ ] `SmsAuthenticationResponse` - 短信认证响应

#### 4.2 短信服务
- [ ] `SmsService` - 短信服务接口
- [ ] `SmsCodeGenerator` - 短信验证码生成器
- [ ] `SmsCodeValidator` - 短信验证码验证器
- [ ] `SmsCodeStorage` - 短信验证码存储

#### 4.3 短信提供商
- [ ] `SmsProvider` - 短信提供商接口
- [ ] `AliyunSmsProvider` - 阿里云短信提供商
- [ ] `TencentSmsProvider` - 腾讯云短信提供商

#### 4.4 配置
- [ ] `SmsProviderAutoConfiguration` - 短信提供者自动配置
- [ ] `SmsProperties` - 短信配置属性

### 5. third-party-provider-starter (第三方认证提供者)

#### 5.1 第三方认证
- [ ] `ThirdPartyAuthenticationProvider` - 第三方认证提供者
- [ ] `ThirdPartyAuthenticationRequest` - 第三方认证请求
- [ ] `ThirdPartyAuthenticationResponse` - 第三方认证响应

#### 5.2 平台适配器
- [ ] `WeComAuthAdapter` - 企业微信认证适配器
- [ ] `DingTalkAuthAdapter` - 钉钉认证适配器
- [ ] `WeChatAuthAdapter` - 微信认证适配器
- [ ] `FeishuAuthAdapter` - 飞书认证适配器

#### 5.3 第三方服务
- [ ] `ThirdPartyUserService` - 第三方用户服务
- [ ] `UserMappingService` - 用户映射服务
- [ ] `ThirdPartyTokenValidator` - 第三方令牌验证器

#### 5.4 配置
- [ ] `ThirdPartyProviderAutoConfiguration` - 第三方提供者自动配置
- [ ] `ThirdPartyProperties` - 第三方配置属性

### 6. 测试模块

#### 6.1 单元测试
- [ ] `AuthenticationProviderTest` - 认证提供者测试
- [ ] `JwtTokenProviderTest` - JWT令牌提供者测试
- [ ] `UserContextTest` - 用户上下文测试

#### 6.2 集成测试
- [ ] `AuthenticationIntegrationTest` - 认证集成测试
- [ ] `MultiProviderTest` - 多提供者测试
- [ ] `SecurityTest` - 安全测试

#### 6.3 测试应用
- [ ] `AuthTestApplication` - 认证测试应用
- [ ] `TestController` - 测试控制器
- [ ] `TestConfiguration` - 测试配置

## 开发优先级

### 第一阶段：核心框架
1. auth-core 核心抽象
2. auth-spring-boot-starter 核心编排器
3. 基础JWT支持

### 第二阶段：基础认证
1. password-provider-starter 密码认证
2. 用户上下文功能
3. 基础安全功能

### 第三阶段：扩展认证
1. sms-provider-starter 短信认证
2. third-party-provider-starter 第三方认证
3. 多提供者集成

### 第四阶段：完善测试
1. 单元测试覆盖
2. 集成测试
3. 测试应用和文档

## 技术要求

### 代码规范
- 作者：HandThing
- 版本：V1.0.0
- Java 8+ 语法
- Spring 命名规范
- 使用 Lombok 简化代码

### 设计原则
- 面向接口编程
- 单一职责原则
- 开闭原则
- 依赖倒置原则

### 质量要求
- 完整的注释说明
- 单元测试覆盖
- Maven 编译通过
- 集成测试验证
