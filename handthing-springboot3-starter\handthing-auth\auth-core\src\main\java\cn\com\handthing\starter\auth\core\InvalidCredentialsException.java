package cn.com.handthing.starter.auth.core;

/**
 * 无效凭证异常
 * <p>
 * 当提供的认证凭证无效时抛出此异常。
 * 包括用户名密码错误、验证码错误、第三方授权码无效等情况。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
public class InvalidCredentialsException extends AuthenticationException {

    /**
     * 构造函数
     *
     * @param message 错误消息
     */
    public InvalidCredentialsException(String message) {
        super("INVALID_CREDENTIALS", ErrorType.INVALID_CREDENTIALS, message);
    }

    /**
     * 构造函数
     *
     * @param message 错误消息
     * @param cause   原因异常
     */
    public InvalidCredentialsException(String message, Throwable cause) {
        super("INVALID_CREDENTIALS", ErrorType.INVALID_CREDENTIALS, message, cause);
    }

    /**
     * 创建密码错误异常
     *
     * @return 无效凭证异常
     */
    public static InvalidCredentialsException invalidPassword() {
        return new InvalidCredentialsException("用户名或密码错误");
    }

    /**
     * 创建验证码错误异常
     *
     * @return 无效凭证异常
     */
    public static InvalidCredentialsException invalidCode() {
        return new InvalidCredentialsException("验证码错误或已过期");
    }

    /**
     * 创建第三方授权码错误异常
     *
     * @param platform 第三方平台名称
     * @return 无效凭证异常
     */
    public static InvalidCredentialsException invalidThirdPartyCode(String platform) {
        return new InvalidCredentialsException(String.format("%s授权码无效或已过期", platform));
    }
}
