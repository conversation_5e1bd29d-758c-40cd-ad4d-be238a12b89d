package cn.com.handthing.starter.auth.core;

/**
 * 认证提供者接口
 * <p>
 * 定义认证提供者的核心接口，基于"认证总线"设计理念。
 * 每种认证方式都需要实现此接口，提供统一的认证入口。
 * 支持多种认证方式的插拔式架构。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
public interface AuthenticationProvider {

    /**
     * 获取支持的授权类型
     *
     * @return 授权类型
     */
    GrantType getSupportedGrantType();

    /**
     * 判断是否支持指定的认证请求
     *
     * @param request 认证请求
     * @return 如果支持返回true，否则返回false
     */
    boolean supports(AuthenticationRequest request);

    /**
     * 执行认证
     *
     * @param request 认证请求
     * @return 认证响应
     * @throws AuthenticationException 认证异常
     */
    AuthenticationResponse authenticate(AuthenticationRequest request) throws AuthenticationException;

    /**
     * 验证令牌
     *
     * @param token 访问令牌
     * @return 如果令牌有效返回true，否则返回false
     */
    boolean validateToken(String token);

    /**
     * 刷新令牌
     *
     * @param refreshToken 刷新令牌
     * @return 认证响应
     * @throws AuthenticationException 认证异常
     */
    AuthenticationResponse refreshToken(String refreshToken) throws AuthenticationException;

    /**
     * 获取提供者名称
     *
     * @return 提供者名称
     */
    default String getProviderName() {
        return getSupportedGrantType().getDescription();
    }

    /**
     * 获取提供者描述
     *
     * @return 提供者描述
     */
    default String getProviderDescription() {
        return String.format("%s认证提供者", getProviderName());
    }

    /**
     * 判断提供者是否可用
     *
     * @return 如果可用返回true，否则返回false
     */
    default boolean isAvailable() {
        return true;
    }

    /**
     * 获取提供者优先级
     * <p>
     * 数值越小优先级越高，默认为0
     * </p>
     *
     * @return 优先级
     */
    default int getPriority() {
        return 0;
    }

    /**
     * 预处理认证请求
     * <p>
     * 在执行认证之前进行预处理，子类可以重写此方法
     * </p>
     *
     * @param request 认证请求
     * @throws AuthenticationException 认证异常
     */
    default void preAuthenticate(AuthenticationRequest request) throws AuthenticationException {
        // 默认不做任何处理
    }

    /**
     * 后处理认证响应
     * <p>
     * 在认证完成之后进行后处理，子类可以重写此方法
     * </p>
     *
     * @param request  认证请求
     * @param response 认证响应
     * @throws AuthenticationException 认证异常
     */
    default void postAuthenticate(AuthenticationRequest request, AuthenticationResponse response) throws AuthenticationException {
        // 默认不做任何处理
    }
}
