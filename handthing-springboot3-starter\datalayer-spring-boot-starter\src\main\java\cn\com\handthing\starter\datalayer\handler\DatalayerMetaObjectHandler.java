package cn.com.handthing.starter.datalayer.handler;

import cn.com.handthing.starter.id.annotation.IdSetter;
import cn.com.handthing.starter.id.generator.IdGenerator;
import cn.com.handthing.starter.id.registry.IdGeneratorRegistry;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.time.LocalDateTime;

/**
 * 数据层元数据自动填充处理器
 * <p>
 * 集成@IdSetter注解处理，自动注入ID到标注字段。
 * 同时提供基础字段的自动填充功能，包括审计字段、时间字段等。
 * </p>
 * 
 * <h3>功能特性：</h3>
 * <ul>
 *   <li>@IdSetter注解处理：根据strategy属性获取对应的IdGenerator</li>
 *   <li>字段类型自动匹配：String→UUID, Long→Snowflake</li>
 *   <li>基础字段自动填充：createBy, updateBy, createTime, updateTime等</li>
 *   <li>扩展性设计：支持插件扩展，为租户插件预留扩展点</li>
 * </ul>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Component
public class DatalayerMetaObjectHandler implements MetaObjectHandler {
    
    /**
     * ID生成器注册中心
     */
    @Autowired(required = false)
    private IdGeneratorRegistry idGeneratorRegistry;
    
    /**
     * 插入时的字段填充
     */
    @Override
    public void insertFill(MetaObject metaObject) {
        log.debug("Starting insert fill for entity: {}", metaObject.getOriginalObject().getClass().getName());
        
        try {
            // 1. 处理@IdSetter注解字段
            processIdSetterFields(metaObject);
            
            // 2. 填充基础审计字段
            fillBasicAuditFields(metaObject);
            
            // 3. 填充时间字段
            fillTimeFields(metaObject, true);
            
            // 4. 填充其他默认字段
            fillDefaultFields(metaObject);
            
            // 5. 扩展点：为插件预留的钩子方法
            doInsertFillExtension(metaObject);
            
        } catch (Exception e) {
            log.error("Error during insert fill for entity: {}", 
                    metaObject.getOriginalObject().getClass().getName(), e);
            // 不抛出异常，避免影响正常的插入操作
        }
        
        log.debug("Completed insert fill for entity: {}", metaObject.getOriginalObject().getClass().getName());
    }
    
    /**
     * 更新时的字段填充
     */
    @Override
    public void updateFill(MetaObject metaObject) {
        log.debug("Starting update fill for entity: {}", metaObject.getOriginalObject().getClass().getName());
        
        try {
            // 1. 填充更新相关的审计字段
            fillUpdateAuditFields(metaObject);
            
            // 2. 填充更新时间
            fillTimeFields(metaObject, false);
            
            // 3. 扩展点：为插件预留的钩子方法
            doUpdateFillExtension(metaObject);
            
        } catch (Exception e) {
            log.error("Error during update fill for entity: {}", 
                    metaObject.getOriginalObject().getClass().getName(), e);
            // 不抛出异常，避免影响正常的更新操作
        }
        
        log.debug("Completed update fill for entity: {}", metaObject.getOriginalObject().getClass().getName());
    }
    
    /**
     * 处理@IdSetter注解字段
     */
    protected void processIdSetterFields(MetaObject metaObject) {
        if (idGeneratorRegistry == null) {
            log.debug("IdGeneratorRegistry not available, skipping @IdSetter processing");
            return;
        }
        
        Object entity = metaObject.getOriginalObject();
        Class<?> entityClass = entity.getClass();
        
        // 遍历所有字段，查找@IdSetter注解
        Field[] fields = entityClass.getDeclaredFields();
        for (Field field : fields) {
            IdSetter idSetter = field.getAnnotation(IdSetter.class);
            if (idSetter == null) {
                continue;
            }
            
            try {
                processIdSetterField(metaObject, field, idSetter);
            } catch (Exception e) {
                log.error("Failed to process @IdSetter field: {}.{}", 
                        entityClass.getName(), field.getName(), e);
                
                // 根据失败策略处理
                switch (idSetter.onFailure()) {
                    case IGNORE:
                        // 忽略错误，继续处理其他字段
                        break;
                    case LOG_ERROR:
                        log.error("@IdSetter processing failed for field: {}.{}, error: {}", 
                                entityClass.getName(), field.getName(), e.getMessage());
                        break;
                    case THROW_EXCEPTION:
                        throw new RuntimeException("@IdSetter processing failed for field: " + 
                                entityClass.getName() + "." + field.getName(), e);
                }
            }
        }
    }
    
    /**
     * 处理单个@IdSetter字段
     */
    protected void processIdSetterField(MetaObject metaObject, Field field, IdSetter idSetter) {
        String fieldName = field.getName();
        
        // 检查字段是否已有值
        if (idSetter.skipIfPresent() && metaObject.hasGetter(fieldName)) {
            Object currentValue = metaObject.getValue(fieldName);
            if (currentValue != null) {
                log.debug("Field {} already has value, skipping ID generation", fieldName);
                return;
            }
        }
        
        // 获取ID生成器
        IdGenerator<?> generator = getIdGenerator(field, idSetter);
        if (generator == null) {
            log.warn("No ID generator found for field: {}, strategy: {}", 
                    fieldName, idSetter.strategy());
            return;
        }
        
        // 生成ID
        Object id = generator.generate();
        if (id == null) {
            log.warn("Generated ID is null for field: {}", fieldName);
            return;
        }
        
        // 设置ID值
        if (metaObject.hasSetter(fieldName)) {
            metaObject.setValue(fieldName, id);
            log.debug("Set ID for field {}: {}", fieldName, id);
        } else {
            log.warn("No setter found for field: {}", fieldName);
        }
    }
    
    /**
     * 获取ID生成器
     */
    protected IdGenerator<?> getIdGenerator(Field field, IdSetter idSetter) {
        String strategy = idSetter.strategy();
        
        try {
            // 如果指定了策略名称，使用指定的生成器
            if (strategy != null && !strategy.trim().isEmpty()) {
                return idGeneratorRegistry.getGenerator(strategy);
            }
            
            // 否则根据字段类型获取默认生成器
            Class<?> fieldType = field.getType();
            return idGeneratorRegistry.getGenerator(fieldType);
            
        } catch (Exception e) {
            log.error("Failed to get ID generator for field: {}, strategy: {}, type: {}", 
                    field.getName(), strategy, field.getType().getName(), e);
            return null;
        }
    }
    
    /**
     * 填充基础审计字段
     */
    protected void fillBasicAuditFields(MetaObject metaObject) {
        // 获取当前用户信息（从AuthContextHolder或其他方式）
        String currentUser = getCurrentUser();
        
        // 填充创建人
        this.strictInsertFill(metaObject, "createBy", String.class, currentUser);
        
        // 填充更新人
        this.strictInsertFill(metaObject, "updateBy", String.class, currentUser);
    }
    
    /**
     * 填充更新审计字段
     */
    protected void fillUpdateAuditFields(MetaObject metaObject) {
        // 获取当前用户信息
        String currentUser = getCurrentUser();
        
        // 填充更新人
        this.strictUpdateFill(metaObject, "updateBy", String.class, currentUser);
    }
    
    /**
     * 填充时间字段
     */
    protected void fillTimeFields(MetaObject metaObject, boolean isInsert) {
        LocalDateTime now = LocalDateTime.now();
        
        if (isInsert) {
            // 插入时填充创建时间和更新时间
            this.strictInsertFill(metaObject, "createTime", LocalDateTime.class, now);
            this.strictInsertFill(metaObject, "updateTime", LocalDateTime.class, now);
        } else {
            // 更新时只填充更新时间
            this.strictUpdateFill(metaObject, "updateTime", LocalDateTime.class, now);
        }
    }
    
    /**
     * 填充其他默认字段
     */
    protected void fillDefaultFields(MetaObject metaObject) {
        // 填充版本号（乐观锁）
        this.strictInsertFill(metaObject, "version", Long.class, 1L);
        
        // 填充逻辑删除标记
        this.strictInsertFill(metaObject, "deleted", Integer.class, 0);
    }
    
    /**
     * 获取当前用户
     * <p>
     * 子类可以重写此方法来实现自定义的用户获取逻辑
     * </p>
     */
    protected String getCurrentUser() {
        // 默认实现：尝试从不同来源获取用户信息
        
        // 1. 尝试从AuthContextHolder获取（如果可用）
        String user = getUserFromAuthContext();
        if (user != null) {
            return user;
        }
        
        // 2. 尝试从Spring Security获取
        user = getUserFromSpringSecurity();
        if (user != null) {
            return user;
        }
        
        // 3. 默认用户
        return "system";
    }
    
    /**
     * 从认证上下文获取用户
     */
    protected String getUserFromAuthContext() {
        try {
            // 使用反射调用AuthContextHolder，避免强依赖
            Class<?> authContextHolderClass = Class.forName("cn.com.handthing.starter.auth.core.AuthContextHolder");
            Object userInfo = authContextHolderClass.getMethod("getCurrentUser").invoke(null);
            if (userInfo != null) {
                Object userId = userInfo.getClass().getMethod("getUserId").invoke(userInfo);
                return userId != null ? userId.toString() : null;
            }
        } catch (Exception e) {
            log.debug("AuthContextHolder not available or failed to get user", e);
        }
        return null;
    }
    
    /**
     * 从Spring Security获取用户
     */
    protected String getUserFromSpringSecurity() {
        try {
            // 使用反射调用Spring Security，避免强依赖
            Class<?> securityContextHolderClass = Class.forName("org.springframework.security.core.context.SecurityContextHolder");
            Object context = securityContextHolderClass.getMethod("getContext").invoke(null);
            if (context != null) {
                Object authentication = context.getClass().getMethod("getAuthentication").invoke(context);
                if (authentication != null) {
                    Object principal = authentication.getClass().getMethod("getPrincipal").invoke(authentication);
                    return principal != null ? principal.toString() : null;
                }
            }
        } catch (Exception e) {
            log.debug("Spring Security not available or failed to get user", e);
        }
        return null;
    }
    
    /**
     * 插入填充扩展点
     * <p>
     * 子类或插件可以重写此方法来实现自定义的插入填充逻辑
     * </p>
     */
    protected void doInsertFillExtension(MetaObject metaObject) {
        // 默认实现为空，子类可以重写
    }
    
    /**
     * 更新填充扩展点
     * <p>
     * 子类或插件可以重写此方法来实现自定义的更新填充逻辑
     * </p>
     */
    protected void doUpdateFillExtension(MetaObject metaObject) {
        // 默认实现为空，子类可以重写
    }
}
