package cn.com.handthing.starter.connector.wecom.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 企业微信用户信息响应模型
 * <p>
 * 企业微信获取用户信息API的响应数据结构
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class WeComUserInfoResponse {

    /**
     * 错误码，0表示成功
     */
    @JsonProperty("errcode")
    private Integer errcode;

    /**
     * 错误信息
     */
    @JsonProperty("errmsg")
    private String errmsg;

    /**
     * 用户ID
     */
    @JsonProperty("userid")
    private String userId;

    /**
     * 用户姓名
     */
    @JsonProperty("name")
    private String name;

    /**
     * 用户头像
     */
    @JsonProperty("avatar")
    private String avatar;

    /**
     * 用户邮箱
     */
    @JsonProperty("email")
    private String email;

    /**
     * 用户手机号
     */
    @JsonProperty("mobile")
    private String mobile;

    /**
     * 用户性别（1-男性，2-女性，0-未知）
     */
    @JsonProperty("gender")
    private String gender;

    /**
     * 用户状态（1-已激活，2-已禁用，4-未激活，5-退出企业）
     */
    @JsonProperty("status")
    private Integer status;

    /**
     * 用户所在部门
     */
    @JsonProperty("department")
    private int[] department;

    /**
     * 用户职位
     */
    @JsonProperty("position")
    private String position;

    /**
     * 是否为部门负责人
     */
    @JsonProperty("isleader")
    private Integer isLeader;

    /**
     * 在所在部门内是否为负责人
     */
    @JsonProperty("is_leader_in_dept")
    private int[] isLeaderInDept;

    /**
     * 用户别名
     */
    @JsonProperty("alias")
    private String alias;

    /**
     * 用户电话
     */
    @JsonProperty("telephone")
    private String telephone;

    /**
     * 用户地址
     */
    @JsonProperty("address")
    private String address;

    /**
     * 用户OpenID（仅在网页授权时返回）
     */
    @JsonProperty("openid")
    private String openId;

    /**
     * 设备ID（仅在扫码登录时返回）
     */
    @JsonProperty("deviceid")
    private String deviceId;

    /**
     * 用户类型（1-企业成员，2-企业外部联系人）
     */
    @JsonProperty("user_type")
    private Integer userType;

    /**
     * 检查响应是否成功
     *
     * @return 如果成功返回true，否则返回false
     */
    public boolean isSuccess() {
        return errcode != null && errcode == 0;
    }

    /**
     * 获取错误描述
     *
     * @return 错误描述
     */
    public String getErrorDescription() {
        if (isSuccess()) {
            return "Success";
        }
        return String.format("Error %d: %s", errcode, errmsg);
    }

    /**
     * 检查用户是否已激活
     *
     * @return 如果已激活返回true，否则返回false
     */
    public boolean isActive() {
        return status != null && status == 1;
    }

    /**
     * 检查用户是否为部门负责人
     *
     * @return 如果是负责人返回true，否则返回false
     */
    public boolean isLeader() {
        return isLeader != null && isLeader == 1;
    }

    /**
     * 获取用户状态描述
     *
     * @return 状态描述
     */
    public String getStatusDescription() {
        if (status == null) {
            return "未知";
        }
        switch (status) {
            case 1:
                return "已激活";
            case 2:
                return "已禁用";
            case 4:
                return "未激活";
            case 5:
                return "退出企业";
            default:
                return "未知状态";
        }
    }

    /**
     * 获取性别描述
     *
     * @return 性别描述
     */
    public String getGenderDescription() {
        if (gender == null) {
            return "未知";
        }
        switch (gender) {
            case "1":
                return "男";
            case "2":
                return "女";
            default:
                return "未知";
        }
    }
}
