package cn.com.handthing.testapp;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * API 文档集成测试
 * 验证 api-doc-spring-boot-starter 的功能
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test")
@AutoConfigureWebMvc
@DisplayName("API文档集成测试")
class ApiDocIntegrationTest {

    @LocalServerPort
    private int port;

    @Autowired
    private TestRestTemplate restTemplate;

    @Test
    @DisplayName("测试 Swagger UI 页面可访问")
    void testSwaggerUiAccessible() {
        String url = "http://localhost:" + port + "/swagger-ui/index.html";
        
        // 不带认证访问应该返回 401
        ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
        assertEquals(HttpStatus.UNAUTHORIZED, response.getStatusCode());
        
        // 带认证访问应该返回 200
        ResponseEntity<String> authenticatedResponse = restTemplate
                .withBasicAuth("admin", "handthing123")
                .getForEntity(url, String.class);
        assertEquals(HttpStatus.OK, authenticatedResponse.getStatusCode());
        
        String body = authenticatedResponse.getBody();
        assertNotNull(body);
        assertTrue(body.contains("swagger-ui"), "响应应该包含 swagger-ui 内容");
    }

    @Test
    @DisplayName("测试 OpenAPI JSON 文档可访问")
    void testOpenApiJsonAccessible() {
        String url = "http://localhost:" + port + "/v3/api-docs";
        
        // 不带认证访问应该返回 401
        ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
        assertEquals(HttpStatus.UNAUTHORIZED, response.getStatusCode());
        
        // 带认证访问应该返回 200
        ResponseEntity<String> authenticatedResponse = restTemplate
                .withBasicAuth("admin", "handthing123")
                .getForEntity(url, String.class);
        assertEquals(HttpStatus.OK, authenticatedResponse.getStatusCode());
        
        String body = authenticatedResponse.getBody();
        assertNotNull(body);
        assertTrue(body.contains("\"openapi\""), "响应应该包含 OpenAPI 规范");
        assertTrue(body.contains("HandThing Test App API Documentation"), "应该包含配置的标题");
        assertTrue(body.contains("API文档测试"), "应该包含测试控制器的标签");
    }

    @Test
    @DisplayName("测试 API 文档包含测试控制器的端点")
    void testApiDocContainsTestEndpoints() {
        String url = "http://localhost:" + port + "/v3/api-docs";
        
        ResponseEntity<String> response = restTemplate
                .withBasicAuth("admin", "handthing123")
                .getForEntity(url, String.class);
        
        assertEquals(HttpStatus.OK, response.getStatusCode());
        String body = response.getBody();
        assertNotNull(body);
        
        // 验证包含测试控制器的端点
        assertTrue(body.contains("/api/doc-test/user/{id}"), "应该包含获取用户信息端点");
        assertTrue(body.contains("/api/doc-test/user"), "应该包含创建用户端点");
        assertTrue(body.contains("/api/doc-test/user/search"), "应该包含搜索用户端点");
        
        // 验证包含日志测试控制器的端点
        assertTrue(body.contains("/api/log/test"), "应该包含日志测试端点");
        assertTrue(body.contains("/api/log/error"), "应该包含错误日志测试端点");
    }

    @Test
    @DisplayName("测试错误的认证信息")
    void testWrongCredentials() {
        String url = "http://localhost:" + port + "/swagger-ui/index.html";
        
        ResponseEntity<String> response = restTemplate
                .withBasicAuth("wrong", "credentials")
                .getForEntity(url, String.class);
        
        assertEquals(HttpStatus.UNAUTHORIZED, response.getStatusCode());
    }

    @Test
    @DisplayName("测试 API 文档配置信息")
    void testApiDocConfiguration() {
        String url = "http://localhost:" + port + "/v3/api-docs";
        
        ResponseEntity<String> response = restTemplate
                .withBasicAuth("admin", "handthing123")
                .getForEntity(url, String.class);
        
        assertEquals(HttpStatus.OK, response.getStatusCode());
        String body = response.getBody();
        assertNotNull(body);
        
        // 验证配置的信息
        assertTrue(body.contains("\"title\":\"HandThing Test App API Documentation\""), 
                "应该包含配置的标题");
        assertTrue(body.contains("\"version\":\"1.0.0-SNAPSHOT\""), 
                "应该包含配置的版本");
        assertTrue(body.contains("HandThing Spring Boot 3 Starters 测试应用的 API 文档"), 
                "应该包含配置的描述");
    }

    @Test
    @DisplayName("测试实际的 API 端点功能")
    void testActualApiEndpoints() {
        // 测试获取用户信息端点
        String userUrl = "http://localhost:" + port + "/api/doc-test/user/123";
        ResponseEntity<String> userResponse = restTemplate.getForEntity(userUrl, String.class);
        assertEquals(HttpStatus.OK, userResponse.getStatusCode());
        
        String userBody = userResponse.getBody();
        assertNotNull(userBody);
        assertTrue(userBody.contains("\"id\":123"), "应该包含用户ID");
        assertTrue(userBody.contains("测试用户123"), "应该包含用户名");
        
        // 测试搜索用户端点
        String searchUrl = "http://localhost:" + port + "/api/doc-test/user/search?keyword=test&page=1&size=10";
        ResponseEntity<String> searchResponse = restTemplate.getForEntity(searchUrl, String.class);
        assertEquals(HttpStatus.OK, searchResponse.getStatusCode());
        
        String searchBody = searchResponse.getBody();
        assertNotNull(searchBody);
        assertTrue(searchBody.contains("\"success\":true"), "应该返回成功状态");
        assertTrue(searchBody.contains("\"keyword\":\"test\""), "应该包含搜索关键词");
    }
}