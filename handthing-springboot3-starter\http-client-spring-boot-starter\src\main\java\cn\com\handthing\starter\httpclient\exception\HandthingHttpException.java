package cn.com.handthing.starter.httpclient.exception;

/**
 * HTTP客户端 starter 中所有自定义异常的根基类。
 * <p>
 * 继承自 {@link RuntimeException}，表示所有HTTP相关的异常都是非受检异常。
 */
public class HandthingHttpException extends RuntimeException {

    /**
     * 使用指定的详细消息构造一个新的运行时异常。
     *
     * @param message 详细消息。
     */
    public HandthingHttpException(String message) {
        super(message);
    }

    /**
     * 使用指定的详细消息和原因构造一个新的运行时异常。
     *
     * @param message 详细消息。
     * @param cause   原因（cause）。(一个 null 值是允许的，并且表示原因不存在或未知。)
     */
    public HandthingHttpException(String message, Throwable cause) {
        super(message, cause);
    }
}