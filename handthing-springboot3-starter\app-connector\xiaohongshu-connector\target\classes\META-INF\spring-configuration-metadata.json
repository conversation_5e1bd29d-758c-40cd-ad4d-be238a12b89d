{"groups": [{"name": "handthing.connector.xiaohongshu", "type": "cn.com.handthing.starter.connector.xiaohongshu.config.XiaoHongShuConfig", "sourceType": "cn.com.handthing.starter.connector.xiaohongshu.config.XiaoHongShuConfig"}], "properties": [{"name": "handthing.connector.xiaohongshu.analytics-enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用数据分析功能", "sourceType": "cn.com.handthing.starter.connector.xiaohongshu.config.XiaoHongShuConfig"}, {"name": "handthing.connector.xiaohongshu.api-base-url", "type": "java.lang.String", "description": "小红书API基础URL", "sourceType": "cn.com.handthing.starter.connector.xiaohongshu.config.XiaoHongShuConfig"}, {"name": "handthing.connector.xiaohongshu.app-key", "type": "java.lang.String", "description": "小红书应用Key (App Key)", "sourceType": "cn.com.handthing.starter.connector.xiaohongshu.config.XiaoHongShuConfig"}, {"name": "handthing.connector.xiaohongshu.app-secret", "type": "java.lang.String", "description": "小红书应用密钥 (App Secret)", "sourceType": "cn.com.handthing.starter.connector.xiaohongshu.config.XiaoHongShuConfig"}, {"name": "handthing.connector.xiaohongshu.app-type", "type": "java.lang.String", "description": "应用类型 personal: 个人开发者 enterprise: 企业开发者 brand: 品牌方", "sourceType": "cn.com.handthing.starter.connector.xiaohongshu.config.XiaoHongShuConfig"}, {"name": "handthing.connector.xiaohongshu.auth-url", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.xiaohongshu.config.XiaoHongShuConfig"}, {"name": "handthing.connector.xiaohongshu.brand-mode", "type": "java.lang.Bo<PERSON>an", "description": "是否为品牌方模式", "sourceType": "cn.com.handthing.starter.connector.xiaohongshu.config.XiaoHongShuConfig"}, {"name": "handthing.connector.xiaohongshu.client-id", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.xiaohongshu.config.XiaoHongShuConfig"}, {"name": "handthing.connector.xiaohongshu.client-secret", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.xiaohongshu.config.XiaoHongShuConfig"}, {"name": "handthing.connector.xiaohongshu.community-enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用社区互动功能", "sourceType": "cn.com.handthing.starter.connector.xiaohongshu.config.XiaoHongShuConfig"}, {"name": "handthing.connector.xiaohongshu.connect-timeout", "type": "java.lang.Integer", "sourceType": "cn.com.handthing.starter.connector.xiaohongshu.config.XiaoHongShuConfig"}, {"name": "handthing.connector.xiaohongshu.content-publish-enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用内容发布功能", "sourceType": "cn.com.handthing.starter.connector.xiaohongshu.config.XiaoHongShuConfig"}, {"name": "handthing.connector.xiaohongshu.ecommerce-enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用电商功能", "sourceType": "cn.com.handthing.starter.connector.xiaohongshu.config.XiaoHongShuConfig"}, {"name": "handthing.connector.xiaohongshu.enabled", "type": "java.lang.Bo<PERSON>an", "sourceType": "cn.com.handthing.starter.connector.xiaohongshu.config.XiaoHongShuConfig"}, {"name": "handthing.connector.xiaohongshu.extra-config", "type": "java.util.Map<java.lang.String,java.lang.Object>", "sourceType": "cn.com.handthing.starter.connector.xiaohongshu.config.XiaoHongShuConfig"}, {"name": "handthing.connector.xiaohongshu.max-retries", "type": "java.lang.Integer", "sourceType": "cn.com.handthing.starter.connector.xiaohongshu.config.XiaoHongShuConfig"}, {"name": "handthing.connector.xiaohongshu.read-timeout", "type": "java.lang.Integer", "sourceType": "cn.com.handthing.starter.connector.xiaohongshu.config.XiaoHongShuConfig"}, {"name": "handthing.connector.xiaohongshu.redirect-uri", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.xiaohongshu.config.XiaoHongShuConfig"}, {"name": "handthing.connector.xiaohongshu.sandbox", "type": "java.lang.Bo<PERSON>an", "sourceType": "cn.com.handthing.starter.connector.xiaohongshu.config.XiaoHongShuConfig"}, {"name": "handthing.connector.xiaohongshu.sandbox-api-base-url", "type": "java.lang.String", "description": "沙箱环境API基础URL", "sourceType": "cn.com.handthing.starter.connector.xiaohongshu.config.XiaoHongShuConfig"}, {"name": "handthing.connector.xiaohongshu.sandbox-mode", "type": "java.lang.Bo<PERSON>an", "description": "是否使用沙箱环境", "sourceType": "cn.com.handthing.starter.connector.xiaohongshu.config.XiaoHongShuConfig"}, {"name": "handthing.connector.xiaohongshu.scope", "type": "java.lang.String", "description": "授权范围", "sourceType": "cn.com.handthing.starter.connector.xiaohongshu.config.XiaoHongShuConfig"}, {"name": "handthing.connector.xiaohongshu.token-url", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.xiaohongshu.config.XiaoHongShuConfig"}, {"name": "handthing.connector.xiaohongshu.user-info-url", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.xiaohongshu.config.XiaoHongShuConfig"}], "hints": [], "ignored": {"properties": []}}