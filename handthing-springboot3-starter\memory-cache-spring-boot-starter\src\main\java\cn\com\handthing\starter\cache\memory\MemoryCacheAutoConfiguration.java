package cn.com.handthing.starter.cache.memory;

import cn.com.handthing.starter.cache.memory.config.MemoryCacheProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cache.CacheManager;
import org.springframework.context.annotation.Bean;

import jakarta.annotation.PreDestroy;

/**
 * 内存缓存自动配置类
 * <p>
 * 当缓存类型配置为memory时，自动配置内存缓存管理器
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@AutoConfiguration
@EnableConfigurationProperties(MemoryCacheProperties.class)
@ConditionalOnProperty(prefix = "handthing.cache", name = "type", havingValue = "memory")
public class MemoryCacheAutoConfiguration {

    private MemoryCacheManager memoryCacheManager;

    /**
     * 创建内存缓存管理器
     *
     * @param properties 内存缓存配置属性
     * @return 内存缓存管理器
     */
    @Bean
    @ConditionalOnMissingBean(CacheManager.class)
    public CacheManager memoryCacheManager(MemoryCacheProperties properties) {
        log.info("Creating MemoryCacheManager with properties: {}", properties);
        this.memoryCacheManager = new MemoryCacheManager(properties);
        return this.memoryCacheManager;
    }

    /**
     * 应用关闭时清理资源
     */
    @PreDestroy
    public void destroy() {
        if (memoryCacheManager != null) {
            memoryCacheManager.shutdown();
        }
    }
}