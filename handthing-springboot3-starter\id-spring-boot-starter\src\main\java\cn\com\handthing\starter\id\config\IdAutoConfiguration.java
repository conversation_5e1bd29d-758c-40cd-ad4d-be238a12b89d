package cn.com.handthing.starter.id.config;

import cn.com.handthing.starter.id.generator.IdGenerator;
import cn.com.handthing.starter.id.generator.impl.PooledIdGenerator;
import cn.com.handthing.starter.id.generator.impl.SnowflakeIdGenerator;
import cn.com.handthing.starter.id.generator.impl.UuidGenerator;
import cn.com.handthing.starter.id.registry.IdGeneratorRegistry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Primary;

/**
 * ID生成器自动配置类
 * <p>
 * 负责自动装配ID生成器相关的Bean，包括内置生成器、注册中心等。
 * 根据配置属性决定是否启用池化功能。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@AutoConfiguration
@EnableConfigurationProperties(IdProperties.class)
@ConditionalOnProperty(prefix = "handthing.id", name = "enabled", havingValue = "true", matchIfMissing = true)
@ComponentScan(basePackages = "cn.com.handthing.starter.id")
public class IdAutoConfiguration {
    
    /**
     * 配置UUID生成器
     *
     * @param properties 配置属性
     * @return UUID生成器
     */
    @Bean("uuidGenerator")
    @ConditionalOnProperty(prefix = "handthing.id.uuid", name = "enabled", havingValue = "true", matchIfMissing = true)
    @ConditionalOnMissingBean(name = "uuidGenerator")
    public UuidGenerator uuidGenerator(IdProperties properties) {
        IdProperties.Uuid uuidConfig = properties.getUuid();
        
        UuidGenerator generator = new UuidGenerator(
                uuidConfig.isIncludeHyphens(),
                uuidConfig.isUpperCase()
        );
        
        log.info("Configured UUID generator: includeHyphens={}, upperCase={}", 
                uuidConfig.isIncludeHyphens(), uuidConfig.isUpperCase());
        
        return generator;
    }
    
    /**
     * 配置Snowflake生成器
     *
     * @param properties 配置属性
     * @return Snowflake生成器
     */
    @Bean("snowflakeIdGenerator")
    @ConditionalOnProperty(prefix = "handthing.id.snowflake", name = "enabled", havingValue = "true", matchIfMissing = true)
    @ConditionalOnMissingBean(name = "snowflakeIdGenerator")
    public SnowflakeIdGenerator snowflakeIdGenerator(IdProperties properties) {
        // 验证配置
        properties.validate();
        
        IdProperties.Snowflake snowflakeConfig = properties.getSnowflake();
        
        SnowflakeIdGenerator generator = new SnowflakeIdGenerator(
                snowflakeConfig.getWorkerId(),
                snowflakeConfig.getDatacenterId()
        );
        
        log.info("Configured Snowflake generator: workerId={}, datacenterId={}", 
                snowflakeConfig.getWorkerId(), snowflakeConfig.getDatacenterId());
        
        return generator;
    }
    
    /**
     * 配置池化Snowflake生成器
     * <p>
     * 当启用池化时，用池化装饰器包装Snowflake生成器，并设置为主要Bean。
     * </p>
     *
     * @param snowflakeGenerator 原始Snowflake生成器
     * @param properties         配置属性
     * @return 池化Snowflake生成器
     */
    @Bean("pooledSnowflakeIdGenerator")
    @Primary
    @ConditionalOnProperty(prefix = "handthing.id.snowflake.pool", name = "enabled", havingValue = "true")
    @ConditionalOnMissingBean(name = "pooledSnowflakeIdGenerator")
    public PooledIdGenerator<Long> pooledSnowflakeIdGenerator(
            SnowflakeIdGenerator snowflakeGenerator,
            IdProperties properties) {
        
        IdProperties.Snowflake.Pool poolConfig = properties.getSnowflake().getPool();
        
        PooledIdGenerator<Long> pooledGenerator = new PooledIdGenerator<>(
                snowflakeGenerator,
                poolConfig.getSize(),
                poolConfig.getThreshold(),
                poolConfig.getBatchSize()
        );
        
        log.info("Configured pooled Snowflake generator: size={}, threshold={}, batchSize={}", 
                poolConfig.getSize(), poolConfig.getThreshold(), poolConfig.getBatchSize());
        
        return pooledGenerator;
    }
    
    /**
     * 配置ID生成器注册中心
     * <p>
     * 注册中心会自动收集所有IdGenerator类型的Bean。
     * </p>
     *
     * @return ID生成器注册中心
     */
    @Bean
    @ConditionalOnMissingBean
    public IdGeneratorRegistry idGeneratorRegistry() {
        log.info("Configured ID generator registry");
        return new IdGeneratorRegistry();
    }
    
    /**
     * 配置启动时的日志输出
     */
    @Bean
    public IdConfigurationLogger idConfigurationLogger(IdProperties properties) {
        return new IdConfigurationLogger(properties);
    }
    
    /**
     * ID配置日志记录器
     */
    public static class IdConfigurationLogger {
        
        public IdConfigurationLogger(IdProperties properties) {
            logConfiguration(properties);
        }
        
        private void logConfiguration(IdProperties properties) {
            log.info("=== HandThing ID Generator Configuration ===");
            log.info("Enabled: {}", properties.isEnabled());
            
            if (properties.getSnowflake().isEnabled()) {
                log.info("Snowflake Generator:");
                log.info("  Worker ID: {}", properties.getSnowflake().getWorkerId());
                log.info("  Datacenter ID: {}", properties.getSnowflake().getDatacenterId());
                
                if (properties.getSnowflake().getPool().isEnabled()) {
                    log.info("  Pool Enabled: true");
                    log.info("  Pool Size: {}", properties.getSnowflake().getPool().getSize());
                    log.info("  Pool Threshold: {}", properties.getSnowflake().getPool().getThreshold());
                    log.info("  Pool Batch Size: {}", properties.getSnowflake().getPool().getBatchSize());
                } else {
                    log.info("  Pool Enabled: false");
                }
            }
            
            if (properties.getUuid().isEnabled()) {
                log.info("UUID Generator:");
                log.info("  Include Hyphens: {}", properties.getUuid().isIncludeHyphens());
                log.info("  Upper Case: {}", properties.getUuid().isUpperCase());
            }
            
            log.info("==========================================");
        }
    }
}
