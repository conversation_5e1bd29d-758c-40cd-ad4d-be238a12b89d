package cn.com.handthing.starter.auth.thirdparty;

import cn.com.handthing.starter.auth.core.UserInfo;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * 微信用户服务接口
 * <p>
 * 定义微信认证相关的用户操作接口，包括用户查找、创建、绑定等。
 * 业务系统需要实现此接口来提供具体的用户数据访问逻辑。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
public interface WechatUserService {

    /**
     * 根据微信OpenID查找用户
     *
     * @param openId 微信OpenID
     * @param appId  应用ID
     * @return 用户信息，如果未找到返回空
     */
    Optional<UserInfo> findByWechatOpenId(String openId, String appId);

    /**
     * 根据微信UnionID查找用户
     *
     * @param unionId 微信UnionID
     * @return 用户信息，如果未找到返回空
     */
    Optional<UserInfo> findByWechatUnionId(String unionId);

    /**
     * 根据用户ID查找用户
     *
     * @param userId 用户ID
     * @return 用户信息，如果未找到返回空
     */
    Optional<UserInfo> findById(String userId);

    /**
     * 根据手机号查找用户
     *
     * @param mobile 手机号
     * @return 用户信息，如果未找到返回空
     */
    Optional<UserInfo> findByMobile(String mobile);

    /**
     * 根据邮箱查找用户
     *
     * @param email 邮箱
     * @return 用户信息，如果未找到返回空
     */
    Optional<UserInfo> findByEmail(String email);

    /**
     * 创建新用户（基于微信信息）
     *
     * @param wechatUserInfo 微信用户信息
     * @param appId          应用ID
     * @return 创建的用户信息
     */
    UserInfo createUser(WechatApiService.WechatUserResult wechatUserInfo, String appId);

    /**
     * 绑定微信用户
     *
     * @param userId  用户ID
     * @param openId  微信OpenID
     * @param unionId 微信UnionID
     * @param appId   应用ID
     * @return 如果绑定成功返回true，否则返回false
     */
    boolean bindWechatUser(String userId, String openId, String unionId, String appId);

    /**
     * 解绑微信用户
     *
     * @param userId 用户ID
     * @param appId  应用ID
     * @return 如果解绑成功返回true，否则返回false
     */
    boolean unbindWechatUser(String userId, String appId);

    /**
     * 检查用户状态是否有效
     *
     * @param userInfo 用户信息
     * @return 如果用户状态有效返回true，否则返回false
     */
    boolean isUserValid(UserInfo userInfo);

    /**
     * 检查用户是否被锁定
     *
     * @param userInfo 用户信息
     * @return 如果用户被锁定返回true，否则返回false
     */
    boolean isUserLocked(UserInfo userInfo);

    /**
     * 检查用户是否被禁用
     *
     * @param userInfo 用户信息
     * @return 如果用户被禁用返回true，否则返回false
     */
    boolean isUserDisabled(UserInfo userInfo);

    /**
     * 检查账户是否过期
     *
     * @param userInfo 用户信息
     * @return 如果账户过期返回true，否则返回false
     */
    boolean isAccountExpired(UserInfo userInfo);

    /**
     * 更新用户最后登录信息
     *
     * @param userId    用户ID
     * @param loginTime 登录时间
     * @param ipAddress IP地址
     * @param userAgent 用户代理
     */
    void updateLastLoginInfo(String userId, LocalDateTime loginTime, String ipAddress, String userAgent);

    /**
     * 记录登录失败
     *
     * @param openId    微信OpenID
     * @param appId     应用ID
     * @param ipAddress IP地址
     * @param reason    失败原因
     */
    void recordLoginFailure(String openId, String appId, String ipAddress, String reason);

    /**
     * 清除登录失败记录
     *
     * @param openId 微信OpenID
     * @param appId  应用ID
     */
    void clearLoginFailures(String openId, String appId);

    /**
     * 获取登录失败次数
     *
     * @param openId 微信OpenID
     * @param appId  应用ID
     * @return 登录失败次数
     */
    int getLoginFailureCount(String openId, String appId);

    /**
     * 锁定用户
     *
     * @param userId    用户ID
     * @param reason    锁定原因
     * @param lockUntil 锁定到期时间
     */
    void lockUser(String userId, String reason, LocalDateTime lockUntil);

    /**
     * 解锁用户
     *
     * @param userId 用户ID
     */
    void unlockUser(String userId);

    /**
     * 检查是否为首次登录
     *
     * @param userInfo 用户信息
     * @return 如果是首次登录返回true，否则返回false
     */
    default boolean isFirstLogin(UserInfo userInfo) {
        return userInfo.getLastLoginTime() == null;
    }

    /**
     * 检查微信用户是否已绑定
     *
     * @param openId 微信OpenID
     * @param appId  应用ID
     * @return 如果已绑定返回true，否则返回false
     */
    default boolean isWechatUserBound(String openId, String appId) {
        return findByWechatOpenId(openId, appId).isPresent();
    }

    /**
     * 生成用户ID
     *
     * @param openId 微信OpenID
     * @param appId  应用ID
     * @return 用户ID
     */
    default String generateUserId(String openId, String appId) {
        return "wechat_" + appId + "_" + openId + "_" + System.currentTimeMillis();
    }

    /**
     * 生成默认用户名
     *
     * @param openId 微信OpenID
     * @param appId  应用ID
     * @return 默认用户名
     */
    default String generateDefaultUsername(String openId, String appId) {
        return "wechat_" + openId;
    }

    /**
     * 获取默认角色
     *
     * @return 默认角色列表
     */
    default java.util.List<String> getDefaultRoles() {
        return java.util.List.of("USER", "WECHAT_USER");
    }

    /**
     * 获取默认权限
     *
     * @return 默认权限列表
     */
    default java.util.List<String> getDefaultPermissions() {
        return java.util.List.of("READ", "WECHAT_ACCESS");
    }

    /**
     * 验证微信OpenID格式
     *
     * @param openId 微信OpenID
     * @return 如果格式正确返回true，否则返回false
     */
    default boolean isValidWechatOpenId(String openId) {
        return openId != null && openId.trim().length() > 0 && openId.length() <= 64;
    }

    /**
     * 验证应用ID格式
     *
     * @param appId 应用ID
     * @return 如果格式正确返回true，否则返回false
     */
    default boolean isValidAppId(String appId) {
        return appId != null && appId.trim().length() > 0 && appId.length() <= 64;
    }

    /**
     * 构建微信用户唯一标识
     *
     * @param openId 微信OpenID
     * @param appId  应用ID
     * @return 唯一标识
     */
    default String buildWechatUserKey(String openId, String appId) {
        return appId + ":" + openId;
    }

    /**
     * 根据性别代码获取性别描述
     *
     * @param sexCode 性别代码（1-男性，2-女性，0-未知）
     * @return 性别描述
     */
    default String getSexDescription(String sexCode) {
        if ("1".equals(sexCode)) {
            return "男";
        } else if ("2".equals(sexCode)) {
            return "女";
        } else {
            return "未知";
        }
    }

    /**
     * 构建用户地址信息
     *
     * @param country  国家
     * @param province 省份
     * @param city     城市
     * @return 地址信息
     */
    default String buildAddress(String country, String province, String city) {
        StringBuilder address = new StringBuilder();
        if (country != null && !country.trim().isEmpty()) {
            address.append(country);
        }
        if (province != null && !province.trim().isEmpty()) {
            if (address.length() > 0) address.append(" ");
            address.append(province);
        }
        if (city != null && !city.trim().isEmpty()) {
            if (address.length() > 0) address.append(" ");
            address.append(city);
        }
        return address.length() > 0 ? address.toString() : null;
    }
}
