# HandThing 多租户认证系统 API 文档

## 概述

HandThing 多租户认证系统提供了完整的 RESTful API 接口，用于管理和查询租户信息、配置和状态。

## 基础信息

- **Base URL**: `http://localhost:8081`
- **API 版本**: v1.0
- **认证方式**: 基于 HTTP 头部的租户识别
- **内容类型**: `application/json`

## 租户识别

所有 API 请求都支持通过 HTTP 头部指定租户：

```http
X-Tenant-ID: demo
```

支持的头部名称（按优先级排序）：
1. `X-Tenant-ID`
2. `Tenant-ID`
3. `X-Tenant`
4. `Tenant`

如果未指定租户头部，系统将使用默认租户 `default`。

## API 接口

### 1. 系统健康检查

检查多租户系统的健康状态。

**请求**
```http
GET /api/tenant/health
```

**请求头**
```http
X-Tenant-ID: demo
```

**响应**
```json
{
  "status": "UP",
  "tenantId": "demo",
  "tenantExists": true,
  "tenantName": "演示租户",
  "contextHolder": "OK",
  "configService": "OK",
  "timestamp": "1753788539960"
}
```

**响应字段说明**

| 字段 | 类型 | 说明 |
|------|------|------|
| status | String | 系统状态：UP/DOWN |
| tenantId | String | 当前租户ID |
| tenantExists | Boolean | 租户是否存在 |
| tenantName | String | 租户名称 |
| contextHolder | String | 上下文管理器状态 |
| configService | String | 配置服务状态 |
| timestamp | String | 时间戳 |

**错误响应**
```json
{
  "success": false,
  "error": "INVALID_TENANT",
  "message": "Tenant not found",
  "tenant": "invalid-tenant",
  "timestamp": "1753788539960"
}
```

### 2. 获取当前租户信息

获取当前请求上下文中的租户详细信息。

**请求**
```http
GET /api/tenant/current
```

**请求头**
```http
X-Tenant-ID: demo
```

**响应**
```json
{
  "tenantId": "demo",
  "tenantName": "演示租户",
  "tenantStatus": "ACTIVE",
  "isActive": true,
  "contextExists": true,
  "hasTenantId": true,
  "resolvedTenantId": "demo",
  "attributes": ["resolved_at"],
  "contextInfo": "Tenant{id='demo', name='演示租户', status=ACTIVE, attributes=1}"
}
```

**响应字段说明**

| 字段 | 类型 | 说明 |
|------|------|------|
| tenantId | String | 租户ID |
| tenantName | String | 租户名称 |
| tenantStatus | String | 租户状态：ACTIVE/INACTIVE/TRIAL |
| isActive | Boolean | 是否为活跃状态 |
| contextExists | Boolean | 租户上下文是否存在 |
| hasTenantId | Boolean | 是否有租户ID |
| resolvedTenantId | String | 解析后的租户ID |
| attributes | Array | 租户属性列表 |
| contextInfo | String | 完整的上下文信息 |

### 3. 获取租户配置

获取指定租户的配置信息。

**请求**
```http
GET /api/tenant/config
GET /api/tenant/config?configKey=jwt.secret
```

**请求参数**

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| configKey | String | 否 | 配置键名，不指定则返回所有配置 |

**请求头**
```http
X-Tenant-ID: demo
```

**响应（单个配置）**
```json
{
  "configKey": "jwt.secret",
  "tenantId": "demo",
  "configValue": "demo-tenant-jwt-secret-key-2024"
}
```

**响应（所有配置）**
```json
{
  "tenantId": "demo",
  "configCount": 15,
  "configs": {
    "tenant.name": "演示租户",
    "tenant.status": "ACTIVE",
    "jwt.secret": "demo-tenant-jwt-secret-key-2024",
    "jwt.access.token.expiration": "3600",
    "password.min.length": "8",
    "wecom.auth.enabled": "true"
  }
}
```

**响应字段说明**

| 字段 | 类型 | 说明 |
|------|------|------|
| configKey | String | 配置键名（单个配置时） |
| tenantId | String | 租户ID |
| configValue | String | 配置值（单个配置时） |
| configCount | Integer | 配置数量（所有配置时） |
| configs | Object | 配置键值对（所有配置时） |

### 4. 获取租户列表

获取系统中所有租户的列表和基本信息。

**请求**
```http
GET /api/tenant/list
```

**响应**
```json
{
  "success": true,
  "tenantCount": 3,
  "tenantIds": ["default", "demo", "test"],
  "tenantInfos": {
    "default": {
      "name": "默认租户",
      "exists": true,
      "status": "ACTIVE"
    },
    "demo": {
      "name": "演示租户",
      "exists": true,
      "status": "ACTIVE"
    },
    "test": {
      "name": "测试租户",
      "exists": true,
      "status": "TRIAL"
    }
  }
}
```

**响应字段说明**

| 字段 | 类型 | 说明 |
|------|------|------|
| success | Boolean | 操作是否成功 |
| tenantCount | Integer | 租户总数 |
| tenantIds | Array | 租户ID列表 |
| tenantInfos | Object | 租户详细信息 |
| tenantInfos.{id}.name | String | 租户名称 |
| tenantInfos.{id}.exists | Boolean | 租户是否存在 |
| tenantInfos.{id}.status | String | 租户状态 |

### 5. 获取租户统计信息

获取租户相关的统计信息。

**请求**
```http
GET /api/tenant/stats
```

**请求头**
```http
X-Tenant-ID: demo
```

**响应**
```json
{
  "tenantId": "demo",
  "tenantName": "演示租户",
  "configCount": 15,
  "activeConfigs": 15,
  "sensitiveConfigs": 3,
  "lastUpdated": "2025-07-29T19:30:00Z",
  "createdAt": "2025-07-29T10:00:00Z",
  "status": "ACTIVE",
  "features": {
    "authEnabled": true,
    "cacheEnabled": true,
    "monitoringEnabled": true
  }
}
```

**响应字段说明**

| 字段 | 类型 | 说明 |
|------|------|------|
| tenantId | String | 租户ID |
| tenantName | String | 租户名称 |
| configCount | Integer | 配置总数 |
| activeConfigs | Integer | 活跃配置数 |
| sensitiveConfigs | Integer | 敏感配置数 |
| lastUpdated | String | 最后更新时间 |
| createdAt | String | 创建时间 |
| status | String | 租户状态 |
| features | Object | 功能特性状态 |

### 6. 租户切换（POST）

切换到指定的租户（用于测试）。

**请求**
```http
POST /api/tenant/switch?targetTenantId=demo
```

**请求参数**

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| targetTenantId | String | 是 | 目标租户ID |

**响应**
```json
{
  "success": true,
  "message": "Tenant switched successfully",
  "fromTenant": "default",
  "toTenant": "demo",
  "timestamp": "1753788539960"
}
```

## 错误处理

### 错误响应格式

所有错误响应都遵循统一的格式：

```json
{
  "success": false,
  "error": "ERROR_CODE",
  "message": "Human readable error message",
  "details": "Additional error details",
  "timestamp": "1753788539960"
}
```

### 常见错误码

| 错误码 | HTTP状态码 | 说明 |
|--------|------------|------|
| INVALID_TENANT | 400 | 租户不存在或无效 |
| CONFIG_NOT_FOUND | 404 | 配置不存在 |
| TENANT_REQUIRED | 400 | 需要指定租户 |
| INTERNAL_ERROR | 500 | 内部服务器错误 |

### 错误示例

**租户不存在**
```json
{
  "success": false,
  "error": "INVALID_TENANT",
  "message": "Tenant not found",
  "tenant": "invalid-tenant",
  "timestamp": "1753788539960"
}
```

**配置不存在**
```json
{
  "success": false,
  "error": "CONFIG_NOT_FOUND",
  "message": "Configuration key not found",
  "configKey": "invalid.key",
  "tenantId": "demo",
  "timestamp": "1753788539960"
}
```

## 使用示例

### cURL 示例

**获取默认租户健康状态**
```bash
curl -X GET http://localhost:8081/api/tenant/health
```

**获取指定租户配置**
```bash
curl -X GET \
  -H "X-Tenant-ID: demo" \
  http://localhost:8081/api/tenant/config?configKey=jwt.secret
```

**获取租户列表**
```bash
curl -X GET http://localhost:8081/api/tenant/list
```

### JavaScript 示例

```javascript
// 获取租户信息
async function getTenantInfo(tenantId) {
  const response = await fetch('/api/tenant/current', {
    headers: {
      'X-Tenant-ID': tenantId
    }
  });
  return await response.json();
}

// 获取租户配置
async function getTenantConfig(tenantId, configKey) {
  const url = configKey 
    ? `/api/tenant/config?configKey=${configKey}`
    : '/api/tenant/config';
    
  const response = await fetch(url, {
    headers: {
      'X-Tenant-ID': tenantId
    }
  });
  return await response.json();
}
```

### Java 示例

```java
@RestController
public class TenantApiClient {
    
    @Autowired
    private RestTemplate restTemplate;
    
    public TenantInfo getTenantInfo(String tenantId) {
        HttpHeaders headers = new HttpHeaders();
        headers.set("X-Tenant-ID", tenantId);
        
        HttpEntity<String> entity = new HttpEntity<>(headers);
        
        ResponseEntity<TenantInfo> response = restTemplate.exchange(
            "/api/tenant/current",
            HttpMethod.GET,
            entity,
            TenantInfo.class
        );
        
        return response.getBody();
    }
}
```

## 最佳实践

### 1. 租户头部设置
- 始终在请求中包含租户头部
- 使用标准的 `X-Tenant-ID` 头部名称
- 确保租户ID的有效性

### 2. 错误处理
- 检查响应中的 `success` 字段
- 根据错误码进行相应的错误处理
- 记录错误信息用于调试

### 3. 性能优化
- 缓存租户配置信息
- 避免频繁的配置查询
- 使用批量接口获取多个配置

### 4. 安全考虑
- 验证租户ID的合法性
- 不要在URL中暴露敏感的租户信息
- 使用HTTPS传输敏感数据

---

更多信息请参考 [HandThing 多租户认证系统文档](../README.md)。
