package cn.com.handthing.starter.connector.wechat.api;

import cn.com.handthing.starter.connector.wechat.config.WeChatConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * 微信消息API
 * <p>
 * 提供微信消息发送相关的API功能，包括文本消息、图片消息、模板消息等。
 * 支持公众号和小程序的消息推送功能。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "handthing.connector.wechat", name = "enabled", havingValue = "true")
public class WeChatMessageApi {

    private final WeChatConfig config;
    private final RestTemplate restTemplate;

    /**
     * 发送文本消息
     *
     * @param accessToken 访问令牌
     * @param toUser      接收用户的openid
     * @param content     消息内容
     * @return 发送结果
     */
    public Map<String, Object> sendTextMessage(String accessToken, String toUser, String content) {
        try {
            log.debug("Sending WeChat text message to user: {}", toUser);

            String url = config.getEffectiveApiBaseUrl() + "/cgi-bin/message/custom/send?access_token=" + accessToken;

            Map<String, Object> message = new HashMap<>();
            message.put("touser", toUser);
            message.put("msgtype", "text");
            
            Map<String, String> text = new HashMap<>();
            text.put("content", content);
            message.put("text", text);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(message, headers);

            Map<String, Object> response = restTemplate.postForObject(url, request, Map.class);
            log.debug("WeChat text message sent, response: {}", response);

            return response;

        } catch (Exception e) {
            log.error("Failed to send WeChat text message", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("errcode", -1);
            errorResponse.put("errmsg", "Failed to send message: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 发送图片消息
     *
     * @param accessToken 访问令牌
     * @param toUser      接收用户的openid
     * @param mediaId     媒体文件ID
     * @return 发送结果
     */
    public Map<String, Object> sendImageMessage(String accessToken, String toUser, String mediaId) {
        try {
            log.debug("Sending WeChat image message to user: {}", toUser);

            String url = config.getEffectiveApiBaseUrl() + "/cgi-bin/message/custom/send?access_token=" + accessToken;

            Map<String, Object> message = new HashMap<>();
            message.put("touser", toUser);
            message.put("msgtype", "image");
            
            Map<String, String> image = new HashMap<>();
            image.put("media_id", mediaId);
            message.put("image", image);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(message, headers);

            Map<String, Object> response = restTemplate.postForObject(url, request, Map.class);
            log.debug("WeChat image message sent, response: {}", response);

            return response;

        } catch (Exception e) {
            log.error("Failed to send WeChat image message", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("errcode", -1);
            errorResponse.put("errmsg", "Failed to send image message: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 发送模板消息
     *
     * @param accessToken  访问令牌
     * @param toUser       接收用户的openid
     * @param templateId   模板ID
     * @param templateData 模板数据
     * @param url          跳转URL（可选）
     * @return 发送结果
     */
    public Map<String, Object> sendTemplateMessage(String accessToken, String toUser, String templateId, 
                                                   Map<String, Object> templateData, String url) {
        try {
            log.debug("Sending WeChat template message to user: {}", toUser);

            String apiUrl = config.getEffectiveApiBaseUrl() + "/cgi-bin/message/template/send?access_token=" + accessToken;

            Map<String, Object> message = new HashMap<>();
            message.put("touser", toUser);
            message.put("template_id", templateId);
            if (url != null && !url.trim().isEmpty()) {
                message.put("url", url);
            }
            message.put("data", templateData);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(message, headers);

            Map<String, Object> response = restTemplate.postForObject(apiUrl, request, Map.class);
            log.debug("WeChat template message sent, response: {}", response);

            return response;

        } catch (Exception e) {
            log.error("Failed to send WeChat template message", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("errcode", -1);
            errorResponse.put("errmsg", "Failed to send template message: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 创建模板消息数据项
     *
     * @param value 数据值
     * @param color 颜色（可选）
     * @return 数据项
     */
    public Map<String, String> createTemplateDataItem(String value, String color) {
        Map<String, String> item = new HashMap<>();
        item.put("value", value);
        if (color != null && !color.trim().isEmpty()) {
            item.put("color", color);
        }
        return item;
    }

    /**
     * 创建模板消息数据项（默认颜色）
     *
     * @param value 数据值
     * @return 数据项
     */
    public Map<String, String> createTemplateDataItem(String value) {
        return createTemplateDataItem(value, null);
    }
}
