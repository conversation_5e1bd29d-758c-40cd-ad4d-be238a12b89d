{"properties": [{"name": "handthing.auth.saas.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用SaaS多租户认证", "defaultValue": true}, {"name": "handthing.auth.saas.default-tenant-id", "type": "java.lang.String", "description": "默认租户ID，当无法解析租户ID时使用", "defaultValue": "default"}, {"name": "handthing.auth.saas.tenant-required", "type": "java.lang.Bo<PERSON>an", "description": "是否要求必须有租户，如果为true，当无法解析租户ID时会返回错误", "defaultValue": false}, {"name": "handthing.auth.tenant.resolver.strategy", "type": "java.lang.String", "description": "租户解析策略，可选值：subdomain, header, path, custom", "defaultValue": "header"}, {"name": "handthing.auth.tenant.resolver.multi-resolver", "type": "java.lang.Bo<PERSON>an", "description": "是否启用多解析器，如果为true，将按优先级尝试所有配置的解析器", "defaultValue": true}, {"name": "handthing.auth.tenant.resolver.subdomain.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用子域名解析", "defaultValue": false}, {"name": "handthing.auth.tenant.resolver.subdomain.pattern", "type": "java.lang.String", "description": "子域名模式，使用{tenant}作为占位符", "defaultValue": "{tenant}.myapp.com"}, {"name": "handthing.auth.tenant.resolver.subdomain.base-domain", "type": "java.lang.String", "description": "基础域名，如果指定，只有匹配此域名的请求才会进行租户解析"}, {"name": "handthing.auth.tenant.resolver.subdomain.ignore-www", "type": "java.lang.Bo<PERSON>an", "description": "是否忽略www前缀", "defaultValue": true}, {"name": "handthing.auth.tenant.resolver.subdomain.order", "type": "java.lang.Integer", "description": "子域名解析器优先级", "defaultValue": 10}, {"name": "handthing.auth.tenant.resolver.header.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用HTTP头解析", "defaultValue": true}, {"name": "handthing.auth.tenant.resolver.header.name", "type": "java.lang.String", "description": "主要的租户头名称", "defaultValue": "X-Tenant-ID"}, {"name": "handthing.auth.tenant.resolver.header.fallback-names", "type": "java.util.List<java.lang.String>", "description": "备选的租户头名称列表", "defaultValue": ["Tenant-ID", "X-Tenant", "Tenant"]}, {"name": "handthing.auth.tenant.resolver.header.case-sensitive", "type": "java.lang.Bo<PERSON>an", "description": "是否区分大小写", "defaultValue": false}, {"name": "handthing.auth.tenant.resolver.header.order", "type": "java.lang.Integer", "description": "HTTP头解析器优先级", "defaultValue": 20}, {"name": "handthing.auth.tenant.resolver.path.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用URL路径解析", "defaultValue": false}, {"name": "handthing.auth.tenant.resolver.path.pattern", "type": "java.lang.String", "description": "路径模式，使用{tenant}作为占位符", "defaultValue": "/api/{tenant}/**"}, {"name": "handthing.auth.tenant.resolver.path.prefix", "type": "java.lang.String", "description": "路径前缀", "defaultValue": "/api"}, {"name": "handthing.auth.tenant.resolver.path.remove-prefix", "type": "java.lang.Bo<PERSON>an", "description": "是否移除路径前缀", "defaultValue": false}, {"name": "handthing.auth.tenant.resolver.path.order", "type": "java.lang.Integer", "description": "URL路径解析器优先级", "defaultValue": 30}, {"name": "handthing.auth.saas.cache.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用缓存", "defaultValue": true}, {"name": "handthing.auth.saas.cache.cache-name", "type": "java.lang.String", "description": "缓存名称", "defaultValue": "tenant-config"}, {"name": "handthing.auth.saas.cache.ttl", "type": "java.time.Duration", "description": "缓存TTL（生存时间）", "defaultValue": "PT5M"}, {"name": "handthing.auth.saas.cache.max-size", "type": "java.lang.Long", "description": "最大缓存条目数", "defaultValue": 1000}, {"name": "handthing.auth.saas.filter.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用租户解析过滤器", "defaultValue": true}, {"name": "handthing.auth.saas.filter.order", "type": "java.lang.Integer", "description": "过滤器顺序", "defaultValue": -200}, {"name": "handthing.auth.saas.filter.exclude-paths", "type": "java.lang.String[]", "description": "排除路径列表，这些路径不会进行租户解析", "defaultValue": ["/actuator/**", "/error", "/favicon.ico", "/static/**", "/public/**"]}, {"name": "handthing.auth.saas.filter.include-paths", "type": "java.lang.String[]", "description": "包含路径列表，如果指定，只有这些路径会进行租户解析", "defaultValue": []}, {"name": "handthing.auth.saas.filter.log-resolution", "type": "java.lang.Bo<PERSON>an", "description": "是否记录租户解析日志", "defaultValue": true}, {"name": "handthing.auth.saas.validation.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用租户ID验证", "defaultValue": true}, {"name": "handthing.auth.saas.validation.min-length", "type": "java.lang.Integer", "description": "租户ID最小长度", "defaultValue": 1}, {"name": "handthing.auth.saas.validation.max-length", "type": "java.lang.Integer", "description": "租户ID最大长度", "defaultValue": 64}, {"name": "handthing.auth.saas.validation.pattern", "type": "java.lang.String", "description": "租户ID正则表达式", "defaultValue": "^[a-zA-Z0-9][a-zA-Z0-9_-]*[a-zA-Z0-9]$|^[a-zA-Z0-9]$"}, {"name": "handthing.auth.saas.validation.allow-empty", "type": "java.lang.Bo<PERSON>an", "description": "是否允许空租户ID", "defaultValue": false}, {"name": "handthing.auth.saas.validation.blacklist", "type": "java.lang.String[]", "description": "禁用的租户ID列表", "defaultValue": ["admin", "root", "system", "api", "www"]}, {"name": "handthing.auth.saas.monitoring.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用监控", "defaultValue": true}, {"name": "handthing.auth.saas.monitoring.enable-metrics", "type": "java.lang.Bo<PERSON>an", "description": "是否启用指标收集", "defaultValue": true}, {"name": "handthing.auth.saas.monitoring.enable-health-check", "type": "java.lang.Bo<PERSON>an", "description": "是否启用健康检查", "defaultValue": true}, {"name": "handthing.auth.saas.monitoring.enable-events", "type": "java.lang.Bo<PERSON>an", "description": "是否启用事件发布", "defaultValue": true}, {"name": "handthing.auth.saas.monitoring.retention-period", "type": "java.time.Duration", "description": "监控数据保留时间", "defaultValue": "P7D"}]}