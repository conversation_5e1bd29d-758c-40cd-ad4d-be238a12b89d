package cn.com.handthing.starter.tenant.datalayer.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashSet;
import java.util.Set;

/**
 * 租户数据层配置属性
 * <p>
 * 提供租户数据层相关的配置选项，包括租户字段配置、忽略表配置等。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
@ConfigurationProperties(prefix = "handthing.datalayer.tenant")
public class TenantDatalayerProperties {
    
    /**
     * 是否启用租户功能
     */
    private boolean enabled = true;
    
    /**
     * 租户ID字段名
     */
    private String tenantIdColumn = "tenant_id";
    
    /**
     * 默认租户ID
     */
    private String defaultTenantId = "default";
    
    /**
     * 是否启用严格模式
     * <p>
     * 严格模式下，租户相关的错误会抛出异常而不是记录日志
     * </p>
     */
    private boolean strictMode = false;
    
    /**
     * 是否验证租户一致性
     * <p>
     * 验证实体的租户ID是否与当前上下文的租户ID一致
     * </p>
     */
    private boolean validateConsistency = true;
    
    /**
     * 是否启用租户ID自动填充
     */
    private boolean autoFillEnabled = true;
    
    /**
     * 忽略租户处理的表名集合
     * <p>
     * 这些表不会自动添加租户条件
     * </p>
     */
    private Set<String> ignoreTables = new HashSet<>();
    
    /**
     * 忽略租户处理的表名前缀集合
     * <p>
     * 以这些前缀开头的表不会自动添加租户条件
     * </p>
     */
    private Set<String> ignoreTablePrefixes = new HashSet<>();
    
    /**
     * 是否忽略系统表
     * <p>
     * 如果为true，系统表（如用户表、角色表等）不会自动添加租户条件
     * </p>
     */
    private boolean ignoreSystemTables = true;
    
    /**
     * 租户隔离策略
     */
    private IsolationStrategy isolationStrategy = IsolationStrategy.COLUMN;
    
    /**
     * 租户数据源配置
     */
    private DataSource dataSource = new DataSource();
    
    /**
     * 租户隔离策略枚举
     */
    public enum IsolationStrategy {
        /**
         * 列级隔离（在同一表中通过租户ID字段隔离）
         */
        COLUMN,
        
        /**
         * 表级隔离（不同租户使用不同的表）
         */
        TABLE,
        
        /**
         * 数据库级隔离（不同租户使用不同的数据库）
         */
        DATABASE,
        
        /**
         * 混合隔离（根据配置使用不同的隔离策略）
         */
        HYBRID
    }
    
    /**
     * 租户数据源配置
     */
    @Data
    public static class DataSource {
        
        /**
         * 是否启用多数据源
         */
        private boolean multiDataSourceEnabled = false;
        
        /**
         * 数据源路由策略
         */
        private String routingStrategy = "tenant_id";
        
        /**
         * 默认数据源名称
         */
        private String defaultDataSource = "default";
        
        /**
         * 数据源前缀
         */
        private String dataSourcePrefix = "tenant_";
        
        /**
         * 是否启用数据源缓存
         */
        private boolean cacheEnabled = true;
        
        /**
         * 数据源缓存过期时间（秒）
         */
        private long cacheExpireSeconds = 3600;
    }
    
    /**
     * 初始化默认配置
     */
    public void initDefaults() {
        // 添加默认忽略表
        if (ignoreTables.isEmpty()) {
            ignoreTables.add("tenants");
            ignoreTables.add("users");
            ignoreTables.add("roles");
            ignoreTables.add("permissions");
            ignoreTables.add("menus");
            ignoreTables.add("configs");
            ignoreTables.add("dictionaries");
            ignoreTables.add("logs");
            ignoreTables.add("audit_logs");
        }
        
        // 添加默认忽略表前缀
        if (ignoreTablePrefixes.isEmpty()) {
            ignoreTablePrefixes.add("sys_");
            ignoreTablePrefixes.add("system_");
            ignoreTablePrefixes.add("config_");
            ignoreTablePrefixes.add("dict_");
            ignoreTablePrefixes.add("menu_");
            ignoreTablePrefixes.add("role_");
            ignoreTablePrefixes.add("permission_");
            ignoreTablePrefixes.add("tenant_");
            ignoreTablePrefixes.add("user_");
            ignoreTablePrefixes.add("auth_");
            ignoreTablePrefixes.add("log_");
            ignoreTablePrefixes.add("monitor_");
            ignoreTablePrefixes.add("schedule_");
        }
    }
    
    /**
     * 验证配置的有效性
     */
    public void validate() {
        if (enabled) {
            validateTenantConfig();
            validateDataSourceConfig();
        }
    }
    
    /**
     * 验证租户配置
     */
    private void validateTenantConfig() {
        if (tenantIdColumn == null || tenantIdColumn.trim().isEmpty()) {
            throw new IllegalArgumentException("Tenant ID column cannot be null or empty");
        }
        
        if (defaultTenantId == null || defaultTenantId.trim().isEmpty()) {
            throw new IllegalArgumentException("Default tenant ID cannot be null or empty");
        }
        
        if (isolationStrategy == null) {
            throw new IllegalArgumentException("Isolation strategy cannot be null");
        }
    }
    
    /**
     * 验证数据源配置
     */
    private void validateDataSourceConfig() {
        if (dataSource.multiDataSourceEnabled) {
            if (dataSource.routingStrategy == null || dataSource.routingStrategy.trim().isEmpty()) {
                throw new IllegalArgumentException("Routing strategy cannot be null or empty when multi-datasource is enabled");
            }
            
            if (dataSource.defaultDataSource == null || dataSource.defaultDataSource.trim().isEmpty()) {
                throw new IllegalArgumentException("Default datasource cannot be null or empty when multi-datasource is enabled");
            }
            
            if (dataSource.cacheExpireSeconds <= 0) {
                throw new IllegalArgumentException("Cache expire seconds must be positive");
            }
        }
    }
    
    /**
     * 检查表是否应该被忽略
     * 
     * @param tableName 表名
     * @return 如果应该被忽略返回true，否则返回false
     */
    public boolean shouldIgnoreTable(String tableName) {
        if (tableName == null || tableName.trim().isEmpty()) {
            return true;
        }
        
        String lowerTableName = tableName.toLowerCase();
        
        // 检查忽略表列表
        if (ignoreTables.contains(lowerTableName)) {
            return true;
        }
        
        // 检查忽略表前缀
        for (String prefix : ignoreTablePrefixes) {
            if (lowerTableName.startsWith(prefix.toLowerCase())) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 添加忽略表
     * 
     * @param tableName 表名
     */
    public void addIgnoreTable(String tableName) {
        if (tableName != null && !tableName.trim().isEmpty()) {
            ignoreTables.add(tableName.toLowerCase());
        }
    }
    
    /**
     * 添加忽略表前缀
     * 
     * @param prefix 表前缀
     */
    public void addIgnoreTablePrefix(String prefix) {
        if (prefix != null && !prefix.trim().isEmpty()) {
            ignoreTablePrefixes.add(prefix.toLowerCase());
        }
    }
    
    /**
     * 移除忽略表
     * 
     * @param tableName 表名
     */
    public void removeIgnoreTable(String tableName) {
        if (tableName != null) {
            ignoreTables.remove(tableName.toLowerCase());
        }
    }
    
    /**
     * 移除忽略表前缀
     * 
     * @param prefix 表前缀
     */
    public void removeIgnoreTablePrefix(String prefix) {
        if (prefix != null) {
            ignoreTablePrefixes.remove(prefix.toLowerCase());
        }
    }
    
    /**
     * 获取配置摘要
     *
     * @return 配置摘要字符串
     */
    public String getConfigSummary() {
        StringBuilder sb = new StringBuilder();
        sb.append("TenantDatalayerProperties{");
        sb.append("enabled=").append(enabled);
        
        if (enabled) {
            sb.append(", tenantIdColumn='").append(tenantIdColumn).append("'");
            sb.append(", defaultTenantId='").append(defaultTenantId).append("'");
            sb.append(", strictMode=").append(strictMode);
            sb.append(", validateConsistency=").append(validateConsistency);
            sb.append(", autoFillEnabled=").append(autoFillEnabled);
            sb.append(", isolationStrategy=").append(isolationStrategy);
            sb.append(", ignoreTables=").append(ignoreTables.size());
            sb.append(", ignoreTablePrefixes=").append(ignoreTablePrefixes.size());
            sb.append(", ignoreSystemTables=").append(ignoreSystemTables);
            
            if (dataSource.multiDataSourceEnabled) {
                sb.append(", multiDataSource={enabled=").append(dataSource.multiDataSourceEnabled);
                sb.append(", routingStrategy='").append(dataSource.routingStrategy).append("'");
                sb.append(", defaultDataSource='").append(dataSource.defaultDataSource).append("'");
                sb.append(", cacheEnabled=").append(dataSource.cacheEnabled);
                sb.append("}");
            }
        }
        
        sb.append("}");
        return sb.toString();
    }
}
