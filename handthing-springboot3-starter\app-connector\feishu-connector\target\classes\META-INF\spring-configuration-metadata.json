{"groups": [{"name": "handthing.connector.feishu", "type": "cn.com.handthing.starter.connector.feishu.config.FeishuConfig", "sourceType": "cn.com.handthing.starter.connector.feishu.config.FeishuConfig"}], "properties": [{"name": "handthing.connector.feishu.api-base-url", "type": "java.lang.String", "description": "飞书API基础URL", "sourceType": "cn.com.handthing.starter.connector.feishu.config.FeishuConfig"}, {"name": "handthing.connector.feishu.app-id", "type": "java.lang.String", "description": "飞书应用ID (App ID)", "sourceType": "cn.com.handthing.starter.connector.feishu.config.FeishuConfig"}, {"name": "handthing.connector.feishu.app-secret", "type": "java.lang.String", "description": "飞书应用密钥 (App Secret)", "sourceType": "cn.com.handthing.starter.connector.feishu.config.FeishuConfig"}, {"name": "handthing.connector.feishu.app-type", "type": "java.lang.String", "description": "应用类型 internal: 企业内部应用 third_party: 第三方应用", "sourceType": "cn.com.handthing.starter.connector.feishu.config.FeishuConfig"}, {"name": "handthing.connector.feishu.auth-url", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.feishu.config.FeishuConfig"}, {"name": "handthing.connector.feishu.bot-enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用机器人功能", "sourceType": "cn.com.handthing.starter.connector.feishu.config.FeishuConfig"}, {"name": "handthing.connector.feishu.bot-secret", "type": "java.lang.String", "description": "机器人密钥 (Bot Secret)", "sourceType": "cn.com.handthing.starter.connector.feishu.config.FeishuConfig"}, {"name": "handthing.connector.feishu.client-id", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.feishu.config.FeishuConfig"}, {"name": "handthing.connector.feishu.client-secret", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.feishu.config.FeishuConfig"}, {"name": "handthing.connector.feishu.connect-timeout", "type": "java.lang.Integer", "sourceType": "cn.com.handthing.starter.connector.feishu.config.FeishuConfig"}, {"name": "handthing.connector.feishu.enabled", "type": "java.lang.Bo<PERSON>an", "sourceType": "cn.com.handthing.starter.connector.feishu.config.FeishuConfig"}, {"name": "handthing.connector.feishu.encrypt-key", "type": "java.lang.String", "description": "消息加密密钥 (Encrypt Key)", "sourceType": "cn.com.handthing.starter.connector.feishu.config.FeishuConfig"}, {"name": "handthing.connector.feishu.encrypt-message", "type": "java.lang.Bo<PERSON>an", "description": "是否启用消息加密", "sourceType": "cn.com.handthing.starter.connector.feishu.config.FeishuConfig"}, {"name": "handthing.connector.feishu.enterprise-id", "type": "java.lang.String", "description": "飞书企业ID (Enterprise ID)", "sourceType": "cn.com.handthing.starter.connector.feishu.config.FeishuConfig"}, {"name": "handthing.connector.feishu.extra-config", "type": "java.util.Map<java.lang.String,java.lang.Object>", "sourceType": "cn.com.handthing.starter.connector.feishu.config.FeishuConfig"}, {"name": "handthing.connector.feishu.lark-api-base-url", "type": "java.lang.String", "description": "海外版飞书API基础URL", "sourceType": "cn.com.handthing.starter.connector.feishu.config.FeishuConfig"}, {"name": "handthing.connector.feishu.lark-mode", "type": "java.lang.Bo<PERSON>an", "description": "是否使用海外版飞书 (Lark)", "sourceType": "cn.com.handthing.starter.connector.feishu.config.FeishuConfig"}, {"name": "handthing.connector.feishu.max-retries", "type": "java.lang.Integer", "sourceType": "cn.com.handthing.starter.connector.feishu.config.FeishuConfig"}, {"name": "handthing.connector.feishu.read-timeout", "type": "java.lang.Integer", "sourceType": "cn.com.handthing.starter.connector.feishu.config.FeishuConfig"}, {"name": "handthing.connector.feishu.redirect-uri", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.feishu.config.FeishuConfig"}, {"name": "handthing.connector.feishu.sandbox", "type": "java.lang.Bo<PERSON>an", "sourceType": "cn.com.handthing.starter.connector.feishu.config.FeishuConfig"}, {"name": "handthing.connector.feishu.scope", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.feishu.config.FeishuConfig"}, {"name": "handthing.connector.feishu.third-party-mode", "type": "java.lang.Bo<PERSON>an", "description": "是否为第三方应用模式", "sourceType": "cn.com.handthing.starter.connector.feishu.config.FeishuConfig"}, {"name": "handthing.connector.feishu.token-url", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.feishu.config.FeishuConfig"}, {"name": "handthing.connector.feishu.user-info-url", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.feishu.config.FeishuConfig"}, {"name": "handthing.connector.feishu.verification-token", "type": "java.lang.String", "description": "消息验证Token (Verification Token)", "sourceType": "cn.com.handthing.starter.connector.feishu.config.FeishuConfig"}], "hints": [], "ignored": {"properties": []}}