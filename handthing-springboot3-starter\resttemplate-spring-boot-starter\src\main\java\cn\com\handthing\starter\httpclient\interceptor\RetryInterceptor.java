package cn.com.handthing.starter.httpclient.interceptor;

import cn.com.handthing.starter.httpclient.config.RetryProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.retry.RetryCallback;
import org.springframework.retry.RetryContext;
import org.springframework.retry.backoff.ExponentialBackOffPolicy;
import org.springframework.retry.policy.SimpleRetryPolicy;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.ResourceAccessException;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * RestTemplate 重试拦截器
 * 
 * <AUTHOR>
 * @since V1.0.0
 */
public class RetryInterceptor implements ClientHttpRequestInterceptor {

    private static final Logger log = LoggerFactory.getLogger(RetryInterceptor.class);
    
    private final RetryProperties retryProperties;
    private final RetryTemplate retryTemplate;

    public RetryInterceptor(RetryProperties retryProperties) {
        this.retryProperties = retryProperties;
        this.retryTemplate = createRetryTemplate();
    }

    @Override
    public ClientHttpResponse intercept(
            HttpRequest request, 
            byte[] body, 
            ClientHttpRequestExecution execution) throws IOException {
        
        try {
            return retryTemplate.execute(new RetryCallback<ClientHttpResponse, IOException>() {
                @Override
                public ClientHttpResponse doWithRetry(RetryContext context) throws IOException {
                    if (context.getRetryCount() > 0) {
                        log.debug("Retrying {} {} (attempt {})", 
                                request.getMethod(), request.getURI(), context.getRetryCount() + 1);
                    }
                    return execution.execute(request, body);
                }
            });
        } catch (IOException e) {
            throw e;
        } catch (Exception e) {
            if (e instanceof RuntimeException) {
                throw (RuntimeException) e;
            } else {
                throw new IOException("Request failed after retries", e);
            }
        }
    }

    private RetryTemplate createRetryTemplate() {
        RetryTemplate template = new RetryTemplate();

        // 设置重试策略
        SimpleRetryPolicy retryPolicy = new SimpleRetryPolicy();
        retryPolicy.setMaxAttempts(retryProperties.getMaxAttempts());

        // 设置可重试的异常
        Map<Class<? extends Throwable>, Boolean> retryableExceptions = new HashMap<>();
        retryableExceptions.put(ResourceAccessException.class, true);
        retryableExceptions.put(HttpServerErrorException.class, true);
        retryableExceptions.put(IOException.class, true);

        // 使用反射设置可重试异常，因为不同版本的 Spring Retry 方法可能不同
        try {
            retryPolicy.getClass().getMethod("setRetryableExceptions", Map.class).invoke(retryPolicy, retryableExceptions);
        } catch (Exception e) {
            log.warn("Failed to set retryable exceptions, using default policy", e);
        }
        template.setRetryPolicy(retryPolicy);

        // 设置退避策略
        ExponentialBackOffPolicy backOffPolicy = new ExponentialBackOffPolicy();
        backOffPolicy.setInitialInterval(retryProperties.getDelay().toMillis());
        backOffPolicy.setMultiplier(2.0);
        backOffPolicy.setMaxInterval(retryProperties.getMaxDelay().toMillis());
        template.setBackOffPolicy(backOffPolicy);

        return template;
    }
}
