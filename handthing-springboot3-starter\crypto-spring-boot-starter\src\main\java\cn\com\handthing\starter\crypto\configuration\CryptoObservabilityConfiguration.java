package cn.com.handthing.starter.crypto.configuration;

import cn.com.handthing.starter.crypto.core.CryptoService;
import org.springframework.boot.actuate.autoconfigure.health.ConditionalOnEnabledHealthIndicator;
import org.springframework.boot.actuate.health.CompositeHealthContributor;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * Crypto Starter 的可观测性配置。
 * <p>
 * 负责配置与 Spring Boot Actuator 集成的健康检查端点。
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Configuration(proxyBeanMethods = false)
@ConditionalOnClass(HealthIndicator.class)
@ConditionalOnProperty(prefix = "handthing.crypto.observability", name = "health-check-enabled", havingValue = "true", matchIfMissing = true)
public class CryptoObservabilityConfiguration {

    /**
     * 创建一个组合式的健康检查贡献者。
     * <p>
     * 它会自动发现所有已注册的 {@link CryptoService} Bean，并为每一个创建一个独立的健康指示器。
     * 最终在 /actuator/health/crypto 端点下，会展示每个处理器的健康状态。
     *
     * @param cryptoServices Spring 自动注入的所有 CryptoService 实例
     * @return 一个名为 "crypto" 的组合式健康贡献者
     */
    @Bean
    @ConditionalOnEnabledHealthIndicator("crypto")
    public CompositeHealthContributor cryptoHealthContributor(Map<String, CryptoService> cryptoServices) {
        Map<String, HealthIndicator> indicators = new LinkedHashMap<>();
        cryptoServices.forEach((name, service) ->
                indicators.put(name, () -> {
                    try {
                        service.selfCheck();
                        return Health.up().withDetail("message", "Processor is properly configured and operational.").build();
                    } catch (Exception e) {
                        return Health.down(e).withDetail("message", "Processor self-check failed.").build();
                    }
                })
        );
        return CompositeHealthContributor.fromMap(indicators);
    }
}