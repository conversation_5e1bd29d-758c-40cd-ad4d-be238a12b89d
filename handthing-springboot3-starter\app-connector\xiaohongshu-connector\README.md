# 小红书连接器 (XiaoHongShu Connector)

## 概述

小红书连接器提供了与小红书开放平台的集成能力，支持个人开发者、企业开发者和品牌方的生活方式内容分享和社区互动。通过统一的API接口，可以轻松实现用户认证、笔记发布、社区互动、数据分析等功能。

## 特性

- ✅ **OAuth2.0认证** - 支持小红书用户授权登录
- ✅ **笔记发布** - 支持图片笔记、视频笔记发布和管理
- ✅ **社区互动** - 支持关注管理、评论互动、数据统计
- ✅ **内容管理** - 支持笔记编辑、删除、搜索等操作
- ✅ **用户管理** - 获取粉丝列表、关注列表、用户画像
- ✅ **多开发者类型** - 支持个人、企业、品牌方不同模式
- ✅ **沙箱支持** - 完整的沙箱环境测试支持

## 快速开始

### 1. 添加依赖

```xml
<dependency>
    <groupId>cn.com.handthing.springboot3.starter</groupId>
    <artifactId>xiaohongshu-connector</artifactId>
    <version>1.0.0-SNAPSHOT</version>
</dependency>
```

### 2. 配置文件

```yaml
handthing:
  connector:
    xiaohongshu:
      enabled: true
      app-key: "your-app-key"
      app-secret: "your-app-secret"
      app-type: "personal"  # personal/enterprise/brand
      brand-mode: false
      sandbox-mode: true
      content-publish-enabled: true
      community-enabled: true
      analytics-enabled: true
      ecommerce-enabled: false
      scope: "user_info,content.write,community.read,analytics.read"
      redirect-uri: "http://your-domain.com/callback/xiaohongshu"
```

### 3. 基本使用

```java
@RestController
public class XiaoHongShuController {
    
    @Autowired
    private XiaoHongShuService xiaoHongShuService;
    
    @Autowired
    private AuthService authService;
    
    // 获取授权URL
    @GetMapping("/xiaohongshu/auth")
    public String getAuthUrl() {
        return authService.getAuthorizationUrl(
            PlatformType.XIAOHONGSHU, 
            "state", 
            "http://your-domain.com/callback/xiaohongshu"
        );
    }
    
    // 发布图片笔记
    @PostMapping("/xiaohongshu/note")
    public Map<String, Object> publishNote(@RequestParam String accessToken,
                                          @RequestParam String title,
                                          @RequestParam String content,
                                          @RequestParam List<String> images,
                                          @RequestParam List<String> tags) {
        return xiaoHongShuService.content().publishNote(accessToken, title, content, images, tags);
    }
    
    // 发布视频笔记
    @PostMapping("/xiaohongshu/video")
    public Map<String, Object> publishVideo(@RequestParam String accessToken,
                                           @RequestParam String title,
                                           @RequestParam String description,
                                           @RequestParam String videoUrl,
                                           @RequestParam String coverImage,
                                           @RequestParam List<String> tags) {
        return xiaoHongShuService.content().publishVideo(accessToken, title, description, videoUrl, coverImage, tags);
    }
    
    // 获取粉丝列表
    @GetMapping("/xiaohongshu/followers")
    public Map<String, Object> getFollowers(@RequestParam String accessToken,
                                           @RequestParam(defaultValue = "1") Integer page,
                                           @RequestParam(defaultValue = "20") Integer pageSize) {
        return xiaoHongShuService.community().getFollowersList(accessToken, page, pageSize);
    }
    
    // 获取笔记统计
    @GetMapping("/xiaohongshu/note/{noteId}/stats")
    public Map<String, Object> getNoteStats(@RequestParam String accessToken,
                                           @PathVariable String noteId) {
        return xiaoHongShuService.community().getNoteStats(accessToken, noteId);
    }
}
```

## 配置说明

| 配置项 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| `enabled` | Boolean | 是 | false | 是否启用小红书连接器 |
| `app-key` | String | 是 | - | 应用AppKey |
| `app-secret` | String | 是 | - | 应用AppSecret |
| `app-type` | String | 否 | personal | 应用类型(personal/enterprise/brand) |
| `brand-mode` | Boolean | 否 | false | 是否为品牌方模式 |
| `sandbox-mode` | Boolean | 否 | false | 是否使用沙箱环境 |
| `content-publish-enabled` | Boolean | 否 | true | 是否启用内容发布功能 |
| `community-enabled` | Boolean | 否 | true | 是否启用社区互动功能 |
| `analytics-enabled` | Boolean | 否 | true | 是否启用数据分析功能 |
| `ecommerce-enabled` | Boolean | 否 | false | 是否启用电商功能 |
| `scope` | String | 否 | user_info,content.write,community.read,analytics.read | 授权范围 |
| `redirect-uri` | String | 否 | - | OAuth回调地址 |

## API文档

### 内容API (XiaoHongShuContentApi)

#### 发布笔记
```java
Map<String, Object> publishNote(String accessToken, String title, String content, List<String> images, List<String> tags)
```

#### 上传图片
```java
Map<String, Object> uploadImage(String accessToken, String imageData, String imageName)
```

#### 发布视频笔记
```java
Map<String, Object> publishVideo(String accessToken, String title, String description, String videoUrl, String coverImage, List<String> tags)
```

#### 获取笔记列表
```java
Map<String, Object> getNoteList(String accessToken, Integer page, Integer pageSize, String noteType)
```

#### 删除笔记
```java
Map<String, Object> deleteNote(String accessToken, String noteId)
```

#### 获取笔记详情
```java
Map<String, Object> getNoteDetail(String accessToken, String noteId)
```

### 社区API (XiaoHongShuCommunityApi)

#### 获取粉丝列表
```java
Map<String, Object> getFollowersList(String accessToken, Integer page, Integer pageSize)
```

#### 获取关注列表
```java
Map<String, Object> getFollowingList(String accessToken, Integer page, Integer pageSize)
```

#### 获取笔记评论
```java
Map<String, Object> getNoteComments(String accessToken, String noteId, Integer page, Integer pageSize)
```

#### 回复评论
```java
Map<String, Object> replyComment(String accessToken, String commentId, String content)
```

#### 获取笔记数据统计
```java
Map<String, Object> getNoteStats(String accessToken, String noteId)
```

#### 获取用户数据统计
```java
Map<String, Object> getUserStats(String accessToken)
```

#### 搜索笔记
```java
Map<String, Object> searchNotes(String accessToken, String keyword, Integer page, Integer pageSize)
```

## 常见问题

### Q: 如何获取小红书的AppKey和AppSecret？
A: 登录小红书开放平台，在"应用管理"中创建应用，即可获取AppKey和AppSecret。

### Q: 个人开发者、企业开发者、品牌方有什么区别？
A: 个人开发者适用于个人创作者，企业开发者适用于企业应用，品牌方有更多营销和电商功能。

### Q: 如何获取内容发布权限？
A: 需要在开放平台申请相应的权限，通过审核后才能发布内容。

### Q: 笔记发布有什么限制？
A: 图片数量不超过9张，视频时长不超过5分钟，内容需要符合社区规范。

## 更多信息

- [小红书开放平台文档](https://open.xiaohongshu.com/)
- [内容发布API](https://open.xiaohongshu.com/docs/content)
- [社区互动API](https://open.xiaohongshu.com/docs/community)
