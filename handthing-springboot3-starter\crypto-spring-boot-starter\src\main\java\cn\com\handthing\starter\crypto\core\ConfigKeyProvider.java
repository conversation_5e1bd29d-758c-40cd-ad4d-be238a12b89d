package cn.com.handthing.starter.crypto.core;

import cn.com.handthing.starter.crypto.exception.CryptoException;
import cn.com.handthing.starter.crypto.properties.CryptoProperties.KeyStoreConfig;
import org.springframework.util.Assert;
import java.security.Key;
import java.util.Base64;
import javax.crypto.spec.SecretKeySpec;

/**
 * 从配置文件 (application.yml) 加载密钥的 KeyProvider。
 *
 * <AUTHOR>
 * @since V1.0.0
 */
class ConfigKeyProvider extends AbstractKeyProvider {

    private final byte[] keyBytes;

    public ConfigKeyProvider(KeyStoreConfig config) {
        super(config);
        Assert.hasText(config.getKeyValue(), "key-value must not be empty for KeyStoreType.CONFIG");
        try {
            this.keyBytes = Base64.getDecoder().decode(config.getKeyValue());
            this.ready = true;
        } catch (IllegalArgumentException e) {
            throw new CryptoException("Invalid Base64 encoded key in 'key-value'", e);
        }
    }

    @Override
    public Key getKey() throws Exception {
        return new SecretKeySpec(this.keyBytes, "RAW");
    }

    @Override
    public byte[] getKeyBytes() {
        return this.keyBytes;
    }
}