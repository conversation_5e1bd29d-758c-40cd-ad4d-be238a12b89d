cn\com\handthing\starter\connector\auth\UnifiedUserInfo.class
cn\com\handthing\starter\connector\auth\UnifiedUserInfo$UnifiedUserInfoBuilder.class
cn\com\handthing\starter\connector\exception\ApiCallException.class
cn\com\handthing\starter\connector\config\PlatformConfig.class
cn\com\handthing\starter\connector\PlatformType.class
cn\com\handthing\starter\connector\auth\UnifiedAccessToken$UnifiedAccessTokenBuilder.class
cn\com\handthing\starter\connector\auth\AuthService.class
cn\com\handthing\starter\connector\auth\UnifiedAccessToken.class
cn\com\handthing\starter\connector\exception\ConnectorException.class
cn\com\handthing\starter\connector\auth\AuthProvider.class
cn\com\handthing\starter\connector\event\UserAuthorizedEvent.class
cn\com\handthing\starter\connector\event\ConnectorEvent.class
cn\com\handthing\starter\connector\exception\AuthException.class
