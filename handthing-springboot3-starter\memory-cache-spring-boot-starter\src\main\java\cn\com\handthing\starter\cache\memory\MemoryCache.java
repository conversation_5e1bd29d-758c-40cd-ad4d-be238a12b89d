package cn.com.handthing.starter.cache.memory;

import cn.com.handthing.starter.cache.CacheStats;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.cache.support.SimpleValueWrapper;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.concurrent.Callable;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 基于ConcurrentHashMap的内存缓存实现
 * <p>
 * 支持TTL过期、统计信息收集等功能
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
public class MemoryCache implements Cache {

    private final String name;
    private final long maxSize;
    private final Duration ttl;
    private final boolean enableStats;
    private final ConcurrentMap<Object, CacheEntry> store;

    // 统计信息
    private final AtomicLong hitCount = new AtomicLong(0);
    private final AtomicLong missCount = new AtomicLong(0);
    private final AtomicLong evictionCount = new AtomicLong(0);

    /**
     * 构造函数
     *
     * @param name        缓存名称
     * @param maxSize     最大缓存条目数
     * @param ttl         过期时间
     * @param enableStats 是否启用统计信息
     */
    public MemoryCache(String name, long maxSize, Duration ttl, boolean enableStats) {
        this.name = name;
        this.maxSize = maxSize;
        this.ttl = ttl;
        this.enableStats = enableStats;
        this.store = new ConcurrentHashMap<>();
    }

    @Override
    public String getName() {
        return name;
    }

    @Override
    public Object getNativeCache() {
        return store;
    }

    @Override
    public ValueWrapper get(Object key) {
        CacheEntry entry = store.get(key);
        if (entry == null) {
            if (enableStats) {
                missCount.incrementAndGet();
            }
            return null;
        }

        if (isExpired(entry)) {
            store.remove(key);
            if (enableStats) {
                missCount.incrementAndGet();
                evictionCount.incrementAndGet();
            }
            return null;
        }

        if (enableStats) {
            hitCount.incrementAndGet();
        }
        return new SimpleValueWrapper(entry.getValue());
    }

    @Override
    public <T> T get(Object key, Class<T> type) {
        ValueWrapper wrapper = get(key);
        if (wrapper == null) {
            return null;
        }
        Object value = wrapper.get();
        if (value != null && type != null && !type.isInstance(value)) {
            throw new IllegalStateException("Cached value is not of required type [" + type.getName() + "]: " + value);
        }
        return (T) value;
    }

    @Override
    public <T> T get(Object key, Callable<T> valueLoader) {
        ValueWrapper wrapper = get(key);
        if (wrapper != null) {
            return (T) wrapper.get();
        }

        try {
            T value = valueLoader.call();
            put(key, value);
            return value;
        } catch (Exception e) {
            throw new ValueRetrievalException(key, valueLoader, e);
        }
    }

    @Override
    public void put(Object key, Object value) {
        // 检查缓存大小限制
        if (store.size() >= maxSize && !store.containsKey(key)) {
            // 简单的LRU策略：移除一个随机条目
            Object firstKey = store.keySet().iterator().next();
            if (firstKey != null) {
                store.remove(firstKey);
                if (enableStats) {
                    evictionCount.incrementAndGet();
                }
            }
        }

        CacheEntry entry = new CacheEntry(value, LocalDateTime.now().plus(ttl));
        store.put(key, entry);
        log.debug("Put cache entry for key: {} in cache: {}", key, name);
    }

    @Override
    public ValueWrapper putIfAbsent(Object key, Object value) {
        CacheEntry existingEntry = store.get(key);
        if (existingEntry != null && !isExpired(existingEntry)) {
            if (enableStats) {
                hitCount.incrementAndGet();
            }
            return new SimpleValueWrapper(existingEntry.getValue());
        }

        put(key, value);
        return null;
    }

    @Override
    public void evict(Object key) {
        CacheEntry removed = store.remove(key);
        if (removed != null) {
            if (enableStats) {
                evictionCount.incrementAndGet();
            }
            log.debug("Evicted cache entry for key: {} from cache: {}", key, name);
        }
    }

    @Override
    public boolean evictIfPresent(Object key) {
        CacheEntry removed = store.remove(key);
        if (removed != null) {
            if (enableStats) {
                evictionCount.incrementAndGet();
            }
            log.debug("Evicted cache entry for key: {} from cache: {}", key, name);
            return true;
        }
        return false;
    }

    @Override
    public void clear() {
        int size = store.size();
        store.clear();
        if (enableStats) {
            evictionCount.addAndGet(size);
        }
        log.debug("Cleared cache: {}", name);
    }

    /**
     * 清理过期的缓存条目
     */
    public void cleanupExpired() {
        LocalDateTime now = LocalDateTime.now();
        int removedCount = 0;
        
        for (ConcurrentMap.Entry<Object, CacheEntry> entry : store.entrySet()) {
            if (entry.getValue().getExpireTime().isBefore(now)) {
                if (store.remove(entry.getKey(), entry.getValue())) {
                    removedCount++;
                }
            }
        }
        
        if (removedCount > 0) {
            if (enableStats) {
                evictionCount.addAndGet(removedCount);
            }
            log.debug("Cleaned up {} expired entries from cache: {}", removedCount, name);
        }
    }

    /**
     * 获取缓存统计信息
     *
     * @return 缓存统计信息
     */
    public CacheStats getStats() {
        if (!enableStats) {
            return null;
        }

        long hits = hitCount.get();
        long misses = missCount.get();
        long requests = hits + misses;

        return CacheStats.builder()
                .hitCount(hits)
                .missCount(misses)
                .requestCount(requests)
                .hitRate(requests > 0 ? (double) hits / requests : 0.0)
                .missRate(requests > 0 ? (double) misses / requests : 0.0)
                .size(store.size())
                .evictionCount(evictionCount.get())
                .averageLoadTime(0.0) // 内存缓存加载时间忽略不计
                .build();
    }

    /**
     * 检查缓存条目是否过期
     *
     * @param entry 缓存条目
     * @return 是否过期
     */
    private boolean isExpired(CacheEntry entry) {
        return entry.getExpireTime().isBefore(LocalDateTime.now());
    }

    /**
     * 缓存条目内部类
     */
    private static class CacheEntry {
        private final Object value;
        private final LocalDateTime expireTime;

        public CacheEntry(Object value, LocalDateTime expireTime) {
            this.value = value;
            this.expireTime = expireTime;
        }

        public Object getValue() {
            return value;
        }

        public LocalDateTime getExpireTime() {
            return expireTime;
        }
    }
}