# 钉钉连接器 (DingTalk Connector)

## 概述

钉钉连接器提供了与钉钉开放平台的集成能力，支持企业内部应用和第三方应用的开发。通过统一的API接口，可以轻松实现用户认证、消息发送、审批流程、考勤管理等功能。

## 特性

- ✅ **OAuth2.0认证** - 支持钉钉用户授权登录
- ✅ **消息发送** - 支持工作通知、群消息、卡片消息等
- ✅ **组织架构** - 获取部门信息、用户信息、组织关系
- ✅ **审批流程** - 创建和管理审批流程
- ✅ **考勤管理** - 获取考勤数据和统计信息
- ✅ **机器人支持** - 支持群机器人消息推送
- ✅ **多环境支持** - 支持开发、测试、生产环境配置

## 快速开始

### 1. 添加依赖

```xml
<dependency>
    <groupId>cn.com.handthing.springboot3.starter</groupId>
    <artifactId>dingtalk-connector</artifactId>
    <version>1.0.0-SNAPSHOT</version>
</dependency>
```

### 2. 配置文件

```yaml
handthing:
  connector:
    dingtalk:
      enabled: true
      app-key: "your-app-key"
      app-secret: "your-app-secret"
      corp-id: "your-corp-id"
      third-party-mode: false
      robot-enabled: true
      robot-webhook: "your-robot-webhook-url"
      robot-secret: "your-robot-secret"
      redirect-uri: "http://your-domain.com/callback/dingtalk"
```

### 3. 基本使用

```java
@RestController
public class DingTalkController {
    
    @Autowired
    private DingTalkService dingTalkService;
    
    @Autowired
    private AuthService authService;
    
    // 获取授权URL
    @GetMapping("/dingtalk/auth")
    public String getAuthUrl() {
        return authService.getAuthorizationUrl(
            PlatformType.DINGTALK, 
            "state", 
            "http://your-domain.com/callback/dingtalk"
        );
    }
    
    // 发送工作通知
    @PostMapping("/dingtalk/notification")
    public Map<String, Object> sendNotification(@RequestParam String accessToken,
                                               @RequestParam String userId,
                                               @RequestParam String content) {
        return dingTalkService.message().sendWorkNotification(accessToken, userId, content);
    }
    
    // 获取用户信息
    @GetMapping("/dingtalk/user/{userId}")
    public Map<String, Object> getUserInfo(@RequestParam String accessToken,
                                          @PathVariable String userId) {
        return dingTalkService.user().getUserInfo(accessToken, userId);
    }
    
    // 发起审批
    @PostMapping("/dingtalk/approval")
    public Map<String, Object> createApproval(@RequestParam String accessToken,
                                             @RequestBody Map<String, Object> approvalData) {
        return dingTalkService.approval().createApproval(accessToken, approvalData);
    }
}
```

## 配置说明

| 配置项 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| `enabled` | Boolean | 是 | false | 是否启用钉钉连接器 |
| `app-key` | String | 是 | - | 应用的AppKey |
| `app-secret` | String | 是 | - | 应用的AppSecret |
| `corp-id` | String | 是 | - | 企业CorpId |
| `third-party-mode` | Boolean | 否 | false | 是否为第三方应用模式 |
| `robot-enabled` | Boolean | 否 | false | 是否启用机器人功能 |
| `robot-webhook` | String | 否 | - | 机器人Webhook地址 |
| `robot-secret` | String | 否 | - | 机器人密钥 |
| `redirect-uri` | String | 否 | - | OAuth回调地址 |

## API文档

### 消息API (DingTalkMessageApi)

#### 发送工作通知
```java
Map<String, Object> sendWorkNotification(String accessToken, String userId, String content)
```

#### 发送群消息
```java
Map<String, Object> sendGroupMessage(String accessToken, String chatId, String content)
```

#### 发送卡片消息
```java
Map<String, Object> sendCardMessage(String accessToken, String userId, Map<String, Object> cardContent)
```

### 用户API (DingTalkUserApi)

#### 获取用户信息
```java
Map<String, Object> getUserInfo(String accessToken, String userId)
```

#### 获取用户列表
```java
Map<String, Object> getUserList(String accessToken, String departmentId, Integer offset, Integer size)
```

#### 获取部门列表
```java
Map<String, Object> getDepartmentList(String accessToken, String departmentId)
```

### 审批API (DingTalkApprovalApi)

#### 创建审批实例
```java
Map<String, Object> createApproval(String accessToken, Map<String, Object> approvalData)
```

#### 获取审批实例详情
```java
Map<String, Object> getApprovalDetail(String accessToken, String processInstanceId)
```

#### 获取审批实例列表
```java
Map<String, Object> getApprovalList(String accessToken, String processCode, Integer offset, Integer size)
```

## 常见问题

### Q: 如何获取钉钉的AppKey和AppSecret？
A: 登录钉钉开发者后台，在"应用开发" -> "企业内部开发"中创建应用，即可获取AppKey和AppSecret。

### Q: 什么是CorpId？
A: CorpId是企业的唯一标识，可以在钉钉管理后台的"企业信息"中查看。

### Q: 如何使用机器人功能？
A: 在群聊中添加自定义机器人，获取Webhook地址和密钥，然后配置到robot-webhook和robot-secret中。

### Q: 审批流程如何创建？
A: 需要先在钉钉管理后台创建审批模板，获取processCode，然后通过API创建审批实例。

## 更多信息

- [钉钉开放平台文档](https://open.dingtalk.com/)
- [服务端API文档](https://open.dingtalk.com/document/orgapp-server/api-overview)
- [审批API文档](https://open.dingtalk.com/document/orgapp-server/approval-overview)
