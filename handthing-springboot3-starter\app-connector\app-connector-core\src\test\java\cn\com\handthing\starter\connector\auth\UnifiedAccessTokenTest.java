package cn.com.handthing.starter.connector.auth;

import cn.com.handthing.starter.connector.PlatformType;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * UnifiedAccessToken 单元测试
 *
 * <AUTHOR>
 * @since V1.0.0
 */
class UnifiedAccessTokenTest {

    @Test
    void testCreate() {
        String accessToken = "test_access_token";
        Long expiresIn = 7200L;
        PlatformType platform = PlatformType.WECOM;
        
        UnifiedAccessToken token = UnifiedAccessToken.create(accessToken, expiresIn, platform);
        
        assertEquals(accessToken, token.getAccessToken());
        assertEquals(expiresIn, token.getExpiresIn());
        assertEquals(platform, token.getPlatform());
        assertEquals("Bearer", token.getTokenType());
        assertNotNull(token.getCreatedAt());
        assertNotNull(token.getExpiresAt());
        
        // 验证过期时间计算
        LocalDateTime expectedExpiresAt = token.getCreatedAt().plusSeconds(expiresIn);
        assertEquals(expectedExpiresAt, token.getExpiresAt());
    }

    @Test
    void testCreateWithoutExpiration() {
        String accessToken = "test_access_token";
        PlatformType platform = PlatformType.WECOM;
        
        UnifiedAccessToken token = UnifiedAccessToken.create(accessToken, null, platform);
        
        assertEquals(accessToken, token.getAccessToken());
        assertNull(token.getExpiresIn());
        assertEquals(platform, token.getPlatform());
        assertNotNull(token.getCreatedAt());
        assertNull(token.getExpiresAt());
    }

    @Test
    void testIsExpired() {
        // 测试未过期的令牌
        UnifiedAccessToken validToken = UnifiedAccessToken.builder()
                .accessToken("test_token")
                .expiresAt(LocalDateTime.now().plusHours(1))
                .build();
        assertFalse(validToken.isExpired());
        
        // 测试已过期的令牌
        UnifiedAccessToken expiredToken = UnifiedAccessToken.builder()
                .accessToken("test_token")
                .expiresAt(LocalDateTime.now().minusHours(1))
                .build();
        assertTrue(expiredToken.isExpired());
        
        // 测试没有过期时间的令牌
        UnifiedAccessToken noExpirationToken = UnifiedAccessToken.builder()
                .accessToken("test_token")
                .build();
        assertFalse(noExpirationToken.isExpired());
    }

    @Test
    void testIsExpiringSoon() {
        // 测试即将过期的令牌（默认5分钟）
        UnifiedAccessToken soonExpiredToken = UnifiedAccessToken.builder()
                .accessToken("test_token")
                .expiresAt(LocalDateTime.now().plusMinutes(3))
                .build();
        assertTrue(soonExpiredToken.isExpiringSoon());
        
        // 测试不会很快过期的令牌
        UnifiedAccessToken notSoonExpiredToken = UnifiedAccessToken.builder()
                .accessToken("test_token")
                .expiresAt(LocalDateTime.now().plusMinutes(10))
                .build();
        assertFalse(notSoonExpiredToken.isExpiringSoon());
        
        // 测试自定义时间
        assertTrue(soonExpiredToken.isExpiringSoon(10)); // 10分钟内过期
        assertFalse(soonExpiredToken.isExpiringSoon(1)); // 1分钟内过期
    }

    @Test
    void testHasRefreshToken() {
        // 测试有刷新令牌
        UnifiedAccessToken tokenWithRefresh = UnifiedAccessToken.builder()
                .accessToken("test_token")
                .refreshToken("refresh_token")
                .build();
        assertTrue(tokenWithRefresh.hasRefreshToken());
        
        // 测试没有刷新令牌
        UnifiedAccessToken tokenWithoutRefresh = UnifiedAccessToken.builder()
                .accessToken("test_token")
                .build();
        assertFalse(tokenWithoutRefresh.hasRefreshToken());
        
        // 测试空刷新令牌
        UnifiedAccessToken tokenWithEmptyRefresh = UnifiedAccessToken.builder()
                .accessToken("test_token")
                .refreshToken("")
                .build();
        assertFalse(tokenWithEmptyRefresh.hasRefreshToken());
    }

    @Test
    void testGetRemainingSeconds() {
        // 测试剩余时间计算
        LocalDateTime futureTime = LocalDateTime.now().plusMinutes(30);
        UnifiedAccessToken token = UnifiedAccessToken.builder()
                .accessToken("test_token")
                .expiresAt(futureTime)
                .build();
        
        long remainingSeconds = token.getRemainingSeconds();
        assertTrue(remainingSeconds > 1700 && remainingSeconds <= 1800); // 大约30分钟
        
        // 测试已过期令牌
        UnifiedAccessToken expiredToken = UnifiedAccessToken.builder()
                .accessToken("test_token")
                .expiresAt(LocalDateTime.now().minusMinutes(10))
                .build();
        assertEquals(0, expiredToken.getRemainingSeconds());
        
        // 测试没有过期时间的令牌
        UnifiedAccessToken noExpirationToken = UnifiedAccessToken.builder()
                .accessToken("test_token")
                .build();
        assertEquals(Long.MAX_VALUE, noExpirationToken.getRemainingSeconds());
    }

    @Test
    void testBuilder() {
        UnifiedAccessToken token = UnifiedAccessToken.builder()
                .platform(PlatformType.DINGTALK)
                .accessToken("access_token_123")
                .refreshToken("refresh_token_456")
                .tokenType("Bearer")
                .expiresIn(3600L)
                .scope("read write")
                .userId("user123")
                .build();
        
        assertEquals(PlatformType.DINGTALK, token.getPlatform());
        assertEquals("access_token_123", token.getAccessToken());
        assertEquals("refresh_token_456", token.getRefreshToken());
        assertEquals("Bearer", token.getTokenType());
        assertEquals(3600L, token.getExpiresIn());
        assertEquals("read write", token.getScope());
        assertEquals("user123", token.getUserId());
    }
}
