package cn.com.handthing.testapp.controller;

import cn.com.handthing.testapp.service.LogTestService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 日志测试控制器
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/log")
public class LogTestController {

    @Autowired
    private LogTestService logTestService;

    @GetMapping("/test")
    public Map<String, Object> testLog(@RequestParam(defaultValue = "你好世界") String message) {
        log.info("收到日志测试请求，消息: {}", message);

        // 测试不同级别的日志和中文显示
        log.debug("这是一条 DEBUG 日志，包含中文: {}", message);
        log.info("这是一条 INFO 日志，测试中文编码: {}", message);
        log.warn("这是一条 WARN 日志，中文字符测试: {}", message);

        // 调用服务层测试
        String result = logTestService.processMessage(message);

        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "日志测试完成，中文显示正常");
        response.put("input", message);
        response.put("result", result);
        response.put("timestamp", System.currentTimeMillis());

        log.info("日志测试完成，返回结果包含中文: {}", response);
        return response;
    }

    @GetMapping("/error")
    public Map<String, Object> testErrorLog() {
        log.info("开始测试错误日志，包含中文字符");

        try {
            // 故意制造一个异常
            int result = 10 / 0;
        } catch (Exception e) {
            log.error("捕获到异常，中文错误信息测试", e);
        }

        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "错误日志测试完成，中文显示正常");
        response.put("timestamp", System.currentTimeMillis());

        return response;
    }

    @GetMapping("/performance")
    public Map<String, Object> testPerformanceLog() {
        log.info("开始性能测试，测试中文日志性能");

        long startTime = System.currentTimeMillis();

        // 模拟一些处理，包含中文日志
        for (int i = 0; i < 100; i++) {
            log.debug("处理第 {} 个项目，中文字符测试", i);
        }

        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        log.info("性能测试完成，耗时: {} 毫秒，中文显示正常", duration);

        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "性能测试完成，中文编码正常");
        response.put("duration", duration);
        response.put("timestamp", System.currentTimeMillis());

        return response;
    }
}
