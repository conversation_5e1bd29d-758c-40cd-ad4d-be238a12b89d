package cn.com.handthing.starter.id.generator.impl;

import cn.com.handthing.starter.id.exception.IdGenerationException;
import cn.com.handthing.starter.id.generator.IdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.UUID;

/**
 * UUID生成器
 * <p>
 * 基于Java标准库的UUID实现，生成32位无连字符的UUID字符串。
 * 作为String类型字段的默认ID生成器。
 * </p>
 * 
 * <h3>特性：</h3>
 * <ul>
 *   <li>线程安全</li>
 *   <li>高性能</li>
 *   <li>全局唯一性</li>
 *   <li>无需配置</li>
 * </ul>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Component("uuidGenerator")
public class UuidGenerator implements IdGenerator<String> {
    
    /**
     * 生成器名称
     */
    private static final String GENERATOR_NAME = "UuidGenerator";
    
    /**
     * 是否包含连字符
     */
    private final boolean includeHyphens;
    
    /**
     * 是否转换为大写
     */
    private final boolean upperCase;
    
    /**
     * 默认构造函数
     * <p>
     * 生成32位无连字符的小写UUID
     * </p>
     */
    public UuidGenerator() {
        this(false, false);
    }
    
    /**
     * 构造函数
     *
     * @param includeHyphens 是否包含连字符
     * @param upperCase      是否转换为大写
     */
    public UuidGenerator(boolean includeHyphens, boolean upperCase) {
        this.includeHyphens = includeHyphens;
        this.upperCase = upperCase;
        log.debug("UuidGenerator initialized: includeHyphens={}, upperCase={}", 
                includeHyphens, upperCase);
    }
    
    @Override
    public String generate() {
        try {
            String uuid = UUID.randomUUID().toString();
            
            // 移除连字符（如果需要）
            if (!includeHyphens) {
                uuid = uuid.replace("-", "");
            }
            
            // 转换大小写（如果需要）
            if (upperCase) {
                uuid = uuid.toUpperCase();
            }
            
            return uuid;
            
        } catch (Exception e) {
            throw new IdGenerationException(GENERATOR_NAME, "UUID_GENERATION_FAILED", 
                    "Failed to generate UUID", e);
        }
    }
    
    @Override
    public Class<String> getIdType() {
        return String.class;
    }
    
    @Override
    public String getName() {
        return GENERATOR_NAME;
    }
    
    @Override
    public boolean isAvailable() {
        // UUID生成器总是可用的
        return true;
    }
    
    @Override
    public void warmUp() {
        // 预热：生成几个UUID以确保JVM优化
        log.debug("Warming up UUID generator...");
        for (int i = 0; i < 10; i++) {
            generate();
        }
        log.debug("UUID generator warmed up successfully");
    }
    
    /**
     * 创建包含连字符的UUID生成器
     *
     * @return UUID生成器实例
     */
    public static UuidGenerator withHyphens() {
        return new UuidGenerator(true, false);
    }
    
    /**
     * 创建大写UUID生成器
     *
     * @return UUID生成器实例
     */
    public static UuidGenerator upperCase() {
        return new UuidGenerator(false, true);
    }
    
    /**
     * 创建包含连字符的大写UUID生成器
     *
     * @return UUID生成器实例
     */
    public static UuidGenerator withHyphensUpperCase() {
        return new UuidGenerator(true, true);
    }
    
    /**
     * 验证UUID格式
     *
     * @param uuid 要验证的UUID字符串
     * @return 如果格式正确返回true，否则返回false
     */
    public static boolean isValidUuid(String uuid) {
        if (uuid == null || uuid.trim().isEmpty()) {
            return false;
        }
        
        try {
            // 如果没有连字符，添加连字符后验证
            String uuidToValidate = uuid;
            if (uuid.length() == 32 && !uuid.contains("-")) {
                uuidToValidate = uuid.substring(0, 8) + "-" +
                        uuid.substring(8, 12) + "-" +
                        uuid.substring(12, 16) + "-" +
                        uuid.substring(16, 20) + "-" +
                        uuid.substring(20, 32);
            }
            
            UUID.fromString(uuidToValidate);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }
    
    /**
     * 获取生成器配置信息
     *
     * @return 配置信息字符串
     */
    public String getConfigInfo() {
        return String.format("UuidGenerator{includeHyphens=%s, upperCase=%s}", 
                includeHyphens, upperCase);
    }
    
    @Override
    public String toString() {
        return getConfigInfo();
    }
}
