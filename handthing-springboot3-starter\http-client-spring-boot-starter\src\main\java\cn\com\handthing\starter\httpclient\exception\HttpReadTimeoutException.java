package cn.com.handthing.starter.httpclient.exception;

/**
 * 表示在成功建立连接后，从服务器读取数据时发生超时的异常。
 * <p>
 * 这是一种特定的 {@link HttpRequestException}。
 */
public class HttpReadTimeoutException extends HttpRequestException {

    /**
     * 使用指定的详细消息和原因构造一个新的读取超时异常。
     *
     * @param message 详细消息。
     * @param cause   原因，通常是底层客户端抛出的与读取超时相关的原生异常 (例如, {@code java.net.SocketTimeoutException})。
     */
    public HttpReadTimeoutException(String message, Throwable cause) {
        super(message, cause);
    }
}