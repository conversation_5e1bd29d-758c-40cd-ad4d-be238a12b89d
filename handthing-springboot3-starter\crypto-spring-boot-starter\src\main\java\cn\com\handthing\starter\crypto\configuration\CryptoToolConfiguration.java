package cn.com.handthing.starter.crypto.configuration;

import cn.com.handthing.starter.crypto.properties.CryptoProperties;
import cn.com.handthing.starter.crypto.tool.CryptoToolRunner;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Crypto Starter 内置工具的配置类。
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Configuration(proxyBeanMethods = false)
@EnableConfigurationProperties(CryptoProperties.class)
public class CryptoToolConfiguration {

    /**
     * 配置密钥生成工具的 CommandLineRunner。
     * <p>
     * 这个 Bean 只有在 "handthing.crypto.tool.generate-key.algorithm" 属性被设置时才会被激活。
     * 这可以防止它在正常的应用启动时运行。
     *
     * @param properties Crypto 配置属性
     * @return CryptoToolRunner 实例
     */
    @Bean
    @ConditionalOnProperty("handthing.crypto.tool.generate-key.algorithm")
    public CryptoToolRunner cryptoToolRunner(CryptoProperties properties) {
        return new CryptoToolRunner(properties.getTool().getGenerateKey());
    }
}