package cn.com.handthing.starter.httpclient.filter;

import cn.com.handthing.starter.httpclient.config.LoggingProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.reactive.function.client.ClientRequest;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.ExchangeFilterFunction;
import org.springframework.web.reactive.function.client.ExchangeFunction;
import reactor.core.publisher.Mono;

/**
 * WebClient 日志过滤器
 * 
 * <AUTHOR>
 * @since V1.0.0
 */
public class WebClientLoggingFilter implements ExchangeFilterFunction {

    private static final Logger log = LoggerFactory.getLogger(WebClientLoggingFilter.class);
    
    private final LoggingProperties loggingProperties;

    public WebClientLoggingFilter(LoggingProperties loggingProperties) {
        this.loggingProperties = loggingProperties;
    }

    @Override
    public Mono<ClientResponse> filter(ClientRequest request, ExchangeFunction next) {
        if (!loggingProperties.isEnabled()) {
            return next.exchange(request);
        }

        // 记录请求日志
        logRequest(request);
        
        long startTime = System.currentTimeMillis();
        
        return next.exchange(request)
                .doOnNext(response -> {
                    long endTime = System.currentTimeMillis();
                    logResponse(response, endTime - startTime);
                })
                .doOnError(error -> {
                    long endTime = System.currentTimeMillis();
                    logError(request, error, endTime - startTime);
                });
    }

    private void logRequest(ClientRequest request) {
        LoggingProperties.LogLevel level = loggingProperties.getLevel();
        
        if (level == LoggingProperties.LogLevel.BASIC || 
            level == LoggingProperties.LogLevel.HEADERS || 
            level == LoggingProperties.LogLevel.FULL) {
            
            log.info("HTTP Request: {} {}", request.method(), request.url());
        }
        
        if (level == LoggingProperties.LogLevel.HEADERS || 
            level == LoggingProperties.LogLevel.FULL) {
            
            request.headers().forEach((name, values) -> {
                if (!isSensitiveHeader(name)) {
                    log.debug("Request Header: {}: {}", name, String.join(", ", values));
                }
            });
        }
        
        if (level == LoggingProperties.LogLevel.FULL) {
            // 对于 WebClient，请求体的日志记录比较复杂，这里简化处理
            log.debug("Request body logging enabled (body content not shown for WebClient)");
        }
    }

    private void logResponse(ClientResponse response, long duration) {
        LoggingProperties.LogLevel level = loggingProperties.getLevel();
        
        if (level == LoggingProperties.LogLevel.BASIC || 
            level == LoggingProperties.LogLevel.HEADERS || 
            level == LoggingProperties.LogLevel.FULL) {
            
            log.info("HTTP Response: {} ({}ms)", 
                    response.statusCode().value(), 
                    duration);
        }
        
        if (level == LoggingProperties.LogLevel.HEADERS || 
            level == LoggingProperties.LogLevel.FULL) {
            
            response.headers().asHttpHeaders().forEach((name, values) -> {
                if (!isSensitiveHeader(name)) {
                    log.debug("Response Header: {}: {}", name, String.join(", ", values));
                }
            });
        }
        
        if (level == LoggingProperties.LogLevel.FULL) {
            // 对于 WebClient，响应体的日志记录需要特殊处理，这里简化
            log.debug("Response body logging enabled (body content not shown for WebClient)");
        }
    }

    private void logError(ClientRequest request, Throwable error, long duration) {
        log.error("HTTP Request failed: {} {} ({}ms) - {}", 
                request.method(), 
                request.url(), 
                duration, 
                error.getMessage());
    }

    private boolean isSensitiveHeader(String headerName) {
        String lowerName = headerName.toLowerCase();
        return lowerName.contains("authorization") || 
               lowerName.contains("cookie") || 
               lowerName.contains("token") ||
               lowerName.contains("password") ||
               lowerName.contains("secret");
    }
}
