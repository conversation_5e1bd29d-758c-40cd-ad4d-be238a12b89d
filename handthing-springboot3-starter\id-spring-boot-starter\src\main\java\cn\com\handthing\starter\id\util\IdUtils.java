package cn.com.handthing.starter.id.util;

import cn.com.handthing.starter.id.generator.IdGenerator;
import cn.com.handthing.starter.id.generator.impl.SnowflakeIdGenerator;
import cn.com.handthing.starter.id.generator.impl.UuidGenerator;
import cn.com.handthing.starter.id.registry.IdGeneratorRegistry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.regex.Pattern;

/**
 * ID生成工具类
 * <p>
 * 提供便捷的ID生成和验证方法，封装了对ID生成器注册中心的访问。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Component
public class IdUtils {
    
    /**
     * UUID格式正则表达式（32位无连字符）
     */
    private static final Pattern UUID_PATTERN = Pattern.compile("^[0-9a-fA-F]{32}$");
    
    /**
     * UUID格式正则表达式（36位含连字符）
     */
    private static final Pattern UUID_WITH_HYPHENS_PATTERN = 
            Pattern.compile("^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$");
    
    /**
     * Snowflake ID的最小值（基于开始时间戳）
     */
    private static final long SNOWFLAKE_MIN_VALUE = 1L << 22; // 大约4M
    
    /**
     * Snowflake ID的最大值（理论最大值）
     */
    private static final long SNOWFLAKE_MAX_VALUE = Long.MAX_VALUE;
    
    /**
     * ID生成器注册中心
     */
    private static IdGeneratorRegistry registry;
    
    @Autowired
    public void setRegistry(IdGeneratorRegistry registry) {
        IdUtils.registry = registry;
    }
    
    /**
     * 生成UUID
     *
     * @return UUID字符串
     */
    public static String generateUuid() {
        return generateUuid(false, false);
    }
    
    /**
     * 生成UUID
     *
     * @param includeHyphens 是否包含连字符
     * @param upperCase      是否转换为大写
     * @return UUID字符串
     */
    public static String generateUuid(boolean includeHyphens, boolean upperCase) {
        UuidGenerator generator = new UuidGenerator(includeHyphens, upperCase);
        return generator.generate();
    }
    
    /**
     * 生成Snowflake ID
     *
     * @return Snowflake ID
     */
    public static Long generateSnowflakeId() {
        return generateSnowflakeId(1L, 1L);
    }
    
    /**
     * 生成Snowflake ID
     *
     * @param workerId     机器ID
     * @param datacenterId 数据中心ID
     * @return Snowflake ID
     */
    public static Long generateSnowflakeId(long workerId, long datacenterId) {
        SnowflakeIdGenerator generator = new SnowflakeIdGenerator(workerId, datacenterId);
        return generator.generate();
    }
    
    /**
     * 使用指定策略生成ID
     *
     * @param strategyName 策略名称
     * @param <T>          ID类型
     * @return 生成的ID
     */
    @SuppressWarnings("unchecked")
    public static <T> T generateId(String strategyName) {
        if (registry == null) {
            throw new IllegalStateException("IdGeneratorRegistry not initialized");
        }
        
        IdGenerator<?> generator = registry.getGenerator(strategyName);
        return (T) generator.generate();
    }
    
    /**
     * 使用默认策略生成ID
     *
     * @param idType ID类型
     * @param <T>    ID类型
     * @return 生成的ID
     */
    @SuppressWarnings("unchecked")
    public static <T> T generateId(Class<T> idType) {
        if (registry == null) {
            throw new IllegalStateException("IdGeneratorRegistry not initialized");
        }
        
        IdGenerator<?> generator = registry.getGenerator(idType);
        return (T) generator.generate();
    }
    
    /**
     * 验证UUID格式
     *
     * @param uuid 要验证的UUID字符串
     * @return 如果格式正确返回true，否则返回false
     */
    public static boolean isValidUuid(String uuid) {
        if (uuid == null || uuid.trim().isEmpty()) {
            return false;
        }
        
        // 检查32位无连字符格式
        if (UUID_PATTERN.matcher(uuid).matches()) {
            return true;
        }
        
        // 检查36位含连字符格式
        return UUID_WITH_HYPHENS_PATTERN.matcher(uuid).matches();
    }
    
    /**
     * 验证Snowflake ID格式
     *
     * @param id 要验证的Snowflake ID
     * @return 如果格式正确返回true，否则返回false
     */
    public static boolean isValidSnowflakeId(Long id) {
        if (id == null) {
            return false;
        }
        
        return id >= SNOWFLAKE_MIN_VALUE && id <= SNOWFLAKE_MAX_VALUE;
    }
    
    /**
     * 解析Snowflake ID
     *
     * @param id Snowflake ID
     * @return ID信息，如果ID无效返回null
     */
    public static SnowflakeIdGenerator.SnowflakeIdInfo parseSnowflakeId(Long id) {
        if (!isValidSnowflakeId(id)) {
            return null;
        }
        
        SnowflakeIdGenerator generator = new SnowflakeIdGenerator();
        return generator.parseId(id);
    }
    
    /**
     * 格式化UUID（添加连字符）
     *
     * @param uuid 32位无连字符UUID
     * @return 36位含连字符UUID
     */
    public static String formatUuid(String uuid) {
        if (uuid == null || uuid.length() != 32) {
            throw new IllegalArgumentException("Invalid UUID format");
        }
        
        return uuid.substring(0, 8) + "-" +
                uuid.substring(8, 12) + "-" +
                uuid.substring(12, 16) + "-" +
                uuid.substring(16, 20) + "-" +
                uuid.substring(20, 32);
    }
    
    /**
     * 移除UUID连字符
     *
     * @param uuid 36位含连字符UUID
     * @return 32位无连字符UUID
     */
    public static String removeUuidHyphens(String uuid) {
        if (uuid == null) {
            throw new IllegalArgumentException("UUID cannot be null");
        }
        
        return uuid.replace("-", "");
    }
    
    /**
     * 检查ID生成器是否可用
     *
     * @param strategyName 策略名称
     * @return 如果可用返回true，否则返回false
     */
    public static boolean isGeneratorAvailable(String strategyName) {
        if (registry == null) {
            return false;
        }
        
        try {
            IdGenerator<?> generator = registry.getGenerator(strategyName);
            return generator.isAvailable();
        } catch (Exception e) {
            log.debug("Generator not available: {}", strategyName, e);
            return false;
        }
    }
    
    /**
     * 检查默认类型生成器是否可用
     *
     * @param idType ID类型
     * @return 如果可用返回true，否则返回false
     */
    public static boolean isDefaultGeneratorAvailable(Class<?> idType) {
        if (registry == null) {
            return false;
        }
        
        try {
            IdGenerator<?> generator = registry.getGenerator(idType);
            return generator.isAvailable();
        } catch (Exception e) {
            log.debug("Default generator not available for type: {}", idType.getName(), e);
            return false;
        }
    }
    
    /**
     * 获取所有已注册的策略名称
     *
     * @return 策略名称集合
     */
    public static java.util.Set<String> getRegisteredStrategies() {
        if (registry == null) {
            return java.util.Collections.emptySet();
        }
        
        return registry.getRegisteredStrategies();
    }
    
    /**
     * 获取所有支持的默认类型
     *
     * @return 支持的类型集合
     */
    public static java.util.Set<Class<?>> getSupportedTypes() {
        if (registry == null) {
            return java.util.Collections.emptySet();
        }
        
        return registry.getSupportedTypes();
    }
    
    /**
     * 获取ID类型描述
     *
     * @param id ID值
     * @return ID类型描述
     */
    public static String getIdTypeDescription(Object id) {
        if (id == null) {
            return "NULL";
        }
        
        if (id instanceof String) {
            String strId = (String) id;
            if (isValidUuid(strId)) {
                return "UUID";
            } else {
                return "STRING";
            }
        } else if (id instanceof Long) {
            Long longId = (Long) id;
            if (isValidSnowflakeId(longId)) {
                return "SNOWFLAKE";
            } else {
                return "LONG";
            }
        } else {
            return id.getClass().getSimpleName().toUpperCase();
        }
    }
    
    /**
     * 生成ID摘要信息
     *
     * @param id ID值
     * @return ID摘要信息
     */
    public static String getIdSummary(Object id) {
        if (id == null) {
            return "ID{type=NULL, value=null}";
        }
        
        String type = getIdTypeDescription(id);
        String value = id.toString();
        
        // 对于长ID，只显示前后几位
        if (value.length() > 20) {
            value = value.substring(0, 8) + "..." + value.substring(value.length() - 8);
        }
        
        return String.format("ID{type=%s, value=%s}", type, value);
    }
}
