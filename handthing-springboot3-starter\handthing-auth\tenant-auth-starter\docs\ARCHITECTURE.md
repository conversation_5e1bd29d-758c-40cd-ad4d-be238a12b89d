# HandThing 多租户认证系统架构设计

## 概述

HandThing 多租户认证系统是一个基于 Spring Boot 3 的企业级多租户 SaaS 解决方案。本文档详细描述了系统的架构设计、核心组件、数据流程和技术实现。

## 系统架构

### 整体架构图

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                           HandThing 多租户认证系统                              │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐   │
│  │   Client    │    │   Client    │    │   Client    │    │   Client    │   │
│  │  (Tenant A) │    │  (Tenant B) │    │  (Tenant C) │    │  (Default)  │   │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘   │
│         │                   │                   │                   │       │
│         └───────────────────┼───────────────────┼───────────────────┘       │
│                             │                   │                           │
│  ┌─────────────────────────────────────────────────────────────────────────┐ │
│  │                        Web Layer                                        │ │
│  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────────────┐  │ │
│  │  │ Tenant Resolver │  │ Authentication  │  │    Business Logic       │  │ │
│  │  │     Filter      │  │     Filter      │  │     Controllers        │  │ │
│  │  │   (Order: -200) │  │   (Order: -100) │  │                         │  │ │
│  │  └─────────────────┘  └─────────────────┘  └─────────────────────────┘  │ │
│  └─────────────────────────────────────────────────────────────────────────┘ │
│                             │                                               │
│  ┌─────────────────────────────────────────────────────────────────────────┐ │
│  │                       Service Layer                                     │ │
│  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────────────┐  │ │
│  │  │ Tenant Context  │  │ Tenant Config   │  │   Tenant Resolver       │  │ │
│  │  │    Holder       │  │    Service      │  │     Registry            │  │ │
│  │  └─────────────────┘  └─────────────────┘  └─────────────────────────┘  │ │
│  └─────────────────────────────────────────────────────────────────────────┘ │
│                             │                                               │
│  ┌─────────────────────────────────────────────────────────────────────────┐ │
│  │                    Data Access Layer                                    │ │
│  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────────────┐  │ │
│  │  │ Tenant Config   │  │    Tenant       │  │       Cache             │  │ │
│  │  │   Repository    │  │   Repository    │  │     Manager             │  │ │
│  │  └─────────────────┘  └─────────────────┘  └─────────────────────────┘  │ │
│  └─────────────────────────────────────────────────────────────────────────┘ │
│                             │                                               │
│  ┌─────────────────────────────────────────────────────────────────────────┐ │
│  │                      Database Layer                                     │ │
│  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────────────┐  │ │
│  │  │ tenant_configs  │  │    tenants      │  │       Indexes           │  │ │
│  │  │     Table       │  │     Table       │  │    & Constraints        │  │ │
│  │  └─────────────────┘  └─────────────────┘  └─────────────────────────┘  │ │
│  └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 分层架构

#### 1. Web Layer（Web层）
- **租户解析过滤器**：解析HTTP请求中的租户信息
- **认证过滤器**：处理用户认证
- **业务控制器**：处理业务逻辑和API请求

#### 2. Service Layer（服务层）
- **租户上下文管理器**：管理当前线程的租户上下文
- **租户配置服务**：提供租户配置的CRUD操作
- **租户解析器注册表**：管理多种租户解析策略

#### 3. Data Access Layer（数据访问层）
- **租户配置仓库**：租户配置数据的持久化
- **租户仓库**：租户基本信息的持久化
- **缓存管理器**：提供配置缓存功能

#### 4. Database Layer（数据库层）
- **租户配置表**：存储租户特定配置
- **租户信息表**：存储租户基本信息
- **索引和约束**：保证数据完整性和查询性能

## 核心组件

### 1. 租户解析器 (TenantResolver)

#### 接口定义
```java
public interface TenantResolver extends Ordered {
    String resolveTenantId(HttpServletRequest request);
    int getOrder();
}
```

#### HTTP头部解析器
```java
@Component
public class HttpHeaderTenantResolver implements TenantResolver {
    private final String primaryHeader = "X-Tenant-ID";
    private final List<String> fallbackHeaders = Arrays.asList(
        "Tenant-ID", "X-Tenant", "Tenant"
    );
    
    @Override
    public String resolveTenantId(HttpServletRequest request) {
        // 按优先级解析租户ID
        String tenantId = request.getHeader(primaryHeader);
        if (StringUtils.hasText(tenantId)) {
            return tenantId;
        }
        
        for (String header : fallbackHeaders) {
            tenantId = request.getHeader(header);
            if (StringUtils.hasText(tenantId)) {
                return tenantId;
            }
        }
        
        return null;
    }
}
```

### 2. 租户上下文管理器 (TenantContextHolder)

#### 设计原理
- 基于 ThreadLocal 实现线程级别的租户隔离
- 自动清理机制防止内存泄漏
- 支持租户信息的传递和继承

#### 核心实现
```java
public class TenantContextHolder {
    private static final ThreadLocal<Tenant> TENANT_CONTEXT = new ThreadLocal<>();
    
    public static void setCurrentTenant(Tenant tenant) {
        TENANT_CONTEXT.set(tenant);
    }
    
    public static Tenant getCurrentTenant() {
        return TENANT_CONTEXT.get();
    }
    
    public static String getCurrentTenantId() {
        Tenant tenant = getCurrentTenant();
        return tenant != null ? tenant.getId() : null;
    }
    
    public static void clear() {
        TENANT_CONTEXT.remove();
    }
}
```

### 3. 租户配置服务 (TenantConfigService)

#### 服务接口
```java
public interface TenantConfigService {
    String getConfig(String configKey);
    String getConfig(String configKey, String defaultValue);
    Map<String, String> getAllConfigs();
    void setConfig(String configKey, String configValue);
    void removeConfig(String configKey);
    boolean hasConfig(String configKey);
}
```

#### 缓存策略
- **L1缓存**：基于 Caffeine 的本地缓存
- **缓存键**：`tenant:{tenantId}:config:{configKey}`
- **TTL**：可配置的生存时间（默认5分钟）
- **失效策略**：配置更新时主动失效

### 4. 租户解析过滤器 (TenantResolverFilter)

#### 过滤器链
```
HTTP Request → TenantResolverFilter → AuthenticationFilter → Business Logic
    ↓               ↓                      ↓                    ↓
设置租户头部    解析租户ID并设置上下文    验证用户身份        处理业务逻辑
```

#### 执行流程
1. **解析租户ID**：使用注册的解析器解析租户ID
2. **验证租户**：检查租户是否存在且有效
3. **设置上下文**：将租户信息设置到ThreadLocal
4. **传递请求**：继续执行后续过滤器
5. **清理上下文**：请求结束后清理ThreadLocal

#### 排除路径处理
```java
private boolean isExcludedPath(HttpServletRequest request) {
    String requestPath = getRequestPath(request);
    
    for (String excludePath : excludePaths) {
        if (isPathMatched(requestPath, excludePath)) {
            return true;
        }
    }
    
    return false;
}
```

## 数据模型

### 1. 租户实体 (Tenant)

```java
@Entity
@Table(name = "tenants")
public class Tenant {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "tenant_id", unique = true, nullable = false)
    private String tenantId;
    
    @Column(name = "tenant_name", nullable = false)
    private String tenantName;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private TenantStatus status;
    
    // 其他字段...
}
```

### 2. 租户配置实体 (TenantConfig)

```java
@Entity
@Table(name = "tenant_configs")
public class TenantConfig {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "tenant_id", nullable = false)
    private String tenantId;
    
    @Column(name = "config_key", nullable = false)
    private String configKey;
    
    @Column(name = "config_value")
    private String configValue;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "config_type")
    private ConfigType configType;
    
    @Column(name = "sensitive")
    private Boolean sensitive = false;
    
    // 其他字段...
}
```

### 3. 数据库索引设计

```sql
-- 租户配置表索引
CREATE UNIQUE INDEX uk_tenant_config ON tenant_configs (tenant_id, config_key);
CREATE INDEX idx_tenant_id ON tenant_configs (tenant_id);
CREATE INDEX idx_config_key ON tenant_configs (config_key);
CREATE INDEX idx_enabled ON tenant_configs (enabled);

-- 租户信息表索引
CREATE UNIQUE INDEX uk_tenant_id ON tenants (tenant_id);
CREATE INDEX idx_tenant_status ON tenants (status);
CREATE INDEX idx_tenant_domain ON tenants (domain);
```

## 配置管理

### 1. 自动配置类

```java
@AutoConfiguration
@ConditionalOnProperty(prefix = "handthing.auth.saas", name = "enabled", havingValue = "true", matchIfMissing = true)
@EnableConfigurationProperties({SaaSAuthProperties.class, TenantResolverProperties.class})
@EnableJpaRepositories(basePackages = "cn.com.handthing.starter.tenant.repository")
@EntityScan(basePackages = "cn.com.handthing.starter.tenant.config")
@ComponentScan(basePackages = "cn.com.handthing.starter.tenant")
public class SaaSAuthAutoConfiguration {
    
    @Bean
    @ConditionalOnMissingBean
    public TenantConfigService tenantConfigService(TenantConfigRepository repository) {
        return new TenantConfigServiceImpl(repository);
    }
    
    @Bean
    @ConditionalOnMissingBean
    public HttpHeaderTenantResolver httpHeaderTenantResolver(TenantResolverProperties properties) {
        return new HttpHeaderTenantResolver(properties.getHttpHeader());
    }
    
    @Bean
    @ConditionalOnProperty(prefix = "handthing.auth.saas.filter", name = "enabled", havingValue = "true", matchIfMissing = true)
    public FilterRegistrationBean<TenantResolverFilter> tenantResolverFilter(
            List<TenantResolver> tenantResolvers,
            TenantConfigService tenantConfigService,
            SaaSAuthProperties properties) {
        
        TenantResolverFilter filter = new TenantResolverFilter(
                tenantResolvers,
                tenantConfigService,
                properties.getEffectiveDefaultTenantId(),
                properties.isTenantRequired(),
                properties.getFilter().getOrder(),
                Arrays.asList(properties.getFilter().getExcludePaths())
        );
        
        FilterRegistrationBean<TenantResolverFilter> registration = new FilterRegistrationBean<>(filter);
        registration.setOrder(properties.getFilter().getOrder());
        registration.addUrlPatterns("/*");
        
        return registration;
    }
}
```

### 2. 配置属性

```java
@ConfigurationProperties(prefix = "handthing.auth.saas")
@Data
public class SaaSAuthProperties {
    private boolean enabled = true;
    private String defaultTenant = "default";
    private boolean tenantRequired = false;
    private boolean multiTenant = true;
    private boolean cache = true;
    private FilterProperties filter = new FilterProperties();
    private TenantResolverProperties resolver = new TenantResolverProperties();
    private boolean validation = true;
    private boolean monitoring = true;
    
    @Data
    public static class FilterProperties {
        private boolean enabled = true;
        private int order = -200;
        private String[] excludePaths = {
            "/actuator/**",
            "/error",
            "/favicon.ico",
            "/static/**",
            "/public/**"
        };
    }
}
```

## 性能优化

### 1. 缓存策略

#### 配置缓存
```java
@Service
public class CachedTenantConfigService implements TenantConfigService {
    
    @Cacheable(value = "tenant-configs", key = "#tenantId + ':' + #configKey")
    public String getConfig(String tenantId, String configKey) {
        return repository.findByTenantIdAndConfigKey(tenantId, configKey)
                .map(TenantConfig::getConfigValue)
                .orElse(null);
    }
    
    @CacheEvict(value = "tenant-configs", key = "#tenantId + ':' + #configKey")
    public void setConfig(String tenantId, String configKey, String configValue) {
        // 更新配置并清除缓存
    }
}
```

#### 缓存配置
```yaml
spring:
  cache:
    type: caffeine
    caffeine:
      spec: maximumSize=1000,expireAfterWrite=5m
```

### 2. 数据库优化

#### 连接池配置
```yaml
spring:
  datasource:
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      max-lifetime: 1800000
      connection-timeout: 30000
```

#### 查询优化
- 使用索引优化常用查询
- 批量查询减少数据库往返
- 分页查询避免大结果集

### 3. 内存管理

#### ThreadLocal清理
```java
public class TenantContextCleanupFilter implements Filter {
    
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        try {
            chain.doFilter(request, response);
        } finally {
            TenantContextHolder.clear();
        }
    }
}
```

## 安全设计

### 1. 租户隔离

#### 数据隔离
- 所有数据查询都基于租户ID进行过滤
- 使用数据库约束确保数据完整性
- 防止跨租户数据访问

#### 配置隔离
- 每个租户独立的配置空间
- 敏感配置的特殊处理
- 配置访问权限控制

### 2. 安全防护

#### 输入验证
```java
public class TenantIdValidator {
    private static final Pattern TENANT_ID_PATTERN = Pattern.compile("^[a-zA-Z0-9_-]{1,64}$");
    
    public static boolean isValidTenantId(String tenantId) {
        return tenantId != null && TENANT_ID_PATTERN.matcher(tenantId).matches();
    }
}
```

#### 敏感信息保护
```java
@Entity
public class TenantConfig {
    @Column(name = "sensitive")
    private Boolean sensitive = false;
    
    public String getConfigValue() {
        if (Boolean.TRUE.equals(sensitive)) {
            return "***"; // 敏感信息脱敏
        }
        return configValue;
    }
}
```

## 监控和运维

### 1. 健康检查

```java
@Component
public class TenantHealthIndicator implements HealthIndicator {
    
    @Override
    public Health health() {
        try {
            String tenantId = TenantContextHolder.getCurrentTenantId();
            boolean tenantExists = tenantConfigService.tenantExists(tenantId);
            
            if (tenantExists) {
                return Health.up()
                        .withDetail("tenantId", tenantId)
                        .withDetail("tenantExists", true)
                        .build();
            } else {
                return Health.down()
                        .withDetail("tenantId", tenantId)
                        .withDetail("tenantExists", false)
                        .build();
            }
        } catch (Exception e) {
            return Health.down(e).build();
        }
    }
}
```

### 2. 指标监控

```java
@Component
public class TenantMetrics {
    private final MeterRegistry meterRegistry;
    private final Counter tenantResolutionCounter;
    private final Timer configQueryTimer;
    
    public TenantMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.tenantResolutionCounter = Counter.builder("tenant.resolution.total")
                .description("Total tenant resolutions")
                .register(meterRegistry);
        this.configQueryTimer = Timer.builder("tenant.config.query.duration")
                .description("Tenant config query duration")
                .register(meterRegistry);
    }
}
```

### 3. 日志记录

```java
@Slf4j
public class TenantResolverFilter implements Filter {
    
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        
        String tenantId = resolveTenantId((HttpServletRequest) request);
        
        log.debug("Resolved tenant ID: {} for request: {}", 
                tenantId, ((HttpServletRequest) request).getRequestURI());
        
        try {
            // 设置MDC用于日志追踪
            MDC.put("tenantId", tenantId);
            
            chain.doFilter(request, response);
        } finally {
            MDC.clear();
            TenantContextHolder.clear();
        }
    }
}
```

## 扩展点

### 1. 自定义租户解析器

```java
@Component
@Order(10)
public class DomainTenantResolver implements TenantResolver {
    
    @Override
    public String resolveTenantId(HttpServletRequest request) {
        String serverName = request.getServerName();
        // 从域名中提取租户ID
        return extractTenantFromDomain(serverName);
    }
}
```

### 2. 配置变更监听

```java
@EventListener
public void onTenantConfigChanged(TenantConfigChangedEvent event) {
    // 处理配置变更
    String tenantId = event.getTenantId();
    String configKey = event.getConfigKey();
    String newValue = event.getNewValue();
    
    // 清除相关缓存
    cacheManager.evict("tenant-configs", tenantId + ":" + configKey);
    
    // 发送通知
    notificationService.notifyConfigChanged(tenantId, configKey, newValue);
}
```

### 3. 租户生命周期管理

```java
@Service
public class TenantLifecycleService {
    
    @Transactional
    public void createTenant(CreateTenantRequest request) {
        // 创建租户
        Tenant tenant = new Tenant();
        tenant.setTenantId(request.getTenantId());
        tenant.setTenantName(request.getTenantName());
        tenant.setStatus(TenantStatus.ACTIVE);
        tenantRepository.save(tenant);
        
        // 初始化默认配置
        initializeDefaultConfigs(request.getTenantId());
        
        // 发布事件
        applicationEventPublisher.publishEvent(new TenantCreatedEvent(tenant));
    }
}
```

## 总结

HandThing 多租户认证系统采用了分层架构设计，通过租户解析器、上下文管理器、配置服务等核心组件，实现了完整的多租户支持。系统具有高性能、高可用、易扩展的特点，能够满足企业级SaaS应用的需求。

关键设计原则：
- **隔离性**：确保租户间的完全隔离
- **性能**：通过缓存和优化提供高性能
- **可扩展性**：支持自定义解析器和扩展点
- **安全性**：提供完整的安全防护机制
- **可运维性**：提供监控、日志和健康检查功能
