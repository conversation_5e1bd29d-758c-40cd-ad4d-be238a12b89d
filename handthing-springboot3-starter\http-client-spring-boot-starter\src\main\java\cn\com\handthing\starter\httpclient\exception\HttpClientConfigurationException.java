package cn.com.handthing.starter.httpclient.exception;

/**
 * 表示HTTP客户端配置错误的异常。
 * <p>
 * 当starter的配置不正确或无法完成初始化时抛出，例如：
 * <ul>
 * <li>SSL/TLS 证书文件未找到</li>
 * <li>配置属性验证失败</li>
 * <li>必要的Bean未能创建</li>
 * </ul>
 */
public class HttpClientConfigurationException extends HandthingHttpException {

    /**
     * 使用指定的详细消息构造一个新的配置异常。
     *
     * @param message 详细消息。
     */
    public HttpClientConfigurationException(String message) {
        super(message);
    }

    /**
     * 使用指定的详细消息和原因构造一个新的配置异常。
     *
     * @param message 详细消息。
     * @param cause   原因。
     */
    public HttpClientConfigurationException(String message, Throwable cause) {
        super(message, cause);
    }
}