package cn.com.handthing.testapp;

import cn.com.handthing.starter.apidoc.annotation.EnableApiDoc;
//import cn.com.handthing.starter.knife4j.annotation.EnableKnife4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

/**
 * 测试应用启动类
 *
 * <p>注意：只启用 @EnableKnife4j，因为 Knife4j 已经继承了 api-doc 的所有功能。
 * <p>如果同时启用 @EnableApiDoc 和 @EnableKnife4j，可能会导致配置冲突。
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@SpringBootApplication
@ComponentScan(basePackages = {"cn.com.handthing.testapp", "cn.com.handthing.test", "cn.com.handthing.auth.testapp", "cn.com.handthing.starter.auth"})
//@EnableKnife4j  // 只启用 Knife4j，它会自动继承 api-doc 的功能
@EnableApiDoc
public class TestApplication {


    public static void main(String[] args) {
        SpringApplication.run(TestApplication.class, args);
    }
}
