package cn.com.handthing.starter.httpclient.internal;

import cn.com.handthing.starter.httpclient.BodySpec;
import cn.com.handthing.starter.httpclient.ResponseSpec;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.lang.NonNull;
import reactor.core.publisher.Mono;

import java.util.function.Function;
import java.util.function.Predicate;

/**
 * ResponseSpec 接口的 RestTemplate 实现。
 * 
 * <AUTHOR>
 * @since V1.0.0
 */
public final class RestTemplateResponseSpec implements ResponseSpec {

    private final ResponseEntity<byte[]> responseEntity;

    public RestTemplateResponseSpec(ResponseEntity<byte[]> responseEntity) {
        this.responseEntity = responseEntity;
    }

    @Override
    @NonNull
    public ResponseSpec onStatus(@NonNull Predicate<HttpStatus> statusPredicate, 
                                @NonNull Function<ClientHttpResponse, Mono<? extends Throwable>> exceptionFunction) {
        HttpStatus status = HttpStatus.valueOf(responseEntity.getStatusCode().value());
        if (statusPredicate.test(status)) {
            // 创建一个模拟的 ClientHttpResponse
            ClientHttpResponse mockResponse = createMockResponse();
            Mono<? extends Throwable> errorMono = exceptionFunction.apply(mockResponse);
            Throwable error = errorMono.block();
            if (error instanceof RuntimeException) {
                throw (RuntimeException) error;
            } else {
                throw new RuntimeException(error);
            }
        }
        return this;
    }

    @Override
    @NonNull
    public <T> BodySpec<T> body(@NonNull Class<T> type) {
        return new RestTemplateBodySpec<>(responseEntity, type);
    }

    private ClientHttpResponse createMockResponse() {
        // 创建一个简单的 ClientHttpResponse 实现
        return new ClientHttpResponse() {
            @Override
            public org.springframework.http.HttpStatusCode getStatusCode() {
                return responseEntity.getStatusCode();
            }

            @Override
            public String getStatusText() {
                return responseEntity.getStatusCode().toString();
            }

            @Override
            public void close() {
                // No-op
            }

            @Override
            public java.io.InputStream getBody() {
                byte[] body = responseEntity.getBody();
                return body != null ? new java.io.ByteArrayInputStream(body) : 
                       new java.io.ByteArrayInputStream(new byte[0]);
            }

            @Override
            public org.springframework.http.HttpHeaders getHeaders() {
                return responseEntity.getHeaders();
            }
        };
    }
}
