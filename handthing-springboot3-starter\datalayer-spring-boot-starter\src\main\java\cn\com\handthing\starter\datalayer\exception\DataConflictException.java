package cn.com.handthing.starter.datalayer.exception;

/**
 * 数据冲突异常
 * <p>
 * 当数据操作发生冲突时抛出此异常，如唯一约束冲突、乐观锁冲突等。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
public class DataConflictException extends DatalayerException {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 冲突类型
     */
    private final ConflictType conflictType;
    
    /**
     * 实体类型
     */
    private final String entityType;
    
    /**
     * 冲突字段
     */
    private final String conflictField;
    
    /**
     * 冲突值
     */
    private final Object conflictValue;
    
    /**
     * 冲突类型枚举
     */
    public enum ConflictType {
        /**
         * 唯一约束冲突
         */
        UNIQUE_CONSTRAINT,
        
        /**
         * 乐观锁冲突
         */
        OPTIMISTIC_LOCK,
        
        /**
         * 外键约束冲突
         */
        FOREIGN_KEY,
        
        /**
         * 业务规则冲突
         */
        BUSINESS_RULE,
        
        /**
         * 其他冲突
         */
        OTHER
    }
    
    /**
     * 构造函数
     *
     * @param message 错误信息
     */
    public DataConflictException(String message) {
        super("DATA_CONFLICT", "SAVE", message);
        this.conflictType = ConflictType.OTHER;
        this.entityType = null;
        this.conflictField = null;
        this.conflictValue = null;
    }
    
    /**
     * 构造函数
     *
     * @param conflictType 冲突类型
     * @param message      错误信息
     */
    public DataConflictException(ConflictType conflictType, String message) {
        super("DATA_CONFLICT", "SAVE", message);
        this.conflictType = conflictType;
        this.entityType = null;
        this.conflictField = null;
        this.conflictValue = null;
    }
    
    /**
     * 构造函数
     *
     * @param conflictType  冲突类型
     * @param entityType    实体类型
     * @param conflictField 冲突字段
     * @param conflictValue 冲突值
     * @param message       错误信息
     */
    public DataConflictException(ConflictType conflictType, String entityType, 
                               String conflictField, Object conflictValue, String message) {
        super("DATA_CONFLICT", "SAVE", message);
        this.conflictType = conflictType;
        this.entityType = entityType;
        this.conflictField = conflictField;
        this.conflictValue = conflictValue;
    }
    
    /**
     * 构造函数
     *
     * @param conflictType  冲突类型
     * @param entityType    实体类型
     * @param conflictField 冲突字段
     * @param conflictValue 冲突值
     * @param message       错误信息
     * @param cause         原因异常
     */
    public DataConflictException(ConflictType conflictType, String entityType, 
                               String conflictField, Object conflictValue, String message, Throwable cause) {
        super("DATA_CONFLICT", "SAVE", message, cause);
        this.conflictType = conflictType;
        this.entityType = entityType;
        this.conflictField = conflictField;
        this.conflictValue = conflictValue;
    }
    
    /**
     * 获取冲突类型
     *
     * @return 冲突类型
     */
    public ConflictType getConflictType() {
        return conflictType;
    }
    
    /**
     * 获取实体类型
     *
     * @return 实体类型
     */
    public String getEntityType() {
        return entityType;
    }
    
    /**
     * 获取冲突字段
     *
     * @return 冲突字段
     */
    public String getConflictField() {
        return conflictField;
    }
    
    /**
     * 获取冲突值
     *
     * @return 冲突值
     */
    public Object getConflictValue() {
        return conflictValue;
    }
    
    @Override
    public String getDetailMessage() {
        StringBuilder sb = new StringBuilder();
        sb.append("Data conflict");
        
        if (conflictType != null) {
            sb.append(" (").append(conflictType).append(")");
        }
        
        if (entityType != null) {
            sb.append(" for entity: ").append(entityType);
        }
        
        if (conflictField != null) {
            sb.append(" on field: ").append(conflictField);
        }
        
        if (conflictValue != null) {
            sb.append(" with value: ").append(conflictValue);
        }
        
        sb.append(", message: ").append(getMessage());
        
        return sb.toString();
    }
    
    /**
     * 创建唯一约束冲突异常
     *
     * @param entityClass 实体类
     * @param field       冲突字段
     * @param value       冲突值
     * @return 异常实例
     */
    public static DataConflictException uniqueConstraint(Class<?> entityClass, String field, Object value) {
        return new DataConflictException(
                ConflictType.UNIQUE_CONSTRAINT,
                entityClass.getSimpleName(),
                field,
                value,
                String.format("Unique constraint violation: %s.%s = %s already exists", 
                        entityClass.getSimpleName(), field, value)
        );
    }
    
    /**
     * 创建乐观锁冲突异常
     *
     * @param entityClass 实体类
     * @param id          实体ID
     * @return 异常实例
     */
    public static DataConflictException optimisticLock(Class<?> entityClass, Object id) {
        return new DataConflictException(
                ConflictType.OPTIMISTIC_LOCK,
                entityClass.getSimpleName(),
                "version",
                null,
                String.format("Optimistic lock conflict: %s with id %s has been modified by another transaction", 
                        entityClass.getSimpleName(), id)
        );
    }
    
    /**
     * 创建外键约束冲突异常
     *
     * @param entityClass 实体类
     * @param field       外键字段
     * @param value       外键值
     * @return 异常实例
     */
    public static DataConflictException foreignKey(Class<?> entityClass, String field, Object value) {
        return new DataConflictException(
                ConflictType.FOREIGN_KEY,
                entityClass.getSimpleName(),
                field,
                value,
                String.format("Foreign key constraint violation: %s.%s = %s", 
                        entityClass.getSimpleName(), field, value)
        );
    }
    
    /**
     * 创建业务规则冲突异常
     *
     * @param entityClass 实体类
     * @param rule        业务规则描述
     * @return 异常实例
     */
    public static DataConflictException businessRule(Class<?> entityClass, String rule) {
        return new DataConflictException(
                ConflictType.BUSINESS_RULE,
                entityClass.getSimpleName(),
                null,
                null,
                String.format("Business rule violation for %s: %s", entityClass.getSimpleName(), rule)
        );
    }
}
