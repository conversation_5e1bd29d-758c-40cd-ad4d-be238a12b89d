# HandThing App Connector 快速开始指南

## 概述

本指南将帮助您在5分钟内快速集成HandThing App Connector Framework，实现第三方平台的连接和使用。

## 前置条件

- Java 17+
- Spring Boot 3.x
- Maven 3.6+

## 步骤1: 添加依赖

在您的Spring Boot项目中添加以下依赖：

```xml
<dependencies>
    <!-- 核心启动器 -->
    <dependency>
        <groupId>cn.com.handthing.springboot3.starter</groupId>
        <artifactId>app-connector-spring-boot-starter</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </dependency>
    
    <!-- 选择需要的平台连接器 -->
    <dependency>
        <groupId>cn.com.handthing.springboot3.starter</groupId>
        <artifactId>wechat-connector</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </dependency>
    
    <dependency>
        <groupId>cn.com.handthing.springboot3.starter</groupId>
        <artifactId>wecom-connector</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </dependency>
</dependencies>
```

## 步骤2: 配置文件

在`application.yml`中添加配置：

```yaml
handthing:
  connector:
    # 微信配置
    wechat:
      enabled: true
      app-id: "your-wechat-app-id"
      app-secret: "your-wechat-app-secret"
      account-type: "service"
      redirect-uri: "http://localhost:8080/callback/wechat"
    
    # 企业微信配置
    wecom:
      enabled: true
      corp-id: "your-corp-id"
      corp-secret: "your-corp-secret"
      agent-id: "your-agent-id"
      redirect-uri: "http://localhost:8080/callback/wecom"
```

## 步骤3: 创建控制器

创建一个简单的控制器来测试功能：

```java
@RestController
@RequestMapping("/demo")
public class DemoController {
    
    @Autowired
    private AuthService authService;
    
    @Autowired
    private WeChatService weChatService;
    
    @Autowired
    private WeComService weComService;
    
    // 获取微信授权URL
    @GetMapping("/wechat/auth")
    public String getWeChatAuthUrl() {
        return authService.getAuthorizationUrl(
            PlatformType.WECHAT, 
            "demo-state", 
            "http://localhost:8080/callback/wechat"
        );
    }
    
    // 获取企业微信授权URL
    @GetMapping("/wecom/auth")
    public String getWeComAuthUrl() {
        return authService.getAuthorizationUrl(
            PlatformType.WECOM, 
            "demo-state", 
            "http://localhost:8080/callback/wecom"
        );
    }
    
    // 处理微信回调
    @GetMapping("/callback/wechat")
    public Map<String, Object> handleWeChatCallback(@RequestParam String code, 
                                                   @RequestParam String state) {
        try {
            UnifiedAccessToken token = authService.exchangeToken(
                PlatformType.WECHAT, code, state, null);
            
            UnifiedUserInfo userInfo = authService.getUserInfo(
                PlatformType.WECHAT, token.getAccessToken());
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("token", token);
            result.put("userInfo", userInfo);
            return result;
            
        } catch (Exception e) {
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("error", e.getMessage());
            return result;
        }
    }
    
    // 发送微信消息
    @PostMapping("/wechat/message")
    public Map<String, Object> sendWeChatMessage(@RequestParam String accessToken,
                                                @RequestParam String openId,
                                                @RequestParam String content) {
        return weChatService.message().sendTextMessage(accessToken, openId, content);
    }
    
    // 发送企业微信消息
    @PostMapping("/wecom/message")
    public Map<String, Object> sendWeComMessage(@RequestParam String accessToken,
                                               @RequestParam String userId,
                                               @RequestParam String content) {
        return weComService.message().sendTextMessage(accessToken, userId, content);
    }
}
```

## 步骤4: 启动应用

启动您的Spring Boot应用：

```bash
mvn spring-boot:run
```

## 步骤5: 测试功能

### 测试授权流程

1. 访问 `http://localhost:8080/demo/wechat/auth` 获取微信授权URL
2. 在浏览器中打开授权URL，完成授权
3. 系统会自动跳转到回调地址并处理授权结果

### 测试消息发送

```bash
# 发送微信消息
curl -X POST "http://localhost:8080/demo/wechat/message" \
  -d "accessToken=YOUR_ACCESS_TOKEN" \
  -d "openId=USER_OPEN_ID" \
  -d "content=Hello from HandThing!"

# 发送企业微信消息
curl -X POST "http://localhost:8080/demo/wecom/message" \
  -d "accessToken=YOUR_ACCESS_TOKEN" \
  -d "userId=USER_ID" \
  -d "content=Hello from HandThing!"
```

## 进阶使用

### 1. 添加更多平台

```xml
<!-- 添加钉钉连接器 -->
<dependency>
    <groupId>cn.com.handthing.springboot3.starter</groupId>
    <artifactId>dingtalk-connector</artifactId>
    <version>1.0.0-SNAPSHOT</version>
</dependency>

<!-- 添加抖音连接器 -->
<dependency>
    <groupId>cn.com.handthing.springboot3.starter</groupId>
    <artifactId>douyin-connector</artifactId>
    <version>1.0.0-SNAPSHOT</version>
</dependency>
```

### 2. 配置Token管理

```yaml
handthing:
  connector:
    token:
      auto-refresh: true
      refresh-threshold: 300
    cache:
      enabled: true
      type: "memory"
      ttl: 3600
```

### 3. 使用Token管理器

```java
@Service
public class UserTokenService {
    
    @Autowired
    private TokenManager tokenManager;
    
    public void saveUserToken(String userId, PlatformType platform, UnifiedAccessToken token) {
        tokenManager.storeToken(userId, platform, token);
    }
    
    public UnifiedAccessToken getUserToken(String userId, PlatformType platform) {
        return tokenManager.getToken(userId, platform);
    }
    
    public void refreshUserToken(String userId, PlatformType platform) {
        tokenManager.refreshToken(userId, platform);
    }
}
```

### 4. 统一消息发送服务

```java
@Service
public class UnifiedMessageService {
    
    @Autowired
    private List<PlatformService> platformServices;
    
    public void sendMessageToAllPlatforms(String userId, String content) {
        platformServices.forEach(service -> {
            try {
                // 获取用户在该平台的Token
                UnifiedAccessToken token = getTokenForPlatform(userId, service.getPlatformType());
                if (token != null) {
                    service.sendMessage(token.getAccessToken(), userId, content);
                }
            } catch (Exception e) {
                log.error("Failed to send message via {}", service.getPlatformType(), e);
            }
        });
    }
    
    private UnifiedAccessToken getTokenForPlatform(String userId, PlatformType platform) {
        // 实现获取Token的逻辑
        return null;
    }
}
```

## 常见问题

### Q: 如何获取各平台的应用凭证？
A: 请参考各平台连接器的README文档，里面有详细的申请步骤。

### Q: 支持哪些Spring Boot版本？
A: 目前支持Spring Boot 3.x版本，Java 17+。

### Q: 如何处理Token过期？
A: 框架提供自动Token刷新功能，只需要在配置中启用`auto-refresh: true`。

### Q: 如何添加自定义平台？
A: 实现`AuthProvider`接口和相应的服务类，然后注册到Spring容器中。

## 下一步

- 查看 [API文档](API.md) 了解详细的接口说明
- 查看 [配置参考](CONFIGURATION.md) 了解所有配置选项
- 查看各平台连接器的README文档了解平台特定功能
- 运行测试应用查看完整示例

## 获取帮助

- 📧 邮箱: <EMAIL>
- 📖 文档: [在线文档](https://docs.handthing.com.cn)
- 🐛 问题反馈: [GitHub Issues](https://github.com/handthing/app-connector/issues)

---

恭喜！您已经成功集成了HandThing App Connector Framework。现在可以开始构建强大的多平台集成应用了！
