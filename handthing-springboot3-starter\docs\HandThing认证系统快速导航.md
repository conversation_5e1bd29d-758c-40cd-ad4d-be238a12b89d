# HandThing认证系统快速导航

## 🚀 快速定位指南

### 🔍 我想要...

#### 添加新的认证方式
1. **创建认证提供者** → `handthing-auth/auth-core/src/main/java/.../core/AuthenticationProvider.java`
2. **实现认证请求** → 参考 `SmsAuthenticationRequest.java`
3. **配置自动装配** → 参考 `SmsAuthAutoConfiguration.java`
4. **添加到控制器** → `AuthenticationController.createSpecificAuthenticationRequest()`

#### 修改JWT配置
1. **JWT属性配置** → `handthing-auth/auth-spring-boot-starter/src/main/java/.../config/JwtProperties.java`
2. **JWT生成器** → `handthing-auth/auth-spring-boot-starter/src/main/java/.../jwt/JwtTokenGenerator.java`
3. **JWT验证器** → `handthing-auth/auth-spring-boot-starter/src/main/java/.../jwt/JwtTokenValidator.java`
4. **配置文件** → `test-app/src/main/resources/application.yml`

#### 调试认证问题
1. **认证管理器** → `handthing-auth/auth-spring-boot-starter/src/main/java/.../AuthenticationManager.java`
2. **认证过滤器** → `handthing-auth/auth-spring-boot-starter/src/main/java/.../AuthenticationFilter.java`
3. **提供者注册表** → `handthing-auth/auth-spring-boot-starter/src/main/java/.../ProviderRegistry.java`
4. **故障排除文档** → `docs/HandThing认证系统故障排除指南.md`

#### 添加第三方平台
1. **第三方提供者** → `handthing-auth/third-party-provider-starter/src/main/java/.../`
2. **API服务接口** → 参考 `WecomApiService.java`
3. **认证请求类** → 参考 `WecomAuthenticationRequest.java`
4. **配置属性类** → 参考 `WecomProperties.java`

#### 自定义缓存策略
1. **缓存服务接口** → `cache-spring-boot-starter/src/main/java/.../CacheService.java`
2. **Caffeine缓存** → `caffeine-cache-spring-boot-starter/src/main/java/.../CaffeineCacheService.java`
3. **缓存配置** → `handthing-auth/auth-spring-boot-starter/src/main/java/.../config/AuthProperties.java`

#### 监控认证指标
1. **认证事件** → `handthing-auth/auth-spring-boot-starter/src/main/java/.../event/`
2. **事件总线** → `handthing-auth/auth-spring-boot-starter/src/main/java/.../event/AuthenticationBus.java`
3. **健康检查** → `handthing-auth/auth-spring-boot-starter/src/main/java/.../health/AuthenticationHealthIndicator.java`
4. **监控端点** → `/actuator/health`, `/actuator/metrics`

## 📁 按功能分类的文件定位

### 🔐 认证核心功能

#### 认证流程控制
```
AuthenticationManager.java              # 认证管理器 - 核心协调器
├── authenticate()                      # 主认证方法
├── findAuthenticationProvider()        # 查找认证提供者
├── preAuthenticate()                   # 认证前处理
└── postAuthenticate()                  # 认证后处理

AuthenticationController.java           # 认证控制器 - HTTP接口
├── login()                            # 统一登录接口
├── createSpecificAuthenticationRequest() # 创建具体认证请求
├── sendSmsCode()                      # 发送短信验证码
└── getXxxAuthUrl()                    # 获取第三方授权URL
```

#### 认证提供者
```
AuthenticationProvider.java             # 认证提供者接口
├── supports()                         # 判断是否支持请求
├── authenticate()                     # 执行认证
├── validateToken()                    # 验证令牌
└── refreshToken()                     # 刷新令牌

PasswordAuthenticationProvider.java     # 密码认证提供者
SmsAuthenticationProvider.java          # 短信认证提供者
WecomAuthenticationProvider.java        # 企业微信认证提供者
DingtalkAuthenticationProvider.java     # 钉钉认证提供者
WechatAuthenticationProvider.java       # 微信认证提供者
FeishuAuthenticationProvider.java       # 飞书认证提供者
```

### 🎫 JWT Token管理

#### JWT核心组件
```
JwtTokenGenerator.java                   # JWT生成器
├── generateAccessToken()               # 生成访问令牌
├── generateRefreshToken()              # 生成刷新令牌
└── createJwtClaims()                   # 创建JWT声明

JwtTokenValidator.java                   # JWT验证器
├── validateToken()                     # 验证令牌
├── parseToken()                        # 解析令牌
└── isTokenExpired()                    # 检查令牌是否过期

JwtTokenParser.java                      # JWT解析器
├── parseAccessToken()                  # 解析访问令牌
├── parseRefreshToken()                 # 解析刷新令牌
└── extractClaims()                     # 提取声明
```

### 🛡️ 安全防护

#### 过滤器和拦截器
```
AuthenticationFilter.java               # 认证过滤器
├── doFilter()                         # 过滤器主方法
├── shouldSkipAuthentication()          # 判断是否跳过认证
└── extractTokenFromRequest()           # 从请求中提取令牌

AuthenticationInterceptor.java          # 认证拦截器
├── preHandle()                        # 预处理
├── checkAuthentication()              # 检查认证
└── checkPermissions()                 # 检查权限
```

### 📊 事件系统

#### 事件定义
```
AuthenticationStartedEvent.java         # 认证开始事件
AuthenticationSuccessEvent.java         # 认证成功事件
AuthenticationFailedEvent.java          # 认证失败事件
AuthenticationCompletedEvent.java       # 认证完成事件

AuthenticationBus.java                   # 认证事件总线
├── publishAuthenticationStartedEvent() # 发布认证开始事件
├── publishAuthenticationSuccessEvent() # 发布认证成功事件
├── publishAuthenticationFailedEvent()  # 发布认证失败事件
└── publishAuthenticationCompletedEvent() # 发布认证完成事件
```

### ⚙️ 配置管理

#### 配置类
```
AuthProperties.java                      # 认证主配置
├── JwtProperties                       # JWT配置
├── FilterProperties                    # 过滤器配置
├── CacheProperties                     # 缓存配置
└── EventProperties                     # 事件配置

SmsAuthProperties.java                   # 短信认证配置
ThirdPartyAuthProperties.java           # 第三方认证配置
├── WecomProperties                     # 企业微信配置
├── DingtalkProperties                  # 钉钉配置
├── WechatProperties                    # 微信配置
└── FeishuProperties                    # 飞书配置
```

#### 自动配置
```
AuthAutoConfiguration.java              # 认证自动配置
├── authenticationManager()             # 认证管理器Bean
├── authenticationFilter()              # 认证过滤器Bean
├── jwtTokenGenerator()                 # JWT生成器Bean
└── authenticationBus()                 # 事件总线Bean

SmsAuthAutoConfiguration.java           # 短信认证自动配置
ThirdPartyAuthAutoConfiguration.java    # 第三方认证自动配置
```

## 🔧 常用操作快速指南

### 添加新认证提供者

1. **创建认证请求类**
```java
// 位置: your-provider-starter/src/main/java/.../YourAuthenticationRequest.java
public class YourAuthenticationRequest extends AuthenticationRequest {
    // 实现具体的认证请求
}
```

2. **创建认证提供者**
```java
// 位置: your-provider-starter/src/main/java/.../YourAuthenticationProvider.java
@Component
public class YourAuthenticationProvider implements AuthenticationProvider {
    @Override
    public GrantType getSupportedGrantType() {
        return GrantType.YOUR_TYPE;
    }
    
    @Override
    public boolean supports(AuthenticationRequest request) {
        return request instanceof YourAuthenticationRequest;
    }
    
    @Override
    public AuthenticationResponse authenticate(AuthenticationRequest request) {
        // 实现认证逻辑
    }
}
```

3. **添加到控制器**
```java
// 位置: AuthenticationController.java
private AuthenticationRequest createYourAuthenticationRequest(Map<String, Object> loginRequest) {
    // 创建具体认证请求的逻辑
}
```

### 修改JWT配置

1. **修改配置文件**
```yaml
# 位置: application.yml
handthing:
  auth:
    jwt:
      secret: your-new-secret
      access-token-expiration: 3600
```

2. **自定义JWT生成器**
```java
// 位置: 自定义配置类
@Bean
@Primary
public JwtTokenGenerator customJwtTokenGenerator() {
    return new CustomJwtTokenGenerator();
}
```

### 添加认证事件监听器

```java
// 位置: 任意Spring组件
@Component
public class CustomAuthenticationEventListener {
    
    @EventListener
    public void handleAuthenticationSuccess(AuthenticationSuccessEvent event) {
        // 处理认证成功事件
    }
    
    @EventListener
    public void handleAuthenticationFailure(AuthenticationFailedEvent event) {
        // 处理认证失败事件
    }
}
```

### 自定义认证过滤器排除路径

```yaml
# 位置: application.yml
handthing:
  auth:
    filter:
      exclude-paths:
        - "/your-custom-path"
        - "/api/public/**"
```

## 📋 开发检查清单

### 新功能开发
- [ ] 创建认证请求类
- [ ] 实现认证提供者
- [ ] 添加配置属性
- [ ] 创建自动配置类
- [ ] 更新控制器方法
- [ ] 编写单元测试
- [ ] 更新文档

### 问题排查
- [ ] 检查认证提供者是否注册
- [ ] 验证配置属性是否正确
- [ ] 查看认证过滤器日志
- [ ] 检查JWT配置
- [ ] 验证事件是否正常发布
- [ ] 查看健康检查状态

### 性能优化
- [ ] 启用缓存
- [ ] 优化JWT配置
- [ ] 调整连接池参数
- [ ] 监控认证指标
- [ ] 分析认证耗时

---

**© 2024 HandThing. All rights reserved.**
