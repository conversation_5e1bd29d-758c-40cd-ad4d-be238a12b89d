package cn.com.handthing.starter.auth.thirdparty;

import cn.com.handthing.starter.auth.core.AuthenticationResponse;
import cn.com.handthing.starter.auth.core.UserInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 飞书认证响应
 * <p>
 * 飞书认证的响应对象，包含认证结果、用户信息、令牌信息等。
 * 支持新用户注册和老用户登录的不同响应信息。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FeishuAuthenticationResponse extends AuthenticationResponse {

    /**
     * 用户信息
     */
    private UserInfo userInfo;

    /**
     * 是否为新注册用户
     */
    private Boolean newUser;

    /**
     * 飞书用户ID
     */
    private String feishuUserId;

    /**
     * 飞书用户名
     */
    private String feishuUsername;

    /**
     * 飞书头像
     */
    private String feishuAvatar;

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 租户Key
     */
    private String tenantKey;

    /**
     * 最后登录时间
     */
    private LocalDateTime lastLoginTime;

    /**
     * 最后登录IP
     */
    private String lastLoginIp;

    /**
     * 是否为首次登录
     */
    private Boolean firstLogin;

    /**
     * 默认构造函数
     */
    public FeishuAuthenticationResponse() {
        super();
    }

    /**
     * 成功响应构造函数
     *
     * @param accessToken  访问令牌
     * @param refreshToken 刷新令牌
     * @param expiresIn    过期时间
     * @param userInfo     用户信息
     */
    public FeishuAuthenticationResponse(String accessToken, String refreshToken, Long expiresIn, UserInfo userInfo) {
        super(accessToken, refreshToken, expiresIn);
        this.userInfo = userInfo;
        if (userInfo != null) {
            setUserId(userInfo.getUserId());
            setUsername(userInfo.getUsername());
        }
    }

    /**
     * 失败响应构造函数
     *
     * @param errorCode        错误代码
     * @param errorDescription 错误描述
     */
    public FeishuAuthenticationResponse(String errorCode, String errorDescription) {
        super(errorCode, errorDescription);
    }

    /**
     * 创建成功响应
     *
     * @param accessToken  访问令牌
     * @param refreshToken 刷新令牌
     * @param expiresIn    过期时间
     * @param userInfo     用户信息
     * @return 飞书认证响应
     */
    public static FeishuAuthenticationResponse success(String accessToken, String refreshToken, 
                                                      Long expiresIn, UserInfo userInfo) {
        return new FeishuAuthenticationResponse(accessToken, refreshToken, expiresIn, userInfo);
    }

    /**
     * 创建成功响应（新用户）
     *
     * @param accessToken  访问令牌
     * @param refreshToken 刷新令牌
     * @param expiresIn    过期时间
     * @param userInfo     用户信息
     * @return 飞书认证响应
     */
    public static FeishuAuthenticationResponse successForNewUser(String accessToken, String refreshToken, 
                                                                Long expiresIn, UserInfo userInfo) {
        FeishuAuthenticationResponse response = new FeishuAuthenticationResponse(accessToken, refreshToken, expiresIn, userInfo);
        response.setNewUser(true);
        response.setFirstLogin(true);
        return response;
    }

    /**
     * 创建失败响应
     *
     * @param errorCode        错误代码
     * @param errorDescription 错误描述
     * @return 飞书认证响应
     */
    public static FeishuAuthenticationResponse failure(String errorCode, String errorDescription) {
        return new FeishuAuthenticationResponse(errorCode, errorDescription);
    }

    /**
     * 创建授权码无效响应
     *
     * @return 飞书认证响应
     */
    public static FeishuAuthenticationResponse invalidCode() {
        return failure("INVALID_FEISHU_CODE", "飞书授权码无效或已过期");
    }

    /**
     * 创建应用配置错误响应
     *
     * @param appId 应用ID
     * @return 飞书认证响应
     */
    public static FeishuAuthenticationResponse invalidAppConfig(String appId) {
        return failure("INVALID_APP_CONFIG", "飞书应用配置错误: " + appId);
    }

    /**
     * 创建用户不存在响应
     *
     * @param feishuUserId 飞书用户ID
     * @return 飞书认证响应
     */
    public static FeishuAuthenticationResponse userNotFound(String feishuUserId) {
        return failure("FEISHU_USER_NOT_FOUND", "飞书用户不存在: " + feishuUserId);
    }

    /**
     * 创建用户被锁定响应
     *
     * @param feishuUserId 飞书用户ID
     * @return 飞书认证响应
     */
    public static FeishuAuthenticationResponse userLocked(String feishuUserId) {
        return failure("USER_LOCKED", "用户已被锁定: " + feishuUserId);
    }

    /**
     * 创建用户被禁用响应
     *
     * @param feishuUserId 飞书用户ID
     * @return 飞书认证响应
     */
    public static FeishuAuthenticationResponse userDisabled(String feishuUserId) {
        return failure("USER_DISABLED", "用户已被禁用: " + feishuUserId);
    }

    /**
     * 创建注册失败响应
     *
     * @param reason 失败原因
     * @return 飞书认证响应
     */
    public static FeishuAuthenticationResponse registrationFailed(String reason) {
        return failure("REGISTRATION_FAILED", "用户注册失败: " + reason);
    }

    /**
     * 创建API调用失败响应
     *
     * @param reason 失败原因
     * @return 飞书认证响应
     */
    public static FeishuAuthenticationResponse apiCallFailed(String reason) {
        return failure("FEISHU_API_FAILED", "飞书API调用失败: " + reason);
    }

    /**
     * 创建租户验证失败响应
     *
     * @param tenantKey 租户Key
     * @return 飞书认证响应
     */
    public static FeishuAuthenticationResponse tenantValidationFailed(String tenantKey) {
        return failure("TENANT_VALIDATION_FAILED", "租户验证失败: " + tenantKey);
    }

    /**
     * 设置用户信息并同步基础字段
     *
     * @param userInfo 用户信息
     */
    public void setUserInfo(UserInfo userInfo) {
        this.userInfo = userInfo;
        if (userInfo != null) {
            setUserId(userInfo.getUserId());
            setUsername(userInfo.getUsername());
            this.lastLoginTime = userInfo.getLastLoginTime();
            this.lastLoginIp = userInfo.getLastLoginIp();
        }
    }

    /**
     * 设置飞书用户信息
     *
     * @param feishuUserId   飞书用户ID
     * @param feishuUsername 飞书用户名
     * @param feishuAvatar   飞书头像
     * @param appId          应用ID
     * @param tenantKey      租户Key
     */
    public void setFeishuUserInfo(String feishuUserId, String feishuUsername, String feishuAvatar, 
                                 String appId, String tenantKey) {
        this.feishuUserId = feishuUserId;
        this.feishuUsername = feishuUsername;
        this.feishuAvatar = feishuAvatar;
        this.appId = appId;
        this.tenantKey = tenantKey;
    }

    @Override
    public String toString() {
        if (isSuccess()) {
            return String.format("FeishuAuthenticationResponse{success=true, userId='%s', feishuUserId='%s', appId='%s', newUser=%s, firstLogin=%s}",
                    getUserId(), feishuUserId, appId, newUser, firstLogin);
        } else {
            return String.format("FeishuAuthenticationResponse{success=false, errorCode='%s', errorDescription='%s'}",
                    getErrorCode(), getErrorDescription());
        }
    }
}
