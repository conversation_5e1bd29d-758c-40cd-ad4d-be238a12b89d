package cn.com.handthing.starter.connector.wechat.config;

import cn.com.handthing.starter.connector.config.PlatformConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 微信连接器配置
 * <p>
 * 支持微信公众号和小程序的配置，包括AppID、AppSecret等认证信息。
 * 支持沙箱环境和生产环境的切换。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ConfigurationProperties(prefix = "handthing.connector.wechat")
public class WeChatConfig extends PlatformConfig {

    /**
     * 微信应用ID (AppID)
     */
    private String appId;

    /**
     * 微信应用密钥 (AppSecret)
     */
    private String appSecret;

    /**
     * 微信公众号原始ID
     */
    private String originalId;

    /**
     * 微信公众号类型
     * subscription: 订阅号
     * service: 服务号
     * enterprise: 企业号
     * miniprogram: 小程序
     */
    private String accountType = "service";

    /**
     * 是否为小程序模式
     */
    private boolean miniProgramMode = false;

    /**
     * 消息加密密钥 (EncodingAESKey)
     */
    private String encodingAESKey;

    /**
     * 消息校验Token
     */
    private String token;

    /**
     * 是否启用消息加密
     */
    private boolean encryptMessage = false;

    /**
     * 微信API基础URL
     */
    private String apiBaseUrl = "https://api.weixin.qq.com";

    /**
     * 是否使用沙箱环境
     */
    private boolean sandboxMode = false;

    /**
     * 沙箱环境API基础URL
     */
    private String sandboxApiBaseUrl = "https://api.weixin.qq.com/sandbox";

    /**
     * 获取实际使用的API基础URL
     *
     * @return API基础URL
     */
    public String getEffectiveApiBaseUrl() {
        return sandboxMode ? sandboxApiBaseUrl : apiBaseUrl;
    }

    /**
     * 验证配置是否有效
     *
     * @return 如果配置有效返回true，否则返回false
     */
    @Override
    public boolean isValid() {
        if (!super.isValid()) {
            return false;
        }

        // 检查必需的配置项
        if (appId == null || appId.trim().isEmpty()) {
            return false;
        }

        if (appSecret == null || appSecret.trim().isEmpty()) {
            return false;
        }

        return true;
    }

    /**
     * 获取配置描述信息
     *
     * @return 配置描述
     */
    public String getConfigDescription() {
        return String.format("WeChat Config - AppId: %s, AccountType: %s, MiniProgram: %s, Sandbox: %s",
                appId, accountType, miniProgramMode, sandboxMode);
    }

    /**
     * 是否为企业微信模式
     *
     * @return 如果是企业微信返回true，否则返回false
     */
    public boolean isEnterpriseMode() {
        return "enterprise".equals(accountType);
    }

    /**
     * 是否为订阅号
     *
     * @return 如果是订阅号返回true，否则返回false
     */
    public boolean isSubscriptionAccount() {
        return "subscription".equals(accountType);
    }

    /**
     * 是否为服务号
     *
     * @return 如果是服务号返回true，否则返回false
     */
    public boolean isServiceAccount() {
        return "service".equals(accountType);
    }
}
