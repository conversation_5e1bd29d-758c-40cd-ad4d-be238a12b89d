# HandThing认证系统

[![Spring Boot](https://img.shields.io/badge/Spring%20Boot-3.5.3-brightgreen.svg)](https://spring.io/projects/spring-boot)
[![Java](https://img.shields.io/badge/Java-21-orange.svg)](https://openjdk.java.net/)
[![License](https://img.shields.io/badge/License-MIT-blue.svg)](LICENSE)
[![Build Status](https://img.shields.io/badge/Build-Passing-brightgreen.svg)](https://github.com/handthing/handthing-springboot3-starter)

> 🚀 基于Spring Boot 3的企业级多认证方式统一认证框架

## 🎯 项目概述

HandThing认证系统是一个功能完整、高性能、易扩展的企业级认证解决方案。支持多种认证方式，提供完整的JWT token管理，具备事件驱动架构和全面的监控能力。

### ✨ 核心特性

- 🔐 **多认证方式** - 密码、短信、企业微信、钉钉、微信、飞书
- 🎫 **JWT Token管理** - 完整的token生成、验证、刷新机制
- 📊 **事件驱动架构** - 认证生命周期事件监控
- ⚡ **高性能缓存** - Caffeine本地缓存 + Redis分布式缓存
- 🛡️ **安全防护** - 认证过滤器、权限拦截器、CORS支持
- 📈 **监控指标** - 完整的认证指标和健康检查
- 🔧 **自动配置** - Spring Boot自动配置，开箱即用

### 🏗️ 技术架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用      │───▶│  认证过滤器     │───▶│  认证管理器     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                       │
                       ┌─────────────────┐    ┌─────────────────┐
                       │  JWT验证器      │◀───│ 认证提供者注册表 │
                       └─────────────────┘    └─────────────────┘
                                                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   事件监听器    │◀───│   事件发布器    │◀───│   认证提供者    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Redis缓存      │◀───│   缓存服务      │◀───│  Caffeine缓存   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 快速开始

### 1. 环境要求

- Java 21+
- Spring Boot 3.5.3+
- Redis 6.0+ (可选)
- Maven 3.8+

### 2. 添加依赖

```xml
<dependency>
    <groupId>cn.com.handthing.springboot3.starter</groupId>
    <artifactId>auth-spring-boot-starter</artifactId>
    <version>1.0.0-SNAPSHOT</version>
</dependency>
```

### 3. 配置文件

```yaml
handthing:
  auth:
    enabled: true
    default-grant-type: password
    jwt:
      secret: your-jwt-secret
      access-token-expiration: 7200
    filter:
      exclude-paths:
        - "/login"
        - "/auth/login"
        - "/public/**"
```

### 4. 启用认证

```java
@SpringBootApplication
@EnableHandThingAuth
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
```

### 5. 前端集成

```javascript
// 登录
function login(username, password) {
    fetch('/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            grant_type: 'password',
            username: username,
            password: password
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            localStorage.setItem('access_token', data.access_token);
            window.location.href = '/dashboard';
        }
    });
}

// API调用
function apiCall(url, options = {}) {
    const token = localStorage.getItem('access_token');
    return fetch(url, {
        ...options,
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
}
```

## 📖 支持的认证方式

### 🔑 密码认证

```http
POST /auth/login
{
  "grant_type": "password",
  "username": "admin",
  "password": "admin123"
}
```

### 📱 短信认证

```http
# 发送验证码
POST /auth/sms/send
{
  "phone": "13800138000",
  "code_type": "login"
}

# 短信登录
POST /auth/login
{
  "grant_type": "sms_code",
  "phone": "13800138000",
  "sms_code": "123456"
}
```

### 🏢 第三方认证

支持企业微信、钉钉、微信、飞书等主流平台：

```http
# 获取授权URL
GET /auth/wecom/auth-url?state=xxx&redirect_uri=xxx

# OAuth回调认证
POST /auth/login
{
  "grant_type": "wecom",
  "code": "authorization_code",
  "corp_id": "your-corp-id"
}
```

## 🧪 测试验证

### 测试覆盖率

| 模块 | 覆盖率 | 状态 |
|------|--------|------|
| 基础功能 | 100% | ✅ |
| 密码认证 | 100% | ✅ |
| 短信认证 | 95% | ✅ |
| 第三方认证 | 100% | ✅ |
| JWT Token | 100% | ✅ |
| 事件系统 | 100% | ✅ |

### 性能指标

- **认证响应时间**: 平均 < 100ms
- **Token验证时间**: 平均 < 10ms
- **并发支持**: 1000+ TPS
- **缓存命中率**: > 95%

### 运行测试

```bash
# 单元测试
mvn test

# 集成测试
mvn test -Dtest=*IntegrationTest

# 性能测试
mvn test -Dtest=*PerformanceTest
```

## 📊 监控与运维

### 健康检查

```http
GET /actuator/health
```

### 指标监控

```http
GET /actuator/metrics/handthing.auth.success
GET /actuator/metrics/handthing.auth.failure
GET /actuator/metrics/handthing.auth.duration
```

### 日志配置

```yaml
logging:
  level:
    cn.com.handthing.starter.auth: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
```

## 🔧 配置参考

### 完整配置示例

```yaml
handthing:
  auth:
    # 基础配置
    enabled: true
    default-grant-type: password
    multi-provider-enabled: true
    
    # JWT配置
    jwt:
      secret: ${JWT_SECRET:handthing-auth-secret}
      issuer: handthing-auth
      access-token-expiration: 7200
      refresh-token-expiration: 604800
    
    # 过滤器配置
    filter:
      enabled: true
      order: -100
      exclude-paths:
        - "/login"
        - "/auth/login"
        - "/auth/sms/send"
        - "/auth/*/auth-url"
        - "/*/callback"
        - "/public/**"
    
    # 缓存配置
    cache:
      enabled: true
      type: redis
      redis:
        host: localhost
        port: 6379
    
    # 短信配置
    sms:
      enabled: true
      code-length: 6
      code-expiration: 300
    
    # 第三方配置
    thirdparty:
      wecom:
        enabled: true
        corp-id: ${WECOM_CORP_ID}
        agent-id: ${WECOM_AGENT_ID}
        corp-secret: ${WECOM_CORP_SECRET}
```

## 📚 文档

- [📖 完整文档](docs/HandThing认证系统完整文档.md)
- [🔧 故障排除指南](docs/HandThing认证系统故障排除指南.md)
- [💡 最佳实践指南](docs/HandThing认证系统最佳实践指南.md)

## 🤝 贡献

欢迎提交Issue和Pull Request！

### 开发环境搭建

```bash
# 克隆项目
git clone https://github.com/handthing/handthing-springboot3-starter.git

# 进入项目目录
cd handthing-springboot3-starter

# 编译项目
mvn clean compile

# 运行测试
mvn test

# 启动测试应用
mvn spring-boot:run -pl test-app
```

### 提交规范

- feat: 新功能
- fix: 修复bug
- docs: 文档更新
- style: 代码格式调整
- refactor: 代码重构
- test: 测试相关
- chore: 构建过程或辅助工具的变动

## 📄 许可证

本项目采用 [MIT](LICENSE) 许可证。

## 📞 联系我们

- **项目地址**: https://github.com/handthing/handthing-springboot3-starter
- **文档地址**: https://docs.handthing.com
- **技术支持**: <EMAIL>
- **问题反馈**: https://github.com/handthing/handthing-springboot3-starter/issues

---

**© 2024 HandThing. All rights reserved.**
