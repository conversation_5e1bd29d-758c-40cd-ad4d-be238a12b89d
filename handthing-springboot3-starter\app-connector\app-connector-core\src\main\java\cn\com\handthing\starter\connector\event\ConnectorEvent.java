package cn.com.handthing.starter.connector.event;

import cn.com.handthing.starter.connector.PlatformType;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 连接器基础事件类
 * <p>
 * 所有连接器事件的基类，继承Spring的ApplicationEvent，
 * 包含平台类型、时间戳等基础信息。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Getter
public abstract class ConnectorEvent extends ApplicationEvent {

    private static final long serialVersionUID = 1L;

    /**
     * 平台类型
     */
    private final PlatformType platform;

    /**
     * 事件发生时间
     */
    private final LocalDateTime eventTime;

    /**
     * 事件类型
     */
    private final String eventType;

    /**
     * 额外的事件数据
     */
    private final Map<String, Object> eventData;

    /**
     * 构造函数
     *
     * @param source    事件源
     * @param platform  平台类型
     * @param eventType 事件类型
     */
    public ConnectorEvent(Object source, PlatformType platform, String eventType) {
        this(source, platform, eventType, null);
    }

    /**
     * 构造函数
     *
     * @param source    事件源
     * @param platform  平台类型
     * @param eventType 事件类型
     * @param eventData 额外事件数据
     */
    public ConnectorEvent(Object source, PlatformType platform, String eventType, Map<String, Object> eventData) {
        super(source);
        this.platform = platform;
        this.eventType = eventType;
        this.eventData = eventData;
        this.eventTime = LocalDateTime.now();
    }

    /**
     * 获取事件的唯一标识
     *
     * @return 事件唯一标识
     */
    public String getEventId() {
        return String.format("%s_%s_%d", 
                platform != null ? platform.getCode() : "unknown",
                eventType,
                getTimestamp());
    }

    /**
     * 获取事件描述
     *
     * @return 事件描述
     */
    public String getEventDescription() {
        return String.format("[%s] %s event occurred at %s",
                platform != null ? platform.getDisplayName() : "Unknown Platform",
                eventType,
                eventTime);
    }

    /**
     * 检查是否包含指定的事件数据
     *
     * @param key 数据键
     * @return 如果包含返回true，否则返回false
     */
    public boolean hasEventData(String key) {
        return eventData != null && eventData.containsKey(key);
    }

    /**
     * 获取事件数据
     *
     * @param key 数据键
     * @return 数据值，如果不存在返回null
     */
    public Object getEventData(String key) {
        return eventData != null ? eventData.get(key) : null;
    }

    /**
     * 获取事件数据（指定类型）
     *
     * @param key  数据键
     * @param type 数据类型
     * @param <T>  泛型类型
     * @return 数据值，如果不存在或类型不匹配返回null
     */
    @SuppressWarnings("unchecked")
    public <T> T getEventData(String key, Class<T> type) {
        Object value = getEventData(key);
        if (value != null && type.isInstance(value)) {
            return (T) value;
        }
        return null;
    }

    @Override
    public String toString() {
        return String.format("%s{platform=%s, eventType='%s', eventTime=%s}",
                getClass().getSimpleName(),
                platform,
                eventType,
                eventTime);
    }
}
