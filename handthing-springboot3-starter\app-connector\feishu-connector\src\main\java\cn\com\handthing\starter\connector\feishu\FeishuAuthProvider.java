package cn.com.handthing.starter.connector.feishu;

import cn.com.handthing.starter.connector.PlatformType;
import cn.com.handthing.starter.connector.auth.AuthProvider;
import cn.com.handthing.starter.connector.auth.UnifiedAccessToken;
import cn.com.handthing.starter.connector.auth.UnifiedUserInfo;
import cn.com.handthing.starter.connector.exception.AuthException;
import cn.com.handthing.starter.connector.feishu.config.FeishuConfig;
import cn.com.handthing.starter.connector.feishu.model.FeishuAccessTokenResponse;
import cn.com.handthing.starter.connector.feishu.model.FeishuUserInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 飞书认证提供者
 * <p>
 * 实现飞书OAuth2.0认证流程，支持企业内部应用和第三方应用的用户认证。
 * 提供统一的认证接口，处理授权码交换、Token刷新等操作。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "handthing.connector.feishu", name = "enabled", havingValue = "true")
public class FeishuAuthProvider implements AuthProvider {

    private final FeishuConfig config;
    private final RestTemplate restTemplate;

    @Override
    public PlatformType getPlatformType() {
        return PlatformType.FEISHU;
    }

    @Override
    public String getAuthorizationUrl(String state, String redirectUri) {
        try {
            log.debug("Generating Feishu authorization URL with state: {}, redirectUri: {}", state, redirectUri);

            // 使用配置的回调地址或传入的地址
            String effectiveRedirectUri = redirectUri != null ? redirectUri : config.getRedirectUri();
            if (effectiveRedirectUri == null || effectiveRedirectUri.trim().isEmpty()) {
                throw new AuthException("Redirect URI is required for Feishu authorization");
            }

            // 构建飞书授权URL
            return UriComponentsBuilder.fromHttpUrl(config.getEffectiveApiBaseUrl() + "/open-apis/authen/v1/index")
                    .queryParam("app_id", config.getAppId())
                    .queryParam("redirect_uri", URLEncoder.encode(effectiveRedirectUri, StandardCharsets.UTF_8))
                    .queryParam("state", state)
                    .build()
                    .toUriString();

        } catch (Exception e) {
            log.error("Failed to generate Feishu authorization URL", e);
            throw new AuthException("Failed to generate authorization URL", e);
        }
    }

    @Override
    public UnifiedAccessToken exchangeToken(String code, String state, String redirectUri) {
        try {
            log.debug("Exchanging Feishu authorization code for access token: {}", code);

            // 构建获取access_token的URL
            String tokenUrl = config.getEffectiveApiBaseUrl() + "/open-apis/authen/v1/access_token";

            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("grant_type", "authorization_code");
            requestBody.put("code", code);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + getAppAccessToken());

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);

            // 发送请求获取access_token
            ResponseEntity<FeishuAccessTokenResponse> response = restTemplate.postForEntity(
                    tokenUrl, request, FeishuAccessTokenResponse.class);

            FeishuAccessTokenResponse tokenResponse = response.getBody();
            if (tokenResponse == null) {
                throw new AuthException("Empty response from Feishu token endpoint");
            }

            if (tokenResponse.getCode() != 0) {
                throw new AuthException("Feishu token exchange failed: " + tokenResponse.getMsg());
            }

            // 转换为统一的访问令牌格式
            Map<String, Object> extraInfo = new HashMap<>();
            extraInfo.put("openId", tokenResponse.getData().getOpenId());
            extraInfo.put("unionId", tokenResponse.getData().getUnionId());
            extraInfo.put("tenantKey", tokenResponse.getData().getTenantKey());
            
            return UnifiedAccessToken.builder()
                    .accessToken(tokenResponse.getData().getAccessToken())
                    .refreshToken(tokenResponse.getData().getRefreshToken())
                    .expiresIn(tokenResponse.getData().getExpiresIn())
                    .scope("user:read")
                    .platform(PlatformType.FEISHU)
                    .userId(tokenResponse.getData().getOpenId())
                    .extraInfo(extraInfo)
                    .createdAt(LocalDateTime.now())
                    .build();

        } catch (Exception e) {
            log.error("Failed to exchange Feishu authorization code for token", e);
            throw new AuthException("Failed to exchange code for token", e);
        }
    }

    @Override
    public UnifiedAccessToken refreshToken(String refreshToken) {
        try {
            log.debug("Refreshing Feishu access token");

            // 构建刷新token的URL
            String refreshUrl = config.getEffectiveApiBaseUrl() + "/open-apis/authen/v1/refresh_access_token";

            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("grant_type", "refresh_token");
            requestBody.put("refresh_token", refreshToken);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + getAppAccessToken());

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);

            // 发送请求刷新token
            ResponseEntity<FeishuAccessTokenResponse> response = restTemplate.postForEntity(
                    refreshUrl, request, FeishuAccessTokenResponse.class);

            FeishuAccessTokenResponse tokenResponse = response.getBody();
            if (tokenResponse == null) {
                throw new AuthException("Empty response from Feishu refresh token endpoint");
            }

            if (tokenResponse.getCode() != 0) {
                throw new AuthException("Feishu token refresh failed: " + tokenResponse.getMsg());
            }

            // 转换为统一的访问令牌格式
            Map<String, Object> extraInfo = new HashMap<>();
            extraInfo.put("openId", tokenResponse.getData().getOpenId());
            extraInfo.put("unionId", tokenResponse.getData().getUnionId());
            extraInfo.put("tenantKey", tokenResponse.getData().getTenantKey());
            
            return UnifiedAccessToken.builder()
                    .accessToken(tokenResponse.getData().getAccessToken())
                    .refreshToken(tokenResponse.getData().getRefreshToken())
                    .expiresIn(tokenResponse.getData().getExpiresIn())
                    .scope("user:read")
                    .platform(PlatformType.FEISHU)
                    .userId(tokenResponse.getData().getOpenId())
                    .extraInfo(extraInfo)
                    .createdAt(LocalDateTime.now())
                    .build();

        } catch (Exception e) {
            log.error("Failed to refresh Feishu access token", e);
            throw new AuthException("Failed to refresh token", e);
        }
    }

    @Override
    public UnifiedUserInfo getUserInfo(String accessToken) {
        try {
            log.debug("Getting Feishu user info with access token");

            // 构建获取用户信息的URL
            String userInfoUrl = config.getEffectiveApiBaseUrl() + "/open-apis/authen/v1/user_info";

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", "Bearer " + accessToken);

            HttpEntity<Void> request = new HttpEntity<>(headers);

            // 发送请求获取用户信息
            ResponseEntity<FeishuUserInfo> response = restTemplate.exchange(
                    userInfoUrl, HttpMethod.GET, request, FeishuUserInfo.class);

            FeishuUserInfo userInfo = response.getBody();
            if (userInfo == null) {
                throw new AuthException("Empty response from Feishu user info endpoint");
            }

            if (userInfo.getCode() != 0) {
                throw new AuthException("Feishu get user info failed: " + userInfo.getMsg());
            }

            // 转换为统一的用户信息格式
            return UnifiedUserInfo.builder()
                    .openId(userInfo.getData().getOpenId())
                    .unionId(userInfo.getData().getUnionId())
                    .nickname(userInfo.getData().getName())
                    .avatar(userInfo.getData().getAvatarUrl())
                    .email(userInfo.getData().getEmail())
                    .mobile(userInfo.getData().getMobile())
                    .platform(PlatformType.FEISHU)
                    .build();

        } catch (Exception e) {
            log.error("Failed to get Feishu user info", e);
            throw new AuthException("Failed to get user info", e);
        }
    }

    @Override
    public boolean validateToken(String accessToken) {
        try {
            log.debug("Validating Feishu access token");

            // 通过获取用户信息来验证token
            getUserInfo(accessToken);
            return true;

        } catch (Exception e) {
            log.error("Failed to validate Feishu access token", e);
            return false;
        }
    }

    /**
     * 获取应用访问令牌
     * 
     * @return 应用访问令牌
     */
    private String getAppAccessToken() {
        try {
            // 构建获取app_access_token的URL
            String tokenUrl = config.getEffectiveApiBaseUrl() + "/open-apis/auth/v3/app_access_token/internal";

            // 构建请求体
            Map<String, String> requestBody = new HashMap<>();
            requestBody.put("app_id", config.getAppId());
            requestBody.put("app_secret", config.getAppSecret());

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Map<String, String>> request = new HttpEntity<>(requestBody, headers);

            // 发送请求获取app_access_token
            ResponseEntity<Map> response = restTemplate.postForEntity(tokenUrl, request, Map.class);
            Map<String, Object> result = response.getBody();

            if (result == null || (Integer) result.get("code") != 0) {
                throw new AuthException("Failed to get Feishu app access token");
            }

            return (String) result.get("app_access_token");

        } catch (Exception e) {
            log.error("Failed to get Feishu app access token", e);
            throw new AuthException("Failed to get app access token", e);
        }
    }
}
