# HandThing 多租户认证系统开发指南

## 概述

本文档为 HandThing 多租户认证系统的开发者提供详细的开发指南，包括项目结构、开发环境搭建、编码规范、测试指南等内容。

## 项目结构

```
tenant-auth-starter/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── cn/com/handthing/starter/tenant/
│   │   │       ├── config/                    # 配置类
│   │   │       │   ├── SaaSAuthAutoConfiguration.java
│   │   │       │   ├── SaaSAuthProperties.java
│   │   │       │   ├── TenantResolverProperties.java
│   │   │       │   └── TenantConfig.java      # JPA实体
│   │   │       ├── context/                   # 上下文管理
│   │   │       │   ├── TenantContextHolder.java
│   │   │       │   ├── Tenant.java
│   │   │       │   └── TenantContextCleanupFilter.java
│   │   │       ├── filter/                    # 过滤器
│   │   │       │   └── TenantResolverFilter.java
│   │   │       ├── resolver/                  # 租户解析器
│   │   │       │   ├── TenantResolver.java
│   │   │       │   └── HttpHeaderTenantResolver.java
│   │   │       ├── service/                   # 服务层
│   │   │       │   ├── TenantConfigService.java
│   │   │       │   └── impl/
│   │   │       │       └── TenantConfigServiceImpl.java
│   │   │       ├── repository/                # 数据访问层
│   │   │       │   └── TenantConfigRepository.java
│   │   │       ├── controller/                # 控制器
│   │   │       │   └── TenantController.java
│   │   │       ├── exception/                 # 异常处理
│   │   │       │   ├── TenantException.java
│   │   │       │   └── TenantNotFoundException.java
│   │   │       └── util/                      # 工具类
│   │   │           └── TenantUtils.java
│   │   └── resources/
│   │       ├── META-INF/
│   │       │   └── spring.factories           # 自动配置
│   │       └── db/migration/                  # 数据库迁移脚本
│   │           └── V1__Create_tenant_tables.sql
│   └── test/
│       ├── java/
│       │   └── cn/com/handthing/starter/tenant/
│       │       ├── config/
│       │       ├── service/
│       │       ├── filter/
│       │       └── integration/
│       └── resources/
│           ├── application-test.yml
│           └── test-data.sql
├── docs/                                      # 文档
│   ├── README.md
│   ├── API.md
│   ├── ARCHITECTURE.md
│   ├── DEPLOYMENT.md
│   └── DEVELOPMENT.md
├── pom.xml
└── README.md
```

## 开发环境搭建

### 1. 环境要求

- **JDK**: 17 或更高版本
- **Maven**: 3.6.0 或更高版本
- **IDE**: IntelliJ IDEA 或 Eclipse
- **数据库**: MySQL 8.0+ 或 H2（开发测试）
- **Git**: 版本控制

### 2. 克隆项目

```bash
git clone https://github.com/handthing/handthing-springboot3-starter.git
cd handthing-springboot3-starter/tenant-auth-starter
```

### 3. 导入IDE

#### IntelliJ IDEA
1. 打开 IntelliJ IDEA
2. 选择 "Open" 或 "Import Project"
3. 选择项目根目录的 `pom.xml`
4. 等待Maven依赖下载完成

#### Eclipse
1. 打开 Eclipse
2. 选择 "File" -> "Import" -> "Existing Maven Projects"
3. 选择项目根目录
4. 等待Maven依赖下载完成

### 4. 配置开发环境

创建开发环境配置文件 `src/test/resources/application-dev.yml`：

```yaml
spring:
  profiles:
    active: dev
  
  # 使用H2内存数据库进行开发
  datasource:
    url: jdbc:h2:mem:devdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: 
    driver-class-name: org.h2.Driver
  
  # 启用H2控制台
  h2:
    console:
      enabled: true
      path: /h2-console
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        use_sql_comments: true

# 开发环境日志配置
logging:
  level:
    cn.com.handthing.starter.tenant: DEBUG
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE

# 多租户配置
handthing:
  auth:
    saas:
      enabled: true
      default-tenant: default
      tenant-required: false
      multi-tenant: true
      cache: true
      
      filter:
        enabled: true
        order: -200
        exclude-paths:
          - "/actuator/**"
          - "/error"
          - "/favicon.ico"
          - "/static/**"
          - "/public/**"
          - "/h2-console/**"
          - "/*.html"
      
      resolver:
        http-header:
          enabled: true
          primary-header: X-Tenant-ID
          case-sensitive: false
      
      validation: true
      monitoring: true
```

## 编码规范

### 1. Java编码规范

#### 命名规范
- **类名**: 使用PascalCase，如 `TenantConfigService`
- **方法名**: 使用camelCase，如 `getCurrentTenantId()`
- **变量名**: 使用camelCase，如 `tenantId`
- **常量名**: 使用UPPER_SNAKE_CASE，如 `DEFAULT_TENANT_ID`
- **包名**: 使用小写，如 `cn.com.handthing.starter.tenant`

#### 注释规范
```java
/**
 * 租户配置服务接口
 * 
 * <AUTHOR>
 * @since V1.0.0
 */
public interface TenantConfigService {
    
    /**
     * 获取租户配置
     * 
     * @param configKey 配置键
     * @return 配置值，如果不存在返回null
     * @throws TenantNotFoundException 如果租户不存在
     */
    String getConfig(String configKey);
    
    /**
     * 获取租户配置（带默认值）
     * 
     * @param configKey 配置键
     * @param defaultValue 默认值
     * @return 配置值，如果不存在返回默认值
     */
    String getConfig(String configKey, String defaultValue);
}
```

#### 异常处理
```java
public class TenantConfigServiceImpl implements TenantConfigService {
    
    @Override
    public String getConfig(String configKey) {
        try {
            String tenantId = TenantContextHolder.getCurrentTenantId();
            if (tenantId == null) {
                throw new TenantNotFoundException("No tenant context found");
            }
            
            return repository.findByTenantIdAndConfigKey(tenantId, configKey)
                    .map(TenantConfig::getConfigValue)
                    .orElse(null);
                    
        } catch (Exception e) {
            log.error("Failed to get config: {} for tenant: {}", 
                    configKey, TenantContextHolder.getCurrentTenantId(), e);
            throw new TenantConfigException("Failed to get config: " + configKey, e);
        }
    }
}
```

### 2. 代码质量

#### 使用Lombok简化代码
```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "tenant_configs")
public class TenantConfig {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "tenant_id", nullable = false)
    private String tenantId;
    
    @Column(name = "config_key", nullable = false)
    private String configKey;
    
    @Column(name = "config_value")
    private String configValue;
    
    // 其他字段...
}
```

#### 使用Spring注解
```java
@Service
@Slf4j
@Transactional(readOnly = true)
public class TenantConfigServiceImpl implements TenantConfigService {
    
    private final TenantConfigRepository repository;
    
    public TenantConfigServiceImpl(TenantConfigRepository repository) {
        this.repository = repository;
    }
    
    @Override
    @Cacheable(value = "tenant-configs", key = "#tenantId + ':' + #configKey")
    public String getConfig(String configKey) {
        // 实现逻辑
    }
    
    @Override
    @Transactional
    @CacheEvict(value = "tenant-configs", key = "#tenantId + ':' + #configKey")
    public void setConfig(String configKey, String configValue) {
        // 实现逻辑
    }
}
```

### 3. 测试规范

#### 单元测试
```java
@ExtendWith(MockitoExtension.class)
class TenantConfigServiceImplTest {
    
    @Mock
    private TenantConfigRepository repository;
    
    @InjectMocks
    private TenantConfigServiceImpl service;
    
    @BeforeEach
    void setUp() {
        TenantContextHolder.setCurrentTenantId("test-tenant");
    }
    
    @AfterEach
    void tearDown() {
        TenantContextHolder.clear();
    }
    
    @Test
    @DisplayName("应该能够获取存在的配置")
    void shouldGetExistingConfig() {
        // Given
        String configKey = "test.key";
        String configValue = "test.value";
        TenantConfig config = TenantConfig.builder()
                .tenantId("test-tenant")
                .configKey(configKey)
                .configValue(configValue)
                .build();
        
        when(repository.findByTenantIdAndConfigKey("test-tenant", configKey))
                .thenReturn(Optional.of(config));
        
        // When
        String result = service.getConfig(configKey);
        
        // Then
        assertThat(result).isEqualTo(configValue);
        verify(repository).findByTenantIdAndConfigKey("test-tenant", configKey);
    }
    
    @Test
    @DisplayName("当配置不存在时应该返回null")
    void shouldReturnNullWhenConfigNotExists() {
        // Given
        String configKey = "non-existent.key";
        when(repository.findByTenantIdAndConfigKey("test-tenant", configKey))
                .thenReturn(Optional.empty());
        
        // When
        String result = service.getConfig(configKey);
        
        // Then
        assertThat(result).isNull();
    }
}
```

#### 集成测试
```java
@SpringBootTest
@TestPropertySource(properties = {
    "handthing.auth.saas.enabled=true",
    "handthing.auth.saas.default-tenant=test"
})
@Sql(scripts = "/test-data.sql", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
@Sql(scripts = "/cleanup.sql", executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD)
class TenantConfigServiceIntegrationTest {
    
    @Autowired
    private TenantConfigService configService;
    
    @Test
    @DisplayName("集成测试：应该能够获取和设置配置")
    void shouldGetAndSetConfigInIntegration() {
        // 设置租户上下文
        TenantContextHolder.setCurrentTenantId("test-tenant");
        
        try {
            // 设置配置
            configService.setConfig("integration.test.key", "integration.test.value");
            
            // 获取配置
            String value = configService.getConfig("integration.test.key");
            
            // 验证
            assertThat(value).isEqualTo("integration.test.value");
        } finally {
            TenantContextHolder.clear();
        }
    }
}
```

## 开发流程

### 1. 功能开发流程

1. **创建功能分支**
   ```bash
   git checkout -b feature/new-tenant-feature
   ```

2. **编写测试用例**
   - 先写测试，后写实现（TDD）
   - 确保测试覆盖率达到80%以上

3. **实现功能**
   - 遵循编码规范
   - 添加必要的注释
   - 处理异常情况

4. **运行测试**
   ```bash
   mvn test
   ```

5. **代码审查**
   - 自我审查代码
   - 提交Pull Request
   - 等待团队审查

6. **合并代码**
   ```bash
   git checkout main
   git merge feature/new-tenant-feature
   git push origin main
   ```

### 2. 调试技巧

#### 启用调试日志
```yaml
logging:
  level:
    cn.com.handthing.starter.tenant: DEBUG
    org.springframework.web.filter: DEBUG
```

#### 使用断点调试
在IDE中设置断点，特别是在以下关键位置：
- `TenantResolverFilter.doFilter()`
- `TenantConfigServiceImpl.getConfig()`
- `TenantContextHolder.setCurrentTenant()`

#### 查看租户上下文
```java
@RestController
public class DebugController {
    
    @GetMapping("/debug/tenant")
    public Map<String, Object> debugTenant() {
        Map<String, Object> debug = new HashMap<>();
        debug.put("tenantId", TenantContextHolder.getCurrentTenantId());
        debug.put("tenant", TenantContextHolder.getCurrentTenant());
        debug.put("hasContext", TenantContextHolder.hasTenant());
        return debug;
    }
}
```

### 3. 性能测试

#### JMeter测试脚本
```xml
<?xml version="1.0" encoding="UTF-8"?>
<jmeterTestPlan version="1.2">
  <hashTree>
    <TestPlan>
      <elementProp name="TestPlan.arguments" elementType="Arguments" guiclass="ArgumentsPanel">
        <collectionProp name="Arguments.arguments"/>
      </elementProp>
      <stringProp name="TestPlan.user_define_classpath"></stringProp>
      <boolProp name="TestPlan.serialize_threadgroups">false</boolProp>
      <boolProp name="TestPlan.functional_mode">false</boolProp>
      <stringProp name="TestPlan.comments">多租户性能测试</stringProp>
    </TestPlan>
    <hashTree>
      <ThreadGroup>
        <stringProp name="ThreadGroup.num_threads">100</stringProp>
        <stringProp name="ThreadGroup.ramp_time">10</stringProp>
        <stringProp name="ThreadGroup.duration">60</stringProp>
        <boolProp name="ThreadGroup.scheduler">true</boolProp>
      </ThreadGroup>
      <hashTree>
        <HTTPSamplerProxy>
          <stringProp name="HTTPSampler.domain">localhost</stringProp>
          <stringProp name="HTTPSampler.port">8080</stringProp>
          <stringProp name="HTTPSampler.path">/api/tenant/config</stringProp>
          <stringProp name="HTTPSampler.method">GET</stringProp>
        </HTTPSamplerProxy>
        <hashTree>
          <HeaderManager>
            <collectionProp name="HeaderManager.headers">
              <elementProp name="" elementType="Header">
                <stringProp name="Header.name">X-Tenant-ID</stringProp>
                <stringProp name="Header.value">${__Random(1,3)}</stringProp>
              </elementProp>
            </collectionProp>
          </HeaderManager>
        </hashTree>
      </hashTree>
    </hashTree>
  </hashTree>
</jmeterTestPlan>
```

## 扩展开发

### 1. 自定义租户解析器

```java
@Component
@Order(10)
public class CustomTenantResolver implements TenantResolver {
    
    @Override
    public String resolveTenantId(HttpServletRequest request) {
        // 自定义解析逻辑
        String subdomain = extractSubdomain(request);
        return mapSubdomainToTenantId(subdomain);
    }
    
    private String extractSubdomain(HttpServletRequest request) {
        String serverName = request.getServerName();
        if (serverName.contains(".")) {
            return serverName.substring(0, serverName.indexOf("."));
        }
        return null;
    }
    
    private String mapSubdomainToTenantId(String subdomain) {
        // 映射逻辑
        return subdomain;
    }
    
    @Override
    public int getOrder() {
        return 10;
    }
}
```

### 2. 自定义配置类型

```java
public enum ConfigType {
    STRING,
    INTEGER,
    BOOLEAN,
    PASSWORD,
    JSON,
    CUSTOM;
    
    public Object parseValue(String value) {
        switch (this) {
            case INTEGER:
                return Integer.parseInt(value);
            case BOOLEAN:
                return Boolean.parseBoolean(value);
            case JSON:
                return JsonUtils.parseJson(value);
            case STRING:
            case PASSWORD:
            default:
                return value;
        }
    }
}
```

### 3. 事件监听器

```java
@Component
@Slf4j
public class TenantEventListener {
    
    @EventListener
    public void onTenantConfigChanged(TenantConfigChangedEvent event) {
        log.info("Tenant config changed: tenant={}, key={}, value={}", 
                event.getTenantId(), event.getConfigKey(), event.getNewValue());
        
        // 清除缓存
        cacheManager.evict("tenant-configs", 
                event.getTenantId() + ":" + event.getConfigKey());
    }
    
    @EventListener
    public void onTenantCreated(TenantCreatedEvent event) {
        log.info("New tenant created: {}", event.getTenant());
        
        // 初始化默认配置
        initializeDefaultConfigs(event.getTenant().getTenantId());
    }
}
```

## 构建和发布

### 1. Maven构建

```bash
# 编译
mvn compile

# 运行测试
mvn test

# 打包
mvn package

# 安装到本地仓库
mvn install

# 部署到远程仓库
mvn deploy
```

### 2. 版本管理

使用语义化版本控制：
- **主版本号**：不兼容的API修改
- **次版本号**：向下兼容的功能性新增
- **修订号**：向下兼容的问题修正

```bash
# 发布新版本
mvn versions:set -DnewVersion=1.1.0
mvn versions:commit
git tag v1.1.0
git push origin v1.1.0
```

### 3. 持续集成

GitHub Actions配置 `.github/workflows/ci.yml`：

```yaml
name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up JDK 17
      uses: actions/setup-java@v3
      with:
        java-version: '17'
        distribution: 'temurin'
    
    - name: Cache Maven packages
      uses: actions/cache@v3
      with:
        path: ~/.m2
        key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
        restore-keys: ${{ runner.os }}-m2
    
    - name: Run tests
      run: mvn clean test
    
    - name: Generate test report
      uses: dorny/test-reporter@v1
      if: success() || failure()
      with:
        name: Maven Tests
        path: target/surefire-reports/*.xml
        reporter: java-junit
```

## 贡献指南

### 1. 提交规范

使用约定式提交格式：
```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

类型：
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

示例：
```
feat(tenant): add domain-based tenant resolver

Add support for resolving tenant ID from subdomain.
This allows multi-tenant applications to use subdomain
routing for tenant identification.

Closes #123
```

### 2. Pull Request流程

1. Fork项目
2. 创建功能分支
3. 编写代码和测试
4. 提交Pull Request
5. 代码审查
6. 合并代码

### 3. 代码审查清单

- [ ] 代码符合编码规范
- [ ] 有充分的测试覆盖
- [ ] 文档已更新
- [ ] 没有引入安全漏洞
- [ ] 性能没有明显下降
- [ ] 向下兼容

---

更多开发相关问题，请参考 [HandThing 官方文档](https://docs.handthing.com) 或加入开发者社区。
