package cn.com.handthing.starter.auth.thirdparty;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 微信控制器
 * <p>
 * 提供微信认证相关的REST API端点，包括授权URL生成、配置验证等功能。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@RestController
@RequestMapping("${handthing.auth.web.auth-path:/auth}/wechat")
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "handthing.auth.third-party.wechat", name = "endpoints-enabled", havingValue = "true", matchIfMissing = true)
public class WechatController {

    private final WechatApiService wechatApiService;
    private final WechatUserService wechatUserService;

    /**
     * 生成微信授权URL
     *
     * @param request 授权请求
     * @return 授权URL
     */
    @PostMapping("/auth-url")
    public ResponseEntity<Map<String, Object>> generateAuthUrl(@RequestBody Map<String, Object> request) {
        try {
            String appId = (String) request.get("app_id");
            String redirectUri = (String) request.get("redirect_uri");
            String state = (String) request.getOrDefault("state", "");
            String authType = (String) request.getOrDefault("auth_type", "web");
            String scope = (String) request.getOrDefault("scope", "snsapi_userinfo");

            // 验证参数
            if (appId == null || appId.trim().isEmpty()) {
                Map<String, Object> response = buildErrorResponse("INVALID_APP_ID", "应用ID不能为空");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
            }

            if (redirectUri == null || redirectUri.trim().isEmpty()) {
                Map<String, Object> response = buildErrorResponse("INVALID_REDIRECT_URI", "重定向URI不能为空");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
            }

            // 生成授权URL
            String authUrl = buildAuthUrl(appId, redirectUri, state, authType, scope);

            Map<String, Object> response = buildSuccessResponse("授权URL生成成功");
            response.put("auth_url", authUrl);
            response.put("app_id", appId);
            response.put("auth_type", authType);
            response.put("scope", scope);

            log.info("Generated wechat auth URL for appId: {}", appId);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Generate auth URL error", e);
            Map<String, Object> response = buildErrorResponse("AUTH_URL_ERROR", "生成授权URL失败");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 验证微信配置
     *
     * @param request 验证请求
     * @return 验证结果
     */
    @PostMapping("/validate-config")
    public ResponseEntity<Map<String, Object>> validateConfig(@RequestBody Map<String, Object> request) {
        try {
            String appId = (String) request.get("app_id");
            String appSecret = (String) request.get("app_secret");

            // 验证参数
            if (appId == null || appId.trim().isEmpty()) {
                Map<String, Object> response = buildErrorResponse("INVALID_APP_ID", "应用ID不能为空");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
            }

            if (appSecret == null || appSecret.trim().isEmpty()) {
                Map<String, Object> response = buildErrorResponse("INVALID_APP_SECRET", "应用密钥不能为空");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
            }

            // 验证配置
            boolean isValid = wechatApiService.validateConfig(appId, appSecret);

            if (isValid) {
                Map<String, Object> response = buildSuccessResponse("微信配置验证成功");
                response.put("valid", true);
                response.put("app_id", appId);

                log.info("Wechat config validation successful for appId: {}", appId);
                return ResponseEntity.ok(response);
            } else {
                Map<String, Object> response = buildErrorResponse("INVALID_CONFIG", "微信配置验证失败");
                response.put("valid", false);
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
            }

        } catch (Exception e) {
            log.error("Validate config error", e);
            Map<String, Object> response = buildErrorResponse("CONFIG_VALIDATE_ERROR", "配置验证失败");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 获取微信用户信息
     *
     * @param request 用户信息请求
     * @return 用户信息
     */
    @PostMapping("/user-info")
    public ResponseEntity<Map<String, Object>> getUserInfo(@RequestBody Map<String, Object> request) {
        try {
            String appId = (String) request.get("app_id");
            String appSecret = (String) request.get("app_secret");
            String code = (String) request.get("code");
            String lang = (String) request.getOrDefault("lang", "zh_CN");

            // 验证参数
            if (appId == null || appSecret == null || code == null) {
                Map<String, Object> response = buildErrorResponse("INVALID_PARAMS", "参数不完整");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
            }

            // 获取访问令牌
            WechatApiService.WechatTokenResult tokenResult = wechatApiService.getAccessToken(appId, appSecret, code);
            if (!tokenResult.isSuccess()) {
                Map<String, Object> response = buildErrorResponse("TOKEN_ERROR", "获取访问令牌失败: " + tokenResult.getErrorMessage());
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
            }

            // 获取用户信息
            WechatApiService.WechatUserResult userResult = wechatApiService.getUserInfo(tokenResult.getAccessToken(), tokenResult.getOpenId(), lang);
            if (!userResult.isSuccess()) {
                Map<String, Object> response = buildErrorResponse("USER_INFO_ERROR", "获取用户信息失败: " + userResult.getErrorMessage());
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
            }

            // 构建响应
            Map<String, Object> response = buildSuccessResponse("获取用户信息成功");
            response.put("open_id", userResult.getOpenId());
            response.put("union_id", userResult.getUnionId());
            response.put("nickname", userResult.getNickname());
            response.put("sex", userResult.getSex());
            response.put("province", userResult.getProvince());
            response.put("city", userResult.getCity());
            response.put("country", userResult.getCountry());
            response.put("head_img_url", userResult.getHeadImgUrl());
            response.put("privilege", userResult.getPrivilege());
            response.put("scope", tokenResult.getScope());

            log.info("Retrieved wechat user info: openId={}, nickname={}", userResult.getOpenId(), userResult.getNickname());
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Get user info error", e);
            Map<String, Object> response = buildErrorResponse("USER_INFO_ERROR", "获取用户信息失败");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 获取微信统计信息
     *
     * @return 统计信息
     */
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getStats() {
        try {
            Map<String, Object> stats = new HashMap<>();
            
            if (wechatUserService instanceof DefaultWechatUserService) {
                DefaultWechatUserService defaultService = (DefaultWechatUserService) wechatUserService;
                stats = defaultService.getStatistics();
            } else {
                stats.put("message", "Statistics not available for custom implementation");
            }

            Map<String, Object> response = buildSuccessResponse("获取统计信息成功");
            response.put("statistics", stats);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Get stats error", e);
            Map<String, Object> response = buildErrorResponse("STATS_ERROR", "获取统计信息失败");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 构建微信授权URL
     *
     * @param appId       应用ID
     * @param redirectUri 重定向URI
     * @param state       状态参数
     * @param authType    授权类型
     * @param scope       授权范围
     * @return 授权URL
     */
    private String buildAuthUrl(String appId, String redirectUri, String state, String authType, String scope) {
        StringBuilder urlBuilder = new StringBuilder();
        
        if ("scan".equals(authType)) {
            // 开放平台扫码登录URL
            urlBuilder.append("https://open.weixin.qq.com/connect/qrconnect");
            urlBuilder.append("?appid=").append(appId);
            urlBuilder.append("&response_type=code");
            urlBuilder.append("&scope=snsapi_login");
        } else {
            // 公众号网页授权URL
            urlBuilder.append("https://open.weixin.qq.com/connect/oauth2/authorize");
            urlBuilder.append("?appid=").append(appId);
            urlBuilder.append("&response_type=code");
            urlBuilder.append("&scope=").append(scope);
        }
        
        urlBuilder.append("&redirect_uri=").append(java.net.URLEncoder.encode(redirectUri, java.nio.charset.StandardCharsets.UTF_8));
        
        if (state != null && !state.trim().isEmpty()) {
            urlBuilder.append("&state=").append(state);
        }
        
        urlBuilder.append("#wechat_redirect");
        
        return urlBuilder.toString();
    }

    /**
     * 构建成功响应
     *
     * @param message 消息
     * @return 响应Map
     */
    private Map<String, Object> buildSuccessResponse(String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", message);
        response.put("timestamp", LocalDateTime.now());
        return response;
    }

    /**
     * 构建错误响应
     *
     * @param errorCode        错误代码
     * @param errorDescription 错误描述
     * @return 响应Map
     */
    private Map<String, Object> buildErrorResponse(String errorCode, String errorDescription) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("error", errorCode);
        response.put("error_description", errorDescription);
        response.put("timestamp", LocalDateTime.now());
        return response;
    }
}
