# HandThing Admin 数据库设计文档

## 1. 数据库设计原则

### 1.1 设计原则
- **多租户隔离**: 所有业务表包含tenant_id字段进行逻辑隔离
- **软删除**: 重要数据采用软删除策略，保留deleted字段
- **审计字段**: 包含create_time、update_time、create_by、update_by
- **版本控制**: 重要表包含version字段支持乐观锁
- **索引优化**: 基于查询场景设计合理的索引策略

### 1.2 命名规范
- **表名**: 小写字母+下划线，如user_info
- **字段名**: 小写字母+下划线，如user_name
- **索引名**: idx_表名_字段名，如idx_user_info_username
- **唯一约束**: uk_表名_字段名，如uk_user_info_username

### 1.3 数据类型规范
- **主键**: BIGINT，使用分布式ID生成器
- **字符串**: VARCHAR，根据实际需要设置长度
- **文本**: TEXT，用于长文本内容
- **时间**: DATETIME，统一使用DATETIME类型
- **状态**: TINYINT，用于状态标识
- **金额**: DECIMAL(10,2)，用于金额字段

## 2. 核心表结构设计

### 2.1 租户相关表

#### 租户信息表 (tenant_info)
```sql
CREATE TABLE tenant_info (
    id BIGINT PRIMARY KEY COMMENT '租户ID',
    tenant_code VARCHAR(50) UNIQUE NOT NULL COMMENT '租户编码',
    tenant_name VARCHAR(100) NOT NULL COMMENT '租户名称',
    company_name VARCHAR(200) COMMENT '公司名称',
    contact_name VARCHAR(50) COMMENT '联系人姓名',
    contact_phone VARCHAR(20) COMMENT '联系人电话',
    contact_email VARCHAR(100) COMMENT '联系人邮箱',
    package_id BIGINT COMMENT '套餐ID',
    status TINYINT DEFAULT 1 COMMENT '状态:1正常,2禁用,3过期',
    expire_time DATETIME COMMENT '到期时间',
    max_users INT DEFAULT 0 COMMENT '最大用户数',
    max_storage BIGINT DEFAULT 0 COMMENT '最大存储空间(字节)',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人',
    update_by BIGINT COMMENT '更新人',
    deleted TINYINT DEFAULT 0 COMMENT '删除标识:0未删除,1已删除',
    version INT DEFAULT 1 COMMENT '版本号',
    INDEX idx_tenant_code (tenant_code),
    INDEX idx_status (status),
    INDEX idx_expire_time (expire_time)
) COMMENT '租户信息表';
```

#### 租户套餐表 (tenant_package)
```sql
CREATE TABLE tenant_package (
    id BIGINT PRIMARY KEY COMMENT '套餐ID',
    package_name VARCHAR(100) NOT NULL COMMENT '套餐名称',
    package_code VARCHAR(50) UNIQUE NOT NULL COMMENT '套餐编码',
    description TEXT COMMENT '套餐描述',
    price_monthly DECIMAL(10,2) COMMENT '月付价格',
    price_yearly DECIMAL(10,2) COMMENT '年付价格',
    max_users INT DEFAULT 0 COMMENT '最大用户数',
    max_storage BIGINT DEFAULT 0 COMMENT '最大存储空间',
    max_api_calls INT DEFAULT 0 COMMENT '每月API调用次数',
    permissions JSON COMMENT '权限配置',
    status TINYINT DEFAULT 1 COMMENT '状态:1启用,0禁用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '删除标识',
    INDEX idx_package_code (package_code),
    INDEX idx_status (status)
) COMMENT '租户套餐表';
```

#### 租户订阅表 (tenant_subscription)
```sql
CREATE TABLE tenant_subscription (
    id BIGINT PRIMARY KEY COMMENT '订阅ID',
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    package_id BIGINT NOT NULL COMMENT '套餐ID',
    start_time DATETIME NOT NULL COMMENT '开始时间',
    end_time DATETIME NOT NULL COMMENT '结束时间',
    status TINYINT DEFAULT 1 COMMENT '状态:1有效,2过期,3取消',
    amount DECIMAL(10,2) COMMENT '订阅金额',
    payment_status TINYINT DEFAULT 0 COMMENT '支付状态:0未支付,1已支付',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_package_id (package_id),
    INDEX idx_end_time (end_time)
) COMMENT '租户订阅表';
```

### 2.2 用户权限相关表

#### 用户信息表 (user_info)
```sql
CREATE TABLE user_info (
    id BIGINT PRIMARY KEY COMMENT '用户ID',
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    username VARCHAR(50) NOT NULL COMMENT '用户名',
    nickname VARCHAR(50) COMMENT '昵称',
    email VARCHAR(100) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    avatar VARCHAR(500) COMMENT '头像URL',
    password VARCHAR(255) COMMENT '密码',
    salt VARCHAR(50) COMMENT '盐值',
    status TINYINT DEFAULT 1 COMMENT '状态:1正常,0禁用',
    last_login_time DATETIME COMMENT '最后登录时间',
    last_login_ip VARCHAR(50) COMMENT '最后登录IP',
    mfa_enabled TINYINT DEFAULT 0 COMMENT '是否启用MFA',
    mfa_secret VARCHAR(100) COMMENT 'MFA密钥',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人',
    update_by BIGINT COMMENT '更新人',
    deleted TINYINT DEFAULT 0 COMMENT '删除标识',
    version INT DEFAULT 1 COMMENT '版本号',
    UNIQUE KEY uk_tenant_username (tenant_id, username),
    INDEX idx_tenant_phone (tenant_id, phone),
    INDEX idx_tenant_email (tenant_id, email),
    INDEX idx_status (status)
) COMMENT '用户信息表';
```

#### 角色信息表 (role_info)
```sql
CREATE TABLE role_info (
    id BIGINT PRIMARY KEY COMMENT '角色ID',
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    role_code VARCHAR(50) NOT NULL COMMENT '角色编码',
    role_name VARCHAR(100) NOT NULL COMMENT '角色名称',
    description TEXT COMMENT '角色描述',
    data_scope TINYINT DEFAULT 1 COMMENT '数据权限:1全部,2本部门,3本部门及下级,4仅本人,5自定义',
    status TINYINT DEFAULT 1 COMMENT '状态:1启用,0禁用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人',
    update_by BIGINT COMMENT '更新人',
    deleted TINYINT DEFAULT 0 COMMENT '删除标识',
    UNIQUE KEY uk_tenant_code (tenant_id, role_code),
    INDEX idx_tenant_id (tenant_id)
) COMMENT '角色信息表';
```

#### 用户角色关联表 (user_role)
```sql
CREATE TABLE user_role (
    id BIGINT PRIMARY KEY COMMENT '关联ID',
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    role_id BIGINT NOT NULL COMMENT '角色ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_by BIGINT COMMENT '创建人',
    UNIQUE KEY uk_user_role (user_id, role_id),
    INDEX idx_tenant_user (tenant_id, user_id),
    INDEX idx_role_id (role_id)
) COMMENT '用户角色关联表';
```

#### 权限信息表 (permission_info)
```sql
CREATE TABLE permission_info (
    id BIGINT PRIMARY KEY COMMENT '权限ID',
    permission_code VARCHAR(100) NOT NULL COMMENT '权限编码',
    permission_name VARCHAR(100) NOT NULL COMMENT '权限名称',
    permission_type TINYINT NOT NULL COMMENT '权限类型:1菜单,2按钮,3API',
    parent_id BIGINT DEFAULT 0 COMMENT '父权限ID',
    path VARCHAR(200) COMMENT '路由路径',
    component VARCHAR(200) COMMENT '组件路径',
    icon VARCHAR(100) COMMENT '图标',
    sort_order INT DEFAULT 0 COMMENT '排序',
    status TINYINT DEFAULT 1 COMMENT '状态:1启用,0禁用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '删除标识',
    UNIQUE KEY uk_permission_code (permission_code),
    INDEX idx_parent_id (parent_id),
    INDEX idx_permission_type (permission_type)
) COMMENT '权限信息表';
```

#### 角色权限关联表 (role_permission)
```sql
CREATE TABLE role_permission (
    id BIGINT PRIMARY KEY COMMENT '关联ID',
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    role_id BIGINT NOT NULL COMMENT '角色ID',
    permission_id BIGINT NOT NULL COMMENT '权限ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_by BIGINT COMMENT '创建人',
    UNIQUE KEY uk_role_permission (role_id, permission_id),
    INDEX idx_tenant_role (tenant_id, role_id),
    INDEX idx_permission_id (permission_id)
) COMMENT '角色权限关联表';
```

### 2.3 组织架构相关表

#### 部门信息表 (department_info)
```sql
CREATE TABLE department_info (
    id BIGINT PRIMARY KEY COMMENT '部门ID',
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    dept_code VARCHAR(50) NOT NULL COMMENT '部门编码',
    dept_name VARCHAR(100) NOT NULL COMMENT '部门名称',
    parent_id BIGINT DEFAULT 0 COMMENT '父部门ID',
    leader_id BIGINT COMMENT '部门负责人ID',
    phone VARCHAR(20) COMMENT '联系电话',
    email VARCHAR(100) COMMENT '邮箱',
    sort_order INT DEFAULT 0 COMMENT '排序',
    status TINYINT DEFAULT 1 COMMENT '状态:1正常,0停用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人',
    update_by BIGINT COMMENT '更新人',
    deleted TINYINT DEFAULT 0 COMMENT '删除标识',
    UNIQUE KEY uk_tenant_code (tenant_id, dept_code),
    INDEX idx_tenant_parent (tenant_id, parent_id),
    INDEX idx_leader_id (leader_id)
) COMMENT '部门信息表';
```

#### 职位信息表 (position_info)
```sql
CREATE TABLE position_info (
    id BIGINT PRIMARY KEY COMMENT '职位ID',
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    position_code VARCHAR(50) NOT NULL COMMENT '职位编码',
    position_name VARCHAR(100) NOT NULL COMMENT '职位名称',
    description TEXT COMMENT '职位描述',
    level_rank INT DEFAULT 0 COMMENT '职级',
    status TINYINT DEFAULT 1 COMMENT '状态:1启用,0禁用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人',
    update_by BIGINT COMMENT '更新人',
    deleted TINYINT DEFAULT 0 COMMENT '删除标识',
    UNIQUE KEY uk_tenant_code (tenant_id, position_code),
    INDEX idx_tenant_id (tenant_id)
) COMMENT '职位信息表';
```

#### 用户部门关联表 (user_department)
```sql
CREATE TABLE user_department (
    id BIGINT PRIMARY KEY COMMENT '关联ID',
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    dept_id BIGINT NOT NULL COMMENT '部门ID',
    position_id BIGINT COMMENT '职位ID',
    is_main TINYINT DEFAULT 1 COMMENT '是否主部门:1是,0否',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_by BIGINT COMMENT '创建人',
    INDEX idx_tenant_user (tenant_id, user_id),
    INDEX idx_tenant_dept (tenant_id, dept_id),
    INDEX idx_position_id (position_id)
) COMMENT '用户部门关联表';
```

### 2.4 系统管理相关表

#### 菜单信息表 (menu_info)
```sql
CREATE TABLE menu_info (
    id BIGINT PRIMARY KEY COMMENT '菜单ID',
    menu_code VARCHAR(100) NOT NULL COMMENT '菜单编码',
    menu_name VARCHAR(100) NOT NULL COMMENT '菜单名称',
    parent_id BIGINT DEFAULT 0 COMMENT '父菜单ID',
    menu_type TINYINT NOT NULL COMMENT '菜单类型:1目录,2菜单,3按钮',
    path VARCHAR(200) COMMENT '路由路径',
    component VARCHAR(200) COMMENT '组件路径',
    icon VARCHAR(100) COMMENT '图标',
    sort_order INT DEFAULT 0 COMMENT '排序',
    visible TINYINT DEFAULT 1 COMMENT '是否可见:1是,0否',
    status TINYINT DEFAULT 1 COMMENT '状态:1启用,0禁用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '删除标识',
    UNIQUE KEY uk_menu_code (menu_code),
    INDEX idx_parent_id (parent_id),
    INDEX idx_menu_type (menu_type)
) COMMENT '菜单信息表';
```

#### 字典类型表 (dict_type)
```sql
CREATE TABLE dict_type (
    id BIGINT PRIMARY KEY COMMENT '字典类型ID',
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    dict_code VARCHAR(100) NOT NULL COMMENT '字典编码',
    dict_name VARCHAR(100) NOT NULL COMMENT '字典名称',
    description TEXT COMMENT '描述',
    status TINYINT DEFAULT 1 COMMENT '状态:1启用,0禁用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人',
    update_by BIGINT COMMENT '更新人',
    deleted TINYINT DEFAULT 0 COMMENT '删除标识',
    UNIQUE KEY uk_tenant_code (tenant_id, dict_code),
    INDEX idx_tenant_id (tenant_id)
) COMMENT '字典类型表';
```

#### 字典数据表 (dict_data)
```sql
CREATE TABLE dict_data (
    id BIGINT PRIMARY KEY COMMENT '字典数据ID',
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    dict_type_id BIGINT NOT NULL COMMENT '字典类型ID',
    dict_label VARCHAR(100) NOT NULL COMMENT '字典标签',
    dict_value VARCHAR(100) NOT NULL COMMENT '字典值',
    sort_order INT DEFAULT 0 COMMENT '排序',
    status TINYINT DEFAULT 1 COMMENT '状态:1启用,0禁用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人',
    update_by BIGINT COMMENT '更新人',
    deleted TINYINT DEFAULT 0 COMMENT '删除标识',
    INDEX idx_tenant_type (tenant_id, dict_type_id),
    INDEX idx_dict_value (dict_value)
) COMMENT '字典数据表';
```

#### 系统配置表 (system_config)
```sql
CREATE TABLE system_config (
    id BIGINT PRIMARY KEY COMMENT '配置ID',
    tenant_id BIGINT COMMENT '租户ID(NULL表示全局配置)',
    config_key VARCHAR(100) NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_name VARCHAR(100) NOT NULL COMMENT '配置名称',
    config_type VARCHAR(20) DEFAULT 'string' COMMENT '配置类型:string,number,boolean,json',
    category VARCHAR(50) DEFAULT 'system' COMMENT '配置分类',
    description TEXT COMMENT '描述',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_tenant_key (tenant_id, config_key),
    INDEX idx_category (category)
) COMMENT '系统配置表';
```

### 2.5 工作流相关表

#### 工作流定义表 (workflow_definition)
```sql
CREATE TABLE workflow_definition (
    id VARCHAR(64) PRIMARY KEY COMMENT '流程定义ID',
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    process_key VARCHAR(100) NOT NULL COMMENT '流程键',
    process_name VARCHAR(200) NOT NULL COMMENT '流程名称',
    category VARCHAR(50) COMMENT '流程分类',
    version INT DEFAULT 1 COMMENT '版本号',
    description TEXT COMMENT '描述',
    bpmn_xml LONGTEXT COMMENT 'BPMN XML内容',
    status VARCHAR(20) DEFAULT 'active' COMMENT '状态:active,suspended',
    deploy_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '部署时间',
    create_by BIGINT COMMENT '创建人',
    INDEX idx_tenant_key (tenant_id, process_key),
    INDEX idx_category (category)
) COMMENT '工作流定义表';
```

#### 工作流实例表 (workflow_instance)
```sql
CREATE TABLE workflow_instance (
    id VARCHAR(64) PRIMARY KEY COMMENT '流程实例ID',
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    process_definition_id VARCHAR(64) NOT NULL COMMENT '流程定义ID',
    process_definition_key VARCHAR(100) NOT NULL COMMENT '流程定义键',
    business_key VARCHAR(100) COMMENT '业务键',
    start_user_id BIGINT COMMENT '发起人ID',
    start_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
    end_time DATETIME COMMENT '结束时间',
    status VARCHAR(20) DEFAULT 'running' COMMENT '状态:running,completed,suspended,terminated',
    variables JSON COMMENT '流程变量',
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_process_key (process_definition_key),
    INDEX idx_business_key (business_key),
    INDEX idx_start_user (start_user_id)
) COMMENT '工作流实例表';
```

#### 工作流任务表 (workflow_task)
```sql
CREATE TABLE workflow_task (
    id VARCHAR(64) PRIMARY KEY COMMENT '任务ID',
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    process_instance_id VARCHAR(64) NOT NULL COMMENT '流程实例ID',
    process_definition_key VARCHAR(100) NOT NULL COMMENT '流程定义键',
    task_name VARCHAR(200) NOT NULL COMMENT '任务名称',
    task_key VARCHAR(100) NOT NULL COMMENT '任务键',
    assignee BIGINT COMMENT '处理人ID',
    candidate_users TEXT COMMENT '候选用户',
    candidate_groups TEXT COMMENT '候选组',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    claim_time DATETIME COMMENT '签收时间',
    complete_time DATETIME COMMENT '完成时间',
    due_date DATETIME COMMENT '到期时间',
    priority INT DEFAULT 50 COMMENT '优先级',
    status VARCHAR(20) DEFAULT 'created' COMMENT '状态:created,claimed,completed',
    variables JSON COMMENT '任务变量',
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_process_instance (process_instance_id),
    INDEX idx_assignee (assignee),
    INDEX idx_status (status)
) COMMENT '工作流任务表';
```

### 2.6 消息中心相关表

#### 消息模板表 (message_template)
```sql
CREATE TABLE message_template (
    id BIGINT PRIMARY KEY COMMENT '模板ID',
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    template_code VARCHAR(100) NOT NULL COMMENT '模板编码',
    template_name VARCHAR(200) NOT NULL COMMENT '模板名称',
    template_type VARCHAR(20) NOT NULL COMMENT '模板类型:sms,email,push,wechat',
    title VARCHAR(200) COMMENT '标题',
    content TEXT NOT NULL COMMENT '内容',
    variables JSON COMMENT '变量定义',
    status TINYINT DEFAULT 1 COMMENT '状态:1启用,0禁用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人',
    update_by BIGINT COMMENT '更新人',
    deleted TINYINT DEFAULT 0 COMMENT '删除标识',
    UNIQUE KEY uk_tenant_code (tenant_id, template_code),
    INDEX idx_template_type (template_type)
) COMMENT '消息模板表';
```

#### 消息记录表 (message_record)
```sql
CREATE TABLE message_record (
    id VARCHAR(64) PRIMARY KEY COMMENT '消息ID',
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    template_id BIGINT NOT NULL COMMENT '模板ID',
    template_code VARCHAR(100) NOT NULL COMMENT '模板编码',
    message_type VARCHAR(20) NOT NULL COMMENT '消息类型',
    receiver VARCHAR(200) NOT NULL COMMENT '接收人',
    title VARCHAR(200) COMMENT '标题',
    content TEXT NOT NULL COMMENT '内容',
    send_time DATETIME COMMENT '发送时间',
    status VARCHAR(20) DEFAULT 'pending' COMMENT '状态:pending,sent,failed',
    error_msg TEXT COMMENT '错误信息',
    retry_count INT DEFAULT 0 COMMENT '重试次数',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_template_id (template_id),
    INDEX idx_receiver (receiver),
    INDEX idx_status (status),
    INDEX idx_send_time (send_time)
) COMMENT '消息记录表';
```

#### 文件信息表 (file_info)
```sql
CREATE TABLE file_info (
    id VARCHAR(64) PRIMARY KEY COMMENT '文件ID',
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    file_name VARCHAR(255) NOT NULL COMMENT '文件名',
    original_name VARCHAR(255) NOT NULL COMMENT '原始文件名',
    file_path VARCHAR(500) NOT NULL COMMENT '文件路径',
    file_url VARCHAR(500) COMMENT '文件URL',
    file_size BIGINT NOT NULL COMMENT '文件大小(字节)',
    file_type VARCHAR(100) COMMENT '文件类型',
    mime_type VARCHAR(100) COMMENT 'MIME类型',
    category VARCHAR(50) DEFAULT 'default' COMMENT '文件分类',
    storage_type VARCHAR(20) DEFAULT 'local' COMMENT '存储类型:local,oss,s3,minio',
    upload_user_id BIGINT COMMENT '上传人ID',
    upload_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
    status TINYINT DEFAULT 1 COMMENT '状态:1正常,0删除',
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_category (category),
    INDEX idx_upload_user (upload_user_id),
    INDEX idx_upload_time (upload_time)
) COMMENT '文件信息表';
```

### 2.7 应用集成相关表

#### 应用信息表 (app_info)
```sql
CREATE TABLE app_info (
    id BIGINT PRIMARY KEY COMMENT '应用ID',
    app_code VARCHAR(100) NOT NULL COMMENT '应用编码',
    app_name VARCHAR(200) NOT NULL COMMENT '应用名称',
    app_type VARCHAR(20) NOT NULL COMMENT '应用类型:internal,external',
    description TEXT COMMENT '应用描述',
    logo_url VARCHAR(500) COMMENT 'Logo URL',
    home_url VARCHAR(500) COMMENT '主页URL',
    callback_url VARCHAR(500) COMMENT '回调URL',
    app_key VARCHAR(100) COMMENT '应用Key',
    app_secret VARCHAR(255) COMMENT '应用Secret',
    status TINYINT DEFAULT 1 COMMENT '状态:1启用,0禁用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人',
    update_by BIGINT COMMENT '更新人',
    deleted TINYINT DEFAULT 0 COMMENT '删除标识',
    UNIQUE KEY uk_app_code (app_code),
    INDEX idx_app_type (app_type)
) COMMENT '应用信息表';
```

#### 租户应用关联表 (tenant_app)
```sql
CREATE TABLE tenant_app (
    id BIGINT PRIMARY KEY COMMENT '关联ID',
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    app_id BIGINT NOT NULL COMMENT '应用ID',
    status TINYINT DEFAULT 1 COMMENT '状态:1启用,0禁用',
    config JSON COMMENT '应用配置',
    install_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '安装时间',
    UNIQUE KEY uk_tenant_app (tenant_id, app_id),
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_app_id (app_id)
) COMMENT '租户应用关联表';
```

#### API信息表 (api_info)
```sql
CREATE TABLE api_info (
    id BIGINT PRIMARY KEY COMMENT 'API ID',
    api_code VARCHAR(100) NOT NULL COMMENT 'API编码',
    api_name VARCHAR(200) NOT NULL COMMENT 'API名称',
    api_path VARCHAR(500) NOT NULL COMMENT 'API路径',
    api_method VARCHAR(10) NOT NULL COMMENT 'HTTP方法',
    api_group VARCHAR(100) COMMENT 'API分组',
    description TEXT COMMENT 'API描述',
    rate_limit INT DEFAULT 0 COMMENT '限流次数(每分钟)',
    auth_required TINYINT DEFAULT 1 COMMENT '是否需要认证:1是,0否',
    status TINYINT DEFAULT 1 COMMENT '状态:1启用,0禁用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_api_code (api_code),
    INDEX idx_api_group (api_group),
    INDEX idx_api_path (api_path)
) COMMENT 'API信息表';
```

### 2.8 运营监控相关表

#### 审计日志表 (audit_log)
```sql
CREATE TABLE audit_log (
    id BIGINT PRIMARY KEY COMMENT '日志ID',
    tenant_id BIGINT COMMENT '租户ID',
    user_id BIGINT COMMENT '用户ID',
    username VARCHAR(50) COMMENT '用户名',
    operation VARCHAR(100) NOT NULL COMMENT '操作类型',
    resource VARCHAR(200) COMMENT '操作资源',
    resource_id VARCHAR(100) COMMENT '资源ID',
    method VARCHAR(10) COMMENT 'HTTP方法',
    url VARCHAR(500) COMMENT '请求URL',
    ip_address VARCHAR(50) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    request_params TEXT COMMENT '请求参数',
    response_status INT COMMENT '响应状态码',
    error_msg TEXT COMMENT '错误信息',
    execution_time INT COMMENT '执行时间(毫秒)',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_user_id (user_id),
    INDEX idx_operation (operation),
    INDEX idx_create_time (create_time),
    INDEX idx_ip_address (ip_address)
) COMMENT '审计日志表'
PARTITION BY RANGE (YEAR(create_time)) (
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p2025 VALUES LESS THAN (2026),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

#### 系统监控表 (system_monitor)
```sql
CREATE TABLE system_monitor (
    id BIGINT PRIMARY KEY COMMENT '监控ID',
    metric_name VARCHAR(100) NOT NULL COMMENT '指标名称',
    metric_value DECIMAL(15,4) NOT NULL COMMENT '指标值',
    metric_unit VARCHAR(20) COMMENT '指标单位',
    instance_id VARCHAR(100) COMMENT '实例ID',
    tags JSON COMMENT '标签',
    collect_time DATETIME NOT NULL COMMENT '采集时间',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_metric_name (metric_name),
    INDEX idx_collect_time (collect_time),
    INDEX idx_instance_id (instance_id)
) COMMENT '系统监控表'
PARTITION BY RANGE (TO_DAYS(collect_time)) (
    PARTITION p_history VALUES LESS THAN (TO_DAYS('2024-01-01')),
    PARTITION p_current VALUES LESS THAN MAXVALUE
);
```

#### 告警规则表 (alert_rule)
```sql
CREATE TABLE alert_rule (
    id BIGINT PRIMARY KEY COMMENT '规则ID',
    tenant_id BIGINT COMMENT '租户ID',
    rule_name VARCHAR(200) NOT NULL COMMENT '规则名称',
    metric_name VARCHAR(100) NOT NULL COMMENT '指标名称',
    condition_operator VARCHAR(10) NOT NULL COMMENT '条件操作符:>,<,>=,<=,=,!=',
    threshold_value DECIMAL(15,4) NOT NULL COMMENT '阈值',
    duration_seconds INT DEFAULT 60 COMMENT '持续时间(秒)',
    severity VARCHAR(20) DEFAULT 'warning' COMMENT '严重级别:info,warning,error,critical',
    notification_channels JSON COMMENT '通知渠道',
    status TINYINT DEFAULT 1 COMMENT '状态:1启用,0禁用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人',
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_metric_name (metric_name),
    INDEX idx_status (status)
) COMMENT '告警规则表';
```

#### 计费记录表 (billing_record)
```sql
CREATE TABLE billing_record (
    id BIGINT PRIMARY KEY COMMENT '计费ID',
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    billing_type VARCHAR(50) NOT NULL COMMENT '计费类型:subscription,usage,api_call',
    resource_type VARCHAR(50) COMMENT '资源类型',
    quantity DECIMAL(15,4) NOT NULL COMMENT '数量',
    unit_price DECIMAL(10,4) COMMENT '单价',
    total_amount DECIMAL(10,2) NOT NULL COMMENT '总金额',
    billing_period VARCHAR(20) COMMENT '计费周期:monthly,yearly,daily',
    billing_date DATE NOT NULL COMMENT '计费日期',
    status VARCHAR(20) DEFAULT 'pending' COMMENT '状态:pending,confirmed,paid',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_billing_type (billing_type),
    INDEX idx_billing_date (billing_date),
    INDEX idx_status (status)
) COMMENT '计费记录表';
```

## 3. 索引设计策略

### 3.1 主要索引类型
- **主键索引**: 每个表的id字段
- **唯一索引**: 业务唯一性约束
- **普通索引**: 常用查询字段
- **复合索引**: 多字段组合查询
- **分区索引**: 分区表的分区字段

### 3.2 索引优化原则
- **最左前缀**: 复合索引遵循最左前缀原则
- **选择性**: 优先为选择性高的字段建索引
- **覆盖索引**: 尽量使用覆盖索引减少回表
- **避免冗余**: 避免创建冗余索引

## 4. 分表分库策略

### 4.1 分表策略
- **日志表**: 按月分表，如audit_log_202401
- **监控表**: 按天分表，如system_monitor_20240101
- **历史数据**: 定期归档到历史表

### 4.2 分库策略
- **租户分库**: 大租户独立数据库
- **业务分库**: 按业务模块分库
- **读写分离**: 主库写入，从库读取

---

**© 2024 HandThing. All rights reserved.**
