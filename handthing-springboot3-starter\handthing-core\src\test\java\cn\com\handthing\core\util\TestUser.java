package cn.com.handthing.core.util;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 测试用户模型类
 * 用于 JsonUtils 单元测试
 */
public class TestUser {
    
    private Long id;
    private String name;
    private Integer age;
    private Double salary;
    private Float score;
    private BigDecimal balance;
    private LocalDateTime createTime;
    private LocalDate birthDate;
    private Boolean active;
    
    public TestUser() {}
    
    public TestUser(Long id, String name, Integer age) {
        this.id = id;
        this.name = name;
        this.age = age;
    }
    
    public TestUser(Long id, String name, Integer age, Double salary, Float score, 
                   BigDecimal balance, LocalDateTime createTime, LocalDate birthDate, Boolean active) {
        this.id = id;
        this.name = name;
        this.age = age;
        this.salary = salary;
        this.score = score;
        this.balance = balance;
        this.createTime = createTime;
        this.birthDate = birthDate;
        this.active = active;
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public Integer getAge() {
        return age;
    }
    
    public void setAge(Integer age) {
        this.age = age;
    }
    
    public Double getSalary() {
        return salary;
    }
    
    public void setSalary(Double salary) {
        this.salary = salary;
    }
    
    public Float getScore() {
        return score;
    }
    
    public void setScore(Float score) {
        this.score = score;
    }
    
    public BigDecimal getBalance() {
        return balance;
    }
    
    public void setBalance(BigDecimal balance) {
        this.balance = balance;
    }
    
    public LocalDateTime getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    
    public LocalDate getBirthDate() {
        return birthDate;
    }
    
    public void setBirthDate(LocalDate birthDate) {
        this.birthDate = birthDate;
    }
    
    public Boolean getActive() {
        return active;
    }
    
    public void setActive(Boolean active) {
        this.active = active;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TestUser testUser = (TestUser) o;
        return Objects.equals(id, testUser.id) &&
               Objects.equals(name, testUser.name) &&
               Objects.equals(age, testUser.age) &&
               Objects.equals(salary, testUser.salary) &&
               Objects.equals(score, testUser.score) &&
               Objects.equals(balance, testUser.balance) &&
               Objects.equals(createTime, testUser.createTime) &&
               Objects.equals(birthDate, testUser.birthDate) &&
               Objects.equals(active, testUser.active);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(id, name, age, salary, score, balance, createTime, birthDate, active);
    }
    
    @Override
    public String toString() {
        return "TestUser{" +
               "id=" + id +
               ", name='" + name + '\'' +
               ", age=" + age +
               ", salary=" + salary +
               ", score=" + score +
               ", balance=" + balance +
               ", createTime=" + createTime +
               ", birthDate=" + birthDate +
               ", active=" + active +
               '}';
    }
}
