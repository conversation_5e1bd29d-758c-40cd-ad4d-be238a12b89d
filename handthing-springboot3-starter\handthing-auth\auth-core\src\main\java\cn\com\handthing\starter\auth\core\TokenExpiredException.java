package cn.com.handthing.starter.auth.core;

/**
 * 令牌过期异常
 * <p>
 * 当访问令牌或刷新令牌过期时抛出此异常。
 * 客户端收到此异常后应该重新进行认证或使用刷新令牌获取新的访问令牌。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
public class TokenExpiredException extends AuthenticationException {

    /**
     * 令牌类型
     */
    private final TokenType tokenType;

    /**
     * 构造函数
     *
     * @param message 错误消息
     */
    public TokenExpiredException(String message) {
        super("TOKEN_EXPIRED", ErrorType.TOKEN_EXPIRED, message);
        this.tokenType = TokenType.ACCESS_TOKEN;
    }

    /**
     * 构造函数
     *
     * @param tokenType 令牌类型
     * @param message   错误消息
     */
    public TokenExpiredException(TokenType tokenType, String message) {
        super("TOKEN_EXPIRED", ErrorType.TOKEN_EXPIRED, message);
        this.tokenType = tokenType;
    }

    /**
     * 构造函数
     *
     * @param tokenType 令牌类型
     * @param message   错误消息
     * @param cause     原因异常
     */
    public TokenExpiredException(TokenType tokenType, String message, Throwable cause) {
        super("TOKEN_EXPIRED", ErrorType.TOKEN_EXPIRED, message, cause);
        this.tokenType = tokenType;
    }

    /**
     * 获取令牌类型
     *
     * @return 令牌类型
     */
    public TokenType getTokenType() {
        return tokenType;
    }

    /**
     * 创建访问令牌过期异常
     *
     * @return 令牌过期异常
     */
    public static TokenExpiredException accessTokenExpired() {
        return new TokenExpiredException(TokenType.ACCESS_TOKEN, "访问令牌已过期");
    }

    /**
     * 创建刷新令牌过期异常
     *
     * @return 令牌过期异常
     */
    public static TokenExpiredException refreshTokenExpired() {
        return new TokenExpiredException(TokenType.REFRESH_TOKEN, "刷新令牌已过期");
    }

    /**
     * 令牌类型枚举
     */
    public enum TokenType {
        /**
         * 访问令牌
         */
        ACCESS_TOKEN("access_token", "访问令牌"),

        /**
         * 刷新令牌
         */
        REFRESH_TOKEN("refresh_token", "刷新令牌");

        private final String code;
        private final String description;

        TokenType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }

    @Override
    public String toString() {
        return String.format("TokenExpiredException{tokenType=%s, message='%s'}", tokenType, getMessage());
    }
}
