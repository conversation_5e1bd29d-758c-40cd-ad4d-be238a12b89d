package cn.com.handthing.starter.crypto.configuration;

import cn.com.handthing.starter.crypto.core.KeyProvider;
import cn.com.handthing.starter.crypto.core.KeyProviderFactory;
import cn.com.handthing.starter.crypto.exception.CryptoException;
import cn.com.handthing.starter.crypto.properties.CryptoProperties;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.beans.factory.support.AbstractBeanDefinition;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.beans.factory.support.BeanDefinitionRegistryPostProcessor;
import org.springframework.boot.context.properties.bind.Binder;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.EnvironmentAware;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.util.ClassUtils;

/**
 * Crypto Starter 的主自动配置类。
 * <p>
 * 负责根据配置动态地、编程方式地注册所有的加密器、摘要器 Bean。
 * 使用 {@link BeanDefinitionRegistryPostProcessor} 以便在 Spring 上下文的早期阶段进行操作，
 * 这使得我们能够完全控制 Bean 的定义和注册过程。
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Configuration(proxyBeanMethods = false)
public class CryptoAutoConfiguration implements BeanDefinitionRegistryPostProcessor, EnvironmentAware, ApplicationContextAware {

    private static final Logger log = LoggerFactory.getLogger(CryptoAutoConfiguration.class);

    private CryptoProperties properties;
    private ApplicationContext applicationContext;
    private MeterRegistry meterRegistry;

    @Override
    public void setEnvironment(Environment environment) {
        // 在早期阶段通过 Binder API 绑定配置，这比 @EnableConfigurationProperties 更早、更灵活
        this.properties = Binder.get(environment)
                .bind("handthing.crypto", CryptoProperties.class)
                .orElseGet(CryptoProperties::new);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @Override
    public void postProcessBeanDefinitionRegistry(BeanDefinitionRegistry registry) throws BeansException {
        if (!properties.isEnabled()) {
            log.info("HandThing Crypto Starter is disabled via handthing.crypto.enabled=false.");
            return;
        }

        log.info("HandThing Crypto Starter is enabled. Initializing crypto processors...");
        this.meterRegistry = resolveMeterRegistry();

        KeyProviderFactory keyProviderFactory = new KeyProviderFactory(applicationContext);

        properties.getSymmetric().forEach((name, config) ->
                registerBean(registry, name, "SymmetricEncryptor", config, keyProviderFactory)
        );
        properties.getAsymmetric().forEach((name, config) ->
                registerBean(registry, name, "AsymmetricEncryptor", config, keyProviderFactory)
        );
        properties.getDigest().forEach((name, config) ->
                registerBean(registry, name, "Digester", config, keyProviderFactory)
        );
    }

    private void registerBean(BeanDefinitionRegistry registry, String name, String beanTypeSuffix, CryptoProperties.BaseConfig config, KeyProviderFactory keyProviderFactory) {
        String beanName = name + beanTypeSuffix;
        try {
            Class<?> implClass = resolveImplementationClass(config.getAlgorithm(), beanTypeSuffix);
            BeanDefinitionBuilder builder = BeanDefinitionBuilder.genericBeanDefinition(implClass);

            // 注入通用构造函数参数
            // beanName for identification
            builder.addConstructorArgValue(name);
            // MeterRegistry for metrics
            builder.addConstructorArgValue(this.meterRegistry);

            // 根据处理器类型注入特定的构造函数参数
            if (config instanceof CryptoProperties.SymmetricConfig symConfig) {
                KeyProvider keyProvider = keyProviderFactory.create(symConfig.getKey());
                builder.addConstructorArgValue(keyProvider);
            } else if (config instanceof CryptoProperties.AsymmetricConfig asymConfig) {
                KeyProvider publicKeyProvider = keyProviderFactory.create(asymConfig.getPublicKey());
                KeyProvider privateKeyProvider = keyProviderFactory.create(asymConfig.getPrivateKey());
                builder.addConstructorArgValue(publicKeyProvider);
                builder.addConstructorArgValue(privateKeyProvider);
            } else if (config instanceof CryptoProperties.DigestConfig digestConfig) {
                builder.addConstructorArgValue(digestConfig.getSalt());
            }

            // V3: 如果启用了动态刷新，并且 Spring Cloud 在 classpath 中，则设置 bean 的 scope 为 "refresh"
            if (properties.getRefresh().isEnabled()) {
                if (ClassUtils.isPresent("org.springframework.cloud.context.config.annotation.RefreshScope", null)) {
                    builder.setScope("refresh");
                    log.debug("Bean [{}] is configured with 'refresh' scope.", beanName);
                } else {
                    log.warn("Refresh scope is enabled in properties but Spring Cloud context is not found. Bean [{}] will not be refreshable.", beanName);
                }
            }

            AbstractBeanDefinition beanDefinition = builder.getBeanDefinition();
            registry.registerBeanDefinition(beanName, beanDefinition);

            log.info("Successfully registered bean [{}], type [{}], algorithm [{}].", beanName, beanTypeSuffix, config.getAlgorithm());

        } catch (ClassNotFoundException e) {
            log.warn("Cannot register bean [{}]: A required class for algorithm [{}] was not found. Please check your dependencies (e.g., BouncyCastle for SM algorithms).", beanName, config.getAlgorithm());
        } catch (CryptoException e) {
            // CryptoException 是我们预期的配置或初始化错误，级别设置为 ERROR
            log.error("Failed to register bean [{}]: {}", beanName, e.getMessage());
        } catch (Exception e) {
            // 其他未知异常
            log.error("Failed to register bean [{}] due to an unexpected error.", beanName, e);
        }
    }

    private Class<?> resolveImplementationClass(String algorithm, String beanTypeSuffix) throws ClassNotFoundException {
        // 使用一个简单的映射逻辑将算法名称转换为实现类名称
        String implName = switch (algorithm.toUpperCase()) {
            case "AES" -> "Aes";
            case "SM4" -> "Sm4";
            case "RSA" -> "Rsa";
            case "SM2" -> "Sm2";
            case "SM3" -> "Sm3";
            case "SHA256" -> "Sha256";
            default -> throw new IllegalArgumentException("Unsupported algorithm: " + algorithm);
        };
        // 拼接完整的类名
        String className = String.format("cn.com.handthing.springboot3.starter.crypto.service.%s%s", implName, beanTypeSuffix);
        return Class.forName(className);
    }

    private MeterRegistry resolveMeterRegistry() {
        // 如果禁用了指标，则返回一个无操作的 MeterRegistry，以避免空指针
        if (!properties.getObservability().isMetricsEnabled()) {
            log.debug("Crypto metrics are disabled. Using a no-op MeterRegistry.");
            return new SimpleMeterRegistry();
        }
        try {
            // 尝试从 Spring 上下文中获取用户配置的 MeterRegistry
            return applicationContext.getBean(MeterRegistry.class);
        } catch (BeansException e) {
            log.info("MeterRegistry bean not found in the application context. Crypto metrics will be disabled. Using a no-op registry.");
            return new SimpleMeterRegistry();
        }
    }

    @Override
    public void postProcessBeanFactory(ConfigurableListableBeanFactory beanFactory) throws BeansException {
        // 在这个后处理器中无需执行任何操作
    }
}