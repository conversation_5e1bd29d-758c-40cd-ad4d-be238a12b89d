package cn.com.handthing.starter.cache.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * 缓存配置属性
 * <p>
 * 定义缓存的全局配置和各个缓存区域的独立配置
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
@ConfigurationProperties(prefix = "handthing.cache")
public class CacheProperties {

    /**
     * 是否启用缓存功能
     */
    private boolean enabled = true;

    /**
     * 缓存类型 (memory, caffeine, redis, level)
     */
    private String type = "memory";

    /**
     * 是否允许缓存null值
     */
    private boolean allowNullValues = true;

    /**
     * 默认过期时间
     */
    private Duration defaultTtl = Duration.ofMinutes(30);

    /**
     * 缓存区域配置映射
     * key: 缓存区域名称
     * value: 缓存区域配置
     */
    private Map<String, CacheSpec> specs = new HashMap<>();

    /**
     * 缓存区域配置
     */
    @Data
    public static class CacheSpec {

        /**
         * 过期时间
         */
        private Duration ttl;

        /**
         * 最大缓存条目数
         */
        private Long maxSize;

        /**
         * 初始容量
         */
        private Integer initialCapacity;

        /**
         * 是否允许null值
         */
        private Boolean allowNullValues;

        /**
         * 自定义配置字符串（如Caffeine的spec）
         */
        private String spec;

        /**
         * 获取有效的TTL，如果未设置则使用默认值
         *
         * @param defaultTtl 默认TTL
         * @return 有效的TTL
         */
        public Duration getEffectiveTtl(Duration defaultTtl) {
            return ttl != null ? ttl : defaultTtl;
        }

        /**
         * 获取有效的allowNullValues，如果未设置则使用默认值
         *
         * @param defaultAllowNullValues 默认allowNullValues
         * @return 有效的allowNullValues
         */
        public boolean getEffectiveAllowNullValues(boolean defaultAllowNullValues) {
            return allowNullValues != null ? allowNullValues : defaultAllowNullValues;
        }
    }
}