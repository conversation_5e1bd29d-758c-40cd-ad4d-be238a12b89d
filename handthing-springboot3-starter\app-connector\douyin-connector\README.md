# 抖音连接器 (Douyin Connector)

## 概述

抖音连接器提供了与抖音开放平台的集成能力，支持个人创作者和企业用户的内容创作和管理。通过统一的API接口，可以轻松实现用户认证、视频上传、直播管理、数据分析等功能。

## 特性

- ✅ **OAuth2.0认证** - 支持抖音用户授权登录
- ✅ **视频管理** - 支持视频上传、编辑、删除等操作
- ✅ **直播功能** - 支持直播间管理和直播数据获取
- ✅ **数据分析** - 获取视频数据、用户数据、直播数据统计
- ✅ **电商功能** - 支持商品管理和订单处理
- ✅ **粉丝管理** - 获取粉丝信息和互动数据
- ✅ **多环境支持** - 支持开发、测试、生产环境配置

## 快速开始

### 1. 添加依赖

```xml
<dependency>
    <groupId>cn.com.handthing.springboot3.starter</groupId>
    <artifactId>douyin-connector</artifactId>
    <version>1.0.0-SNAPSHOT</version>
</dependency>
```

### 2. 配置文件

```yaml
handthing:
  connector:
    douyin:
      enabled: true
      client-key: "your-client-key"
      client-secret: "your-client-secret"
      app-type: "web"
      enterprise-mode: false
      sandbox-mode: true
      video-upload-enabled: true
      live-enabled: false
      ecommerce-enabled: false
      redirect-uri: "http://your-domain.com/callback/douyin"
```

### 3. 基本使用

```java
@RestController
public class DouyinController {
    
    @Autowired
    private DouyinService douyinService;
    
    @Autowired
    private AuthService authService;
    
    // 获取授权URL
    @GetMapping("/douyin/auth")
    public String getAuthUrl() {
        return authService.getAuthorizationUrl(
            PlatformType.DOUYIN, 
            "state", 
            "http://your-domain.com/callback/douyin"
        );
    }
    
    // 上传视频
    @PostMapping("/douyin/video/upload")
    public Map<String, Object> uploadVideo(@RequestParam String accessToken,
                                          @RequestParam String title,
                                          @RequestParam String videoUrl,
                                          @RequestParam String cover) {
        List<String> tags = Arrays.asList("生活", "美食");
        return douyinService.video().uploadVideo(accessToken, title, videoUrl, cover, tags);
    }
    
    // 获取视频列表
    @GetMapping("/douyin/videos")
    public Map<String, Object> getVideoList(@RequestParam String accessToken,
                                           @RequestParam(defaultValue = "1") Integer page,
                                           @RequestParam(defaultValue = "20") Integer pageSize) {
        return douyinService.video().getVideoList(accessToken, page, pageSize);
    }
    
    // 获取数据统计
    @GetMapping("/douyin/analytics/video/{videoId}")
    public Map<String, Object> getVideoAnalytics(@RequestParam String accessToken,
                                                @PathVariable String videoId) {
        return douyinService.analytics().getVideoAnalytics(accessToken, videoId);
    }
}
```

## 配置说明

| 配置项 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| `enabled` | Boolean | 是 | false | 是否启用抖音连接器 |
| `client-key` | String | 是 | - | 应用的ClientKey |
| `client-secret` | String | 是 | - | 应用的ClientSecret |
| `app-type` | String | 否 | web | 应用类型(web/mobile/server) |
| `enterprise-mode` | Boolean | 否 | false | 是否为企业模式 |
| `sandbox-mode` | Boolean | 否 | false | 是否使用沙箱环境 |
| `video-upload-enabled` | Boolean | 否 | true | 是否启用视频上传功能 |
| `live-enabled` | Boolean | 否 | false | 是否启用直播功能 |
| `ecommerce-enabled` | Boolean | 否 | false | 是否启用电商功能 |
| `redirect-uri` | String | 否 | - | OAuth回调地址 |

## API文档

### 视频API (DouyinVideoApi)

#### 上传视频
```java
Map<String, Object> uploadVideo(String accessToken, String title, String videoUrl, String cover, List<String> tags)
```

#### 获取视频列表
```java
Map<String, Object> getVideoList(String accessToken, Integer page, Integer pageSize)
```

#### 获取视频详情
```java
Map<String, Object> getVideoDetail(String accessToken, String videoId)
```

#### 删除视频
```java
Map<String, Object> deleteVideo(String accessToken, String videoId)
```

### 直播API (DouyinLiveApi)

#### 创建直播间
```java
Map<String, Object> createLiveRoom(String accessToken, String title, String cover)
```

#### 获取直播间信息
```java
Map<String, Object> getLiveRoomInfo(String accessToken, String roomId)
```

#### 获取直播数据
```java
Map<String, Object> getLiveData(String accessToken, String roomId)
```

### 数据分析API (DouyinAnalyticsApi)

#### 获取视频数据
```java
Map<String, Object> getVideoAnalytics(String accessToken, String videoId)
```

#### 获取用户数据
```java
Map<String, Object> getUserAnalytics(String accessToken, LocalDate startDate, LocalDate endDate)
```

#### 获取粉丝数据
```java
Map<String, Object> getFansAnalytics(String accessToken)
```

## 常见问题

### Q: 如何获取抖音的ClientKey和ClientSecret？
A: 登录抖音开放平台，在"应用管理"中创建应用，即可获取ClientKey和ClientSecret。

### Q: 沙箱环境和生产环境有什么区别？
A: 沙箱环境用于开发测试，数据不会真实发布；生产环境的操作会真实生效。

### Q: 视频上传有什么限制？
A: 视频大小不能超过128MB，时长不能超过15分钟，支持MP4格式。

### Q: 如何开通直播功能？
A: 需要满足一定的粉丝数量和内容质量要求，在抖音APP中申请开通直播权限。

## 更多信息

- [抖音开放平台文档](https://developer.open-douyin.com/)
- [视频管理API](https://developer.open-douyin.com/docs/resource/zh-CN/dop/develop/openapi/video-management/video-upload)
- [数据开放API](https://developer.open-douyin.com/docs/resource/zh-CN/dop/develop/openapi/data-open-service/data-open-service-overview)
