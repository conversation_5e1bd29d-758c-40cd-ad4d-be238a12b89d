# HandThing 多租户认证系统 (Tenant Auth Starter)

[![Maven Central](https://img.shields.io/maven-central/v/cn.com.handthing.springboot3.starter/tenant-auth-starter.svg)](https://search.maven.org/artifact/cn.com.handthing.springboot3.starter/tenant-auth-starter)
[![License](https://img.shields.io/badge/License-Apache%202.0-blue.svg)](https://opensource.org/licenses/Apache-2.0)
[![Spring Boot](https://img.shields.io/badge/Spring%20Boot-3.5+-green.svg)](https://spring.io/projects/spring-boot)

## 📖 简介

HandThing 多租户认证系统是一个基于 Spring Boot 3 的企业级多租户 SaaS 认证解决方案。它提供了完整的租户隔离、配置管理、上下文管理等功能，支持真正的多租户应用场景。

### 🌟 核心特性

- **🏢 多租户支持**：完整的租户隔离和管理
- **⚙️ 配置隔离**：每个租户独立的配置管理
- **🔄 上下文管理**：线程级租户上下文，自动清理
- **🚀 高性能**：基于 ThreadLocal 的高效实现
- **🔧 易集成**：Spring Boot Starter，开箱即用
- **📊 监控支持**：完整的健康检查和统计功能

## 🚀 快速开始

### 1. 添加依赖

```xml
<dependency>
    <groupId>cn.com.handthing.springboot3.starter</groupId>
    <artifactId>tenant-auth-starter</artifactId>
    <version>1.0.0-SNAPSHOT</version>
</dependency>
```

### 2. 配置数据库

```yaml
spring:
  datasource:
    url: *****************************************
    username: your_username
    password: your_password
  jpa:
    hibernate:
      ddl-auto: update
```

### 3. 启用多租户

```yaml
handthing:
  auth:
    saas:
      enabled: true
      default-tenant: default
      tenant-required: false
      multi-tenant: true
```

### 4. 使用租户上下文

```java
@RestController
public class YourController {
    
    @GetMapping("/api/current-tenant")
    public String getCurrentTenant() {
        return TenantContextHolder.getCurrentTenantId();
    }
    
    @GetMapping("/api/tenant-config")
    public String getTenantConfig(@Autowired TenantConfigService configService) {
        return configService.getConfig("your.config.key");
    }
}
```

## 📋 功能特性

### 🏢 租户管理

#### 租户解析器
- **HTTP 头部解析**：支持 `X-Tenant-ID`、`Tenant-ID` 等头部
- **域名解析**：支持基于域名的租户识别（扩展功能）
- **自定义解析器**：支持自定义租户解析逻辑

#### 租户上下文
```java
// 获取当前租户ID
String tenantId = TenantContextHolder.getCurrentTenantId();

// 获取当前租户信息
Tenant tenant = TenantContextHolder.getCurrentTenant();

// 检查租户是否存在
boolean exists = TenantContextHolder.hasTenant();
```

### ⚙️ 配置管理

#### 租户配置服务
```java
@Autowired
private TenantConfigService configService;

// 获取配置
String value = configService.getConfig("jwt.secret");

// 获取所有配置
Map<String, String> configs = configService.getAllConfigs();

// 获取配置（带默认值）
String value = configService.getConfig("custom.key", "default-value");
```

#### 配置类型支持
- **STRING**：字符串配置
- **INTEGER**：整数配置
- **BOOLEAN**：布尔配置
- **PASSWORD**：敏感信息配置

### 🔄 过滤器集成

#### 租户解析过滤器
- **优先级**：-200（早于认证过滤器）
- **排除路径**：支持静态资源、管理端点等排除
- **错误处理**：优雅的租户不存在错误处理

```yaml
handthing:
  auth:
    saas:
      filter:
        order: -200
        exclude-paths:
          - "/actuator/**"
          - "/static/**"
          - "/h2-console/**"
```

## 🛠️ 配置说明

### 完整配置示例

```yaml
handthing:
  auth:
    saas:
      # 基础配置
      enabled: true                    # 是否启用多租户
      default-tenant: default          # 默认租户ID
      tenant-required: false           # 是否要求必须有租户
      multi-tenant: true               # 是否启用多租户模式
      
      # 缓存配置
      cache: true                      # 是否启用缓存
      
      # 过滤器配置
      filter:
        enabled: true                  # 是否启用过滤器
        order: -200                    # 过滤器优先级
        exclude-paths:                 # 排除路径
          - "/actuator/**"
          - "/error"
          - "/static/**"
          - "/h2-console/**"
      
      # 租户解析器配置
      resolver:
        http-header:
          enabled: true                # 是否启用HTTP头部解析器
          primary-header: X-Tenant-ID  # 主要头部名称
          fallback-headers:            # 备用头部名称
            - Tenant-ID
            - X-Tenant
            - Tenant
          case-sensitive: false        # 是否区分大小写
          order: 20                    # 解析器优先级
      
      # 验证配置
      validation: true                 # 是否启用验证
      
      # 监控配置
      monitoring: true                 # 是否启用监控
```

### 配置属性说明

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `enabled` | Boolean | true | 是否启用多租户功能 |
| `default-tenant` | String | default | 默认租户ID |
| `tenant-required` | Boolean | false | 是否要求必须有租户 |
| `multi-tenant` | Boolean | true | 是否启用多租户模式 |
| `cache` | Boolean | true | 是否启用配置缓存 |
| `filter.enabled` | Boolean | true | 是否启用租户过滤器 |
| `filter.order` | Integer | -200 | 过滤器优先级 |
| `resolver.http-header.enabled` | Boolean | true | 是否启用HTTP头部解析器 |
| `resolver.http-header.primary-header` | String | X-Tenant-ID | 主要HTTP头部名称 |

## 📊 API 接口

### 租户管理接口

#### 1. 健康检查
```http
GET /api/tenant/health
```

响应示例：
```json
{
  "status": "UP",
  "tenantId": "default",
  "tenantExists": true,
  "tenantName": "默认租户",
  "contextHolder": "OK",
  "configService": "OK",
  "timestamp": "1753788539960"
}
```

#### 2. 当前租户信息
```http
GET /api/tenant/current
```

响应示例：
```json
{
  "tenantId": "default",
  "tenantName": "默认租户",
  "tenantStatus": "ACTIVE",
  "isActive": true,
  "contextExists": true,
  "hasTenantId": true,
  "attributes": ["resolved_at"],
  "contextInfo": "Tenant{id='default', name='默认租户', status=ACTIVE, attributes=1}"
}
```

#### 3. 租户配置
```http
GET /api/tenant/config
GET /api/tenant/config?configKey=jwt.secret
```

响应示例：
```json
{
  "configKey": "jwt.secret",
  "tenantId": "default",
  "configValue": "handthing-auth-default-secret-key-2024"
}
```

#### 4. 租户列表
```http
GET /api/tenant/list
```

响应示例：
```json
{
  "success": true,
  "tenantCount": 3,
  "tenantIds": ["default", "demo", "test"],
  "tenantInfos": {
    "default": {
      "name": "默认租户",
      "exists": true,
      "status": "ACTIVE"
    },
    "demo": {
      "name": "演示租户",
      "exists": true,
      "status": "ACTIVE"
    },
    "test": {
      "name": "测试租户",
      "exists": true,
      "status": "TRIAL"
    }
  }
}
```

#### 5. 租户统计
```http
GET /api/tenant/stats
```

## 🗄️ 数据库设计

### 租户配置表 (tenant_configs)

| 字段 | 类型 | 说明 |
|------|------|------|
| id | BIGINT | 主键ID |
| tenant_id | VARCHAR(64) | 租户ID |
| config_key | VARCHAR(128) | 配置键 |
| config_value | TEXT | 配置值 |
| config_type | VARCHAR(32) | 配置类型 |
| description | VARCHAR(255) | 配置描述 |
| enabled | BOOLEAN | 是否启用 |
| sensitive | BOOLEAN | 是否敏感信息 |
| created_at | TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | 更新时间 |
| created_by | VARCHAR(64) | 创建者 |
| updated_by | VARCHAR(64) | 更新者 |
| version | BIGINT | 版本号 |

### 租户信息表 (tenants)

| 字段 | 类型 | 说明 |
|------|------|------|
| id | BIGINT | 主键ID |
| tenant_id | VARCHAR(64) | 租户ID（唯一） |
| tenant_name | VARCHAR(128) | 租户名称 |
| tenant_code | VARCHAR(64) | 租户编码 |
| status | VARCHAR(32) | 租户状态 |
| description | TEXT | 租户描述 |
| contact_email | VARCHAR(255) | 联系邮箱 |
| contact_phone | VARCHAR(32) | 联系电话 |
| contact_person | VARCHAR(128) | 联系人 |
| domain | VARCHAR(255) | 域名 |
| logo_url | VARCHAR(512) | Logo URL |
| theme_config | VARCHAR(2000) | 主题配置 |
| feature_flags | VARCHAR(2000) | 功能标志 |
| quota_config | VARCHAR(2000) | 配额配置 |
| created_at | TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | 更新时间 |
| created_by | VARCHAR(64) | 创建者 |
| updated_by | VARCHAR(64) | 更新者 |
| version | BIGINT | 版本号 |

## 🔧 高级用法

### 自定义租户解析器

```java
@Component
public class CustomTenantResolver implements TenantResolver {
    
    @Override
    public String resolveTenantId(HttpServletRequest request) {
        // 自定义租户解析逻辑
        String subdomain = extractSubdomain(request);
        return mapSubdomainToTenantId(subdomain);
    }
    
    @Override
    public int getOrder() {
        return 10; // 优先级
    }
}
```

### 租户配置监听器

```java
@Component
public class TenantConfigListener {
    
    @EventListener
    public void onTenantConfigChanged(TenantConfigChangedEvent event) {
        // 处理租户配置变更
        String tenantId = event.getTenantId();
        String configKey = event.getConfigKey();
        String newValue = event.getNewValue();
        
        // 自定义处理逻辑
    }
}
```

### 租户上下文注解

```java
@RestController
public class TenantController {
    
    @GetMapping("/api/tenant-info")
    @TenantRequired // 要求必须有租户
    public ResponseEntity<TenantInfo> getTenantInfo() {
        String tenantId = TenantContextHolder.getCurrentTenantId();
        // 业务逻辑
        return ResponseEntity.ok(tenantInfo);
    }
}
```

## 🧪 测试

### 单元测试

```java
@SpringBootTest
@TestPropertySource(properties = {
    "handthing.auth.saas.enabled=true",
    "handthing.auth.saas.default-tenant=test"
})
class TenantAuthTest {
    
    @Autowired
    private TenantConfigService configService;
    
    @Test
    void testTenantConfig() {
        // 设置租户上下文
        TenantContextHolder.setCurrentTenantId("test");
        
        // 测试配置获取
        String config = configService.getConfig("test.key");
        assertThat(config).isNotNull();
        
        // 清理上下文
        TenantContextHolder.clear();
    }
}
```

### 集成测试

项目提供了完整的测试页面，启动应用后访问：
```
http://localhost:8081/tenant-test.html
```

## 📈 性能优化

### 缓存配置

```yaml
handthing:
  auth:
    saas:
      cache: true
      cache-config:
        type: caffeine
        ttl: 300
        maximum-size: 1000
```

### 数据库优化

- 使用连接池（推荐 HikariCP）
- 配置合适的索引
- 启用查询缓存

### 监控指标

系统提供以下监控指标：
- 租户解析成功率
- 配置查询响应时间
- 租户上下文命中率
- 数据库连接池状态

## 🔒 安全考虑

### 租户隔离

- **数据隔离**：通过租户ID确保数据完全隔离
- **配置隔离**：每个租户独立的配置空间
- **上下文隔离**：线程级别的租户上下文

### 敏感信息保护

- **密码配置**：标记为敏感信息的配置会被特殊处理
- **日志脱敏**：敏感配置不会出现在日志中
- **传输加密**：建议使用 HTTPS 传输

## 🐛 故障排除

### 常见问题

#### 1. 租户解析失败
```
错误：Tenant not found
解决：检查HTTP头部是否正确设置，确认租户在数据库中存在
```

#### 2. 配置获取失败
```
错误：Config not found
解决：检查租户配置是否正确初始化，确认配置键名正确
```

#### 3. 过滤器不生效
```
错误：过滤器未执行
解决：检查自动配置是否启用，确认过滤器优先级设置
```

### 调试模式

启用调试日志：
```yaml
logging:
  level:
    cn.com.handthing.starter.tenant: DEBUG
```

## 📚 更多资源

- [HandThing 官方文档](https://docs.handthing.com)
- [Spring Boot 官方文档](https://spring.io/projects/spring-boot)
- [多租户架构最佳实践](https://docs.handthing.com/multi-tenant)

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

本项目采用 Apache License 2.0 许可证。详见 [LICENSE](LICENSE) 文件。

## 👥 作者

- **HandThing Team** - *Initial work* - [HandThing](https://github.com/handthing)

---

**HandThing 多租户认证系统** - 让多租户应用开发更简单！ 🚀
