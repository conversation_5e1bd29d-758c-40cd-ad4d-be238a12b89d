package cn.com.handthing.starter.auth.core;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 认证上下文
 * <p>
 * 封装认证过程中的上下文信息，包括请求信息、响应信息、用户信息等。
 * 用于在认证流程中传递和共享数据。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AuthenticationContext {

    /**
     * 认证请求
     */
    private AuthenticationRequest request;

    /**
     * 认证响应
     */
    private AuthenticationResponse response;

    /**
     * 用户信息
     */
    private UserInfo userInfo;

    /**
     * JWT声明
     */
    private JwtClaims jwtClaims;

    /**
     * 认证提供者名称
     */
    private String providerName;

    /**
     * 认证开始时间
     */
    private LocalDateTime startTime;

    /**
     * 认证结束时间
     */
    private LocalDateTime endTime;

    /**
     * 认证是否成功
     */
    private boolean success;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 异常信息
     */
    private Throwable exception;

    /**
     * 扩展属性
     */
    private Map<String, Object> attributes;

    /**
     * 构造函数
     *
     * @param request 认证请求
     */
    public AuthenticationContext(AuthenticationRequest request) {
        this.startTime = LocalDateTime.now();
        this.attributes = new HashMap<>();
        this.request = request;
    }

    /**
     * 添加扩展属性
     *
     * @param key   属性键
     * @param value 属性值
     */
    public void addAttribute(String key, Object value) {
        if (attributes == null) {
            attributes = new HashMap<>();
        }
        attributes.put(key, value);
    }

    /**
     * 获取扩展属性
     *
     * @param key 属性键
     * @return 属性值
     */
    public Object getAttribute(String key) {
        return attributes != null ? attributes.get(key) : null;
    }

    /**
     * 获取扩展属性（指定类型）
     *
     * @param key   属性键
     * @param clazz 属性类型
     * @param <T>   泛型类型
     * @return 属性值
     */
    @SuppressWarnings("unchecked")
    public <T> T getAttribute(String key, Class<T> clazz) {
        Object value = getAttribute(key);
        if (value != null && clazz.isInstance(value)) {
            return (T) value;
        }
        return null;
    }

    /**
     * 标记认证成功
     *
     * @param response 认证响应
     * @param userInfo 用户信息
     */
    public void markSuccess(AuthenticationResponse response, UserInfo userInfo) {
        this.success = true;
        this.response = response;
        this.userInfo = userInfo;
        this.endTime = LocalDateTime.now();
    }

    /**
     * 标记认证失败
     *
     * @param errorMessage 错误信息
     * @param exception    异常信息
     */
    public void markFailure(String errorMessage, Throwable exception) {
        this.success = false;
        this.errorMessage = errorMessage;
        this.exception = exception;
        this.endTime = LocalDateTime.now();
    }

    /**
     * 标记认证失败
     *
     * @param response 认证响应
     */
    public void markFailure(AuthenticationResponse response) {
        this.success = false;
        this.response = response;
        this.errorMessage = response.getFullErrorMessage();
        this.endTime = LocalDateTime.now();
    }

    /**
     * 获取认证耗时（毫秒）
     *
     * @return 认证耗时
     */
    public long getDuration() {
        if (startTime == null) {
            return 0;
        }
        
        LocalDateTime end = endTime != null ? endTime : LocalDateTime.now();
        return java.time.Duration.between(startTime, end).toMillis();
    }

    /**
     * 获取授权类型
     *
     * @return 授权类型
     */
    public GrantType getGrantType() {
        return request != null ? request.getGrantType() : null;
    }

    /**
     * 获取客户端ID
     *
     * @return 客户端ID
     */
    public String getClientId() {
        return request != null ? request.getClientId() : null;
    }

    /**
     * 获取用户ID
     *
     * @return 用户ID
     */
    public String getUserId() {
        return userInfo != null ? userInfo.getUserId() : null;
    }

    /**
     * 获取用户名
     *
     * @return 用户名
     */
    public String getUsername() {
        return userInfo != null ? userInfo.getUsername() : null;
    }

    /**
     * 获取访问令牌
     *
     * @return 访问令牌
     */
    public String getAccessToken() {
        return response != null ? response.getAccessToken() : null;
    }

    /**
     * 获取刷新令牌
     *
     * @return 刷新令牌
     */
    public String getRefreshToken() {
        return response != null ? response.getRefreshToken() : null;
    }

    /**
     * 判断是否已完成
     *
     * @return 如果已完成返回true，否则返回false
     */
    public boolean isCompleted() {
        return endTime != null;
    }

    /**
     * 判断是否正在进行中
     *
     * @return 如果正在进行中返回true，否则返回false
     */
    public boolean isInProgress() {
        return startTime != null && endTime == null;
    }

    /**
     * 获取上下文描述
     *
     * @return 上下文描述
     */
    public String getDescription() {
        StringBuilder sb = new StringBuilder();
        sb.append("AuthenticationContext{");

        if (request != null) {
            sb.append("grantType=").append(request.getGrantType());
            sb.append(", clientId=").append(request.getClientId());
        }

        if (userInfo != null) {
            if (request != null) sb.append(", ");
            sb.append("userId=").append(userInfo.getUserId());
            sb.append(", username=").append(userInfo.getUsername());
        }

        sb.append(", success=").append(success);
        sb.append(", duration=").append(getDuration()).append("ms");

        if (!success && errorMessage != null) {
            sb.append(", error=").append(errorMessage);
        }

        sb.append("}");
        return sb.toString();
    }

    @Override
    public String toString() {
        return getDescription();
    }
}
