package cn.com.handthing.starter.connector.wecom.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 企业微信消息模型
 * <p>
 * 企业微信消息相关的数据结构
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class WeComMessage {

    /**
     * 消息类型
     */
    private String msgType;

    /**
     * 接收用户ID
     */
    private String toUser;

    /**
     * 接收部门ID
     */
    private String toParty;

    /**
     * 接收标签ID
     */
    private String toTag;

    /**
     * 应用ID
     */
    private String agentId;

    /**
     * 消息内容
     */
    private Object content;

    /**
     * 是否保密消息
     */
    private Integer safe = 0;

    /**
     * 是否开启重复消息检查
     */
    private Integer enableDuplicateCheck = 0;

    /**
     * 重复消息检查的时间间隔
     */
    private Integer duplicateCheckInterval = 1800;

    /**
     * 消息发送结果
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class SendResult {

        /**
         * 错误码，0表示成功
         */
        @JsonProperty("errcode")
        private Integer errcode;

        /**
         * 错误信息
         */
        @JsonProperty("errmsg")
        private String errmsg;

        /**
         * 无效的用户ID列表
         */
        @JsonProperty("invaliduser")
        private String invalidUser;

        /**
         * 无效的部门ID列表
         */
        @JsonProperty("invalidparty")
        private String invalidParty;

        /**
         * 无效的标签ID列表
         */
        @JsonProperty("invalidtag")
        private String invalidTag;

        /**
         * 没有基础接口许可的用户ID列表
         */
        @JsonProperty("unlicenseduser")
        private String unlicensedUser;

        /**
         * 消息ID
         */
        @JsonProperty("msgid")
        private String msgId;

        /**
         * 响应包
         */
        @JsonProperty("response_code")
        private String responseCode;

        /**
         * 检查响应是否成功
         *
         * @return 如果成功返回true，否则返回false
         */
        public boolean isSuccess() {
            return errcode != null && errcode == 0;
        }

        /**
         * 获取错误描述
         *
         * @return 错误描述
         */
        public String getErrorDescription() {
            if (isSuccess()) {
                return "Success";
            }
            return String.format("Error %d: %s", errcode, errmsg);
        }

        /**
         * 检查是否有无效用户
         *
         * @return 如果有无效用户返回true，否则返回false
         */
        public boolean hasInvalidUser() {
            return invalidUser != null && !invalidUser.trim().isEmpty();
        }

        /**
         * 检查是否有无效部门
         *
         * @return 如果有无效部门返回true，否则返回false
         */
        public boolean hasInvalidParty() {
            return invalidParty != null && !invalidParty.trim().isEmpty();
        }

        /**
         * 检查是否有无效标签
         *
         * @return 如果有无效标签返回true，否则返回false
         */
        public boolean hasInvalidTag() {
            return invalidTag != null && !invalidTag.trim().isEmpty();
        }

        /**
         * 检查是否有未授权用户
         *
         * @return 如果有未授权用户返回true，否则返回false
         */
        public boolean hasUnlicensedUser() {
            return unlicensedUser != null && !unlicensedUser.trim().isEmpty();
        }

        /**
         * 获取发送摘要
         *
         * @return 发送摘要
         */
        public String getSendSummary() {
            StringBuilder summary = new StringBuilder();
            summary.append("Send result: ").append(isSuccess() ? "Success" : "Failed");
            
            if (msgId != null) {
                summary.append(", MsgId: ").append(msgId);
            }
            
            if (hasInvalidUser()) {
                summary.append(", Invalid users: ").append(invalidUser);
            }
            
            if (hasInvalidParty()) {
                summary.append(", Invalid parties: ").append(invalidParty);
            }
            
            if (hasInvalidTag()) {
                summary.append(", Invalid tags: ").append(invalidTag);
            }
            
            if (hasUnlicensedUser()) {
                summary.append(", Unlicensed users: ").append(unlicensedUser);
            }
            
            return summary.toString();
        }
    }

    /**
     * 文本消息内容
     */
    @Data
    public static class TextContent {
        private String content;
    }

    /**
     * 图片消息内容
     */
    @Data
    public static class ImageContent {
        @JsonProperty("media_id")
        private String mediaId;
    }

    /**
     * 文件消息内容
     */
    @Data
    public static class FileContent {
        @JsonProperty("media_id")
        private String mediaId;
    }

    /**
     * 卡片消息内容
     */
    @Data
    public static class TextCardContent {
        private String title;
        private String description;
        private String url;
        @JsonProperty("btntxt")
        private String btnText;
    }

    /**
     * Markdown消息内容
     */
    @Data
    public static class MarkdownContent {
        private String content;
    }
}
