{"groups": [{"name": "handthing.auth.saas", "type": "cn.com.handthing.starter.tenant.config.SaaSAuthProperties", "sourceType": "cn.com.handthing.starter.tenant.config.SaaSAuthProperties"}, {"name": "handthing.auth.saas.cache", "type": "cn.com.handthing.starter.tenant.config.SaaSAuthProperties$CacheConfig", "sourceType": "cn.com.handthing.starter.tenant.config.SaaSAuthProperties", "sourceMethod": "public cn.com.handthing.starter.tenant.config.SaaSAuthProperties.CacheConfig getCache() "}, {"name": "handthing.auth.saas.filter", "type": "cn.com.handthing.starter.tenant.config.SaaSAuthProperties$FilterConfig", "sourceType": "cn.com.handthing.starter.tenant.config.SaaSAuthProperties", "sourceMethod": "public cn.com.handthing.starter.tenant.config.SaaSAuthProperties.FilterConfig getFilter() "}, {"name": "handthing.auth.saas.monitoring", "type": "cn.com.handthing.starter.tenant.config.SaaSAuthProperties$MonitoringConfig", "sourceType": "cn.com.handthing.starter.tenant.config.SaaSAuthProperties", "sourceMethod": "public cn.com.handthing.starter.tenant.config.SaaSAuthProperties.MonitoringConfig getMonitoring() "}, {"name": "handthing.auth.saas.validation", "type": "cn.com.handthing.starter.tenant.config.SaaSAuthProperties$ValidationConfig", "sourceType": "cn.com.handthing.starter.tenant.config.SaaSAuthProperties", "sourceMethod": "public cn.com.handthing.starter.tenant.config.SaaSAuthProperties.ValidationConfig getValidation() "}, {"name": "handthing.auth.tenant.resolver", "type": "cn.com.handthing.starter.tenant.config.TenantResolverProperties", "sourceType": "cn.com.handthing.starter.tenant.config.TenantResolverProperties"}, {"name": "handthing.auth.tenant.resolver.header", "type": "cn.com.handthing.starter.tenant.config.TenantResolverProperties$HeaderConfig", "sourceType": "cn.com.handthing.starter.tenant.config.TenantResolverProperties", "sourceMethod": "public cn.com.handthing.starter.tenant.config.TenantResolverProperties.HeaderConfig getHeader() "}, {"name": "handthing.auth.tenant.resolver.path", "type": "cn.com.handthing.starter.tenant.config.TenantResolverProperties$PathConfig", "sourceType": "cn.com.handthing.starter.tenant.config.TenantResolverProperties", "sourceMethod": "public cn.com.handthing.starter.tenant.config.TenantResolverProperties.PathConfig getPath() "}, {"name": "handthing.auth.tenant.resolver.subdomain", "type": "cn.com.handthing.starter.tenant.config.TenantResolverProperties$SubdomainConfig", "sourceType": "cn.com.handthing.starter.tenant.config.TenantResolverProperties", "sourceMethod": "public cn.com.handthing.starter.tenant.config.TenantResolverProperties.SubdomainConfig getSubdomain() "}], "properties": [{"name": "handthing.auth.saas.cache.cache-name", "type": "java.lang.String", "description": "缓存名称", "sourceType": "cn.com.handthing.starter.tenant.config.SaaSAuthProperties$CacheConfig"}, {"name": "handthing.auth.saas.cache.enable-stats", "type": "java.lang.Bo<PERSON>an", "description": "是否启用缓存统计", "sourceType": "cn.com.handthing.starter.tenant.config.SaaSAuthProperties$CacheConfig"}, {"name": "handthing.auth.saas.cache.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用缓存", "sourceType": "cn.com.handthing.starter.tenant.config.SaaSAuthProperties$CacheConfig"}, {"name": "handthing.auth.saas.cache.max-size", "type": "java.lang.Long", "description": "最大缓存条目数", "sourceType": "cn.com.handthing.starter.tenant.config.SaaSAuthProperties$CacheConfig"}, {"name": "handthing.auth.saas.cache.ttl", "type": "java.time.Duration", "description": "缓存TTL（生存时间）", "sourceType": "cn.com.handthing.starter.tenant.config.SaaSAuthProperties$CacheConfig"}, {"name": "handthing.auth.saas.default-tenant-id", "type": "java.lang.String", "description": "默认租户ID 当无法解析租户ID时使用的默认值", "sourceType": "cn.com.handthing.starter.tenant.config.SaaSAuthProperties"}, {"name": "handthing.auth.saas.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用SaaS多租户认证", "sourceType": "cn.com.handthing.starter.tenant.config.SaaSAuthProperties"}, {"name": "handthing.auth.saas.filter.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用租户解析过滤器", "sourceType": "cn.com.handthing.starter.tenant.config.SaaSAuthProperties$FilterConfig"}, {"name": "handthing.auth.saas.filter.exclude-paths", "type": "java.lang.String[]", "description": "排除路径列表 这些路径不会进行租户解析", "sourceType": "cn.com.handthing.starter.tenant.config.SaaSAuthProperties$FilterConfig"}, {"name": "handthing.auth.saas.filter.include-paths", "type": "java.lang.String[]", "description": "包含路径列表 如果指定，只有这些路径会进行租户解析", "sourceType": "cn.com.handthing.starter.tenant.config.SaaSAuthProperties$FilterConfig"}, {"name": "handthing.auth.saas.filter.log-resolution", "type": "java.lang.Bo<PERSON>an", "description": "是否记录租户解析日志", "sourceType": "cn.com.handthing.starter.tenant.config.SaaSAuthProperties$FilterConfig"}, {"name": "handthing.auth.saas.filter.order", "type": "java.lang.Integer", "description": "过滤器顺序", "sourceType": "cn.com.handthing.starter.tenant.config.SaaSAuthProperties$FilterConfig"}, {"name": "handthing.auth.saas.monitoring.enable-events", "type": "java.lang.Bo<PERSON>an", "description": "是否启用事件发布", "sourceType": "cn.com.handthing.starter.tenant.config.SaaSAuthProperties$MonitoringConfig"}, {"name": "handthing.auth.saas.monitoring.enable-health-check", "type": "java.lang.Bo<PERSON>an", "description": "是否启用健康检查", "sourceType": "cn.com.handthing.starter.tenant.config.SaaSAuthProperties$MonitoringConfig"}, {"name": "handthing.auth.saas.monitoring.enable-metrics", "type": "java.lang.Bo<PERSON>an", "description": "是否启用指标收集", "sourceType": "cn.com.handthing.starter.tenant.config.SaaSAuthProperties$MonitoringConfig"}, {"name": "handthing.auth.saas.monitoring.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用监控", "sourceType": "cn.com.handthing.starter.tenant.config.SaaSAuthProperties$MonitoringConfig"}, {"name": "handthing.auth.saas.monitoring.retention-period", "type": "java.time.Duration", "description": "监控数据保留时间", "sourceType": "cn.com.handthing.starter.tenant.config.SaaSAuthProperties$MonitoringConfig"}, {"name": "handthing.auth.saas.resolver", "type": "cn.com.handthing.starter.tenant.config.TenantResolverProperties", "description": "租户解析器配置", "sourceType": "cn.com.handthing.starter.tenant.config.SaaSAuthProperties"}, {"name": "handthing.auth.saas.tenant-required", "type": "java.lang.Bo<PERSON>an", "description": "是否要求必须有租户 如果为true，当无法解析租户ID时会返回错误", "sourceType": "cn.com.handthing.starter.tenant.config.SaaSAuthProperties"}, {"name": "handthing.auth.saas.validation.allow-empty", "type": "java.lang.Bo<PERSON>an", "description": "是否允许空租户ID", "sourceType": "cn.com.handthing.starter.tenant.config.SaaSAuthProperties$ValidationConfig"}, {"name": "handthing.auth.saas.validation.blacklist", "type": "java.lang.String[]", "description": "禁用的租户ID列表", "sourceType": "cn.com.handthing.starter.tenant.config.SaaSAuthProperties$ValidationConfig"}, {"name": "handthing.auth.saas.validation.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用租户ID验证", "sourceType": "cn.com.handthing.starter.tenant.config.SaaSAuthProperties$ValidationConfig"}, {"name": "handthing.auth.saas.validation.max-length", "type": "java.lang.Integer", "description": "租户ID最大长度", "sourceType": "cn.com.handthing.starter.tenant.config.SaaSAuthProperties$ValidationConfig"}, {"name": "handthing.auth.saas.validation.min-length", "type": "java.lang.Integer", "description": "租户ID最小长度", "sourceType": "cn.com.handthing.starter.tenant.config.SaaSAuthProperties$ValidationConfig"}, {"name": "handthing.auth.saas.validation.pattern", "type": "java.lang.String", "description": "租户ID正则表达式", "sourceType": "cn.com.handthing.starter.tenant.config.SaaSAuthProperties$ValidationConfig"}, {"name": "handthing.auth.tenant.resolver.enabled-strategies", "type": "java.util.List<java.lang.String>", "sourceType": "cn.com.handthing.starter.tenant.config.TenantResolverProperties"}, {"name": "handthing.auth.tenant.resolver.header.case-sensitive", "type": "java.lang.Bo<PERSON>an", "description": "是否区分大小写", "sourceType": "cn.com.handthing.starter.tenant.config.TenantResolverProperties$HeaderConfig"}, {"name": "handthing.auth.tenant.resolver.header.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用HTTP头解析", "sourceType": "cn.com.handthing.starter.tenant.config.TenantResolverProperties$HeaderConfig"}, {"name": "handthing.auth.tenant.resolver.header.fallback-names", "type": "java.util.List<java.lang.String>", "description": "备选的租户头名称列表", "sourceType": "cn.com.handthing.starter.tenant.config.TenantResolverProperties$HeaderConfig"}, {"name": "handthing.auth.tenant.resolver.header.name", "type": "java.lang.String", "description": "主要的租户头名称", "sourceType": "cn.com.handthing.starter.tenant.config.TenantResolverProperties$HeaderConfig"}, {"name": "handthing.auth.tenant.resolver.header.order", "type": "java.lang.Integer", "description": "解析器优先级", "sourceType": "cn.com.handthing.starter.tenant.config.TenantResolverProperties$HeaderConfig"}, {"name": "handthing.auth.tenant.resolver.multi-resolver", "type": "java.lang.Bo<PERSON>an", "description": "是否启用多解析器 如果为true，将按优先级尝试所有配置的解析器", "sourceType": "cn.com.handthing.starter.tenant.config.TenantResolverProperties"}, {"name": "handthing.auth.tenant.resolver.path.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用URL路径解析", "sourceType": "cn.com.handthing.starter.tenant.config.TenantResolverProperties$PathConfig"}, {"name": "handthing.auth.tenant.resolver.path.order", "type": "java.lang.Integer", "description": "解析器优先级", "sourceType": "cn.com.handthing.starter.tenant.config.TenantResolverProperties$PathConfig"}, {"name": "handthing.auth.tenant.resolver.path.pattern", "type": "java.lang.String", "description": "路径模式 使用{tenant}作为占位符，例如：/api/{tenant}/**", "sourceType": "cn.com.handthing.starter.tenant.config.TenantResolverProperties$PathConfig"}, {"name": "handthing.auth.tenant.resolver.path.prefix", "type": "java.lang.String", "description": "路径前缀", "sourceType": "cn.com.handthing.starter.tenant.config.TenantResolverProperties$PathConfig"}, {"name": "handthing.auth.tenant.resolver.path.remove-prefix", "type": "java.lang.Bo<PERSON>an", "description": "是否移除路径前缀 如果为true，会尝试从请求路径中移除租户前缀", "sourceType": "cn.com.handthing.starter.tenant.config.TenantResolverProperties$PathConfig"}, {"name": "handthing.auth.tenant.resolver.strategy", "type": "java.lang.String", "description": "租户解析策略 可选值：subdomain, header, path, custom", "sourceType": "cn.com.handthing.starter.tenant.config.TenantResolverProperties"}, {"name": "handthing.auth.tenant.resolver.subdomain.base-domain", "type": "java.lang.String", "description": "基础域名 如果指定，只有匹配此域名的请求才会进行租户解析", "sourceType": "cn.com.handthing.starter.tenant.config.TenantResolverProperties$SubdomainConfig"}, {"name": "handthing.auth.tenant.resolver.subdomain.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用子域名解析", "sourceType": "cn.com.handthing.starter.tenant.config.TenantResolverProperties$SubdomainConfig"}, {"name": "handthing.auth.tenant.resolver.subdomain.ignore-www", "type": "java.lang.Bo<PERSON>an", "description": "是否忽略www前缀", "sourceType": "cn.com.handthing.starter.tenant.config.TenantResolverProperties$SubdomainConfig"}, {"name": "handthing.auth.tenant.resolver.subdomain.order", "type": "java.lang.Integer", "description": "解析器优先级", "sourceType": "cn.com.handthing.starter.tenant.config.TenantResolverProperties$SubdomainConfig"}, {"name": "handthing.auth.tenant.resolver.subdomain.pattern", "type": "java.lang.String", "description": "子域名模式 使用{tenant}作为占位符，例如：{tenant}.myapp.com", "sourceType": "cn.com.handthing.starter.tenant.config.TenantResolverProperties$SubdomainConfig"}], "hints": [], "ignored": {"properties": []}}