package cn.com.handthing.starter.connector.auth;

import cn.com.handthing.starter.connector.PlatformType;
import cn.com.handthing.starter.connector.exception.AuthException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 默认认证服务实现
 * <p>
 * 管理多个AuthProvider实例，提供统一的认证入口方法。
 * 支持按平台类型路由到对应的AuthProvider。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Service
public class DefaultAuthService implements AuthService {

    private final Map<PlatformType, AuthProvider> authProviders = new ConcurrentHashMap<>();

    /**
     * 注册认证提供者
     *
     * @param authProvider 认证提供者
     */
    public void registerAuthProvider(AuthProvider authProvider) {
        authProviders.put(authProvider.getPlatformType(), authProvider);
        log.info("Registered auth provider for platform: {}", authProvider.getPlatformType());
    }

    @Override
    public String getAuthorizationUrl(PlatformType platform, String state, String redirectUri) throws AuthException {
        AuthProvider provider = getAuthProvider(platform);
        
        try {
            String authUrl = provider.getAuthorizationUrl(state, redirectUri);
            log.debug("Generated authorization URL for platform: {}, state: {}", platform, state);
            return authUrl;
        } catch (Exception e) {
            log.error("Failed to generate authorization URL for platform: {}", platform, e);
            throw new AuthException("Failed to generate authorization URL", 
                    AuthException.CONFIGURATION_ERROR, platform, e);
        }
    }

    @Override
    public UnifiedAccessToken exchangeToken(PlatformType platform, String code, String state, String redirectUri) throws AuthException {
        AuthProvider provider = getAuthProvider(platform);
        
        try {
            UnifiedAccessToken token = provider.exchangeToken(code, state, redirectUri);
            log.info("Successfully exchanged token for platform: {}, state: {}", platform, state);
            return token;
        } catch (Exception e) {
            log.error("Failed to exchange token for platform: {}, code: {}", platform, code, e);
            if (e instanceof AuthException) {
                throw e;
            }
            throw new AuthException("Failed to exchange token", 
                    AuthException.INVALID_CODE, platform, e);
        }
    }

    @Override
    public UnifiedAccessToken refreshToken(PlatformType platform, String refreshToken) throws AuthException {
        AuthProvider provider = getAuthProvider(platform);
        
        if (!provider.supportsRefreshToken()) {
            throw new AuthException("Platform does not support token refresh: " + platform, 
                    AuthException.REFRESH_TOKEN_EXPIRED, platform);
        }
        
        try {
            UnifiedAccessToken token = provider.refreshToken(refreshToken);
            log.info("Successfully refreshed token for platform: {}", platform);
            return token;
        } catch (Exception e) {
            log.error("Failed to refresh token for platform: {}", platform, e);
            if (e instanceof AuthException) {
                throw e;
            }
            throw new AuthException("Failed to refresh token", 
                    AuthException.REFRESH_TOKEN_EXPIRED, platform, e);
        }
    }

    @Override
    public UnifiedUserInfo getUserInfo(PlatformType platform, String accessToken) throws AuthException {
        AuthProvider provider = getAuthProvider(platform);
        
        try {
            UnifiedUserInfo userInfo = provider.getUserInfo(accessToken);
            log.debug("Successfully retrieved user info for platform: {}, userId: {}", 
                    platform, userInfo != null ? userInfo.getUserId() : "null");
            return userInfo;
        } catch (Exception e) {
            log.error("Failed to get user info for platform: {}", platform, e);
            if (e instanceof AuthException) {
                throw e;
            }
            throw new AuthException("Failed to get user info", 
                    AuthException.INVALID_TOKEN, platform, e);
        }
    }

    @Override
    public boolean validateToken(PlatformType platform, String accessToken) throws AuthException {
        AuthProvider provider = getAuthProvider(platform);
        
        try {
            boolean isValid = provider.validateToken(accessToken);
            log.debug("Token validation result for platform: {}, valid: {}", platform, isValid);
            return isValid;
        } catch (Exception e) {
            log.error("Failed to validate token for platform: {}", platform, e);
            if (e instanceof AuthException) {
                throw e;
            }
            throw new AuthException("Failed to validate token", 
                    AuthException.INVALID_TOKEN, platform, e);
        }
    }

    @Override
    public void revokeToken(PlatformType platform, String accessToken) throws AuthException {
        AuthProvider provider = getAuthProvider(platform);
        
        if (!provider.supportsTokenRevocation()) {
            throw new AuthException("Platform does not support token revocation: " + platform, 
                    AuthException.PLATFORM_NOT_SUPPORTED, platform);
        }
        
        try {
            provider.revokeToken(accessToken);
            log.info("Successfully revoked token for platform: {}", platform);
        } catch (Exception e) {
            log.error("Failed to revoke token for platform: {}", platform, e);
            if (e instanceof AuthException) {
                throw e;
            }
            throw new AuthException("Failed to revoke token", 
                    AuthException.INVALID_TOKEN, platform, e);
        }
    }

    @Override
    public boolean isPlatformSupported(PlatformType platform) {
        return authProviders.containsKey(platform);
    }

    @Override
    public List<PlatformType> getSupportedPlatforms() {
        return authProviders.keySet().stream()
                .sorted((p1, p2) -> p1.getDisplayName().compareTo(p2.getDisplayName()))
                .collect(Collectors.toList());
    }

    @Override
    public boolean supportsRefreshToken(PlatformType platform) throws AuthException {
        AuthProvider provider = getAuthProvider(platform);
        return provider.supportsRefreshToken();
    }

    @Override
    public boolean supportsTokenRevocation(PlatformType platform) throws AuthException {
        AuthProvider provider = getAuthProvider(platform);
        return provider.supportsTokenRevocation();
    }

    @Override
    public String getDefaultScope(PlatformType platform) throws AuthException {
        AuthProvider provider = getAuthProvider(platform);
        return provider.getDefaultScope();
    }

    /**
     * 获取认证提供者
     *
     * @param platform 平台类型
     * @return 认证提供者
     * @throws AuthException 如果平台不支持
     */
    private AuthProvider getAuthProvider(PlatformType platform) throws AuthException {
        AuthProvider provider = authProviders.get(platform);
        if (provider == null) {
            throw AuthException.platformNotSupported(platform);
        }
        return provider;
    }

    /**
     * 获取所有认证提供者
     *
     * @return 认证提供者映射
     */
    public Map<PlatformType, AuthProvider> getAllAuthProviders() {
        return Map.copyOf(authProviders);
    }

    /**
     * 获取认证提供者数量
     *
     * @return 认证提供者数量
     */
    public int getProviderCount() {
        return authProviders.size();
    }
}
