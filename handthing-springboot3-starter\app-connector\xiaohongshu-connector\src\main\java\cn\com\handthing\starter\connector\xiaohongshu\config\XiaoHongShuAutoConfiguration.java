package cn.com.handthing.starter.connector.xiaohongshu.config;

import cn.com.handthing.starter.connector.auth.AuthService;
import cn.com.handthing.starter.connector.auth.DefaultAuthService;
import cn.com.handthing.starter.connector.token.TokenManager;
import cn.com.handthing.starter.connector.xiaohongshu.XiaoHongShuAuthProvider;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;

import jakarta.annotation.PostConstruct;

/**
 * 小红书连接器自动配置
 * <p>
 * 自动配置小红书连接器相关的Bean，包括配置类、认证提供者、服务类等。
 * 当启用小红书连接器时，自动注册到认证服务和Token管理器中。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@RequiredArgsConstructor
@AutoConfiguration
@EnableConfigurationProperties(XiaoHongShuConfig.class)
@ConditionalOnProperty(prefix = "handthing.connector.xiaohongshu", name = "enabled", havingValue = "true")
@ConditionalOnClass(AuthService.class)
@ComponentScan(basePackages = "cn.com.handthing.starter.connector.xiaohongshu")
public class XiaoHongShuAutoConfiguration {

    private final AuthService authService;
    private final XiaoHongShuAuthProvider xiaoHongShuAuthProvider;
    private final TokenManager tokenManager;
    private final XiaoHongShuConfig xiaoHongShuConfig;

    /**
     * 注册小红书认证提供者到认证服务
     */
    @PostConstruct
    public void registerXiaoHongShuAuthProvider() {
        if (authService instanceof DefaultAuthService) {
            DefaultAuthService defaultAuthService = (DefaultAuthService) authService;
            defaultAuthService.registerAuthProvider(xiaoHongShuAuthProvider);
            tokenManager.registerAuthProvider(xiaoHongShuAuthProvider);
            
            log.info("Registered XiaoHongShu auth provider - AppKey: {}, AppType: {}, Brand: {}, Valid: {}", 
                    xiaoHongShuConfig.getAppKey(), 
                    xiaoHongShuConfig.getAppType(),
                    xiaoHongShuConfig.isBrandDeveloper(),
                    xiaoHongShuConfig.isValid());
        } else {
            log.warn("AuthService is not DefaultAuthService, cannot register XiaoHongShu auth provider");
        }
    }

    /**
     * 配置信息日志输出
     */
    @PostConstruct
    public void logConfiguration() {
        log.info("XiaoHongShu configuration: enabled=true, valid={}, appKey={}, appType={}, brandMode={}, sandboxMode={}, contentPublishEnabled={}, communityEnabled={}",
                xiaoHongShuConfig.isValid(),
                xiaoHongShuConfig.getAppKey(),
                xiaoHongShuConfig.getAppType(),
                xiaoHongShuConfig.isBrandDeveloper(),
                xiaoHongShuConfig.isSandboxMode(),
                xiaoHongShuConfig.isContentPublishAvailable(),
                xiaoHongShuConfig.isCommunityAvailable());
    }
}
