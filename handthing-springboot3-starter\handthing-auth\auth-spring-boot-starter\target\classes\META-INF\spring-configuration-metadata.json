{"groups": [{"name": "handthing.auth", "type": "cn.com.handthing.starter.auth.AuthProperties", "sourceType": "cn.com.handthing.starter.auth.AuthProperties"}, {"name": "handthing.auth.security", "type": "cn.com.handthing.starter.auth.AuthProperties$Security", "sourceType": "cn.com.handthing.starter.auth.AuthProperties", "sourceMethod": "public cn.com.handthing.starter.auth.AuthProperties.Security getSecurity() "}, {"name": "handthing.auth.web", "type": "cn.com.handthing.starter.auth.AuthProperties$Web", "sourceType": "cn.com.handthing.starter.auth.AuthProperties", "sourceMethod": "public cn.com.handthing.starter.auth.AuthProperties.Web getWeb() "}], "properties": [{"name": "handthing.auth.cache-enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用认证缓存", "sourceType": "cn.com.handthing.starter.auth.AuthProperties"}, {"name": "handthing.auth.cache-expiration", "type": "java.time.Duration", "description": "缓存过期时间", "sourceType": "cn.com.handthing.starter.auth.AuthProperties"}, {"name": "handthing.auth.default-grant-type", "type": "java.lang.String", "description": "默认授权类型", "sourceType": "cn.com.handthing.starter.auth.AuthProperties"}, {"name": "handthing.auth.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用认证框架", "sourceType": "cn.com.handthing.starter.auth.AuthProperties"}, {"name": "handthing.auth.events-enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用认证事件", "sourceType": "cn.com.handthing.starter.auth.AuthProperties"}, {"name": "handthing.auth.failure-lock-duration", "type": "java.time.Duration", "description": "认证失败锁定时间", "sourceType": "cn.com.handthing.starter.auth.AuthProperties"}, {"name": "handthing.auth.ip-blacklist", "type": "java.util.List<java.lang.String>", "description": "IP黑名单", "sourceType": "cn.com.handthing.starter.auth.AuthProperties"}, {"name": "handthing.auth.ip-blacklist-enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用IP黑名单", "sourceType": "cn.com.handthing.starter.auth.AuthProperties"}, {"name": "handthing.auth.ip-whitelist", "type": "java.util.List<java.lang.String>", "description": "IP白名单", "sourceType": "cn.com.handthing.starter.auth.AuthProperties"}, {"name": "handthing.auth.ip-whitelist-enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用IP白名单", "sourceType": "cn.com.handthing.starter.auth.AuthProperties"}, {"name": "handthing.auth.jwt", "type": "cn.com.handthing.starter.auth.core.JwtConfig", "description": "JWT配置", "sourceType": "cn.com.handthing.starter.auth.AuthProperties"}, {"name": "handthing.auth.logging-enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用认证日志", "sourceType": "cn.com.handthing.starter.auth.AuthProperties"}, {"name": "handthing.auth.max-failure-attempts", "type": "java.lang.Integer", "description": "最大认证失败次数", "sourceType": "cn.com.handthing.starter.auth.AuthProperties"}, {"name": "handthing.auth.metrics-enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用认证指标", "sourceType": "cn.com.handthing.starter.auth.AuthProperties"}, {"name": "handthing.auth.multi-provider-enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用多提供者模式", "sourceType": "cn.com.handthing.starter.auth.AuthProperties"}, {"name": "handthing.auth.provider-priority", "type": "java.util.List<java.lang.String>", "description": "提供者优先级列表", "sourceType": "cn.com.handthing.starter.auth.AuthProperties"}, {"name": "handthing.auth.security.allow-credentials", "type": "java.lang.Bo<PERSON>an", "description": "是否允许凭证", "sourceType": "cn.com.handthing.starter.auth.AuthProperties$Security"}, {"name": "handthing.auth.security.allowed-headers", "type": "java.util.List<java.lang.String>", "description": "允许的头部", "sourceType": "cn.com.handthing.starter.auth.AuthProperties$Security"}, {"name": "handthing.auth.security.allowed-methods", "type": "java.util.List<java.lang.String>", "description": "允许的方法", "sourceType": "cn.com.handthing.starter.auth.AuthProperties$Security"}, {"name": "handthing.auth.security.allowed-origins", "type": "java.util.List<java.lang.String>", "description": "允许的源", "sourceType": "cn.com.handthing.starter.auth.AuthProperties$Security"}, {"name": "handthing.auth.security.content-security-policy", "type": "java.lang.String", "description": "内容安全策略", "sourceType": "cn.com.handthing.starter.auth.AuthProperties$Security"}, {"name": "handthing.auth.security.cors-enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用CORS", "sourceType": "cn.com.handthing.starter.auth.AuthProperties$Security"}, {"name": "handthing.auth.security.csrf-enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用CSRF保护", "sourceType": "cn.com.handthing.starter.auth.AuthProperties$Security"}, {"name": "handthing.auth.security.https-redirect-enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用HTTPS重定向", "sourceType": "cn.com.handthing.starter.auth.AuthProperties$Security"}, {"name": "handthing.auth.security.max-age", "type": "java.time.Duration", "description": "预检请求缓存时间", "sourceType": "cn.com.handthing.starter.auth.AuthProperties$Security"}, {"name": "handthing.auth.security.security-headers-enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用安全头部", "sourceType": "cn.com.handthing.starter.auth.AuthProperties$Security"}, {"name": "handthing.auth.web.auth-path", "type": "java.lang.String", "description": "认证端点路径", "sourceType": "cn.com.handthing.starter.auth.AuthProperties$Web"}, {"name": "handthing.auth.web.endpoints-enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用认证端点", "sourceType": "cn.com.handthing.starter.auth.AuthProperties$Web"}, {"name": "handthing.auth.web.exclude-paths", "type": "java.util.List<java.lang.String>", "description": "排除路径列表", "sourceType": "cn.com.handthing.starter.auth.AuthProperties$Web"}, {"name": "handthing.auth.web.filter-enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用认证过滤器", "sourceType": "cn.com.handthing.starter.auth.AuthProperties$Web"}, {"name": "handthing.auth.web.filter-order", "type": "java.lang.Integer", "description": "过滤器顺序", "sourceType": "cn.com.handthing.starter.auth.AuthProperties$Web"}, {"name": "handthing.auth.web.login-path", "type": "java.lang.String", "description": "登录端点路径", "sourceType": "cn.com.handthing.starter.auth.AuthProperties$Web"}, {"name": "handthing.auth.web.logout-path", "type": "java.lang.String", "description": "登出端点路径", "sourceType": "cn.com.handthing.starter.auth.AuthProperties$Web"}, {"name": "handthing.auth.web.refresh-path", "type": "java.lang.String", "description": "刷新令牌端点路径", "sourceType": "cn.com.handthing.starter.auth.AuthProperties$Web"}, {"name": "handthing.auth.web.static-paths", "type": "java.util.List<java.lang.String>", "description": "静态资源路径", "sourceType": "cn.com.handthing.starter.auth.AuthProperties$Web"}, {"name": "handthing.auth.web.swagger-enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用Swagger文档", "sourceType": "cn.com.handthing.starter.auth.AuthProperties$Web"}, {"name": "handthing.auth.web.user-info-path", "type": "java.lang.String", "description": "用户信息端点路径", "sourceType": "cn.com.handthing.starter.auth.AuthProperties$Web"}], "hints": [], "ignored": {"properties": []}}