package cn.com.handthing.starter.cache.memory;

import cn.com.handthing.starter.cache.memory.config.MemoryCacheProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;

import java.time.Duration;
import java.util.Collection;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 内存缓存管理器
 * <p>
 * 基于ConcurrentHashMap实现的缓存管理器，支持TTL过期和定时清理
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
public class MemoryCacheManager implements CacheManager {

    private final MemoryCacheProperties properties;
    private final ConcurrentMap<String, Cache> cacheMap = new ConcurrentHashMap<>();
    private final ScheduledExecutorService cleanupExecutor;

    /**
     * 构造函数
     *
     * @param properties 内存缓存配置属性
     */
    public MemoryCacheManager(MemoryCacheProperties properties) {
        this.properties = properties;
        this.cleanupExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread thread = new Thread(r, "memory-cache-cleanup");
            thread.setDaemon(true);
            return thread;
        });

        // 启动定时清理任务
        startCleanupTask();
        log.info("MemoryCacheManager initialized with cleanup interval: {}", properties.getCleanupInterval());
    }

    @Override
    public Cache getCache(String name) {
        return cacheMap.computeIfAbsent(name, this::createCache);
    }

    @Override
    public Collection<String> getCacheNames() {
        return cacheMap.keySet();
    }

    /**
     * 创建缓存实例
     *
     * @param name 缓存名称
     * @return 缓存实例
     */
    private Cache createCache(String name) {
        MemoryCacheProperties.MemoryCacheSpec spec = properties.getCaches().get(name);
        
        long maxSize = spec != null ? spec.getEffectiveMaxSize(properties.getDefaultMaxSize()) : properties.getDefaultMaxSize();
        Duration ttl = spec != null ? spec.getEffectiveTtl(properties.getDefaultTtl()) : properties.getDefaultTtl();
        boolean enableStats = spec != null ? spec.getEffectiveEnableStats(properties.isEnableStats()) : properties.isEnableStats();

        MemoryCache cache = new MemoryCache(name, maxSize, ttl, enableStats);
        log.info("Created memory cache: {} with maxSize: {}, ttl: {}, enableStats: {}", 
                name, maxSize, ttl, enableStats);
        return cache;
    }

    /**
     * 启动定时清理任务
     */
    private void startCleanupTask() {
        long intervalSeconds = properties.getCleanupInterval().getSeconds();
        cleanupExecutor.scheduleWithFixedDelay(this::cleanupExpiredEntries, 
                intervalSeconds, intervalSeconds, TimeUnit.SECONDS);
    }

    /**
     * 清理所有缓存中的过期条目
     */
    private void cleanupExpiredEntries() {
        try {
            for (Cache cache : cacheMap.values()) {
                if (cache instanceof MemoryCache) {
                    ((MemoryCache) cache).cleanupExpired();
                }
            }
        } catch (Exception e) {
            log.error("Error during cache cleanup", e);
        }
    }

    /**
     * 关闭缓存管理器
     */
    public void shutdown() {
        if (cleanupExecutor != null && !cleanupExecutor.isShutdown()) {
            cleanupExecutor.shutdown();
            try {
                if (!cleanupExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    cleanupExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                cleanupExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        log.info("MemoryCacheManager shutdown completed");
    }
}