<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>cn.com.handthing.springboot3.starter</groupId>
        <artifactId>starter-parent</artifactId>
        <version>${revision}</version>
        <relativePath>../starter-parent/pom.xml</relativePath>
    </parent>

    <artifactId>handthing-auth</artifactId>
    <packaging>pom</packaging>
    <name>HandThing :: Auth :: Parent</name>
    <description>HandThing认证授权体系父模块</description>

    <modules>
        <module>auth-core</module>
        <module>auth-spring-boot-starter</module>
        <module>password-provider-starter</module>
        <module>sms-provider-starter</module>
        <module>third-party-provider-starter</module>
        <!-- Tenant Auth 多租户认证系列 -->
        <module>tenant-auth-starter</module>
    </modules>

    <properties>
        <jwt.version>0.11.5</jwt.version>
        <spring-security.version>6.1.5</spring-security.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- Auth Core -->
            <dependency>
                <groupId>cn.com.handthing.springboot3.starter</groupId>
                <artifactId>auth-core</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- Auth Spring Boot Starter -->
            <dependency>
                <groupId>cn.com.handthing.springboot3.starter</groupId>
                <artifactId>auth-spring-boot-starter</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- Password Provider Starter -->
            <dependency>
                <groupId>cn.com.handthing.springboot3.starter</groupId>
                <artifactId>password-provider-starter</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- SMS Provider Starter -->
            <dependency>
                <groupId>cn.com.handthing.springboot3.starter</groupId>
                <artifactId>sms-provider-starter</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- Third Party Provider Starter -->
            <dependency>
                <groupId>cn.com.handthing.springboot3.starter</groupId>
                <artifactId>third-party-provider-starter</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- JWT -->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-api</artifactId>
                <version>${jwt.version}</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-impl</artifactId>
                <version>${jwt.version}</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-jackson</artifactId>
                <version>${jwt.version}</version>
            </dependency>

            <!-- Spring Security -->
            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-core</artifactId>
                <version>${spring-security.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-crypto</artifactId>
                <version>${spring-security.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

</project>
