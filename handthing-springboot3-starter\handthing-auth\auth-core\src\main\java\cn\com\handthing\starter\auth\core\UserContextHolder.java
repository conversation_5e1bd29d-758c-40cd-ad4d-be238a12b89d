package cn.com.handthing.starter.auth.core;

/**
 * 用户上下文持有者
 * <p>
 * 使用ThreadLocal存储当前线程的用户上下文信息。
 * 提供静态方法来设置、获取和清除用户上下文。
 * 支持继承性ThreadLocal，确保子线程也能访问到用户上下文。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
public class UserContextHolder {

    /**
     * 用户上下文ThreadLocal
     */
    private static final InheritableThreadLocal<UserContext> CONTEXT_HOLDER = new InheritableThreadLocal<>();

    /**
     * 私有构造函数，防止实例化
     */
    private UserContextHolder() {
    }

    /**
     * 设置用户上下文
     *
     * @param context 用户上下文
     */
    public static void setContext(UserContext context) {
        CONTEXT_HOLDER.set(context);
    }

    /**
     * 获取用户上下文
     *
     * @return 用户上下文，如果未设置返回null
     */
    public static UserContext getContext() {
        return CONTEXT_HOLDER.get();
    }

    /**
     * 清除用户上下文
     */
    public static void clearContext() {
        CONTEXT_HOLDER.remove();
    }

    /**
     * 获取当前用户信息
     *
     * @return 用户信息，如果未设置返回null
     */
    public static UserInfo getCurrentUser() {
        UserContext context = getContext();
        return context != null ? context.getUserInfo() : null;
    }

    /**
     * 获取当前用户ID
     *
     * @return 用户ID，如果未设置返回null
     */
    public static String getCurrentUserId() {
        UserContext context = getContext();
        return context != null ? context.getUserId() : null;
    }

    /**
     * 获取当前用户名
     *
     * @return 用户名，如果未设置返回null
     */
    public static String getCurrentUsername() {
        UserContext context = getContext();
        return context != null ? context.getUsername() : null;
    }

    /**
     * 获取当前访问令牌
     *
     * @return 访问令牌，如果未设置返回null
     */
    public static String getCurrentAccessToken() {
        UserContext context = getContext();
        return context != null ? context.getAccessToken() : null;
    }

    /**
     * 判断当前是否已认证
     *
     * @return 如果已认证返回true，否则返回false
     */
    public static boolean isAuthenticated() {
        UserContext context = getContext();
        return context != null && context.isAuthenticated();
    }

    /**
     * 判断当前用户是否有指定角色
     *
     * @param role 角色名称
     * @return 如果有指定角色返回true，否则返回false
     */
    public static boolean hasRole(String role) {
        UserContext context = getContext();
        return context != null && context.hasRole(role);
    }

    /**
     * 判断当前用户是否有指定权限
     *
     * @param permission 权限名称
     * @return 如果有指定权限返回true，否则返回false
     */
    public static boolean hasPermission(String permission) {
        UserContext context = getContext();
        return context != null && context.hasPermission(permission);
    }

    /**
     * 获取当前用户显示名称
     *
     * @return 显示名称，如果未设置返回null
     */
    public static String getCurrentDisplayName() {
        UserContext context = getContext();
        return context != null ? context.getDisplayName() : null;
    }

    /**
     * 执行带用户上下文的操作
     *
     * @param context  用户上下文
     * @param runnable 要执行的操作
     */
    public static void runWithContext(UserContext context, Runnable runnable) {
        UserContext originalContext = getContext();
        try {
            setContext(context);
            runnable.run();
        } finally {
            if (originalContext != null) {
                setContext(originalContext);
            } else {
                clearContext();
            }
        }
    }

    /**
     * 执行带用户上下文的操作（有返回值）
     *
     * @param context  用户上下文
     * @param supplier 要执行的操作
     * @param <T>      返回值类型
     * @return 操作结果
     */
    public static <T> T runWithContext(UserContext context, java.util.function.Supplier<T> supplier) {
        UserContext originalContext = getContext();
        try {
            setContext(context);
            return supplier.get();
        } finally {
            if (originalContext != null) {
                setContext(originalContext);
            } else {
                clearContext();
            }
        }
    }
}
