package cn.com.handthing.starter.auth.thirdparty;

import cn.com.handthing.starter.auth.core.UserInfo;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * 钉钉用户服务接口
 * <p>
 * 定义钉钉认证相关的用户操作接口，包括用户查找、创建、绑定等。
 * 业务系统需要实现此接口来提供具体的用户数据访问逻辑。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
public interface DingtalkUserService {

    /**
     * 根据钉钉用户ID查找用户
     *
     * @param dingtalkUserId 钉钉用户ID
     * @param appKey         应用Key
     * @return 用户信息，如果未找到返回空
     */
    Optional<UserInfo> findByDingtalkUserId(String dingtalkUserId, String appKey);

    /**
     * 根据用户ID查找用户
     *
     * @param userId 用户ID
     * @return 用户信息，如果未找到返回空
     */
    Optional<UserInfo> findById(String userId);

    /**
     * 根据手机号查找用户
     *
     * @param mobile 手机号
     * @return 用户信息，如果未找到返回空
     */
    Optional<UserInfo> findByMobile(String mobile);

    /**
     * 根据邮箱查找用户
     *
     * @param email 邮箱
     * @return 用户信息，如果未找到返回空
     */
    Optional<UserInfo> findByEmail(String email);

    /**
     * 创建新用户（基于钉钉信息）
     *
     * @param dingtalkUserDetail 钉钉用户详细信息
     * @param appKey             应用Key
     * @return 创建的用户信息
     */
    UserInfo createUser(DingtalkApiService.DingtalkUserDetailResult dingtalkUserDetail, String appKey);

    /**
     * 绑定钉钉用户
     *
     * @param userId         用户ID
     * @param dingtalkUserId 钉钉用户ID
     * @param appKey         应用Key
     * @return 如果绑定成功返回true，否则返回false
     */
    boolean bindDingtalkUser(String userId, String dingtalkUserId, String appKey);

    /**
     * 解绑钉钉用户
     *
     * @param userId 用户ID
     * @param appKey 应用Key
     * @return 如果解绑成功返回true，否则返回false
     */
    boolean unbindDingtalkUser(String userId, String appKey);

    /**
     * 检查用户状态是否有效
     *
     * @param userInfo 用户信息
     * @return 如果用户状态有效返回true，否则返回false
     */
    boolean isUserValid(UserInfo userInfo);

    /**
     * 检查用户是否被锁定
     *
     * @param userInfo 用户信息
     * @return 如果用户被锁定返回true，否则返回false
     */
    boolean isUserLocked(UserInfo userInfo);

    /**
     * 检查用户是否被禁用
     *
     * @param userInfo 用户信息
     * @return 如果用户被禁用返回true，否则返回false
     */
    boolean isUserDisabled(UserInfo userInfo);

    /**
     * 检查账户是否过期
     *
     * @param userInfo 用户信息
     * @return 如果账户过期返回true，否则返回false
     */
    boolean isAccountExpired(UserInfo userInfo);

    /**
     * 更新用户最后登录信息
     *
     * @param userId    用户ID
     * @param loginTime 登录时间
     * @param ipAddress IP地址
     * @param userAgent 用户代理
     */
    void updateLastLoginInfo(String userId, LocalDateTime loginTime, String ipAddress, String userAgent);

    /**
     * 记录登录失败
     *
     * @param dingtalkUserId 钉钉用户ID
     * @param appKey         应用Key
     * @param ipAddress      IP地址
     * @param reason         失败原因
     */
    void recordLoginFailure(String dingtalkUserId, String appKey, String ipAddress, String reason);

    /**
     * 清除登录失败记录
     *
     * @param dingtalkUserId 钉钉用户ID
     * @param appKey         应用Key
     */
    void clearLoginFailures(String dingtalkUserId, String appKey);

    /**
     * 获取登录失败次数
     *
     * @param dingtalkUserId 钉钉用户ID
     * @param appKey         应用Key
     * @return 登录失败次数
     */
    int getLoginFailureCount(String dingtalkUserId, String appKey);

    /**
     * 锁定用户
     *
     * @param userId    用户ID
     * @param reason    锁定原因
     * @param lockUntil 锁定到期时间
     */
    void lockUser(String userId, String reason, LocalDateTime lockUntil);

    /**
     * 解锁用户
     *
     * @param userId 用户ID
     */
    void unlockUser(String userId);

    /**
     * 检查是否为首次登录
     *
     * @param userInfo 用户信息
     * @return 如果是首次登录返回true，否则返回false
     */
    default boolean isFirstLogin(UserInfo userInfo) {
        return userInfo.getLastLoginTime() == null;
    }

    /**
     * 检查钉钉用户是否已绑定
     *
     * @param dingtalkUserId 钉钉用户ID
     * @param appKey         应用Key
     * @return 如果已绑定返回true，否则返回false
     */
    default boolean isDingtalkUserBound(String dingtalkUserId, String appKey) {
        return findByDingtalkUserId(dingtalkUserId, appKey).isPresent();
    }

    /**
     * 生成用户ID
     *
     * @param dingtalkUserId 钉钉用户ID
     * @param appKey         应用Key
     * @return 用户ID
     */
    default String generateUserId(String dingtalkUserId, String appKey) {
        return "dingtalk_" + appKey + "_" + dingtalkUserId + "_" + System.currentTimeMillis();
    }

    /**
     * 生成默认用户名
     *
     * @param dingtalkUserId 钉钉用户ID
     * @param appKey         应用Key
     * @return 默认用户名
     */
    default String generateDefaultUsername(String dingtalkUserId, String appKey) {
        return "dingtalk_" + dingtalkUserId;
    }

    /**
     * 获取默认角色
     *
     * @return 默认角色列表
     */
    default java.util.List<String> getDefaultRoles() {
        return java.util.List.of("USER", "DINGTALK_USER");
    }

    /**
     * 获取默认权限
     *
     * @return 默认权限列表
     */
    default java.util.List<String> getDefaultPermissions() {
        return java.util.List.of("READ", "DINGTALK_ACCESS");
    }

    /**
     * 验证钉钉用户ID格式
     *
     * @param dingtalkUserId 钉钉用户ID
     * @return 如果格式正确返回true，否则返回false
     */
    default boolean isValidDingtalkUserId(String dingtalkUserId) {
        return dingtalkUserId != null && dingtalkUserId.trim().length() > 0 && dingtalkUserId.length() <= 64;
    }

    /**
     * 验证应用Key格式
     *
     * @param appKey 应用Key
     * @return 如果格式正确返回true，否则返回false
     */
    default boolean isValidAppKey(String appKey) {
        return appKey != null && appKey.trim().length() > 0 && appKey.length() <= 64;
    }

    /**
     * 构建钉钉用户唯一标识
     *
     * @param dingtalkUserId 钉钉用户ID
     * @param appKey         应用Key
     * @return 唯一标识
     */
    default String buildDingtalkUserKey(String dingtalkUserId, String appKey) {
        return appKey + ":" + dingtalkUserId;
    }
}
