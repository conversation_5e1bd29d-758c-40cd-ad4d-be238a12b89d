# HandThing App Connector 配置参考

## 概述

本文档详细描述了HandThing App Connector Framework的所有配置选项，包括全局配置和各平台特定配置。

## 全局配置

### 基础配置
```yaml
handthing:
  connector:
    # 是否启用连接器框架
    enabled: true
    
    # 缓存配置
    cache:
      enabled: true
      type: "memory"  # memory/redis
      ttl: 3600       # 缓存过期时间(秒)
      max-size: 1000  # 最大缓存条目数
    
    # Token管理配置
    token:
      auto-refresh: true        # 是否自动刷新Token
      refresh-threshold: 300    # 刷新阈值(秒)
      max-retry: 3             # 最大重试次数
    
    # HTTP配置
    http:
      connect-timeout: 5000     # 连接超时(毫秒)
      read-timeout: 10000       # 读取超时(毫秒)
      max-connections: 100      # 最大连接数
      max-connections-per-route: 20  # 每个路由最大连接数
```

## 平台配置

### 企业微信 (WeCom)
```yaml
handthing:
  connector:
    wecom:
      enabled: true
      corp-id: "your-corp-id"                    # 企业ID (必填)
      corp-secret: "your-corp-secret"            # 企业应用密钥 (必填)
      agent-id: "your-agent-id"                  # 应用ID (必填)
      third-party-mode: false                    # 是否为第三方应用模式
      webhook-enabled: false                     # 是否启用Webhook回调
      webhook-token: "your-webhook-token"        # Webhook验证Token
      webhook-encoding-aes-key: "your-key"       # Webhook消息加密密钥
      redirect-uri: "http://your-domain.com/callback/wecom"  # OAuth回调地址
      api-base-url: "https://qyapi.weixin.qq.com"  # API基础URL
```

### 钉钉 (DingTalk)
```yaml
handthing:
  connector:
    dingtalk:
      enabled: true
      app-key: "your-app-key"                    # 应用AppKey (必填)
      app-secret: "your-app-secret"              # 应用AppSecret (必填)
      corp-id: "your-corp-id"                    # 企业CorpId (必填)
      third-party-mode: false                    # 是否为第三方应用模式
      robot-enabled: false                       # 是否启用机器人功能
      robot-webhook: "your-robot-webhook-url"    # 机器人Webhook地址
      robot-secret: "your-robot-secret"          # 机器人密钥
      redirect-uri: "http://your-domain.com/callback/dingtalk"  # OAuth回调地址
      api-base-url: "https://oapi.dingtalk.com"  # API基础URL
```

### 抖音 (Douyin)
```yaml
handthing:
  connector:
    douyin:
      enabled: true
      client-key: "your-client-key"              # 应用ClientKey (必填)
      client-secret: "your-client-secret"        # 应用ClientSecret (必填)
      app-type: "web"                           # 应用类型 (web/mobile/server)
      enterprise-mode: false                     # 是否为企业模式
      sandbox-mode: false                        # 是否使用沙箱环境
      video-upload-enabled: true                 # 是否启用视频上传功能
      live-enabled: false                        # 是否启用直播功能
      ecommerce-enabled: false                   # 是否启用电商功能
      scope: "user_info,video.upload,live"       # 授权范围
      redirect-uri: "http://your-domain.com/callback/douyin"  # OAuth回调地址
      api-base-url: "https://open.douyin.com"    # API基础URL
      sandbox-api-base-url: "https://open-sandbox.douyin.com"  # 沙箱API基础URL
```

### 支付宝 (Alipay)
```yaml
handthing:
  connector:
    alipay:
      enabled: true
      app-id: "your-app-id"                      # 应用ID (必填)
      private-key: "your-private-key"            # 应用私钥 (必填)
      alipay-public-key: "alipay-public-key"     # 支付宝公钥 (必填)
      sandbox: false                             # 是否使用沙箱环境
      cert-mode: false                           # 是否使用证书模式
      app-cert-path: "classpath:alipay/appCertPublicKey.crt"      # 应用公钥证书路径
      alipay-cert-path: "classpath:alipay/alipayCertPublicKey_RSA2.crt"  # 支付宝公钥证书路径
      alipay-root-cert-path: "classpath:alipay/alipayRootCert.crt"  # 支付宝根证书路径
      redirect-uri: "http://your-domain.com/callback/alipay"  # OAuth回调地址
      api-base-url: "https://openapi.alipay.com"  # API基础URL
      sandbox-api-base-url: "https://openapi.alipaydev.com"  # 沙箱API基础URL
```

### 微信 (WeChat)
```yaml
handthing:
  connector:
    wechat:
      enabled: true
      app-id: "your-app-id"                      # 微信应用ID (必填)
      app-secret: "your-app-secret"              # 微信应用密钥 (必填)
      account-type: "service"                    # 账号类型 (service/subscription/mini)
      mini-program-mode: false                   # 是否为小程序模式
      sandbox-mode: false                        # 是否使用沙箱环境
      encrypt-message: false                     # 是否启用消息加密
      encoding-aes-key: "your-encoding-aes-key"  # 消息加密密钥
      token: "your-token"                        # 接口配置Token
      redirect-uri: "http://your-domain.com/callback/wechat"  # OAuth回调地址
      api-base-url: "https://api.weixin.qq.com"  # API基础URL
```

### 飞书 (Feishu)
```yaml
handthing:
  connector:
    feishu:
      enabled: true
      app-id: "your-app-id"                      # 飞书应用ID (必填)
      app-secret: "your-app-secret"              # 飞书应用密钥 (必填)
      app-type: "internal"                       # 应用类型 (internal/third_party)
      third-party-mode: false                    # 是否为第三方应用模式
      bot-enabled: false                         # 是否启用机器人功能
      bot-secret: "your-bot-secret"              # 机器人密钥
      lark-mode: false                           # 是否为海外版飞书模式
      encrypt-message: false                     # 是否启用消息加密
      encrypt-key: "your-encrypt-key"            # 消息加密密钥
      verification-token: "your-verification-token"  # 消息验证Token
      redirect-uri: "http://your-domain.com/callback/feishu"  # OAuth回调地址
      api-base-url: "https://open.feishu.cn"     # API基础URL
      lark-api-base-url: "https://open.larksuite.com"  # Lark API基础URL
```

### 今日头条 (Toutiao)
```yaml
handthing:
  connector:
    toutiao:
      enabled: true
      client-key: "your-client-key"              # 应用ClientKey (必填)
      client-secret: "your-client-secret"        # 应用ClientSecret (必填)
      app-type: "web"                           # 应用类型 (web/mobile/server)
      enterprise-mode: false                     # 是否为企业应用模式
      sandbox-mode: false                        # 是否使用沙箱环境
      content-publish-enabled: true              # 是否启用内容发布功能
      analytics-enabled: true                    # 是否启用数据分析功能
      user-management-enabled: true              # 是否启用用户管理功能
      scope: "user_info,content.write,data.read"  # 授权范围
      redirect-uri: "http://your-domain.com/callback/toutiao"  # OAuth回调地址
      api-base-url: "https://open.toutiao.com"   # API基础URL
      sandbox-api-base-url: "https://open-sandbox.toutiao.com"  # 沙箱API基础URL
```

### 小红书 (XiaoHongShu)
```yaml
handthing:
  connector:
    xiaohongshu:
      enabled: true
      app-key: "your-app-key"                    # 应用AppKey (必填)
      app-secret: "your-app-secret"              # 应用AppSecret (必填)
      app-type: "personal"                       # 应用类型 (personal/enterprise/brand)
      brand-mode: false                          # 是否为品牌方模式
      sandbox-mode: false                        # 是否使用沙箱环境
      content-publish-enabled: true              # 是否启用内容发布功能
      community-enabled: true                    # 是否启用社区互动功能
      analytics-enabled: true                    # 是否启用数据分析功能
      ecommerce-enabled: false                   # 是否启用电商功能
      scope: "user_info,content.write,community.read,analytics.read"  # 授权范围
      redirect-uri: "http://your-domain.com/callback/xiaohongshu"  # OAuth回调地址
      api-base-url: "https://open.xiaohongshu.com"  # API基础URL
      sandbox-api-base-url: "https://open-sandbox.xiaohongshu.com"  # 沙箱API基础URL
```

### Bilibili
```yaml
handthing:
  connector:
    bilibili:
      enabled: true
      app-key: "your-app-key"                    # 应用AppKey (必填)
      app-secret: "your-app-secret"              # 应用AppSecret (必填)
      app-type: "personal"                       # 应用类型 (personal/enterprise/mcn)
      mcn-mode: false                            # 是否为MCN机构模式
      test-mode: false                           # 是否使用测试环境
      video-upload-enabled: true                 # 是否启用视频上传功能
      live-enabled: false                        # 是否启用直播功能
      analytics-enabled: true                    # 是否启用数据分析功能
      community-enabled: true                    # 是否启用社区互动功能
      scope: "user_info,video.upload,community.read,analytics.read"  # 授权范围
      redirect-uri: "http://your-domain.com/callback/bilibili"  # OAuth回调地址
      api-base-url: "https://api.bilibili.com"   # API基础URL
      test-api-base-url: "https://api-test.bilibili.com"  # 测试API基础URL
```

## 环境变量配置

支持通过环境变量覆盖配置：

```bash
# 企业微信配置
HANDTHING_CONNECTOR_WECOM_ENABLED=true
HANDTHING_CONNECTOR_WECOM_CORP_ID=your-corp-id
HANDTHING_CONNECTOR_WECOM_CORP_SECRET=your-corp-secret

# 微信配置
HANDTHING_CONNECTOR_WECHAT_ENABLED=true
HANDTHING_CONNECTOR_WECHAT_APP_ID=your-app-id
HANDTHING_CONNECTOR_WECHAT_APP_SECRET=your-app-secret

# 更多平台配置...
```

## 配置验证

框架提供配置验证功能：

```java
@Component
public class ConfigValidator {
    
    @Autowired
    private List<PlatformConfig> configs;
    
    @PostConstruct
    public void validateConfigs() {
        configs.forEach(config -> {
            if (!config.isValid()) {
                throw new ConfigException("Invalid config for " + config.getClass().getSimpleName());
            }
        });
    }
}
```

## 最佳实践

### 1. 安全配置
- 使用环境变量存储敏感信息
- 启用配置加密
- 定期轮换密钥

### 2. 性能配置
- 合理设置连接池大小
- 启用缓存机制
- 调整超时时间

### 3. 监控配置
- 启用健康检查
- 配置监控指标
- 设置告警阈值

### 4. 多环境配置
- 使用Profile区分环境
- 配置环境特定参数
- 实现配置热更新
