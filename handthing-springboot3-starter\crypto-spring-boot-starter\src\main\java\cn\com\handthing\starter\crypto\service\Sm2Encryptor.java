package cn.com.handthing.starter.crypto.service;

import cn.com.handthing.starter.crypto.core.AsymmetricEncryptor;
import cn.com.handthing.starter.crypto.core.KeyProvider;
import cn.com.handthing.starter.crypto.exception.CryptoException;
import cn.com.handthing.starter.crypto.service.AbstractCryptoService;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import org.bouncycastle.crypto.engines.SM2Engine;
import org.bouncycastle.crypto.params.ECDomainParameters;
import org.bouncycastle.crypto.params.ECPrivateKeyParameters;
import org.bouncycastle.crypto.params.ECPublicKeyParameters;
import org.bouncycastle.crypto.params.ParametersWithRandom;
import org.bouncycastle.jcajce.provider.asymmetric.ec.BCECPrivateKey;
import org.bouncycastle.jcajce.provider.asymmetric.ec.BCECPublicKey;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.jce.spec.ECParameterSpec;

import java.nio.charset.StandardCharsets;
import java.security.KeyPair;
import java.security.SecureRandom;
import java.security.Security;
import java.security.Signature;
import java.util.Base64;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 使用国密 SM2 算法的非对称加密器实现。
 *
 * <AUTHOR>
 * @since V1.0.0
 */
public class Sm2Encryptor extends AbstractCryptoService implements AsymmetricEncryptor {

    static {
        if (Security.getProvider(BouncyCastleProvider.PROVIDER_NAME) == null) {
            Security.addProvider(new BouncyCastleProvider());
        }
    }

    private final KeyProvider publicKeyProvider;
    private final KeyProvider privateKeyProvider;
    private final MeterRegistry meterRegistry;
    private final AtomicReference<KeyPair> keyPairRef = new AtomicReference<>();

    private static final String SIGNATURE_ALGORITHM = "SM3withSM2";

    public Sm2Encryptor(String beanName, KeyProvider publicKeyProvider, KeyProvider privateKeyProvider, MeterRegistry meterRegistry) {
        super(beanName);
        this.publicKeyProvider = Objects.requireNonNull(publicKeyProvider, "PublicKeyProvider must not be null");
        this.privateKeyProvider = Objects.requireNonNull(privateKeyProvider, "PrivateKeyProvider must not be null");
        this.meterRegistry = Objects.requireNonNull(meterRegistry, "MeterRegistry must not be null");
        init();
    }

    private void init() {
        try {
            publicKeyProvider.checkReady();
            privateKeyProvider.checkReady();
            KeyPair keyPair = privateKeyProvider.getKeyPair();
            this.keyPairRef.set(keyPair);
        } catch (Exception e) {
            throw new CryptoException("Failed to initialize SM2 key pair for processor: " + beanName, e);
        }
    }

    @Override
    public String encrypt(String plaintext) {
        return Timer.builder("crypto.operations.duration").tag("processor", beanName).tag("operation", "encrypt").register(meterRegistry)
                .record(() -> {
                    try {
                        BCECPublicKey publicKey = (BCECPublicKey) keyPairRef.get().getPublic();
                        ECParameterSpec ecParameterSpec = publicKey.getParameters();
                        ECDomainParameters domainParameters = new ECDomainParameters(ecParameterSpec.getCurve(), ecParameterSpec.getG(), ecParameterSpec.getN(), ecParameterSpec.getH());
                        ECPublicKeyParameters publicKeyParameters = new ECPublicKeyParameters(publicKey.getQ(), domainParameters);

                        SM2Engine sm2Engine = new SM2Engine();
                        sm2Engine.init(true, new ParametersWithRandom(publicKeyParameters, new SecureRandom()));
                        byte[] encrypted = sm2Engine.processBlock(plaintext.getBytes(StandardCharsets.UTF_8), 0, plaintext.getBytes(StandardCharsets.UTF_8).length);
                        return Base64.getEncoder().encodeToString(encrypted);
                    } catch (Exception e) {
                        throw new CryptoException("SM2 encryption failed for processor: " + beanName, e);
                    }
                });
    }

    @Override
    public String decrypt(String ciphertext) {
        return Timer.builder("crypto.operations.duration").tag("processor", beanName).tag("operation", "decrypt").register(meterRegistry)
                .record(() -> {
                    try {
                        BCECPrivateKey privateKey = (BCECPrivateKey) keyPairRef.get().getPrivate();
                        ECParameterSpec ecParameterSpec = privateKey.getParameters();
                        ECDomainParameters domainParameters = new ECDomainParameters(ecParameterSpec.getCurve(), ecParameterSpec.getG(), ecParameterSpec.getN(), ecParameterSpec.getH());
                        ECPrivateKeyParameters privateKeyParameters = new ECPrivateKeyParameters(privateKey.getD(), domainParameters);

                        SM2Engine sm2Engine = new SM2Engine();
                        sm2Engine.init(false, privateKeyParameters);
                        byte[] encryptedBytes = Base64.getDecoder().decode(ciphertext);
                        byte[] decrypted = sm2Engine.processBlock(encryptedBytes, 0, encryptedBytes.length);
                        return new String(decrypted, StandardCharsets.UTF_8);
                    } catch (Exception e) {
                        throw new CryptoException("SM2 decryption failed for processor: " + beanName, e);
                    }
                });
    }

    @Override
    public String sign(String data) {
        return Timer.builder("crypto.operations.duration").tag("processor", beanName).tag("operation", "sign").register(meterRegistry)
                .record(() -> {
                    try {
                        Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM, BouncyCastleProvider.PROVIDER_NAME);
                        signature.initSign(keyPairRef.get().getPrivate());
                        signature.update(data.getBytes(StandardCharsets.UTF_8));
                        return Base64.getEncoder().encodeToString(signature.sign());
                    } catch (Exception e) {
                        throw new CryptoException("SM2 signing failed for processor: " + beanName, e);
                    }
                });
    }

    @Override
    public boolean verify(String data, String signatureString) {
        return Timer.builder("crypto.operations.duration").tag("processor", beanName).tag("operation", "verify").register(meterRegistry)
                .record(() -> {
                    try {
                        Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM, BouncyCastleProvider.PROVIDER_NAME);
                        signature.initVerify(keyPairRef.get().getPublic());
                        signature.update(data.getBytes(StandardCharsets.UTF_8));
                        return signature.verify(Base64.getDecoder().decode(signatureString));
                    } catch (Exception e) {
                        return false;
                    }
                });
    }

    @Override
    public void selfCheck() throws CryptoException {
        String testData = "HandThing-SM2-SelfCheck";
        String signature = this.sign(testData);
        if (!this.verify(testData, signature)) {
            throw new IllegalStateException("Self-check failed for SM2 processor: " + beanName);
        }
        String encrypted = this.encrypt(testData);
        if (!testData.equals(this.decrypt(encrypted))) {
            throw new IllegalStateException("Encryption/Decryption self-check failed for SM2 processor: " + beanName);
        }
    }
}