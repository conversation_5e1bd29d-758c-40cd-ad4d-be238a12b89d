package cn.com.handthing.starter.connector.auth;

import cn.com.handthing.starter.connector.PlatformType;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 统一访问令牌模型
 * <p>
 * 统一不同平台的访问令牌格式，包含访问令牌、刷新令牌、过期时间等通用字段
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UnifiedAccessToken implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 平台类型
     */
    private PlatformType platform;

    /**
     * 访问令牌
     */
    private String accessToken;

    /**
     * 刷新令牌（如果平台支持）
     */
    private String refreshToken;

    /**
     * 令牌类型（通常为 "Bearer"）
     */
    private String tokenType;

    /**
     * 过期时间（秒）
     */
    private Long expiresIn;

    /**
     * 授权范围
     */
    private String scope;

    /**
     * 令牌创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 令牌过期时间
     */
    private LocalDateTime expiresAt;

    /**
     * 用户唯一标识（如果包含在令牌响应中）
     */
    private String userId;

    /**
     * 平台特定的额外信息
     */
    private Map<String, Object> extraInfo;

    /**
     * 检查令牌是否已过期
     *
     * @return 如果已过期返回true，否则返回false
     */
    public boolean isExpired() {
        if (expiresAt == null) {
            return false;
        }
        return LocalDateTime.now().isAfter(expiresAt);
    }

    /**
     * 检查令牌是否即将过期（默认提前5分钟）
     *
     * @return 如果即将过期返回true，否则返回false
     */
    public boolean isExpiringSoon() {
        return isExpiringSoon(5);
    }

    /**
     * 检查令牌是否即将过期
     *
     * @param minutesBefore 提前多少分钟判断为即将过期
     * @return 如果即将过期返回true，否则返回false
     */
    public boolean isExpiringSoon(int minutesBefore) {
        if (expiresAt == null) {
            return false;
        }
        return LocalDateTime.now().plusMinutes(minutesBefore).isAfter(expiresAt);
    }

    /**
     * 检查是否有刷新令牌
     *
     * @return 如果有刷新令牌返回true，否则返回false
     */
    public boolean hasRefreshToken() {
        return refreshToken != null && !refreshToken.trim().isEmpty();
    }

    /**
     * 获取剩余有效时间（秒）
     *
     * @return 剩余有效时间，如果已过期返回0
     */
    public long getRemainingSeconds() {
        if (expiresAt == null) {
            return Long.MAX_VALUE;
        }
        
        LocalDateTime now = LocalDateTime.now();
        if (now.isAfter(expiresAt)) {
            return 0;
        }
        
        return java.time.Duration.between(now, expiresAt).getSeconds();
    }

    /**
     * 创建一个新的令牌实例，设置创建时间和过期时间
     *
     * @param accessToken 访问令牌
     * @param expiresIn   过期时间（秒）
     * @param platform    平台类型
     * @return 新的令牌实例
     */
    public static UnifiedAccessToken create(String accessToken, Long expiresIn, PlatformType platform) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime expiresAt = expiresIn != null ? now.plusSeconds(expiresIn) : null;
        
        return UnifiedAccessToken.builder()
                .accessToken(accessToken)
                .expiresIn(expiresIn)
                .platform(platform)
                .tokenType("Bearer")
                .createdAt(now)
                .expiresAt(expiresAt)
                .build();
    }
}
