package cn.com.handthing.starter.crypto.core;

import java.security.Key;
import java.security.KeyPair;

/**
 * 密钥提供者接口。
 * <p>
 * 负责从不同的来源（如配置文件、文件系统、环境变量、Vault等）加载密钥，
 * 并将其抽象为统一的 {@link Key} 或 {@link KeyPair} 对象。
 *
 * <AUTHOR>
 * @since V1.0.0
 */
public interface KeyProvider {

    /**
     * 获取单个密钥。
     * <p>
     * 用于对称加密（返回 {@link javax.crypto.SecretKey}）或
     * 非对称加密的单个公钥/私钥（返回 {@link java.security.PublicKey} 或 {@link java.security.PrivateKey}）。
     *
     * @return 实现 {@link Key} 接口的密钥对象
     * @throws Exception 如果加载密钥失败
     */
    Key getKey() throws Exception;

    /**
     * 获取密钥对。
     * <p>
     * 专用于非对称加密，同时加载公钥和私钥。
     *
     * @return 包含公钥和私钥的 {@link KeyPair} 对象
     * @throws Exception 如果加载密钥对失败
     */
    KeyPair getKeyPair() throws Exception;

    /**
     * 获取原始的密钥字节数组。
     *
     * @return 密钥的字节表示
     * @throws Exception 如果加载密钥失败
     */
    byte[] getKeyBytes() throws Exception;

    /**
     * 检查此 KeyProvider 是否已成功初始化并准备就绪。
     *
     * @throws IllegalStateException 如果提供者未就绪
     */
    void checkReady() throws IllegalStateException;
}