package cn.com.handthing.starter.id.generator.impl;

import cn.com.handthing.starter.id.exception.IdGenerationException;
import cn.com.handthing.starter.id.generator.IdGenerator;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 池化ID生成器装饰器
 * <p>
 * 为了解决高并发下ID生成的性能抖动问题，特别是需要加锁的生成器（如Snowflake），
 * 引入池化技术。这不是一个直接的生成器，而是一个装饰器。
 * </p>
 * 
 * <h3>工作原理：</h3>
 * <ul>
 *   <li>内部维护一个有界BlockingQueue作为ID缓冲池</li>
 *   <li>后台线程异步填充ID池</li>
 *   <li>generate()方法直接从池中获取预生成的ID</li>
 *   <li>当池中ID数量低于阈值时，触发批量填充</li>
 * </ul>
 *
 * @param <T> ID类型
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
public class PooledIdGenerator<T> implements IdGenerator<T> {
    
    /**
     * 被装饰的真实ID生成器
     */
    private final IdGenerator<T> delegate;
    
    /**
     * ID缓冲池
     */
    private final BlockingQueue<T> idPool;
    
    /**
     * 池大小
     */
    private final int poolSize;
    
    /**
     * 填充阈值
     */
    private final int threshold;
    
    /**
     * 批量填充大小
     */
    private final int batchSize;
    
    /**
     * 后台填充线程池
     */
    private final ExecutorService fillExecutor;
    
    /**
     * 是否正在填充
     */
    private final AtomicBoolean filling = new AtomicBoolean(false);
    
    /**
     * 是否已关闭
     */
    private final AtomicBoolean shutdown = new AtomicBoolean(false);
    
    /**
     * 统计信息
     */
    private final AtomicLong totalGenerated = new AtomicLong(0);
    private final AtomicLong poolHits = new AtomicLong(0);
    private final AtomicLong poolMisses = new AtomicLong(0);
    private final AtomicLong fillCount = new AtomicLong(0);
    
    /**
     * 构造函数
     *
     * @param delegate  被装饰的ID生成器
     * @param poolSize  池大小
     * @param threshold 填充阈值
     * @param batchSize 批量填充大小
     */
    public PooledIdGenerator(IdGenerator<T> delegate, int poolSize, int threshold, int batchSize) {
        if (delegate == null) {
            throw new IllegalArgumentException("Delegate generator cannot be null");
        }
        if (poolSize <= 0) {
            throw new IllegalArgumentException("Pool size must be positive");
        }
        if (threshold <= 0 || threshold >= poolSize) {
            throw new IllegalArgumentException("Threshold must be positive and less than pool size");
        }
        if (batchSize <= 0 || batchSize > poolSize) {
            throw new IllegalArgumentException("Batch size must be positive and not greater than pool size");
        }
        
        this.delegate = delegate;
        this.poolSize = poolSize;
        this.threshold = threshold;
        this.batchSize = batchSize;
        this.idPool = new ArrayBlockingQueue<>(poolSize);
        this.fillExecutor = Executors.newSingleThreadExecutor(r -> {
            Thread t = new Thread(r, "PooledIdGenerator-Fill-" + delegate.getName());
            t.setDaemon(true);
            return t;
        });
        
        log.info("PooledIdGenerator created: delegate={}, poolSize={}, threshold={}, batchSize={}", 
                delegate.getName(), poolSize, threshold, batchSize);
    }
    
    /**
     * 使用默认参数的构造函数
     *
     * @param delegate 被装饰的ID生成器
     */
    public PooledIdGenerator(IdGenerator<T> delegate) {
        this(delegate, 200, 50, 100);
    }
    
    @Override
    public T generate() {
        if (shutdown.get()) {
            throw new IdGenerationException(getName(), "GENERATOR_SHUTDOWN", 
                    "Pooled generator has been shut down");
        }
        
        // 尝试从池中获取ID
        T id = idPool.poll();
        if (id != null) {
            poolHits.incrementAndGet();
            totalGenerated.incrementAndGet();
            
            // 检查是否需要填充
            if (idPool.size() <= threshold && filling.compareAndSet(false, true)) {
                fillExecutor.submit(this::fillPool);
            }
            
            return id;
        }
        
        // 池中没有ID，直接生成
        poolMisses.incrementAndGet();
        totalGenerated.incrementAndGet();
        
        // 异步填充池
        if (filling.compareAndSet(false, true)) {
            fillExecutor.submit(this::fillPool);
        }
        
        // 直接从委托生成器获取ID
        return delegate.generate();
    }
    
    /**
     * 填充ID池
     */
    private void fillPool() {
        try {
            int currentSize = idPool.size();
            int needFill = Math.min(batchSize, poolSize - currentSize);
            
            if (needFill <= 0) {
                return;
            }
            
            log.debug("Filling ID pool: current={}, need={}", currentSize, needFill);
            
            for (int i = 0; i < needFill; i++) {
                if (shutdown.get()) {
                    break;
                }
                
                try {
                    T id = delegate.generate();
                    if (!idPool.offer(id)) {
                        // 池已满，停止填充
                        break;
                    }
                } catch (Exception e) {
                    log.warn("Failed to generate ID during pool fill: {}", e.getMessage());
                    // 继续尝试生成其他ID
                }
            }
            
            fillCount.incrementAndGet();
            log.debug("ID pool filled: size={}", idPool.size());
            
        } catch (Exception e) {
            log.error("Error during pool fill", e);
        } finally {
            filling.set(false);
        }
    }
    
    @Override
    public Class<T> getIdType() {
        return delegate.getIdType();
    }
    
    @Override
    public String getName() {
        return "Pooled" + delegate.getName();
    }
    
    @Override
    public boolean isAvailable() {
        return !shutdown.get() && delegate.isAvailable();
    }
    
    @Override
    public void warmUp() {
        log.debug("Warming up pooled generator...");
        
        // 预热委托生成器
        delegate.warmUp();
        
        // 预填充池
        if (filling.compareAndSet(false, true)) {
            fillExecutor.submit(() -> {
                try {
                    // 填充到阈值
                    for (int i = 0; i < threshold && !shutdown.get(); i++) {
                        T id = delegate.generate();
                        idPool.offer(id);
                    }
                    log.debug("Pool pre-filled: size={}", idPool.size());
                } catch (Exception e) {
                    log.warn("Error during pool pre-fill", e);
                } finally {
                    filling.set(false);
                }
            });
        }
        
        log.debug("Pooled generator warmed up");
    }
    
    @Override
    public void shutdown() {
        if (shutdown.compareAndSet(false, true)) {
            log.info("Shutting down pooled generator...");
            
            // 关闭填充线程池
            fillExecutor.shutdown();
            try {
                if (!fillExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    fillExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                fillExecutor.shutdownNow();
            }
            
            // 清空池
            idPool.clear();
            
            // 关闭委托生成器
            delegate.shutdown();
            
            log.info("Pooled generator shut down. Stats: {}", getStatistics());
        }
    }
    
    /**
     * 获取池状态
     *
     * @return 池状态信息
     */
    public PoolStatus getPoolStatus() {
        return new PoolStatus(
                idPool.size(),
                poolSize,
                threshold,
                batchSize,
                filling.get(),
                shutdown.get()
        );
    }
    
    /**
     * 获取统计信息
     *
     * @return 统计信息
     */
    public PoolStatistics getStatistics() {
        long total = totalGenerated.get();
        long hits = poolHits.get();
        long misses = poolMisses.get();
        double hitRate = total > 0 ? (double) hits / total : 0.0;
        
        return new PoolStatistics(
                total,
                hits,
                misses,
                hitRate,
                fillCount.get()
        );
    }
    
    /**
     * 池状态信息
     */
    public static class PoolStatus {
        private final int currentSize;
        private final int maxSize;
        private final int threshold;
        private final int batchSize;
        private final boolean filling;
        private final boolean shutdown;
        
        public PoolStatus(int currentSize, int maxSize, int threshold, int batchSize, 
                         boolean filling, boolean shutdown) {
            this.currentSize = currentSize;
            this.maxSize = maxSize;
            this.threshold = threshold;
            this.batchSize = batchSize;
            this.filling = filling;
            this.shutdown = shutdown;
        }
        
        // Getters
        public int getCurrentSize() { return currentSize; }
        public int getMaxSize() { return maxSize; }
        public int getThreshold() { return threshold; }
        public int getBatchSize() { return batchSize; }
        public boolean isFilling() { return filling; }
        public boolean isShutdown() { return shutdown; }
        
        @Override
        public String toString() {
            return String.format("PoolStatus{size=%d/%d, threshold=%d, batchSize=%d, filling=%s, shutdown=%s}", 
                    currentSize, maxSize, threshold, batchSize, filling, shutdown);
        }
    }
    
    /**
     * 池统计信息
     */
    public static class PoolStatistics {
        private final long totalGenerated;
        private final long poolHits;
        private final long poolMisses;
        private final double hitRate;
        private final long fillCount;
        
        public PoolStatistics(long totalGenerated, long poolHits, long poolMisses, 
                             double hitRate, long fillCount) {
            this.totalGenerated = totalGenerated;
            this.poolHits = poolHits;
            this.poolMisses = poolMisses;
            this.hitRate = hitRate;
            this.fillCount = fillCount;
        }
        
        // Getters
        public long getTotalGenerated() { return totalGenerated; }
        public long getPoolHits() { return poolHits; }
        public long getPoolMisses() { return poolMisses; }
        public double getHitRate() { return hitRate; }
        public long getFillCount() { return fillCount; }
        
        @Override
        public String toString() {
            return String.format("PoolStatistics{total=%d, hits=%d, misses=%d, hitRate=%.2f%%, fills=%d}", 
                    totalGenerated, poolHits, poolMisses, hitRate * 100, fillCount);
        }
    }
    
    @Override
    public String toString() {
        return String.format("PooledIdGenerator{delegate=%s, status=%s, stats=%s}", 
                delegate.getName(), getPoolStatus(), getStatistics());
    }
}
