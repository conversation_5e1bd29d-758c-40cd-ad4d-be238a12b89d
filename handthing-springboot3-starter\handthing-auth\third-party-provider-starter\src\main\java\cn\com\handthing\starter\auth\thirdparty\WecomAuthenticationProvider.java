package cn.com.handthing.starter.auth.thirdparty;

import cn.com.handthing.starter.auth.core.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * 企业微信认证提供者
 * <p>
 * 实现基于企业微信OAuth2的认证逻辑，支持扫码登录和网页授权登录。
 * 提供完整的企业微信认证流程，包括授权码验证、用户信息获取、自动注册等。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WecomAuthenticationProvider implements AuthenticationProvider {

    private final WecomUserService wecomUserService;
    private final WecomApiService wecomApiService;
    private final JwtTokenProvider jwtTokenProvider;

    @Override
    public GrantType getSupportedGrantType() {
        return GrantType.WECOM;
    }

    @Override
    public boolean supports(AuthenticationRequest request) {
        return request instanceof WecomAuthenticationRequest && 
               request.getGrantType() == GrantType.WECOM;
    }

    @Override
    public AuthenticationResponse authenticate(AuthenticationRequest request) throws AuthenticationException {
        log.debug("Starting Wecom authentication for request: {}", request);

        if (!(request instanceof WecomAuthenticationRequest)) {
            throw new AuthenticationException("INVALID_REQUEST", "Invalid authentication request type");
        }

        WecomAuthenticationRequest wecomRequest = (WecomAuthenticationRequest) request;

        try {
            // 预处理
            preAuthenticate(wecomRequest);

            // 获取企业微信访问令牌
            String accessToken = getWecomAccessToken(wecomRequest);

            // 根据授权码获取用户信息
            WecomApiService.WecomUserResult userResult = wecomApiService.getUserInfoByCode(accessToken, wecomRequest.getCode());
            if (!userResult.isSuccess()) {
                throw new InvalidCredentialsException("企业微信授权码无效: " + userResult.getErrorMessage());
            }

            // 获取用户详细信息
            WecomApiService.WecomUserDetailResult userDetail = wecomApiService.getUserDetail(accessToken, userResult.getUserId());
            if (!userDetail.isSuccess()) {
                throw new AuthenticationException("WECOM_API_ERROR", "获取用户详细信息失败: " + userDetail.getErrorMessage());
            }

            // 查找或创建用户
            UserInfo userInfo = findOrCreateUser(wecomRequest, userDetail);

            // 验证用户状态
            validateUserStatus(userInfo);

            // 生成令牌
            String jwtAccessToken = generateAccessToken(userInfo, wecomRequest);
            String refreshToken = generateRefreshToken(userInfo, wecomRequest);
            Long expiresIn = 7200L; // 2小时

            // 更新登录信息
            updateLoginInfo(userInfo, wecomRequest);

            // 清除登录失败记录
            wecomUserService.clearLoginFailures(userResult.getUserId(), wecomRequest.getCorpId());

            // 创建成功响应
            WecomAuthenticationResponse response;
            if (wecomRequest.getAutoRegister() && wecomUserService.isFirstLogin(userInfo)) {
                response = WecomAuthenticationResponse.successForNewUser(jwtAccessToken, refreshToken, expiresIn, userInfo);
            } else {
                response = WecomAuthenticationResponse.success(jwtAccessToken, refreshToken, expiresIn, userInfo);
            }

            // 设置额外信息
            setAdditionalInfo(response, userInfo, wecomRequest, userDetail);

            log.info("Wecom authentication successful for user: {}, corpId: {}", userResult.getUserId(), wecomRequest.getCorpId());
            return response;

        } catch (AuthenticationException e) {
            // 记录登录失败
            recordLoginFailure(wecomRequest, e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Wecom authentication error", e);
            recordLoginFailure(wecomRequest, "System error");
            throw new AuthenticationException("AUTHENTICATION_ERROR", "Authentication failed", e);
        }
    }

    @Override
    public boolean validateToken(String token) {
        return jwtTokenProvider.validateToken(token);
    }

    @Override
    public AuthenticationResponse refreshToken(String refreshToken) throws AuthenticationException {
        try {
            JwtClaims claims = jwtTokenProvider.parseToken(refreshToken);
            
            // 验证是否为刷新令牌
            if (!"refresh_token".equals(claims.getTokenType())) {
                throw new AuthenticationException("INVALID_REFRESH_TOKEN", "Invalid refresh token type");
            }

            // 查找用户
            Optional<UserInfo> userOptional = wecomUserService.findById(claims.getSubject());
            if (userOptional.isEmpty()) {
                throw new AuthenticationException("USER_NOT_FOUND", "User not found");
            }

            UserInfo userInfo = userOptional.get();

            // 验证用户状态
            validateUserStatus(userInfo);

            // 生成新的访问令牌
            String newAccessToken = generateAccessToken(userInfo, null);
            Long expiresIn = 7200L;

            return WecomAuthenticationResponse.success(newAccessToken, refreshToken, expiresIn, userInfo);

        } catch (AuthenticationException e) {
            throw e;
        } catch (Exception e) {
            log.error("Refresh token error", e);
            throw new AuthenticationException("REFRESH_TOKEN_ERROR", "Failed to refresh token", e);
        }
    }

    @Override
    public String getProviderName() {
        return "Wecom Authentication Provider";
    }

    @Override
    public boolean isAvailable() {
        return true;
    }

    @Override
    public int getPriority() {
        return 2;
    }

    /**
     * 预处理认证请求
     *
     * @param request 企业微信认证请求
     * @throws AuthenticationException 认证异常
     */
    private void preAuthenticate(WecomAuthenticationRequest request) throws AuthenticationException {
        // 验证请求参数
        if (!request.isValid()) {
            throw new AuthenticationException("INVALID_REQUEST", "Invalid authentication request");
        }

        // 验证企业微信配置
        if (!wecomApiService.validateConfig(request.getCorpId(), request.getCorpSecret(), request.getAgentId())) {
            throw new AuthenticationException("INVALID_WECOM_CONFIG", "Invalid Wecom configuration");
        }
    }

    /**
     * 获取企业微信访问令牌
     *
     * @param request 企业微信认证请求
     * @return 访问令牌
     * @throws AuthenticationException 认证异常
     */
    private String getWecomAccessToken(WecomAuthenticationRequest request) throws AuthenticationException {
        WecomApiService.WecomTokenResult tokenResult = wecomApiService.getAccessToken(
                request.getCorpId(), request.getCorpSecret());
        
        if (!tokenResult.isSuccess()) {
            throw new AuthenticationException("WECOM_TOKEN_ERROR", 
                    "获取企业微信访问令牌失败: " + tokenResult.getErrorMessage());
        }

        return tokenResult.getAccessToken();
    }

    /**
     * 查找或创建用户
     *
     * @param request    企业微信认证请求
     * @param userDetail 企业微信用户详细信息
     * @return 用户信息
     * @throws AuthenticationException 认证异常
     */
    private UserInfo findOrCreateUser(WecomAuthenticationRequest request, 
                                     WecomApiService.WecomUserDetailResult userDetail) throws AuthenticationException {
        
        Optional<UserInfo> userOptional = wecomUserService.findByWecomUserId(userDetail.getUserId(), request.getCorpId());

        if (userOptional.isPresent()) {
            return userOptional.get();
        }

        // 尝试根据手机号或邮箱查找现有用户
        if (userDetail.getMobile() != null) {
            userOptional = wecomUserService.findByMobile(userDetail.getMobile());
            if (userOptional.isPresent()) {
                // 绑定企业微信用户
                UserInfo existingUser = userOptional.get();
                wecomUserService.bindWecomUser(existingUser.getUserId(), userDetail.getUserId(), 
                        request.getCorpId(), request.getAgentId());
                return existingUser;
            }
        }

        if (userDetail.getEmail() != null) {
            userOptional = wecomUserService.findByEmail(userDetail.getEmail());
            if (userOptional.isPresent()) {
                // 绑定企业微信用户
                UserInfo existingUser = userOptional.get();
                wecomUserService.bindWecomUser(existingUser.getUserId(), userDetail.getUserId(), 
                        request.getCorpId(), request.getAgentId());
                return existingUser;
            }
        }

        // 用户不存在
        if (!request.getAutoRegister()) {
            throw AuthenticationFailedException.userNotFound(userDetail.getUserId());
        }

        // 自动注册新用户
        try {
            UserInfo newUser = wecomUserService.createUser(userDetail, request.getCorpId(), request.getAgentId());
            log.info("Auto-registered new user for wecom userId: {}, corpId: {}", 
                    userDetail.getUserId(), request.getCorpId());
            return newUser;

        } catch (Exception e) {
            log.error("Failed to create user for wecom userId: {}, corpId: {}", 
                    userDetail.getUserId(), request.getCorpId(), e);
            throw new AuthenticationException("REGISTRATION_FAILED", "Failed to create user", e);
        }
    }

    /**
     * 验证用户状态
     *
     * @param userInfo 用户信息
     * @throws AuthenticationException 认证异常
     */
    private void validateUserStatus(UserInfo userInfo) throws AuthenticationException {
        // 检查用户是否有效
        if (!wecomUserService.isUserValid(userInfo)) {
            throw AuthenticationFailedException.userDisabled(userInfo.getUsername());
        }

        // 检查用户是否被锁定
        if (wecomUserService.isUserLocked(userInfo)) {
            throw AuthenticationFailedException.userLocked(userInfo.getUsername());
        }

        // 检查用户是否被禁用
        if (wecomUserService.isUserDisabled(userInfo)) {
            throw AuthenticationFailedException.userDisabled(userInfo.getUsername());
        }

        // 检查账户是否过期
        if (wecomUserService.isAccountExpired(userInfo)) {
            throw new AuthenticationException("ACCOUNT_EXPIRED", "Account has expired");
        }
    }

    /**
     * 生成访问令牌
     *
     * @param userInfo 用户信息
     * @param request  认证请求
     * @return 访问令牌
     */
    private String generateAccessToken(UserInfo userInfo, WecomAuthenticationRequest request) {
        JwtClaims claims = JwtClaims.builder()
                .subject(userInfo.getUserId())
                .username(userInfo.getUsername())
                .nickname(userInfo.getNickname())
                .email(userInfo.getEmail())
                .phone(userInfo.getPhone())
                .roles(userInfo.getRoles())
                .permissions(userInfo.getPermissions())
                .grantType(GrantType.WECOM.getCode())
                .tokenType("access_token")
                .build();

        if (request != null) {
            claims.setClientId(request.getClientId());
            claims.setScope(request.getScope());
            claims.setIpAddress(request.getIpAddress());
            claims.setUserAgent(request.getUserAgent());
            claims.setDeviceId(request.getDeviceId());
            
            // 添加企业微信相关信息
            claims.addCustomClaim("corp_id", request.getCorpId());
            claims.addCustomClaim("agent_id", request.getAgentId());
        }

        return jwtTokenProvider.generateAccessToken(claims);
    }

    /**
     * 生成刷新令牌
     *
     * @param userInfo 用户信息
     * @param request  认证请求
     * @return 刷新令牌
     */
    private String generateRefreshToken(UserInfo userInfo, WecomAuthenticationRequest request) {
        JwtClaims claims = JwtClaims.builder()
                .subject(userInfo.getUserId())
                .username(userInfo.getUsername())
                .grantType(GrantType.WECOM.getCode())
                .tokenType("refresh_token")
                .build();

        if (request != null) {
            claims.setClientId(request.getClientId());
            claims.addCustomClaim("corp_id", request.getCorpId());
        }

        return jwtTokenProvider.generateRefreshToken(claims);
    }

    /**
     * 更新登录信息
     *
     * @param userInfo 用户信息
     * @param request  认证请求
     */
    private void updateLoginInfo(UserInfo userInfo, WecomAuthenticationRequest request) {
        wecomUserService.updateLastLoginInfo(
                userInfo.getUserId(),
                LocalDateTime.now(),
                request.getIpAddress(),
                request.getUserAgent()
        );
    }

    /**
     * 设置额外信息
     *
     * @param response   认证响应
     * @param userInfo   用户信息
     * @param request    认证请求
     * @param userDetail 企业微信用户详细信息
     */
    private void setAdditionalInfo(WecomAuthenticationResponse response, UserInfo userInfo, 
                                  WecomAuthenticationRequest request, WecomApiService.WecomUserDetailResult userDetail) {
        response.setFirstLogin(wecomUserService.isFirstLogin(userInfo));
        response.setLastLoginTime(userInfo.getLastLoginTime());
        response.setLastLoginIp(userInfo.getLastLoginIp());
        
        // 设置企业微信相关信息
        response.setWecomUserInfo(userDetail.getUserId(), userDetail.getName(), userDetail.getAvatar(), 
                request.getCorpId(), request.getAgentId());
    }

    /**
     * 记录登录失败
     *
     * @param request 认证请求
     * @param reason  失败原因
     */
    private void recordLoginFailure(WecomAuthenticationRequest request, String reason) {
        try {
            // 这里使用企业微信用户标识记录失败
            String identifier = request.getCorpId() + ":" + request.getCode();
            wecomUserService.recordLoginFailure(identifier, request.getCorpId(), request.getIpAddress(), reason);
        } catch (Exception e) {
            log.error("Failed to record login failure", e);
        }
    }
}
