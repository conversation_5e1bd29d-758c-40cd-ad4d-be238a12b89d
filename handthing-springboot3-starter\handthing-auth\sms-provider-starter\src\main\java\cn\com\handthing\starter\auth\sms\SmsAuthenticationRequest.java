package cn.com.handthing.starter.auth.sms;

import cn.com.handthing.starter.auth.core.AuthenticationRequest;
import cn.com.handthing.starter.auth.core.GrantType;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 短信验证码认证请求
 * <p>
 * 用于短信验证码认证的请求对象，包含手机号、验证码等认证信息。
 * 支持登录和注册两种场景的短信验证码认证。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SmsAuthenticationRequest extends AuthenticationRequest {

    /**
     * 手机号
     */
    private String phone;

    /**
     * 短信验证码
     */
    private String smsCode;

    /**
     * 验证码类型（login-登录，register-注册）
     */
    private String codeType = "login";

    /**
     * 是否自动注册（如果用户不存在）
     */
    private Boolean autoRegister = false;

    /**
     * 用户昵称（自动注册时使用）
     */
    private String nickname;

    /**
     * 默认构造函数
     */
    public SmsAuthenticationRequest() {
        super(GrantType.SMS_CODE);
    }

    /**
     * 构造函数
     *
     * @param phone   手机号
     * @param smsCode 短信验证码
     */
    public SmsAuthenticationRequest(String phone, String smsCode) {
        super(GrantType.SMS_CODE);
        this.phone = phone;
        this.smsCode = smsCode;
    }

    /**
     * 构造函数
     *
     * @param phone        手机号
     * @param smsCode      短信验证码
     * @param codeType     验证码类型
     * @param autoRegister 是否自动注册
     */
    public SmsAuthenticationRequest(String phone, String smsCode, String codeType, Boolean autoRegister) {
        super(GrantType.SMS_CODE);
        this.phone = phone;
        this.smsCode = smsCode;
        this.codeType = codeType;
        this.autoRegister = autoRegister;
    }

    @Override
    public String getAuthenticationIdentifier() {
        return phone;
    }

    @Override
    public Object getCredentials() {
        return smsCode;
    }

    @Override
    public boolean isValid() {
        return super.isValid() && 
               phone != null && !phone.trim().isEmpty() &&
               smsCode != null && !smsCode.trim().isEmpty() &&
               isValidPhoneNumber(phone);
    }

    /**
     * 验证手机号格式
     *
     * @param phone 手机号
     * @return 如果格式正确返回true，否则返回false
     */
    private boolean isValidPhoneNumber(String phone) {
        // 简单的手机号格式验证（中国大陆手机号）
        return phone != null && phone.matches("^1[3-9]\\d{9}$");
    }

    /**
     * 判断是否为登录类型
     *
     * @return 如果是登录类型返回true，否则返回false
     */
    public boolean isLoginType() {
        return "login".equals(codeType);
    }

    /**
     * 判断是否为注册类型
     *
     * @return 如果是注册类型返回true，否则返回false
     */
    public boolean isRegisterType() {
        return "register".equals(codeType);
    }

    /**
     * 获取格式化的手机号（隐藏中间4位）
     *
     * @return 格式化的手机号
     */
    public String getMaskedPhone() {
        if (phone == null || phone.length() != 11) {
            return phone;
        }
        return phone.substring(0, 3) + "****" + phone.substring(7);
    }

    /**
     * 创建短信认证请求
     *
     * @param phone   手机号
     * @param smsCode 短信验证码
     * @return 短信认证请求
     */
    public static SmsAuthenticationRequest of(String phone, String smsCode) {
        return new SmsAuthenticationRequest(phone, smsCode);
    }

    /**
     * 创建登录类型的短信认证请求
     *
     * @param phone   手机号
     * @param smsCode 短信验证码
     * @return 短信认证请求
     */
    public static SmsAuthenticationRequest forLogin(String phone, String smsCode) {
        return new SmsAuthenticationRequest(phone, smsCode, "login", false);
    }

    /**
     * 创建注册类型的短信认证请求
     *
     * @param phone    手机号
     * @param smsCode  短信验证码
     * @param nickname 用户昵称
     * @return 短信认证请求
     */
    public static SmsAuthenticationRequest forRegister(String phone, String smsCode, String nickname) {
        SmsAuthenticationRequest request = new SmsAuthenticationRequest(phone, smsCode, "register", true);
        request.setNickname(nickname);
        return request;
    }

    /**
     * 创建自动注册的短信认证请求
     *
     * @param phone   手机号
     * @param smsCode 短信验证码
     * @return 短信认证请求
     */
    public static SmsAuthenticationRequest withAutoRegister(String phone, String smsCode) {
        return new SmsAuthenticationRequest(phone, smsCode, "login", true);
    }

    @Override
    public String toString() {
        return String.format("SmsAuthenticationRequest{phone='%s', codeType='%s', autoRegister=%s}",
                getMaskedPhone(), codeType, autoRegister);
    }
}
