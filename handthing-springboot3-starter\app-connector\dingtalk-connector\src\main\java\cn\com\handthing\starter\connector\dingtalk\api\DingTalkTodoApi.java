package cn.com.handthing.starter.connector.dingtalk.api;

import cn.com.handthing.starter.connector.dingtalk.config.DingTalkConfig;
import cn.com.handthing.starter.connector.dingtalk.model.DingTalkTodo;
import cn.com.handthing.starter.connector.exception.ApiCallException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.List;

/**
 * 钉钉待办事项API
 * <p>
 * 提供钉钉待办事项管理功能，包括创建、查询、更新待办事项等功能。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "handthing.connector.dingtalk", name = "enabled", havingValue = "true")
public class DingTalkTodoApi {

    private final DingTalkConfig config;
    private final RestTemplate restTemplate;

    /**
     * 创建待办事项
     *
     * @param accessToken 访问令牌
     * @param todo        待办事项信息
     * @return 创建结果
     * @throws ApiCallException 如果创建失败
     */
    public Object createTodo(String accessToken, DingTalkTodo todo) throws ApiCallException {
        // TODO: 实现创建待办事项
        log.debug("Creating DingTalk todo: {}", todo.getSubject());
        throw new UnsupportedOperationException("createTodo not implemented yet");
    }

    /**
     * 获取待办事项列表
     *
     * @param accessToken 访问令牌
     * @param userId      用户ID
     * @return 待办事项列表
     * @throws ApiCallException 如果获取失败
     */
    public List<DingTalkTodo> getTodoList(String accessToken, String userId) throws ApiCallException {
        // TODO: 实现获取待办事项列表
        log.debug("Getting DingTalk todo list for userId: {}", userId);
        throw new UnsupportedOperationException("getTodoList not implemented yet");
    }

    /**
     * 更新待办事项状态
     *
     * @param accessToken 访问令牌
     * @param todoId      待办事项ID
     * @param status      新状态
     * @return 更新结果
     * @throws ApiCallException 如果更新失败
     */
    public Object updateTodoStatus(String accessToken, String todoId, String status) throws ApiCallException {
        // TODO: 实现更新待办事项状态
        log.debug("Updating DingTalk todo status: {} -> {}", todoId, status);
        throw new UnsupportedOperationException("updateTodoStatus not implemented yet");
    }
}
