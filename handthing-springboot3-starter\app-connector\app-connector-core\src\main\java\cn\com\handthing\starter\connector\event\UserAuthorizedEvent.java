package cn.com.handthing.starter.connector.event;

import cn.com.handthing.starter.connector.PlatformType;
import cn.com.handthing.starter.connector.auth.UnifiedAccessToken;
import cn.com.handthing.starter.connector.auth.UnifiedUserInfo;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 用户授权成功事件
 * <p>
 * 当用户成功完成OAuth授权流程后发布此事件，
 * 包含用户信息、访问令牌等相关数据。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Getter
public class UserAuthorizedEvent extends ConnectorEvent {

    private static final long serialVersionUID = 1L;

    /**
     * 事件类型常量
     */
    public static final String EVENT_TYPE = "USER_AUTHORIZED";

    /**
     * 用户信息
     */
    private final UnifiedUserInfo userInfo;

    /**
     * 访问令牌
     */
    private final UnifiedAccessToken accessToken;

    /**
     * 授权状态参数
     */
    private final String state;

    /**
     * 授权码
     */
    private final String authorizationCode;

    /**
     * 构造函数
     *
     * @param source            事件源
     * @param platform          平台类型
     * @param userInfo          用户信息
     * @param accessToken       访问令牌
     * @param state             状态参数
     * @param authorizationCode 授权码
     */
    public UserAuthorizedEvent(Object source, PlatformType platform, UnifiedUserInfo userInfo, 
                              UnifiedAccessToken accessToken, String state, String authorizationCode) {
        super(source, platform, EVENT_TYPE, createEventData(userInfo, accessToken, state, authorizationCode));
        this.userInfo = userInfo;
        this.accessToken = accessToken;
        this.state = state;
        this.authorizationCode = authorizationCode;
    }

    /**
     * 构造函数（简化版本）
     *
     * @param source      事件源
     * @param platform    平台类型
     * @param userInfo    用户信息
     * @param accessToken 访问令牌
     */
    public UserAuthorizedEvent(Object source, PlatformType platform, UnifiedUserInfo userInfo, UnifiedAccessToken accessToken) {
        this(source, platform, userInfo, accessToken, null, null);
    }

    /**
     * 检查是否包含用户信息
     *
     * @return 如果包含用户信息返回true，否则返回false
     */
    public boolean hasUserInfo() {
        return userInfo != null;
    }

    /**
     * 检查是否包含访问令牌
     *
     * @return 如果包含访问令牌返回true，否则返回false
     */
    public boolean hasAccessToken() {
        return accessToken != null;
    }

    /**
     * 检查访问令牌是否有效
     *
     * @return 如果访问令牌有效返回true，否则返回false
     */
    public boolean isAccessTokenValid() {
        return accessToken != null && !accessToken.isExpired();
    }

    /**
     * 获取用户的唯一标识
     *
     * @return 用户唯一标识，如果用户信息不存在返回null
     */
    public String getUserUniqueId() {
        return userInfo != null ? userInfo.getUniqueId() : null;
    }

    /**
     * 获取用户显示名称
     *
     * @return 用户显示名称，如果用户信息不存在返回null
     */
    public String getUserDisplayName() {
        return userInfo != null ? userInfo.getDisplayName() : null;
    }

    /**
     * 获取访问令牌字符串
     *
     * @return 访问令牌字符串，如果令牌不存在返回null
     */
    public String getAccessTokenString() {
        return accessToken != null ? accessToken.getAccessToken() : null;
    }

    /**
     * 检查是否有状态参数
     *
     * @return 如果有状态参数返回true，否则返回false
     */
    public boolean hasState() {
        return state != null && !state.trim().isEmpty();
    }

    /**
     * 检查是否有授权码
     *
     * @return 如果有授权码返回true，否则返回false
     */
    public boolean hasAuthorizationCode() {
        return authorizationCode != null && !authorizationCode.trim().isEmpty();
    }

    /**
     * 创建事件数据
     *
     * @param userInfo          用户信息
     * @param accessToken       访问令牌
     * @param state             状态参数
     * @param authorizationCode 授权码
     * @return 事件数据Map
     */
    private static Map<String, Object> createEventData(UnifiedUserInfo userInfo, UnifiedAccessToken accessToken, 
                                                       String state, String authorizationCode) {
        Map<String, Object> eventData = new HashMap<>();
        
        if (userInfo != null) {
            eventData.put("userId", userInfo.getUserId());
            eventData.put("userDisplayName", userInfo.getDisplayName());
            eventData.put("userUniqueId", userInfo.getUniqueId());
        }
        
        if (accessToken != null) {
            eventData.put("accessToken", accessToken.getAccessToken());
            eventData.put("tokenType", accessToken.getTokenType());
            eventData.put("expiresIn", accessToken.getExpiresIn());
            eventData.put("hasRefreshToken", accessToken.hasRefreshToken());
        }
        
        if (state != null) {
            eventData.put("state", state);
        }
        
        if (authorizationCode != null) {
            eventData.put("authorizationCode", authorizationCode);
        }
        
        return eventData;
    }

    @Override
    public String getEventDescription() {
        StringBuilder sb = new StringBuilder();
        sb.append(String.format("[%s] User authorized successfully", 
                getPlatform() != null ? getPlatform().getDisplayName() : "Unknown Platform"));
        
        if (userInfo != null) {
            sb.append(String.format(" - User: %s", userInfo.getDisplayName()));
        }
        
        if (accessToken != null) {
            sb.append(String.format(" - Token expires in: %s seconds", accessToken.getExpiresIn()));
        }
        
        sb.append(String.format(" at %s", getEventTime()));
        
        return sb.toString();
    }
}
