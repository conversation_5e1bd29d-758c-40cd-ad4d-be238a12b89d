<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>cn.com.handthing.springboot3.starter</groupId>
        <artifactId>starter-parent</artifactId>
        <version>${revision}</version>
        <relativePath>../starter-parent</relativePath>
    </parent>

    <artifactId>test-app</artifactId>
    <name>HandThing :: Test Application</name>
    <description>用于测试各种 HandThing Starter 功能的综合测试应用</description>

    <dependencies>
        <!-- Spring Boot Web Starter -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!-- Spring Boot Actuator -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <!-- Spring Boot Thymeleaf -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
        </dependency>

        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- API Doc Spring Boot Starter -->
        <dependency>
            <groupId>cn.com.handthing.springboot3.starter</groupId>
            <artifactId>api-doc-spring-boot-starter</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- App Connector Spring Boot Starter -->
        <dependency>
            <groupId>cn.com.handthing.springboot3.starter</groupId>
            <artifactId>app-connector-spring-boot-starter</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- WeCom Connector -->
        <dependency>
            <groupId>cn.com.handthing.springboot3.starter</groupId>
            <artifactId>wecom-connector</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- DingTalk Connector -->
        <dependency>
            <groupId>cn.com.handthing.springboot3.starter</groupId>
            <artifactId>dingtalk-connector</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- Douyin Connector -->
        <dependency>
            <groupId>cn.com.handthing.springboot3.starter</groupId>
            <artifactId>douyin-connector</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- Alipay Connector -->
        <dependency>
            <groupId>cn.com.handthing.springboot3.starter</groupId>
            <artifactId>alipay-connector</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- WeChat Connector -->
        <dependency>
            <groupId>cn.com.handthing.springboot3.starter</groupId>
            <artifactId>wechat-connector</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- Feishu Connector -->
        <dependency>
            <groupId>cn.com.handthing.springboot3.starter</groupId>
            <artifactId>feishu-connector</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- Toutiao Connector -->
        <dependency>
            <groupId>cn.com.handthing.springboot3.starter</groupId>
            <artifactId>toutiao-connector</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- XiaoHongShu Connector -->
        <dependency>
            <groupId>cn.com.handthing.springboot3.starter</groupId>
            <artifactId>xiaohongshu-connector</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- Bilibili Connector -->
        <dependency>
            <groupId>cn.com.handthing.springboot3.starter</groupId>
            <artifactId>bilibili-connector</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- Cache Spring Boot Starter -->
        <dependency>
            <groupId>cn.com.handthing.springboot3.starter</groupId>
            <artifactId>cache-spring-boot-starter</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- Tenant Auth Starter -->
        <dependency>
            <groupId>cn.com.handthing.springboot3.starter</groupId>
            <artifactId>tenant-auth-starter</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- Distributed Log Spring Boot Starter - 暂时禁用 -->
        <!--
        <dependency>
            <groupId>cn.com.handthing.springboot3.starter</groupId>
            <artifactId>distributed-log-spring-boot-starter</artifactId>
            <version>${revision}</version>
        </dependency>
        -->

        <!-- HTTP Client Spring Boot Starter -->
        <dependency>
            <groupId>cn.com.handthing.springboot3.starter</groupId>
            <artifactId>http-client-spring-boot-starter</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- HandThing Auth Starters -->
        <dependency>
            <groupId>cn.com.handthing.springboot3.starter</groupId>
            <artifactId>auth-spring-boot-starter</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>cn.com.handthing.springboot3.starter</groupId>
            <artifactId>password-provider-starter</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>cn.com.handthing.springboot3.starter</groupId>
            <artifactId>sms-provider-starter</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>cn.com.handthing.springboot3.starter</groupId>
            <artifactId>third-party-provider-starter</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- Database Dependencies -->
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>runtime</scope>
        </dependency>

        <!-- Test Dependencies -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>
