package cn.com.handthing.starter.dataauth.datalayer.config;

import cn.com.handthing.starter.dataauth.datalayer.aspect.DataPermissionAspect;
import cn.com.handthing.starter.dataauth.datalayer.handler.HandthingDataPermissionHandler;
import cn.com.handthing.starter.dataauth.datalayer.provider.DataPermissionProvider;
import cn.com.handthing.starter.dataauth.datalayer.provider.impl.DeptPermissionProvider;
import cn.com.handthing.starter.dataauth.datalayer.provider.impl.UserPermissionProvider;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.DataPermissionInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Primary;

import java.util.List;

/**
 * 数据权限自动配置类
 * <p>
 * 负责自动装配数据权限相关的Bean，包括权限拦截器、AOP切面、权限提供者等。
 * 根据配置属性决定启用哪些数据权限功能。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@AutoConfiguration
@EnableConfigurationProperties(DataAuthDatalayerProperties.class)
@ConditionalOnProperty(prefix = "handthing.datalayer.dataauth", name = "enabled", havingValue = "true", matchIfMissing = true)
@ConditionalOnClass(MybatisPlusInterceptor.class)
@ComponentScan(basePackages = "cn.com.handthing.starter.dataauth.datalayer")
public class DataAuthDatalayerAutoConfiguration {
    
    /**
     * 配置数据权限处理器
     * <p>
     * 实现MyBatis-Plus的DataPermissionHandler接口，自动在SQL中添加权限条件
     * </p>
     *
     * @param properties 数据权限配置属性
     * @return 数据权限处理器
     */
    @Bean
    @ConditionalOnMissingBean
    public HandthingDataPermissionHandler handthingDataPermissionHandler(DataAuthDatalayerProperties properties) {
        // 初始化默认配置
        properties.initDefaults();
        
        // 验证配置
        properties.validate();
        
        log.info("Configured HandThing data permission handler: strictMode={}, defaultFailureStrategy={}", 
                properties.isStrictMode(),
                properties.getDefaultFailureStrategy());
        
        return new HandthingDataPermissionHandler();
    }
    
    /**
     * 配置数据权限拦截器
     * <p>
     * 向MybatisPlusInterceptor添加DataPermissionInterceptor
     * </p>
     *
     * @param mybatisPlusInterceptor MyBatis-Plus拦截器
     * @param dataPermissionHandler  数据权限处理器
     * @param properties             数据权限配置属性
     * @return 增强后的MyBatis-Plus拦截器
     */
    @Bean
    @Primary
    public MybatisPlusInterceptor dataAuthMybatisPlusInterceptor(
            MybatisPlusInterceptor mybatisPlusInterceptor,
            HandthingDataPermissionHandler dataPermissionHandler,
            DataAuthDatalayerProperties properties) {
        
        // 创建数据权限拦截器
        DataPermissionInterceptor dataPermissionInterceptor = new DataPermissionInterceptor(dataPermissionHandler);
        
        // 添加到拦截器链，位置在租户拦截器之后
        mybatisPlusInterceptor.getInterceptors().add(dataPermissionInterceptor);
        
        log.info("Added data permission interceptor to MyBatis-Plus interceptor chain");
        log.info("Data permission interceptor configuration: {}", properties.getConfigSummary());
        
        return mybatisPlusInterceptor;
    }
    
    /**
     * 配置数据权限AOP切面
     * <p>
     * 拦截@DataPermission注解的方法，设置权限上下文
     * </p>
     *
     * @param properties 数据权限配置属性
     * @return 数据权限AOP切面
     */
    @Bean
    @ConditionalOnProperty(prefix = "handthing.datalayer.dataauth", name = "aop-enabled", havingValue = "true", matchIfMissing = true)
    @ConditionalOnMissingBean
    public DataPermissionAspect dataPermissionAspect(DataAuthDatalayerProperties properties) {
        log.info("Configured data permission AOP aspect");
        return new DataPermissionAspect();
    }
    
    /**
     * 配置部门权限提供者
     * <p>
     * 实现基于部门的数据权限控制
     * </p>
     *
     * @param properties 数据权限配置属性
     * @return 部门权限提供者
     */
    @Bean
    @ConditionalOnProperty(prefix = "handthing.datalayer.dataauth.provider.dept", name = "enabled", havingValue = "true", matchIfMissing = true)
    @ConditionalOnMissingBean(DeptPermissionProvider.class)
    public DeptPermissionProvider deptPermissionProvider(DataAuthDatalayerProperties properties) {
        log.info("Configured dept permission provider: defaultField={}, hierarchyEnabled={}", 
                properties.getProvider().getDept().getDefaultField(),
                properties.getProvider().getDept().isHierarchyEnabled());
        return new DeptPermissionProvider();
    }
    
    /**
     * 配置用户权限提供者
     * <p>
     * 实现基于用户的数据权限控制
     * </p>
     *
     * @param properties 数据权限配置属性
     * @return 用户权限提供者
     */
    @Bean
    @ConditionalOnProperty(prefix = "handthing.datalayer.dataauth.provider.user", name = "enabled", havingValue = "true", matchIfMissing = true)
    @ConditionalOnMissingBean(UserPermissionProvider.class)
    public UserPermissionProvider userPermissionProvider(DataAuthDatalayerProperties properties) {
        log.info("Configured user permission provider: defaultField={}, userIdField={}", 
                properties.getProvider().getUser().getDefaultField(),
                properties.getProvider().getUser().getUserIdField());
        return new UserPermissionProvider();
    }
    
    /**
     * 配置启动时的日志输出
     */
    @Bean
    public DataAuthDatalayerConfigurationLogger dataAuthDatalayerConfigurationLogger(
            DataAuthDatalayerProperties properties,
            @Autowired(required = false) HandthingDataPermissionHandler dataPermissionHandler,
            @Autowired(required = false) List<DataPermissionProvider> permissionProviders) {
        return new DataAuthDatalayerConfigurationLogger(properties, dataPermissionHandler, permissionProviders);
    }
    
    /**
     * 数据权限配置日志记录器
     */
    public static class DataAuthDatalayerConfigurationLogger {
        
        public DataAuthDatalayerConfigurationLogger(DataAuthDatalayerProperties properties, 
                                                   HandthingDataPermissionHandler dataPermissionHandler,
                                                   List<DataPermissionProvider> permissionProviders) {
            logConfiguration(properties, dataPermissionHandler, permissionProviders);
        }
        
        private void logConfiguration(DataAuthDatalayerProperties properties, 
                                    HandthingDataPermissionHandler dataPermissionHandler,
                                    List<DataPermissionProvider> permissionProviders) {
            log.info("=== HandThing DataAuth DataLayer Configuration ===");
            log.info("Enabled: {}", properties.isEnabled());
            
            if (properties.isEnabled()) {
                log.info("Data Permission Configuration:");
                log.info("  AOP Enabled: {}", properties.isAopEnabled());
                log.info("  Strict Mode: {}", properties.isStrictMode());
                log.info("  Default Failure Strategy: {}", properties.getDefaultFailureStrategy());
                
                if (properties.isCacheEnabled()) {
                    log.info("Cache Configuration:");
                    log.info("  Enabled: {}", properties.isCacheEnabled());
                    log.info("  Type: {}", properties.getCache().getType());
                    log.info("  Expire Seconds: {}", properties.getCache().getExpireSeconds());
                    log.info("  Max Size: {}", properties.getCache().getMaxSize());
                    log.info("  Key Prefix: {}", properties.getCache().getKeyPrefix());
                }
                
                log.info("Super Admin Configuration:");
                log.info("  Enabled: {}", properties.getSuperAdmin().isEnabled());
                log.info("  Check Strategy: {}", properties.getSuperAdmin().getCheckStrategy());
                log.info("  Roles: {} roles", properties.getSuperAdmin().getRoles().size());
                if (!properties.getSuperAdmin().getRoles().isEmpty()) {
                    log.info("    Roles: {}", properties.getSuperAdmin().getRoles());
                }
                log.info("  User IDs: {} users", properties.getSuperAdmin().getUserIds().size());
                
                log.info("Provider Configuration:");
                log.info("  Dept Provider: enabled={}, defaultField={}, hierarchyEnabled={}", 
                        properties.getProvider().getDept().isEnabled(),
                        properties.getProvider().getDept().getDefaultField(),
                        properties.getProvider().getDept().isHierarchyEnabled());
                log.info("  User Provider: enabled={}, defaultField={}, userIdField={}", 
                        properties.getProvider().getUser().isEnabled(),
                        properties.getProvider().getUser().getDefaultField(),
                        properties.getProvider().getUser().getUserIdField());
                log.info("  Role Provider: enabled={}, defaultField={}", 
                        properties.getProvider().getRole().isEnabled(),
                        properties.getProvider().getRole().getDefaultField());
                
                log.info("Ignore Configuration:");
                log.info("  Ignore Packages: {} packages", properties.getIgnorePackages().size());
                if (!properties.getIgnorePackages().isEmpty()) {
                    log.info("    Packages: {}", properties.getIgnorePackages());
                }
                log.info("  Ignore Classes: {} classes", properties.getIgnoreClasses().size());
                if (!properties.getIgnoreClasses().isEmpty()) {
                    log.info("    Classes: {}", properties.getIgnoreClasses());
                }
                log.info("  Ignore Method Patterns: {} patterns", properties.getIgnoreMethodPatterns().size());
                if (!properties.getIgnoreMethodPatterns().isEmpty()) {
                    log.info("    Patterns: {}", properties.getIgnoreMethodPatterns());
                }
                
                // 验证数据权限处理器状态
                if (dataPermissionHandler != null) {
                    log.info("Data Permission Handler Status:");
                    log.info("  Handler: {}", dataPermissionHandler.getClass().getSimpleName());
                    log.info("  Status: {}", dataPermissionHandler.getStatusInfo());
                } else {
                    log.warn("Data Permission Handler is not available");
                }
                
                // 验证权限提供者
                if (permissionProviders != null && !permissionProviders.isEmpty()) {
                    log.info("Permission Providers: {} providers", permissionProviders.size());
                    for (DataPermissionProvider provider : permissionProviders) {
                        log.info("  Provider: {} (order={}, available={})", 
                                provider.getName(), 
                                provider.getOrder(), 
                                provider.isAvailable());
                    }
                } else {
                    log.warn("No permission providers found");
                }
            }
            
            log.info("================================================");
        }
    }
}
