package cn.com.handthing.starter.auth.core;

/**
 * 认证异常基类
 * <p>
 * 定义认证过程中可能出现的各种异常的基类。
 * 提供统一的异常处理机制，包含错误代码和详细描述。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
public class AuthenticationException extends RuntimeException {

    /**
     * 错误代码
     */
    private final String errorCode;

    /**
     * 错误类型
     */
    private final ErrorType errorType;

    /**
     * 构造函数
     *
     * @param message 错误消息
     */
    public AuthenticationException(String message) {
        super(message);
        this.errorCode = "AUTH_ERROR";
        this.errorType = ErrorType.GENERAL;
    }

    /**
     * 构造函数
     *
     * @param message 错误消息
     * @param cause   原因异常
     */
    public AuthenticationException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = "AUTH_ERROR";
        this.errorType = ErrorType.GENERAL;
    }

    /**
     * 构造函数
     *
     * @param errorCode 错误代码
     * @param message   错误消息
     */
    public AuthenticationException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
        this.errorType = ErrorType.GENERAL;
    }

    /**
     * 构造函数
     *
     * @param errorCode 错误代码
     * @param message   错误消息
     * @param cause     原因异常
     */
    public AuthenticationException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.errorType = ErrorType.GENERAL;
    }

    /**
     * 构造函数
     *
     * @param errorCode 错误代码
     * @param errorType 错误类型
     * @param message   错误消息
     */
    public AuthenticationException(String errorCode, ErrorType errorType, String message) {
        super(message);
        this.errorCode = errorCode;
        this.errorType = errorType;
    }

    /**
     * 构造函数
     *
     * @param errorCode 错误代码
     * @param errorType 错误类型
     * @param message   错误消息
     * @param cause     原因异常
     */
    public AuthenticationException(String errorCode, ErrorType errorType, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.errorType = errorType;
    }

    /**
     * 获取错误代码
     *
     * @return 错误代码
     */
    public String getErrorCode() {
        return errorCode;
    }

    /**
     * 获取错误类型
     *
     * @return 错误类型
     */
    public ErrorType getErrorType() {
        return errorType;
    }

    /**
     * 获取完整的错误信息
     *
     * @return 错误信息
     */
    public String getFullErrorMessage() {
        return String.format("[%s] %s", errorCode, getMessage());
    }

    /**
     * 错误类型枚举
     */
    public enum ErrorType {
        /**
         * 一般错误
         */
        GENERAL("general", "一般错误"),

        /**
         * 认证失败
         */
        AUTHENTICATION_FAILED("authentication_failed", "认证失败"),

        /**
         * 无效凭证
         */
        INVALID_CREDENTIALS("invalid_credentials", "无效凭证"),

        /**
         * 令牌过期
         */
        TOKEN_EXPIRED("token_expired", "令牌过期"),

        /**
         * 无效令牌
         */
        INVALID_TOKEN("invalid_token", "无效令牌"),

        /**
         * 访问被拒绝
         */
        ACCESS_DENIED("access_denied", "访问被拒绝"),

        /**
         * 用户不存在
         */
        USER_NOT_FOUND("user_not_found", "用户不存在"),

        /**
         * 用户被锁定
         */
        USER_LOCKED("user_locked", "用户被锁定"),

        /**
         * 用户被禁用
         */
        USER_DISABLED("user_disabled", "用户被禁用"),

        /**
         * 密码错误
         */
        INVALID_PASSWORD("invalid_password", "密码错误"),

        /**
         * 验证码错误
         */
        INVALID_CODE("invalid_code", "验证码错误"),

        /**
         * 验证码过期
         */
        CODE_EXPIRED("code_expired", "验证码过期"),

        /**
         * 第三方认证失败
         */
        THIRD_PARTY_AUTH_FAILED("third_party_auth_failed", "第三方认证失败"),

        /**
         * 配置错误
         */
        CONFIGURATION_ERROR("configuration_error", "配置错误"),

        /**
         * 网络错误
         */
        NETWORK_ERROR("network_error", "网络错误"),

        /**
         * 系统错误
         */
        SYSTEM_ERROR("system_error", "系统错误");

        private final String code;
        private final String description;

        ErrorType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }

    @Override
    public String toString() {
        return String.format("AuthenticationException{errorCode='%s', errorType=%s, message='%s'}",
                errorCode, errorType, getMessage());
    }
}
