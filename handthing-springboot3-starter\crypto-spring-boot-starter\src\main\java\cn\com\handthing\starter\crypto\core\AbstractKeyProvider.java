package cn.com.handthing.starter.crypto.core;

import cn.com.handthing.starter.crypto.properties.CryptoProperties.KeyStoreConfig;

import java.security.Key;
import java.security.KeyPair;

/**
 * KeyProvider 的抽象基类，提供通用实现和空实现。
 *
 * <AUTHOR>
 * @since V1.0.0
 */
public abstract class AbstractKeyProvider implements KeyProvider {

    protected final KeyStoreConfig config;
    protected boolean ready = false;

    protected AbstractKeyProvider(KeyStoreConfig config) {
        this.config = config;
    }

    @Override
    public Key getKey() throws Exception {
        throw new UnsupportedOperationException("This KeyProvider does not support retrieving a single key.");
    }

    @Override
    public KeyPair getKeyPair() throws Exception {
        throw new UnsupportedOperationException("This KeyProvider does not support retrieving a key pair.");
    }

    @Override
    public byte[] getKeyBytes() throws Exception {
        throw new UnsupportedOperationException("This KeyProvider does not support retrieving raw key bytes.");
    }
    
    @Override
    public void checkReady() throws IllegalStateException {
        if (!ready) {
            throw new IllegalStateException("KeyProvider is not ready. Key might not have been loaded successfully.");
        }
    }
}