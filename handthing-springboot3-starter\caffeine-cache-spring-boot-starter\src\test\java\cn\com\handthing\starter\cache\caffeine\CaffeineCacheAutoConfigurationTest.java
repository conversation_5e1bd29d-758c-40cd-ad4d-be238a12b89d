package cn.com.handthing.starter.cache.caffeine;

import cn.com.handthing.starter.cache.CacheService;
import cn.com.handthing.starter.cache.caffeine.config.CaffeineCacheProperties;
import org.junit.jupiter.api.Test;
import org.springframework.boot.autoconfigure.AutoConfigurations;
import org.springframework.boot.test.context.runner.ApplicationContextRunner;
import org.springframework.cache.CacheManager;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * CaffeineCacheAutoConfiguration 单元测试
 *
 * <AUTHOR>
 * @since V1.0.0
 */
class CaffeineCacheAutoConfigurationTest {

    private final ApplicationContextRunner contextRunner = new ApplicationContextRunner()
            .withConfiguration(AutoConfigurations.of(CaffeineCacheAutoConfiguration.class));

    @Test
    void testAutoConfiguration_Enabled() {
        contextRunner
                .withPropertyValues("handthing.cache.type=caffeine")
                .run(context -> {
                    assertThat(context).hasSingleBean(CaffeineCacheProperties.class);
                    assertThat(context).hasSingleBean(CacheManager.class);
                    assertThat(context).hasSingleBean(CacheService.class);
                    assertThat(context).getBean(CacheManager.class)
                            .isInstanceOf(CaffeineCacheManager.class);
                    assertThat(context).getBean(CacheService.class)
                            .isInstanceOf(CaffeineCacheService.class);
                });
    }

    @Test
    void testAutoConfiguration_Disabled() {
        contextRunner
                .withPropertyValues("handthing.cache.type=redis")
                .run(context -> {
                    assertThat(context).doesNotHaveBean(CaffeineCacheManager.class);
                    assertThat(context).doesNotHaveBean(CaffeineCacheService.class);
                });
    }

    @Test
    void testAutoConfiguration_MissingCaffeineClass() {
        contextRunner
                .withPropertyValues("handthing.cache.type=caffeine")
                .withClassLoader(new FilteredClassLoader(com.github.benmanes.caffeine.cache.Caffeine.class))
                .run(context -> {
                    assertThat(context).doesNotHaveBean(CaffeineCacheManager.class);
                });
    }

    @Test
    void testCaffeineCacheProperties_Binding() {
        contextRunner
                .withPropertyValues(
                        "handthing.cache.type=caffeine",
                        "handthing.cache.caffeine.default-spec=initialCapacity=100,maximumSize=2000,expireAfterWrite=15m",
                        "handthing.cache.caffeine.default-ttl=PT20M",
                        "handthing.cache.caffeine.enable-stats=false",
                        "handthing.cache.caffeine.enable-soft-values=true",
                        "handthing.cache.caffeine.enable-weak-keys=true",
                        "handthing.cache.caffeine.caches.users.spec=maximumSize=500,expireAfterWrite=5m",
                        "handthing.cache.caffeine.caches.users.maximum-size=500",
                        "handthing.cache.caffeine.caches.users.expire-after-write=PT5M"
                )
                .run(context -> {
                    CaffeineCacheProperties properties = context.getBean(CaffeineCacheProperties.class);
                    assertThat(properties.getDefaultSpec()).isEqualTo("initialCapacity=100,maximumSize=2000,expireAfterWrite=15m");
                    assertThat(properties.getDefaultTtl().toMinutes()).isEqualTo(20);
                    assertThat(properties.isEnableStats()).isFalse();
                    assertThat(properties.isEnableSoftValues()).isTrue();
                    assertThat(properties.isEnableWeakKeys()).isTrue();
                    
                    CaffeineCacheProperties.CaffeineCacheSpec userSpec = properties.getCaches().get("users");
                    assertThat(userSpec).isNotNull();
                    assertThat(userSpec.getSpec()).isEqualTo("maximumSize=500,expireAfterWrite=5m");
                    assertThat(userSpec.getMaximumSize()).isEqualTo(500L);
                    assertThat(userSpec.getExpireAfterWrite().toMinutes()).isEqualTo(5);
                });
    }

    @Test
    void testCaffeineCacheManager_Creation() {
        contextRunner
                .withPropertyValues("handthing.cache.type=caffeine")
                .run(context -> {
                    CacheManager cacheManager = context.getBean(CacheManager.class);
                    assertThat(cacheManager).isInstanceOf(CaffeineCacheManager.class);
                    
                    // 测试缓存创建
                    var cache = cacheManager.getCache("testCache");
                    assertThat(cache).isNotNull();
                    assertThat(cache.getName()).isEqualTo("testCache");
                });
    }

    @Test
    void testCaffeineCacheService_Creation() {
        contextRunner
                .withPropertyValues("handthing.cache.type=caffeine")
                .run(context -> {
                    CacheService cacheService = context.getBean(CacheService.class);
                    assertThat(cacheService).isInstanceOf(CaffeineCacheService.class);
                    
                    // 测试基本功能
                    cacheService.put("testCache", "testKey", "testValue");
                    String result = cacheService.get("testCache", "testKey");
                    assertThat(result).isEqualTo("testValue");
                });
    }

    /**
     * 过滤类加载器，用于模拟缺少特定类的情况
     */
    private static class FilteredClassLoader extends ClassLoader {
        private final Class<?> filteredClass;

        public FilteredClassLoader(Class<?> filteredClass) {
            super(CaffeineCacheAutoConfigurationTest.class.getClassLoader());
            this.filteredClass = filteredClass;
        }

        @Override
        protected Class<?> loadClass(String name, boolean resolve) throws ClassNotFoundException {
            if (filteredClass.getName().equals(name)) {
                throw new ClassNotFoundException();
            }
            return super.loadClass(name, resolve);
        }
    }
}