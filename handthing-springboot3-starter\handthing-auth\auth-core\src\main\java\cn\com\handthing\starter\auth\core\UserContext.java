package cn.com.handthing.starter.auth.core;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 用户上下文接口
 * <p>
 * 定义用户上下文的核心接口，用于在请求处理过程中传递用户信息。
 * 提供用户信息、认证信息、会话信息等的访问接口。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
public interface UserContext {

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    UserInfo getUserInfo();

    /**
     * 获取用户ID
     *
     * @return 用户ID
     */
    default String getUserId() {
        UserInfo userInfo = getUserInfo();
        return userInfo != null ? userInfo.getUserId() : null;
    }

    /**
     * 获取用户名
     *
     * @return 用户名
     */
    default String getUsername() {
        UserInfo userInfo = getUserInfo();
        return userInfo != null ? userInfo.getUsername() : null;
    }

    /**
     * 获取访问令牌
     *
     * @return 访问令牌
     */
    String getAccessToken();

    /**
     * 获取刷新令牌
     *
     * @return 刷新令牌
     */
    String getRefreshToken();

    /**
     * 获取令牌类型
     *
     * @return 令牌类型
     */
    String getTokenType();

    /**
     * 获取授权范围
     *
     * @return 授权范围
     */
    String getScope();

    /**
     * 获取授权类型
     *
     * @return 授权类型
     */
    GrantType getGrantType();

    /**
     * 获取客户端ID
     *
     * @return 客户端ID
     */
    String getClientId();

    /**
     * 获取会话ID
     *
     * @return 会话ID
     */
    String getSessionId();

    /**
     * 获取请求IP地址
     *
     * @return IP地址
     */
    String getIpAddress();

    /**
     * 获取用户代理
     *
     * @return 用户代理
     */
    String getUserAgent();

    /**
     * 获取设备ID
     *
     * @return 设备ID
     */
    String getDeviceId();

    /**
     * 获取认证时间
     *
     * @return 认证时间
     */
    LocalDateTime getAuthenticationTime();

    /**
     * 获取令牌过期时间
     *
     * @return 过期时间
     */
    LocalDateTime getExpirationTime();

    /**
     * 获取扩展属性
     *
     * @return 扩展属性
     */
    Map<String, Object> getAttributes();

    /**
     * 获取扩展属性
     *
     * @param key 属性键
     * @return 属性值
     */
    Object getAttribute(String key);

    /**
     * 获取扩展属性（指定类型）
     *
     * @param key   属性键
     * @param clazz 属性类型
     * @param <T>   泛型类型
     * @return 属性值
     */
    <T> T getAttribute(String key, Class<T> clazz);

    /**
     * 判断是否已认证
     *
     * @return 如果已认证返回true，否则返回false
     */
    boolean isAuthenticated();

    /**
     * 判断令牌是否过期
     *
     * @return 如果过期返回true，否则返回false
     */
    boolean isExpired();

    /**
     * 判断用户是否有指定角色
     *
     * @param role 角色名称
     * @return 如果有指定角色返回true，否则返回false
     */
    default boolean hasRole(String role) {
        UserInfo userInfo = getUserInfo();
        return userInfo != null && userInfo.hasRole(role);
    }

    /**
     * 判断用户是否有指定权限
     *
     * @param permission 权限名称
     * @return 如果有指定权限返回true，否则返回false
     */
    default boolean hasPermission(String permission) {
        UserInfo userInfo = getUserInfo();
        return userInfo != null && userInfo.hasPermission(permission);
    }

    /**
     * 获取用户显示名称
     *
     * @return 显示名称
     */
    default String getDisplayName() {
        UserInfo userInfo = getUserInfo();
        return userInfo != null ? userInfo.getDisplayName() : null;
    }
}
