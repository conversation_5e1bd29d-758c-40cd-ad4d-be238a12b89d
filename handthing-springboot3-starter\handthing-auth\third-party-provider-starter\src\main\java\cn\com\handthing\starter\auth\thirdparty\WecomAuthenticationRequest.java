package cn.com.handthing.starter.auth.thirdparty;

import cn.com.handthing.starter.auth.core.AuthenticationRequest;
import cn.com.handthing.starter.auth.core.GrantType;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 企业微信认证请求
 * <p>
 * 用于企业微信OAuth2认证的请求对象，包含授权码、应用信息等认证信息。
 * 支持企业微信扫码登录和网页授权登录。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WecomAuthenticationRequest extends AuthenticationRequest {

    /**
     * 企业微信授权码
     */
    private String code;

    /**
     * 企业ID
     */
    private String corpId;

    /**
     * 应用ID
     */
    private String agentId;

    /**
     * 应用密钥
     */
    private String corpSecret;

    /**
     * 重定向URI
     */
    private String redirectUri;

    /**
     * 状态参数
     */
    private String state;

    /**
     * 授权类型（web-网页授权，scan-扫码登录）
     */
    private String authType = "web";

    /**
     * 是否自动注册（如果用户不存在）
     */
    private Boolean autoRegister = true;

    /**
     * 默认构造函数
     */
    public WecomAuthenticationRequest() {
        super(GrantType.WECOM);
    }

    /**
     * 构造函数
     *
     * @param code   授权码
     * @param corpId 企业ID
     */
    public WecomAuthenticationRequest(String code, String corpId) {
        super(GrantType.WECOM);
        this.code = code;
        this.corpId = corpId;
    }

    /**
     * 构造函数
     *
     * @param code       授权码
     * @param corpId     企业ID
     * @param agentId    应用ID
     * @param corpSecret 应用密钥
     */
    public WecomAuthenticationRequest(String code, String corpId, String agentId, String corpSecret) {
        super(GrantType.WECOM);
        this.code = code;
        this.corpId = corpId;
        this.agentId = agentId;
        this.corpSecret = corpSecret;
    }

    @Override
    public String getAuthenticationIdentifier() {
        return corpId + ":" + agentId;
    }

    @Override
    public Object getCredentials() {
        return code;
    }

    @Override
    public boolean isValid() {
        return super.isValid() && 
               code != null && !code.trim().isEmpty() &&
               corpId != null && !corpId.trim().isEmpty();
    }

    /**
     * 判断是否为网页授权类型
     *
     * @return 如果是网页授权返回true，否则返回false
     */
    public boolean isWebAuth() {
        return "web".equals(authType);
    }

    /**
     * 判断是否为扫码登录类型
     *
     * @return 如果是扫码登录返回true，否则返回false
     */
    public boolean isScanAuth() {
        return "scan".equals(authType);
    }

    /**
     * 获取完整的应用标识
     *
     * @return 应用标识
     */
    public String getAppIdentifier() {
        return corpId + "_" + agentId;
    }

    /**
     * 创建企业微信认证请求
     *
     * @param code   授权码
     * @param corpId 企业ID
     * @return 企业微信认证请求
     */
    public static WecomAuthenticationRequest of(String code, String corpId) {
        return new WecomAuthenticationRequest(code, corpId);
    }

    /**
     * 创建网页授权的企业微信认证请求
     *
     * @param code        授权码
     * @param corpId      企业ID
     * @param agentId     应用ID
     * @param corpSecret  应用密钥
     * @param redirectUri 重定向URI
     * @return 企业微信认证请求
     */
    public static WecomAuthenticationRequest forWebAuth(String code, String corpId, String agentId, 
                                                       String corpSecret, String redirectUri) {
        WecomAuthenticationRequest request = new WecomAuthenticationRequest(code, corpId, agentId, corpSecret);
        request.setAuthType("web");
        request.setRedirectUri(redirectUri);
        return request;
    }

    /**
     * 创建扫码登录的企业微信认证请求
     *
     * @param code       授权码
     * @param corpId     企业ID
     * @param agentId    应用ID
     * @param corpSecret 应用密钥
     * @return 企业微信认证请求
     */
    public static WecomAuthenticationRequest forScanAuth(String code, String corpId, String agentId, String corpSecret) {
        WecomAuthenticationRequest request = new WecomAuthenticationRequest(code, corpId, agentId, corpSecret);
        request.setAuthType("scan");
        return request;
    }

    /**
     * 创建自动注册的企业微信认证请求
     *
     * @param code   授权码
     * @param corpId 企业ID
     * @return 企业微信认证请求
     */
    public static WecomAuthenticationRequest withAutoRegister(String code, String corpId) {
        WecomAuthenticationRequest request = new WecomAuthenticationRequest(code, corpId);
        request.setAutoRegister(true);
        return request;
    }

    @Override
    public String toString() {
        return String.format("WecomAuthenticationRequest{corpId='%s', agentId='%s', authType='%s', autoRegister=%s}",
                corpId, agentId, authType, autoRegister);
    }
}
