package cn.com.handthing.starter.cache.level;

import cn.com.handthing.starter.cache.level.config.LevelCacheProperties;
import cn.com.handthing.starter.cache.level.sync.CacheSyncService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;

import java.util.Collection;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 多级缓存管理器
 * <p>
 * 协调L1(本地缓存)和L2(远程缓存)，实现多级缓存架构
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
public class LevelCacheManager implements CacheManager {

    private final LevelCacheProperties properties;
    private final CacheManager l1CacheManager;
    private final CacheManager l2CacheManager;
    private final CacheSyncService syncService;
    private final ConcurrentMap<String, Cache> cacheMap = new ConcurrentHashMap<>();

    /**
     * 构造函数
     *
     * @param properties      多级缓存配置属性
     * @param l1CacheManager  L1缓存管理器
     * @param l2CacheManager  L2缓存管理器
     * @param syncService     同步服务
     */
    public LevelCacheManager(LevelCacheProperties properties,
                            CacheManager l1CacheManager,
                            CacheManager l2CacheManager,
                            CacheSyncService syncService) {
        this.properties = properties;
        this.l1CacheManager = l1CacheManager;
        this.l2CacheManager = l2CacheManager;
        this.syncService = syncService;

        // 订阅同步消息
        if (syncService != null) {
            syncService.subscribe(message -> {
                Cache cache = cacheMap.get(message.getCacheName());
                if (cache instanceof LevelCache) {
                    LevelCache levelCache = (LevelCache) cache;
                    levelCache.handleSyncMessage(message.getKey(), message.getOperation().name());
                }
            });
        }

        log.info("LevelCacheManager initialized with L1: {}, L2: {}, sync: {}", 
                l1CacheManager.getClass().getSimpleName(),
                l2CacheManager.getClass().getSimpleName(),
                syncService != null ? "enabled" : "disabled");
    }

    @Override
    public Cache getCache(String name) {
        return cacheMap.computeIfAbsent(name, this::createCache);
    }

    @Override
    public Collection<String> getCacheNames() {
        return cacheMap.keySet();
    }

    /**
     * 创建多级缓存实例
     *
     * @param name 缓存名称
     * @return 多级缓存实例
     */
    private Cache createCache(String name) {
        // 获取L1和L2缓存
        Cache l1Cache = l1CacheManager.getCache(name);
        Cache l2Cache = l2CacheManager.getCache(name);

        if (l1Cache == null) {
            throw new IllegalStateException("L1 cache not found: " + name);
        }
        if (l2Cache == null) {
            throw new IllegalStateException("L2 cache not found: " + name);
        }

        // 检查是否启用同步
        boolean enableSync = isEnableSyncForCache(name);

        // 创建多级缓存
        LevelCache levelCache = new LevelCache(name, l1Cache, l2Cache, syncService, enableSync);

        log.info("Created level cache: {} with L1: {}, L2: {}, sync: {}", 
                name, 
                l1Cache.getClass().getSimpleName(),
                l2Cache.getClass().getSimpleName(),
                enableSync ? "enabled" : "disabled");

        return levelCache;
    }

    /**
     * 检查指定缓存是否启用同步
     *
     * @param cacheName 缓存名称
     * @return 是否启用同步
     */
    private boolean isEnableSyncForCache(String cacheName) {
        // 简化实现，直接使用全局同步配置
        return properties.getSync().isEnabled();
    }

    /**
     * 获取L1缓存管理器
     *
     * @return L1缓存管理器
     */
    public CacheManager getL1CacheManager() {
        return l1CacheManager;
    }

    /**
     * 获取L2缓存管理器
     *
     * @return L2缓存管理器
     */
    public CacheManager getL2CacheManager() {
        return l2CacheManager;
    }

    /**
     * 获取同步服务
     *
     * @return 同步服务
     */
    public CacheSyncService getSyncService() {
        return syncService;
    }

    /**
     * 清理所有缓存
     */
    public void clearAll() {
        for (Cache cache : cacheMap.values()) {
            cache.clear();
        }
        log.info("Cleared all level caches");
    }

    /**
     * 获取缓存统计信息
     *
     * @param cacheName 缓存名称
     * @return 缓存统计信息
     */
    public cn.com.handthing.starter.cache.CacheStats getCacheStats(String cacheName) {
        Cache cache = cacheMap.get(cacheName);
        if (cache instanceof LevelCache) {
            LevelCache levelCache = (LevelCache) cache;
            // 这里可以实现多级缓存的统计信息聚合
            // 暂时返回null，具体实现可以根据需要扩展
            log.debug("Level cache stats not implemented for cacheName: {}", cacheName);
        }
        return null;
    }
}