# HandThing DataLayer Spring Boot Starter 系列 - 任务清单

## 📋 项目概述

基于 `datalayer-requirement.md` 需求文档，开发模块化的数据层解决方案：

### 🏗️ 模块架构
```
id-spring-boot-starter (基础模块)
    ↓ (依赖)
datalayer-spring-boot-starter (核心模块)
    ↓ (依赖)
├── tenant-datalayer-spring-boot-starter (租户插件)
└── dataauth-datalayer-spring-boot-starter (权限插件)
```

### 📦 模块说明
- **基础模块**：`id-spring-boot-starter` - 分布式ID生成器，支持@IdSetter注解
- **核心模块**：`datalayer-spring-boot-starter` - 基础持久化能力，集成ID生成器
- **插件模块**：`tenant-datalayer-spring-boot-starter` - 多租户数据隔离
- **插件模块**：`dataauth-datalayer-spring-boot-starter` - 数据权限控制

## 🎯 开发原则

- **@author**: HandThing
- **@since**: V1.0.0
- **代码风格**: 使用JDK8语法，Spring命名规范，Lombok简化代码
- **设计模式**: 使用合适的设计模式，完整注释说明
- **依赖优先级**: JDK > Spring > Apache Commons > 自定义
- **测试要求**: 至少编译通过，必要时完成单元测试

## 📦 模块0: id-spring-boot-starter (ID生成器基础模块)

### 0.1 项目结构创建
- [ ] 创建Maven模块 `id-spring-boot-starter`
- [ ] 配置pom.xml依赖（Spring Boot、Lombok等）
- [ ] 创建包结构

### 0.2 核心注解
- [ ] **@IdSetter** - ID自动注入注解
  - 支持strategy属性指定生成策略
  - 支持字段类型自动匹配默认策略
  - 运行时保留，字段级别注解

### 0.3 ID生成器接口
- [ ] **IdGenerator<T>** - ID生成器策略接口
  - 泛型设计支持不同类型ID
  - generate()方法生成新ID
  - 支持策略模式扩展

### 0.4 ID生成器注册中心
- [ ] **IdGeneratorRegistry** - ID生成器注册管理
  - 维护策略名称到生成器的映射
  - 维护字段类型到默认生成器的映射
  - 提供getGenerator方法获取生成器
  - 自动收集Spring容器中的IdGenerator Bean

### 0.5 内置ID生成器实现
- [ ] **UuidGenerator** - UUID生成器
  - 实现IdGenerator<String>接口
  - 生成32位无连字符UUID
  - 注册为String类型默认生成器

- [ ] **SnowflakeIdGenerator** - 雪花算法生成器
  - 实现IdGenerator<Long>接口
  - 支持worker-id和datacenter-id配置
  - 注册为Long类型默认生成器
  - 线程安全实现

### 0.6 性能优化组件
- [ ] **PooledIdGenerator** - 池化装饰器
  - 装饰器模式包装其他生成器
  - 维护ID缓冲池（BlockingQueue）
  - 后台线程异步填充ID池
  - 支持池大小、阈值、批次大小配置

### 0.7 自动配置类
- [ ] **IdAutoConfiguration** - ID生成器自动配置
  - 自动装配内置生成器
  - 自动装配注册中心
  - 条件装配池化功能
  - 配置雪花算法参数

### 0.8 配置属性类
- [ ] **IdProperties** - ID生成器配置属性
  - 雪花算法worker-id和datacenter-id配置
  - 池化功能开关和参数配置
  - 默认策略配置

### 0.9 工具类和异常
- [ ] **IdUtils** - ID生成工具类
- [ ] **IdGenerationException** - ID生成异常类

## 📦 模块1: datalayer-spring-boot-starter (核心模块)

### 1.1 项目结构创建
- [ ] 创建Maven模块 `datalayer-spring-boot-starter`
- [ ] 配置pom.xml依赖：
  - **必须依赖**：id-spring-boot-starter（ID生成器）
  - **必须依赖**：mybatis-plus-boot-starter（MyBatis-Plus）
  - **可选依赖**：auth-starter（认证上下文，弱依赖）
- [ ] 创建包结构

### 1.2 基础实体类
- [ ] **BaseEntity** - 基础实体类
  - 字段：id, createBy, createTime, updateBy, updateTime, version, deleted
  - 不包含tenantId（由插件模块扩展）
  - 使用Lombok注解简化代码

### 1.3 基础Mapper和Service
- [ ] **BaseMapper<T>** - 基础Mapper接口
  - 继承MyBatis-Plus的BaseMapper
  - 提供基础CRUD操作
  
- [ ] **BaseService<M, T>** - 基础Service接口
  - 泛型设计：M为Mapper类型，T为实体类型
  - 提供基础业务操作方法
  
- [ ] **BaseServiceImpl<M, T>** - 基础Service实现类
  - 实现BaseService接口
  - 集成MyBatis-Plus的ServiceImpl

### 1.4 自动填充处理器
- [ ] **DatalayerMetaObjectHandler** - 元数据自动填充处理器
  - **集成@IdSetter注解处理**：
    - 扫描实体字段上的@IdSetter注解
    - 根据strategy属性获取对应的IdGenerator
    - 支持字段类型自动匹配默认生成器（String→UUID, Long→Snowflake）
    - 在insertFill时自动注入ID到标注字段
  - **集成AuthContextHolder**：
    - 从AuthContextHolder获取用户信息
    - 自动填充createBy, updateBy字段
  - **基础字段自动填充**：
    - 自动填充createTime, updateTime
    - 支持自定义填充逻辑
  - **扩展性设计**：
    - 支持插件扩展（为租户插件预留扩展点）
    - 提供钩子方法供子类重写

### 1.5 自动配置类
- [ ] **DatalayerAutoConfiguration** - 核心自动配置类
  - 自动装配MybatisPlusInterceptor
  - 内置PaginationInnerInterceptor（分页）
  - 内置OptimisticLockerInnerInterceptor（乐观锁）
  - 自动装配DatalayerMetaObjectHandler
  - 配置逻辑删除等基础功能

### 1.6 配置属性类
- [ ] **DatalayerProperties** - 配置属性类
  - 逻辑删除配置
  - 分页配置
  - 乐观锁配置
  - 其他基础配置项

### 1.7 工具类
- [ ] **DatalayerUtils** - 数据层工具类
  - 提供常用的数据操作工具方法
  - 分页参数处理
  - 查询条件构建等

### 1.8 异常处理
- [ ] **DatalayerException** - 数据层异常基类
- [ ] **DataNotFoundException** - 数据不存在异常
- [ ] **DataConflictException** - 数据冲突异常

## 📦 模块2: tenant-datalayer-spring-boot-starter (租户隔离插件)

### 2.1 项目结构创建
- [ ] 创建Maven模块 `tenant-datalayer-spring-boot-starter`
- [ ] 配置pom.xml依赖（依赖核心模块和auth-starter）
- [ ] 创建包结构

### 2.2 租户基础实体
- [ ] **TenantBaseEntity** - 租户基础实体类
  - 继承BaseEntity
  - 新增tenantId字段
  - 需要多租户能力的实体继承此类

### 2.3 租户拦截器
- [ ] **SaasTenantLineHandler** - 租户行处理器
  - 实现MyBatis-Plus的TenantLineHandler接口
  - getTenantId()方法从TenantContextHolder获取租户ID
  - 支持忽略表配置
  - 支持动态租户ID获取

### 2.4 租户元数据处理器
- [ ] **TenantMetaObjectHandler** - 租户元数据处理器
  - 扩展DatalayerMetaObjectHandler
  - 在insertFill时自动填充tenantId
  - 支持租户上下文集成

### 2.5 租户自动配置
- [ ] **TenantDatalayerAutoConfiguration** - 租户自动配置类
  - 条件装配：@ConditionalOnProperty("handthing.datalayer.tenant.enabled")
  - 向MybatisPlusInterceptor添加TenantLineInnerInterceptor
  - 增强DatalayerMetaObjectHandler支持租户填充
  - 集成TenantContextHolder

### 2.6 租户配置属性
- [ ] **TenantDatalayerProperties** - 租户配置属性类
  - 租户功能开关
  - 忽略表配置
  - 租户字段名配置
  - 其他租户相关配置

### 2.7 租户工具类
- [ ] **TenantDatalayerUtils** - 租户数据层工具类
  - 租户上下文操作
  - 租户数据查询工具
  - 跨租户操作支持

## 📦 模块3: dataauth-datalayer-spring-boot-starter (数据权限插件)

### 3.1 项目结构创建
- [ ] 创建Maven模块 `dataauth-datalayer-spring-boot-starter`
- [ ] 配置pom.xml依赖（依赖核心模块和auth-starter）
- [ ] 创建包结构

### 3.2 数据权限注解
- [ ] **@DataPermission** - 数据权限注解
  - 用于标记需要权限过滤的Mapper方法
  - 支持权限类型配置
  - 支持自定义权限规则

### 3.3 权限规则提供者
- [ ] **DataPermissionRuleProvider** - 权限规则提供者接口
  - 定义权限规则生成接口
  - 支持多种权限类型
  - 由主应用实现具体逻辑

- [ ] **DefaultDataPermissionRuleProvider** - 默认权限规则提供者
  - 提供基础的权限规则实现
  - 支持角色级权限
  - 支持部门级权限

### 3.4 数据权限拦截器
- [ ] **DataPermissionInterceptor** - 数据权限拦截器
  - 实现MyBatis-Plus的InnerInterceptor
  - 动态拼接SQL权限条件
  - 支持注解驱动的权限控制
  - 集成AuthContextHolder获取用户权限

### 3.5 权限自动配置
- [ ] **DataAuthDatalayerAutoConfiguration** - 数据权限自动配置类
  - 条件装配：@ConditionalOnProperty("handthing.datalayer.data-auth.enabled")
  - 向MybatisPlusInterceptor添加DataPermissionInterceptor
  - 自动装配权限规则提供者

### 3.6 权限配置属性
- [ ] **DataAuthDatalayerProperties** - 数据权限配置属性类
  - 数据权限功能开关
  - 权限类型配置
  - 默认权限规则配置

### 3.7 权限工具类
- [ ] **DataAuthUtils** - 数据权限工具类
  - 权限检查工具方法
  - SQL权限条件构建
  - 权限上下文操作

## 🧪 测试模块

### 4.0 ID生成器模块测试
- [ ] **IdGeneratorRegistryTest** - ID生成器注册中心测试
- [ ] **UuidGeneratorTest** - UUID生成器测试
- [ ] **SnowflakeIdGeneratorTest** - 雪花算法生成器测试
- [ ] **PooledIdGeneratorTest** - 池化生成器测试
- [ ] **IdAutoConfigurationTest** - ID自动配置测试
- [ ] **IdSetterAnnotationTest** - @IdSetter注解功能测试

### 4.1 核心模块测试
- [ ] **DatalayerAutoConfigurationTest** - 自动配置测试
- [ ] **BaseEntityTest** - 基础实体测试
- [ ] **BaseServiceTest** - 基础服务测试
- [ ] **DatalayerMetaObjectHandlerTest** - 元数据处理器测试

### 4.2 租户模块测试
- [ ] **TenantDatalayerAutoConfigurationTest** - 租户自动配置测试
- [ ] **TenantBaseEntityTest** - 租户实体测试
- [ ] **SaasTenantLineHandlerTest** - 租户拦截器测试
- [ ] **TenantMetaObjectHandlerTest** - 租户元数据处理器测试

### 4.3 权限模块测试
- [ ] **DataAuthDatalayerAutoConfigurationTest** - 权限自动配置测试
- [ ] **DataPermissionInterceptorTest** - 权限拦截器测试
- [ ] **DataPermissionRuleProviderTest** - 权限规则提供者测试

### 4.4 集成测试
- [ ] **DatalayerIntegrationTest** - 数据层集成测试
- [ ] **TenantDatalayerIntegrationTest** - 租户数据层集成测试
- [ ] **DataAuthDatalayerIntegrationTest** - 权限数据层集成测试

## 📚 文档和示例 ✅ 已完成

### 5.1 文档编写 ✅ 已完成
- [x] **README.md** - 项目主文档 ✅ 已完成
- [x] **API.md** - API接口文档 ✅ 已完成
- [x] **CONFIGURATION.md** - 配置说明文档 ✅ 已完成
- [x] **EXAMPLES.md** - 使用示例文档 ✅ 已完成
- [x] **QUICK_START.md** - 快速开始指南 ✅ 已完成

### 5.2 示例应用
- [ ] **id-example** - ID生成器示例（展示@IdSetter注解使用）
- [ ] **simple-example** - 简单应用示例（仅核心模块）
- [ ] **saas-example** - SaaS应用示例（核心+租户模块）
- [ ] **complex-example** - 复杂应用示例（核心+租户+权限模块）

### 5.3 使用示例代码
- [ ] **@IdSetter基础使用示例**：
```java
@Data
public class Product extends BaseEntity {
    @IdSetter  // 自动使用Long类型默认生成器（Snowflake）
    @TableId
    private Long id;

    @IdSetter  // 自动使用String类型默认生成器（UUID）
    private String productNo;

    private String name;
}
```

- [ ] **@IdSetter自定义策略示例**：
```java
@Component("orderNoGenerator")
public class OrderNoGenerator implements IdGenerator<String> {
    @Override
    public String generate() {
        return "ORD" + System.currentTimeMillis();
    }
}

@Data
public class Order extends BaseEntity {
    @IdSetter(strategy = "orderNoGenerator")  // 使用自定义生成器
    private String orderNo;
}
```

## 🔧 配置文件模板

### 6.1 Spring Boot配置
- [ ] **application.yml模板** - 完整配置示例：
```yaml
handthing:
  id:
    snowflake:
      worker-id: 1
      datacenter-id: 1
      pool:
        enabled: true
        size: 200
        threshold: 50
        batch-size: 100
  datalayer:
    tenant:
      enabled: true
    data-auth:
      enabled: true
```
- [ ] **application-dev.yml** - 开发环境配置
- [ ] **application-prod.yml** - 生产环境配置

### 6.2 MyBatis配置
- [ ] **mybatis-config.xml** - MyBatis配置文件
- [ ] **mapper配置示例** - Mapper XML配置示例

## ✅ 验收标准

### 7.1 功能验收
- [ ] 所有模块编译通过
- [ ] 单元测试覆盖率 > 80%
- [ ] 集成测试全部通过
- [ ] 示例应用正常运行

### 7.2 代码质量
- [ ] 代码符合规范要求
- [ ] 注释完整清晰
- [ ] 无明显性能问题
- [ ] 无安全漏洞

### 7.3 文档完整性
- [ ] API文档完整
- [ ] 配置说明清晰
- [ ] 使用示例丰富
- [ ] 部署指南详细

## 🚀 开发优先级

### 第零阶段：ID生成器基础模块
1. **id-spring-boot-starter** 核心功能
   - @IdSetter注解和IdGenerator接口
   - IdGeneratorRegistry注册中心
   - 内置生成器（UUID、Snowflake）
   - 池化优化和自动配置

### 第一阶段：数据层核心模块
1. **datalayer-spring-boot-starter** 基础功能
   - 基础实体、Mapper、Service
   - DatalayerMetaObjectHandler（集成@IdSetter）
   - 自动配置和基础功能

### 第二阶段：租户插件
1. **tenant-datalayer-spring-boot-starter**
   - 租户隔离功能
   - 租户自动配置
   - 租户元数据处理

### 第三阶段：权限插件
1. **dataauth-datalayer-spring-boot-starter**
   - 数据权限控制
   - 权限拦截器
   - 权限规则提供者

### 第四阶段：测试和文档
1. 完整的测试覆盖
2. 详细的文档编写
3. 示例应用开发

---

**任务清单创建时间**: 2025年7月29日  
**预计完成时间**: 根据开发进度调整  
**负责人**: HandThing Team
