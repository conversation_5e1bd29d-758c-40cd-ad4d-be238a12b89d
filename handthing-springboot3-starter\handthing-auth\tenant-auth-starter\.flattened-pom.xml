<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>cn.com.handthing.springboot3.starter</groupId>
    <artifactId>handthing-auth</artifactId>
    <version>1.0.0-SNAPSHOT</version>
  </parent>
  <artifactId>tenant-auth-starter</artifactId>
  <version>1.0.0-SNAPSHOT</version>
  <name>HandThing :: Tenant Auth Starter</name>
  <description>HandThing多租户认证启动器 - V3.0 SaaS增强版</description>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0</url>
    </license>
  </licenses>
  <dependencies>
    <dependency>
      <groupId>cn.com.handthing.springboot3.starter</groupId>
      <artifactId>handthing-core</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>cn.com.handthing.springboot3.starter</groupId>
      <artifactId>auth-core</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>cn.com.handthing.springboot3.starter</groupId>
      <artifactId>auth-spring-boot-starter</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>cn.com.handthing.springboot3.starter</groupId>
      <artifactId>cache-spring-boot-starter</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-data-jpa</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-validation</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-configuration-processor</artifactId>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.h2database</groupId>
      <artifactId>h2</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>
  <build>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <configuration>
          <skip>true</skip>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
