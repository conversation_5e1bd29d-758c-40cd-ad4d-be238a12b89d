# 💡 HandThing DataLayer 使用示例

本文档提供了 HandThing DataLayer Spring Boot Starter 系列的丰富使用示例，涵盖各种常见场景。

## 📋 目录

- [🔑 ID生成器示例](#-id生成器示例)
- [🏛️ 基础数据层示例](#️-基础数据层示例)
- [🏢 多租户应用示例](#-多租户应用示例)
- [🔐 数据权限控制示例](#-数据权限控制示例)
- [🔧 高级用法示例](#-高级用法示例)
- [🧪 测试示例](#-测试示例)

---

## 🔑 ID生成器示例

### 基础ID生成

```java
// 实体类
@Data
@TableName("users")
public class User {
    @IdSetter  // 自动生成Long类型ID (雪花算法)
    @TableId
    private Long id;
    
    @IdSetter  // 自动生成String类型编号 (UUID)
    private String userNo;
    
    private String username;
    private String email;
}

// 使用示例
@Service
public class UserService {
    
    @Autowired
    private UserMapper userMapper;
    
    public User createUser(String username, String email) {
        User user = new User();
        user.setUsername(username);
        user.setEmail(email);
        
        // 保存时会自动生成id和userNo
        userMapper.insert(user);
        
        System.out.println("Generated ID: " + user.getId());
        System.out.println("Generated UserNo: " + user.getUserNo());
        
        return user;
    }
}
```

### 自定义ID生成策略

```java
// 自定义ID生成器
@Component
public class CustomOrderNoGenerator implements IdGenerator<String> {
    
    private final AtomicLong counter = new AtomicLong(1);
    
    @Override
    public String generate() {
        String date = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        long sequence = counter.getAndIncrement();
        return String.format("ORD%s%06d", date, sequence);
    }
    
    @Override
    public String getName() {
        return "orderNo";
    }
    
    @Override
    public Class<String> getType() {
        return String.class;
    }
}

// 使用自定义生成器
@Data
@TableName("orders")
public class Order {
    @IdSetter
    @TableId
    private Long id;
    
    @IdSetter(strategy = "orderNo")  // 使用自定义生成器
    private String orderNo;
    
    private BigDecimal amount;
}
```

### 批量ID生成

```java
@Service
public class BatchService {
    
    public void createBatchUsers(List<String> usernames) {
        // 批量生成ID
        List<Long> ids = IdUtils.generateBatch("snowflake", usernames.size());
        List<String> userNos = IdUtils.generateBatch("uuid", usernames.size());
        
        List<User> users = new ArrayList<>();
        for (int i = 0; i < usernames.size(); i++) {
            User user = new User();
            user.setId(ids.get(i));
            user.setUserNo(userNos.get(i));
            user.setUsername(usernames.get(i));
            users.add(user);
        }
        
        // 批量插入
        userMapper.insertBatch(users);
    }
}
```

---

## 🏛️ 基础数据层示例

### 基础CRUD操作

```java
// 实体类
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("products")
public class Product extends BaseEntity {
    @IdSetter
    @TableId
    private Long id;
    
    @IdSetter
    private String productNo;
    
    private String name;
    private String category;
    private BigDecimal price;
    private Integer stock;
}

// Mapper
@Mapper
public interface ProductMapper extends BaseMapper<Product> {
    
    @Select("SELECT * FROM products WHERE category = #{category} AND price BETWEEN #{minPrice} AND #{maxPrice}")
    List<Product> findByPriceRange(@Param("category") String category, 
                                  @Param("minPrice") BigDecimal minPrice, 
                                  @Param("maxPrice") BigDecimal maxPrice);
}

// Service
@Service
public class ProductService extends BaseServiceImpl<ProductMapper, Product> {
    
    public Product createProduct(Product product) {
        // 自动填充ID、审计字段
        save(product);
        return product;
    }
    
    public Product updateProduct(Product product) {
        // 自动更新updateBy、updateTime、version
        updateById(product);
        return product;
    }
    
    public boolean deleteProduct(Long id) {
        // 逻辑删除
        return removeById(id);
    }
    
    public List<Product> findByCategory(String category) {
        QueryWrapper<Product> wrapper = new QueryWrapper<>();
        wrapper.eq("category", category);
        return list(wrapper);
    }
    
    public IPage<Product> findByPriceRange(String category, BigDecimal minPrice, BigDecimal maxPrice, int page, int size) {
        IPage<Product> pageParam = new Page<>(page, size);
        List<Product> products = baseMapper.findByPriceRange(category, minPrice, maxPrice);
        // 手动设置分页结果
        pageParam.setRecords(products);
        return pageParam;
    }
}
```

### 复杂查询示例

```java
@Service
public class ProductQueryService {
    
    @Autowired
    private ProductMapper productMapper;
    
    public List<Product> complexQuery(ProductQueryDTO queryDTO) {
        QueryWrapper<Product> wrapper = DatalayerUtils.createQueryWrapper();
        
        // 动态条件构建
        DatalayerUtils.eq(wrapper, "category", queryDTO.getCategory());
        DatalayerUtils.like(wrapper, "name", queryDTO.getName());
        DatalayerUtils.between(wrapper, "price", queryDTO.getMinPrice(), queryDTO.getMaxPrice());
        DatalayerUtils.in(wrapper, "status", queryDTO.getStatusList());
        
        // 排序
        wrapper.orderByDesc("create_time");
        
        return productMapper.selectList(wrapper);
    }
    
    public IPage<Product> pageQuery(ProductQueryDTO queryDTO, int page, int size) {
        // 创建安全分页对象
        IPage<Product> pageParam = DatalayerUtils.createSafePage((long) page, (long) size, 10, 1000);
        
        // 构建查询条件
        QueryWrapper<Product> wrapper = buildQueryWrapper(queryDTO);
        
        return productMapper.selectPage(pageParam, wrapper);
    }
    
    private QueryWrapper<Product> buildQueryWrapper(ProductQueryDTO queryDTO) {
        Map<String, Object> params = new HashMap<>();
        params.put("category", queryDTO.getCategory());
        params.put("name_like", queryDTO.getName());
        params.put("price_ge", queryDTO.getMinPrice());
        params.put("price_le", queryDTO.getMaxPrice());
        params.put("status_in", queryDTO.getStatusList());
        
        return DatalayerUtils.buildDynamicQuery(params);
    }
}
```

### 批处理示例

```java
@Service
public class ProductBatchService {

    @Autowired
    private ProductMapper productMapper;

    public void batchImport(List<Product> products) {
        // 分批处理，每批1000条
        DatalayerUtils.processBatch(products, 1000, (batch, batchNum) -> {
            System.out.println("Processing batch " + batchNum + " with " + batch.size() + " items");

            // 批量插入
            for (Product product : batch) {
                productMapper.insert(product);
            }
        });
    }

    public void batchUpdate(List<Product> products) {
        // 使用MyBatis-Plus的批量更新
        saveBatch(products, 500);
    }

    public void batchDelete(List<Long> ids) {
        // 分批删除
        DatalayerUtils.processBatch(ids, 100, (batch, batchNum) -> {
            removeByIds(batch);
        });
    }
}
```

---

## 🏢 多租户应用示例

### 多租户实体

```java
// 多租户订单实体
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("orders")
public class Order extends TenantBaseEntity {
    @IdSetter
    @TableId
    private Long id;

    @IdSetter
    private String orderNo;

    private Long customerId;
    private BigDecimal totalAmount;
    private String status;
}

// 多租户客户实体
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("customers")
public class Customer extends TenantBaseEntity {
    @IdSetter
    @TableId
    private Long id;

    private String name;
    private String email;
    private String phone;
}
```

### 多租户Service

```java
@Service
public class OrderService extends BaseServiceImpl<OrderMapper, Order> {

    public Order createOrder(Order order) {
        // 租户ID会自动填充
        save(order);
        return order;
    }

    public List<Order> findCurrentTenantOrders() {
        // 查询会自动添加租户条件
        return list();
    }

    public List<Order> findOrdersByStatus(String status) {
        QueryWrapper<Order> wrapper = new QueryWrapper<>();
        wrapper.eq("status", status);
        // 租户条件会自动添加
        return list(wrapper);
    }

    public Order findOrderById(Long id) {
        Order order = getById(id);

        // 验证订单是否属于当前租户
        if (order != null && !TenantDatalayerUtils.belongsToCurrentTenant(order)) {
            throw new IllegalAccessException("Order does not belong to current tenant");
        }

        return order;
    }
}
```

### 跨租户操作

```java
@Service
public class CrossTenantService {

    @Autowired
    private OrderService orderService;

    public void processOrdersForAllTenants(List<String> tenantIds) {
        // 跨租户执行操作
        TenantDatalayerUtils.forEachTenant(tenantIds, tenantId -> {
            System.out.println("Processing orders for tenant: " + tenantId);

            List<Order> orders = orderService.findCurrentTenantOrders();
            // 处理当前租户的订单
            processOrders(orders);
        });
    }

    public Map<String, Integer> getOrderCountByTenant(List<String> tenantIds) {
        // 跨租户收集结果
        return TenantDatalayerUtils.mapTenants(tenantIds, tenantId -> {
            List<Order> orders = orderService.findCurrentTenantOrders();
            return Map.entry(tenantId, orders.size());
        }).stream().collect(Collectors.toMap(
            Map.Entry::getKey,
            Map.Entry::getValue
        ));
    }

    private void processOrders(List<Order> orders) {
        // 处理订单逻辑
        for (Order order : orders) {
            if ("PENDING".equals(order.getStatus())) {
                order.setStatus("PROCESSING");
                orderService.updateById(order);
            }
        }
    }
}
```

---

## 🔐 数据权限控制示例

### 基础权限控制

```java
@Service
public class EmployeeService extends BaseServiceImpl<EmployeeMapper, Employee> {

    @DataPermission(type = DataPermissionType.DEPT)
    public List<Employee> findDeptEmployees() {
        // 自动添加部门权限条件：dept_id = '当前用户部门ID'
        return list();
    }

    @DataPermission(type = DataPermissionType.USER, field = "create_by")
    public List<Employee> findMyEmployees() {
        // 自动添加用户权限条件：create_by = '当前用户ID'
        return list();
    }

    @DataPermission(type = DataPermissionType.ROLE, field = "role_id")
    public List<Employee> findRoleEmployees() {
        // 自动添加角色权限条件：role_id IN ('当前用户角色列表')
        return list();
    }

    @IgnoreDataPermission(reason = "管理员查看所有员工")
    public List<Employee> findAllEmployees() {
        // 跳过权限检查，返回所有员工
        return list();
    }
}
```

### 高级权限控制

```java
@Service
public class SalesOrderService extends BaseServiceImpl<SalesOrderMapper, SalesOrder> {

    @DataPermission(
        type = DataPermissionType.DEPT,
        scope = PermissionScope.CURRENT_AND_CHILDREN,
        field = "sales_dept_id"
    )
    public List<SalesOrder> findDeptAndSubDeptOrders() {
        // 查询当前部门及子部门的订单
        return list();
    }

    @DataPermission(
        type = DataPermissionType.CUSTOM,
        expression = "(sales_dept_id = #{deptId} OR sales_person_id = #{userId}) AND status != 'DELETED'",
        onFailure = FailureStrategy.EXCEPTION
    )
    public List<SalesOrder> findAccessibleOrders() {
        // 自定义权限：部门订单或个人订单，且未删除
        return list();
    }

    @DataPermission(
        type = DataPermissionType.USER,
        field = "sales_person_id",
        enabled = "#{user.hasRole('SALES_PERSON')}"
    )
    public List<SalesOrder> findPersonalOrders() {
        // 只有销售人员角色才应用用户权限
        return list();
    }
}
```

### 权限组合使用

```java
@Service
public class DocumentService extends BaseServiceImpl<DocumentMapper, Document> {

    @DataPermission(type = DataPermissionType.DEPT, field = "owner_dept_id")
    public List<Document> findDeptDocuments() {
        return list();
    }

    @DataPermission(type = DataPermissionType.USER, field = "create_by")
    public List<Document> findMyDocuments() {
        return list();
    }

    public List<Document> findAccessibleDocuments() {
        // 手动组合多种权限
        List<Document> deptDocs = findDeptDocuments();
        List<Document> myDocs = findMyDocuments();

        // 合并并去重
        Set<Long> docIds = new HashSet<>();
        List<Document> result = new ArrayList<>();

        for (Document doc : deptDocs) {
            if (docIds.add(doc.getId())) {
                result.add(doc);
            }
        }

        for (Document doc : myDocs) {
            if (docIds.add(doc.getId())) {
                result.add(doc);
            }
        }

        return result;
    }
}
```

---

## 🔧 高级用法示例

### 自定义权限提供者

```java
@Component
public class ProjectPermissionProvider implements DataPermissionProvider {

    @Override
    public boolean supports(DataPermissionType type) {
        return DataPermissionType.CUSTOM == type;
    }

    @Override
    public String generatePermissionCondition(DataPermission permission, String mappedStatementId) {
        // 获取当前用户的项目权限
        List<String> projectIds = getCurrentUserProjectIds();

        if (projectIds.isEmpty()) {
            return "1 = 0"; // 无权限时拒绝访问
        }

        String field = permission.field().isEmpty() ? "project_id" : permission.field();
        String projectIdList = projectIds.stream()
            .map(id -> "'" + id + "'")
            .collect(Collectors.joining(", "));

        return String.format("%s IN (%s)", field, projectIdList);
    }

    private List<String> getCurrentUserProjectIds() {
        // 从用户上下文或数据库获取用户的项目权限
        return Arrays.asList("proj1", "proj2", "proj3");
    }

    @Override
    public int getOrder() {
        return 300; // 优先级
    }
}

// 使用自定义权限提供者
@Service
public class ProjectTaskService extends BaseServiceImpl<ProjectTaskMapper, ProjectTask> {

    @DataPermission(type = DataPermissionType.CUSTOM, field = "project_id")
    public List<ProjectTask> findProjectTasks() {
        // 使用自定义项目权限提供者
        return list();
    }
}
```

### 动态权限配置

```java
@Service
public class DynamicPermissionService {

    public List<Order> findOrdersWithDynamicPermission(String permissionType) {
        // 根据参数动态设置权限
        DataPermission.DataPermissionType type = DataPermission.DataPermissionType.valueOf(permissionType);

        // 创建权限上下文
        DataPermission permission = createDynamicPermission(type);
        DataPermissionContext context = DataPermissionContext.create(permission);
        DataPermissionContext.setCurrentContext(context);

        try {
            return orderService.list();
        } finally {
            DataPermissionContext.clearContext();
        }
    }

    private DataPermission createDynamicPermission(DataPermission.DataPermissionType type) {
        // 这里需要使用代理或其他方式创建DataPermission实例
        // 实际项目中可以使用注解处理器或反射
        return new DataPermission() {
            @Override
            public DataPermissionType type() { return type; }
            @Override
            public String field() { return ""; }
            @Override
            public String alias() { return ""; }
            @Override
            public String expression() { return ""; }
            @Override
            public String enabled() { return "true"; }
            @Override
            public FailureStrategy onFailure() { return FailureStrategy.FILTER; }
            @Override
            public boolean ignoreSuperAdmin() { return true; }
            @Override
            public PermissionScope scope() { return PermissionScope.CURRENT; }
            @Override
            public int cacheSeconds() { return 300; }
            @Override
            public Class<? extends Annotation> annotationType() { return DataPermission.class; }
        };
    }
}
```

---

## 🧪 测试示例

### 单元测试

```java
@SpringBootTest
class ProductServiceTest {

    @Autowired
    private ProductService productService;

    @Test
    @DisplayName("测试产品创建和ID自动生成")
    void testCreateProduct() {
        // Given
        Product product = new Product();
        product.setName("测试产品");
        product.setCategory("电子产品");
        product.setPrice(new BigDecimal("999.99"));
        product.setStock(100);

        // When
        Product created = productService.createProduct(product);

        // Then
        assertThat(created.getId()).isNotNull();
        assertThat(created.getProductNo()).isNotNull();
        assertThat(created.getCreateTime()).isNotNull();
        assertThat(created.getUpdateTime()).isNotNull();
        assertThat(created.getVersion()).isEqualTo(1L);
        assertThat(created.getDeleted()).isEqualTo(0);
    }

    @Test
    @DisplayName("测试产品更新和乐观锁")
    void testUpdateProduct() {
        // Given
        Product product = createTestProduct();
        Long originalVersion = product.getVersion();

        // When
        product.setPrice(new BigDecimal("1299.99"));
        Product updated = productService.updateProduct(product);

        // Then
        assertThat(updated.getVersion()).isEqualTo(originalVersion + 1);
        assertThat(updated.getUpdateTime()).isAfter(updated.getCreateTime());
    }

    @Test
    @DisplayName("测试产品逻辑删除")
    void testDeleteProduct() {
        // Given
        Product product = createTestProduct();

        // When
        boolean deleted = productService.deleteProduct(product.getId());

        // Then
        assertThat(deleted).isTrue();

        // 验证逻辑删除
        Product found = productService.getById(product.getId());
        assertThat(found).isNull(); // 逻辑删除后查询不到
    }

    private Product createTestProduct() {
        Product product = new Product();
        product.setName("测试产品");
        product.setCategory("测试分类");
        product.setPrice(new BigDecimal("99.99"));
        product.setStock(50);
        return productService.createProduct(product);
    }
}
```

### 多租户测试

```java
@SpringBootTest
class TenantOrderServiceTest {

    @Autowired
    private OrderService orderService;

    @Test
    @DisplayName("测试多租户数据隔离")
    void testTenantDataIsolation() {
        // 在租户1环境中创建订单
        Order order1 = TenantDatalayerUtils.supplyWithTenant("tenant1", () -> {
            Order order = new Order();
            order.setOrderNo("ORDER001");
            order.setTotalAmount(new BigDecimal("100.00"));
            return orderService.createOrder(order);
        });

        // 在租户2环境中创建订单
        Order order2 = TenantDatalayerUtils.supplyWithTenant("tenant2", () -> {
            Order order = new Order();
            order.setOrderNo("ORDER002");
            order.setTotalAmount(new BigDecimal("200.00"));
            return orderService.createOrder(order);
        });

        // 验证租户隔离
        List<Order> tenant1Orders = TenantDatalayerUtils.supplyWithTenant("tenant1", () -> {
            return orderService.list();
        });

        List<Order> tenant2Orders = TenantDatalayerUtils.supplyWithTenant("tenant2", () -> {
            return orderService.list();
        });

        assertThat(tenant1Orders).hasSize(1);
        assertThat(tenant1Orders.get(0).getId()).isEqualTo(order1.getId());

        assertThat(tenant2Orders).hasSize(1);
        assertThat(tenant2Orders.get(0).getId()).isEqualTo(order2.getId());
    }
}
```

### 数据权限测试

```java
@SpringBootTest
class DataPermissionTest {

    @Autowired
    private EmployeeService employeeService;

    @Test
    @DisplayName("测试部门权限控制")
    void testDeptPermission() {
        // 模拟用户上下文
        mockUserContext("user1", "dept1", Arrays.asList("EMPLOYEE"));

        // 创建测试数据
        createTestEmployees();

        // 测试部门权限
        List<Employee> deptEmployees = employeeService.findDeptEmployees();

        // 验证只返回当前部门的员工
        assertThat(deptEmployees).isNotEmpty();
        assertThat(deptEmployees).allMatch(emp -> "dept1".equals(emp.getDeptId()));
    }

    @Test
    @DisplayName("测试用户权限控制")
    void testUserPermission() {
        // 模拟用户上下文
        mockUserContext("user1", "dept1", Arrays.asList("MANAGER"));

        // 创建测试数据
        createTestEmployees();

        // 测试用户权限
        List<Employee> myEmployees = employeeService.findMyEmployees();

        // 验证只返回当前用户创建的员工
        assertThat(myEmployees).isNotEmpty();
        assertThat(myEmployees).allMatch(emp -> "user1".equals(emp.getCreateBy()));
    }

    private void mockUserContext(String userId, String deptId, List<String> roles) {
        // 模拟设置用户上下文
        DataPermissionContext.UserInfo userInfo = new DataPermissionContext.UserInfo();
        userInfo.setUserId(userId);
        userInfo.setDeptId(deptId);
        userInfo.setRoles(roles);

        DataPermissionContext context = new DataPermissionContext();
        context.setUserInfo(userInfo);
        DataPermissionContext.setCurrentContext(context);
    }

    private void createTestEmployees() {
        // 创建不同部门和创建人的测试员工数据
        // 实现省略...
    }
}
```

### 性能测试

```java
@SpringBootTest
class PerformanceTest {

    @Autowired
    private ProductService productService;

    @Test
    @DisplayName("测试ID生成性能")
    void testIdGenerationPerformance() {
        int count = 10000;
        long startTime = System.currentTimeMillis();

        List<Product> products = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            Product product = new Product();
            product.setName("Product " + i);
            product.setCategory("Category");
            product.setPrice(new BigDecimal("99.99"));
            product.setStock(100);
            products.add(product);
        }

        // 批量保存
        productService.saveBatch(products);

        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        System.out.println(String.format("Generated and saved %d products in %d ms", count, duration));
        System.out.println(String.format("Average: %.2f products/second", count * 1000.0 / duration));

        // 验证所有产品都有ID
        assertThat(products).allMatch(p -> p.getId() != null && p.getProductNo() != null);
    }
}
```

---

📝 **注意**：以上示例展示了框架的主要使用方式，实际使用时请根据具体业务需求进行调整。更多详细信息请参考 [API文档](./API.md) 和 [配置参考](./CONFIGURATION.md)。
```
```
```
