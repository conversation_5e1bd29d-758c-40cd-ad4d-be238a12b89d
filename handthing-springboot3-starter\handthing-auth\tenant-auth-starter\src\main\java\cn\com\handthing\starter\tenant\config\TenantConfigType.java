package cn.com.handthing.starter.tenant.config;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.List;
import java.util.Map;

/**
 * 租户配置类型枚举
 * <p>
 * 定义租户配置项的数据类型，支持类型转换和验证。
 * </p>
 *
 * <AUTHOR>
 * @since V3.0.0
 */
public enum TenantConfigType {

    /**
     * 字符串类型
     */
    STRING("STRING", "字符串", String.class),

    /**
     * 整数类型
     */
    INTEGER("INTEGER", "整数", Integer.class),

    /**
     * 长整数类型
     */
    LONG("LONG", "长整数", Long.class),

    /**
     * 布尔类型
     */
    BOOLEAN("BOOLEAN", "布尔值", Boolean.class),

    /**
     * 双精度浮点数类型
     */
    DOUBLE("DOUBLE", "双精度浮点数", Double.class),

    /**
     * JSON对象类型
     */
    JSON("JSON", "JSON对象", Map.class),

    /**
     * JSON数组类型
     */
    JSON_ARRAY("JSON_ARRAY", "JSON数组", List.class),

    /**
     * 密码类型（敏感信息）
     */
    PASSWORD("PASSWORD", "密码", String.class),

    /**
     * URL类型
     */
    URL("URL", "URL地址", String.class),

    /**
     * 邮箱类型
     */
    EMAIL("EMAIL", "邮箱地址", String.class);

    /**
     * 类型代码
     */
    private final String code;

    /**
     * 类型名称
     */
    private final String name;

    /**
     * Java类型
     */
    private final Class<?> javaType;

    /**
     * JSON对象映射器
     */
    private static final ObjectMapper objectMapper = new ObjectMapper();

    TenantConfigType(String code, String name, Class<?> javaType) {
        this.code = code;
        this.name = name;
        this.javaType = javaType;
    }

    /**
     * 获取类型代码
     *
     * @return 类型代码
     */
    public String getCode() {
        return code;
    }

    /**
     * 获取类型名称
     *
     * @return 类型名称
     */
    public String getName() {
        return name;
    }

    /**
     * 获取Java类型
     *
     * @return Java类型
     */
    public Class<?> getJavaType() {
        return javaType;
    }

    /**
     * 检查是否为敏感类型
     *
     * @return 如果是敏感类型返回true，否则返回false
     */
    public boolean isSensitive() {
        return this == PASSWORD;
    }

    /**
     * 转换配置值到指定类型
     *
     * @param value       配置值
     * @param targetClass 目标类型
     * @param <T>         泛型类型
     * @return 转换后的值
     */
    @SuppressWarnings("unchecked")
    public <T> T convertValue(String value, Class<T> targetClass) {
        if (value == null) {
            return null;
        }

        try {
            switch (this) {
                case STRING:
                case PASSWORD:
                case URL:
                case EMAIL:
                    return (T) value;

                case INTEGER:
                    return (T) Integer.valueOf(value);

                case LONG:
                    return (T) Long.valueOf(value);

                case BOOLEAN:
                    return (T) Boolean.valueOf(value);

                case DOUBLE:
                    return (T) Double.valueOf(value);

                case JSON:
                    if (targetClass == String.class) {
                        return (T) value;
                    } else if (targetClass == Map.class) {
                        return (T) objectMapper.readValue(value, new TypeReference<Map<String, Object>>() {});
                    } else {
                        return objectMapper.readValue(value, targetClass);
                    }

                case JSON_ARRAY:
                    if (targetClass == String.class) {
                        return (T) value;
                    } else if (targetClass == List.class) {
                        return (T) objectMapper.readValue(value, new TypeReference<List<Object>>() {});
                    } else {
                        return objectMapper.readValue(value, targetClass);
                    }

                default:
                    throw new IllegalArgumentException("Unsupported config type: " + this);
            }
        } catch (Exception e) {
            throw new IllegalArgumentException(
                    String.format("Cannot convert value '%s' from type %s to %s", 
                                value, this, targetClass.getSimpleName()), e);
        }
    }

    /**
     * 验证配置值是否符合类型要求
     *
     * @param value 配置值
     * @return 如果有效返回true，否则返回false
     */
    public boolean isValidValue(String value) {
        if (value == null) {
            return true; // null值被认为是有效的
        }

        try {
            switch (this) {
                case STRING:
                case PASSWORD:
                    return true; // 字符串总是有效的

                case INTEGER:
                    Integer.parseInt(value);
                    return true;

                case LONG:
                    Long.parseLong(value);
                    return true;

                case BOOLEAN:
                    String lowerValue = value.toLowerCase();
                    return "true".equals(lowerValue) || "false".equals(lowerValue);

                case DOUBLE:
                    Double.parseDouble(value);
                    return true;

                case JSON:
                    objectMapper.readValue(value, Map.class);
                    return true;

                case JSON_ARRAY:
                    objectMapper.readValue(value, List.class);
                    return true;

                case URL:
                    return value.matches("^https?://.*");

                case EMAIL:
                    return value.matches("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$");

                default:
                    return false;
            }
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 根据类型代码获取枚举值
     *
     * @param code 类型代码
     * @return 对应的枚举值，如果不存在则返回STRING
     */
    public static TenantConfigType fromCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return STRING;
        }

        for (TenantConfigType type : values()) {
            if (type.code.equalsIgnoreCase(code.trim())) {
                return type;
            }
        }

        return STRING;
    }

    /**
     * 根据Java类型推断配置类型
     *
     * @param clazz Java类型
     * @return 对应的配置类型
     */
    public static TenantConfigType fromClass(Class<?> clazz) {
        if (clazz == null) {
            return STRING;
        }

        if (clazz == String.class) {
            return STRING;
        } else if (clazz == Integer.class || clazz == int.class) {
            return INTEGER;
        } else if (clazz == Long.class || clazz == long.class) {
            return LONG;
        } else if (clazz == Boolean.class || clazz == boolean.class) {
            return BOOLEAN;
        } else if (clazz == Double.class || clazz == double.class || 
                   clazz == Float.class || clazz == float.class) {
            return DOUBLE;
        } else if (Map.class.isAssignableFrom(clazz)) {
            return JSON;
        } else if (List.class.isAssignableFrom(clazz)) {
            return JSON_ARRAY;
        } else {
            return STRING;
        }
    }

    /**
     * 获取默认值
     *
     * @return 默认值
     */
    public Object getDefaultValue() {
        switch (this) {
            case STRING:
            case PASSWORD:
            case URL:
            case EMAIL:
                return "";
            case INTEGER:
                return 0;
            case LONG:
                return 0L;
            case BOOLEAN:
                return false;
            case DOUBLE:
                return 0.0;
            case JSON:
                return "{}";
            case JSON_ARRAY:
                return "[]";
            default:
                return null;
        }
    }

    /**
     * 获取类型描述
     *
     * @return 类型描述
     */
    public String getDescription() {
        switch (this) {
            case STRING:
                return "字符串类型，支持任意文本内容";
            case INTEGER:
                return "整数类型，范围：-2,147,483,648 到 2,147,483,647";
            case LONG:
                return "长整数类型，范围：-9,223,372,036,854,775,808 到 9,223,372,036,854,775,807";
            case BOOLEAN:
                return "布尔类型，值为 true 或 false";
            case DOUBLE:
                return "双精度浮点数类型";
            case JSON:
                return "JSON对象类型，格式：{\"key\": \"value\"}";
            case JSON_ARRAY:
                return "JSON数组类型，格式：[\"item1\", \"item2\"]";
            case PASSWORD:
                return "密码类型，敏感信息，会在日志中脱敏显示";
            case URL:
                return "URL地址类型，必须以 http:// 或 https:// 开头";
            case EMAIL:
                return "邮箱地址类型，必须符合邮箱格式";
            default:
                return name;
        }
    }

    @Override
    public String toString() {
        return code;
    }
}
