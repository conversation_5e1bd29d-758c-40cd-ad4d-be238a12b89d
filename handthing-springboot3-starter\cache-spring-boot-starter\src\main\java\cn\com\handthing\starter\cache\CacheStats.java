package cn.com.handthing.starter.cache;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 缓存统计信息
 * <p>
 * 包含缓存的命中率、请求次数、缓存大小等统计数据
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CacheStats {

    /**
     * 缓存命中次数
     */
    private long hitCount;

    /**
     * 缓存未命中次数
     */
    private long missCount;

    /**
     * 缓存请求总次数
     */
    private long requestCount;

    /**
     * 缓存命中率 (0.0 - 1.0)
     */
    private double hitRate;

    /**
     * 缓存未命中率 (0.0 - 1.0)
     */
    private double missRate;

    /**
     * 缓存中的条目数量
     */
    private long size;

    /**
     * 缓存驱逐次数
     */
    private long evictionCount;

    /**
     * 平均加载时间（纳秒）
     */
    private double averageLoadTime;

    /**
     * 计算命中率
     *
     * @return 命中率
     */
    public double calculateHitRate() {
        if (requestCount == 0) {
            return 0.0;
        }
        return (double) hitCount / requestCount;
    }

    /**
     * 计算未命中率
     *
     * @return 未命中率
     */
    public double calculateMissRate() {
        if (requestCount == 0) {
            return 0.0;
        }
        return (double) missCount / requestCount;
    }

    /**
     * 获取总请求次数
     *
     * @return 总请求次数
     */
    public long getTotalRequestCount() {
        return hitCount + missCount;
    }
}