package cn.com.handthing.starter.cache;

import java.time.Duration;

/**
 * 缓存服务核心接口
 * <p>
 * 提供统一的缓存操作API，支持多种缓存实现的抽象层。
 * 相比Spring Cache接口，提供更简洁的编程式缓存访问能力。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
public interface CacheService {

    /**
     * 从指定缓存区域获取缓存值
     *
     * @param cacheName 缓存区域名称
     * @param key       缓存键
     * @param <T>       返回值类型
     * @return 缓存值，如果不存在则返回null
     */
    <T> T get(String cacheName, String key);

    /**
     * 从指定缓存区域获取缓存值，支持类型转换
     *
     * @param cacheName 缓存区域名称
     * @param key       缓存键
     * @param type      期望的返回类型
     * @param <T>       返回值类型
     * @return 缓存值，如果不存在则返回null
     */
    <T> T get(String cacheName, String key, Class<T> type);

    /**
     * 向指定缓存区域存储缓存值
     *
     * @param cacheName 缓存区域名称
     * @param key       缓存键
     * @param value     缓存值
     */
    void put(String cacheName, String key, Object value);

    /**
     * 向指定缓存区域存储缓存值，并设置过期时间
     *
     * @param cacheName 缓存区域名称
     * @param key       缓存键
     * @param value     缓存值
     * @param ttl       过期时间
     */
    void put(String cacheName, String key, Object value, Duration ttl);

    /**
     * 从指定缓存区域删除缓存项
     *
     * @param cacheName 缓存区域名称
     * @param key       缓存键
     */
    void evict(String cacheName, String key);

    /**
     * 清空指定缓存区域的所有缓存项
     *
     * @param cacheName 缓存区域名称
     */
    void clear(String cacheName);

    /**
     * 检查指定缓存区域是否存在指定键
     *
     * @param cacheName 缓存区域名称
     * @param key       缓存键
     * @return 如果存在返回true，否则返回false
     */
    boolean exists(String cacheName, String key);

    /**
     * 获取指定缓存区域的统计信息
     *
     * @param cacheName 缓存区域名称
     * @return 缓存统计信息，如果不支持则返回null
     */
    CacheStats getStats(String cacheName);
}