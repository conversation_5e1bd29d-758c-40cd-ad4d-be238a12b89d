package cn.com.handthing.starter.auth;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;

import java.util.concurrent.atomic.AtomicLong;

/**
 * 认证指标收集器
 * <p>
 * 收集认证相关的指标数据，用于监控和分析。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@RequiredArgsConstructor
public class AuthenticationMetrics {

    private final AuthProperties authProperties;

    private final AtomicLong totalAuthenticationAttempts = new AtomicLong(0);
    private final AtomicLong successfulAuthentications = new AtomicLong(0);
    private final AtomicLong failedAuthentications = new AtomicLong(0);
    private final AtomicLong authenticationErrors = new AtomicLong(0);

    /**
     * 监听认证开始事件
     *
     * @param event 认证开始事件
     */
    @EventListener
    public void handleAuthenticationStarted(AuthenticationBus.AuthenticationStartedEvent event) {
        totalAuthenticationAttempts.incrementAndGet();
    }

    /**
     * 监听认证成功事件
     *
     * @param event 认证成功事件
     */
    @EventListener
    public void handleAuthenticationSuccess(AuthenticationBus.AuthenticationSuccessEvent event) {
        successfulAuthentications.incrementAndGet();
    }

    /**
     * 监听认证失败事件
     *
     * @param event 认证失败事件
     */
    @EventListener
    public void handleAuthenticationFailed(AuthenticationBus.AuthenticationFailedEvent event) {
        failedAuthentications.incrementAndGet();
    }

    /**
     * 监听认证错误事件
     *
     * @param event 认证错误事件
     */
    @EventListener
    public void handleAuthenticationError(AuthenticationBus.AuthenticationErrorEvent event) {
        authenticationErrors.incrementAndGet();
    }

    /**
     * 获取总认证尝试次数
     *
     * @return 总认证尝试次数
     */
    public long getTotalAuthenticationAttempts() {
        return totalAuthenticationAttempts.get();
    }

    /**
     * 获取成功认证次数
     *
     * @return 成功认证次数
     */
    public long getSuccessfulAuthentications() {
        return successfulAuthentications.get();
    }

    /**
     * 获取失败认证次数
     *
     * @return 失败认证次数
     */
    public long getFailedAuthentications() {
        return failedAuthentications.get();
    }

    /**
     * 获取认证错误次数
     *
     * @return 认证错误次数
     */
    public long getAuthenticationErrors() {
        return authenticationErrors.get();
    }

    /**
     * 获取认证成功率
     *
     * @return 认证成功率（百分比）
     */
    public double getSuccessRate() {
        long total = totalAuthenticationAttempts.get();
        if (total == 0) {
            return 0.0;
        }
        return (double) successfulAuthentications.get() / total * 100;
    }
}
