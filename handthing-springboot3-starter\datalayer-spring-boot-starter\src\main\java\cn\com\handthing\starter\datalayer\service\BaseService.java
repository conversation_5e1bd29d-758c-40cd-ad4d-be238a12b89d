package cn.com.handthing.starter.datalayer.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * 基础Service接口
 * <p>
 * 提供基础的业务操作方法，泛型设计支持不同的Mapper和实体类型。
 * 所有Service接口都应该继承此接口。
 * </p>
 * 
 * <h3>使用示例：</h3>
 * <pre>
 * public interface ProductService extends BaseService&lt;ProductMapper, Product&gt; {
 *     // 自定义业务方法
 *     List&lt;Product&gt; findByCategory(String category);
 * }
 * </pre>
 *
 * @param <M> Mapper类型
 * @param <T> 实体类型
 * <AUTHOR>
 * @since V1.0.0
 */
public interface BaseService<M, T> {
    
    /**
     * 获取Mapper实例
     * 
     * @return Mapper实例
     */
    M getMapper();
    
    // ========================================
    // 查询操作
    // ========================================
    
    /**
     * 根据ID查询
     *
     * @param id 主键ID
     * @return 实体对象的Optional包装
     */
    Optional<T> getById(Serializable id);
    
    /**
     * 根据ID查询（直接返回实体）
     *
     * @param id 主键ID
     * @return 实体对象，如果不存在返回null
     */
    T findById(Serializable id);
    
    /**
     * 批量查询
     *
     * @param idList ID列表
     * @return 实体列表
     */
    List<T> listByIds(Collection<? extends Serializable> idList);
    
    /**
     * 查询所有记录
     *
     * @return 所有实体列表
     */
    List<T> list();
    
    /**
     * 条件查询
     *
     * @param queryWrapper 查询条件
     * @return 实体列表
     */
    List<T> list(Wrapper<T> queryWrapper);
    
    /**
     * 查询第一条记录
     *
     * @param queryWrapper 查询条件
     * @return 第一条记录的Optional包装
     */
    Optional<T> getOne(Wrapper<T> queryWrapper);
    
    /**
     * 分页查询
     *
     * @param page         分页参数
     * @param queryWrapper 查询条件
     * @return 分页结果
     */
    IPage<T> page(IPage<T> page, Wrapper<T> queryWrapper);
    
    /**
     * 分页查询（简化版）
     *
     * @param current 当前页
     * @param size    每页大小
     * @return 分页结果
     */
    IPage<T> page(long current, long size);
    
    /**
     * 分页查询（带条件）
     *
     * @param current      当前页
     * @param size         每页大小
     * @param queryWrapper 查询条件
     * @return 分页结果
     */
    IPage<T> page(long current, long size, Wrapper<T> queryWrapper);
    
    /**
     * 统计查询
     *
     * @return 总记录数
     */
    long count();
    
    /**
     * 条件统计
     *
     * @param queryWrapper 查询条件
     * @return 记录数
     */
    long count(Wrapper<T> queryWrapper);
    
    /**
     * 检查记录是否存在
     *
     * @param id 主键ID
     * @return 如果存在返回true，否则返回false
     */
    boolean exists(Serializable id);
    
    /**
     * 检查记录是否存在
     *
     * @param queryWrapper 查询条件
     * @return 如果存在返回true，否则返回false
     */
    boolean exists(Wrapper<T> queryWrapper);
    
    // ========================================
    // 保存操作
    // ========================================
    
    /**
     * 保存实体
     *
     * @param entity 实体对象
     * @return 如果保存成功返回true，否则返回false
     */
    boolean save(T entity);
    
    /**
     * 批量保存
     *
     * @param entityList 实体列表
     * @return 如果保存成功返回true，否则返回false
     */
    boolean saveBatch(Collection<T> entityList);
    
    /**
     * 批量保存（指定批次大小）
     *
     * @param entityList 实体列表
     * @param batchSize  批次大小
     * @return 如果保存成功返回true，否则返回false
     */
    boolean saveBatch(Collection<T> entityList, int batchSize);
    
    /**
     * 保存或更新
     *
     * @param entity 实体对象
     * @return 如果操作成功返回true，否则返回false
     */
    boolean saveOrUpdate(T entity);
    
    /**
     * 批量保存或更新
     *
     * @param entityList 实体列表
     * @return 如果操作成功返回true，否则返回false
     */
    boolean saveOrUpdateBatch(Collection<T> entityList);
    
    // ========================================
    // 更新操作
    // ========================================
    
    /**
     * 根据ID更新
     *
     * @param entity 实体对象
     * @return 如果更新成功返回true，否则返回false
     */
    boolean updateById(T entity);
    
    /**
     * 条件更新
     *
     * @param entity        实体对象
     * @param updateWrapper 更新条件
     * @return 如果更新成功返回true，否则返回false
     */
    boolean update(T entity, Wrapper<T> updateWrapper);
    
    /**
     * 批量更新
     *
     * @param entityList 实体列表
     * @return 如果更新成功返回true，否则返回false
     */
    boolean updateBatchById(Collection<T> entityList);
    
    /**
     * 批量更新（指定批次大小）
     *
     * @param entityList 实体列表
     * @param batchSize  批次大小
     * @return 如果更新成功返回true，否则返回false
     */
    boolean updateBatchById(Collection<T> entityList, int batchSize);
    
    // ========================================
    // 删除操作
    // ========================================
    
    /**
     * 根据ID删除
     *
     * @param id 主键ID
     * @return 如果删除成功返回true，否则返回false
     */
    boolean removeById(Serializable id);
    
    /**
     * 批量删除
     *
     * @param idList ID列表
     * @return 如果删除成功返回true，否则返回false
     */
    boolean removeByIds(Collection<? extends Serializable> idList);
    
    /**
     * 条件删除
     *
     * @param queryWrapper 删除条件
     * @return 如果删除成功返回true，否则返回false
     */
    boolean remove(Wrapper<T> queryWrapper);
    
    // ========================================
    // 业务操作
    // ========================================
    
    /**
     * 刷新实体（重新从数据库加载）
     *
     * @param entity 实体对象
     * @return 刷新后的实体
     */
    Optional<T> refresh(T entity);
    
    /**
     * 获取实体的审计信息
     *
     * @param id 主键ID
     * @return 审计信息字符串
     */
    String getAuditInfo(Serializable id);
    
    /**
     * 验证实体数据
     *
     * @param entity 实体对象
     * @return 验证结果，如果验证通过返回空列表
     */
    List<String> validate(T entity);
    
    /**
     * 预处理实体（在保存前调用）
     *
     * @param entity 实体对象
     */
    void preProcess(T entity);
    
    /**
     * 后处理实体（在保存后调用）
     *
     * @param entity 实体对象
     */
    void postProcess(T entity);
}
