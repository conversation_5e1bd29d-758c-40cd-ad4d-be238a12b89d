package cn.com.handthing.starter.crypto.core;

import cn.com.handthing.starter.crypto.exception.CryptoException;
import cn.com.handthing.starter.crypto.properties.CryptoProperties.KeyStoreConfig;
import org.springframework.context.ApplicationContext;
import org.springframework.util.Assert;

/**
 * 密钥提供者（KeyProvider）的工厂类。
 * <p>
 * 根据提供的 {@link KeyStoreConfig} 配置，创建对应类型的 {@link KeyProvider} 实例。
 * 这是策略模式的实现，将密钥的获取方式与使用方式解耦。
 *
 * <AUTHOR>
 * @since V1.0.0
 */
public class KeyProviderFactory {

    private final ApplicationContext applicationContext;

    /**
     * 构造一个新的 KeyProviderFactory。
     *
     * @param applicationContext Spring 应用上下文，用于获取可选的依赖（如 VaultOperations）
     */
    public KeyProviderFactory(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    /**
     * 根据配置创建 KeyProvider 实例。
     *
     * @param config 密钥来源配置
     * @return 对应配置类型的 KeyProvider 实例
     * @throws CryptoException 如果配置无效或创建失败
     */
    public KeyProvider create(KeyStoreConfig config) {
        Assert.notNull(config, "KeyStoreConfig cannot be null");
        Assert.notNull(config.getType(), "KeyStoreType cannot be null");

        switch (config.getType()) {
            case CONFIG:
                return new ConfigKeyProvider(config);
            case FILE:
                return new FileKeyProvider(config);
            case CLASSPATH:
                return new ClasspathKeyProvider(config);
            case ENV:
                return new EnvironmentKeyProvider(config);
            case VAULT:
                // VaultKeyProvider 需要 VaultOperations Bean，所以从 applicationContext 获取
                return new VaultKeyProvider(config, applicationContext);
            default:
                throw new CryptoException("Unsupported KeyStoreType: " + config.getType());
        }
    }
}