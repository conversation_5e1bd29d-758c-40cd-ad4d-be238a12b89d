package cn.com.handthing.starter.connector.toutiao;

import cn.com.handthing.starter.connector.PlatformType;
import cn.com.handthing.starter.connector.auth.AuthProvider;
import cn.com.handthing.starter.connector.auth.UnifiedAccessToken;
import cn.com.handthing.starter.connector.auth.UnifiedUserInfo;
import cn.com.handthing.starter.connector.exception.AuthException;
import cn.com.handthing.starter.connector.toutiao.config.ToutiaoConfig;
import cn.com.handthing.starter.connector.toutiao.model.ToutiaoAccessTokenResponse;
import cn.com.handthing.starter.connector.toutiao.model.ToutiaoUserInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 今日头条认证提供者
 * <p>
 * 实现今日头条OAuth2.0认证流程，支持内容创作者和企业用户的认证。
 * 提供统一的认证接口，处理授权码交换、Token刷新等操作。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "handthing.connector.toutiao", name = "enabled", havingValue = "true")
public class ToutiaoAuthProvider implements AuthProvider {

    private final ToutiaoConfig config;
    private final RestTemplate restTemplate;

    @Override
    public PlatformType getPlatformType() {
        return PlatformType.TOUTIAO;
    }

    @Override
    public String getAuthorizationUrl(String state, String redirectUri) {
        try {
            log.debug("Generating Toutiao authorization URL with state: {}, redirectUri: {}", state, redirectUri);

            // 使用配置的回调地址或传入的地址
            String effectiveRedirectUri = redirectUri != null ? redirectUri : config.getRedirectUri();
            if (effectiveRedirectUri == null || effectiveRedirectUri.trim().isEmpty()) {
                throw new AuthException("Redirect URI is required for Toutiao authorization");
            }

            // 构建今日头条授权URL
            return UriComponentsBuilder.fromHttpUrl(config.getEffectiveApiBaseUrl() + "/platform/oauth/connect")
                    .queryParam("client_key", config.getClientKey())
                    .queryParam("response_type", "code")
                    .queryParam("scope", config.getScope())
                    .queryParam("redirect_uri", URLEncoder.encode(effectiveRedirectUri, StandardCharsets.UTF_8))
                    .queryParam("state", state)
                    .build()
                    .toUriString();

        } catch (Exception e) {
            log.error("Failed to generate Toutiao authorization URL", e);
            throw new AuthException("Failed to generate authorization URL", e);
        }
    }

    @Override
    public UnifiedAccessToken exchangeToken(String code, String state, String redirectUri) {
        try {
            log.debug("Exchanging Toutiao authorization code for access token: {}", code);

            // 构建获取access_token的URL
            String tokenUrl = config.getEffectiveApiBaseUrl() + "/oauth/access_token/";

            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("client_key", config.getClientKey());
            requestBody.put("client_secret", config.getClientSecret());
            requestBody.put("code", code);
            requestBody.put("grant_type", "authorization_code");

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);

            // 发送请求获取access_token
            ResponseEntity<ToutiaoAccessTokenResponse> response = restTemplate.postForEntity(
                    tokenUrl, request, ToutiaoAccessTokenResponse.class);

            ToutiaoAccessTokenResponse tokenResponse = response.getBody();
            if (tokenResponse == null) {
                throw new AuthException("Empty response from Toutiao token endpoint");
            }

            if (tokenResponse.getErrorCode() != 0) {
                throw new AuthException("Toutiao token exchange failed: " + tokenResponse.getDescription());
            }

            // 转换为统一的访问令牌格式
            Map<String, Object> extraInfo = new HashMap<>();
            extraInfo.put("openId", tokenResponse.getData().getOpenId());
            extraInfo.put("scope", tokenResponse.getData().getScope());
            
            return UnifiedAccessToken.builder()
                    .accessToken(tokenResponse.getData().getAccessToken())
                    .refreshToken(tokenResponse.getData().getRefreshToken())
                    .expiresIn(tokenResponse.getData().getExpiresIn())
                    .scope(tokenResponse.getData().getScope())
                    .platform(PlatformType.TOUTIAO)
                    .userId(tokenResponse.getData().getOpenId())
                    .extraInfo(extraInfo)
                    .createdAt(LocalDateTime.now())
                    .build();

        } catch (Exception e) {
            log.error("Failed to exchange Toutiao authorization code for token", e);
            throw new AuthException("Failed to exchange code for token", e);
        }
    }

    @Override
    public UnifiedAccessToken refreshToken(String refreshToken) {
        try {
            log.debug("Refreshing Toutiao access token");

            // 构建刷新token的URL
            String refreshUrl = config.getEffectiveApiBaseUrl() + "/oauth/refresh_token/";

            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("client_key", config.getClientKey());
            requestBody.put("client_secret", config.getClientSecret());
            requestBody.put("refresh_token", refreshToken);
            requestBody.put("grant_type", "refresh_token");

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);

            // 发送请求刷新token
            ResponseEntity<ToutiaoAccessTokenResponse> response = restTemplate.postForEntity(
                    refreshUrl, request, ToutiaoAccessTokenResponse.class);

            ToutiaoAccessTokenResponse tokenResponse = response.getBody();
            if (tokenResponse == null) {
                throw new AuthException("Empty response from Toutiao refresh token endpoint");
            }

            if (tokenResponse.getErrorCode() != 0) {
                throw new AuthException("Toutiao token refresh failed: " + tokenResponse.getDescription());
            }

            // 转换为统一的访问令牌格式
            Map<String, Object> extraInfo = new HashMap<>();
            extraInfo.put("openId", tokenResponse.getData().getOpenId());
            extraInfo.put("scope", tokenResponse.getData().getScope());
            
            return UnifiedAccessToken.builder()
                    .accessToken(tokenResponse.getData().getAccessToken())
                    .refreshToken(tokenResponse.getData().getRefreshToken())
                    .expiresIn(tokenResponse.getData().getExpiresIn())
                    .scope(tokenResponse.getData().getScope())
                    .platform(PlatformType.TOUTIAO)
                    .userId(tokenResponse.getData().getOpenId())
                    .extraInfo(extraInfo)
                    .createdAt(LocalDateTime.now())
                    .build();

        } catch (Exception e) {
            log.error("Failed to refresh Toutiao access token", e);
            throw new AuthException("Failed to refresh token", e);
        }
    }

    @Override
    public UnifiedUserInfo getUserInfo(String accessToken) {
        try {
            log.debug("Getting Toutiao user info with access token");

            // 构建获取用户信息的URL
            String userInfoUrl = UriComponentsBuilder.fromHttpUrl(config.getEffectiveApiBaseUrl() + "/oauth/userinfo/")
                    .queryParam("access_token", accessToken)
                    .build()
                    .toUriString();

            // 发送请求获取用户信息
            ResponseEntity<ToutiaoUserInfo> response = restTemplate.getForEntity(
                    userInfoUrl, ToutiaoUserInfo.class);

            ToutiaoUserInfo userInfo = response.getBody();
            if (userInfo == null) {
                throw new AuthException("Empty response from Toutiao user info endpoint");
            }

            if (userInfo.getErrorCode() != 0) {
                throw new AuthException("Toutiao get user info failed: " + userInfo.getDescription());
            }

            // 转换为统一的用户信息格式
            return UnifiedUserInfo.builder()
                    .openId(userInfo.getData().getOpenId())
                    .unionId(userInfo.getData().getUnionId())
                    .nickname(userInfo.getData().getNickname())
                    .avatar(userInfo.getData().getAvatar())
                    .gender(userInfo.getData().getGender())
                    .country(userInfo.getData().getCountry())
                    .province(userInfo.getData().getProvince())
                    .city(userInfo.getData().getCity())
                    .platform(PlatformType.TOUTIAO)
                    .build();

        } catch (Exception e) {
            log.error("Failed to get Toutiao user info", e);
            throw new AuthException("Failed to get user info", e);
        }
    }

    @Override
    public boolean validateToken(String accessToken) {
        try {
            log.debug("Validating Toutiao access token");

            // 通过获取用户信息来验证token
            getUserInfo(accessToken);
            return true;

        } catch (Exception e) {
            log.error("Failed to validate Toutiao access token", e);
            return false;
        }
    }
}
