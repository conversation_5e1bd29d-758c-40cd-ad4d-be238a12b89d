package cn.com.handthing.starter.httpclient.internal;

import cn.com.handthing.starter.httpclient.BodySpec;
import cn.com.handthing.starter.httpclient.crypto.ResponseDecryptor;
import cn.com.handthing.starter.httpclient.exception.HandthingHttpException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Duration;

/**
 * BodySpec 接口的 WebClient 实现。
 *
 * @param <T> 响应体的泛型类型
 * <AUTHOR>
 * @since V1.0.0
 */
public final class WebClientBodySpec<T> implements BodySpec<T> {

    private static final Logger log = LoggerFactory.getLogger(WebClientBodySpec.class);
    private static final Duration DEFAULT_BLOCK_TIMEOUT = Duration.ofSeconds(30);

    private final Mono<T> responseMono;
    private final ResponseDecryptor responseDecryptor;

    public WebClientBodySpec(Mono<T> responseMono, ResponseDecryptor responseDecryptor) {
        this.responseMono = responseMono;
        this.responseDecryptor = responseDecryptor;
    }

    @Override
    @Nullable
    public T block() {
        try {
            log.debug("Blocking to get response body");
            
            // 应用响应解密（如果启用）
            Mono<T> processedMono = applyResponseDecryption(responseMono);
            
            // 阻塞获取结果，使用默认超时
            return processedMono.block(DEFAULT_BLOCK_TIMEOUT);
            
        } catch (Exception e) {
            log.error("Failed to block and get response body", e);
            throw new HandthingHttpException("Failed to get response body", e);
        }
    }

    @Override
    @NonNull
    public Mono<T> mono() {
        log.debug("Returning reactive Mono for response body");

        // 应用响应解密（如果启用）
        return applyResponseDecryption(responseMono);
    }



    /**
     * 应用响应解密处理
     * 注意：WebClient 的响应解密比较复杂，这里简化处理
     * 实际的解密应该在 WebClientCryptoFilter 中完成
     */
    private Mono<T> applyResponseDecryption(Mono<T> originalMono) {
        // 对于 WebClient 实现，解密逻辑在过滤器中处理
        // 这里直接返回原始 Mono
        return originalMono;
    }
}
