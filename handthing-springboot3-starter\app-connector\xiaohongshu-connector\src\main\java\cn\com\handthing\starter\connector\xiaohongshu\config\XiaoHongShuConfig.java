package cn.com.handthing.starter.connector.xiaohongshu.config;

import cn.com.handthing.starter.connector.config.PlatformConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 小红书连接器配置
 * <p>
 * 支持小红书开放平台的配置，包括AppKey、AppSecret等认证信息。
 * 支持生活方式内容分享、社区互动等功能的配置。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ConfigurationProperties(prefix = "handthing.connector.xiaohongshu")
public class XiaoHongShuConfig extends PlatformConfig {

    /**
     * 小红书应用Key (App Key)
     */
    private String appKey;

    /**
     * 小红书应用密钥 (App Secret)
     */
    private String appSecret;

    /**
     * 应用类型
     * personal: 个人开发者
     * enterprise: 企业开发者
     * brand: 品牌方
     */
    private String appType = "personal";

    /**
     * 是否为品牌方模式
     */
    private boolean brandMode = false;

    /**
     * 是否启用内容发布功能
     */
    private boolean contentPublishEnabled = true;

    /**
     * 是否启用社区互动功能
     */
    private boolean communityEnabled = true;

    /**
     * 是否启用数据分析功能
     */
    private boolean analyticsEnabled = true;

    /**
     * 是否启用电商功能
     */
    private boolean ecommerceEnabled = false;

    /**
     * 小红书API基础URL
     */
    private String apiBaseUrl = "https://open.xiaohongshu.com";

    /**
     * 是否使用沙箱环境
     */
    private boolean sandboxMode = false;

    /**
     * 沙箱环境API基础URL
     */
    private String sandboxApiBaseUrl = "https://open-sandbox.xiaohongshu.com";

    /**
     * 授权范围
     */
    private String scope = "user_info,content.write,community.read,analytics.read";

    /**
     * 获取实际使用的API基础URL
     *
     * @return API基础URL
     */
    public String getEffectiveApiBaseUrl() {
        return sandboxMode ? sandboxApiBaseUrl : apiBaseUrl;
    }

    /**
     * 验证配置是否有效
     *
     * @return 如果配置有效返回true，否则返回false
     */
    @Override
    public boolean isValid() {
        if (!super.isValid()) {
            return false;
        }

        // 检查必需的配置项
        if (appKey == null || appKey.trim().isEmpty()) {
            return false;
        }

        if (appSecret == null || appSecret.trim().isEmpty()) {
            return false;
        }

        return true;
    }

    /**
     * 获取配置描述信息
     *
     * @return 配置描述
     */
    public String getConfigDescription() {
        return String.format("XiaoHongShu Config - AppKey: %s, AppType: %s, Brand: %s, Sandbox: %s",
                appKey, appType, brandMode, sandboxMode);
    }

    /**
     * 是否为个人开发者
     *
     * @return 如果是个人开发者返回true，否则返回false
     */
    public boolean isPersonalDeveloper() {
        return "personal".equals(appType);
    }

    /**
     * 是否为企业开发者
     *
     * @return 如果是企业开发者返回true，否则返回false
     */
    public boolean isEnterpriseDeveloper() {
        return "enterprise".equals(appType);
    }

    /**
     * 是否为品牌方
     *
     * @return 如果是品牌方返回true，否则返回false
     */
    public boolean isBrandDeveloper() {
        return "brand".equals(appType) || brandMode;
    }

    /**
     * 是否启用了内容发布功能
     *
     * @return 如果启用了内容发布功能返回true，否则返回false
     */
    public boolean isContentPublishAvailable() {
        return contentPublishEnabled && scope != null && scope.contains("content.write");
    }

    /**
     * 是否启用了社区互动功能
     *
     * @return 如果启用了社区互动功能返回true，否则返回false
     */
    public boolean isCommunityAvailable() {
        return communityEnabled && scope != null && scope.contains("community.read");
    }

    /**
     * 是否启用了数据分析功能
     *
     * @return 如果启用了数据分析功能返回true，否则返回false
     */
    public boolean isAnalyticsAvailable() {
        return analyticsEnabled && scope != null && scope.contains("analytics.read");
    }

    /**
     * 是否启用了电商功能
     *
     * @return 如果启用了电商功能返回true，否则返回false
     */
    public boolean isEcommerceAvailable() {
        return ecommerceEnabled && scope != null && scope.contains("ecommerce");
    }
}
