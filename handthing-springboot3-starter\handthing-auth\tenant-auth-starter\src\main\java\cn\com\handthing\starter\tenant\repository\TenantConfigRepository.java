package cn.com.handthing.starter.tenant.repository;

import cn.com.handthing.starter.tenant.config.TenantConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 租户配置数据访问层
 * <p>
 * 提供tenant_configs表的CRUD操作，支持按租户ID和配置键查询，支持批量配置操作。
 * </p>
 *
 * <AUTHOR>
 * @since V3.0.0
 */
@Repository
public interface TenantConfigRepository extends JpaRepository<TenantConfig, Long> {

    /**
     * 根据租户ID和配置键查找配置
     *
     * @param tenantId  租户ID
     * @param configKey 配置键
     * @return 配置项，如果不存在则返回空
     */
    Optional<TenantConfig> findByTenantIdAndConfigKey(String tenantId, String configKey);

    /**
     * 根据租户ID查找所有配置
     *
     * @param tenantId 租户ID
     * @return 配置项列表
     */
    List<TenantConfig> findByTenantId(String tenantId);

    /**
     * 根据租户ID查找所有启用的配置
     *
     * @param tenantId 租户ID
     * @param enabled  是否启用
     * @return 配置项列表
     */
    List<TenantConfig> findByTenantIdAndEnabled(String tenantId, Boolean enabled);

    /**
     * 根据配置键查找所有租户的配置
     *
     * @param configKey 配置键
     * @return 配置项列表
     */
    List<TenantConfig> findByConfigKey(String configKey);

    /**
     * 根据配置键查找所有启用的配置
     *
     * @param configKey 配置键
     * @param enabled   是否启用
     * @return 配置项列表
     */
    List<TenantConfig> findByConfigKeyAndEnabled(String configKey, Boolean enabled);

    /**
     * 检查租户是否存在
     *
     * @param tenantId 租户ID
     * @return 如果存在返回true，否则返回false
     */
    boolean existsByTenantId(String tenantId);

    /**
     * 检查租户配置是否存在
     *
     * @param tenantId  租户ID
     * @param configKey 配置键
     * @return 如果存在返回true，否则返回false
     */
    boolean existsByTenantIdAndConfigKey(String tenantId, String configKey);

    /**
     * 根据租户ID删除所有配置
     *
     * @param tenantId 租户ID
     * @return 删除的记录数
     */
    long deleteByTenantId(String tenantId);

    /**
     * 根据租户ID和配置键删除配置
     *
     * @param tenantId  租户ID
     * @param configKey 配置键
     * @return 删除的记录数
     */
    long deleteByTenantIdAndConfigKey(String tenantId, String configKey);

    /**
     * 获取所有不同的租户ID
     *
     * @return 租户ID列表
     */
    @Query("SELECT DISTINCT tc.tenantId FROM TenantConfig tc WHERE tc.enabled = true")
    List<String> findDistinctTenantIds();

    /**
     * 获取租户的配置数量
     *
     * @param tenantId 租户ID
     * @return 配置数量
     */
    long countByTenantId(String tenantId);

    /**
     * 获取租户的启用配置数量
     *
     * @param tenantId 租户ID
     * @param enabled  是否启用
     * @return 配置数量
     */
    long countByTenantIdAndEnabled(String tenantId, Boolean enabled);

    /**
     * 根据租户ID和配置键前缀查找配置
     *
     * @param tenantId     租户ID
     * @param keyPrefix    配置键前缀
     * @return 配置项列表
     */
    @Query("SELECT tc FROM TenantConfig tc WHERE tc.tenantId = :tenantId AND tc.configKey LIKE :keyPrefix% AND tc.enabled = true")
    List<TenantConfig> findByTenantIdAndConfigKeyStartingWith(@Param("tenantId") String tenantId, 
                                                              @Param("keyPrefix") String keyPrefix);

    /**
     * 根据租户ID和配置键列表查找配置
     *
     * @param tenantId   租户ID
     * @param configKeys 配置键列表
     * @return 配置项列表
     */
    @Query("SELECT tc FROM TenantConfig tc WHERE tc.tenantId = :tenantId AND tc.configKey IN :configKeys AND tc.enabled = true")
    List<TenantConfig> findByTenantIdAndConfigKeyIn(@Param("tenantId") String tenantId, 
                                                    @Param("configKeys") List<String> configKeys);

    /**
     * 获取租户的敏感配置
     *
     * @param tenantId 租户ID
     * @return 敏感配置项列表
     */
    @Query("SELECT tc FROM TenantConfig tc WHERE tc.tenantId = :tenantId AND tc.sensitive = true AND tc.enabled = true")
    List<TenantConfig> findSensitiveConfigsByTenantId(@Param("tenantId") String tenantId);

    /**
     * 获取租户的非敏感配置
     *
     * @param tenantId 租户ID
     * @return 非敏感配置项列表
     */
    @Query("SELECT tc FROM TenantConfig tc WHERE tc.tenantId = :tenantId AND (tc.sensitive = false OR tc.sensitive IS NULL) AND tc.enabled = true")
    List<TenantConfig> findNonSensitiveConfigsByTenantId(@Param("tenantId") String tenantId);

    /**
     * 批量更新配置的启用状态
     *
     * @param tenantId 租户ID
     * @param enabled  是否启用
     * @return 更新的记录数
     */
    @Query("UPDATE TenantConfig tc SET tc.enabled = :enabled, tc.updatedAt = CURRENT_TIMESTAMP WHERE tc.tenantId = :tenantId")
    int updateEnabledByTenantId(@Param("tenantId") String tenantId, @Param("enabled") Boolean enabled);

    /**
     * 批量更新指定配置键的启用状态
     *
     * @param tenantId   租户ID
     * @param configKeys 配置键列表
     * @param enabled    是否启用
     * @return 更新的记录数
     */
    @Query("UPDATE TenantConfig tc SET tc.enabled = :enabled, tc.updatedAt = CURRENT_TIMESTAMP WHERE tc.tenantId = :tenantId AND tc.configKey IN :configKeys")
    int updateEnabledByTenantIdAndConfigKeys(@Param("tenantId") String tenantId, 
                                           @Param("configKeys") List<String> configKeys, 
                                           @Param("enabled") Boolean enabled);

    /**
     * 获取配置的最后更新时间
     *
     * @param tenantId  租户ID
     * @param configKey 配置键
     * @return 最后更新时间
     */
    @Query("SELECT tc.updatedAt FROM TenantConfig tc WHERE tc.tenantId = :tenantId AND tc.configKey = :configKey")
    Optional<java.time.LocalDateTime> findLastUpdatedTime(@Param("tenantId") String tenantId, 
                                                          @Param("configKey") String configKey);

    /**
     * 获取租户配置的最后更新时间
     *
     * @param tenantId 租户ID
     * @return 最后更新时间
     */
    @Query("SELECT MAX(tc.updatedAt) FROM TenantConfig tc WHERE tc.tenantId = :tenantId")
    Optional<java.time.LocalDateTime> findLastUpdatedTimeByTenantId(@Param("tenantId") String tenantId);

    /**
     * 查找过期的配置（用于清理）
     *
     * @param beforeTime 时间阈值
     * @return 过期的配置项列表
     */
    @Query("SELECT tc FROM TenantConfig tc WHERE tc.updatedAt < :beforeTime AND tc.enabled = false")
    List<TenantConfig> findExpiredConfigs(@Param("beforeTime") java.time.LocalDateTime beforeTime);

    /**
     * 获取配置统计信息
     *
     * @param tenantId 租户ID
     * @return 统计信息：[总数, 启用数, 禁用数, 敏感数]
     */
    @Query("SELECT COUNT(tc), " +
           "SUM(CASE WHEN tc.enabled = true THEN 1 ELSE 0 END), " +
           "SUM(CASE WHEN tc.enabled = false THEN 1 ELSE 0 END), " +
           "SUM(CASE WHEN tc.sensitive = true THEN 1 ELSE 0 END) " +
           "FROM TenantConfig tc WHERE tc.tenantId = :tenantId")
    Object[] getConfigStatsByTenantId(@Param("tenantId") String tenantId);
}
