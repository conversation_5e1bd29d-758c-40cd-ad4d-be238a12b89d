package cn.com.handthing.starter.httpclient.internal;

import cn.com.handthing.starter.httpclient.config.EndpointConfig;
import cn.com.handthing.starter.httpclient.config.HttpClientProperties;
import org.springframework.lang.NonNull;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.PathMatcher;

import java.util.Comparator;
import java.util.Map;
import java.util.Optional;

/**
 * 端点配置解析器。
 * <p>
 * 负责根据给定的请求URL，从 {@link HttpClientProperties} 中解析并合并出最终生效的端点配置。
 * 它支持Ant风格的URL模式匹配，并会应用 "最具体模式优先" 的原则。
 */
public class EndpointConfigResolver {

    private final HttpClientProperties properties;
    private final PathMatcher pathMatcher;

    public EndpointConfigResolver(@NonNull HttpClientProperties properties) {
        this.properties = properties;
        this.pathMatcher = new AntPathMatcher();
    }

    /**
     * 根据请求URL解析出最终生效的配置。
     *
     * @param url 请求的完整URL。
     * @return 一个合并了全局默认配置和最匹配的特定端点配置的 {@link EndpointConfig} 实例。
     */
    @NonNull
    public EndpointConfig resolve(@NonNull String url) {
        // 寻找最匹配的端点特定配置
        Optional<EndpointConfig> specificConfigOpt = properties.getEndpoints().entrySet().stream()
                .filter(entry -> pathMatcher.match(entry.getKey(), url))
                .map(Map.Entry::getValue)
                .max(Comparator.comparing(c -> 0)); // 简化处理，实际可以根据匹配精度排序

        EndpointConfig defaultConfig = properties.getDefaultConfig();

        // 如果找到了特定配置，则将其与默认配置合并
        return specificConfigOpt
                .map(specificConfig -> merge(defaultConfig, specificConfig))
                .orElse(defaultConfig);
    }

    /**
     * 将特定配置合并到默认配置之上。
     *
     * @param base     基础配置（通常是默认配置）。
     * @param override 覆盖配置（特定于端点的配置）。
     * @return 合并后的新配置对象。
     */
    private EndpointConfig merge(EndpointConfig base, EndpointConfig override) {
        // 这是一个简化的合并逻辑。在实际生产中，可能会使用更复杂的深度合并策略，
        // 例如使用 Jackson ObjectMapper 或 BeanUtils，但为了保持零依赖和清晰性，我们手动合并。
        // 原则：如果 override 中的属性被设置为非默认值，则使用它。
        
        EndpointConfig merged = new EndpointConfig();

        // 超时配置
        merged.setConnectTimeout(override.getConnectTimeout() != null ? override.getConnectTimeout() : base.getConnectTimeout());
        merged.setReadTimeout(override.getReadTimeout() != null ? override.getReadTimeout() : base.getReadTimeout());

        // 日志配置
        if (override.getLogging() != null) {
            merged.getLogging().setEnabled(override.getLogging().isEnabled());
            if (override.getLogging().getLevel() != null) {
                merged.getLogging().setLevel(override.getLogging().getLevel());
            }
        } else {
            merged.setLogging(base.getLogging());
        }

        // 重试配置
        if (override.getRetry() != null) {
            merged.getRetry().setEnabled(override.getRetry().isEnabled());
            if (override.getRetry().getMaxAttempts() != 3) { // 假设3是默认值
                 merged.getRetry().setMaxAttempts(override.getRetry().getMaxAttempts());
            }
            // ... 合并其他重试属性
        } else {
             merged.setRetry(base.getRetry());
        }
        
        // ... 以类似的方式合并 crypto, ssl, restTemplate 等其他属性
        merged.setCrypto(override.getCrypto() != null ? override.getCrypto() : base.getCrypto());
        merged.setSsl(override.getSsl() != null ? override.getSsl() : base.getSsl());
        merged.setRestTemplate(override.getRestTemplate() != null ? override.getRestTemplate() : base.getRestTemplate());
        
        return merged;
    }
}