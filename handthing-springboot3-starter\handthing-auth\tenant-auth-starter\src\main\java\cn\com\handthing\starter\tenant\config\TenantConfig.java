package cn.com.handthing.starter.tenant.config;

import cn.com.handthing.starter.tenant.context.TenantStatus;
import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;

/**
 * 租户配置实体类
 * <p>
 * 对应tenant_configs表的实体模型，支持配置项的类型转换和验证。
 * 每个租户可以有独立的认证配置，支持分层配置读取。
 * </p>
 *
 * <AUTHOR>
 * @since V3.0.0
 */
@Entity
@Table(name = "tenant_configs", 
       uniqueConstraints = @UniqueConstraint(columnNames = {"tenant_id", "config_key"}),
       indexes = {
           @Index(name = "idx_tenant_id", columnList = "tenant_id"),
           @Index(name = "idx_config_key", columnList = "config_key")
       })
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TenantConfig {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 租户ID
     */
    @Column(name = "tenant_id", nullable = false, length = 64)
    private String tenantId;

    /**
     * 配置键
     */
    @Column(name = "config_key", nullable = false, length = 128)
    private String configKey;

    /**
     * 配置值
     */
    @Column(name = "config_value", columnDefinition = "TEXT")
    private String configValue;

    /**
     * 配置类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "config_type", length = 32)
    @Builder.Default
    private TenantConfigType configType = TenantConfigType.STRING;

    /**
     * 配置描述
     */
    @Column(name = "description", length = 255)
    private String description;

    /**
     * 是否启用
     */
    @Column(name = "enabled")
    @Builder.Default
    private Boolean enabled = true;

    /**
     * 是否敏感信息（用于日志脱敏）
     */
    @Column(name = "sensitive")
    @Builder.Default
    private Boolean sensitive = false;

    /**
     * 创建时间
     */
    @Column(name = "created_at", nullable = false, updatable = false)
    @Builder.Default
    private LocalDateTime createdAt = LocalDateTime.now();

    /**
     * 更新时间
     */
    @Column(name = "updated_at", nullable = false)
    @Builder.Default
    private LocalDateTime updatedAt = LocalDateTime.now();

    /**
     * 创建者
     */
    @Column(name = "created_by", length = 64)
    private String createdBy;

    /**
     * 更新者
     */
    @Column(name = "updated_by", length = 64)
    private String updatedBy;

    /**
     * 版本号（乐观锁）
     */
    @Version
    private Long version;

    /**
     * 构造函数（基本信息）
     *
     * @param tenantId    租户ID
     * @param configKey   配置键
     * @param configValue 配置值
     */
    public TenantConfig(String tenantId, String configKey, String configValue) {
        this.tenantId = tenantId;
        this.configKey = configKey;
        this.configValue = configValue;
        this.configType = TenantConfigType.STRING;
        this.enabled = true;
        this.sensitive = false;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 构造函数（指定类型）
     *
     * @param tenantId    租户ID
     * @param configKey   配置键
     * @param configValue 配置值
     * @param configType  配置类型
     */
    public TenantConfig(String tenantId, String configKey, String configValue, TenantConfigType configType) {
        this.tenantId = tenantId;
        this.configKey = configKey;
        this.configValue = configValue;
        this.configType = configType;
        this.enabled = true;
        this.sensitive = false;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 获取配置值（指定类型）
     *
     * @param clazz 目标类型
     * @param <T>   泛型类型
     * @return 转换后的配置值
     */
    @SuppressWarnings("unchecked")
    public <T> T getValue(Class<T> clazz) {
        if (configValue == null) {
            return null;
        }

        try {
            return configType.convertValue(configValue, clazz);
        } catch (Exception e) {
            throw new IllegalArgumentException(
                    String.format("Cannot convert config value '%s' to type %s for key %s", 
                                configValue, clazz.getSimpleName(), configKey), e);
        }
    }

    /**
     * 获取字符串值
     *
     * @return 字符串值
     */
    public String getStringValue() {
        return configValue;
    }

    /**
     * 获取整数值
     *
     * @return 整数值
     */
    public Integer getIntValue() {
        return getValue(Integer.class);
    }

    /**
     * 获取长整数值
     *
     * @return 长整数值
     */
    public Long getLongValue() {
        return getValue(Long.class);
    }

    /**
     * 获取布尔值
     *
     * @return 布尔值
     */
    public Boolean getBooleanValue() {
        return getValue(Boolean.class);
    }

    /**
     * 获取双精度浮点数值
     *
     * @return 双精度浮点数值
     */
    public Double getDoubleValue() {
        return getValue(Double.class);
    }

    /**
     * 设置配置值（自动推断类型）
     *
     * @param value 配置值
     */
    public void setValue(Object value) {
        if (value == null) {
            this.configValue = null;
            this.configType = TenantConfigType.STRING;
        } else {
            this.configValue = value.toString();
            this.configType = TenantConfigType.fromClass(value.getClass());
        }
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 设置配置值（指定类型）
     *
     * @param value      配置值
     * @param configType 配置类型
     */
    public void setValue(Object value, TenantConfigType configType) {
        this.configValue = value != null ? value.toString() : null;
        this.configType = configType;
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 检查配置是否有效
     *
     * @return 如果有效返回true，否则返回false
     */
    public boolean isValid() {
        return tenantId != null && !tenantId.trim().isEmpty() &&
               configKey != null && !configKey.trim().isEmpty() &&
               enabled != null && enabled;
    }

    /**
     * 检查配置是否为敏感信息
     *
     * @return 如果是敏感信息返回true，否则返回false
     */
    public boolean isSensitive() {
        return sensitive != null && sensitive;
    }

    /**
     * 获取脱敏后的配置值（用于日志）
     *
     * @return 脱敏后的配置值
     */
    public String getMaskedValue() {
        if (configValue == null) {
            return null;
        }

        if (isSensitive()) {
            if (configValue.length() <= 4) {
                return "****";
            } else {
                return configValue.substring(0, 2) + "****" + configValue.substring(configValue.length() - 2);
            }
        }

        return configValue;
    }

    /**
     * 更新时间戳
     */
    @PreUpdate
    protected void onUpdate() {
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 创建时间戳
     */
    @PrePersist
    protected void onCreate() {
        LocalDateTime now = LocalDateTime.now();
        if (this.createdAt == null) {
            this.createdAt = now;
        }
        this.updatedAt = now;
    }

    /**
     * 获取配置的完整键名（包含租户前缀）
     *
     * @return 完整键名
     */
    public String getFullKey() {
        return tenantId + "." + configKey;
    }

    /**
     * 复制配置（不包含ID和时间戳）
     *
     * @return 配置副本
     */
    public TenantConfig copy() {
        return TenantConfig.builder()
                .tenantId(this.tenantId)
                .configKey(this.configKey)
                .configValue(this.configValue)
                .configType(this.configType)
                .description(this.description)
                .enabled(this.enabled)
                .sensitive(this.sensitive)
                .createdBy(this.createdBy)
                .updatedBy(this.updatedBy)
                .build();
    }

    @Override
    public String toString() {
        return String.format("TenantConfig{tenantId='%s', key='%s', value='%s', type=%s, enabled=%s}", 
                           tenantId, configKey, getMaskedValue(), configType, enabled);
    }
}
