package cn.com.handthing.starter.id.generator.impl;

import cn.com.handthing.starter.id.exception.IdGenerationException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

/**
 * Snowflake ID生成器测试
 *
 * <AUTHOR>
 * @since V1.0.0
 */
class SnowflakeIdGeneratorTest {
    
    @Test
    @DisplayName("应该生成有效的Snowflake ID")
    void shouldGenerateValidSnowflakeId() {
        // Given
        SnowflakeIdGenerator generator = new SnowflakeIdGenerator(1L, 1L);
        
        // When
        Long id = generator.generate();
        
        // Then
        assertThat(id).isNotNull();
        assertThat(id).isPositive();
    }
    
    @Test
    @DisplayName("应该生成唯一的ID")
    void shouldGenerateUniqueIds() {
        // Given
        SnowflakeIdGenerator generator = new SnowflakeIdGenerator(1L, 1L);
        Set<Long> ids = new HashSet<>();
        int count = 1000;
        
        // When
        for (int i = 0; i < count; i++) {
            Long id = generator.generate();
            ids.add(id);
        }
        
        // Then
        assertThat(ids).hasSize(count);
    }
    
    @Test
    @DisplayName("应该生成递增的ID")
    void shouldGenerateIncreasingIds() {
        // Given
        SnowflakeIdGenerator generator = new SnowflakeIdGenerator(1L, 1L);
        
        // When
        Long id1 = generator.generate();
        Long id2 = generator.generate();
        Long id3 = generator.generate();
        
        // Then
        assertThat(id2).isGreaterThan(id1);
        assertThat(id3).isGreaterThan(id2);
    }
    
    @Test
    @DisplayName("应该支持并发生成")
    void shouldSupportConcurrentGeneration() throws InterruptedException {
        // Given
        SnowflakeIdGenerator generator = new SnowflakeIdGenerator(1L, 1L);
        int threadCount = 10;
        int idsPerThread = 100;
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        Set<Long> allIds = new HashSet<>();
        AtomicInteger errorCount = new AtomicInteger(0);
        
        // When
        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                try {
                    Set<Long> threadIds = new HashSet<>();
                    for (int j = 0; j < idsPerThread; j++) {
                        Long id = generator.generate();
                        threadIds.add(id);
                    }
                    
                    synchronized (allIds) {
                        allIds.addAll(threadIds);
                    }
                } catch (Exception e) {
                    errorCount.incrementAndGet();
                } finally {
                    latch.countDown();
                }
            });
        }
        
        latch.await(10, TimeUnit.SECONDS);
        executor.shutdown();
        
        // Then
        assertThat(errorCount.get()).isZero();
        assertThat(allIds).hasSize(threadCount * idsPerThread);
    }
    
    @Test
    @DisplayName("应该验证workerId范围")
    void shouldValidateWorkerIdRange() {
        // When & Then
        assertThatThrownBy(() -> new SnowflakeIdGenerator(-1L, 1L))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("Worker ID must be between 0 and 31");
        
        assertThatThrownBy(() -> new SnowflakeIdGenerator(32L, 1L))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("Worker ID must be between 0 and 31");
    }
    
    @Test
    @DisplayName("应该验证datacenterId范围")
    void shouldValidateDatacenterIdRange() {
        // When & Then
        assertThatThrownBy(() -> new SnowflakeIdGenerator(1L, -1L))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("Datacenter ID must be between 0 and 31");
        
        assertThatThrownBy(() -> new SnowflakeIdGenerator(1L, 32L))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("Datacenter ID must be between 0 and 31");
    }
    
    @Test
    @DisplayName("应该返回正确的ID类型")
    void shouldReturnCorrectIdType() {
        // Given
        SnowflakeIdGenerator generator = new SnowflakeIdGenerator(1L, 1L);
        
        // When & Then
        assertThat(generator.getIdType()).isEqualTo(Long.class);
    }
    
    @Test
    @DisplayName("应该返回正确的生成器名称")
    void shouldReturnCorrectGeneratorName() {
        // Given
        SnowflakeIdGenerator generator = new SnowflakeIdGenerator(1L, 1L);
        
        // When & Then
        assertThat(generator.getName()).isEqualTo("SnowflakeIdGenerator");
    }
    
    @Test
    @DisplayName("应该检查可用性")
    void shouldCheckAvailability() {
        // Given
        SnowflakeIdGenerator generator = new SnowflakeIdGenerator(1L, 1L);
        
        // When & Then
        assertThat(generator.isAvailable()).isTrue();
    }
    
    @Test
    @DisplayName("预热应该成功")
    void shouldWarmUpSuccessfully() {
        // Given
        SnowflakeIdGenerator generator = new SnowflakeIdGenerator(1L, 1L);
        
        // When & Then
        // 应该不抛出异常
        generator.warmUp();
    }
    
    @Test
    @DisplayName("应该能够解析Snowflake ID")
    void shouldParseSnowflakeId() {
        // Given
        SnowflakeIdGenerator generator = new SnowflakeIdGenerator(5L, 3L);
        Long id = generator.generate();
        
        // When
        SnowflakeIdGenerator.SnowflakeIdInfo info = generator.parseId(id);
        
        // Then
        assertThat(info).isNotNull();
        assertThat(info.getWorkerId()).isEqualTo(5L);
        assertThat(info.getDatacenterId()).isEqualTo(3L);
        assertThat(info.getTimestamp()).isPositive();
        assertThat(info.getSequence()).isGreaterThanOrEqualTo(0L);
    }
    
    @Test
    @DisplayName("应该返回配置信息")
    void shouldReturnConfigInfo() {
        // Given
        SnowflakeIdGenerator generator = new SnowflakeIdGenerator(5L, 3L);
        
        // When
        String configInfo = generator.getConfigInfo();
        
        // Then
        assertThat(configInfo).contains("workerId=5");
        assertThat(configInfo).contains("datacenterId=3");
    }
    
    @Test
    @DisplayName("toString应该返回配置信息")
    void shouldReturnConfigInfoInToString() {
        // Given
        SnowflakeIdGenerator generator = new SnowflakeIdGenerator(1L, 1L);
        
        // When & Then
        assertThat(generator.toString()).isEqualTo(generator.getConfigInfo());
    }
    
    @Test
    @DisplayName("默认构造函数应该使用默认参数")
    void shouldUseDefaultParameters() {
        // Given & When
        SnowflakeIdGenerator generator = new SnowflakeIdGenerator();
        Long id = generator.generate();
        SnowflakeIdGenerator.SnowflakeIdInfo info = generator.parseId(id);
        
        // Then
        assertThat(info.getWorkerId()).isEqualTo(1L);
        assertThat(info.getDatacenterId()).isEqualTo(1L);
    }
    
    @Test
    @DisplayName("不同workerId应该生成不同的ID")
    void shouldGenerateDifferentIdsForDifferentWorkerIds() {
        // Given
        SnowflakeIdGenerator generator1 = new SnowflakeIdGenerator(1L, 1L);
        SnowflakeIdGenerator generator2 = new SnowflakeIdGenerator(2L, 1L);
        
        // When
        Long id1 = generator1.generate();
        Long id2 = generator2.generate();
        
        // Then
        assertThat(id1).isNotEqualTo(id2);
        
        SnowflakeIdGenerator.SnowflakeIdInfo info1 = generator1.parseId(id1);
        SnowflakeIdGenerator.SnowflakeIdInfo info2 = generator2.parseId(id2);
        
        assertThat(info1.getWorkerId()).isEqualTo(1L);
        assertThat(info2.getWorkerId()).isEqualTo(2L);
    }
}
