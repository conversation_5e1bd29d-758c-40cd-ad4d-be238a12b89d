package cn.com.handthing.starter.tenant.resolver;

import jakarta.servlet.http.HttpServletRequest;

/**
 * 租户识别策略接口
 * <p>
 * 定义从HTTP请求中解析租户ID的标准接口，支持多种解析策略的插拔式架构。
 * 实现类可以从子域名、HTTP头、URL路径等不同来源解析租户标识。
 * </p>
 *
 * <AUTHOR>
 * @since V3.0.0
 */
public interface TenantResolver {

    /**
     * 从HTTP请求中解析租户ID
     *
     * @param request HTTP请求对象
     * @return 租户ID，如果无法解析则返回null
     */
    String resolveTenantId(HttpServletRequest request);

    /**
     * 检查是否支持解析指定的请求
     * <p>
     * 某些解析器可能只在特定条件下才能工作，例如子域名解析器需要请求包含Host头
     * </p>
     *
     * @param request HTTP请求对象
     * @return 如果支持解析返回true，否则返回false
     */
    default boolean supports(HttpServletRequest request) {
        return true;
    }

    /**
     * 获取解析器的优先级
     * <p>
     * 当配置了多个解析器时，优先级高的解析器会优先执行。
     * 数值越小优先级越高，默认优先级为100。
     * </p>
     *
     * @return 优先级数值
     */
    default int getOrder() {
        return 100;
    }

    /**
     * 获取解析器的名称
     * <p>
     * 用于日志记录和调试，建议使用简洁明了的名称
     * </p>
     *
     * @return 解析器名称
     */
    default String getName() {
        return this.getClass().getSimpleName();
    }

    /**
     * 获取解析器的描述
     * <p>
     * 用于配置文档和错误信息，描述解析器的工作原理
     * </p>
     *
     * @return 解析器描述
     */
    default String getDescription() {
        return "Tenant resolver: " + getName();
    }

    /**
     * 验证解析出的租户ID是否有效
     * <p>
     * 子类可以重写此方法来实现自定义的租户ID验证逻辑
     * </p>
     *
     * @param tenantId 租户ID
     * @return 如果有效返回true，否则返回false
     */
    default boolean isValidTenantId(String tenantId) {
        return tenantId != null && 
               !tenantId.trim().isEmpty() && 
               tenantId.length() <= 64 &&
               tenantId.matches("^[a-zA-Z0-9][a-zA-Z0-9_-]*[a-zA-Z0-9]$|^[a-zA-Z0-9]$");
    }

    /**
     * 标准化租户ID
     * <p>
     * 对解析出的租户ID进行标准化处理，例如转换为小写、去除空格等
     * </p>
     *
     * @param tenantId 原始租户ID
     * @return 标准化后的租户ID
     */
    default String normalizeTenantId(String tenantId) {
        if (tenantId == null) {
            return null;
        }
        
        String normalized = tenantId.trim().toLowerCase();
        return normalized.isEmpty() ? null : normalized;
    }

    /**
     * 解析租户ID的完整流程
     * <p>
     * 包含支持性检查、解析、验证和标准化的完整流程
     * </p>
     *
     * @param request HTTP请求对象
     * @return 标准化后的租户ID，如果解析失败则返回null
     */
    default String resolve(HttpServletRequest request) {
        // 检查是否支持解析
        if (!supports(request)) {
            return null;
        }

        // 解析租户ID
        String tenantId = resolveTenantId(request);
        if (tenantId == null) {
            return null;
        }

        // 标准化租户ID
        String normalized = normalizeTenantId(tenantId);
        if (normalized == null) {
            return null;
        }

        // 验证租户ID
        if (!isValidTenantId(normalized)) {
            return null;
        }

        return normalized;
    }
}
