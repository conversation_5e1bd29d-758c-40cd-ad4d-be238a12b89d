package cn.com.handthing.starter.connector.wecom.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 企业微信部门模型
 * <p>
 * 企业微信部门信息的数据结构
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class WeComDepartment {

    /**
     * 部门ID
     */
    @JsonProperty("id")
    private Integer id;

    /**
     * 部门名称
     */
    @JsonProperty("name")
    private String name;

    /**
     * 部门英文名称
     */
    @JsonProperty("name_en")
    private String nameEn;

    /**
     * 父部门ID
     */
    @JsonProperty("parentid")
    private Integer parentId;

    /**
     * 部门排序
     */
    @JsonProperty("order")
    private Integer order;

    /**
     * 部门负责人的UserID
     */
    @JsonProperty("department_leader")
    private String[] departmentLeader;

    /**
     * 检查是否为根部门
     *
     * @return 如果是根部门返回true，否则返回false
     */
    public boolean isRootDepartment() {
        return id != null && id == 1;
    }

    /**
     * 检查是否有父部门
     *
     * @return 如果有父部门返回true，否则返回false
     */
    public boolean hasParent() {
        return parentId != null && parentId > 0;
    }

    /**
     * 检查是否有部门负责人
     *
     * @return 如果有部门负责人返回true，否则返回false
     */
    public boolean hasLeader() {
        return departmentLeader != null && departmentLeader.length > 0;
    }

    /**
     * 获取显示名称
     *
     * @return 显示名称
     */
    public String getDisplayName() {
        if (name != null && !name.trim().isEmpty()) {
            return name;
        }
        if (nameEn != null && !nameEn.trim().isEmpty()) {
            return nameEn;
        }
        return "部门" + id;
    }

    /**
     * 获取部门层级路径
     *
     * @return 部门层级路径
     */
    public String getPath() {
        if (isRootDepartment()) {
            return "/" + getDisplayName();
        }
        return "/" + getDisplayName();
    }
}
