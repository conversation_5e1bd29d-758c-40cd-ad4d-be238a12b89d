# HandThing Spring Boot 3 Starters 完整代码索引

## 📁 项目架构总览

```
handthing-springboot3-starter/
├── 🏗️ 基础设施层
│   ├── starter-parent/                    # Maven父模块
│   └── handthing-core/                   # 核心工具库
├── 🔑 身份认证层  
│   └── handthing-auth/                   # 认证系统 (详见专门索引)
├── 💾 数据持久层
│   ├── datalayer-spring-boot-starter/    # 基础数据层
│   ├── tenant-datalayer-spring-boot-starter/  # 多租户数据层
│   └── dataauth-datalayer-spring-boot-starter/ # 数据权限层
├── 🚀 缓存加速层
│   ├── cache-spring-boot-starter/        # 缓存抽象层
│   ├── caffeine-cache-spring-boot-starter/ # Caffeine本地缓存
│   ├── redis-cache-spring-boot-starter/  # Redis分布式缓存
│   ├── memory-cache-spring-boot-starter/ # 内存缓存
│   └── level-cache-spring-boot-starter/  # 多级缓存
├── 🌐 网络通信层
│   ├── http-client-spring-boot-starter/  # HTTP客户端抽象
│   ├── resttemplate-spring-boot-starter/ # RestTemplate实现
│   └── webclient-spring-boot-starter/    # WebClient实现
├── 🔗 应用连接层
│   └── app-connector/                    # 第三方平台连接器
├── 🛡️ 安全加密层
│   └── crypto-spring-boot-starter/       # 加密解密工具
├── 📊 可观测性层
│   ├── distributed-log-spring-boot-starter/ # 分布式日志
│   ├── api-doc-spring-boot-starter/      # API文档生成
│   └── knife4j-spring-boot-starter/      # Knife4j文档增强
├── 🆔 ID生成层
│   └── id-spring-boot-starter/           # 分布式ID生成器
└── 🧪 测试验证层
    └── test-app/                         # 集成测试应用
```

## 🏗️ 基础设施层

### 📦 starter-parent
**Maven父模块，统一依赖管理**

| 组件 | 描述 |
|------|------|
| `pom.xml` | 统一版本管理、依赖声明、插件配置 |

### 📦 handthing-core
**核心工具库，提供通用功能**

| 类名 | 路径 | 描述 |
|------|------|------|
| `JsonUtils` | `handthing-core/src/main/java/.../JsonUtils.java` | JSON序列化工具 |
| `DateUtils` | `handthing-core/src/main/java/.../DateUtils.java` | 日期时间工具 |
| `StringUtils` | `handthing-core/src/main/java/.../StringUtils.java` | 字符串处理工具 |
| `CryptoUtils` | `handthing-core/src/main/java/.../CryptoUtils.java` | 基础加密工具 |

## 🆔 ID生成层

### 📦 id-spring-boot-starter
**分布式ID生成器，支持@IdSetter注解**

#### 核心组件
| 类名 | 路径 | 描述 |
|------|------|------|
| `@IdSetter` | `id-spring-boot-starter/src/main/java/.../annotation/IdSetter.java` | ID自动注入注解 |
| `SnowflakeIdGenerator` | `id-spring-boot-starter/src/main/java/.../generator/SnowflakeIdGenerator.java` | 雪花算法ID生成器 |
| `UuidIdGenerator` | `id-spring-boot-starter/src/main/java/.../generator/UuidIdGenerator.java` | UUID生成器 |
| `PooledIdGenerator` | `id-spring-boot-starter/src/main/java/.../generator/PooledIdGenerator.java` | 池化ID生成器 |
| `IdAutoConfiguration` | `id-spring-boot-starter/src/main/java/.../config/IdAutoConfiguration.java` | ID生成器自动配置 |

#### 配置属性
```yaml
handthing.id.enabled                      # 是否启用ID生成器
handthing.id.snowflake.worker-id          # Snowflake工作节点ID
handthing.id.snowflake.datacenter-id      # Snowflake数据中心ID
handthing.id.snowflake.pool.enabled       # 是否启用ID池化
```

## 💾 数据持久层

### 📦 datalayer-spring-boot-starter
**基础数据层，集成MyBatis-Plus**

#### 核心组件
| 类名 | 路径 | 描述 |
|------|------|------|
| `DatalayerAutoConfiguration` | `datalayer-spring-boot-starter/src/main/java/.../config/DatalayerAutoConfiguration.java` | 数据层自动配置 |
| `DatalayerMetaObjectHandler` | `datalayer-spring-boot-starter/src/main/java/.../handler/DatalayerMetaObjectHandler.java` | 元数据处理器 |
| `BaseEntity` | `datalayer-spring-boot-starter/src/main/java/.../entity/BaseEntity.java` | 基础实体类 |
| `DatalayerProperties` | `datalayer-spring-boot-starter/src/main/java/.../config/DatalayerProperties.java` | 数据层配置属性 |

#### 配置属性
```yaml
handthing.datalayer.enabled               # 是否启用数据层
handthing.datalayer.pagination.enabled    # 是否启用分页
handthing.datalayer.auto-fill.enabled     # 是否启用字段自动填充
```

### 📦 tenant-datalayer-spring-boot-starter
**多租户数据隔离插件**

#### 核心组件
| 类名 | 路径 | 描述 |
|------|------|------|
| `TenantInterceptor` | `tenant-datalayer-spring-boot-starter/src/main/java/.../interceptor/TenantInterceptor.java` | 租户拦截器 |
| `TenantContext` | `tenant-datalayer-spring-boot-starter/src/main/java/.../context/TenantContext.java` | 租户上下文 |
| `@TenantIgnore` | `tenant-datalayer-spring-boot-starter/src/main/java/.../annotation/TenantIgnore.java` | 租户忽略注解 |

### 📦 dataauth-datalayer-spring-boot-starter
**数据权限控制插件**

#### 核心组件
| 类名 | 路径 | 描述 |
|------|------|------|
| `DataAuthInterceptor` | `dataauth-datalayer-spring-boot-starter/src/main/java/.../interceptor/DataAuthInterceptor.java` | 数据权限拦截器 |
| `@DataAuth` | `dataauth-datalayer-spring-boot-starter/src/main/java/.../annotation/DataAuth.java` | 数据权限注解 |

## 🚀 缓存加速层

### 📦 cache-spring-boot-starter
**缓存抽象层，统一缓存接口**

#### 核心组件
| 类名 | 路径 | 描述 |
|------|------|------|
| `CacheService` | `cache-spring-boot-starter/src/main/java/.../CacheService.java` | 缓存服务接口 |
| `CacheAutoConfiguration` | `cache-spring-boot-starter/src/main/java/.../CacheAutoConfiguration.java` | 缓存自动配置 |

### 📦 caffeine-cache-spring-boot-starter
**Caffeine本地缓存实现**

#### 核心组件
| 类名 | 路径 | 描述 |
|------|------|------|
| `CaffeineCacheService` | `caffeine-cache-spring-boot-starter/src/main/java/.../CaffeineCacheService.java` | Caffeine缓存服务 |
| `CaffeineCacheAutoConfiguration` | `caffeine-cache-spring-boot-starter/src/main/java/.../config/CaffeineCacheAutoConfiguration.java` | Caffeine自动配置 |

#### 配置属性
```yaml
handthing.cache.type: caffeine            # 缓存类型
handthing.cache.caffeine.default-spec     # 默认缓存规格
handthing.cache.caffeine.stats-enabled    # 是否启用统计
```

## 🌐 网络通信层

### 📦 http-client-spring-boot-starter
**HTTP客户端抽象层**

#### 核心组件
| 类名 | 路径 | 描述 |
|------|------|------|
| `HttpClientService` | `http-client-spring-boot-starter/src/main/java/.../HttpClientService.java` | HTTP客户端服务接口 |
| `HttpClientAutoConfiguration` | `http-client-spring-boot-starter/src/main/java/.../config/HttpClientAutoConfiguration.java` | HTTP客户端自动配置 |

### 📦 resttemplate-spring-boot-starter
**RestTemplate实现**

### 📦 webclient-spring-boot-starter
**WebClient实现**

## 🔗 应用连接层

### 📦 app-connector
**第三方平台连接器，支持多平台OAuth2.0认证**

#### 核心模块结构
```
app-connector/
├── app-connector-core/              # 核心抽象层
├── app-connector-spring-boot-starter/ # 自动配置层
├── wecom-connector/                 # 企业微信连接器
├── dingtalk-connector/              # 钉钉连接器
├── douyin-connector/                # 抖音连接器
├── alipay-connector/                # 支付宝连接器
├── wechat-connector/                # 微信连接器
├── feishu-connector/                # 飞书连接器
├── toutiao-connector/               # 今日头条连接器
├── xiaohongshu-connector/           # 小红书连接器
└── bilibili-connector/              # Bilibili连接器
```

#### 核心接口
| 接口名 | 路径 | 描述 |
|--------|------|------|
| `AuthProvider` | `app-connector-core/src/main/java/.../auth/AuthProvider.java` | 认证提供者策略接口 |
| `PlatformConnector` | `app-connector-core/src/main/java/.../PlatformConnector.java` | 平台连接器接口 |
| `PlatformType` | `app-connector-core/src/main/java/.../PlatformType.java` | 平台类型枚举 |

## 🛡️ 安全加密层

### 📦 crypto-spring-boot-starter
**加密解密工具**

#### 核心组件
| 类名 | 路径 | 描述 |
|------|------|------|
| `CryptoService` | `crypto-spring-boot-starter/src/main/java/.../CryptoService.java` | 加密服务接口 |
| `AESCryptoService` | `crypto-spring-boot-starter/src/main/java/.../AESCryptoService.java` | AES加密实现 |

## 📊 可观测性层

### 📦 distributed-log-spring-boot-starter
**分布式日志**

### 📦 api-doc-spring-boot-starter
**API文档生成**

### 📦 knife4j-spring-boot-starter
**Knife4j文档增强**

## 🔑 身份认证层

### 📦 handthing-auth
**完整的认证授权系统**

> **详细信息请参考**: [HandThing认证系统代码索引.md](./HandThing认证系统代码索引.md)

#### 模块概览
- `auth-core/` - 认证核心抽象
- `auth-spring-boot-starter/` - 认证启动器
- `password-provider-starter/` - 密码认证
- `sms-provider-starter/` - 短信认证  
- `third-party-provider-starter/` - 第三方认证
- `tenant-auth-starter/` - 租户认证

## 🧪 测试验证层

### 📦 test-app
**集成测试应用**

#### 核心组件
| 类名 | 路径 | 描述 |
|------|------|------|
| `TestController` | `test-app/src/main/java/.../TestController.java` | 测试控制器 |
| `AppConnectorTestController` | `test-app/src/main/java/.../AppConnectorTestController.java` | 应用连接器测试 |

## 🔗 模块依赖关系

### 依赖层次图
```
┌─────────────────┐
│   test-app      │ ← 集成测试，依赖所有模块
└─────────────────┘
         ↑
┌─────────────────┐
│  业务功能层      │ ← auth, app-connector, crypto
└─────────────────┘
         ↑
┌─────────────────┐
│  数据缓存层      │ ← datalayer, cache系列
└─────────────────┘
         ↑
┌─────────────────┐
│  基础工具层      │ ← id, http-client, core
└─────────────────┘
         ↑
┌─────────────────┐
│ starter-parent  │ ← Maven父模块
└─────────────────┘
```

### 关键依赖关系
| 模块 | 依赖模块 | 关系说明 |
|------|----------|----------|
| `handthing-auth` | `handthing-core`, `cache-*` | 认证依赖核心工具和缓存 |
| `app-connector` | `http-client-*` | 连接器依赖HTTP客户端 |
| `tenant-datalayer` | `datalayer` | 租户插件扩展基础数据层 |
| `dataauth-datalayer` | `datalayer` | 权限插件扩展基础数据层 |
| `datalayer` | `id-spring-boot-starter` | 数据层集成ID生成器 |

## 📋 配置属性总览

### 全局配置前缀
```yaml
handthing:
  # 基础设施
  core:
    enabled: true

  # ID生成器
  id:
    enabled: true
    snowflake:
      enabled: true
      worker-id: 1
      datacenter-id: 1
      pool:
        enabled: false
        size: 1000
        threshold: 100
        batch-size: 100

  # 数据层
  datalayer:
    enabled: true
    pagination:
      enabled: true
      default-page-size: 10
      max-page-size: 1000
    auto-fill:
      enabled: true
      create-time-field: "createTime"
      update-time-field: "updateTime"

  # 租户
  tenant:
    enabled: false
    tenant-id-field: "tenantId"
    ignore-tables: []

  # 数据权限
  dataauth:
    enabled: false
    default-strategy: "user"

  # 缓存
  cache:
    enabled: true
    type: "caffeine"  # caffeine, redis, memory, level
    caffeine:
      default-spec: "maximumSize=10000,expireAfterWrite=1h"
      stats-enabled: true
    redis:
      key-prefix: "handthing:cache:"
      ttl: 3600

  # HTTP客户端
  http-client:
    enabled: true
    type: "webclient"  # resttemplate, webclient
    timeout:
      connect: 5000
      read: 30000

  # 认证系统
  auth:
    enabled: true
    default-grant-type: "password"
    jwt:
      secret: "your-secret-key"
      access-token-expiration: 7200
      refresh-token-expiration: 604800

  # 应用连接器
  app-connector:
    enabled: true
    platforms:
      wecom:
        enabled: false
        corp-id: ""
        corp-secret: ""
      dingtalk:
        enabled: false
        app-key: ""
        app-secret: ""

  # 加密
  crypto:
    enabled: true
    default-algorithm: "AES"
    key: "your-encryption-key"
```

## 🚀 快速开始指南

### 场景1：基础应用 (ID + 数据层)
```xml
<dependencies>
    <dependency>
        <groupId>cn.com.handthing.springboot3.starter</groupId>
        <artifactId>id-spring-boot-starter</artifactId>
        <version>${revision}</version>
    </dependency>
    <dependency>
        <groupId>cn.com.handthing.springboot3.starter</groupId>
        <artifactId>datalayer-spring-boot-starter</artifactId>
        <version>${revision}</version>
    </dependency>
</dependencies>
```

### 场景2：多租户SaaS应用
```xml
<dependencies>
    <!-- 基础数据层 -->
    <dependency>
        <groupId>cn.com.handthing.springboot3.starter</groupId>
        <artifactId>datalayer-spring-boot-starter</artifactId>
        <version>${revision}</version>
    </dependency>
    <!-- 多租户插件 -->
    <dependency>
        <groupId>cn.com.handthing.springboot3.starter</groupId>
        <artifactId>tenant-datalayer-spring-boot-starter</artifactId>
        <version>${revision}</version>
    </dependency>
    <!-- 认证系统 -->
    <dependency>
        <groupId>cn.com.handthing.springboot3.starter</groupId>
        <artifactId>auth-spring-boot-starter</artifactId>
        <version>${revision}</version>
    </dependency>
</dependencies>
```

### 场景3：第三方集成应用
```xml
<dependencies>
    <!-- HTTP客户端 -->
    <dependency>
        <groupId>cn.com.handthing.springboot3.starter</groupId>
        <artifactId>webclient-spring-boot-starter</artifactId>
        <version>${revision}</version>
    </dependency>
    <!-- 应用连接器 -->
    <dependency>
        <groupId>cn.com.handthing.springboot3.starter</groupId>
        <artifactId>app-connector-spring-boot-starter</artifactId>
        <version>${revision}</version>
    </dependency>
    <!-- 企业微信连接器 -->
    <dependency>
        <groupId>cn.com.handthing.springboot3.starter</groupId>
        <artifactId>wecom-connector</artifactId>
        <version>${revision}</version>
    </dependency>
</dependencies>
```

## 📚 文档导航

### 主要文档
| 文档名称 | 路径 | 描述 |
|----------|------|------|
| **项目总览** | `README.md` | 项目介绍和快速开始 |
| **完整文档** | `DOCUMENTATION.md` | 详细使用文档 |
| **配置说明** | `CONFIGURATION.md` | 配置参数详解 |
| **使用示例** | `EXAMPLES.md` | 代码示例集合 |
| **快速开始** | `QUICK_START.md` | 快速上手指南 |

### 专项文档
| 文档名称 | 路径 | 描述 |
|----------|------|------|
| **认证系统索引** | `docs/HandThing认证系统代码索引.md` | 认证系统详细代码索引 |
| **认证系统文档** | `docs/HandThing认证系统完整文档.md` | 认证系统完整文档 |
| **故障排除** | `docs/HandThing认证系统故障排除指南.md` | 认证系统故障排除 |
| **最佳实践** | `docs/HandThing认证系统最佳实践指南.md` | 认证系统最佳实践 |

### 需求文档
| 文档名称 | 路径 | 描述 |
|----------|------|------|
| **认证需求** | `auth-requirement.md` | 认证系统需求分析 |
| **数据层需求** | `datalayer-requirement.md` | 数据层需求分析 |
| **连接器需求** | `app-connector-requirement.md` | 应用连接器需求分析 |
| **租户需求** | `Tenant-requirement.md` | 多租户需求分析 |

## 🧪 测试指南

### 测试应用使用
```bash
# 启动测试应用
cd test-app
mvn spring-boot:run

# 访问测试页面
http://localhost:8080
```

### 单元测试覆盖
| 模块 | 测试数量 | 覆盖率 |
|------|----------|--------|
| `id-spring-boot-starter` | 28个测试 | 95%+ |
| `datalayer-spring-boot-starter` | 10个测试 | 90%+ |
| `tenant-datalayer-spring-boot-starter` | 13个测试 | 92%+ |
| `dataauth-datalayer-spring-boot-starter` | 9个测试 | 88%+ |

## 🔍 API端点索引

### 认证相关API
| 端点 | 方法 | 描述 |
|------|------|------|
| `/auth/login` | POST | 统一登录接口 |
| `/auth/refresh` | POST | Token刷新 |
| `/auth/logout` | POST | 登出 |

### 应用连接器API
| 端点 | 方法 | 描述 |
|------|------|------|
| `/connector/{platform}/auth-url` | GET | 获取授权URL |
| `/connector/{platform}/callback` | GET | OAuth回调处理 |
| `/connector/{platform}/user-info` | GET | 获取用户信息 |

### 测试API
| 端点 | 方法 | 描述 |
|------|------|------|
| `/test/id` | GET | 测试ID生成器 |
| `/test/cache` | GET | 测试缓存功能 |
| `/test/data` | GET | 测试数据层功能 |

## 🎯 设计模式应用

### 策略模式 (Strategy Pattern)
- **ID生成器**: 不同类型的ID生成策略
- **缓存实现**: 不同的缓存存储策略
- **HTTP客户端**: RestTemplate vs WebClient策略
- **认证提供者**: 多种认证方式策略

### 工厂模式 (Factory Pattern)
- **连接器工厂**: 根据平台类型创建对应连接器
- **缓存管理器工厂**: 根据配置创建缓存管理器

### 装饰器模式 (Decorator Pattern)
- **池化ID生成器**: 装饰基础ID生成器
- **多级缓存**: 装饰单级缓存实现

### 观察者模式 (Observer Pattern)
- **认证事件**: 认证过程中的事件通知
- **缓存事件**: 缓存操作事件监听

## 🚀 性能优化

### ID生成器优化
- **池化技术**: 预生成ID池，减少实时生成开销
- **批量生成**: 批量获取ID，减少锁竞争

### 缓存优化
- **多级缓存**: L1本地缓存 + L2分布式缓存
- **缓存预热**: 应用启动时预加载热点数据
- **智能淘汰**: W-TinyLFU算法优化命中率

### 数据层优化
- **分页优化**: 合理的分页参数和索引使用
- **批量操作**: 支持批量插入和更新
- **连接池**: 优化数据库连接池配置

---

**© 2024 HandThing. All rights reserved.**
