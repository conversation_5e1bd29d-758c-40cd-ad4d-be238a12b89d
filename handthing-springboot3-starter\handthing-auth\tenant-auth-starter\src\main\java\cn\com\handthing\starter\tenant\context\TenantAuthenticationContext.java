package cn.com.handthing.starter.tenant.context;

import cn.com.handthing.starter.auth.core.AuthenticationContext;
import cn.com.handthing.starter.auth.core.AuthenticationRequest;
import cn.com.handthing.starter.auth.core.AuthenticationResponse;
import cn.com.handthing.starter.auth.core.UserInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 租户认证上下文
 * <p>
 * 扩展基础认证上下文，添加多租户支持。
 * 在多租户环境中，tenantId是认证上下文的核心标识，
 * 所有认证决策都基于当前租户的配置进行。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class TenantAuthenticationContext extends AuthenticationContext {

    /**
     * 租户ID - 多租户认证的核心字段
     * <p>
     * 在多租户环境中，tenantId是认证上下文的核心标识，
     * 所有认证决策都基于当前租户的配置进行。
     * </p>
     */
    private String tenantId;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 租户状态
     */
    private String tenantStatus;

    /**
     * 是否为默认租户
     */
    private boolean defaultTenant;

    /**
     * 构造函数
     *
     * @param request 认证请求
     */
    public TenantAuthenticationContext(AuthenticationRequest request) {
        super(request);
        // 从TenantContextHolder获取当前租户ID (如果可用)
        try {
            TenantContext currentTenant = TenantContextHolder.getContext();
            if (currentTenant != null) {
                this.tenantId = currentTenant.getTenantId();
                this.tenantName = currentTenant.getTenantName();
                this.tenantStatus = currentTenant.getStatus() != null ?
                    currentTenant.getStatus().name() : null;
                this.defaultTenant = "default".equals(currentTenant.getTenantId());
            }
        } catch (Exception e) {
            // TenantContextHolder可能还未初始化，忽略异常
            this.tenantId = null;
        }
    }

    /**
     * 构造函数（指定租户ID）
     *
     * @param request  认证请求
     * @param tenantId 租户ID
     */
    public TenantAuthenticationContext(AuthenticationRequest request, String tenantId) {
        super(request);
        this.tenantId = tenantId;
        this.defaultTenant = "default".equals(tenantId);
        
        // 尝试从TenantContextHolder获取租户详细信息
        try {
            TenantContext currentTenant = TenantContextHolder.getContext();
            if (currentTenant != null && tenantId.equals(currentTenant.getTenantId())) {
                this.tenantName = currentTenant.getTenantName();
                this.tenantStatus = currentTenant.getStatus() != null ?
                    currentTenant.getStatus().name() : null;
            }
        } catch (Exception e) {
            // 忽略异常
        }
    }

    /**
     * 构造函数（指定租户信息）
     *
     * @param request    认证请求
     * @param tenantId   租户ID
     * @param tenantName 租户名称
     */
    public TenantAuthenticationContext(AuthenticationRequest request, String tenantId, String tenantName) {
        super(request);
        this.tenantId = tenantId;
        this.tenantName = tenantName;
        this.defaultTenant = "default".equals(tenantId);
    }

    /**
     * 构造函数（完整租户信息）
     *
     * @param request      认证请求
     * @param tenantId     租户ID
     * @param tenantName   租户名称
     * @param tenantStatus 租户状态
     */
    public TenantAuthenticationContext(AuthenticationRequest request, String tenantId, 
                                     String tenantName, String tenantStatus) {
        super(request);
        this.tenantId = tenantId;
        this.tenantName = tenantName;
        this.tenantStatus = tenantStatus;
        this.defaultTenant = "default".equals(tenantId);
    }

    /**
     * 从基础认证上下文创建租户认证上下文
     *
     * @param context  基础认证上下文
     * @param tenantId 租户ID
     * @return 租户认证上下文
     */
    public static TenantAuthenticationContext from(AuthenticationContext context, String tenantId) {
        TenantAuthenticationContext tenantContext = new TenantAuthenticationContext(
            context.getRequest(), tenantId);
        
        // 复制基础上下文的所有属性
        tenantContext.setResponse(context.getResponse());
        tenantContext.setUserInfo(context.getUserInfo());
        tenantContext.setJwtClaims(context.getJwtClaims());
        tenantContext.setProviderName(context.getProviderName());
        tenantContext.setStartTime(context.getStartTime());
        tenantContext.setEndTime(context.getEndTime());
        tenantContext.setSuccess(context.isSuccess());
        tenantContext.setErrorMessage(context.getErrorMessage());
        tenantContext.setException(context.getException());
        tenantContext.setAttributes(context.getAttributes());
        
        return tenantContext;
    }

    /**
     * 从当前租户上下文创建租户认证上下文
     *
     * @param request 认证请求
     * @return 租户认证上下文
     */
    public static TenantAuthenticationContext fromCurrentTenant(AuthenticationRequest request) {
        return new TenantAuthenticationContext(request);
    }

    /**
     * 检查是否有租户信息
     *
     * @return 如果有租户ID返回true，否则返回false
     */
    public boolean hasTenant() {
        return tenantId != null && !tenantId.trim().isEmpty();
    }

    /**
     * 检查是否为指定租户
     *
     * @param targetTenantId 目标租户ID
     * @return 如果是指定租户返回true，否则返回false
     */
    public boolean isTenant(String targetTenantId) {
        return tenantId != null && tenantId.equals(targetTenantId);
    }

    /**
     * 检查租户是否处于活跃状态
     *
     * @return 如果租户状态为ACTIVE返回true，否则返回false
     */
    public boolean isTenantActive() {
        return "ACTIVE".equals(tenantStatus);
    }

    /**
     * 获取租户信息描述
     *
     * @return 租户信息描述
     */
    public String getTenantDescription() {
        if (!hasTenant()) {
            return "No tenant";
        }
        
        StringBuilder sb = new StringBuilder();
        sb.append("Tenant{id='").append(tenantId).append("'");
        
        if (tenantName != null) {
            sb.append(", name='").append(tenantName).append("'");
        }
        
        if (tenantStatus != null) {
            sb.append(", status=").append(tenantStatus);
        }
        
        if (defaultTenant) {
            sb.append(", default=true");
        }
        
        sb.append("}");
        return sb.toString();
    }

    /**
     * 获取上下文描述（重写父类方法以包含租户信息）
     *
     * @return 上下文描述
     */
    @Override
    public String getDescription() {
        StringBuilder sb = new StringBuilder();
        sb.append("TenantAuthenticationContext{");

        // 优先显示租户信息
        if (hasTenant()) {
            sb.append("tenantId=").append(tenantId);
            if (tenantName != null) {
                sb.append(", tenantName=").append(tenantName);
            }
            if (tenantStatus != null) {
                sb.append(", tenantStatus=").append(tenantStatus);
            }
        }

        if (getRequest() != null) {
            if (hasTenant()) sb.append(", ");
            sb.append("grantType=").append(getRequest().getGrantType());
            sb.append(", clientId=").append(getRequest().getClientId());
        }

        if (getUserInfo() != null) {
            sb.append(", userId=").append(getUserInfo().getUserId());
            sb.append(", username=").append(getUserInfo().getUsername());
        }

        sb.append(", success=").append(isSuccess());
        sb.append(", duration=").append(getDuration()).append("ms");
        
        if (!isSuccess() && getErrorMessage() != null) {
            sb.append(", error=").append(getErrorMessage());
        }
        
        sb.append("}");
        return sb.toString();
    }

    /**
     * 标记认证成功（重写以支持租户信息记录）
     *
     * @param response 认证响应
     * @param userInfo 用户信息
     */
    @Override
    public void markSuccess(AuthenticationResponse response, UserInfo userInfo) {
        super.markSuccess(response, userInfo);
        
        // 在扩展属性中记录租户信息
        if (hasTenant()) {
            addAttribute("tenant.id", tenantId);
            addAttribute("tenant.name", tenantName);
            addAttribute("tenant.status", tenantStatus);
            addAttribute("tenant.default", defaultTenant);
        }
    }

    /**
     * 标记认证失败（重写以支持租户信息记录）
     *
     * @param errorMessage 错误信息
     * @param exception    异常信息
     */
    @Override
    public void markFailure(String errorMessage, Throwable exception) {
        super.markFailure(errorMessage, exception);
        
        // 在扩展属性中记录租户信息
        if (hasTenant()) {
            addAttribute("tenant.id", tenantId);
            addAttribute("tenant.name", tenantName);
            addAttribute("tenant.status", tenantStatus);
            addAttribute("tenant.default", defaultTenant);
        }
    }

    /**
     * 标记认证失败（重写以支持租户信息记录）
     *
     * @param response 认证响应
     */
    @Override
    public void markFailure(AuthenticationResponse response) {
        super.markFailure(response);
        
        // 在扩展属性中记录租户信息
        if (hasTenant()) {
            addAttribute("tenant.id", tenantId);
            addAttribute("tenant.name", tenantName);
            addAttribute("tenant.status", tenantStatus);
            addAttribute("tenant.default", defaultTenant);
        }
    }

    @Override
    public String toString() {
        return getDescription();
    }
}
