# 微信连接器 (WeChat Connector)

## 概述

微信连接器提供了与微信开放平台的集成能力，支持公众号和小程序的开发。通过统一的API接口，可以轻松实现用户认证、消息发送、用户管理、小程序功能等。

## 特性

- ✅ **OAuth2.0认证** - 支持微信用户授权登录
- ✅ **消息管理** - 支持文本、图片、语音、视频等消息类型
- ✅ **用户管理** - 获取用户信息、标签管理、分组管理
- ✅ **模板消息** - 支持模板消息和订阅消息推送
- ✅ **小程序支持** - 支持小程序开发和管理
- ✅ **客服功能** - 支持客服消息和多媒体消息
- ✅ **多环境支持** - 支持开发、测试、生产环境配置

## 快速开始

### 1. 添加依赖

```xml
<dependency>
    <groupId>cn.com.handthing.springboot3.starter</groupId>
    <artifactId>wechat-connector</artifactId>
    <version>1.0.0-SNAPSHOT</version>
</dependency>
```

### 2. 配置文件

```yaml
handthing:
  connector:
    wechat:
      enabled: true
      app-id: "your-app-id"
      app-secret: "your-app-secret"
      account-type: "service"  # service/subscription/mini
      mini-program-mode: false
      sandbox-mode: false
      encrypt-message: false
      encoding-aes-key: "your-encoding-aes-key"
      token: "your-token"
      redirect-uri: "http://your-domain.com/callback/wechat"
```

### 3. 基本使用

```java
@RestController
public class WeChatController {
    
    @Autowired
    private WeChatService weChatService;
    
    @Autowired
    private AuthService authService;
    
    // 获取授权URL
    @GetMapping("/wechat/auth")
    public String getAuthUrl() {
        return authService.getAuthorizationUrl(
            PlatformType.WECHAT, 
            "state", 
            "http://your-domain.com/callback/wechat"
        );
    }
    
    // 发送文本消息
    @PostMapping("/wechat/message/text")
    public Map<String, Object> sendTextMessage(@RequestParam String accessToken,
                                              @RequestParam String openId,
                                              @RequestParam String content) {
        return weChatService.message().sendTextMessage(accessToken, openId, content);
    }
    
    // 发送模板消息
    @PostMapping("/wechat/message/template")
    public Map<String, Object> sendTemplateMessage(@RequestParam String accessToken,
                                                   @RequestParam String openId,
                                                   @RequestParam String templateId,
                                                   @RequestBody Map<String, Object> data) {
        return weChatService.message().sendTemplateMessage(accessToken, openId, templateId, data);
    }
    
    // 获取用户信息
    @GetMapping("/wechat/user/{openId}")
    public Map<String, Object> getUserInfo(@RequestParam String accessToken,
                                          @PathVariable String openId) {
        return weChatService.user().getUserInfo(accessToken, openId);
    }
}
```

## 配置说明

| 配置项 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| `enabled` | Boolean | 是 | false | 是否启用微信连接器 |
| `app-id` | String | 是 | - | 微信应用ID |
| `app-secret` | String | 是 | - | 微信应用密钥 |
| `account-type` | String | 否 | service | 账号类型(service/subscription/mini) |
| `mini-program-mode` | Boolean | 否 | false | 是否为小程序模式 |
| `sandbox-mode` | Boolean | 否 | false | 是否使用沙箱环境 |
| `encrypt-message` | Boolean | 否 | false | 是否启用消息加密 |
| `encoding-aes-key` | String | 否 | - | 消息加密密钥 |
| `token` | String | 否 | - | 接口配置Token |
| `redirect-uri` | String | 否 | - | OAuth回调地址 |

## API文档

### 消息API (WeChatMessageApi)

#### 发送文本消息
```java
Map<String, Object> sendTextMessage(String accessToken, String openId, String content)
```

#### 发送图片消息
```java
Map<String, Object> sendImageMessage(String accessToken, String openId, String mediaId)
```

#### 发送模板消息
```java
Map<String, Object> sendTemplateMessage(String accessToken, String openId, String templateId, Map<String, Object> data)
```

#### 发送客服消息
```java
Map<String, Object> sendCustomMessage(String accessToken, String openId, String msgType, Map<String, Object> content)
```

### 用户API (WeChatUserApi)

#### 获取用户信息
```java
Map<String, Object> getUserInfo(String accessToken, String openId)
```

#### 获取用户列表
```java
Map<String, Object> getUserList(String accessToken, String nextOpenId)
```

#### 设置用户标签
```java
Map<String, Object> setUserTag(String accessToken, String openId, Integer tagId)
```

#### 创建用户标签
```java
Map<String, Object> createTag(String accessToken, String tagName)
```

## 常见问题

### Q: 如何获取微信的AppId和AppSecret？
A: 登录微信公众平台或开放平台，在"开发" -> "基本配置"中可以查看AppId和AppSecret。

### Q: 服务号、订阅号、小程序有什么区别？
A: 服务号可以主动推送消息，订阅号只能被动回复，小程序是独立的应用程序。

### Q: 如何处理微信回调？
A: 配置服务器URL和Token，验证签名后处理消息，返回相应的XML格式回复。

### Q: 模板消息和客服消息有什么区别？
A: 模板消息用于通知，有发送次数限制；客服消息用于客服场景，48小时内可发送。

## 更多信息

- [微信公众平台文档](https://developers.weixin.qq.com/doc/offiaccount/Getting_Started/Overview.html)
- [微信小程序文档](https://developers.weixin.qq.com/miniprogram/dev/framework/)
- [微信开放平台文档](https://developers.weixin.qq.com/doc/oplatform/Website_App/WeChat_Login/Wechat_Login.html)
