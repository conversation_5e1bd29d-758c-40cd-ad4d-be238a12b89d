package cn.com.handthing.starter.datalayer.config;

import cn.com.handthing.starter.datalayer.handler.DatalayerMetaObjectHandler;
import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;

/**
 * 数据层自动配置类
 * <p>
 * 负责自动装配数据层相关的Bean，包括MyBatis-Plus拦截器、元数据处理器等。
 * 根据配置属性决定启用哪些功能。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@AutoConfiguration
@EnableConfigurationProperties(DatalayerProperties.class)
@ConditionalOnProperty(prefix = "handthing.datalayer", name = "enabled", havingValue = "true", matchIfMissing = true)
@ConditionalOnClass(MybatisPlusInterceptor.class)
@ComponentScan(basePackages = "cn.com.handthing.starter.datalayer")
public class DatalayerAutoConfiguration {
    
    /**
     * 配置MyBatis-Plus拦截器
     * <p>
     * 集成分页拦截器、乐观锁拦截器等基础功能
     * </p>
     *
     * @param properties 配置属性
     * @return MyBatis-Plus拦截器
     */
    @Bean
    @ConditionalOnMissingBean
    public MybatisPlusInterceptor mybatisPlusInterceptor(DatalayerProperties properties) {
        // 验证配置
        properties.validate();
        
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        
        // 添加分页拦截器
        if (properties.getPagination().isEnabled()) {
            PaginationInnerInterceptor paginationInterceptor = createPaginationInterceptor(properties);
            interceptor.addInnerInterceptor(paginationInterceptor);
            log.info("Enabled pagination interceptor: defaultSize={}, maxSize={}, reasonable={}", 
                    properties.getPagination().getDefaultPageSize(),
                    properties.getPagination().getMaxPageSize(),
                    properties.getPagination().isReasonable());
        }
        
        // 添加乐观锁拦截器
        if (properties.getOptimisticLock().isEnabled()) {
            OptimisticLockerInnerInterceptor optimisticLockerInterceptor = new OptimisticLockerInnerInterceptor();
            interceptor.addInnerInterceptor(optimisticLockerInterceptor);
            log.info("Enabled optimistic lock interceptor: field={}", 
                    properties.getOptimisticLock().getFieldName());
        }
        
        log.info("Configured MyBatis-Plus interceptor with {} inner interceptors", 
                interceptor.getInterceptors().size());
        
        return interceptor;
    }
    
    /**
     * 创建分页拦截器
     */
    private PaginationInnerInterceptor createPaginationInterceptor(DatalayerProperties properties) {
        DatalayerProperties.Pagination paginationConfig = properties.getPagination();
        
        // 自动检测数据库类型，默认使用MySQL
        PaginationInnerInterceptor paginationInterceptor = new PaginationInnerInterceptor(DbType.MYSQL);
        
        // 设置最大单页限制数量
        paginationInterceptor.setMaxLimit(paginationConfig.getMaxPageSize());
        
        // 设置合理化分页
        paginationInterceptor.setOptimizeJoin(paginationConfig.isReasonable());
        
        return paginationInterceptor;
    }
    
    /**
     * 配置数据层元数据处理器
     * <p>
     * 集成@IdSetter注解处理和基础字段自动填充
     * </p>
     *
     * @return 元数据处理器
     */
    @Bean
    @ConditionalOnMissingBean(MetaObjectHandler.class)
    @ConditionalOnProperty(prefix = "handthing.datalayer.auto-fill", name = "enabled", havingValue = "true", matchIfMissing = true)
    public DatalayerMetaObjectHandler datalayerMetaObjectHandler() {
        log.info("Configured DataLayer meta object handler with @IdSetter support");
        return new DatalayerMetaObjectHandler();
    }
    
    /**
     * 配置启动时的日志输出
     */
    @Bean
    public DatalayerConfigurationLogger datalayerConfigurationLogger(DatalayerProperties properties) {
        return new DatalayerConfigurationLogger(properties);
    }
    
    /**
     * 数据层配置日志记录器
     */
    public static class DatalayerConfigurationLogger {
        
        public DatalayerConfigurationLogger(DatalayerProperties properties) {
            logConfiguration(properties);
        }
        
        private void logConfiguration(DatalayerProperties properties) {
            log.info("=== HandThing DataLayer Configuration ===");
            log.info("Enabled: {}", properties.isEnabled());
            
            if (properties.getLogicDelete().isEnabled()) {
                log.info("Logic Delete:");
                log.info("  Field: {}", properties.getLogicDelete().getFieldName());
                log.info("  Deleted Value: {}", properties.getLogicDelete().getDeletedValue());
                log.info("  Not Deleted Value: {}", properties.getLogicDelete().getNotDeletedValue());
            }
            
            if (properties.getPagination().isEnabled()) {
                log.info("Pagination:");
                log.info("  Default Page Size: {}", properties.getPagination().getDefaultPageSize());
                log.info("  Max Page Size: {}", properties.getPagination().getMaxPageSize());
                log.info("  Reasonable: {}", properties.getPagination().isReasonable());
                log.info("  Support Method Arguments: {}", properties.getPagination().isSupportMethodsArguments());
            }
            
            if (properties.getOptimisticLock().isEnabled()) {
                log.info("Optimistic Lock:");
                log.info("  Field: {}", properties.getOptimisticLock().getFieldName());
            }
            
            if (properties.getAutoFill().isEnabled()) {
                log.info("Auto Fill:");
                log.info("  @IdSetter Enabled: {}", properties.getAutoFill().isIdSetterEnabled());
                log.info("  Audit Enabled: {}", properties.getAutoFill().isAuditEnabled());
                log.info("  Time Enabled: {}", properties.getAutoFill().isTimeEnabled());
                log.info("  Create By Field: {}", properties.getAutoFill().getCreateByField());
                log.info("  Update By Field: {}", properties.getAutoFill().getUpdateByField());
                log.info("  Create Time Field: {}", properties.getAutoFill().getCreateTimeField());
                log.info("  Update Time Field: {}", properties.getAutoFill().getUpdateTimeField());
            }
            
            log.info("==========================================");
        }
    }
}
