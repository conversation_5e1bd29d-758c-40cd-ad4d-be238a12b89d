package cn.com.handthing.starter.httpclient.config;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.time.Duration;
import java.util.Objects;

/**
 * 代表一个端点或全局默认的完整配置集合。
 * <p>
 * 它聚合了所有细分的配置属性，如超时、日志、重试等。
 */
public class EndpointConfig {

    /**
     * 建立TCP连接的超时时间。
     */
    @NotNull(message = "connect-timeout cannot be null")
    private Duration connectTimeout = Duration.ofSeconds(10);

    /**
     * 从服务器读取数据的超时时间。
     * 这指的是在建立连接后，等待服务器响应数据的最长时间。
     */
    @NotNull(message = "read-timeout cannot be null")
    private Duration readTimeout = Duration.ofSeconds(30);

    /**
     * 日志相关配置。
     */
    @Valid // 级联验证: 确保 LoggingProperties 内部的约束也被检查
    @NotNull(message = "logging properties cannot be null")
    private LoggingProperties logging = new LoggingProperties();

    /**
     * 重试相关配置。
     */
    @Valid
    @NotNull(message = "retry properties cannot be null")
    private RetryProperties retry = new RetryProperties();

    /**
     * 加解密相关配置。
     */
    @Valid
    @NotNull(message = "crypto properties cannot be null")
    private CryptoProperties crypto = new CryptoProperties();

    /**
     * SSL/TLS 相关配置。
     */
    @Valid
    @NotNull(message = "ssl properties cannot be null")
    private SslProperties ssl = new SslProperties();

    /**
     * RestTemplate 实现的特定配置。
     */
    @Valid
    @NotNull(message = "rest-template properties cannot be null")
    private RestTemplateSpecificProperties restTemplate = new RestTemplateSpecificProperties();

    // --- Standard Getters and Setters, equals, hashCode, toString ---

    public EndpointConfig() {
    }

    public Duration getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(Duration connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public Duration getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(Duration readTimeout) {
        this.readTimeout = readTimeout;
    }

    public LoggingProperties getLogging() {
        return logging;
    }

    public void setLogging(LoggingProperties logging) {
        this.logging = logging;
    }

    public RetryProperties getRetry() {
        return retry;
    }

    public void setRetry(RetryProperties retry) {
        this.retry = retry;
    }

    public CryptoProperties getCrypto() {
        return crypto;
    }

    public void setCrypto(CryptoProperties crypto) {
        this.crypto = crypto;
    }

    public SslProperties getSsl() {
        return ssl;
    }

    public void setSsl(SslProperties ssl) {
        this.ssl = ssl;
    }

    public RestTemplateSpecificProperties getRestTemplate() {
        return restTemplate;
    }

    public void setRestTemplate(RestTemplateSpecificProperties restTemplate) {
        this.restTemplate = restTemplate;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        EndpointConfig that = (EndpointConfig) o;
        return Objects.equals(connectTimeout, that.connectTimeout) && Objects.equals(readTimeout, that.readTimeout) && Objects.equals(logging, that.logging) && Objects.equals(retry, that.retry) && Objects.equals(crypto, that.crypto) && Objects.equals(ssl, that.ssl) && Objects.equals(restTemplate, that.restTemplate);
    }

    @Override
    public int hashCode() {
        return Objects.hash(connectTimeout, readTimeout, logging, retry, crypto, ssl, restTemplate);
    }

    @Override
    public String toString() {
        return "EndpointConfig{" +
                "connectTimeout=" + connectTimeout +
                ", readTimeout=" + readTimeout +
                ", logging=" + logging +
                ", retry=" + retry +
                ", crypto=" + crypto +
                ", ssl=" + ssl +
                ", restTemplate=" + restTemplate +
                '}';
    }
}