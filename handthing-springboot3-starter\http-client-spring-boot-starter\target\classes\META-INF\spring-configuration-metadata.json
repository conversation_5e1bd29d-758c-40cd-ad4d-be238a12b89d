{"groups": [{"name": "handthing.http-client", "type": "cn.com.handthing.starter.httpclient.config.HttpClientProperties", "sourceType": "cn.com.handthing.starter.httpclient.config.HttpClientProperties"}], "properties": [{"name": "handthing.http-client.client-type", "type": "cn.com.handthing.starter.httpclient.config.ClientType", "description": "选择HTTP客户端的实现类型。", "sourceType": "cn.com.handthing.starter.httpclient.config.HttpClientProperties"}, {"name": "handthing.http-client.default-config", "type": "cn.com.handthing.starter.httpclient.config.EndpointConfig", "description": "全局默认配置。 当一个请求URL没有在 'endpoints' 映射中找到特定配置时，将使用此配置。", "sourceType": "cn.com.handthing.starter.httpclient.config.HttpClientProperties"}, {"name": "handthing.http-client.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用整个HTTP客户端starter。", "sourceType": "cn.com.handthing.starter.httpclient.config.HttpClientProperties"}, {"name": "handthing.http-client.endpoints", "type": "java.util.Map<java.lang.String,cn.com.handthing.starter.httpclient.config.EndpointConfig>", "description": "针对特定URL模式的端点配置映射。 Key是Ant风格的URL匹配模式 (例如: \"/api/v1/**\", \"https://example.com/payment/**\")。 Value是该模式对应的具体配置。", "sourceType": "cn.com.handthing.starter.httpclient.config.HttpClientProperties"}], "hints": [], "ignored": {"properties": []}}