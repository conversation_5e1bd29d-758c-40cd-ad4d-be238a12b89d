package cn.com.handthing.starter.httpclient.config;

import java.util.Objects;

/**
 * HTTP请求日志配置属性。
 */
public class LoggingProperties {

    /**
     * 是否启用请求日志。
     */
    private boolean enabled = true;

    /**
     * 日志打印级别。
     */
    private LogLevel level = LogLevel.BASIC;

    /**
     * 日志级别枚举。
     */
    public enum LogLevel {
        /**
         * 不打印任何日志。
         */
        NONE,
        /**
         * 仅打印请求行和响应状态行。
         * 例如: "--> GET /api/users/1", "<-- 200 OK"
         */
        BASIC,
        /**
         * 在 BASIC 级别上，额外打印请求头和响应头。
         */
        HEADERS,
        /**
         * 在 HEADERS 级别上，额外打印请求体和响应体。
         * 注意：可能会打印大量信息，并可能包含敏感数据。请在生产环境中谨慎使用。
         */
        FULL
    }

    // --- Standard Getters and Setters, equals, hashCode, toString ---

    public LoggingProperties() {
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public LogLevel getLevel() {
        return level;
    }

    public void setLevel(LogLevel level) {
        this.level = level;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        LoggingProperties that = (LoggingProperties) o;
        return enabled == that.enabled && level == that.level;
    }

    @Override
    public int hashCode() {
        return Objects.hash(enabled, level);
    }

    @Override
    public String toString() {
        return "LoggingProperties{" +
                "enabled=" + enabled +
                ", level=" + level +
                '}';
    }
}