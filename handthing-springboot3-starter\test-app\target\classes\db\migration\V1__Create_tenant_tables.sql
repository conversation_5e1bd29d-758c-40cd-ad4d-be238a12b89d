-- HandThing多租户认证系统数据库初始化脚本
-- 版本: V3.0.0
-- 作者: HandThing
-- 创建时间: 2025-07-29

-- ========================================
-- 租户配置表
-- ========================================
CREATE TABLE tenant_configs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    tenant_id VARCHAR(64) NOT NULL,
    config_key VARCHAR(128) NOT NULL,
    config_value TEXT,
    config_type VARCHAR(32) DEFAULT 'STRING',
    description VARCHAR(255),
    enabled BOOLEAN DEFAULT TRUE,
    sensitive BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(64),
    updated_by VARCHAR(64),
    version BIGINT DEFAULT 0
);

-- 创建唯一索引
CREATE UNIQUE INDEX uk_tenant_config ON tenant_configs (tenant_id, config_key);

-- 创建普通索引
CREATE INDEX idx_tenant_id ON tenant_configs (tenant_id);
CREATE INDEX idx_config_key ON tenant_configs (config_key);
CREATE INDEX idx_enabled ON tenant_configs (enabled);

-- ========================================
-- 租户信息表（可选）
-- ========================================
CREATE TABLE tenants (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    tenant_id VARCHAR(64) UNIQUE NOT NULL,
    tenant_name VARCHAR(128) NOT NULL,
    tenant_code VARCHAR(64),
    status VARCHAR(32) DEFAULT 'ACTIVE',
    description TEXT,
    contact_email VARCHAR(255),
    contact_phone VARCHAR(32),
    contact_person VARCHAR(128),
    domain VARCHAR(255),
    logo_url VARCHAR(512),
    theme_config VARCHAR(2000),
    feature_flags VARCHAR(2000),
    quota_config VARCHAR(2000),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(64),
    updated_by VARCHAR(64),
    version BIGINT DEFAULT 0
);

-- 创建索引
CREATE INDEX idx_tenant_status ON tenants (status);
CREATE INDEX idx_tenant_domain ON tenants (domain);

-- ========================================
-- 插入默认数据
-- ========================================

-- 插入默认租户
INSERT INTO tenants (tenant_id, tenant_name, status, description, created_by) VALUES 
('default', '默认租户', 'ACTIVE', '系统默认租户，用于单租户模式或作为后备租户', 'system');

-- 插入默认租户的基础配置
INSERT INTO tenant_configs (tenant_id, config_key, config_value, config_type, description, created_by) VALUES 
-- 基础配置
('default', 'tenant.name', '默认租户', 'STRING', '租户显示名称', 'system'),
('default', 'tenant.status', 'ACTIVE', 'STRING', '租户状态', 'system'),
('default', 'tenant.description', '系统默认租户', 'STRING', '租户描述信息', 'system'),

-- 认证配置
('default', 'auth.default.grant.type', 'password', 'STRING', '默认认证类型', 'system'),
('default', 'auth.multi.provider.enabled', 'true', 'BOOLEAN', '是否启用多认证提供者', 'system'),

-- JWT配置
('default', 'jwt.secret', 'handthing-auth-default-secret-key-2024', 'PASSWORD', 'JWT签名密钥', 'system'),
('default', 'jwt.issuer', 'handthing-auth', 'STRING', 'JWT签发者', 'system'),
('default', 'jwt.audience', 'handthing-app', 'STRING', 'JWT受众', 'system'),
('default', 'jwt.access.token.expiration', '7200', 'INTEGER', '访问令牌过期时间（秒）', 'system'),
('default', 'jwt.refresh.token.expiration', '604800', 'INTEGER', '刷新令牌过期时间（秒）', 'system'),

-- 密码认证配置
('default', 'password.auth.enabled', 'true', 'BOOLEAN', '是否启用密码认证', 'system'),
('default', 'password.min.length', '6', 'INTEGER', '密码最小长度', 'system'),
('default', 'password.max.length', '32', 'INTEGER', '密码最大长度', 'system'),

-- 短信认证配置
('default', 'sms.auth.enabled', 'true', 'BOOLEAN', '是否启用短信认证', 'system'),
('default', 'sms.code.length', '6', 'INTEGER', '短信验证码长度', 'system'),
('default', 'sms.code.expiration', '300', 'INTEGER', '短信验证码过期时间（秒）', 'system'),

-- 第三方认证配置（默认禁用）
('default', 'wecom.auth.enabled', 'false', 'BOOLEAN', '是否启用企业微信认证', 'system'),
('default', 'dingtalk.auth.enabled', 'false', 'BOOLEAN', '是否启用钉钉认证', 'system'),
('default', 'wechat.auth.enabled', 'false', 'BOOLEAN', '是否启用微信认证', 'system'),
('default', 'feishu.auth.enabled', 'false', 'BOOLEAN', '是否启用飞书认证', 'system'),

-- 缓存配置
('default', 'cache.enabled', 'true', 'BOOLEAN', '是否启用缓存', 'system'),
('default', 'cache.type', 'caffeine', 'STRING', '缓存类型', 'system'),
('default', 'cache.ttl', '300', 'INTEGER', '缓存生存时间（秒）', 'system');

-- 插入测试租户（用于演示）
INSERT INTO tenants (tenant_id, tenant_name, status, description, domain, created_by) VALUES 
('demo', '演示租户', 'ACTIVE', '用于演示多租户功能的测试租户', 'demo.localhost:8081', 'system'),
('test', '测试租户', 'TRIAL', '用于测试的租户，试用状态', 'test.localhost:8081', 'system');

-- 为演示租户插入配置（继承默认配置但有所不同）
INSERT INTO tenant_configs (tenant_id, config_key, config_value, config_type, description, created_by) VALUES 
-- 演示租户配置
('demo', 'tenant.name', '演示租户', 'STRING', '租户显示名称', 'system'),
('demo', 'tenant.status', 'ACTIVE', 'STRING', '租户状态', 'system'),
('demo', 'jwt.secret', 'demo-tenant-jwt-secret-key-2024', 'PASSWORD', 'JWT签名密钥', 'system'),
('demo', 'jwt.access.token.expiration', '3600', 'INTEGER', '访问令牌过期时间（秒）', 'system'),
('demo', 'password.min.length', '8', 'INTEGER', '密码最小长度', 'system'),
('demo', 'wecom.auth.enabled', 'true', 'BOOLEAN', '是否启用企业微信认证', 'system'),

-- 测试租户配置
('test', 'tenant.name', '测试租户', 'STRING', '租户显示名称', 'system'),
('test', 'tenant.status', 'TRIAL', 'STRING', '租户状态', 'system'),
('test', 'jwt.secret', 'test-tenant-jwt-secret-key-2024', 'PASSWORD', 'JWT签名密钥', 'system'),
('test', 'jwt.access.token.expiration', '1800', 'INTEGER', '访问令牌过期时间（秒）', 'system'),
('test', 'password.min.length', '6', 'INTEGER', '密码最小长度', 'system');
