package cn.com.handthing.starter.httpclient.config;

import java.util.Objects;

/**
 * SSL/TLS 配置属性，用于HTTPS请求。
 */
public class SslProperties {

    /**
     * 是否为该端点启用自定义SSL配置。
     */
    private boolean enabled = false;

    /**
     * 信任库（TrustStore）的路径。
     * 例如: "classpath:certs/truststore.jks" 或 "file:/path/to/truststore.p12"
     */
    private String trustStore;

    /**
     * 信任库的密码。
     */
    private String trustStorePassword;

    /**
     * 密钥库（KeyStore）的路径，用于客户端证书认证。
     * 例如: "classpath:certs/keystore.jks"
     */
    private String keyStore;

    /**
     * 密钥库的密码。
     */
    private String keyStorePassword;

    // --- Standard Getters and Setters, equals, hashCode, toString ---

    public SslProperties() {
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public String getTrustStore() {
        return trustStore;
    }

    public void setTrustStore(String trustStore) {
        this.trustStore = trustStore;
    }

    public String getTrustStorePassword() {
        return trustStorePassword;
    }

    public void setTrustStorePassword(String trustStorePassword) {
        this.trustStorePassword = trustStorePassword;
    }

    public String getKeyStore() {
        return keyStore;
    }

    public void setKeyStore(String keyStore) {
        this.keyStore = keyStore;
    }

    public String getKeyStorePassword() {
        return keyStorePassword;
    }

    public void setKeyStorePassword(String keyStorePassword) {
        this.keyStorePassword = keyStorePassword;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SslProperties that = (SslProperties) o;
        return enabled == that.enabled && Objects.equals(trustStore, that.trustStore) && Objects.equals(trustStorePassword, that.trustStorePassword) && Objects.equals(keyStore, that.keyStore) && Objects.equals(keyStorePassword, that.keyStorePassword);
    }

    @Override
    public int hashCode() {
        return Objects.hash(enabled, trustStore, trustStorePassword, keyStore, keyStorePassword);
    }

    @Override
    public String toString() {
        // Note: Passwords are not included in toString for security reasons.
        return "SslProperties{" +
                "enabled=" + enabled +
                ", trustStore='" + trustStore + '\'' +
                ", keyStore='" + keyStore + '\'' +
                '}';
    }
}