# 今日头条连接器 (Toutiao Connector)

## 概述

今日头条连接器提供了与今日头条开放平台的集成能力，支持个人创作者和企业用户的内容创作和管理。通过统一的API接口，可以轻松实现用户认证、内容发布、数据分析、粉丝管理等功能。

## 特性

- ✅ **OAuth2.0认证** - 支持今日头条用户授权登录
- ✅ **内容发布** - 支持文章、视频、图片等内容发布
- ✅ **数据分析** - 获取内容数据、用户数据、收益数据统计
- ✅ **粉丝管理** - 获取粉丝画像和互动数据
- ✅ **热门内容** - 获取热门内容排行和趋势分析
- ✅ **多应用类型** - 支持网页、移动、服务端应用
- ✅ **沙箱支持** - 完整的沙箱环境测试支持

## 快速开始

### 1. 添加依赖

```xml
<dependency>
    <groupId>cn.com.handthing.springboot3.starter</groupId>
    <artifactId>toutiao-connector</artifactId>
    <version>1.0.0-SNAPSHOT</version>
</dependency>
```

### 2. 配置文件

```yaml
handthing:
  connector:
    toutiao:
      enabled: true
      client-key: "your-client-key"
      client-secret: "your-client-secret"
      app-type: "web"  # web/mobile/server
      enterprise-mode: false
      sandbox-mode: true
      content-publish-enabled: true
      analytics-enabled: true
      user-management-enabled: true
      scope: "user_info,content.write,data.read"
      redirect-uri: "http://your-domain.com/callback/toutiao"
```

### 3. 基本使用

```java
@RestController
public class ToutiaoController {
    
    @Autowired
    private ToutiaoService toutiaoService;
    
    @Autowired
    private AuthService authService;
    
    // 获取授权URL
    @GetMapping("/toutiao/auth")
    public String getAuthUrl() {
        return authService.getAuthorizationUrl(
            PlatformType.TOUTIAO, 
            "state", 
            "http://your-domain.com/callback/toutiao"
        );
    }
    
    // 发布文章
    @PostMapping("/toutiao/article")
    public Map<String, Object> publishArticle(@RequestParam String accessToken,
                                             @RequestParam String title,
                                             @RequestParam String content,
                                             @RequestParam String coverImage,
                                             @RequestParam List<String> tags) {
        return toutiaoService.content().publishArticle(accessToken, title, content, coverImage, tags);
    }
    
    // 发布视频
    @PostMapping("/toutiao/video")
    public Map<String, Object> publishVideo(@RequestParam String accessToken,
                                           @RequestParam String title,
                                           @RequestParam String videoUrl,
                                           @RequestParam String coverImage,
                                           @RequestParam String description,
                                           @RequestParam List<String> tags) {
        return toutiaoService.content().publishVideo(accessToken, title, videoUrl, coverImage, description, tags);
    }
    
    // 获取内容数据
    @GetMapping("/toutiao/analytics/content/{contentId}")
    public Map<String, Object> getContentStats(@RequestParam String accessToken,
                                              @PathVariable String contentId,
                                              @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
                                              @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        return toutiaoService.analytics().getContentStats(accessToken, contentId, startDate, endDate);
    }
}
```

## 配置说明

| 配置项 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| `enabled` | Boolean | 是 | false | 是否启用今日头条连接器 |
| `client-key` | String | 是 | - | 应用ClientKey |
| `client-secret` | String | 是 | - | 应用ClientSecret |
| `app-type` | String | 否 | web | 应用类型(web/mobile/server) |
| `enterprise-mode` | Boolean | 否 | false | 是否为企业应用模式 |
| `sandbox-mode` | Boolean | 否 | false | 是否使用沙箱环境 |
| `content-publish-enabled` | Boolean | 否 | true | 是否启用内容发布功能 |
| `analytics-enabled` | Boolean | 否 | true | 是否启用数据分析功能 |
| `user-management-enabled` | Boolean | 否 | true | 是否启用用户管理功能 |
| `scope` | String | 否 | user_info,content.write,data.read | 授权范围 |
| `redirect-uri` | String | 否 | - | OAuth回调地址 |

## API文档

### 内容API (ToutiaoContentApi)

#### 发布文章
```java
Map<String, Object> publishArticle(String accessToken, String title, String content, String coverImage, List<String> tags)
```

#### 上传图片
```java
Map<String, Object> uploadImage(String accessToken, String imageData, String imageName)
```

#### 发布视频
```java
Map<String, Object> publishVideo(String accessToken, String title, String videoUrl, String coverImage, String description, List<String> tags)
```

#### 获取内容列表
```java
Map<String, Object> getContentList(String accessToken, Integer page, Integer pageSize, String contentType)
```

#### 删除内容
```java
Map<String, Object> deleteContent(String accessToken, String contentId)
```

### 数据分析API (ToutiaoAnalyticsApi)

#### 获取内容数据统计
```java
Map<String, Object> getContentStats(String accessToken, String contentId, LocalDate startDate, LocalDate endDate)
```

#### 获取用户数据统计
```java
Map<String, Object> getUserStats(String accessToken, LocalDate startDate, LocalDate endDate)
```

#### 获取收益数据
```java
Map<String, Object> getRevenueStats(String accessToken, LocalDate startDate, LocalDate endDate)
```

#### 获取热门内容排行
```java
Map<String, Object> getTrendingContent(String accessToken, String contentType, String period, Integer limit)
```

#### 获取粉丝画像数据
```java
Map<String, Object> getFansProfile(String accessToken)
```

## 常见问题

### Q: 如何获取今日头条的ClientKey和ClientSecret？
A: 登录今日头条开放平台，在"应用管理"中创建应用，即可获取ClientKey和ClientSecret。

### Q: 不同应用类型有什么区别？
A: web应用适用于网页端，mobile应用适用于移动端，server应用适用于服务端调用。

### Q: 如何获取内容发布权限？
A: 需要在开放平台申请相应的权限，通过审核后才能发布内容。

### Q: 数据统计有延迟吗？
A: 数据统计通常有1-2小时的延迟，实时数据可能不够准确。

## 更多信息

- [今日头条开放平台文档](https://open.toutiao.com/)
- [内容发布API](https://open.toutiao.com/doc/content-api)
- [数据开放API](https://open.toutiao.com/doc/data-api)
