package cn.com.handthing.starter.dataauth.datalayer.provider;

import cn.com.handthing.starter.dataauth.datalayer.annotation.DataPermission;

/**
 * 数据权限提供者接口
 * <p>
 * 定义数据权限的生成策略，不同的权限类型对应不同的实现。
 * 支持部门权限、用户权限、角色权限等多种权限模式。
 * </p>
 * 
 * <h3>实现示例：</h3>
 * <pre>
 * &#64;Component
 * public class DeptPermissionProvider implements DataPermissionProvider {
 *     
 *     &#64;Override
 *     public boolean supports(DataPermission.DataPermissionType type) {
 *         return DataPermission.DataPermissionType.DEPT == type;
 *     }
 *     
 *     &#64;Override
 *     public String generatePermissionCondition(DataPermission permission, String mappedStatementId) {
 *         String deptId = getCurrentUserDeptId();
 *         String field = permission.field().isEmpty() ? "dept_id" : permission.field();
 *         return String.format("%s = '%s'", field, deptId);
 *     }
 * }
 * </pre>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
public interface DataPermissionProvider {
    
    /**
     * 检查是否支持指定的权限类型
     * 
     * @param type 权限类型
     * @return 如果支持返回true，否则返回false
     */
    boolean supports(DataPermission.DataPermissionType type);
    
    /**
     * 生成权限条件SQL片段
     * <p>
     * 根据权限配置和当前用户信息，生成相应的SQL WHERE条件。
     * </p>
     * 
     * @param permission        权限配置
     * @param mappedStatementId Mapper方法ID
     * @return SQL权限条件，如果无需权限控制返回null或空字符串
     */
    String generatePermissionCondition(DataPermission permission, String mappedStatementId);
    
    /**
     * 获取权限提供者的优先级
     * <p>
     * 当多个提供者支持同一权限类型时，优先级高的提供者会被优先使用。
     * 数值越小优先级越高。
     * </p>
     * 
     * @return 优先级，默认为0
     */
    default int getOrder() {
        return 0;
    }
    
    /**
     * 获取权限提供者名称
     * 
     * @return 提供者名称
     */
    default String getName() {
        return this.getClass().getSimpleName();
    }
    
    /**
     * 验证权限配置
     * <p>
     * 检查权限配置是否有效，如必要的字段是否配置等。
     * </p>
     * 
     * @param permission 权限配置
     * @return 验证结果，如果配置有效返回null或空字符串，否则返回错误信息
     */
    default String validatePermissionConfig(DataPermission permission) {
        return null;
    }
    
    /**
     * 检查权限提供者是否可用
     * <p>
     * 检查提供者的依赖条件是否满足，如用户上下文是否可用等。
     * </p>
     * 
     * @return 如果可用返回true，否则返回false
     */
    default boolean isAvailable() {
        return true;
    }
    
    /**
     * 获取权限提供者的描述信息
     * 
     * @return 描述信息
     */
    default String getDescription() {
        return "Data permission provider for " + getName();
    }
}
