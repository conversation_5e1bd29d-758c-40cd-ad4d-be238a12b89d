package cn.com.handthing.starter.cache;

import cn.com.handthing.starter.cache.config.CacheProperties;
import cn.com.handthing.starter.cache.impl.DefaultCacheService;
// import cn.com.handthing.starter.cache.health.CacheHealthIndicator;
import lombok.extern.slf4j.Slf4j;
// import org.springframework.boot.actuator.health.HealthIndicator;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;

/**
 * 缓存自动配置类
 * <p>
 * 提供缓存服务的自动配置，包括CacheService的默认实现和Spring Cache的启用
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@AutoConfiguration
@EnableCaching
@EnableConfigurationProperties(CacheProperties.class)
@ConditionalOnProperty(prefix = "handthing.cache", name = "enabled", havingValue = "true", matchIfMissing = true)
public class CacheAutoConfiguration {

    /**
     * 创建默认的缓存服务实现
     *
     * @param cacheManager Spring缓存管理器
     * @return 缓存服务实例
     */
    @Bean
    @ConditionalOnClass(CacheManager.class)
    @ConditionalOnMissingBean(CacheService.class)
    public CacheService cacheService(CacheManager cacheManager) {
        log.info("Creating default CacheService with CacheManager: {}", 
                cacheManager.getClass().getSimpleName());
        return new DefaultCacheService(cacheManager);
    }

    /*
     * 创建缓存健康检查指示器
     *
     * @param cacheService 缓存服务
     * @param cacheManager 缓存管理器
     * @return 健康检查指示器
     */
    /*
    @Bean
    @ConditionalOnClass(HealthIndicator.class)
    @ConditionalOnMissingBean(name = "cacheHealthIndicator")
    @ConditionalOnProperty(prefix = "management.health.cache", name = "enabled", havingValue = "true", matchIfMissing = true)
    public HealthIndicator cacheHealthIndicator(CacheService cacheService, CacheManager cacheManager) {
        log.info("Creating CacheHealthIndicator");
        return new CacheHealthIndicator(cacheService, cacheManager);
    }
    */
}