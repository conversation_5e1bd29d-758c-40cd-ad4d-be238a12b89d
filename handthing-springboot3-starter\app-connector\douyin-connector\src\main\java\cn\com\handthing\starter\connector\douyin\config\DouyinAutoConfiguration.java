package cn.com.handthing.starter.connector.douyin.config;

import cn.com.handthing.starter.connector.auth.AuthService;
import cn.com.handthing.starter.connector.auth.DefaultAuthService;
import cn.com.handthing.starter.connector.douyin.DouyinAuthProvider;
import cn.com.handthing.starter.connector.token.TokenManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.web.client.RestTemplate;

import jakarta.annotation.PostConstruct;

/**
 * 抖音自动配置类
 * <p>
 * 当抖音配置启用时，自动装配抖音相关的Bean，
 * 并注册到统一的认证服务中。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@RequiredArgsConstructor
@AutoConfiguration
@EnableConfigurationProperties(DouyinConfig.class)
@ConditionalOnProperty(prefix = "handthing.connector.douyin", name = "enabled", havingValue = "true")
@ConditionalOnClass(AuthService.class)
@ComponentScan(basePackages = "cn.com.handthing.starter.connector.douyin")
public class DouyinAutoConfiguration {



    private final AuthService authService;
    private final DouyinAuthProvider douyinAuthProvider;
    private final TokenManager tokenManager;
    private final DouyinConfig douyinConfig;



    /**
     * 注册抖音认证提供者到认证服务
     */
    @PostConstruct
    public void registerDouyinAuthProvider() {
        if (authService instanceof DefaultAuthService) {
            DefaultAuthService defaultAuthService = (DefaultAuthService) authService;
            defaultAuthService.registerAuthProvider(douyinAuthProvider);
            tokenManager.registerAuthProvider(douyinAuthProvider);

            log.info("Registered Douyin auth provider - ClientKey: {}, EnterpriseMode: {}, Valid: {}",
                    douyinConfig.getClientKey(),
                    douyinConfig.isEnterpriseMode(),
                    douyinConfig.isValid());
        } else {
            log.warn("AuthService is not DefaultAuthService, cannot register Douyin auth provider");
        }
    }

    /**
     * 抖音配置信息Bean
     *
     * @param douyinConfig 抖音配置
     * @return 配置信息
     */
    @Bean
    public DouyinConfigInfo douyinConfigInfo(DouyinConfig douyinConfig) {
        DouyinConfigInfo configInfo = new DouyinConfigInfo();
        configInfo.setEnabled(douyinConfig.isEnabled());
        configInfo.setValid(douyinConfig.isValid());
        configInfo.setClientKey(douyinConfig.getClientKey());
        configInfo.setEnterpriseMode(douyinConfig.isEnterpriseMode());
        configInfo.setMiniProgramMode(douyinConfig.isMiniProgramMode());
        configInfo.setWebhookEnabled(douyinConfig.isWebhookEnabled());
        configInfo.setApiBaseUrl(douyinConfig.getEffectiveApiBaseUrl());
        configInfo.setAuthUrl(douyinConfig.getEffectiveAuthUrl());
        
        log.info("Douyin configuration: enabled={}, valid={}, clientKey={}, enterpriseMode={}, miniProgramMode={}, webhookEnabled={}", 
                configInfo.isEnabled(), 
                configInfo.isValid(),
                configInfo.getClientKey(),
                configInfo.isEnterpriseMode(),
                configInfo.isMiniProgramMode(),
                configInfo.isWebhookEnabled());
        
        return configInfo;
    }

    /**
     * 抖音配置信息类
     */
    public static class DouyinConfigInfo {
        private boolean enabled;
        private boolean valid;
        private String clientKey;
        private boolean enterpriseMode;
        private boolean miniProgramMode;
        private boolean webhookEnabled;
        private String apiBaseUrl;
        private String authUrl;

        // Getters and Setters
        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public boolean isValid() {
            return valid;
        }

        public void setValid(boolean valid) {
            this.valid = valid;
        }

        public String getClientKey() {
            return clientKey;
        }

        public void setClientKey(String clientKey) {
            this.clientKey = clientKey;
        }

        public boolean isEnterpriseMode() {
            return enterpriseMode;
        }

        public void setEnterpriseMode(boolean enterpriseMode) {
            this.enterpriseMode = enterpriseMode;
        }

        public boolean isMiniProgramMode() {
            return miniProgramMode;
        }

        public void setMiniProgramMode(boolean miniProgramMode) {
            this.miniProgramMode = miniProgramMode;
        }

        public boolean isWebhookEnabled() {
            return webhookEnabled;
        }

        public void setWebhookEnabled(boolean webhookEnabled) {
            this.webhookEnabled = webhookEnabled;
        }

        public String getApiBaseUrl() {
            return apiBaseUrl;
        }

        public void setApiBaseUrl(String apiBaseUrl) {
            this.apiBaseUrl = apiBaseUrl;
        }

        public String getAuthUrl() {
            return authUrl;
        }

        public void setAuthUrl(String authUrl) {
            this.authUrl = authUrl;
        }

        @Override
        public String toString() {
            return String.format("DouyinConfigInfo{enabled=%s, valid=%s, clientKey='%s', enterpriseMode=%s, miniProgramMode=%s, webhookEnabled=%s, apiBaseUrl='%s', authUrl='%s'}",
                    enabled, valid, clientKey, enterpriseMode, miniProgramMode, webhookEnabled, apiBaseUrl, authUrl);
        }
    }
}
