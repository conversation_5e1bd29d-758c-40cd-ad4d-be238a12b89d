package cn.com.handthing.starter.tenant.datalayer.config;

import cn.com.handthing.starter.tenant.datalayer.handler.SaasTenantLineHandler;
import cn.com.handthing.starter.tenant.datalayer.handler.TenantMetaObjectHandler;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.TenantLineInnerInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Primary;

/**
 * 租户数据层自动配置类
 * <p>
 * 负责自动装配租户数据层相关的Bean，包括租户拦截器、元数据处理器等。
 * 根据配置属性决定启用哪些租户功能。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@AutoConfiguration
@EnableConfigurationProperties(TenantDatalayerProperties.class)
@ConditionalOnProperty(prefix = "handthing.datalayer.tenant", name = "enabled", havingValue = "true", matchIfMissing = true)
@ConditionalOnClass(MybatisPlusInterceptor.class)
@ComponentScan(basePackages = "cn.com.handthing.starter.tenant.datalayer")
public class TenantDatalayerAutoConfiguration {
    
    /**
     * 配置租户行处理器
     * <p>
     * 实现MyBatis-Plus的TenantLineHandler接口，自动在SQL中添加租户条件
     * </p>
     *
     * @param properties 租户配置属性
     * @return 租户行处理器
     */
    @Bean
    @ConditionalOnMissingBean
    public SaasTenantLineHandler saasTenantLineHandler(TenantDatalayerProperties properties) {
        // 初始化默认配置
        properties.initDefaults();
        
        // 验证配置
        properties.validate();
        
        log.info("Configured SaaS tenant line handler: column={}, defaultTenant={}, strictMode={}", 
                properties.getTenantIdColumn(),
                properties.getDefaultTenantId(),
                properties.isStrictMode());
        
        return new SaasTenantLineHandler();
    }
    
    /**
     * 配置租户拦截器
     * <p>
     * 向MybatisPlusInterceptor添加TenantLineInnerInterceptor
     * </p>
     *
     * @param mybatisPlusInterceptor MyBatis-Plus拦截器
     * @param tenantLineHandler      租户行处理器
     * @param properties             租户配置属性
     * @return 增强后的MyBatis-Plus拦截器
     */
    @Bean
    @Primary
    public MybatisPlusInterceptor tenantMybatisPlusInterceptor(
            MybatisPlusInterceptor mybatisPlusInterceptor,
            SaasTenantLineHandler tenantLineHandler,
            TenantDatalayerProperties properties) {
        
        // 创建租户拦截器
        TenantLineInnerInterceptor tenantInterceptor = new TenantLineInnerInterceptor(tenantLineHandler);
        
        // 添加到拦截器链的最前面，确保租户条件优先处理
        mybatisPlusInterceptor.getInterceptors().add(0, tenantInterceptor);
        
        log.info("Added tenant line interceptor to MyBatis-Plus interceptor chain");
        log.info("Tenant interceptor configuration: {}", properties.getConfigSummary());
        
        return mybatisPlusInterceptor;
    }
    
    /**
     * 配置租户元数据处理器
     * <p>
     * 扩展DatalayerMetaObjectHandler，支持租户ID自动填充
     * </p>
     *
     * @param properties 租户配置属性
     * @return 租户元数据处理器
     */
    @Bean
    @Primary
    @ConditionalOnProperty(prefix = "handthing.datalayer.tenant", name = "auto-fill-enabled", havingValue = "true", matchIfMissing = true)
    @ConditionalOnMissingBean(name = "tenantMetaObjectHandler")
    public TenantMetaObjectHandler tenantMetaObjectHandler(TenantDatalayerProperties properties) {
        log.info("Configured tenant meta object handler with auto-fill support: column={}", 
                properties.getTenantIdColumn());
        return new TenantMetaObjectHandler();
    }
    
    /**
     * 配置启动时的日志输出
     */
    @Bean
    public TenantDatalayerConfigurationLogger tenantDatalayerConfigurationLogger(
            TenantDatalayerProperties properties,
            @Autowired(required = false) SaasTenantLineHandler tenantLineHandler) {
        return new TenantDatalayerConfigurationLogger(properties, tenantLineHandler);
    }
    
    /**
     * 租户数据层配置日志记录器
     */
    public static class TenantDatalayerConfigurationLogger {
        
        public TenantDatalayerConfigurationLogger(TenantDatalayerProperties properties, 
                                                 SaasTenantLineHandler tenantLineHandler) {
            logConfiguration(properties, tenantLineHandler);
        }
        
        private void logConfiguration(TenantDatalayerProperties properties, 
                                    SaasTenantLineHandler tenantLineHandler) {
            log.info("=== HandThing Tenant DataLayer Configuration ===");
            log.info("Enabled: {}", properties.isEnabled());
            
            if (properties.isEnabled()) {
                log.info("Tenant Configuration:");
                log.info("  Tenant ID Column: {}", properties.getTenantIdColumn());
                log.info("  Default Tenant ID: {}", properties.getDefaultTenantId());
                log.info("  Strict Mode: {}", properties.isStrictMode());
                log.info("  Validate Consistency: {}", properties.isValidateConsistency());
                log.info("  Auto Fill Enabled: {}", properties.isAutoFillEnabled());
                log.info("  Isolation Strategy: {}", properties.getIsolationStrategy());
                
                log.info("Ignore Configuration:");
                log.info("  Ignore Tables: {} tables", properties.getIgnoreTables().size());
                if (!properties.getIgnoreTables().isEmpty()) {
                    log.info("    Tables: {}", properties.getIgnoreTables());
                }
                log.info("  Ignore Table Prefixes: {} prefixes", properties.getIgnoreTablePrefixes().size());
                if (!properties.getIgnoreTablePrefixes().isEmpty()) {
                    log.info("    Prefixes: {}", properties.getIgnoreTablePrefixes());
                }
                log.info("  Ignore System Tables: {}", properties.isIgnoreSystemTables());
                
                if (properties.getDataSource().isMultiDataSourceEnabled()) {
                    log.info("Multi-DataSource Configuration:");
                    log.info("  Enabled: {}", properties.getDataSource().isMultiDataSourceEnabled());
                    log.info("  Routing Strategy: {}", properties.getDataSource().getRoutingStrategy());
                    log.info("  Default DataSource: {}", properties.getDataSource().getDefaultDataSource());
                    log.info("  DataSource Prefix: {}", properties.getDataSource().getDataSourcePrefix());
                    log.info("  Cache Enabled: {}", properties.getDataSource().isCacheEnabled());
                    log.info("  Cache Expire Seconds: {}", properties.getDataSource().getCacheExpireSeconds());
                }
                
                // 验证租户处理器状态
                if (tenantLineHandler != null) {
                    log.info("Tenant Line Handler Status:");
                    log.info("  Context Available: {}", tenantLineHandler.isTenantContextAvailable());
                    log.info("  Current Tenant: {}", tenantLineHandler.getCurrentTenantSummary());
                    log.info("  Ignore Tables Summary: {}", tenantLineHandler.getIgnoreTablesSummary());
                    
                    // 验证配置
                    String validationResult = tenantLineHandler.validateConfiguration();
                    if (!validationResult.isEmpty()) {
                        log.warn("Tenant configuration validation issues: {}", validationResult);
                    } else {
                        log.info("  Configuration Validation: PASSED");
                    }
                } else {
                    log.warn("Tenant Line Handler is not available");
                }
            }
            
            log.info("===============================================");
        }
    }
}
