# 📚 HandThing DataLayer 文档索引

欢迎使用 HandThing DataLayer Spring Boot Starter 系列！本文档索引将帮助您快速找到所需的文档。

## 📖 核心文档

### 🚀 [README.md](./README.md)
**项目主文档** - 项目概述、核心特性、架构设计、模块介绍
- 项目概述和特性介绍
- 架构设计说明
- 模块依赖关系
- 快速开始示例

### 🏃‍♂️ [QUICK_START.md](./QUICK_START.md)
**快速开始指南** - 5分钟快速上手指南
- 前置条件和环境要求
- 依赖配置和模块选择
- 基础配置示例
- 实体类和Service创建
- 测试和验证

### 📖 [API.md](./API.md)
**API接口文档** - 详细的API接口说明
- ID生成器模块API
- 数据层核心模块API
- 租户插件模块API
- 数据权限插件模块API
- 配置API说明

### ⚙️ [CONFIGURATION.md](./CONFIGURATION.md)
**配置参考文档** - 完整的配置项说明
- ID生成器配置
- 数据层核心配置
- 租户插件配置
- 数据权限插件配置
- 环境配置示例

### 💡 [EXAMPLES.md](./EXAMPLES.md)
**使用示例文档** - 丰富的使用示例
- ID生成器使用示例
- 基础数据层示例
- 多租户应用示例
- 数据权限控制示例
- 高级用法和测试示例

## 📋 需求和任务文档

### 📝 [datalayer-requirement.md](./datalayer-requirement.md)
**需求文档** - 详细的功能需求说明
- 项目背景和目标
- 功能需求详细说明
- 技术要求和约束
- 验收标准

### ✅ [datalayer-requirement-task-list.md](./datalayer-requirement-task-list.md)
**任务清单** - 开发任务分解和进度跟踪
- 模块开发任务分解
- 开发进度跟踪
- 测试任务清单
- 文档编写任务

## 🏗️ 架构和设计

### 🎯 模块架构

```
┌─────────────────────────────────────────────────────────────┐
│                    Your Application                         │
│  ┌─────────────────┐  ┌─────────────────┐                  │
│  │   Tenant Plugin │  │ DataAuth Plugin │  (可选插件)      │
│  └─────────────────┘  └─────────────────┘                  │
│  ┌─────────────────────────────────────────┐                │
│  │        DataLayer Core Module            │  (核心模块)    │
│  └─────────────────────────────────────────┘                │
│  ┌─────────────────────────────────────────┐                │
│  │         ID Generator Module             │  (基础模块)    │
│  └─────────────────────────────────────────┘                │
└─────────────────────────────────────────────────────────────┘
```

### 📦 模块说明

| 模块 | 状态 | 测试 | 描述 |
|------|------|------|------|
| **id-spring-boot-starter** | ✅ 已完成 | 28个测试通过 | 分布式ID生成器，支持@IdSetter注解 |
| **datalayer-spring-boot-starter** | ✅ 已完成 | 10个测试通过 | 基础持久化能力，集成MyBatis-Plus |
| **tenant-datalayer-spring-boot-starter** | ✅ 已完成 | 13个测试通过 | 多租户数据隔离插件 |
| **dataauth-datalayer-spring-boot-starter** | ✅ 已完成 | 9个测试通过 | 数据权限控制插件 |

## 🎯 使用场景导航

### 📋 场景1：简单应用
**需求**：基础CRUD、ID生成、审计字段
- **文档**：[快速开始 - 场景1](./QUICK_START.md#场景1基础应用)
- **示例**：[基础数据层示例](./EXAMPLES.md#️-基础数据层示例)
- **配置**：[基础配置](./CONFIGURATION.md#️-数据层核心配置)

### 🏢 场景2：多租户SaaS应用
**需求**：多租户数据隔离
- **文档**：[快速开始 - 场景2](./QUICK_START.md#场景2多租户saas应用)
- **示例**：[多租户应用示例](./EXAMPLES.md#-多租户应用示例)
- **配置**：[租户配置](./CONFIGURATION.md#-租户插件配置)

### 🔐 场景3：权限控制应用
**需求**：数据权限控制
- **文档**：[快速开始 - 场景3](./QUICK_START.md#场景3权限控制应用)
- **示例**：[数据权限控制示例](./EXAMPLES.md#-数据权限控制示例)
- **配置**：[权限配置](./CONFIGURATION.md#-数据权限插件配置)

### 🏛️ 场景4：完整企业应用
**需求**：多租户 + 数据权限
- **文档**：[快速开始 - 场景4](./QUICK_START.md#场景4完整企业应用)
- **示例**：[高级用法示例](./EXAMPLES.md#-高级用法示例)
- **配置**：[完整配置示例](./CONFIGURATION.md#-完整配置示例)

## 🔍 问题排查

### ❓ 常见问题
- **ID生成器不工作？** → [快速开始 - 常见问题](./QUICK_START.md#-常见问题)
- **租户隔离不生效？** → [配置参考 - 租户配置](./CONFIGURATION.md#-租户插件配置)
- **数据权限不生效？** → [API文档 - 数据权限](./API.md#-数据权限插件模块)

### 🐛 故障排除
1. **检查依赖配置** → [快速开始 - 添加依赖](./QUICK_START.md#️-添加依赖)
2. **验证配置文件** → [配置参考](./CONFIGURATION.md)
3. **查看启动日志** → 框架会输出详细的配置信息
4. **运行测试用例** → [使用示例 - 测试示例](./EXAMPLES.md#-测试示例)

## 📊 开发统计

### ✅ 完成情况
- **总模块数**：4个
- **总测试数**：60个
- **测试通过率**：100%
- **文档完成度**：100%

### 🎯 核心特性
- ✅ **零侵入** - 基于注解和自动配置
- ✅ **模块化** - 核心 + 插件架构
- ✅ **高性能** - 池化ID生成、权限缓存
- ✅ **企业级** - 多租户、数据权限、审计日志
- ✅ **类型安全** - 完整的泛型设计
- ✅ **可观测** - 完整的日志记录

## 🤝 贡献指南

如果您想为项目做出贡献，请：

1. **阅读文档** - 先了解项目架构和设计理念
2. **运行测试** - 确保所有测试通过
3. **提交Issue** - 报告问题或提出建议
4. **提交PR** - 贡献代码或文档改进

## 📄 许可证

本项目采用 [Apache License 2.0](./LICENSE) 许可证。

---

⭐ **提示**：建议按照以下顺序阅读文档：
1. [README.md](./README.md) - 了解项目概述
2. [QUICK_START.md](./QUICK_START.md) - 快速上手
3. [EXAMPLES.md](./EXAMPLES.md) - 学习使用示例
4. [API.md](./API.md) - 深入了解API
5. [CONFIGURATION.md](./CONFIGURATION.md) - 掌握配置选项

📞 **需要帮助？** 请查看相应的文档章节或提交Issue。
