package cn.com.handthing.starter.dataauth.datalayer.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 忽略数据权限注解
 * <p>
 * 通过在方法或类上声明@IgnoreDataPermission注解，跳过数据权限检查。
 * 通常用于系统管理功能或不需要权限控制的查询方法。
 * </p>
 * 
 * <h3>使用示例：</h3>
 * <pre>
 * &#64;IgnoreDataPermission
 * public List&lt;User&gt; findAllUsers() {
 *     return userMapper.selectList(null);
 * }
 * 
 * &#64;IgnoreDataPermission(reason = "系统初始化数据")
 * public void initSystemData() {
 *     // 初始化系统数据
 * }
 * 
 * &#64;IgnoreDataPermission(onlyForSuperAdmin = true)
 * public void deleteAllData() {
 *     // 只有超级管理员可以执行
 * }
 * </pre>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD, ElementType.TYPE})
@Documented
public @interface IgnoreDataPermission {
    
    /**
     * 忽略权限的原因
     * <p>
     * 用于记录为什么要忽略数据权限，便于代码审查和维护。
     * </p>
     * 
     * @return 忽略原因
     */
    String reason() default "";
    
    /**
     * 是否仅对超级管理员生效
     * <p>
     * 如果为true，只有超级管理员才能忽略权限检查，
     * 普通用户仍然会进行权限验证。
     * </p>
     * 
     * @return 是否仅对超级管理员生效，默认为false
     */
    boolean onlyForSuperAdmin() default false;
    
    /**
     * 是否记录忽略日志
     * <p>
     * 如果为true，会记录权限忽略的日志，用于审计。
     * </p>
     * 
     * @return 是否记录日志，默认为true
     */
    boolean logIgnore() default true;
    
    /**
     * 忽略的权限类型
     * <p>
     * 指定要忽略的权限类型，如果为空则忽略所有权限。
     * </p>
     * 
     * @return 要忽略的权限类型数组
     */
    DataPermission.DataPermissionType[] ignoreTypes() default {};
    
    /**
     * 条件表达式
     * <p>
     * 支持SpEL表达式，用于动态判断是否忽略权限。
     * 例如：#{user.isAdmin()} 或 #{environment.getProperty('app.debug') == 'true'}
     * </p>
     * 
     * @return 条件表达式，默认为空（总是忽略）
     */
    String condition() default "";
}
