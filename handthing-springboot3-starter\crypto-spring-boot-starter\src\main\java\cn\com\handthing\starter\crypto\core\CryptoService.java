package cn.com.handthing.starter.crypto.core;

import cn.com.handthing.starter.crypto.exception.CryptoException;

/**
 * 所有密码学服务的基础接口。
 * <p>
 * 定义了所有处理器必须实现的自检方法，用于健康检查。
 *
 * <AUTHOR>
 * @since V1.0.0
 */
public interface CryptoService {

    /**
     * 执行自检操作以验证处理器是否配置正确且可用。
     * <p>
     * 例如，一个加密器可以尝试加密并解密一段预定义的数据来验证其密钥和算法是否正常工作。
     * 如果自检失败，应抛出异常。
     *
     * @throws CryptoException 如果自检失败
     */
    void selfCheck() throws CryptoException;
}