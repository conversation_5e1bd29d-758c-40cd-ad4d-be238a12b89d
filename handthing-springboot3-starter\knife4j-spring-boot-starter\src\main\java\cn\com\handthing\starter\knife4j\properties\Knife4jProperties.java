package cn.com.handthing.starter.knife4j.properties;

import cn.com.handthing.starter.apidoc.properties.ApiDocProperties;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.io.Serializable;

/**
 * Knife4j 配置属性类
 *
 * <p>继承 ApiDocProperties，实现继承性扩展：
 * <ul>
 * <li>继承 api-doc 的所有基础配置（title, description, version, group, basePackages, auth）</li>
 * <li>添加 Knife4j 特有的配置（contact, ui, enhancement）</li>
 * <li>支持配置覆盖：如果 knife4j 配置了相同属性，则覆盖继承的值</li>
 * </ul>
 *
 * <p>配置路径：handthing.api-doc.knife4j.*
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@ConfigurationProperties(prefix = "handthing.api-doc")
@Data
@EqualsAndHashCode(callSuper = true)
public class Knife4jProperties extends ApiDocProperties {
    private Knife4j knife4j = new Knife4j();

    @Data
    public static class Knife4j implements Serializable {

        /**
         * Knife4j 扩展的联系人信息
         */
        private final Knife4jContact contact = new Knife4jContact();

        /**
         * UI 配置
         */
        private final Ui ui = new Ui();

        /**
         * 增强功能配置
         */
        private final Enhancement enhancement = new Enhancement();
    }



    /**
     * Knife4j 扩展的联系人信息配置
     */
    @Data
    public static class Knife4jContact {
        private String name = "HandThing Team";
        private String email = "<EMAIL>";
        private String url = "https://www.handthing.com";
        private String licenseName = "MIT License";
        private String licenseUrl = "https://opensource.org/licenses/MIT";
    }



    /**
     * UI 配置类
     */
    @Data
    public static class Ui {
        /**
         * 主题：default, dark
         */
        private String theme = "default";

        /**
         * 是否启用搜索功能
         */
        private boolean enableSearch = true;

        /**
         * 是否启用在线调试
         */
        private boolean enableDebug = true;

        /**
         * 是否启用文档导出
         */
        private boolean enableExport = true;

        /**
         * 是否启用接口分组
         */
        private boolean enableGroup = true;

        /**
         * 是否显示 OpenAPI 规范
         */
        private boolean enableOpenApi = true;

        /**
         * 是否启用动态参数
         */
        private boolean enableDynamicParameter = true;
    }

    /**
     * 增强功能配置类
     */
    @Data
    public static class Enhancement {
        /**
         * 是否启用请求缓存
         */
        private boolean enableRequestCache = true;

        /**
         * 是否过滤 multipart 接口
         */
        private boolean enableFilterMultipartApis = false;

        /**
         * 是否启用 Host 显示
         */
        private boolean enableHost = true;

        /**
         * 是否启用接口排序
         */
        private boolean enableSort = true;

        /**
         * 生产环境保护
         */
        private boolean productionSecurityEnabled = true;
    }
}