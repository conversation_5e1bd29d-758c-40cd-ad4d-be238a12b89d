package cn.com.handthing.starter.auth.thirdparty;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 企业微信控制器
 * <p>
 * 提供企业微信认证相关的REST API端点，包括授权URL生成、配置验证等功能。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@RestController
@RequestMapping("${handthing.auth.web.auth-path:/auth}/wecom")
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "handthing.auth.third-party.wecom", name = "endpoints-enabled", havingValue = "true", matchIfMissing = true)
public class WecomController {

    private final WecomApiService wecomApiService;
    private final WecomUserService wecomUserService;

    /**
     * 生成企业微信授权URL
     *
     * @param request 授权请求
     * @return 授权URL
     */
    @PostMapping("/auth-url")
    public ResponseEntity<Map<String, Object>> generateAuthUrl(@RequestBody Map<String, Object> request) {
        try {
            String corpId = (String) request.get("corp_id");
            String agentId = (String) request.get("agent_id");
            String redirectUri = (String) request.get("redirect_uri");
            String state = (String) request.getOrDefault("state", "");
            String authType = (String) request.getOrDefault("auth_type", "web");

            // 验证参数
            if (corpId == null || corpId.trim().isEmpty()) {
                Map<String, Object> response = ResponseUtils.buildErrorResponse("INVALID_CORP_ID", "企业ID不能为空");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
            }

            if (redirectUri == null || redirectUri.trim().isEmpty()) {
                Map<String, Object> response = ResponseUtils.buildErrorResponse("INVALID_REDIRECT_URI", "重定向URI不能为空");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
            }

            // 生成授权URL
            String authUrl = buildAuthUrl(corpId, agentId, redirectUri, state, authType);

            Map<String, Object> response = ResponseUtils.buildSuccessResponse("授权URL生成成功");
            response.put("auth_url", authUrl);
            response.put("corp_id", corpId);
            response.put("agent_id", agentId);
            response.put("auth_type", authType);

            log.info("Generated wecom auth URL for corpId: {}, agentId: {}", corpId, agentId);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Generate auth URL error", e);
            Map<String, Object> response = ResponseUtils.buildErrorResponse("AUTH_URL_ERROR", "生成授权URL失败");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 验证企业微信配置
     *
     * @param request 验证请求
     * @return 验证结果
     */
    @PostMapping("/validate-config")
    public ResponseEntity<Map<String, Object>> validateConfig(@RequestBody Map<String, Object> request) {
        try {
            String corpId = (String) request.get("corp_id");
            String corpSecret = (String) request.get("corp_secret");
            String agentId = (String) request.get("agent_id");

            // 验证参数
            if (corpId == null || corpId.trim().isEmpty()) {
                Map<String, Object> response = ResponseUtils.buildErrorResponse("INVALID_CORP_ID", "企业ID不能为空");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
            }

            if (corpSecret == null || corpSecret.trim().isEmpty()) {
                Map<String, Object> response = ResponseUtils.buildErrorResponse("INVALID_CORP_SECRET", "企业密钥不能为空");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
            }

            // 验证配置
            boolean isValid = wecomApiService.validateConfig(corpId, corpSecret, agentId);

            if (isValid) {
                Map<String, Object> response = ResponseUtils.buildSuccessResponse("企业微信配置验证成功");
                response.put("valid", true);
                response.put("corp_id", corpId);
                response.put("agent_id", agentId);

                log.info("Wecom config validation successful for corpId: {}, agentId: {}", corpId, agentId);
                return ResponseEntity.ok(response);
            } else {
                Map<String, Object> response = ResponseUtils.buildErrorResponse("INVALID_CONFIG", "企业微信配置验证失败");
                response.put("valid", false);
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
            }

        } catch (Exception e) {
            log.error("Validate config error", e);
            Map<String, Object> response = ResponseUtils.buildErrorResponse("CONFIG_VALIDATE_ERROR", "配置验证失败");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 获取企业微信用户信息
     *
     * @param request 用户信息请求
     * @return 用户信息
     */
    @PostMapping("/user-info")
    public ResponseEntity<Map<String, Object>> getUserInfo(@RequestBody Map<String, Object> request) {
        try {
            String corpId = (String) request.get("corp_id");
            String corpSecret = (String) request.get("corp_secret");
            String code = (String) request.get("code");

            // 验证参数
            if (corpId == null || corpSecret == null || code == null) {
                Map<String, Object> response = ResponseUtils.buildErrorResponse("INVALID_PARAMS", "参数不完整");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
            }

            // 获取访问令牌
            WecomApiService.WecomTokenResult tokenResult = wecomApiService.getAccessToken(corpId, corpSecret);
            if (!tokenResult.isSuccess()) {
                Map<String, Object> response = ResponseUtils.buildErrorResponse("TOKEN_ERROR", "获取访问令牌失败: " + tokenResult.getErrorMessage());
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
            }

            // 获取用户信息
            WecomApiService.WecomUserResult userResult = wecomApiService.getUserInfoByCode(tokenResult.getAccessToken(), code);
            if (!userResult.isSuccess()) {
                Map<String, Object> response = ResponseUtils.buildErrorResponse("USER_INFO_ERROR", "获取用户信息失败: " + userResult.getErrorMessage());
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
            }

            // 获取用户详细信息
            WecomApiService.WecomUserDetailResult userDetail = wecomApiService.getUserDetail(tokenResult.getAccessToken(), userResult.getUserId());
            if (!userDetail.isSuccess()) {
                Map<String, Object> response = ResponseUtils.buildErrorResponse("USER_DETAIL_ERROR", "获取用户详细信息失败: " + userDetail.getErrorMessage());
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
            }

            // 构建响应
            Map<String, Object> response = ResponseUtils.buildSuccessResponse("获取用户信息成功");
            response.put("user_id", userDetail.getUserId());
            response.put("name", userDetail.getName());
            response.put("mobile", userDetail.getMobile());
            response.put("email", userDetail.getEmail());
            response.put("gender", userDetail.getGender());
            response.put("avatar", userDetail.getAvatar());
            response.put("position", userDetail.getPosition());
            response.put("department", userDetail.getDepartment());
            response.put("status", userDetail.getStatus());

            log.info("Retrieved wecom user info: userId={}, name={}", userDetail.getUserId(), userDetail.getName());
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Get user info error", e);
            Map<String, Object> response = ResponseUtils.buildErrorResponse("USER_INFO_ERROR", "获取用户信息失败");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 获取企业微信统计信息
     *
     * @return 统计信息
     */
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getStats() {
        try {
            Map<String, Object> stats = new HashMap<>();
            
            if (wecomUserService instanceof DefaultWecomUserService) {
                DefaultWecomUserService defaultService = (DefaultWecomUserService) wecomUserService;
                stats = defaultService.getStatistics();
            } else {
                stats.put("message", "Statistics not available for custom implementation");
            }

            Map<String, Object> response = ResponseUtils.buildSuccessResponse("获取统计信息成功");
            response.put("statistics", stats);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Get stats error", e);
            Map<String, Object> response = ResponseUtils.buildErrorResponse("STATS_ERROR", "获取统计信息失败");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 构建企业微信授权URL
     *
     * @param corpId      企业ID
     * @param agentId     应用ID
     * @param redirectUri 重定向URI
     * @param state       状态参数
     * @param authType    授权类型
     * @return 授权URL
     */
    private String buildAuthUrl(String corpId, String agentId, String redirectUri, String state, String authType) {
        StringBuilder urlBuilder = new StringBuilder();
        
        if ("scan".equals(authType)) {
            // 扫码登录URL
            urlBuilder.append("https://open.work.weixin.qq.com/wwopen/sso/qrConnect");
            urlBuilder.append("?appid=").append(corpId);
            if (agentId != null) {
                urlBuilder.append("&agentid=").append(agentId);
            }
        } else {
            // 网页授权URL
            urlBuilder.append("https://open.weixin.qq.com/connect/oauth2/authorize");
            urlBuilder.append("?appid=").append(corpId);
            urlBuilder.append("&response_type=code");
            urlBuilder.append("&scope=snsapi_base");
        }
        
        urlBuilder.append("&redirect_uri=").append(java.net.URLEncoder.encode(redirectUri, java.nio.charset.StandardCharsets.UTF_8));
        
        if (state != null && !state.trim().isEmpty()) {
            urlBuilder.append("&state=").append(state);
        }
        
        urlBuilder.append("#wechat_redirect");
        
        return urlBuilder.toString();
    }


}
