package cn.com.handthing.starter.tenant.context;

import cn.com.handthing.starter.auth.core.AuthenticationContext;
import cn.com.handthing.starter.auth.core.AuthenticationRequest;
import cn.com.handthing.starter.auth.core.AuthenticationResponse;
import cn.com.handthing.starter.auth.core.GrantType;
import cn.com.handthing.starter.auth.core.UserInfo;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * 租户认证上下文测试
 *
 * <AUTHOR>
 * @since V1.0.0
 */
class TenantAuthenticationContextTest {

    private AuthenticationRequest request;

    @BeforeEach
    void setUp() {
        request = new AuthenticationRequest(GrantType.PASSWORD) {
            @Override
            public String getAuthenticationIdentifier() {
                return "test-user";
            }

            @Override
            public Object getCredentials() {
                return "test-password";
            }
        };
        request.setClientId("test-client");
        request.addExtraParam("username", "test-user");
        request.addExtraParam("password", "test-password");
    }

    @AfterEach
    void tearDown() {
        TenantContextHolder.clearContext();
    }

    @Test
    @DisplayName("应该能够创建租户认证上下文")
    void shouldCreateTenantAuthenticationContext() {
        // Given
        String tenantId = "test-tenant";
        String tenantName = "测试租户";

        // When
        TenantAuthenticationContext context = new TenantAuthenticationContext(request, tenantId, tenantName);

        // Then
        assertThat(context).isNotNull();
        assertThat(context.getTenantId()).isEqualTo(tenantId);
        assertThat(context.getTenantName()).isEqualTo(tenantName);
        assertThat(context.getRequest()).isEqualTo(request);
        assertThat(context.hasTenant()).isTrue();
        assertThat(context.isDefaultTenant()).isFalse();
    }

    @Test
    @DisplayName("应该能够识别默认租户")
    void shouldIdentifyDefaultTenant() {
        // Given
        String defaultTenantId = "default";

        // When
        TenantAuthenticationContext context = new TenantAuthenticationContext(request, defaultTenantId);

        // Then
        assertThat(context.getTenantId()).isEqualTo(defaultTenantId);
        assertThat(context.isDefaultTenant()).isTrue();
    }

    @Test
    @DisplayName("应该能够从基础认证上下文创建租户认证上下文")
    void shouldCreateFromBaseAuthenticationContext() {
        // Given
        AuthenticationContext baseContext = new AuthenticationContext(request);
        baseContext.setProviderName("test-provider");
        baseContext.setSuccess(true);
        
        String tenantId = "test-tenant";

        // When
        TenantAuthenticationContext tenantContext = TenantAuthenticationContext.from(baseContext, tenantId);

        // Then
        assertThat(tenantContext).isNotNull();
        assertThat(tenantContext.getTenantId()).isEqualTo(tenantId);
        assertThat(tenantContext.getRequest()).isEqualTo(request);
        assertThat(tenantContext.getProviderName()).isEqualTo("test-provider");
        assertThat(tenantContext.isSuccess()).isTrue();
    }

    @Test
    @DisplayName("应该能够检查租户状态")
    void shouldCheckTenantStatus() {
        // Given
        String tenantId = "test-tenant";
        String tenantName = "测试租户";
        String tenantStatus = "ACTIVE";

        // When
        TenantAuthenticationContext context = new TenantAuthenticationContext(
                request, tenantId, tenantName, tenantStatus);

        // Then
        assertThat(context.getTenantStatus()).isEqualTo(tenantStatus);
        assertThat(context.isTenantActive()).isTrue();
    }

    @Test
    @DisplayName("应该能够检查非活跃租户状态")
    void shouldCheckInactiveTenantStatus() {
        // Given
        String tenantId = "test-tenant";
        String tenantName = "测试租户";
        String tenantStatus = "INACTIVE";

        // When
        TenantAuthenticationContext context = new TenantAuthenticationContext(
                request, tenantId, tenantName, tenantStatus);

        // Then
        assertThat(context.getTenantStatus()).isEqualTo(tenantStatus);
        assertThat(context.isTenantActive()).isFalse();
    }

    @Test
    @DisplayName("应该能够检查是否为指定租户")
    void shouldCheckIfSpecificTenant() {
        // Given
        String tenantId = "test-tenant";
        TenantAuthenticationContext context = new TenantAuthenticationContext(request, tenantId);

        // When & Then
        assertThat(context.isTenant("test-tenant")).isTrue();
        assertThat(context.isTenant("other-tenant")).isFalse();
        assertThat(context.isTenant(null)).isFalse();
    }

    @Test
    @DisplayName("应该能够获取租户描述信息")
    void shouldGetTenantDescription() {
        // Given
        String tenantId = "test-tenant";
        String tenantName = "测试租户";
        String tenantStatus = "ACTIVE";
        TenantAuthenticationContext context = new TenantAuthenticationContext(
                request, tenantId, tenantName, tenantStatus);

        // When
        String description = context.getTenantDescription();

        // Then
        assertThat(description).contains(tenantId);
        assertThat(description).contains(tenantName);
        assertThat(description).contains(tenantStatus);
    }

    @Test
    @DisplayName("应该能够获取上下文描述信息")
    void shouldGetContextDescription() {
        // Given
        String tenantId = "test-tenant";
        String tenantName = "测试租户";
        TenantAuthenticationContext context = new TenantAuthenticationContext(request, tenantId, tenantName);

        // When
        String description = context.getDescription();

        // Then
        assertThat(description).contains("TenantAuthenticationContext");
        assertThat(description).contains(tenantId);
        assertThat(description).contains(tenantName);
        assertThat(description).contains("password");
        assertThat(description).contains("test-client");
    }

    @Test
    @DisplayName("标记成功时应该记录租户信息")
    void shouldRecordTenantInfoOnSuccess() {
        // Given
        String tenantId = "test-tenant";
        String tenantName = "测试租户";
        TenantAuthenticationContext context = new TenantAuthenticationContext(request, tenantId, tenantName);
        
        AuthenticationResponse response = AuthenticationResponse.success(
                "test-access-token", "test-refresh-token", 7200L);

        UserInfo userInfo = UserInfo.builder()
                .userId("test-user-id")
                .username("test-username")
                .build();

        // When
        context.markSuccess(response, userInfo);

        // Then
        assertThat(context.isSuccess()).isTrue();
        assertThat(context.getAttribute("tenant.id")).isEqualTo(tenantId);
        assertThat(context.getAttribute("tenant.name")).isEqualTo(tenantName);
        assertThat(context.getAttribute("tenant.default")).isEqualTo(false);
    }

    @Test
    @DisplayName("标记失败时应该记录租户信息")
    void shouldRecordTenantInfoOnFailure() {
        // Given
        String tenantId = "test-tenant";
        String tenantName = "测试租户";
        TenantAuthenticationContext context = new TenantAuthenticationContext(request, tenantId, tenantName);
        
        String errorMessage = "Authentication failed";
        Exception exception = new RuntimeException("Test exception");

        // When
        context.markFailure(errorMessage, exception);

        // Then
        assertThat(context.isSuccess()).isFalse();
        assertThat(context.getErrorMessage()).isEqualTo(errorMessage);
        assertThat(context.getAttribute("tenant.id")).isEqualTo(tenantId);
        assertThat(context.getAttribute("tenant.name")).isEqualTo(tenantName);
        assertThat(context.getAttribute("tenant.default")).isEqualTo(false);
    }

    @Test
    @DisplayName("没有租户信息时应该正确处理")
    void shouldHandleNoTenantInfo() {
        // Given
        TenantAuthenticationContext context = new TenantAuthenticationContext(request, null);

        // When & Then
        assertThat(context.hasTenant()).isFalse();
        assertThat(context.getTenantId()).isNull();
        assertThat(context.getTenantName()).isNull();
        assertThat(context.isTenant("any-tenant")).isFalse();
        assertThat(context.isDefaultTenant()).isFalse();
        assertThat(context.getTenantDescription()).isEqualTo("No tenant");
    }

    @Test
    @DisplayName("从当前租户上下文创建认证上下文")
    void shouldCreateFromCurrentTenantContext() {
        // Given
        String tenantId = "current-tenant";
        TenantContext tenant = new TenantContext();
        tenant.setTenantId(tenantId);
        tenant.setTenantName("当前租户");
        tenant.setStatus(TenantStatus.ACTIVE);
        TenantContextHolder.setContext(tenant);

        // When
        TenantAuthenticationContext context = TenantAuthenticationContext.fromCurrentTenant(request);

        // Then
        assertThat(context).isNotNull();
        assertThat(context.getTenantId()).isEqualTo(tenantId);
        assertThat(context.getTenantName()).isEqualTo("当前租户");
        assertThat(context.getTenantStatus()).isEqualTo("ACTIVE");
    }

    @Test
    @DisplayName("toString方法应该返回描述信息")
    void shouldReturnDescriptionInToString() {
        // Given
        String tenantId = "test-tenant";
        TenantAuthenticationContext context = new TenantAuthenticationContext(request, tenantId);

        // When
        String toString = context.toString();

        // Then
        assertThat(toString).isEqualTo(context.getDescription());
        assertThat(toString).contains(tenantId);
    }
}
