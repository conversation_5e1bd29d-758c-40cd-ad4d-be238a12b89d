package cn.com.handthing.starter.auth;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.boot.web.servlet.FilterRegistrationBean;

/**
 * 安全自动配置
 * <p>
 * 配置安全相关的组件，包括过滤器注册、安全头部等。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Configuration
@ConditionalOnProperty(prefix = "handthing.auth", name = "enabled", havingValue = "true", matchIfMissing = true)
public class SecurityAutoConfiguration {

    /**
     * 注册认证过滤器
     *
     * @param authenticationFilter 认证过滤器
     * @param authProperties       认证配置属性
     * @return 过滤器注册Bean
     */
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(prefix = "handthing.auth.web", name = "filter-enabled", havingValue = "true", matchIfMissing = true)
    public FilterRegistrationBean<AuthenticationFilter> authenticationFilterRegistration(
            AuthenticationFilter authenticationFilter,
            AuthProperties authProperties) {
        
        FilterRegistrationBean<AuthenticationFilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(authenticationFilter);
        registration.addUrlPatterns("/*");
        registration.setOrder(authProperties.getWeb().getFilterOrder());
        registration.setName("authenticationFilter");
        
        log.info("Registered authentication filter with order: {}", authProperties.getWeb().getFilterOrder());
        return registration;
    }
}
