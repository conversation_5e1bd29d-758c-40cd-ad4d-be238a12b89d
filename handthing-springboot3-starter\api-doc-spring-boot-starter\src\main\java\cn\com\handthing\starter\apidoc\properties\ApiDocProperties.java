package cn.com.handthing.starter.apidoc.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.ArrayList;
import java.util.List;

/**
 * API 文档配置属性。
 * <p>
 * 允许用户通过 application.yml/.properties 文件进行配置。
 */
@ConfigurationProperties(prefix = "handthing.api-doc")
public class ApiDocProperties {

    /**
     * 是否启用 API 文档功能。
     * 此配置优先级高于 @EnableApiDoc 注解。如果为 false，则文档功能强制关闭。
     */
    private Boolean enabled;

    /**
     * API 文档标题。
     */
    private String title = "Online API Documentation";

    /**
     * API 文档描述。
     */
    private String description = "API documentation for the service";

    /**
     * API 版本号。
     */
    private String version = "1.0.0";

    /**
     * API 分组名称。
     */
    private String group = "default";


    /**
     * API 文档页面的安全认证配置。
     */
    private final Auth auth = new Auth();

    public static class Auth {
        /**
         * 是否为 API 文档页面启用基础认证。
         */
        private boolean enabled = true;

        /**
         * 登录用户名。
         */
        private String username = "admin";

        /**
         * 登录密码。
         */
        private String password = "password"; // 强烈建议用户在生产中修改

        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }

        public String getPassword() {
            return password;
        }

        public void setPassword(String password) {
            this.password = password;
        }
    }




    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getGroup() {
        return group;
    }

    public void setGroup(String group) {
        this.group = group;
    }

    public Auth getAuth() {
        return auth;
    }
}