package cn.com.handthing.starter.connector.bilibili;

import cn.com.handthing.starter.connector.bilibili.api.BilibiliVideoApi;
import cn.com.handthing.starter.connector.bilibili.api.BilibiliCommunityApi;
import cn.com.handthing.starter.connector.bilibili.config.BilibiliConfig;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

/**
 * Bilibili服务
 * <p>
 * 提供Bilibili平台的核心服务功能，包括视频上传、直播管理、社区互动等。
 * 支持个人创作者、企业用户和MCN机构的不同业务场景。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "handthing.connector.bilibili", name = "enabled", havingValue = "true")
public class BilibiliService {

    private final BilibiliConfig config;
    private final BilibiliVideoApi videoApi;
    private final BilibiliCommunityApi communityApi;

    /**
     * 获取视频API
     *
     * @return 视频API实例
     */
    public BilibiliVideoApi video() {
        return videoApi;
    }

    /**
     * 获取社区API
     *
     * @return 社区API实例
     */
    public BilibiliCommunityApi community() {
        return communityApi;
    }

    /**
     * 检查服务是否可用
     *
     * @return 如果可用返回true，否则返回false
     */
    public boolean isAvailable() {
        return config.isEnabled() && config.isValid();
    }

    /**
     * 获取配置信息
     *
     * @return 配置对象
     */
    public BilibiliConfig getConfig() {
        return config;
    }

    /**
     * 获取服务状态
     *
     * @return 服务状态
     */
    public ServiceStatus getStatus() {
        return ServiceStatus.builder()
                .enabled(config.isEnabled())
                .valid(config.isValid())
                .appKey(config.getAppKey())
                .appType(config.getAppType())
                .mcnMode(config.isMcnInstitution())
                .testMode(config.isTestMode())
                .videoUploadEnabled(config.isVideoUploadAvailable())
                .liveEnabled(config.isLiveAvailable())
                .analyticsEnabled(config.isAnalyticsAvailable())
                .communityEnabled(config.isCommunityAvailable())
                .apiBaseUrl(config.getEffectiveApiBaseUrl())
                .build();
    }

    /**
     * 服务状态信息
     */
    @Data
    @lombok.Builder
    public static class ServiceStatus {
        /**
         * 是否启用
         */
        private boolean enabled;

        /**
         * 配置是否有效
         */
        private boolean valid;

        /**
         * 应用Key
         */
        private String appKey;

        /**
         * 应用类型
         */
        private String appType;

        /**
         * 是否为MCN机构模式
         */
        private boolean mcnMode;

        /**
         * 是否为测试模式
         */
        private boolean testMode;

        /**
         * 是否启用视频上传功能
         */
        private boolean videoUploadEnabled;

        /**
         * 是否启用直播功能
         */
        private boolean liveEnabled;

        /**
         * 是否启用数据分析功能
         */
        private boolean analyticsEnabled;

        /**
         * 是否启用社区互动功能
         */
        private boolean communityEnabled;

        /**
         * API基础URL
         */
        private String apiBaseUrl;

        @Override
        public String toString() {
            return String.format("BilibiliServiceStatus{enabled=%s, valid=%s, appKey='%s', appType='%s', " +
                            "mcnMode=%s, testMode=%s, videoUploadEnabled=%s, liveEnabled=%s, " +
                            "analyticsEnabled=%s, communityEnabled=%s, apiBaseUrl='%s'}",
                    enabled, valid, appKey, appType, mcnMode, testMode, 
                    videoUploadEnabled, liveEnabled, analyticsEnabled, communityEnabled, apiBaseUrl);
        }
    }
}
