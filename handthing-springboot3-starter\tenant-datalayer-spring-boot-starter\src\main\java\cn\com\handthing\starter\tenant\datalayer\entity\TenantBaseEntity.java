package cn.com.handthing.starter.tenant.datalayer.entity;

import cn.com.handthing.starter.datalayer.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 租户基础实体类
 * <p>
 * 继承BaseEntity，新增tenantId字段，支持多租户数据隔离。
 * 需要多租户能力的实体应该继承此类。
 * </p>
 * 
 * <h3>租户字段：</h3>
 * <ul>
 *   <li>tenantId - 租户ID，用于数据隔离</li>
 * </ul>
 * 
 * <h3>使用示例：</h3>
 * <pre>
 * &#64;Data
 * &#64;EqualsAndHashCode(callSuper = true)
 * &#64;TableName("products")
 * public class Product extends TenantBaseEntity {
 *     &#64;IdSetter
 *     &#64;TableId
 *     private Long id;
 *     
 *     private String name;
 *     private BigDecimal price;
 * }
 * </pre>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public abstract class TenantBaseEntity extends BaseEntity {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 租户ID
     * <p>
     * 用于多租户数据隔离，在插入时自动填充当前租户ID。
     * 查询时会自动添加租户ID条件，确保数据隔离。
     * </p>
     */
    @TableField(fill = FieldFill.INSERT)
    private String tenantId;
    
    /**
     * 检查是否属于指定租户
     * 
     * @param targetTenantId 目标租户ID
     * @return 如果属于指定租户返回true，否则返回false
     */
    public boolean belongsToTenant(String targetTenantId) {
        return tenantId != null && tenantId.equals(targetTenantId);
    }
    
    /**
     * 检查是否为默认租户
     * 
     * @return 如果是默认租户返回true，否则返回false
     */
    public boolean isDefaultTenant() {
        return "default".equals(tenantId);
    }
    
    /**
     * 检查是否有租户信息
     * 
     * @return 如果有租户ID返回true，否则返回false
     */
    public boolean hasTenant() {
        return tenantId != null && !tenantId.trim().isEmpty();
    }
    
    /**
     * 获取租户信息摘要
     * 
     * @return 租户信息字符串
     */
    public String getTenantSummary() {
        if (!hasTenant()) {
            return "No tenant";
        }
        
        StringBuilder sb = new StringBuilder();
        sb.append("Tenant{id='").append(tenantId).append("'");
        
        if (isDefaultTenant()) {
            sb.append(", default=true");
        }
        
        sb.append("}");
        return sb.toString();
    }
    
    /**
     * 获取完整的审计信息摘要（包含租户信息）
     * 
     * @return 完整审计信息字符串
     */
    public String getFullAuditSummary() {
        StringBuilder sb = new StringBuilder();
        
        // 添加租户信息
        sb.append(getTenantSummary());
        
        // 添加基础审计信息
        String baseAudit = super.getAuditSummary();
        if (!"Audit{}".equals(baseAudit)) {
            sb.append(", ").append(baseAudit);
        }
        
        return sb.toString();
    }
    
    /**
     * 预插入处理（重写以支持租户验证）
     */
    @Override
    public void preInsert() {
        super.preInsert();
        
        // 验证租户ID
        if (!hasTenant()) {
            throw new IllegalStateException("Tenant ID is required for tenant entity");
        }
        
        // 调用租户特定的预插入处理
        doTenantPreInsert();
    }
    
    /**
     * 预更新处理（重写以支持租户验证）
     */
    @Override
    public void preUpdate() {
        super.preUpdate();
        
        // 验证租户ID
        if (!hasTenant()) {
            throw new IllegalStateException("Tenant ID is required for tenant entity");
        }
        
        // 调用租户特定的预更新处理
        doTenantPreUpdate();
    }
    
    /**
     * 租户特定的预插入处理
     * <p>
     * 子类可以重写此方法来实现租户特定的预插入逻辑
     * </p>
     */
    protected void doTenantPreInsert() {
        // 默认实现为空，子类可以重写
    }
    
    /**
     * 租户特定的预更新处理
     * <p>
     * 子类可以重写此方法来实现租户特定的预更新逻辑
     * </p>
     */
    protected void doTenantPreUpdate() {
        // 默认实现为空，子类可以重写
    }
    
    /**
     * 租户特定的后插入处理
     * <p>
     * 子类可以重写此方法来实现租户特定的后插入逻辑
     * </p>
     */
    protected void doTenantPostInsert() {
        // 默认实现为空，子类可以重写
    }
    
    /**
     * 租户特定的后更新处理
     * <p>
     * 子类可以重写此方法来实现租户特定的后更新逻辑
     * </p>
     */
    protected void doTenantPostUpdate() {
        // 默认实现为空，子类可以重写
    }
    
    /**
     * 后插入处理（重写以支持租户处理）
     */
    @Override
    public void postInsert() {
        super.postInsert();
        doTenantPostInsert();
    }
    
    /**
     * 后更新处理（重写以支持租户处理）
     */
    @Override
    public void postUpdate() {
        super.postUpdate();
        doTenantPostUpdate();
    }
    
    /**
     * 设置租户ID（内部方法）
     * <p>
     * 此方法主要供框架内部使用，业务代码不应直接调用
     * </p>
     * 
     * @param tenantId 租户ID
     */
    public void setTenantIdInternal(String tenantId) {
        this.tenantId = tenantId;
    }
    
    /**
     * 验证租户一致性
     * <p>
     * 验证实体的租户ID是否与当前上下文的租户ID一致
     * </p>
     * 
     * @param contextTenantId 上下文租户ID
     * @return 如果一致返回true，否则返回false
     */
    public boolean validateTenantConsistency(String contextTenantId) {
        if (contextTenantId == null && tenantId == null) {
            return true;
        }
        
        if (contextTenantId == null || tenantId == null) {
            return false;
        }
        
        return contextTenantId.equals(tenantId);
    }
    
    /**
     * 克隆实体到指定租户
     * <p>
     * 创建当前实体的副本，并设置为指定租户
     * 注意：这是一个浅拷贝，复杂对象字段需要特殊处理
     * </p>
     * 
     * @param targetTenantId 目标租户ID
     * @return 克隆的实体（需要子类实现具体的克隆逻辑）
     */
    public TenantBaseEntity cloneToTenant(String targetTenantId) {
        throw new UnsupportedOperationException("Clone to tenant not implemented. Subclass should override this method.");
    }
}
