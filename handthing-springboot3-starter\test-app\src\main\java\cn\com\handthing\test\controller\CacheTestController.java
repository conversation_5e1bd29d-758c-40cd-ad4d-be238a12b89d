package cn.com.handthing.test.controller;

import cn.com.handthing.starter.cache.CacheService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.web.bind.annotation.*;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 缓存测试控制器
 * <p>
 * 提供缓存服务的功能测试接口。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/test/cache")
@RequiredArgsConstructor
@ConditionalOnBean(CacheService.class)
public class CacheTestController {

    private final CacheService cacheService;

    /**
     * 缓存服务状态
     *
     * @return 缓存服务状态
     */
    @GetMapping("/status")
    public Map<String, Object> getCacheStatus() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            result.put("success", true);
            result.put("cacheService", cacheService.getClass().getSimpleName());
            result.put("available", true);
            result.put("timestamp", System.currentTimeMillis());
            
        } catch (Exception e) {
            log.error("Failed to get cache status", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    /**
     * 测试缓存基本操作
     *
     * @return 测试结果
     */
    @PostMapping("/test/basic")
    public Map<String, Object> testBasicOperations() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String testKey = "test:basic:" + System.currentTimeMillis();
            String testValue = "test-value-" + ThreadLocalRandom.current().nextInt(1000);
            
            String cacheName = "test-cache";

            // 测试设置缓存
            cacheService.put(cacheName, testKey, testValue);
            result.put("set", true);

            // 测试获取缓存
            String cachedValue = cacheService.get(cacheName, testKey, String.class);
            result.put("get", testValue.equals(cachedValue));
            result.put("cachedValue", cachedValue);

            // 测试删除缓存
            cacheService.evict(cacheName, testKey);
            result.put("delete", true);

            // 验证删除结果
            String deletedValue = cacheService.get(cacheName, testKey, String.class);
            result.put("deletedValueIsNull", deletedValue == null);
            
            result.put("success", true);
            result.put("testKey", testKey);
            result.put("testValue", testValue);
            
        } catch (Exception e) {
            log.error("Failed to test basic cache operations", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    /**
     * 测试缓存TTL操作
     *
     * @return 测试结果
     */
    @PostMapping("/test/ttl")
    public Map<String, Object> testTtlOperations() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String testKey = "test:ttl:" + System.currentTimeMillis();
            String testValue = "ttl-test-value";
            Duration ttl = Duration.ofSeconds(5);
            
            String cacheName = "test-cache";

            // 测试设置带TTL的缓存
            cacheService.put(cacheName, testKey, testValue, ttl);
            result.put("setWithTtl", true);

            // 立即获取
            String cachedValue = cacheService.get(cacheName, testKey, String.class);
            result.put("immediateGet", testValue.equals(cachedValue));

            // 注意：CacheService接口没有getTtl方法，这里跳过TTL检查
            result.put("ttlSet", true);
            result.put("remainingTtlSeconds", ttl.getSeconds());
            
            result.put("success", true);
            result.put("testKey", testKey);
            result.put("testValue", testValue);
            result.put("ttlSeconds", ttl.getSeconds());
            result.put("note", "Key will expire in " + ttl.getSeconds() + " seconds");
            
        } catch (Exception e) {
            log.error("Failed to test TTL cache operations", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    /**
     * 测试缓存对象操作
     *
     * @return 测试结果
     */
    @PostMapping("/test/object")
    public Map<String, Object> testObjectOperations() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String testKey = "test:object:" + System.currentTimeMillis();
            
            // 创建测试对象
            Map<String, Object> testObject = new HashMap<>();
            testObject.put("id", ThreadLocalRandom.current().nextInt(1000));
            testObject.put("name", "test-object");
            testObject.put("timestamp", System.currentTimeMillis());
            testObject.put("active", true);
            
            String cacheName = "test-cache";

            // 测试设置对象缓存
            cacheService.put(cacheName, testKey, testObject);
            result.put("setObject", true);

            // 测试获取对象缓存
            @SuppressWarnings("unchecked")
            Map<String, Object> cachedObject = cacheService.get(cacheName, testKey, Map.class);
            result.put("getObject", cachedObject != null);
            result.put("objectEquals", testObject.equals(cachedObject));
            result.put("cachedObject", cachedObject);

            // 清理
            cacheService.evict(cacheName, testKey);
            
            result.put("success", true);
            result.put("testKey", testKey);
            result.put("testObject", testObject);
            
        } catch (Exception e) {
            log.error("Failed to test object cache operations", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    /**
     * 测试缓存存在性检查
     *
     * @return 测试结果
     */
    @PostMapping("/test/exists")
    public Map<String, Object> testExistsOperations() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String testKey = "test:exists:" + System.currentTimeMillis();
            String testValue = "exists-test-value";
            
            String cacheName = "test-cache";

            // 检查不存在的key
            Object valueBeforeSet = cacheService.get(cacheName, testKey, String.class);
            boolean existsBeforeSet = valueBeforeSet != null;
            result.put("existsBeforeSet", existsBeforeSet);

            // 设置缓存
            cacheService.put(cacheName, testKey, testValue);

            // 检查存在的key
            Object valueAfterSet = cacheService.get(cacheName, testKey, String.class);
            boolean existsAfterSet = valueAfterSet != null;
            result.put("existsAfterSet", existsAfterSet);

            // 删除缓存
            cacheService.evict(cacheName, testKey);

            // 检查删除后的key
            Object valueAfterDelete = cacheService.get(cacheName, testKey, String.class);
            boolean existsAfterDelete = valueAfterDelete != null;
            result.put("existsAfterDelete", existsAfterDelete);
            
            result.put("success", true);
            result.put("testKey", testKey);
            
        } catch (Exception e) {
            log.error("Failed to test exists cache operations", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    /**
     * 综合缓存测试
     *
     * @return 测试结果
     */
    @PostMapping("/test/comprehensive")
    public Map<String, Object> comprehensiveTest() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            Map<String, Object> tests = new HashMap<>();
            
            // 基本操作测试
            Map<String, Object> basicTest = testBasicOperations();
            tests.put("basic", basicTest.get("success"));
            
            // TTL操作测试
            Map<String, Object> ttlTest = testTtlOperations();
            tests.put("ttl", ttlTest.get("success"));
            
            // 对象操作测试
            Map<String, Object> objectTest = testObjectOperations();
            tests.put("object", objectTest.get("success"));
            
            // 存在性检查测试
            Map<String, Object> existsTest = testExistsOperations();
            tests.put("exists", existsTest.get("success"));
            
            // 计算总体结果
            boolean allPassed = tests.values().stream()
                    .allMatch(success -> Boolean.TRUE.equals(success));
            
            result.put("success", allPassed);
            result.put("tests", tests);
            result.put("totalTests", tests.size());
            result.put("passedTests", (int) tests.values().stream().mapToInt(success -> Boolean.TRUE.equals(success) ? 1 : 0).sum());
            result.put("timestamp", System.currentTimeMillis());
            
        } catch (Exception e) {
            log.error("Failed to run comprehensive cache test", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    /**
     * 清理测试缓存
     *
     * @return 清理结果
     */
    @DeleteMapping("/cleanup")
    public Map<String, Object> cleanup() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 注意：这里只是示例，实际实现需要根据具体的缓存实现来清理测试数据
            result.put("success", true);
            result.put("message", "Test cache cleanup completed");
            result.put("note", "This is a placeholder implementation. Actual cleanup depends on cache implementation.");
            result.put("timestamp", System.currentTimeMillis());
            
        } catch (Exception e) {
            log.error("Failed to cleanup test cache", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }
}
