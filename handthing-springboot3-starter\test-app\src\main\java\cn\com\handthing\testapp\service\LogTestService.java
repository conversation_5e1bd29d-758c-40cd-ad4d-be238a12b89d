package cn.com.handthing.testapp.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 日志测试服务
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Service
public class LogTestService {

    public String processMessage(String message) {
        log.info("开始处理消息，测试中文编码: {}", message);

        if (message == null || message.trim().isEmpty()) {
            log.warn("收到空消息，使用默认中文值");
            message = "默认中文消息";
        }

        // 模拟一些业务处理
        String processed = message.toUpperCase() + "_已处理";

        log.debug("消息处理中间结果，包含中文: {}", processed);

        // 模拟一些复杂处理
        try {
            Thread.sleep(50); // 模拟处理时间
        } catch (InterruptedException e) {
            log.error("处理被中断，中文错误信息", e);
            Thread.currentThread().interrupt();
        }

        String result = processed + "_" + System.currentTimeMillis();

        log.info("消息处理完成，最终结果包含中文: {}", result);
        return result;
    }

    public void testDifferentLogLevels() {
        log.trace("这是 TRACE 级别日志，中文编码测试");
        log.debug("这是 DEBUG 级别日志，包含中文字符");
        log.info("这是 INFO 级别日志，测试中文显示");
        log.warn("这是 WARN 级别日志，中文警告信息");
        log.error("这是 ERROR 级别日志，中文错误信息");
    }

    public void testLogWithMDC() {
        // 这个方法可以用来测试 MDC (Mapped Diagnostic Context)
        org.slf4j.MDC.put("userId", "用户12345");
        org.slf4j.MDC.put("requestId", "请求-" + System.currentTimeMillis());

        try {
            log.info("带有 MDC 信息的日志，包含中文");
            log.warn("这条日志也包含 MDC 信息，中文测试");
        } finally {
            org.slf4j.MDC.clear();
        }
    }
}
