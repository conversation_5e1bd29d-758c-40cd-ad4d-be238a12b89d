package cn.com.handthing.starter.id.exception;

/**
 * ID生成异常
 * <p>
 * 当ID生成过程中发生错误时抛出此异常。
 * 这是一个运行时异常，调用方可以选择是否捕获处理。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
public class IdGenerationException extends RuntimeException {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 生成器名称
     */
    private final String generatorName;
    
    /**
     * 错误代码
     */
    private final String errorCode;
    
    /**
     * 构造函数
     *
     * @param message 错误信息
     */
    public IdGenerationException(String message) {
        super(message);
        this.generatorName = null;
        this.errorCode = null;
    }
    
    /**
     * 构造函数
     *
     * @param message 错误信息
     * @param cause   原因异常
     */
    public IdGenerationException(String message, Throwable cause) {
        super(message, cause);
        this.generatorName = null;
        this.errorCode = null;
    }
    
    /**
     * 构造函数
     *
     * @param generatorName 生成器名称
     * @param message       错误信息
     */
    public IdGenerationException(String generatorName, String message) {
        super(message);
        this.generatorName = generatorName;
        this.errorCode = null;
    }
    
    /**
     * 构造函数
     *
     * @param generatorName 生成器名称
     * @param message       错误信息
     * @param cause         原因异常
     */
    public IdGenerationException(String generatorName, String message, Throwable cause) {
        super(message, cause);
        this.generatorName = generatorName;
        this.errorCode = null;
    }
    
    /**
     * 构造函数
     *
     * @param generatorName 生成器名称
     * @param errorCode     错误代码
     * @param message       错误信息
     */
    public IdGenerationException(String generatorName, String errorCode, String message) {
        super(message);
        this.generatorName = generatorName;
        this.errorCode = errorCode;
    }
    
    /**
     * 构造函数
     *
     * @param generatorName 生成器名称
     * @param errorCode     错误代码
     * @param message       错误信息
     * @param cause         原因异常
     */
    public IdGenerationException(String generatorName, String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.generatorName = generatorName;
        this.errorCode = errorCode;
    }
    
    /**
     * 获取生成器名称
     *
     * @return 生成器名称
     */
    public String getGeneratorName() {
        return generatorName;
    }
    
    /**
     * 获取错误代码
     *
     * @return 错误代码
     */
    public String getErrorCode() {
        return errorCode;
    }
    
    /**
     * 获取详细错误信息
     *
     * @return 详细错误信息
     */
    public String getDetailMessage() {
        StringBuilder sb = new StringBuilder();
        
        if (generatorName != null) {
            sb.append("Generator: ").append(generatorName);
        }
        
        if (errorCode != null) {
            if (sb.length() > 0) {
                sb.append(", ");
            }
            sb.append("ErrorCode: ").append(errorCode);
        }
        
        if (sb.length() > 0) {
            sb.append(", ");
        }
        sb.append("Message: ").append(getMessage());
        
        return sb.toString();
    }
    
    @Override
    public String toString() {
        return "IdGenerationException{" +
                "generatorName='" + generatorName + '\'' +
                ", errorCode='" + errorCode + '\'' +
                ", message='" + getMessage() + '\'' +
                '}';
    }
}
