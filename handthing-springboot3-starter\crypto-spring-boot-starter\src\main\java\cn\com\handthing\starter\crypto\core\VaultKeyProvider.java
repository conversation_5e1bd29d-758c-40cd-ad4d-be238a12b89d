package cn.com.handthing.starter.crypto.core;

import cn.com.handthing.starter.crypto.exception.CryptoException;
import cn.com.handthing.starter.crypto.properties.CryptoProperties.KeyStoreConfig;
import cn.com.handthing.starter.crypto.properties.CryptoProperties.VaultConfig;
import org.springframework.context.ApplicationContext;
import org.springframework.util.Assert;
import org.springframework.vault.core.VaultOperations;
import org.springframework.vault.support.VaultResponse;

import java.util.Base64;
import java.util.Map;
import java.util.Optional;

/**
 * 从 Vault 加载密钥的 KeyProvider。
 *
 * <AUTHOR>
 * @since V1.0.0
 */
class VaultKeyProvider extends AbstractKeyProvider {

    private final byte[] keyBytes;

    public VaultKeyProvider(KeyStoreConfig config, ApplicationContext context) {
        super(config);
        VaultConfig vaultConfig = Optional.ofNullable(config.getVault())
                .orElseThrow(() -> new CryptoException("Vault config is missing for KeyStoreType.VAULT"));
        
        validateVaultConfig(vaultConfig);

        try {
            // VaultOperations 是可选依赖，所以我们从上下文中动态获取
            VaultOperations vaultOperations = context.getBean(VaultOperations.class);
            VaultResponse response = vaultOperations.read(vaultConfig.getSecretPath());

            if (response == null || response.getData() == null) {
                throw new CryptoException("Secret not found at Vault path: " + vaultConfig.getSecretPath());
            }

            Map<String, Object> secretData = response.getData();
            Object keyValue = secretData.get(vaultConfig.getSecretKey());

            if (keyValue == null) {
                throw new CryptoException("Key '" + vaultConfig.getSecretKey() + "' not found in secret at path: " + vaultConfig.getSecretPath());
            }

            this.keyBytes = Base64.getDecoder().decode(keyValue.toString());
            this.ready = true;
            
        } catch (Exception e) {
            throw new CryptoException("Failed to retrieve key from Vault", e);
        }
    }

    private void validateVaultConfig(VaultConfig vaultConfig) {
        Assert.hasText(vaultConfig.getSecretPath(), "vault.secret-path must not be empty");
        Assert.hasText(vaultConfig.getSecretKey(), "vault.secret-key must not be empty");
    }

    @Override
    public byte[] getKeyBytes() {
        return this.keyBytes;
    }
}