package cn.com.handthing.starter.connector.dingtalk;

import cn.com.handthing.starter.connector.PlatformType;
import cn.com.handthing.starter.connector.dingtalk.config.DingTalkConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.client.RestTemplate;

import static org.junit.jupiter.api.Assertions.*;

/**
 * DingTalkAuthProvider 单元测试
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@ExtendWith(MockitoExtension.class)
class DingTalkAuthProviderTest {

    @Mock
    private RestTemplate restTemplate;

    private DingTalkConfig config;
    private DingTalkAuthProvider authProvider;

    @BeforeEach
    void setUp() {
        config = new DingTalkConfig();
        config.setEnabled(true);
        config.setAppKey("test-app-key");
        config.setAppSecret("test-app-secret");
        config.setCorpId("test-corp-id");
        config.setRedirectUri("https://test.com/callback");
        
        authProvider = new DingTalkAuthProvider(config, restTemplate);
    }

    @Test
    void testGetPlatformType() {
        assertEquals(PlatformType.DINGTALK, authProvider.getPlatformType());
    }

    @Test
    void testSupportsRefreshToken() {
        assertTrue(authProvider.supportsRefreshToken());
    }

    @Test
    void testGetDefaultScope() {
        assertEquals("openid", authProvider.getDefaultScope());
    }

    @Test
    void testGetAuthorizationUrl() {
        String state = "test-state";
        String redirectUri = "https://test.com/callback";
        
        String authUrl = authProvider.getAuthorizationUrl(state, redirectUri);
        
        assertNotNull(authUrl);
        assertTrue(authUrl.contains("client_id=" + config.getAppKey()));
        assertTrue(authUrl.contains("state=" + state));
        assertTrue(authUrl.contains("redirect_uri="));
        assertTrue(authUrl.contains("response_type=code"));
        assertTrue(authUrl.contains("scope=openid"));
        assertTrue(authUrl.contains("prompt=consent"));
    }

    @Test
    void testGetAuthorizationUrlWithNullState() {
        String redirectUri = "https://test.com/callback";
        
        String authUrl = authProvider.getAuthorizationUrl(null, redirectUri);
        
        assertNotNull(authUrl);
        assertTrue(authUrl.contains("state="));
    }

    @Test
    void testGetAuthorizationUrlWithDefaultRedirectUri() {
        String state = "test-state";
        
        String authUrl = authProvider.getAuthorizationUrl(state, null);
        
        assertNotNull(authUrl);
        assertTrue(authUrl.contains("redirect_uri="));
    }

    @Test
    void testConfigValidation() {
        // 测试有效配置
        assertTrue(config.isValid());
        
        // 测试无效配置
        DingTalkConfig invalidConfig = new DingTalkConfig();
        invalidConfig.setEnabled(true);
        assertFalse(invalidConfig.isValid());
        
        invalidConfig.setAppKey("test-app-key");
        assertFalse(invalidConfig.isValid());
        
        invalidConfig.setAppSecret("test-app-secret");
        assertFalse(invalidConfig.isValid());
        
        invalidConfig.setCorpId("test-corp-id");
        assertTrue(invalidConfig.isValid());
    }

    @Test
    void testConfigUrls() {
        assertEquals("https://api.dingtalk.com", config.getEffectiveApiBaseUrl());
        assertEquals("https://login.dingtalk.com/oauth2/auth", config.getEffectiveAuthUrl());
        
        assertNotNull(config.getTokenUrl());
        assertNotNull(config.getUserInfoUrl());
        assertNotNull(config.getDepartmentListUrl());
        assertNotNull(config.getSendWorkNoticeUrl());
    }

    @Test
    void testInternalMode() {
        assertTrue(config.isInternalMode());
        
        config.setInternalApp(false);
        assertFalse(config.isInternalMode());
    }

    @Test
    void testThirdPartyMode() {
        assertFalse(config.isThirdPartyMode());
        
        config.setThirdPartyApp(true);
        config.setSuiteKey("test-suite-key");
        config.setSuiteSecret("test-suite-secret");
        
        assertTrue(config.isThirdPartyMode());
    }

    @Test
    void testCallbackConfiguration() {
        assertFalse(config.isCallbackEnabled());
        
        config.setEnableCallback(true);
        config.setToken("test-token");
        config.setEncodingAESKey("test-aes-key");
        
        assertTrue(config.isCallbackEnabled());
    }
}
