package cn.com.handthing.starter.auth.thirdparty;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 飞书控制器
 * <p>
 * 提供飞书认证相关的REST API端点，包括授权URL生成、配置验证等功能。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@RestController
@RequestMapping("${handthing.auth.web.auth-path:/auth}/feishu")
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "handthing.auth.third-party.feishu", name = "endpoints-enabled", havingValue = "true", matchIfMissing = true)
public class FeishuController {

    private final FeishuApiService feishuApiService;
    private final FeishuUserService feishuUserService;

    /**
     * 生成飞书授权URL
     *
     * @param request 授权请求
     * @return 授权URL
     */
    @PostMapping("/auth-url")
    public ResponseEntity<Map<String, Object>> generateAuthUrl(@RequestBody Map<String, Object> request) {
        try {
            String appId = (String) request.get("app_id");
            String redirectUri = (String) request.get("redirect_uri");
            String state = (String) request.getOrDefault("state", "");
            String authType = (String) request.getOrDefault("auth_type", "web");

            // 验证参数
            if (appId == null || appId.trim().isEmpty()) {
                Map<String, Object> response = buildErrorResponse("INVALID_APP_ID", "应用ID不能为空");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
            }

            if (redirectUri == null || redirectUri.trim().isEmpty()) {
                Map<String, Object> response = buildErrorResponse("INVALID_REDIRECT_URI", "重定向URI不能为空");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
            }

            // 生成授权URL
            String authUrl = buildAuthUrl(appId, redirectUri, state, authType);

            Map<String, Object> response = buildSuccessResponse("授权URL生成成功");
            response.put("auth_url", authUrl);
            response.put("app_id", appId);
            response.put("auth_type", authType);

            log.info("Generated feishu auth URL for appId: {}", appId);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Generate auth URL error", e);
            Map<String, Object> response = buildErrorResponse("AUTH_URL_ERROR", "生成授权URL失败");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 验证飞书配置
     *
     * @param request 验证请求
     * @return 验证结果
     */
    @PostMapping("/validate-config")
    public ResponseEntity<Map<String, Object>> validateConfig(@RequestBody Map<String, Object> request) {
        try {
            String appId = (String) request.get("app_id");
            String appSecret = (String) request.get("app_secret");

            // 验证参数
            if (appId == null || appId.trim().isEmpty()) {
                Map<String, Object> response = buildErrorResponse("INVALID_APP_ID", "应用ID不能为空");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
            }

            if (appSecret == null || appSecret.trim().isEmpty()) {
                Map<String, Object> response = buildErrorResponse("INVALID_APP_SECRET", "应用密钥不能为空");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
            }

            // 验证配置
            boolean isValid = feishuApiService.validateConfig(appId, appSecret);

            if (isValid) {
                Map<String, Object> response = buildSuccessResponse("飞书配置验证成功");
                response.put("valid", true);
                response.put("app_id", appId);

                log.info("Feishu config validation successful for appId: {}", appId);
                return ResponseEntity.ok(response);
            } else {
                Map<String, Object> response = buildErrorResponse("INVALID_CONFIG", "飞书配置验证失败");
                response.put("valid", false);
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
            }

        } catch (Exception e) {
            log.error("Validate config error", e);
            Map<String, Object> response = buildErrorResponse("CONFIG_VALIDATE_ERROR", "配置验证失败");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 获取飞书用户信息
     *
     * @param request 用户信息请求
     * @return 用户信息
     */
    @PostMapping("/user-info")
    public ResponseEntity<Map<String, Object>> getUserInfo(@RequestBody Map<String, Object> request) {
        try {
            String appId = (String) request.get("app_id");
            String appSecret = (String) request.get("app_secret");
            String code = (String) request.get("code");

            // 验证参数
            if (appId == null || appSecret == null || code == null) {
                Map<String, Object> response = buildErrorResponse("INVALID_PARAMS", "参数不完整");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
            }

            // 获取应用访问令牌
            FeishuApiService.FeishuAppTokenResult appTokenResult = feishuApiService.getAppAccessToken(appId, appSecret);
            if (!appTokenResult.isSuccess()) {
                Map<String, Object> response = buildErrorResponse("APP_TOKEN_ERROR", "获取应用访问令牌失败: " + appTokenResult.getErrorMessage());
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
            }

            // 获取用户访问令牌
            FeishuApiService.FeishuUserTokenResult userTokenResult = feishuApiService.getUserAccessToken(appTokenResult.getAppAccessToken(), code);
            if (!userTokenResult.isSuccess()) {
                Map<String, Object> response = buildErrorResponse("USER_TOKEN_ERROR", "获取用户访问令牌失败: " + userTokenResult.getErrorMessage());
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
            }

            // 获取用户信息
            FeishuApiService.FeishuUserInfoResult userInfo = feishuApiService.getUserInfo(userTokenResult.getAccessToken());
            if (!userInfo.isSuccess()) {
                Map<String, Object> response = buildErrorResponse("USER_INFO_ERROR", "获取用户信息失败: " + userInfo.getErrorMessage());
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
            }

            // 构建响应
            Map<String, Object> response = buildSuccessResponse("获取用户信息成功");
            response.put("user_id", userInfo.getUserId());
            response.put("union_id", userInfo.getUnionId());
            response.put("open_id", userInfo.getOpenId());
            response.put("name", userInfo.getName());
            response.put("en_name", userInfo.getEnName());
            response.put("nickname", userInfo.getNickname());
            response.put("email", userInfo.getEmail());
            response.put("mobile", userInfo.getMobile());
            response.put("avatar", userInfo.getAvatar());
            response.put("status", userInfo.getStatus());
            response.put("employee_type", userInfo.getEmployeeType());
            response.put("department_ids", userInfo.getDepartmentIds());
            response.put("tenant_key", userInfo.getTenantKey());
            response.put("custom_attrs", userInfo.getCustomAttrs());

            log.info("Retrieved feishu user info: userId={}, name={}", userInfo.getUserId(), userInfo.getName());
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Get user info error", e);
            Map<String, Object> response = buildErrorResponse("USER_INFO_ERROR", "获取用户信息失败");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 获取飞书统计信息
     *
     * @return 统计信息
     */
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getStats() {
        try {
            Map<String, Object> stats = new HashMap<>();
            
            if (feishuUserService instanceof DefaultFeishuUserService) {
                DefaultFeishuUserService defaultService = (DefaultFeishuUserService) feishuUserService;
                stats = defaultService.getStatistics();
            } else {
                stats.put("message", "Statistics not available for custom implementation");
            }

            Map<String, Object> response = buildSuccessResponse("获取统计信息成功");
            response.put("statistics", stats);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Get stats error", e);
            Map<String, Object> response = buildErrorResponse("STATS_ERROR", "获取统计信息失败");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 构建飞书授权URL
     *
     * @param appId       应用ID
     * @param redirectUri 重定向URI
     * @param state       状态参数
     * @param authType    授权类型
     * @return 授权URL
     */
    private String buildAuthUrl(String appId, String redirectUri, String state, String authType) {
        StringBuilder urlBuilder = new StringBuilder();
        
        if ("mobile".equals(authType)) {
            // 移动端授权URL
            urlBuilder.append("https://open.feishu.cn/open-apis/authen/v1/authorize");
        } else {
            // 网页授权URL
            urlBuilder.append("https://open.feishu.cn/open-apis/authen/v1/authorize");
        }
        
        urlBuilder.append("?app_id=").append(appId);
        urlBuilder.append("&response_type=code");
        urlBuilder.append("&redirect_uri=").append(java.net.URLEncoder.encode(redirectUri, java.nio.charset.StandardCharsets.UTF_8));
        
        if (state != null && !state.trim().isEmpty()) {
            urlBuilder.append("&state=").append(state);
        }
        
        return urlBuilder.toString();
    }

    /**
     * 构建成功响应
     *
     * @param message 消息
     * @return 响应Map
     */
    private Map<String, Object> buildSuccessResponse(String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", message);
        response.put("timestamp", LocalDateTime.now());
        return response;
    }

    /**
     * 构建错误响应
     *
     * @param errorCode        错误代码
     * @param errorDescription 错误描述
     * @return 响应Map
     */
    private Map<String, Object> buildErrorResponse(String errorCode, String errorDescription) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("error", errorCode);
        response.put("error_description", errorDescription);
        response.put("timestamp", LocalDateTime.now());
        return response;
    }
}
