package cn.com.handthing.starter.connector.alipay.util;

import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.Signature;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 支付宝签名工具类
 * <p>
 * 提供支付宝API调用所需的签名生成和验证功能，
 * 支持RSA和RSA2签名算法。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
public class AlipaySignatureUtil {

    private static final String RSA_ALGORITHM = "RSA";
    private static final String RSA2_ALGORITHM = "RSA";
    private static final String RSA_SIGN_ALGORITHM = "SHA1WithRSA";
    private static final String RSA2_SIGN_ALGORITHM = "SHA256WithRSA";

    /**
     * 生成签名
     *
     * @param params     参数Map
     * @param privateKey 私钥字符串
     * @param signType   签名类型（RSA、RSA2）
     * @return 签名字符串
     * @throws Exception 如果签名失败
     */
    public static String generateSignature(Map<String, String> params, String privateKey, String signType) throws Exception {
        // 1. 排序并拼接参数
        String content = buildSignContent(params);
        
        // 2. 生成签名
        return sign(content, privateKey, signType);
    }

    /**
     * 验证签名
     *
     * @param params    参数Map
     * @param publicKey 公钥字符串
     * @param sign      签名字符串
     * @param signType  签名类型（RSA、RSA2）
     * @return 验证结果
     * @throws Exception 如果验证失败
     */
    public static boolean verifySignature(Map<String, String> params, String publicKey, String sign, String signType) throws Exception {
        // 1. 排序并拼接参数
        String content = buildSignContent(params);
        
        // 2. 验证签名
        return verify(content, sign, publicKey, signType);
    }

    /**
     * 构建签名内容
     *
     * @param params 参数Map
     * @return 签名内容字符串
     */
    private static String buildSignContent(Map<String, String> params) {
        return params.entrySet().stream()
                .filter(entry -> entry.getValue() != null && !entry.getValue().isEmpty())
                .filter(entry -> !"sign".equals(entry.getKey()) && !"sign_type".equals(entry.getKey()))
                .sorted(Map.Entry.comparingByKey())
                .map(entry -> entry.getKey() + "=" + entry.getValue())
                .collect(Collectors.joining("&"));
    }

    /**
     * 签名
     *
     * @param content    待签名内容
     * @param privateKey 私钥字符串
     * @param signType   签名类型
     * @return 签名字符串
     * @throws Exception 如果签名失败
     */
    private static String sign(String content, String privateKey, String signType) throws Exception {
        try {
            // 解析私钥
            PrivateKey priKey = getPrivateKeyFromString(privateKey);
            
            // 选择签名算法
            String algorithm = "RSA2".equals(signType) ? RSA2_SIGN_ALGORITHM : RSA_SIGN_ALGORITHM;
            
            // 执行签名
            Signature signature = Signature.getInstance(algorithm);
            signature.initSign(priKey);
            signature.update(content.getBytes(StandardCharsets.UTF_8));
            
            byte[] signed = signature.sign();
            return Base64.getEncoder().encodeToString(signed);
            
        } catch (Exception e) {
            log.error("Failed to generate signature for content: {}", content, e);
            throw new Exception("Failed to generate signature", e);
        }
    }

    /**
     * 验证签名
     *
     * @param content   待验证内容
     * @param sign      签名字符串
     * @param publicKey 公钥字符串
     * @param signType  签名类型
     * @return 验证结果
     * @throws Exception 如果验证失败
     */
    private static boolean verify(String content, String sign, String publicKey, String signType) throws Exception {
        try {
            // 解析公钥
            PublicKey pubKey = getPublicKeyFromString(publicKey);
            
            // 选择签名算法
            String algorithm = "RSA2".equals(signType) ? RSA2_SIGN_ALGORITHM : RSA_SIGN_ALGORITHM;
            
            // 执行验证
            Signature signature = Signature.getInstance(algorithm);
            signature.initVerify(pubKey);
            signature.update(content.getBytes(StandardCharsets.UTF_8));
            
            return signature.verify(Base64.getDecoder().decode(sign));
            
        } catch (Exception e) {
            log.error("Failed to verify signature for content: {}", content, e);
            throw new Exception("Failed to verify signature", e);
        }
    }

    /**
     * 从字符串获取私钥
     *
     * @param privateKeyStr 私钥字符串
     * @return 私钥对象
     * @throws Exception 如果解析失败
     */
    private static PrivateKey getPrivateKeyFromString(String privateKeyStr) throws Exception {
        // 移除私钥头尾标识和换行符
        String privateKeyContent = privateKeyStr
                .replace("-----BEGIN PRIVATE KEY-----", "")
                .replace("-----END PRIVATE KEY-----", "")
                .replace("-----BEGIN RSA PRIVATE KEY-----", "")
                .replace("-----END RSA PRIVATE KEY-----", "")
                .replaceAll("\\s", "");
        
        byte[] keyBytes = Base64.getDecoder().decode(privateKeyContent);
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
        return keyFactory.generatePrivate(keySpec);
    }

    /**
     * 从字符串获取公钥
     *
     * @param publicKeyStr 公钥字符串
     * @return 公钥对象
     * @throws Exception 如果解析失败
     */
    private static PublicKey getPublicKeyFromString(String publicKeyStr) throws Exception {
        // 移除公钥头尾标识和换行符
        String publicKeyContent = publicKeyStr
                .replace("-----BEGIN PUBLIC KEY-----", "")
                .replace("-----END PUBLIC KEY-----", "")
                .replaceAll("\\s", "");
        
        byte[] keyBytes = Base64.getDecoder().decode(publicKeyContent);
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
        return keyFactory.generatePublic(keySpec);
    }

    /**
     * 格式化私钥字符串
     *
     * @param privateKey 原始私钥字符串
     * @return 格式化后的私钥字符串
     */
    public static String formatPrivateKey(String privateKey) {
        if (privateKey == null || privateKey.trim().isEmpty()) {
            return privateKey;
        }
        
        String key = privateKey.trim();
        if (!key.startsWith("-----BEGIN")) {
            key = "-----BEGIN PRIVATE KEY-----\n" + key + "\n-----END PRIVATE KEY-----";
        }
        return key;
    }

    /**
     * 格式化公钥字符串
     *
     * @param publicKey 原始公钥字符串
     * @return 格式化后的公钥字符串
     */
    public static String formatPublicKey(String publicKey) {
        if (publicKey == null || publicKey.trim().isEmpty()) {
            return publicKey;
        }
        
        String key = publicKey.trim();
        if (!key.startsWith("-----BEGIN")) {
            key = "-----BEGIN PUBLIC KEY-----\n" + key + "\n-----END PUBLIC KEY-----";
        }
        return key;
    }
}
