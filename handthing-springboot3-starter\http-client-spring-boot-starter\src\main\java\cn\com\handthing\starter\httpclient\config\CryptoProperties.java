package cn.com.handthing.starter.httpclient.config;

import java.util.Objects;

/**
 * 请求和响应加解密配置属性。
 * <p>
 * 这只是一个开关，具体的加解密逻辑由用户提供的 RequestEncryptor 和 ResponseDecryptor Bean 实现。
 */
public class CryptoProperties {

    /**
     * 是否启用加解密功能。
     * <p>
     * 如果为 true，starter会尝试从Spring上下文中寻找 RequestEncryptor 和 ResponseDecryptor Bean 并应用它们。
     */
    private boolean enabled = false;

    // --- Standard Getters and Setters, equals, hashCode, toString ---

    public CryptoProperties() {
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CryptoProperties that = (CryptoProperties) o;
        return enabled == that.enabled;
    }

    @Override
    public int hashCode() {
        return Objects.hash(enabled);
    }

    @Override
    public String toString() {
        return "CryptoProperties{" +
                "enabled=" + enabled +
                '}';
    }
}