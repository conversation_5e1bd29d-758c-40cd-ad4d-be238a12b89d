package cn.com.handthing.starter.connector.exception;

import cn.com.handthing.starter.connector.PlatformType;

/**
 * 认证异常类
 * <p>
 * 专门处理认证相关的错误，如授权失败、令牌无效、用户拒绝授权等。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
public class AuthException extends ConnectorException {

    private static final long serialVersionUID = 1L;

    /**
     * 常用错误码
     */
    public static final String INVALID_CODE = "INVALID_CODE";
    public static final String INVALID_TOKEN = "INVALID_TOKEN";
    public static final String TOKEN_EXPIRED = "TOKEN_EXPIRED";
    public static final String REFRESH_TOKEN_EXPIRED = "REFRESH_TOKEN_EXPIRED";
    public static final String USER_DENIED = "USER_DENIED";
    public static final String INVALID_STATE = "INVALID_STATE";
    public static final String PLATFORM_NOT_SUPPORTED = "PLATFORM_NOT_SUPPORTED";
    public static final String CONFIGURATION_ERROR = "CONFIGURATION_ERROR";
    public static final String NETWORK_ERROR = "NETWORK_ERROR";
    public static final String PARSE_ERROR = "PARSE_ERROR";

    /**
     * 构造函数
     *
     * @param message 错误消息
     */
    public AuthException(String message) {
        super(message);
    }

    /**
     * 构造函数
     *
     * @param message 错误消息
     * @param cause   原因异常
     */
    public AuthException(String message, Throwable cause) {
        super(message, cause);
    }

    /**
     * 构造函数
     *
     * @param message   错误消息
     * @param errorCode 错误码
     */
    public AuthException(String message, String errorCode) {
        super(message, errorCode);
    }

    /**
     * 构造函数
     *
     * @param message   错误消息
     * @param errorCode 错误码
     * @param cause     原因异常
     */
    public AuthException(String message, String errorCode, Throwable cause) {
        super(message, errorCode, cause);
    }

    /**
     * 构造函数
     *
     * @param message   错误消息
     * @param errorCode 错误码
     * @param platform  平台类型
     */
    public AuthException(String message, String errorCode, PlatformType platform) {
        super(message, errorCode, platform);
    }

    /**
     * 构造函数
     *
     * @param message   错误消息
     * @param errorCode 错误码
     * @param platform  平台类型
     * @param cause     原因异常
     */
    public AuthException(String message, String errorCode, PlatformType platform, Throwable cause) {
        super(message, errorCode, platform, cause);
    }

    /**
     * 完整构造函数
     *
     * @param message   错误消息
     * @param errorCode 错误码
     * @param platform  平台类型
     * @param errorData 额外错误信息
     * @param cause     原因异常
     */
    public AuthException(String message, String errorCode, PlatformType platform, Object errorData, Throwable cause) {
        super(message, errorCode, platform, errorData, cause);
    }

    // 静态工厂方法，用于创建常见的认证异常

    /**
     * 创建无效授权码异常
     *
     * @param platform 平台类型
     * @return 认证异常
     */
    public static AuthException invalidCode(PlatformType platform) {
        return new AuthException("Invalid authorization code", INVALID_CODE, platform);
    }

    /**
     * 创建无效令牌异常
     *
     * @param platform 平台类型
     * @return 认证异常
     */
    public static AuthException invalidToken(PlatformType platform) {
        return new AuthException("Invalid access token", INVALID_TOKEN, platform);
    }

    /**
     * 创建令牌过期异常
     *
     * @param platform 平台类型
     * @return 认证异常
     */
    public static AuthException tokenExpired(PlatformType platform) {
        return new AuthException("Access token has expired", TOKEN_EXPIRED, platform);
    }

    /**
     * 创建刷新令牌过期异常
     *
     * @param platform 平台类型
     * @return 认证异常
     */
    public static AuthException refreshTokenExpired(PlatformType platform) {
        return new AuthException("Refresh token has expired", REFRESH_TOKEN_EXPIRED, platform);
    }

    /**
     * 创建用户拒绝授权异常
     *
     * @param platform 平台类型
     * @return 认证异常
     */
    public static AuthException userDenied(PlatformType platform) {
        return new AuthException("User denied authorization", USER_DENIED, platform);
    }

    /**
     * 创建无效状态参数异常
     *
     * @param platform 平台类型
     * @return 认证异常
     */
    public static AuthException invalidState(PlatformType platform) {
        return new AuthException("Invalid state parameter", INVALID_STATE, platform);
    }

    /**
     * 创建平台不支持异常
     *
     * @param platform 平台类型
     * @return 认证异常
     */
    public static AuthException platformNotSupported(PlatformType platform) {
        return new AuthException("Platform not supported: " + platform, PLATFORM_NOT_SUPPORTED, platform);
    }

    /**
     * 创建配置错误异常
     *
     * @param platform 平台类型
     * @param message  错误消息
     * @return 认证异常
     */
    public static AuthException configurationError(PlatformType platform, String message) {
        return new AuthException("Configuration error: " + message, CONFIGURATION_ERROR, platform);
    }

    /**
     * 创建网络错误异常
     *
     * @param platform 平台类型
     * @param cause    原因异常
     * @return 认证异常
     */
    public static AuthException networkError(PlatformType platform, Throwable cause) {
        return new AuthException("Network error occurred", NETWORK_ERROR, platform, cause);
    }

    /**
     * 创建解析错误异常
     *
     * @param platform 平台类型
     * @param cause    原因异常
     * @return 认证异常
     */
    public static AuthException parseError(PlatformType platform, Throwable cause) {
        return new AuthException("Failed to parse response", PARSE_ERROR, platform, cause);
    }
}
