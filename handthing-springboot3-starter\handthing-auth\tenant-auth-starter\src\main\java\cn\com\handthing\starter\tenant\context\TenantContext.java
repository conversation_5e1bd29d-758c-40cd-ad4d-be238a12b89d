package cn.com.handthing.starter.tenant.context;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 租户上下文信息
 * <p>
 * 封装租户ID、租户名称、租户状态等信息，提供租户信息的验证和格式化方法。
 * 用于在认证流程中传递和共享租户相关数据。
 * </p>
 *
 * <AUTHOR>
 * @since V3.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TenantContext {

    /**
     * 租户ID - 核心标识
     */
    private String tenantId;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 租户状态
     */
    private TenantStatus status;

    /**
     * 租户创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 租户最后更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 扩展属性
     */
    private Map<String, Object> attributes;

    /**
     * 构造函数（仅指定租户ID）
     *
     * @param tenantId 租户ID
     */
    public TenantContext(String tenantId) {
        this.tenantId = tenantId;
        this.status = TenantStatus.ACTIVE;
        this.attributes = new HashMap<>();
    }

    /**
     * 构造函数（指定租户ID和名称）
     *
     * @param tenantId   租户ID
     * @param tenantName 租户名称
     */
    public TenantContext(String tenantId, String tenantName) {
        this.tenantId = tenantId;
        this.tenantName = tenantName;
        this.status = TenantStatus.ACTIVE;
        this.attributes = new HashMap<>();
    }

    /**
     * 构造函数（指定租户ID、名称和状态）
     *
     * @param tenantId   租户ID
     * @param tenantName 租户名称
     * @param status     租户状态
     */
    public TenantContext(String tenantId, String tenantName, TenantStatus status) {
        this.tenantId = tenantId;
        this.tenantName = tenantName;
        this.status = status;
        this.attributes = new HashMap<>();
    }

    /**
     * 验证租户上下文是否有效
     *
     * @return 如果有效返回true，否则返回false
     */
    public boolean isValid() {
        return tenantId != null && !tenantId.trim().isEmpty();
    }

    /**
     * 检查租户是否处于活跃状态
     *
     * @return 如果处于活跃状态返回true，否则返回false
     */
    public boolean isActive() {
        return status == null || status == TenantStatus.ACTIVE;
    }

    /**
     * 检查租户是否被禁用
     *
     * @return 如果被禁用返回true，否则返回false
     */
    public boolean isDisabled() {
        return status == TenantStatus.DISABLED;
    }

    /**
     * 检查租户是否被暂停
     *
     * @return 如果被暂停返回true，否则返回false
     */
    public boolean isSuspended() {
        return status == TenantStatus.SUSPENDED;
    }

    /**
     * 添加扩展属性
     *
     * @param key   属性键
     * @param value 属性值
     */
    public void addAttribute(String key, Object value) {
        if (attributes == null) {
            attributes = new HashMap<>();
        }
        attributes.put(key, value);
    }

    /**
     * 获取扩展属性
     *
     * @param key 属性键
     * @return 属性值
     */
    public Object getAttribute(String key) {
        return attributes != null ? attributes.get(key) : null;
    }

    /**
     * 获取扩展属性（指定类型）
     *
     * @param key   属性键
     * @param clazz 属性类型
     * @param <T>   泛型类型
     * @return 属性值
     */
    @SuppressWarnings("unchecked")
    public <T> T getAttribute(String key, Class<T> clazz) {
        Object value = getAttribute(key);
        if (value != null && clazz.isInstance(value)) {
            return (T) value;
        }
        return null;
    }

    /**
     * 移除扩展属性
     *
     * @param key 属性键
     * @return 被移除的属性值
     */
    public Object removeAttribute(String key) {
        return attributes != null ? attributes.remove(key) : null;
    }

    /**
     * 检查是否包含指定的扩展属性
     *
     * @param key 属性键
     * @return 如果包含返回true，否则返回false
     */
    public boolean hasAttribute(String key) {
        return attributes != null && attributes.containsKey(key);
    }

    /**
     * 获取所有扩展属性的键
     *
     * @return 属性键集合
     */
    public java.util.Set<String> getAttributeKeys() {
        return attributes != null ? attributes.keySet() : java.util.Collections.emptySet();
    }

    /**
     * 清空所有扩展属性
     */
    public void clearAttributes() {
        if (attributes != null) {
            attributes.clear();
        }
    }

    /**
     * 创建当前上下文的副本
     *
     * @return 上下文副本
     */
    public TenantContext copy() {
        TenantContext copy = new TenantContext();
        copy.tenantId = this.tenantId;
        copy.tenantName = this.tenantName;
        copy.status = this.status;
        copy.createdAt = this.createdAt;
        copy.updatedAt = this.updatedAt;
        
        if (this.attributes != null) {
            copy.attributes = new HashMap<>(this.attributes);
        }
        
        return copy;
    }

    /**
     * 获取租户显示名称
     * <p>
     * 如果租户名称不为空则返回租户名称，否则返回租户ID
     * </p>
     *
     * @return 租户显示名称
     */
    public String getDisplayName() {
        return tenantName != null && !tenantName.trim().isEmpty() ? tenantName : tenantId;
    }

    /**
     * 获取租户状态描述
     *
     * @return 租户状态描述
     */
    public String getStatusDescription() {
        return status != null ? status.getDescription() : "未知";
    }

    /**
     * 格式化租户信息
     *
     * @return 格式化的租户信息字符串
     */
    public String format() {
        StringBuilder sb = new StringBuilder();
        sb.append("Tenant{");
        sb.append("id='").append(tenantId).append("'");
        
        if (tenantName != null && !tenantName.trim().isEmpty()) {
            sb.append(", name='").append(tenantName).append("'");
        }
        
        if (status != null) {
            sb.append(", status=").append(status);
        }
        
        if (attributes != null && !attributes.isEmpty()) {
            sb.append(", attributes=").append(attributes.size());
        }
        
        sb.append("}");
        return sb.toString();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TenantContext that = (TenantContext) o;
        return Objects.equals(tenantId, that.tenantId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(tenantId);
    }

    @Override
    public String toString() {
        return format();
    }
}
