package cn.com.handthing.starter.auth.sms;

import cn.com.handthing.starter.auth.core.UserInfo;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * 短信认证用户服务接口
 * <p>
 * 定义短信认证相关的用户操作接口，包括用户查找、创建、状态检查等。
 * 业务系统需要实现此接口来提供具体的用户数据访问逻辑。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
public interface SmsUserService {

    /**
     * 根据手机号查找用户
     *
     * @param phone 手机号
     * @return 用户信息，如果未找到返回空
     */
    Optional<UserInfo> findByPhone(String phone);

    /**
     * 根据用户ID查找用户
     *
     * @param userId 用户ID
     * @return 用户信息，如果未找到返回空
     */
    Optional<UserInfo> findById(String userId);

    /**
     * 创建新用户
     *
     * @param phone    手机号
     * @param nickname 昵称
     * @return 创建的用户信息
     */
    UserInfo createUser(String phone, String nickname);

    /**
     * 检查用户状态是否有效
     *
     * @param userInfo 用户信息
     * @return 如果用户状态有效返回true，否则返回false
     */
    boolean isUserValid(UserInfo userInfo);

    /**
     * 检查用户是否被锁定
     *
     * @param userInfo 用户信息
     * @return 如果用户被锁定返回true，否则返回false
     */
    boolean isUserLocked(UserInfo userInfo);

    /**
     * 检查用户是否被禁用
     *
     * @param userInfo 用户信息
     * @return 如果用户被禁用返回true，否则返回false
     */
    boolean isUserDisabled(UserInfo userInfo);

    /**
     * 检查账户是否过期
     *
     * @param userInfo 用户信息
     * @return 如果账户过期返回true，否则返回false
     */
    boolean isAccountExpired(UserInfo userInfo);

    /**
     * 更新用户最后登录信息
     *
     * @param userId    用户ID
     * @param loginTime 登录时间
     * @param ipAddress IP地址
     * @param userAgent 用户代理
     */
    void updateLastLoginInfo(String userId, LocalDateTime loginTime, String ipAddress, String userAgent);

    /**
     * 记录登录失败
     *
     * @param phone     手机号
     * @param ipAddress IP地址
     * @param reason    失败原因
     */
    void recordLoginFailure(String phone, String ipAddress, String reason);

    /**
     * 清除登录失败记录
     *
     * @param phone 手机号
     */
    void clearLoginFailures(String phone);

    /**
     * 获取登录失败次数
     *
     * @param phone 手机号
     * @return 登录失败次数
     */
    int getLoginFailureCount(String phone);

    /**
     * 锁定用户
     *
     * @param userId    用户ID
     * @param reason    锁定原因
     * @param lockUntil 锁定到期时间
     */
    void lockUser(String userId, String reason, LocalDateTime lockUntil);

    /**
     * 解锁用户
     *
     * @param userId 用户ID
     */
    void unlockUser(String userId);

    /**
     * 检查是否为首次登录
     *
     * @param userInfo 用户信息
     * @return 如果是首次登录返回true，否则返回false
     */
    default boolean isFirstLogin(UserInfo userInfo) {
        return userInfo.getLastLoginTime() == null;
    }

    /**
     * 检查手机号是否已注册
     *
     * @param phone 手机号
     * @return 如果已注册返回true，否则返回false
     */
    default boolean isPhoneRegistered(String phone) {
        return findByPhone(phone).isPresent();
    }

    /**
     * 生成用户ID
     *
     * @param phone 手机号
     * @return 用户ID
     */
    default String generateUserId(String phone) {
        return "sms_" + System.currentTimeMillis() + "_" + phone.hashCode();
    }

    /**
     * 生成默认用户名
     *
     * @param phone 手机号
     * @return 默认用户名
     */
    default String generateDefaultUsername(String phone) {
        return "user_" + phone.substring(phone.length() - 4);
    }

    /**
     * 生成默认昵称
     *
     * @param phone 手机号
     * @return 默认昵称
     */
    default String generateDefaultNickname(String phone) {
        return "手机用户" + phone.substring(phone.length() - 4);
    }

    /**
     * 获取默认角色
     *
     * @return 默认角色列表
     */
    default java.util.List<String> getDefaultRoles() {
        return java.util.List.of("USER");
    }

    /**
     * 获取默认权限
     *
     * @return 默认权限列表
     */
    default java.util.List<String> getDefaultPermissions() {
        return java.util.List.of("READ");
    }

    /**
     * 验证手机号格式
     *
     * @param phone 手机号
     * @return 如果格式正确返回true，否则返回false
     */
    default boolean isValidPhoneFormat(String phone) {
        return phone != null && phone.matches("^1[3-9]\\d{9}$");
    }

    /**
     * 验证昵称格式
     *
     * @param nickname 昵称
     * @return 如果格式正确返回true，否则返回false
     */
    default boolean isValidNickname(String nickname) {
        return nickname != null && nickname.trim().length() >= 2 && nickname.trim().length() <= 20;
    }
}
