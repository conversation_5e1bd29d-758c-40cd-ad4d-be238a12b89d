SaaS后台管理系统详细功能规格说明 (PRD)
本PRD文档旨在对SaaS后台的19个核心模块进行最细粒度的功能规格定义，明确每个功能的具体实现细节、界面元素和业务规则。

一、 核心基础层 (Core Foundation)
1. 统一用户认证 (Unified Authentication)

登录方式管理

界面: 系统设置 > 登录设置 > 登录方式。

功能:

提供一个列表，包含 [账号密码]、[手机验证码]、[邮箱验证码]、[微信扫码]、[钉钉扫码] 等选项。

每个选项后有一个 启用/禁用 的开关控件。

社交登录集成

界面: 系统设置 > 登录设置 > 第三方集成。

功能:

为每个第三方登录（如微信、钉钉）提供独立的配置卡片。

卡片内包含输入框：AppID, AppSecret。

系统自动生成并显示 回调地址(Callback URL)，并提供一键复制按钮。

企业身份源 (IdP) 对接

界面: 系统设置 > 登录设置 > 企业身份源(IdP)。

功能:

提供 “新增对接” 按钮，可选对接协议（SAML 2.0 / OIDC）。

SAML配置界面: 包含输入框 - 对接名称、Entity ID、单点登录URL(SSO URL)，以及上传 X.509 证书的按钮。

OIDC配置界面: 包含输入框 - 对接名称、Client ID、Client Secret、Discovery URL。

安全策略

界面: 系统设置 > 安全策略。

功能:

密码策略:

数字输入框：最小密码长度、密码过期天数（0为永不过期）、历史密码查重次数。

复选框：必须包含大写字母、小写字母、数字、特殊符号。

多因素认证(MFA):

开关：平台强制启用MFA / 允许用户自行选择。

登录风控:

开关：启用异常地点登录检测、启用异常设备登录检测。

文本域：可信IP白名单，支持输入IP段。

Token管理

界面: 系统监控 > 在线用户。

功能:

一个可搜索、可分页的用户列表，列出：用户名、登录时间、登录IP、设备信息。

每行记录后有一个 “强制下线” 按钮，点击后该用户会话立即失效。

2. 租户管理 (Tenant Management)

租户列表

界面: 租户管理 > 租户列表。

功能:

显示所有租户，列出：租户名称、套餐版本、状态（试用、正常、已到期、禁用）、创建时间、到期时间。

提供搜索框，可按租户名称或联系人手机号搜索。

每行提供操作按钮：[详情]、[编辑]、[禁用/启用]、[续费]、[删除]。

创建/编辑租户

界面: 点击“新增租户”或“编辑”按钮后的弹窗/页面。

功能:

表单包含：租户名称、公司信息、联系人姓名、联系人电话。

下拉选择框：选择租户套餐、设置租户状态。

日期选择器：设置服务到期时间。

为新租户设置管理员账号和初始密码。

套餐与规格管理

界面: 租户管理 > 套餐管理。

功能:

“新增套餐” 按钮。

表单包含：套餐名称（如基础版）、价格（按月/按年）。

使用权限树（Tree组件）勾选此套餐可用的菜单权限和功能权限。

数字输入框设置资源配额：最大用户数、最大存储空间(GB)、每月API调用次数等。

3. 组织架构管理 (Organizational Structure)

部门管理

界面: 组织管理 > 部门管理。

功能:

左侧为树状部门结构图，右侧为该部门下用户信息列表。

支持在树上右键或使用按钮进行 新增子部门、编辑部门、删除部门 操作。

支持拖拽部门节点以调整层级和排序。

职务/岗位管理

界面: 组织管理 > 职务管理。

功能: 提供简单的列表增删改查，维护职务名称和描述。

4. 用户与角色权限 (User, Role & Permission)

用户管理

界面: 组织管理 > 用户管理。

功能:

用户列表，可按姓名、手机号、部门搜索。

“新增用户” / “批量导入”（提供模板下载）按钮。

操作列：[编辑]、[分配角色]、[重置密码]、[禁用/启用]。

角色管理

界面: 权限管理 > 角色管理。

功能:

角色列表，显示角色名和描述。

“新增角色” / “复制角色” 按钮。

操作列：[编辑]、[分配权限]、[删除]。

权限分配

界面: 点击“分配权限”后的弹窗/页面。

功能:

左侧为 菜单权限树，勾选后用户即可看到对应菜单。

右侧为 操作权限 和 数据权限 配置。

数据权限：提供单选按钮组 - [查看全部数据]、[查看本部门数据]、[查看本部门及下级部门数据]、[仅查看本人数据]、[自定义规则]。

(为了简洁，后续模块将采用更精炼的格式进行描述)

二、 业务支撑层 (Business Support)
5. 菜单管理: 界面提供树形控件，可拖拽排序，每个节点可配置名称、图标(提供图标选择器)、路由、可见性。
6. 审批工作流: 流程设计器使用BPMN.js等库，提供拖拽节点（开始、审批、条件、结束）和连线功能。表单设计器提供拖拽组件（输入框、日期选择器、下拉框）生成表单。
7. 字典与标签管理: 字典管理是左侧字典类型、右侧该类型下的键值列表。标签管理是创建标签组，再在组下创建标签。
8. 消息中心: 模板管理提供富文本编辑器。发送记录是带状态筛选（成功/失败）的日志列表。
9. 文件存储服务: 配置页提供单选切换存储引擎，并填写对应的AccessKey、Bucket等信息。文件管理页类似网盘，提供列表/网格视图切换。

三、 应用与集成层 (Application & Integration)
10. 应用管理: 应用市场以卡片形式展示应用，显示Logo、简介和“开通”按钮。租户后台有“我的应用”列表。
11. 开放平台: 开发者中心让开发者注册并创建应用后，自动生成AppID和AppSecret。API管理界面可为每个API配置分组、版本和流量限制规则。
12. 数据同步: 提供“新增同步”按钮，选择同步源（如钉钉），走OAuth2授权流程，授权后配置定时同步的Cron表达式。
13. 任务调度中心: 提供任务列表，可查看每个任务的上次执行时间、下次执行时间、状态。可手动触发执行一次。

四、 运营与监控层 (Operation & Monitoring)
14. 审计与日志: 提供强大的搜索栏，可按时间范围、操作人、关键词、IP地址进行组合查询。
15. 系统监控与告警: 监控大盘使用图表库（如ECharts）展示曲线图、仪表盘。告警规则配置界面类似 “IF [指标] [> / <] [阈值] FOR [持续时间] THEN [发送通知]”。
16. 计费与订阅管理: 订单列表支持按支付状态筛选。支付集成需在后台配置支付商户号和密钥。

五、 个人化与系统设置 (Personalization & System Settings)
17. 个人中心: 采用标签页设计，区分 [基本信息]、[安全设置]、[我的通知] 等。
18. 系统管理: 全局设置是一个大表单。主题定制提供颜色选择器。公告管理提供富文本编辑器和发布时间选择器。