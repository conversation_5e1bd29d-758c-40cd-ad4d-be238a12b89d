package cn.com.handthing.starter.connector.token;

import cn.com.handthing.starter.cache.CacheService;
import cn.com.handthing.starter.connector.PlatformType;
import cn.com.handthing.starter.connector.auth.AuthProvider;
import cn.com.handthing.starter.connector.auth.UnifiedAccessToken;
import cn.com.handthing.starter.connector.exception.AuthException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Token管理器
 * <p>
 * 基于CacheService实现Token存储和管理，使用专门的缓存区域存储Token，
 * 利用CacheService的TTL功能自动管理Token过期，支持自动刷新过期Token。
 * 支持所有缓存后端（内存、Caffeine、Redis、多级等）。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
public class TokenManager {

    private final CacheService cacheService;
    private final String tokenCacheName;
    private final Map<PlatformType, AuthProvider> authProviders = new ConcurrentHashMap<>();

    /**
     * 构造函数
     *
     * @param cacheService    缓存服务
     * @param tokenCacheName  Token缓存区域名称
     */
    public TokenManager(CacheService cacheService, String tokenCacheName) {
        this.cacheService = cacheService;
        this.tokenCacheName = tokenCacheName;
    }

    /**
     * 注册认证提供者
     *
     * @param authProvider 认证提供者
     */
    public void registerAuthProvider(AuthProvider authProvider) {
        authProviders.put(authProvider.getPlatformType(), authProvider);
        log.info("Registered auth provider for platform: {}", authProvider.getPlatformType());
    }

    /**
     * 存储Token
     *
     * @param userId      用户ID
     * @param platform    平台类型
     * @param accessToken 访问令牌
     */
    public void storeToken(String userId, PlatformType platform, UnifiedAccessToken accessToken) {
        String cacheKey = buildTokenKey(userId, platform);
        
        // 计算TTL
        Duration ttl = calculateTtl(accessToken);
        
        if (ttl != null) {
            cacheService.put(tokenCacheName, cacheKey, accessToken, ttl);
            log.debug("Stored token for user: {}, platform: {}, TTL: {} seconds", 
                    userId, platform, ttl.getSeconds());
        } else {
            cacheService.put(tokenCacheName, cacheKey, accessToken);
            log.debug("Stored token for user: {}, platform: {} (no TTL)", userId, platform);
        }
    }

    /**
     * 获取Token
     *
     * @param userId   用户ID
     * @param platform 平台类型
     * @return 访问令牌，如果不存在或已过期返回null
     */
    public UnifiedAccessToken getToken(String userId, PlatformType platform) {
        String cacheKey = buildTokenKey(userId, platform);
        UnifiedAccessToken token = cacheService.get(tokenCacheName, cacheKey);
        
        if (token == null) {
            log.debug("No token found for user: {}, platform: {}", userId, platform);
            return null;
        }
        
        // 检查Token是否过期
        if (token.isExpired()) {
            log.debug("Token expired for user: {}, platform: {}", userId, platform);
            removeToken(userId, platform);
            return null;
        }
        
        log.debug("Retrieved token for user: {}, platform: {}", userId, platform);
        return token;
    }

    /**
     * 获取有效Token（如果即将过期则尝试刷新）
     *
     * @param userId   用户ID
     * @param platform 平台类型
     * @return 有效的访问令牌，如果无法获取返回null
     */
    public UnifiedAccessToken getValidToken(String userId, PlatformType platform) {
        UnifiedAccessToken token = getToken(userId, platform);
        
        if (token == null) {
            return null;
        }
        
        // 如果Token即将过期且有刷新令牌，尝试刷新
        if (token.isExpiringSoon() && token.hasRefreshToken()) {
            log.debug("Token expiring soon for user: {}, platform: {}, attempting refresh", 
                    userId, platform);
            
            try {
                UnifiedAccessToken refreshedToken = refreshToken(userId, platform, token.getRefreshToken());
                if (refreshedToken != null) {
                    return refreshedToken;
                }
            } catch (Exception e) {
                log.warn("Failed to refresh token for user: {}, platform: {}", userId, platform, e);
            }
        }
        
        return token;
    }

    /**
     * 刷新Token
     *
     * @param userId       用户ID
     * @param platform     平台类型
     * @param refreshToken 刷新令牌
     * @return 新的访问令牌
     * @throws AuthException 如果刷新失败
     */
    public UnifiedAccessToken refreshToken(String userId, PlatformType platform, String refreshToken) throws AuthException {
        AuthProvider authProvider = authProviders.get(platform);
        if (authProvider == null) {
            throw AuthException.platformNotSupported(platform);
        }
        
        if (!authProvider.supportsRefreshToken()) {
            throw new AuthException("Platform does not support token refresh: " + platform, 
                    AuthException.REFRESH_TOKEN_EXPIRED, platform);
        }
        
        try {
            UnifiedAccessToken newToken = authProvider.refreshToken(refreshToken);
            
            // 存储新Token
            storeToken(userId, platform, newToken);
            
            log.info("Successfully refreshed token for user: {}, platform: {}", userId, platform);
            return newToken;
            
        } catch (Exception e) {
            log.error("Failed to refresh token for user: {}, platform: {}", userId, platform, e);
            throw new AuthException("Failed to refresh token", AuthException.REFRESH_TOKEN_EXPIRED, platform, e);
        }
    }

    /**
     * 移除Token
     *
     * @param userId   用户ID
     * @param platform 平台类型
     */
    public void removeToken(String userId, PlatformType platform) {
        String cacheKey = buildTokenKey(userId, platform);
        cacheService.evict(tokenCacheName, cacheKey);
        log.debug("Removed token for user: {}, platform: {}", userId, platform);
    }

    /**
     * 检查Token是否存在
     *
     * @param userId   用户ID
     * @param platform 平台类型
     * @return 如果存在有效Token返回true，否则返回false
     */
    public boolean hasValidToken(String userId, PlatformType platform) {
        return getValidToken(userId, platform) != null;
    }

    /**
     * 清空指定用户的所有Token
     *
     * @param userId 用户ID
     */
    public void clearUserTokens(String userId) {
        for (PlatformType platform : PlatformType.values()) {
            removeToken(userId, platform);
        }
        log.debug("Cleared all tokens for user: {}", userId);
    }

    /**
     * 清空所有Token
     */
    public void clearAllTokens() {
        cacheService.clear(tokenCacheName);
        log.info("Cleared all tokens from cache");
    }

    /**
     * 构建Token缓存键
     *
     * @param userId   用户ID
     * @param platform 平台类型
     * @return 缓存键
     */
    private String buildTokenKey(String userId, PlatformType platform) {
        return String.format("token:%s:%s", platform.getCode(), userId);
    }

    /**
     * 计算Token的TTL
     *
     * @param accessToken 访问令牌
     * @return TTL时长，如果无法计算返回null
     */
    private Duration calculateTtl(UnifiedAccessToken accessToken) {
        if (accessToken.getExpiresIn() != null && accessToken.getExpiresIn() > 0) {
            // 提前5分钟过期，避免边界情况
            long ttlSeconds = Math.max(accessToken.getExpiresIn() - 300, 60);
            return Duration.ofSeconds(ttlSeconds);
        }
        return null;
    }
}
