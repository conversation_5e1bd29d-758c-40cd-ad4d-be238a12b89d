{"groups": [{"name": "handthing.connector", "type": "cn.com.handthing.starter.connector.config.AppConnectorProperties", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties"}, {"name": "handthing.connector.alipay", "type": "cn.com.handthing.starter.connector.config.AppConnectorProperties$AlipayConfig", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties", "sourceMethod": "public cn.com.handthing.starter.connector.config.AppConnectorProperties.AlipayConfig getAlipay() "}, {"name": "handthing.connector.dingtalk", "type": "cn.com.handthing.starter.connector.config.AppConnectorProperties$DingTalkConfig", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties", "sourceMethod": "public cn.com.handthing.starter.connector.config.AppConnectorProperties.DingTalkConfig getDingtalk() "}, {"name": "handthing.connector.douyin", "type": "cn.com.handthing.starter.connector.config.AppConnectorProperties$DouyinConfig", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties", "sourceMethod": "public cn.com.handthing.starter.connector.config.AppConnectorProperties.DouyinConfig getDouyin() "}, {"name": "handthing.connector.feishu", "type": "cn.com.handthing.starter.connector.config.AppConnectorProperties$FeishuConfig", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties", "sourceMethod": "public cn.com.handthing.starter.connector.config.AppConnectorProperties.FeishuConfig getFeishu() "}, {"name": "handthing.connector.wechat", "type": "cn.com.handthing.starter.connector.config.AppConnectorProperties$WeChatConfig", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties", "sourceMethod": "public cn.com.handthing.starter.connector.config.AppConnectorProperties.WeChatConfig getWechat() "}, {"name": "handthing.connector.wecom", "type": "cn.com.handthing.starter.connector.config.AppConnectorProperties$WeComConfig", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties", "sourceMethod": "public cn.com.handthing.starter.connector.config.AppConnectorProperties.WeComConfig getWecom() "}], "properties": [{"name": "handthing.connector.alipay.alipay-public-key", "type": "java.lang.String", "description": "支付宝公钥", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$AlipayConfig"}, {"name": "handthing.connector.alipay.api-base-url", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$AlipayConfig"}, {"name": "handthing.connector.alipay.app-id", "type": "java.lang.String", "description": "应用ID", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$AlipayConfig"}, {"name": "handthing.connector.alipay.auth-url", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$AlipayConfig"}, {"name": "handthing.connector.alipay.client-id", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$AlipayConfig"}, {"name": "handthing.connector.alipay.client-secret", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$AlipayConfig"}, {"name": "handthing.connector.alipay.connect-timeout", "type": "java.lang.Integer", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$AlipayConfig"}, {"name": "handthing.connector.alipay.enabled", "type": "java.lang.Bo<PERSON>an", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$AlipayConfig"}, {"name": "handthing.connector.alipay.extra-config", "type": "java.util.Map<java.lang.String,java.lang.Object>", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$AlipayConfig"}, {"name": "handthing.connector.alipay.max-retries", "type": "java.lang.Integer", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$AlipayConfig"}, {"name": "handthing.connector.alipay.private-key", "type": "java.lang.String", "description": "应用私钥", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$AlipayConfig"}, {"name": "handthing.connector.alipay.read-timeout", "type": "java.lang.Integer", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$AlipayConfig"}, {"name": "handthing.connector.alipay.redirect-uri", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$AlipayConfig"}, {"name": "handthing.connector.alipay.sandbox", "type": "java.lang.Bo<PERSON>an", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$AlipayConfig"}, {"name": "handthing.connector.alipay.scope", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$AlipayConfig"}, {"name": "handthing.connector.alipay.sign-type", "type": "java.lang.String", "description": "签名类型", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$AlipayConfig"}, {"name": "handthing.connector.alipay.token-url", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$AlipayConfig"}, {"name": "handthing.connector.alipay.user-info-url", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$AlipayConfig"}, {"name": "handthing.connector.all-platform-configs", "type": "java.util.Map<java.lang.String,cn.com.handthing.starter.connector.config.PlatformConfig>", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties"}, {"name": "handthing.connector.callback-path-prefix", "type": "java.lang.String", "description": "回调处理器路径前缀", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties"}, {"name": "handthing.connector.default-connect-timeout", "type": "java.lang.Integer", "description": "默认连接超时时间（毫秒）", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties"}, {"name": "handthing.connector.default-max-retries", "type": "java.lang.Integer", "description": "默认最大重试次数", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties"}, {"name": "handthing.connector.default-read-timeout", "type": "java.lang.Integer", "description": "默认读取超时时间（毫秒）", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties"}, {"name": "handthing.connector.dingtalk.api-base-url", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$DingTalkConfig"}, {"name": "handthing.connector.dingtalk.app-key", "type": "java.lang.String", "description": "应用Key", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$DingTalkConfig"}, {"name": "handthing.connector.dingtalk.app-secret", "type": "java.lang.String", "description": "应用密钥", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$DingTalkConfig"}, {"name": "handthing.connector.dingtalk.auth-url", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$DingTalkConfig"}, {"name": "handthing.connector.dingtalk.client-id", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$DingTalkConfig"}, {"name": "handthing.connector.dingtalk.client-secret", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$DingTalkConfig"}, {"name": "handthing.connector.dingtalk.connect-timeout", "type": "java.lang.Integer", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$DingTalkConfig"}, {"name": "handthing.connector.dingtalk.enabled", "type": "java.lang.Bo<PERSON>an", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$DingTalkConfig"}, {"name": "handthing.connector.dingtalk.extra-config", "type": "java.util.Map<java.lang.String,java.lang.Object>", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$DingTalkConfig"}, {"name": "handthing.connector.dingtalk.max-retries", "type": "java.lang.Integer", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$DingTalkConfig"}, {"name": "handthing.connector.dingtalk.read-timeout", "type": "java.lang.Integer", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$DingTalkConfig"}, {"name": "handthing.connector.dingtalk.redirect-uri", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$DingTalkConfig"}, {"name": "handthing.connector.dingtalk.sandbox", "type": "java.lang.Bo<PERSON>an", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$DingTalkConfig"}, {"name": "handthing.connector.dingtalk.scope", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$DingTalkConfig"}, {"name": "handthing.connector.dingtalk.token-url", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$DingTalkConfig"}, {"name": "handthing.connector.dingtalk.user-info-url", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$DingTalkConfig"}, {"name": "handthing.connector.douyin.api-base-url", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$DouyinConfig"}, {"name": "handthing.connector.douyin.auth-url", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$DouyinConfig"}, {"name": "handthing.connector.douyin.client-id", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$DouyinConfig"}, {"name": "handthing.connector.douyin.client-key", "type": "java.lang.String", "description": "客户端Key", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$DouyinConfig"}, {"name": "handthing.connector.douyin.client-secret", "type": "java.lang.String", "description": "客户端密钥", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$DouyinConfig"}, {"name": "handthing.connector.douyin.connect-timeout", "type": "java.lang.Integer", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$DouyinConfig"}, {"name": "handthing.connector.douyin.enabled", "type": "java.lang.Bo<PERSON>an", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$DouyinConfig"}, {"name": "handthing.connector.douyin.extra-config", "type": "java.util.Map<java.lang.String,java.lang.Object>", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$DouyinConfig"}, {"name": "handthing.connector.douyin.max-retries", "type": "java.lang.Integer", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$DouyinConfig"}, {"name": "handthing.connector.douyin.read-timeout", "type": "java.lang.Integer", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$DouyinConfig"}, {"name": "handthing.connector.douyin.redirect-uri", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$DouyinConfig"}, {"name": "handthing.connector.douyin.sandbox", "type": "java.lang.Bo<PERSON>an", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$DouyinConfig"}, {"name": "handthing.connector.douyin.scope", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$DouyinConfig"}, {"name": "handthing.connector.douyin.token-url", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$DouyinConfig"}, {"name": "handthing.connector.douyin.user-info-url", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$DouyinConfig"}, {"name": "handthing.connector.enable-event-publishing", "type": "java.lang.Bo<PERSON>an", "description": "是否启用事件发布", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties"}, {"name": "handthing.connector.enable-unified-callback", "type": "java.lang.Bo<PERSON>an", "description": "是否启用统一回调处理器", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties"}, {"name": "handthing.connector.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用App Connector", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties"}, {"name": "handthing.connector.enabled-platform-configs", "type": "java.util.Map<java.lang.String,cn.com.handthing.starter.connector.config.PlatformConfig>", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties"}, {"name": "handthing.connector.feishu.api-base-url", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$FeishuConfig"}, {"name": "handthing.connector.feishu.app-id", "type": "java.lang.String", "description": "应用ID", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$FeishuConfig"}, {"name": "handthing.connector.feishu.app-secret", "type": "java.lang.String", "description": "应用密钥", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$FeishuConfig"}, {"name": "handthing.connector.feishu.auth-url", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$FeishuConfig"}, {"name": "handthing.connector.feishu.client-id", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$FeishuConfig"}, {"name": "handthing.connector.feishu.client-secret", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$FeishuConfig"}, {"name": "handthing.connector.feishu.connect-timeout", "type": "java.lang.Integer", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$FeishuConfig"}, {"name": "handthing.connector.feishu.enabled", "type": "java.lang.Bo<PERSON>an", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$FeishuConfig"}, {"name": "handthing.connector.feishu.extra-config", "type": "java.util.Map<java.lang.String,java.lang.Object>", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$FeishuConfig"}, {"name": "handthing.connector.feishu.max-retries", "type": "java.lang.Integer", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$FeishuConfig"}, {"name": "handthing.connector.feishu.read-timeout", "type": "java.lang.Integer", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$FeishuConfig"}, {"name": "handthing.connector.feishu.redirect-uri", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$FeishuConfig"}, {"name": "handthing.connector.feishu.sandbox", "type": "java.lang.Bo<PERSON>an", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$FeishuConfig"}, {"name": "handthing.connector.feishu.scope", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$FeishuConfig"}, {"name": "handthing.connector.feishu.token-url", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$FeishuConfig"}, {"name": "handthing.connector.feishu.user-info-url", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$FeishuConfig"}, {"name": "handthing.connector.redirect-uri-prefix", "type": "java.lang.String", "description": "统一回调地址前缀 最终回调地址格式：{redirectUriPrefix}/connector/callback/{platform} 例如：https://your.domain.com/auth/connector/callback/wecom", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties"}, {"name": "handthing.connector.token-cache-name", "type": "java.lang.String", "description": "Token缓存区域名称", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties"}, {"name": "handthing.connector.wechat.api-base-url", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$WeChatConfig"}, {"name": "handthing.connector.wechat.app-id", "type": "java.lang.String", "description": "应用ID", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$WeChatConfig"}, {"name": "handthing.connector.wechat.app-secret", "type": "java.lang.String", "description": "应用密钥", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$WeChatConfig"}, {"name": "handthing.connector.wechat.auth-url", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$WeChatConfig"}, {"name": "handthing.connector.wechat.client-id", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$WeChatConfig"}, {"name": "handthing.connector.wechat.client-secret", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$WeChatConfig"}, {"name": "handthing.connector.wechat.connect-timeout", "type": "java.lang.Integer", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$WeChatConfig"}, {"name": "handthing.connector.wechat.enabled", "type": "java.lang.Bo<PERSON>an", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$WeChatConfig"}, {"name": "handthing.connector.wechat.extra-config", "type": "java.util.Map<java.lang.String,java.lang.Object>", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$WeChatConfig"}, {"name": "handthing.connector.wechat.max-retries", "type": "java.lang.Integer", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$WeChatConfig"}, {"name": "handthing.connector.wechat.read-timeout", "type": "java.lang.Integer", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$WeChatConfig"}, {"name": "handthing.connector.wechat.redirect-uri", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$WeChatConfig"}, {"name": "handthing.connector.wechat.sandbox", "type": "java.lang.Bo<PERSON>an", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$WeChatConfig"}, {"name": "handthing.connector.wechat.scope", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$WeChatConfig"}, {"name": "handthing.connector.wechat.token-url", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$WeChatConfig"}, {"name": "handthing.connector.wechat.user-info-url", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$WeChatConfig"}, {"name": "handthing.connector.wecom.agent-id", "type": "java.lang.String", "description": "应用ID", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$WeComConfig"}, {"name": "handthing.connector.wecom.api-base-url", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$WeComConfig"}, {"name": "handthing.connector.wecom.auth-url", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$WeComConfig"}, {"name": "handthing.connector.wecom.client-id", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$WeComConfig"}, {"name": "handthing.connector.wecom.client-secret", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$WeComConfig"}, {"name": "handthing.connector.wecom.connect-timeout", "type": "java.lang.Integer", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$WeComConfig"}, {"name": "handthing.connector.wecom.corp-id", "type": "java.lang.String", "description": "企业ID", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$WeComConfig"}, {"name": "handthing.connector.wecom.enabled", "type": "java.lang.Bo<PERSON>an", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$WeComConfig"}, {"name": "handthing.connector.wecom.extra-config", "type": "java.util.Map<java.lang.String,java.lang.Object>", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$WeComConfig"}, {"name": "handthing.connector.wecom.max-retries", "type": "java.lang.Integer", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$WeComConfig"}, {"name": "handthing.connector.wecom.read-timeout", "type": "java.lang.Integer", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$WeComConfig"}, {"name": "handthing.connector.wecom.redirect-uri", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$WeComConfig"}, {"name": "handthing.connector.wecom.sandbox", "type": "java.lang.Bo<PERSON>an", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$WeComConfig"}, {"name": "handthing.connector.wecom.scope", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$WeComConfig"}, {"name": "handthing.connector.wecom.secret", "type": "java.lang.String", "description": "应用密钥", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$WeComConfig"}, {"name": "handthing.connector.wecom.token-url", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$WeComConfig"}, {"name": "handthing.connector.wecom.user-info-url", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.config.AppConnectorProperties$WeComConfig"}], "hints": [], "ignored": {"properties": []}}