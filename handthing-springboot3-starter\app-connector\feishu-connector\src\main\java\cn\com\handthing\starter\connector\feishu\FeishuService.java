package cn.com.handthing.starter.connector.feishu;

import cn.com.handthing.starter.connector.feishu.api.FeishuMessageApi;
import cn.com.handthing.starter.connector.feishu.api.FeishuUserApi;
import cn.com.handthing.starter.connector.feishu.config.FeishuConfig;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

/**
 * 飞书服务
 * <p>
 * 提供飞书平台的核心服务功能，包括消息发送、用户管理、文档协作等。
 * 支持企业内部应用和第三方应用的不同业务场景。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "handthing.connector.feishu", name = "enabled", havingValue = "true")
public class FeishuService {

    private final FeishuConfig config;
    private final FeishuMessageApi messageApi;
    private final FeishuUserApi userApi;

    /**
     * 获取消息API
     *
     * @return 消息API实例
     */
    public FeishuMessageApi message() {
        return messageApi;
    }

    /**
     * 获取用户API
     *
     * @return 用户API实例
     */
    public FeishuUserApi user() {
        return userApi;
    }

    /**
     * 检查服务是否可用
     *
     * @return 如果可用返回true，否则返回false
     */
    public boolean isAvailable() {
        return config.isEnabled() && config.isValid();
    }

    /**
     * 获取配置信息
     *
     * @return 配置对象
     */
    public FeishuConfig getConfig() {
        return config;
    }

    /**
     * 获取服务状态
     *
     * @return 服务状态
     */
    public ServiceStatus getStatus() {
        return ServiceStatus.builder()
                .enabled(config.isEnabled())
                .valid(config.isValid())
                .appId(config.getAppId())
                .appType(config.getAppType())
                .thirdPartyMode(config.isThirdPartyApp())
                .botEnabled(config.isBotAvailable())
                .larkMode(config.isLarkMode())
                .encryptMessage(config.isEncryptionEnabled())
                .apiBaseUrl(config.getEffectiveApiBaseUrl())
                .build();
    }

    /**
     * 服务状态信息
     */
    @Data
    @lombok.Builder
    public static class ServiceStatus {
        /**
         * 是否启用
         */
        private boolean enabled;

        /**
         * 配置是否有效
         */
        private boolean valid;

        /**
         * 应用ID
         */
        private String appId;

        /**
         * 应用类型
         */
        private String appType;

        /**
         * 是否为第三方应用模式
         */
        private boolean thirdPartyMode;

        /**
         * 是否启用机器人功能
         */
        private boolean botEnabled;

        /**
         * 是否为海外版飞书模式
         */
        private boolean larkMode;

        /**
         * 是否启用消息加密
         */
        private boolean encryptMessage;

        /**
         * API基础URL
         */
        private String apiBaseUrl;

        @Override
        public String toString() {
            return String.format("FeishuServiceStatus{enabled=%s, valid=%s, appId='%s', appType='%s', " +
                            "thirdPartyMode=%s, botEnabled=%s, larkMode=%s, encryptMessage=%s, apiBaseUrl='%s'}",
                    enabled, valid, appId, appType, thirdPartyMode, botEnabled, larkMode, encryptMessage, apiBaseUrl);
        }
    }
}
