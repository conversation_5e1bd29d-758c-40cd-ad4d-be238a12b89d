package cn.com.handthing.starter.connector.douyin;

import cn.com.handthing.starter.connector.douyin.api.DouyinDataApi;
import cn.com.handthing.starter.connector.douyin.api.DouyinVideoApi;
import cn.com.handthing.starter.connector.douyin.config.DouyinConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

/**
 * 抖音服务主入口
 * <p>
 * 聚合各个API服务，提供统一的抖音服务访问入口。
 * 包含视频管理API、数据开放API等功能模块。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "handthing.connector.douyin", name = "enabled", havingValue = "true")
public class DouyinService {

    private final DouyinConfig config;
    private final DouyinVideoApi videoApi;
    private final DouyinDataApi dataApi;

    /**
     * 获取视频管理API
     *
     * @return 视频管理API实例
     */
    public DouyinVideoApi video() {
        return videoApi;
    }

    /**
     * 获取数据开放API
     *
     * @return 数据开放API实例
     */
    public DouyinDataApi data() {
        return dataApi;
    }

    /**
     * 获取抖音配置
     *
     * @return 配置实例
     */
    public DouyinConfig getConfig() {
        return config;
    }

    /**
     * 检查服务是否可用
     *
     * @return 如果服务可用返回true，否则返回false
     */
    public boolean isAvailable() {
        return config.isEnabled() && config.isValid();
    }

    /**
     * 获取服务状态信息
     *
     * @return 服务状态信息
     */
    public ServiceStatus getStatus() {
        ServiceStatus status = new ServiceStatus();
        status.setEnabled(config.isEnabled());
        status.setValid(config.isValid());
        status.setClientKey(config.getClientKey());
        status.setEnterpriseMode(config.isEnterpriseMode());
        status.setMiniProgramMode(config.isMiniProgramMode());
        status.setWebhookEnabled(config.isWebhookEnabled());
        status.setApiBaseUrl(config.getEffectiveApiBaseUrl());
        return status;
    }

    /**
     * 服务状态信息类
     */
    public static class ServiceStatus {
        private boolean enabled;
        private boolean valid;
        private String clientKey;
        private boolean enterpriseMode;
        private boolean miniProgramMode;
        private boolean webhookEnabled;
        private String apiBaseUrl;

        // Getters and Setters
        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public boolean isValid() {
            return valid;
        }

        public void setValid(boolean valid) {
            this.valid = valid;
        }

        public String getClientKey() {
            return clientKey;
        }

        public void setClientKey(String clientKey) {
            this.clientKey = clientKey;
        }

        public boolean isEnterpriseMode() {
            return enterpriseMode;
        }

        public void setEnterpriseMode(boolean enterpriseMode) {
            this.enterpriseMode = enterpriseMode;
        }

        public boolean isMiniProgramMode() {
            return miniProgramMode;
        }

        public void setMiniProgramMode(boolean miniProgramMode) {
            this.miniProgramMode = miniProgramMode;
        }

        public boolean isWebhookEnabled() {
            return webhookEnabled;
        }

        public void setWebhookEnabled(boolean webhookEnabled) {
            this.webhookEnabled = webhookEnabled;
        }

        public String getApiBaseUrl() {
            return apiBaseUrl;
        }

        public void setApiBaseUrl(String apiBaseUrl) {
            this.apiBaseUrl = apiBaseUrl;
        }

        @Override
        public String toString() {
            return String.format("DouyinServiceStatus{enabled=%s, valid=%s, clientKey='%s', enterpriseMode=%s, miniProgramMode=%s, webhookEnabled=%s, apiBaseUrl='%s'}",
                    enabled, valid, clientKey, enterpriseMode, miniProgramMode, webhookEnabled, apiBaseUrl);
        }
    }
}
