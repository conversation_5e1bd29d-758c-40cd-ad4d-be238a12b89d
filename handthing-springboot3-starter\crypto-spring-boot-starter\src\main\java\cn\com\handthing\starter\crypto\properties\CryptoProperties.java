package cn.com.handthing.starter.crypto.properties;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import java.util.Map;
import java.util.HashMap;

/**
 * HandThing Crypto Starter 的主配置属性类。
 * <p>
 * 映射 "handthing.crypto" 前缀下的所有配置项。
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
@ConfigurationProperties(prefix = "handthing.crypto")
public class CryptoProperties {

    /**
     * 是否启用 HandThing Crypto Starter。如果为 false，则不会注册任何 Bean。
     */
    private boolean enabled = true;

    /**
     * 可观测性相关配置。
     */
    private final Observability observability = new Observability();

    /**
     * 动态刷新相关配置。
     */
    private final Refresh refresh = new Refresh();

    /**
     * 内置工具相关配置。
     */
    private final Tool tool = new Tool();

    /**
     * 对称加密算法配置映射。Key 是用户自定义的处理器名称，例如 "sensitiveDataEncryptor"。
     */
    private Map<String, SymmetricConfig> symmetric = new HashMap<>();

    /**
     * 非对称加密算法配置映射。Key 是用户自定义的处理器名称。
     */
    private Map<String, AsymmetricConfig> asymmetric = new HashMap<>();

    /**
     * 摘要（哈希）算法配置映射。Key 是用户自定义的处理器名称。
     */
    private Map<String, DigestConfig> digest = new HashMap<>();


    /**
     * 可观测性配置。
     */
    @Data
    public static class Observability {
        /**
         * 是否为 Crypto Starter 启用 Actuator 健康检查端点 (/actuator/health/crypto)。
         */
        private boolean healthCheckEnabled = true;

        /**
         * 是否为 Crypto Starter 启用 Micrometer 性能指标。
         */
        private boolean metricsEnabled = true;

    }

    /**
     * 动态刷新配置。
     */
    @Data
    public static class Refresh {
        /**
         * 是否启用配置动态刷新。
         * 启用后，当从配置中心（如 Nacos, Consul）获取到新的配置时，
         * 加密器和摘要器实例将被重新创建以应用新配置。
         * 这需要项目中存在 Spring Cloud 上下文环境。
         */
        private boolean enabled = false;
    }

    /**
     * 内置工具配置。
     */
    @Getter
    public static class Tool {
        private final GenerateKey generateKey = new GenerateKey();

        /**
         * 密钥生成工具配置。
         */
        @Data
        public static class GenerateKey {
            /**
             * 要生成的密钥算法，例如 "AES"。设置此属性将激活工具。
             */
            private String algorithm;
            /**
             * 要生成的密钥长度（比特）。例如 AES 支持 128, 192, 256。
             */
            private int size = 256;
        }
    }
    
    /**
     * 所有密码学处理器配置的基类。
     */
    @Data
    public static abstract class BaseConfig {
        /**
         * 算法名称。
         * 对称加密支持: "AES", "SM4"。
         * 非对称加密支持: "RSA", "SM2"。
         * 摘要支持: "SHA256", "SM3"。
         */
        private String algorithm;
    }

    /**
     * 对称加密配置。
     */
    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class SymmetricConfig extends BaseConfig {
        /**
         * 对称密钥的来源配置。
         */
        private KeyStoreConfig key;
    }

    /**
     * 非对称加密配置。
     */
    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class AsymmetricConfig extends BaseConfig {
        /**
         * 公钥的来源配置。
         */
        private KeyStoreConfig publicKey;
        /**
         * 私钥的来源配置。
         */
        private KeyStoreConfig privateKey;

    }

    /**
     * 摘要（哈希）配置。
     */
    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class DigestConfig extends BaseConfig {
        /**
         * 可选的盐值，用于增加摘要的安全性。建议为每个摘要器配置独立的、复杂的盐值。
         */
        private String salt;
    }

    /**
     * 密钥来源的通用配置。
     */
    @Data
    public static class KeyStoreConfig {
        /**
         * 密钥存储类型。
         */
        private KeyStoreType type = KeyStoreType.CONFIG;

        /**
         * 当 type=CONFIG 时, 此处为 Base64 编码的密钥字符串。
         */
        private String keyValue;

        /**
         * 当 type=FILE 或 type=CLASSPATH 时, 此处为密钥文件的路径。
         */
        private String path;

        /**
         * 当 type=ENV 时, 此处为操作系统环境变量的名称。
         */
        private String envVariable;

        /**
         * 密钥库密码 (主要用于 .jks, .p12 文件类型的密钥库)。
         */
        private String password;

        /**
         * 密钥在密钥库中的别名。
         */
        private String alias;

        /**
         * 当 type=VAULT 时, Vault 的相关配置。
         */
        private VaultConfig vault;

    }

    /**
     * Vault 相关配置。
     */
    @Data
    public static class VaultConfig {
        /**
         * Vault 服务地址, e.g., "http://localhost:8200"。
         */
        private String address;
        /**
         * Vault 认证 Token。
         */
        private String token;
        /**
         * 密钥在 Vault 中的 KV 引擎路径, e.g., "secret/data/myapp/crypto"。
         */
        private String secretPath;
        /**
         * 密钥在指定路径下的具体 Key, e.g., "aes-key"。
         */
        private String secretKey;
    }

    /**
     * 支持的密钥来源类型。
     */
    public enum KeyStoreType {
        /** 从配置文件的 'key-value' 字段读取 Base64 编码的密钥。仅推荐用于测试。 */
        CONFIG,
        /** 从文件系统路径读取密钥。 */
        FILE,
        /** 从应用的 Classpath 路径读取密钥。 */
        CLASSPATH,
        /** 从操作系统环境变量读取密钥。推荐用于容器化环境。 */
        ENV,
        /** 从 HashiCorp Vault 读取密钥。推荐用于生产环境。 */
        VAULT
    }
}