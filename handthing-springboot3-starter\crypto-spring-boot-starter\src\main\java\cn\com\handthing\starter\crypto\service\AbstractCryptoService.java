package cn.com.handthing.starter.crypto.service;

import cn.com.handthing.starter.crypto.core.CryptoService;

/**
 * 所有密码学服务实现的抽象基类。
 * <p>
 * 提供了注入 Bean 名称的通用能力，用于日志和监控指标的区分。
 *
 * <AUTHOR>
 * @since V1.0.0
 */
public abstract class AbstractCryptoService implements CryptoService {

    /**
     * 当前服务实例在 Spring 上下文中的 Bean 名称。
     */
    protected final String beanName;

    /**
     * 构造一个服务实例。
     *
     * @param beanName Spring Bean 的名称
     */
    protected AbstractCryptoService(String beanName) {
        this.beanName = beanName;
    }
}