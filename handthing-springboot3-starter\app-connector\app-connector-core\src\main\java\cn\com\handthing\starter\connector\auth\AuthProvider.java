package cn.com.handthing.starter.connector.auth;

import cn.com.handthing.starter.connector.PlatformType;
import cn.com.handthing.starter.connector.exception.AuthException;

/**
 * 认证提供者策略接口
 * <p>
 * 定义统一的认证方法，每个第三方平台对应一个具体实现。
 * 支持OAuth 2.0授权流程、Token交换和刷新等功能。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
public interface AuthProvider {

    /**
     * 获取支持的平台类型
     *
     * @return 平台类型
     */
    PlatformType getPlatformType();

    /**
     * 生成授权URL
     * <p>
     * 生成用户授权的URL，用户访问此URL进行授权
     * </p>
     *
     * @param state      自定义状态参数，用于防止CSRF攻击和传递额外信息
     * @param redirectUri 回调地址（可选，如果为null则使用默认配置）
     * @return 授权URL
     * @throws AuthException 如果生成授权URL失败
     */
    String getAuthorizationUrl(String state, String redirectUri) throws AuthException;

    /**
     * 生成授权URL（使用默认回调地址）
     *
     * @param state 自定义状态参数
     * @return 授权URL
     * @throws AuthException 如果生成授权URL失败
     */
    default String getAuthorizationUrl(String state) throws AuthException {
        return getAuthorizationUrl(state, null);
    }

    /**
     * 使用授权码交换访问令牌
     * <p>
     * 在用户授权后，使用授权码换取访问令牌
     * </p>
     *
     * @param code        授权码
     * @param state       状态参数（用于验证）
     * @param redirectUri 回调地址（必须与授权时使用的一致）
     * @return 访问令牌信息
     * @throws AuthException 如果交换令牌失败
     */
    UnifiedAccessToken exchangeToken(String code, String state, String redirectUri) throws AuthException;

    /**
     * 刷新访问令牌
     * <p>
     * 使用刷新令牌获取新的访问令牌（如果平台支持）
     * </p>
     *
     * @param refreshToken 刷新令牌
     * @return 新的访问令牌信息
     * @throws AuthException 如果刷新令牌失败或平台不支持刷新
     */
    UnifiedAccessToken refreshToken(String refreshToken) throws AuthException;

    /**
     * 获取用户信息
     * <p>
     * 使用访问令牌获取用户基本信息
     * </p>
     *
     * @param accessToken 访问令牌
     * @return 用户信息
     * @throws AuthException 如果获取用户信息失败
     */
    UnifiedUserInfo getUserInfo(String accessToken) throws AuthException;

    /**
     * 验证访问令牌是否有效
     * <p>
     * 检查访问令牌是否仍然有效（可选实现）
     * </p>
     *
     * @param accessToken 访问令牌
     * @return 如果令牌有效返回true，否则返回false
     * @throws AuthException 如果验证过程中发生错误
     */
    default boolean validateToken(String accessToken) throws AuthException {
        try {
            getUserInfo(accessToken);
            return true;
        } catch (AuthException e) {
            return false;
        }
    }

    /**
     * 撤销访问令牌
     * <p>
     * 撤销访问令牌，使其失效（如果平台支持）
     * </p>
     *
     * @param accessToken 访问令牌
     * @throws AuthException 如果撤销令牌失败或平台不支持撤销
     */
    default void revokeToken(String accessToken) throws AuthException {
        throw new AuthException("Token revocation not supported for platform: " + getPlatformType());
    }

    /**
     * 检查平台是否支持刷新令牌
     *
     * @return 如果支持返回true，否则返回false
     */
    default boolean supportsRefreshToken() {
        return true;
    }

    /**
     * 检查平台是否支持令牌撤销
     *
     * @return 如果支持返回true，否则返回false
     */
    default boolean supportsTokenRevocation() {
        return false;
    }

    /**
     * 获取平台特定的授权范围
     * <p>
     * 返回该平台默认请求的授权范围
     * </p>
     *
     * @return 授权范围字符串
     */
    default String getDefaultScope() {
        return null;
    }
}
