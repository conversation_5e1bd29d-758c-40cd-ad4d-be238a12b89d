package cn.com.handthing.starter.cache.redis;

import cn.com.handthing.starter.cache.CacheService;
import cn.com.handthing.starter.cache.CacheStats;
import cn.com.handthing.starter.cache.exception.CacheOperationException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.data.redis.cache.RedisCache;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;

import java.time.Duration;
import java.util.concurrent.TimeUnit;

/**
 * Redis缓存服务实现
 * <p>
 * 基于Redis的分布式缓存服务，支持TTL、管道操作等高级特性
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@RequiredArgsConstructor
public class RedisCacheService implements CacheService {

    private final RedisCacheManager cacheManager;
    private final RedisTemplate<String, Object> redisTemplate;
    private final RedisConnectionFactory connectionFactory;

    @Override
    public <T> T get(String cacheName, String key) {
        try {
            Cache cache = getCache(cacheName);
            Cache.ValueWrapper wrapper = cache.get(key);
            return wrapper != null ? (T) wrapper.get() : null;
        } catch (Exception e) {
            log.error("Failed to get cache value for cacheName: {}, key: {}", cacheName, key, e);
            throw new CacheOperationException("Failed to get cache value", e);
        }
    }

    @Override
    public <T> T get(String cacheName, String key, Class<T> type) {
        try {
            Cache cache = getCache(cacheName);
            return cache.get(key, type);
        } catch (Exception e) {
            log.error("Failed to get cache value for cacheName: {}, key: {}, type: {}", 
                    cacheName, key, type.getName(), e);
            throw new CacheOperationException("Failed to get cache value", e);
        }
    }

    @Override
    public void put(String cacheName, String key, Object value) {
        try {
            Cache cache = getCache(cacheName);
            cache.put(key, value);
            log.debug("Put cache value for cacheName: {}, key: {}", cacheName, key);
        } catch (Exception e) {
            log.error("Failed to put cache value for cacheName: {}, key: {}", cacheName, key, e);
            throw new CacheOperationException("Failed to put cache value", e);
        }
    }

    @Override
    public void put(String cacheName, String key, Object value, Duration ttl) {
        try {
            // 使用RedisTemplate直接设置TTL
            String fullKey = buildFullKey(cacheName, key);
            redisTemplate.opsForValue().set(fullKey, value, ttl.toSeconds(), TimeUnit.SECONDS);
            log.debug("Put cache value with TTL for cacheName: {}, key: {}, ttl: {}", cacheName, key, ttl);
        } catch (Exception e) {
            log.error("Failed to put cache value with TTL for cacheName: {}, key: {}", cacheName, key, e);
            throw new CacheOperationException("Failed to put cache value with TTL", e);
        }
    }

    @Override
    public void evict(String cacheName, String key) {
        try {
            Cache cache = getCache(cacheName);
            cache.evict(key);
            log.debug("Evicted cache value for cacheName: {}, key: {}", cacheName, key);
        } catch (Exception e) {
            log.error("Failed to evict cache value for cacheName: {}, key: {}", cacheName, key, e);
            throw new CacheOperationException("Failed to evict cache value", e);
        }
    }

    @Override
    public void clear(String cacheName) {
        try {
            Cache cache = getCache(cacheName);
            cache.clear();
            log.debug("Cleared cache for cacheName: {}", cacheName);
        } catch (Exception e) {
            log.error("Failed to clear cache for cacheName: {}", cacheName, e);
            throw new CacheOperationException("Failed to clear cache", e);
        }
    }

    @Override
    public boolean exists(String cacheName, String key) {
        try {
            Cache cache = getCache(cacheName);
            return cache.get(key) != null;
        } catch (Exception e) {
            log.error("Failed to check cache existence for cacheName: {}, key: {}", cacheName, key, e);
            return false;
        }
    }

    @Override
    public CacheStats getStats(String cacheName) {
        try {
            return cacheManager.getCacheStats(cacheName);
        } catch (Exception e) {
            log.error("Failed to get cache stats for cacheName: {}", cacheName, e);
            return null;
        }
    }

    /**
     * 设置缓存过期时间
     *
     * @param cacheName 缓存名称
     * @param key       缓存键
     * @param ttl       过期时间
     * @return 是否设置成功
     */
    public boolean expire(String cacheName, String key, Duration ttl) {
        try {
            String fullKey = buildFullKey(cacheName, key);
            Boolean result = redisTemplate.expire(fullKey, ttl.toSeconds(), TimeUnit.SECONDS);
            log.debug("Set expiration for cacheName: {}, key: {}, ttl: {}, result: {}", 
                    cacheName, key, ttl, result);
            return Boolean.TRUE.equals(result);
        } catch (Exception e) {
            log.error("Failed to set expiration for cacheName: {}, key: {}", cacheName, key, e);
            return false;
        }
    }

    /**
     * 获取缓存剩余过期时间
     *
     * @param cacheName 缓存名称
     * @param key       缓存键
     * @return 剩余过期时间（秒），-1表示永不过期，-2表示键不存在
     */
    public long getExpire(String cacheName, String key) {
        try {
            String fullKey = buildFullKey(cacheName, key);
            Long expire = redisTemplate.getExpire(fullKey, TimeUnit.SECONDS);
            return expire != null ? expire : -2;
        } catch (Exception e) {
            log.error("Failed to get expiration for cacheName: {}, key: {}", cacheName, key, e);
            return -2;
        }
    }

    /**
     * 如果不存在则放入缓存
     *
     * @param cacheName 缓存名称
     * @param key       缓存键
     * @param value     缓存值
     * @return 如果已存在则返回现有值，否则返回null
     */
    public <T> T putIfAbsent(String cacheName, String key, Object value) {
        try {
            Cache cache = getCache(cacheName);
            Cache.ValueWrapper existing = cache.putIfAbsent(key, value);
            return existing != null ? (T) existing.get() : null;
        } catch (Exception e) {
            log.error("Failed to putIfAbsent cache value for cacheName: {}, key: {}", cacheName, key, e);
            throw new CacheOperationException("Failed to putIfAbsent cache value", e);
        }
    }

    /**
     * 获取Redis连接信息
     *
     * @return Redis连接信息
     */
    public String getRedisInfo() {
        try {
            try (RedisConnection connection = connectionFactory.getConnection()) {
                return connection.info().toString();
            }
        } catch (Exception e) {
            log.error("Failed to get Redis info", e);
            return "Failed to get Redis info: " + e.getMessage();
        }
    }

    /**
     * 执行Redis PING命令
     *
     * @return PING响应
     */
    public String ping() {
        try {
            try (RedisConnection connection = connectionFactory.getConnection()) {
                return connection.ping();
            }
        } catch (Exception e) {
            log.error("Failed to ping Redis", e);
            return "FAILED: " + e.getMessage();
        }
    }

    /**
     * 获取Redis数据库大小
     *
     * @return 数据库中键的数量
     */
    public long dbSize() {
        try {
            try (RedisConnection connection = connectionFactory.getConnection()) {
                return connection.dbSize();
            }
        } catch (Exception e) {
            log.error("Failed to get Redis database size", e);
            return -1;
        }
    }

    /**
     * 构建完整的缓存键
     *
     * @param cacheName 缓存名称
     * @param key       缓存键
     * @return 完整的缓存键
     */
    private String buildFullKey(String cacheName, String key) {
        // 这里简化处理，实际应该根据缓存配置构建键
        return cacheName + "::" + key;
    }

    /**
     * 获取缓存实例
     *
     * @param cacheName 缓存名称
     * @return 缓存实例
     * @throws CacheOperationException 如果缓存不存在
     */
    private Cache getCache(String cacheName) {
        Cache cache = cacheManager.getCache(cacheName);
        if (cache == null) {
            throw new CacheOperationException("Cache not found: " + cacheName);
        }
        return cache;
    }
}