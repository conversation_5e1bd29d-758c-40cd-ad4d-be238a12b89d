package cn.com.handthing.testapp.controller;

import cn.com.handthing.testapp.model.ApiResponse;
import cn.com.handthing.testapp.model.CreateUserRequest;
import cn.com.handthing.testapp.model.User;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 用户管理控制器
 * 
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/users")
@Tag(name = "用户管理", description = "用户相关操作接口，包括用户的增删改查等功能")
@Validated
public class UserController {

    private final ConcurrentHashMap<Long, User> userStorage = new ConcurrentHashMap<>();
    private final AtomicLong idGenerator = new AtomicLong(1);

    public UserController() {
        // 初始化一些测试数据
        initTestData();
    }

    private void initTestData() {
        User user1 = new User(1L, "张三", "<EMAIL>", 25, "13800138001", "ACTIVE", "测试用户1");
        User user2 = new User(2L, "李四", "<EMAIL>", 30, "13800138002", "ACTIVE", "测试用户2");
        User user3 = new User(3L, "王五", "<EMAIL>", 28, "13800138003", "INACTIVE", "测试用户3");
        
        userStorage.put(1L, user1);
        userStorage.put(2L, user2);
        userStorage.put(3L, user3);
        idGenerator.set(4);
    }

    @GetMapping
    @Operation(summary = "获取用户列表", description = "分页获取用户列表，支持按状态筛选")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "获取成功",
                    content = @Content(schema = @Schema(implementation = ApiResponse.class))),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "请求参数错误"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public ResponseEntity<ApiResponse<List<User>>> getUsers(
            @Parameter(description = "页码，从1开始", example = "1")
            @RequestParam(defaultValue = "1") @Min(1) Integer page,
            @Parameter(description = "每页大小", example = "10")
            @RequestParam(defaultValue = "10") @Min(1) Integer size,
            @Parameter(description = "用户状态筛选", example = "ACTIVE")
            @RequestParam(required = false) String status) {
        
        log.info("获取用户列表 - page: {}, size: {}, status: {}", page, size, status);
        
        List<User> users = new ArrayList<>(userStorage.values());
        
        // 按状态筛选
        if (status != null && !status.trim().isEmpty()) {
            users = users.stream()
                    .filter(user -> status.equals(user.getStatus()))
                    .toList();
        }
        
        // 简单分页
        int start = (page - 1) * size;
        int end = Math.min(start + size, users.size());
        List<User> pagedUsers = users.subList(Math.max(0, start), Math.max(0, end));
        
        return ResponseEntity.ok(ApiResponse.success("获取用户列表成功", pagedUsers));
    }

    @GetMapping("/{id}")
    @Operation(summary = "根据ID获取用户", description = "根据用户ID获取用户详细信息")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "获取成功"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "用户不存在"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "请求参数错误")
    })
    public ResponseEntity<ApiResponse<User>> getUser(
            @Parameter(description = "用户ID", required = true, example = "1")
            @PathVariable @Min(1) Long id) {
        
        log.info("获取用户详情 - id: {}", id);
        
        User user = userStorage.get(id);
        if (user == null) {
            return ResponseEntity.status(404)
                    .body(ApiResponse.error(404, "用户不存在"));
        }
        
        return ResponseEntity.ok(ApiResponse.success("获取用户成功", user));
    }

    @PostMapping
    @Operation(summary = "创建用户", description = "创建新的用户账户")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "201", description = "创建成功"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "请求参数错误"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "409", description = "用户已存在")
    })
    public ResponseEntity<ApiResponse<User>> createUser(
            @Parameter(description = "创建用户请求", required = true)
            @RequestBody @Valid CreateUserRequest request) {
        
        log.info("创建用户 - request: {}", request);
        
        // 检查邮箱是否已存在
        boolean emailExists = userStorage.values().stream()
                .anyMatch(user -> user.getEmail().equals(request.getEmail()));
        
        if (emailExists) {
            return ResponseEntity.status(409)
                    .body(ApiResponse.error(409, "邮箱已存在"));
        }
        
        // 创建新用户
        Long newId = idGenerator.getAndIncrement();
        User newUser = new User(
                newId,
                request.getUsername(),
                request.getEmail(),
                request.getAge(),
                request.getPhone(),
                "ACTIVE",
                request.getRemark()
        );
        
        userStorage.put(newId, newUser);
        
        return ResponseEntity.status(201)
                .body(ApiResponse.success("创建用户成功", newUser));
    }

    @PutMapping("/{id}")
    @Operation(summary = "更新用户", description = "根据用户ID更新用户信息")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "更新成功"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "用户不存在"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "请求参数错误")
    })
    public ResponseEntity<ApiResponse<User>> updateUser(
            @Parameter(description = "用户ID", required = true, example = "1")
            @PathVariable @Min(1) Long id,
            @Parameter(description = "更新用户请求", required = true)
            @RequestBody @Valid CreateUserRequest request) {
        
        log.info("更新用户 - id: {}, request: {}", id, request);
        
        User existingUser = userStorage.get(id);
        if (existingUser == null) {
            return ResponseEntity.status(404)
                    .body(ApiResponse.error(404, "用户不存在"));
        }
        
        // 检查邮箱是否被其他用户使用
        boolean emailConflict = userStorage.values().stream()
                .anyMatch(user -> !user.getId().equals(id) && user.getEmail().equals(request.getEmail()));
        
        if (emailConflict) {
            return ResponseEntity.status(409)
                    .body(ApiResponse.error(409, "邮箱已被其他用户使用"));
        }
        
        // 更新用户信息
        existingUser.setUsername(request.getUsername());
        existingUser.setEmail(request.getEmail());
        existingUser.setAge(request.getAge());
        existingUser.setPhone(request.getPhone());
        existingUser.setRemark(request.getRemark());
        
        return ResponseEntity.ok(ApiResponse.success("更新用户成功", existingUser));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除用户", description = "根据用户ID删除用户")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "删除成功"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "用户不存在"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "请求参数错误")
    })
    public ResponseEntity<ApiResponse<Void>> deleteUser(
            @Parameter(description = "用户ID", required = true, example = "1")
            @PathVariable @Min(1) Long id) {
        
        log.info("删除用户 - id: {}", id);
        
        User removedUser = userStorage.remove(id);
        if (removedUser == null) {
            return ResponseEntity.status(404)
                    .body(ApiResponse.error(404, "用户不存在"));
        }
        
        return ResponseEntity.ok(ApiResponse.success("删除用户成功", null));
    }

    @PatchMapping("/{id}/status")
    @Operation(summary = "更新用户状态", description = "更新用户的状态（激活/禁用/封禁）")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "状态更新成功"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "用户不存在"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "请求参数错误")
    })
    public ResponseEntity<ApiResponse<User>> updateUserStatus(
            @Parameter(description = "用户ID", required = true, example = "1")
            @PathVariable @Min(1) Long id,
            @Parameter(description = "新状态", required = true, example = "ACTIVE")
            @RequestParam String status) {
        
        log.info("更新用户状态 - id: {}, status: {}", id, status);
        
        // 验证状态值
        if (!List.of("ACTIVE", "INACTIVE", "BANNED").contains(status)) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(400, "无效的状态值"));
        }
        
        User user = userStorage.get(id);
        if (user == null) {
            return ResponseEntity.status(404)
                    .body(ApiResponse.error(404, "用户不存在"));
        }
        
        user.setStatus(status);
        
        return ResponseEntity.ok(ApiResponse.success("状态更新成功", user));
    }
}
