package cn.com.handthing.starter.connector.config;

import cn.com.handthing.starter.cache.CacheService;
import cn.com.handthing.starter.connector.auth.AuthService;
import cn.com.handthing.starter.connector.token.TokenManager;
import org.junit.jupiter.api.Test;
import org.springframework.boot.autoconfigure.AutoConfigurations;
import org.springframework.boot.test.context.runner.ApplicationContextRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;

/**
 * AppConnectorAutoConfiguration 单元测试
 *
 * <AUTHOR>
 * @since V1.0.0
 */
class AppConnectorAutoConfigurationTest {

    private final ApplicationContextRunner contextRunner = new ApplicationContextRunner()
            .withConfiguration(AutoConfigurations.of(AppConnectorAutoConfiguration.class))
            .withUserConfiguration(TestCacheConfiguration.class);

    @Test
    void testAutoConfigurationEnabled() {
        contextRunner
                .withPropertyValues(
                        "handthing.connector.enabled=true",
                        "handthing.connector.redirect-uri-prefix=https://test.com"
                )
                .run(context -> {
                    assertThat(context).hasSingleBean(AuthService.class);
                    assertThat(context).hasSingleBean(TokenManager.class);
                    assertThat(context).hasSingleBean(AppConnectorProperties.class);
                    assertThat(context).hasSingleBean(AppConnectorAutoConfiguration.AppConnectorConfigInfo.class);
                });
    }

    @Test
    void testAutoConfigurationDisabled() {
        contextRunner
                .withPropertyValues("handthing.connector.enabled=false")
                .run(context -> {
                    assertThat(context).doesNotHaveBean(AuthService.class);
                    assertThat(context).doesNotHaveBean(TokenManager.class);
                });
    }

    @Test
    void testPropertiesBinding() {
        contextRunner
                .withPropertyValues(
                        "handthing.connector.enabled=true",
                        "handthing.connector.redirect-uri-prefix=https://example.com",
                        "handthing.connector.token-cache-name=custom-tokens",
                        "handthing.connector.enable-unified-callback=false",
                        "handthing.connector.callback-path-prefix=/custom/callback",
                        "handthing.connector.enable-event-publishing=false"
                )
                .run(context -> {
                    AppConnectorProperties properties = context.getBean(AppConnectorProperties.class);
                    assertThat(properties.isEnabled()).isTrue();
                    assertThat(properties.getRedirectUriPrefix()).isEqualTo("https://example.com");
                    assertThat(properties.getTokenCacheName()).isEqualTo("custom-tokens");
                    assertThat(properties.isEnableUnifiedCallback()).isFalse();
                    assertThat(properties.getCallbackPathPrefix()).isEqualTo("/custom/callback");
                    assertThat(properties.isEnableEventPublishing()).isFalse();
                });
    }

    @Test
    void testPlatformConfigurationBinding() {
        contextRunner
                .withPropertyValues(
                        "handthing.connector.enabled=true",
                        "handthing.connector.redirect-uri-prefix=https://example.com",
                        "handthing.connector.wecom.enabled=true",
                        "handthing.connector.wecom.corp-id=test-corp-id",
                        "handthing.connector.wecom.agent-id=test-agent-id",
                        "handthing.connector.wecom.secret=test-secret",
                        "handthing.connector.dingtalk.enabled=true",
                        "handthing.connector.dingtalk.app-key=test-app-key",
                        "handthing.connector.dingtalk.app-secret=test-app-secret"
                )
                .run(context -> {
                    AppConnectorProperties properties = context.getBean(AppConnectorProperties.class);
                    
                    // 测试企业微信配置
                    AppConnectorProperties.WeComConfig wecom = properties.getWecom();
                    assertThat(wecom.isEnabled()).isTrue();
                    assertThat(wecom.getCorpId()).isEqualTo("test-corp-id");
                    assertThat(wecom.getAgentId()).isEqualTo("test-agent-id");
                    assertThat(wecom.getSecret()).isEqualTo("test-secret");
                    assertThat(wecom.isValid()).isTrue();
                    
                    // 测试钉钉配置
                    AppConnectorProperties.DingTalkConfig dingtalk = properties.getDingtalk();
                    assertThat(dingtalk.isEnabled()).isTrue();
                    assertThat(dingtalk.getAppKey()).isEqualTo("test-app-key");
                    assertThat(dingtalk.getAppSecret()).isEqualTo("test-app-secret");
                    assertThat(dingtalk.isValid()).isTrue();
                });
    }

    @Test
    void testTokenManagerConfiguration() {
        contextRunner
                .withPropertyValues(
                        "handthing.connector.enabled=true",
                        "handthing.connector.redirect-uri-prefix=https://example.com",
                        "handthing.connector.token-cache-name=test-tokens"
                )
                .run(context -> {
                    TokenManager tokenManager = context.getBean(TokenManager.class);
                    assertThat(tokenManager).isNotNull();
                    
                    AppConnectorProperties properties = context.getBean(AppConnectorProperties.class);
                    assertThat(properties.getTokenCacheName()).isEqualTo("test-tokens");
                });
    }

    @Test
    void testConfigInfoBean() {
        contextRunner
                .withPropertyValues(
                        "handthing.connector.enabled=true",
                        "handthing.connector.redirect-uri-prefix=https://example.com",
                        "handthing.connector.wecom.enabled=true",
                        "handthing.connector.wecom.corp-id=test-corp-id",
                        "handthing.connector.wecom.agent-id=test-agent-id",
                        "handthing.connector.wecom.secret=test-secret"
                )
                .run(context -> {
                    AppConnectorAutoConfiguration.AppConnectorConfigInfo configInfo = 
                            context.getBean(AppConnectorAutoConfiguration.AppConnectorConfigInfo.class);
                    
                    assertThat(configInfo.isEnabled()).isTrue();
                    assertThat(configInfo.getRedirectUriPrefix()).isEqualTo("https://example.com");
                    assertThat(configInfo.getTokenCacheName()).isEqualTo("app-connector-tokens");
                    assertThat(configInfo.isEnableUnifiedCallback()).isTrue();
                    assertThat(configInfo.getCallbackPathPrefix()).isEqualTo("/connector/callback");
                    assertThat(configInfo.isEnableEventPublishing()).isTrue();
                    assertThat(configInfo.getEnabledPlatforms()).contains("wecom");
                });
    }

    /**
     * 测试配置类，提供模拟的CacheService
     */
    @Configuration
    static class TestCacheConfiguration {
        
        @Bean
        public CacheService cacheService() {
            return mock(CacheService.class);
        }
    }
}
