package cn.com.handthing.starter.connector.douyin.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 抖音数据统计模型
 * <p>
 * 抖音数据统计信息的数据结构
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DouyinDataStats {

    /**
     * 日期类型（7、30天）
     */
    @JsonProperty("date_type")
    private Integer dateType;

    /**
     * 统计数据列表
     */
    @JsonProperty("result_list")
    private List<StatsData> resultList;

    /**
     * 获取日期类型描述
     *
     * @return 日期类型描述
     */
    public String getDateTypeDescription() {
        if (dateType == null) {
            return "未知";
        }
        switch (dateType) {
            case 7:
                return "近7天";
            case 30:
                return "近30天";
            default:
                return dateType + "天";
        }
    }

    /**
     * 统计数据
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class StatsData {

        /**
         * 日期
         */
        @JsonProperty("date")
        private String date;

        /**
         * 新增粉丝数
         */
        @JsonProperty("new_fans")
        private Long newFans;

        /**
         * 总粉丝数
         */
        @JsonProperty("total_fans")
        private Long totalFans;

        /**
         * 播放数
         */
        @JsonProperty("play_count")
        private Long playCount;

        /**
         * 点赞数
         */
        @JsonProperty("digg_count")
        private Long diggCount;

        /**
         * 评论数
         */
        @JsonProperty("comment_count")
        private Long commentCount;

        /**
         * 分享数
         */
        @JsonProperty("share_count")
        private Long shareCount;

        /**
         * 转发数
         */
        @JsonProperty("forward_count")
        private Long forwardCount;

        /**
         * 关注数
         */
        @JsonProperty("follow_count")
        private Long followCount;

        /**
         * 个人主页浏览量
         */
        @JsonProperty("profile_uv")
        private Long profileUv;

        /**
         * 获取总互动数
         *
         * @return 总互动数
         */
        public Long getTotalInteraction() {
            long total = 0;
            if (diggCount != null) total += diggCount;
            if (commentCount != null) total += commentCount;
            if (shareCount != null) total += shareCount;
            if (forwardCount != null) total += forwardCount;
            if (followCount != null) total += followCount;
            return total;
        }

        /**
         * 获取互动率
         *
         * @return 互动率（百分比）
         */
        public Double getInteractionRate() {
            if (playCount == null || playCount == 0) {
                return 0.0;
            }
            return (getTotalInteraction().doubleValue() / playCount.doubleValue()) * 100;
        }

        /**
         * 获取粉丝增长率
         *
         * @return 粉丝增长率（百分比）
         */
        public Double getFansGrowthRate() {
            if (totalFans == null || totalFans == 0 || newFans == null) {
                return 0.0;
            }
            long previousFans = totalFans - newFans;
            if (previousFans <= 0) {
                return 100.0;
            }
            return (newFans.doubleValue() / previousFans) * 100;
        }
    }
}
