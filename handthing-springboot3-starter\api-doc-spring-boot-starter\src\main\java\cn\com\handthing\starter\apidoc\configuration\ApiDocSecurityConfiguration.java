package cn.com.handthing.starter.apidoc.configuration;

import cn.com.handthing.starter.apidoc.properties.ApiDocProperties;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.provisioning.InMemoryUserDetailsManager;
import org.springframework.security.web.SecurityFilterChain;

import static org.springframework.security.config.Customizer.withDefaults;

@Configuration
@EnableWebSecurity
// 仅当用户在配置中启用了文档安全时，此配置才生效
@ConditionalOnProperty(prefix = "handthing.api-doc.auth", name = "enabled", havingValue = "true")
public class ApiDocSecurityConfiguration {

    // 定义文档相关的路径
    private static final String[] DOC_URLS = {
            "/swagger-ui.html",
            "/swagger-ui/**",
            "/v3/api-docs/**",
            "/swagger-resources/**",
            "/webjars/**"
    };

    @Bean
    public InMemoryUserDetailsManager apiDocUserDetailsManager(ApiDocProperties properties) {
        ApiDocProperties.Auth authConfig = properties.getAuth();
        
        UserDetails user = User.builder()
                .username(authConfig.getUsername())
                .password("{noop}" + authConfig.getPassword()) // {noop} 表示纯文本密码
                .roles("DOC_USER")
                .build();
        
        return new InMemoryUserDetailsManager(user);
    }

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http, InMemoryUserDetailsManager userDetailsManager) throws Exception {
        http
            .authorizeHttpRequests(authorize -> authorize
                .requestMatchers(DOC_URLS).authenticated() // API 文档需要认证
                .anyRequest().permitAll() // 其他所有请求都允许
            )
            .httpBasic(withDefaults()) // 启用 HTTP Basic 认证
            .formLogin(withDefaults()) // 启用表单登录
            .csrf(AbstractHttpConfigurer::disable) // 关闭 CSRF
            .userDetailsService(userDetailsManager); // 设置用户详情服务

        return http.build();
    }
}