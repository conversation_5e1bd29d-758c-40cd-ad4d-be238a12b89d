package cn.com.handthing.starter.connector.feishu.api;

import cn.com.handthing.starter.connector.feishu.config.FeishuConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * 飞书消息API
 * <p>
 * 提供飞书消息发送相关的API功能，包括文本消息、富文本消息、卡片消息等。
 * 支持企业内部应用和第三方应用的消息推送功能。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "handthing.connector.feishu", name = "enabled", havingValue = "true")
public class FeishuMessageApi {

    private final FeishuConfig config;
    private final RestTemplate restTemplate;

    /**
     * 发送文本消息
     *
     * @param accessToken 访问令牌
     * @param receiveId   接收者ID（用户open_id、user_id、email等）
     * @param receiveIdType 接收者ID类型（open_id、user_id、email等）
     * @param content     消息内容
     * @return 发送结果
     */
    public Map<String, Object> sendTextMessage(String accessToken, String receiveId, String receiveIdType, String content) {
        try {
            log.debug("Sending Feishu text message to: {}", receiveId);

            String url = config.getEffectiveApiBaseUrl() + "/open-apis/im/v1/messages?receive_id_type=" + receiveIdType;

            Map<String, Object> message = new HashMap<>();
            message.put("receive_id", receiveId);
            message.put("msg_type", "text");
            
            Map<String, String> textContent = new HashMap<>();
            textContent.put("text", content);
            message.put("content", textContent);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + accessToken);

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(message, headers);

            Map<String, Object> response = restTemplate.postForObject(url, request, Map.class);
            log.debug("Feishu text message sent, response: {}", response);

            return response;

        } catch (Exception e) {
            log.error("Failed to send Feishu text message", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", -1);
            errorResponse.put("msg", "Failed to send message: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 发送富文本消息
     *
     * @param accessToken 访问令牌
     * @param receiveId   接收者ID
     * @param receiveIdType 接收者ID类型
     * @param richText    富文本内容
     * @return 发送结果
     */
    public Map<String, Object> sendRichTextMessage(String accessToken, String receiveId, String receiveIdType, Map<String, Object> richText) {
        try {
            log.debug("Sending Feishu rich text message to: {}", receiveId);

            String url = config.getEffectiveApiBaseUrl() + "/open-apis/im/v1/messages?receive_id_type=" + receiveIdType;

            Map<String, Object> message = new HashMap<>();
            message.put("receive_id", receiveId);
            message.put("msg_type", "post");
            message.put("content", richText);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + accessToken);

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(message, headers);

            Map<String, Object> response = restTemplate.postForObject(url, request, Map.class);
            log.debug("Feishu rich text message sent, response: {}", response);

            return response;

        } catch (Exception e) {
            log.error("Failed to send Feishu rich text message", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", -1);
            errorResponse.put("msg", "Failed to send rich text message: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 发送卡片消息
     *
     * @param accessToken 访问令牌
     * @param receiveId   接收者ID
     * @param receiveIdType 接收者ID类型
     * @param cardContent 卡片内容
     * @return 发送结果
     */
    public Map<String, Object> sendCardMessage(String accessToken, String receiveId, String receiveIdType, Map<String, Object> cardContent) {
        try {
            log.debug("Sending Feishu card message to: {}", receiveId);

            String url = config.getEffectiveApiBaseUrl() + "/open-apis/im/v1/messages?receive_id_type=" + receiveIdType;

            Map<String, Object> message = new HashMap<>();
            message.put("receive_id", receiveId);
            message.put("msg_type", "interactive");
            message.put("content", cardContent);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + accessToken);

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(message, headers);

            Map<String, Object> response = restTemplate.postForObject(url, request, Map.class);
            log.debug("Feishu card message sent, response: {}", response);

            return response;

        } catch (Exception e) {
            log.error("Failed to send Feishu card message", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", -1);
            errorResponse.put("msg", "Failed to send card message: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 发送图片消息
     *
     * @param accessToken 访问令牌
     * @param receiveId   接收者ID
     * @param receiveIdType 接收者ID类型
     * @param imageKey    图片key
     * @return 发送结果
     */
    public Map<String, Object> sendImageMessage(String accessToken, String receiveId, String receiveIdType, String imageKey) {
        try {
            log.debug("Sending Feishu image message to: {}", receiveId);

            String url = config.getEffectiveApiBaseUrl() + "/open-apis/im/v1/messages?receive_id_type=" + receiveIdType;

            Map<String, Object> message = new HashMap<>();
            message.put("receive_id", receiveId);
            message.put("msg_type", "image");
            
            Map<String, String> imageContent = new HashMap<>();
            imageContent.put("image_key", imageKey);
            message.put("content", imageContent);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + accessToken);

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(message, headers);

            Map<String, Object> response = restTemplate.postForObject(url, request, Map.class);
            log.debug("Feishu image message sent, response: {}", response);

            return response;

        } catch (Exception e) {
            log.error("Failed to send Feishu image message", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", -1);
            errorResponse.put("msg", "Failed to send image message: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 创建简单的富文本内容
     *
     * @param title 标题
     * @param content 内容
     * @return 富文本内容
     */
    public Map<String, Object> createSimpleRichText(String title, String content) {
        Map<String, Object> post = new HashMap<>();
        Map<String, Object> zhCn = new HashMap<>();
        
        // 标题
        Map<String, Object> titleElement = new HashMap<>();
        titleElement.put("tag", "text");
        titleElement.put("text", title);
        zhCn.put("title", titleElement);
        
        // 内容
        Map<String, Object> contentElement = new HashMap<>();
        contentElement.put("tag", "text");
        contentElement.put("text", content);
        zhCn.put("content", new Object[][]{{contentElement}});
        
        post.put("zh_cn", zhCn);
        return post;
    }
}
