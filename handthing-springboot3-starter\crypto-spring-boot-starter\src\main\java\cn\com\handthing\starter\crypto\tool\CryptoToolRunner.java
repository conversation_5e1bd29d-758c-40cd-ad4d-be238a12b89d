package cn.com.handthing.starter.crypto.tool;

import cn.com.handthing.starter.crypto.properties.CryptoProperties;
import org.springframework.boot.CommandLineRunner;

import javax.crypto.KeyGenerator;
import java.security.Key;
import java.security.SecureRandom;
import java.util.Base64;
import java.util.Objects;

/**
 * 用于生成对称密钥的命令行工具。
 * <p>
 * 当通过命令行参数激活时，此工具会生成一个指定算法和长度的密钥，
 * 将其以 Base64 格式打印到控制台，然后退出应用程序。
 * 这避免了在正常应用启动时执行此逻辑。
 *
 * <AUTHOR>
 * @since V1.0.0
 */
public class CryptoToolRunner implements CommandLineRunner {

    private final CryptoProperties.Tool.GenerateKey config;

    /**
     * 构造一个新的 CryptoToolRunner。
     *
     * @param config 密钥生成工具的配置属性
     */
    public CryptoToolRunner(CryptoProperties.Tool.GenerateKey config) {
        this.config = Objects.requireNonNull(config, "GenerateKey config must not be null");
    }

    /**
     * 执行密钥生成逻辑。
     *
     * @param args 命令行参数（未使用）
     * @throws Exception 如果密钥生成失败
     */
    @Override
    public void run(String... args) throws Exception {
        String algorithm = config.getAlgorithm();
        int keySize = config.getSize();

        System.out.println("--- HandThing Crypto Key Generation Tool ---");
        System.out.printf("Algorithm: %s, Key Size: %d bits%n", algorithm, keySize);
        System.out.println("----------------------------------------------");

        try {
            KeyGenerator keyGen = KeyGenerator.getInstance(algorithm);
            keyGen.init(keySize, new SecureRandom());
            Key key = keyGen.generateKey();

            String encodedKey = Base64.getEncoder().encodeToString(key.getEncoded());

            System.out.println("Generated Base64 Encoded Key:");
            System.out.println("==============================================");
            System.out.println(encodedKey);
            System.out.println("==============================================");
            System.out.println("You can use this key in your application.yml with 'key-value' property under a symmetric key configuration.");

        } catch (Exception e) {
            System.err.println("ERROR: Key generation failed. Please check if the algorithm and key size are valid for your JDK/JCE provider.");
            e.printStackTrace(System.err);
            // 以非零状态码退出，表示错误
            System.exit(1);
        }

        // 成功生成密钥后，以状态码 0 强制退出应用，避免执行任何后续的业务逻辑
        System.exit(0);
    }
}