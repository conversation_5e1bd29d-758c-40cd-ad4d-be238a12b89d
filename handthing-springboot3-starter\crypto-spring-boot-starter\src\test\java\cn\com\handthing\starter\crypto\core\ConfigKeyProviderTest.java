package cn.com.handthing.starter.crypto.core;

import cn.com.handthing.starter.crypto.properties.CryptoProperties.KeyStoreConfig;
import cn.com.handthing.starter.crypto.exception.CryptoException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import javax.crypto.spec.SecretKeySpec;
import java.security.Key;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

/**
 * ConfigKeyProvider 的单元测试。
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@DisplayName("ConfigKeyProvider Tests")
public class ConfigKeyProviderTest {

    private static final String VALID_BASE64_KEY = "AAECAwQFBgcICQoLDA0ODw=="; // 16 bytes
    private static final String INVALID_BASE64_KEY = "invalid-base64-!";

    @Test
    @DisplayName("应能成功从有效的 Base64 字符串加载密钥")
    void shouldLoadKeyFromValidBase64() throws Exception {
        KeyStoreConfig config = new KeyStoreConfig();
        config.setKeyValue(VALID_BASE64_KEY);

        ConfigKeyProvider provider = new ConfigKeyProvider(config);
        provider.checkReady();

        Key key = provider.getKey();
        byte[] keyBytes = provider.getKeyBytes();

        assertThat(key).isNotNull().isInstanceOf(SecretKeySpec.class);
        assertThat(key.getAlgorithm()).isEqualTo("RAW");
        assertThat(keyBytes).hasSize(16);
        assertThat(key.getEncoded()).isEqualTo(keyBytes);
    }

    @Test
    @DisplayName("当 key-value 为 null 或为空时应抛出异常")
    void shouldThrowExceptionForNullOrEmptyKeyValue() {
        KeyStoreConfig configNull = new KeyStoreConfig();
        configNull.setKeyValue(null);

        KeyStoreConfig configEmpty = new KeyStoreConfig();
        configEmpty.setKeyValue("");

        assertThatThrownBy(() -> new ConfigKeyProvider(configNull))
                .isInstanceOf(IllegalArgumentException.class);
        assertThatThrownBy(() -> new ConfigKeyProvider(configEmpty))
                .isInstanceOf(IllegalArgumentException.class);
    }

    @Test
    @DisplayName("当 key-value 是无效的 Base64 时应抛出异常")
    void shouldThrowExceptionForInvalidBase64() {
        KeyStoreConfig config = new KeyStoreConfig();
        config.setKeyValue(INVALID_BASE64_KEY);

        assertThatThrownBy(() -> new ConfigKeyProvider(config))
                .isInstanceOf(CryptoException.class)
                .hasMessageContaining("Invalid Base64");
    }
}
