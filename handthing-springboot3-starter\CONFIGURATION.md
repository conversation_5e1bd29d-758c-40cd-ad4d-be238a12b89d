# ⚙️ HandThing DataLayer 配置参考

本文档详细介绍了 HandThing DataLayer Spring Boot Starter 系列的所有配置选项。

## 📋 目录

- [🔑 ID生成器配置](#-id生成器配置)
- [🏛️ 数据层核心配置](#️-数据层核心配置)
- [🏢 租户插件配置](#-租户插件配置)
- [🔐 数据权限插件配置](#-数据权限插件配置)
- [🔧 完整配置示例](#-完整配置示例)

---

## 🔑 ID生成器配置

### 基础配置

```yaml
handthing:
  id:
    enabled: true  # 是否启用ID生成器，默认true
```

### 雪花算法配置

```yaml
handthing:
  id:
    snowflake:
      enabled: true           # 是否启用雪花算法，默认true
      worker-id: 1           # 工作节点ID (0-31)，必须配置
      datacenter-id: 1       # 数据中心ID (0-31)，必须配置
      start-timestamp: 1640995200000  # 起始时间戳，默认2022-01-01
      worker-id-bits: 5      # 工作节点ID位数，默认5
      datacenter-id-bits: 5  # 数据中心ID位数，默认5
      sequence-bits: 12      # 序列号位数，默认12
```

**重要说明**：
- `worker-id` 和 `datacenter-id` 在集群环境中必须唯一
- 同一数据中心内的不同节点使用不同的 `worker-id`
- 不同数据中心使用不同的 `datacenter-id`

### UUID配置

```yaml
handthing:
  id:
    uuid:
      enabled: true          # 是否启用UUID生成器，默认true
      remove-hyphens: false  # 是否移除连字符，默认false
      uppercase: false       # 是否转为大写，默认false
```

### ID池化配置

```yaml
handthing:
  id:
    pool:
      enabled: true          # 是否启用ID池化，默认true
      size: 200             # 池大小，默认200
      threshold: 50         # 补充阈值，默认50
      max-wait-millis: 1000 # 最大等待时间(毫秒)，默认1000
      statistics-enabled: false  # 是否启用统计，默认false
```

### 自定义生成器配置

```yaml
handthing:
  id:
    custom:
      generators:
        - name: "custom-long"     # 生成器名称
          class: "com.example.CustomLongGenerator"  # 实现类
          type: "java.lang.Long"  # 生成的类型
          properties:             # 自定义属性
            prefix: "ID"
            length: 10
```

---

## 🏛️ 数据层核心配置

### 基础配置

```yaml
handthing:
  datalayer:
    enabled: true  # 是否启用数据层功能，默认true
```

### 逻辑删除配置

```yaml
handthing:
  datalayer:
    logic-delete:
      enabled: true              # 是否启用逻辑删除，默认true
      field-name: "deleted"      # 逻辑删除字段名，默认deleted
      deleted-value: "1"         # 已删除值，默认1
      not-deleted-value: "0"     # 未删除值，默认0
```

### 分页配置

```yaml
handthing:
  datalayer:
    pagination:
      enabled: true                    # 是否启用分页，默认true
      default-page-size: 10           # 默认页大小，默认10
      max-page-size: 1000             # 最大页大小，默认1000
      reasonable: true                # 是否启用合理化分页，默认true
      support-methods-arguments: true # 是否支持接口参数传递，默认true
```

### 乐观锁配置

```yaml
handthing:
  datalayer:
    optimistic-lock:
      enabled: true           # 是否启用乐观锁，默认true
      field-name: "version"   # 版本字段名，默认version
```

### 自动填充配置

```yaml
handthing:
  datalayer:
    auto-fill:
      enabled: true                    # 是否启用自动填充，默认true
      id-setter-enabled: true          # 是否启用@IdSetter注解处理，默认true
      audit-enabled: true              # 是否启用审计字段自动填充，默认true
      time-enabled: true               # 是否启用时间字段自动填充，默认true
      create-by-field: "createBy"      # 创建人字段名，默认createBy
      update-by-field: "updateBy"      # 更新人字段名，默认updateBy
      create-time-field: "createTime"  # 创建时间字段名，默认createTime
      update-time-field: "updateTime"  # 更新时间字段名，默认updateTime
      version-field: "version"         # 版本字段名，默认version
      deleted-field: "deleted"         # 逻辑删除字段名，默认deleted
```

---

## 🏢 租户插件配置

### 基础配置

```yaml
handthing:
  datalayer:
    tenant:
      enabled: true  # 是否启用租户功能，默认true
```

### 租户字段配置

```yaml
handthing:
  datalayer:
    tenant:
      tenant-id-column: "tenant_id"    # 租户ID字段名，默认tenant_id
      default-tenant-id: "default"     # 默认租户ID，默认default
      strict-mode: false               # 是否启用严格模式，默认false
      validate-consistency: true       # 是否验证租户一致性，默认true
      auto-fill-enabled: true          # 是否启用租户ID自动填充，默认true
```

### 忽略表配置

```yaml
handthing:
  datalayer:
    tenant:
      ignore-tables:           # 忽略租户处理的表名
        - users
        - roles
        - permissions
        - menus
        - configs
        - dictionaries
        - logs
        - audit_logs
      ignore-table-prefixes:   # 忽略租户处理的表名前缀
        - sys_
        - system_
        - config_
        - dict_
        - menu_
        - role_
        - permission_
        - tenant_
        - user_
        - auth_
        - log_
        - monitor_
        - schedule_
      ignore-system-tables: true  # 是否忽略系统表，默认true
```

### 租户隔离策略

```yaml
handthing:
  datalayer:
    tenant:
      isolation-strategy: COLUMN  # 隔离策略：COLUMN/TABLE/DATABASE/HYBRID
```

### 多数据源配置

```yaml
handthing:
  datalayer:
    tenant:
      data-source:
        multi-data-source-enabled: false    # 是否启用多数据源，默认false
        routing-strategy: "tenant_id"       # 数据源路由策略，默认tenant_id
        default-data-source: "default"      # 默认数据源名称，默认default
        data-source-prefix: "tenant_"       # 数据源前缀，默认tenant_
        cache-enabled: true                 # 是否启用数据源缓存，默认true
        cache-expire-seconds: 3600          # 数据源缓存过期时间，默认3600秒
```

---

## 🔐 数据权限插件配置

### 基础配置

```yaml
handthing:
  datalayer:
    dataauth:
      enabled: true          # 是否启用数据权限功能，默认true
      aop-enabled: true      # 是否启用AOP拦截器，默认true
      strict-mode: false     # 是否启用严格模式，默认false
      default-failure-strategy: "FILTER"  # 默认权限失败策略，默认FILTER
```

### 权限缓存配置

```yaml
handthing:
  datalayer:
    dataauth:
      cache-enabled: true    # 是否启用权限缓存，默认true
      cache:
        type: "memory"       # 缓存类型，默认memory
        expire-seconds: 300  # 缓存过期时间(秒)，默认300
        max-size: 1000      # 最大缓存条目数，默认1000
        key-prefix: "dataauth:"  # 缓存键前缀，默认dataauth:
        statistics-enabled: false  # 是否启用缓存统计，默认false
```

### 超级管理员配置

```yaml
handthing:
  datalayer:
    dataauth:
      super-admin:
        enabled: true        # 是否启用超级管理员跳过权限检查，默认true
        check-strategy: "role"  # 超级管理员检查策略，默认role
        roles:              # 超级管理员角色名称
          - SUPER_ADMIN
          - SYSTEM_ADMIN
        user-ids:           # 超级管理员用户ID
          - admin
          - root
```

### 忽略配置

```yaml
handthing:
  datalayer:
    dataauth:
      ignore-packages:      # 忽略权限检查的包路径
        - cn.com.handthing.starter.dataauth
        - org.springframework
        - com.baomidou.mybatisplus
      ignore-classes:       # 忽略权限检查的类名模式
        - "*Controller"
        - "*Config*"
        - "*Test*"
      ignore-method-patterns:  # 忽略权限检查的方法名模式
        - "get*"
        - "is*"
        - "has*"
        - "toString"
        - "hashCode"
        - "equals"
```

### 权限提供者配置

```yaml
handthing:
  datalayer:
    dataauth:
      provider:
        dept:                    # 部门权限配置
          enabled: true          # 是否启用部门权限，默认true
          default-field: "dept_id"  # 默认部门字段名，默认dept_id
          hierarchy-enabled: true   # 是否支持部门层级，默认true
          hierarchy-query: ""       # 部门层级查询SQL
        user:                    # 用户权限配置
          enabled: true          # 是否启用用户权限，默认true
          default-field: "create_by"  # 默认用户字段名，默认create_by
          user-id-field: "user_id"    # 用户ID字段名，默认user_id
        role:                    # 角色权限配置
          enabled: true          # 是否启用角色权限，默认true
          default-field: "role_id"    # 默认角色字段名，默认role_id
          permission-query: ""        # 角色权限查询SQL
```

---

## 🔧 完整配置示例

### 开发环境配置

```yaml
# application-dev.yml
spring:
  datasource:
    url: ******************************************************************************************************
    username: dev_user
    password: dev_password

handthing:
  id:
    snowflake:
      worker-id: 1
      datacenter-id: 1
    pool:
      enabled: true
      size: 100
      threshold: 25
  datalayer:
    enabled: true
    pagination:
      default-page-size: 20
      max-page-size: 500
    tenant:
      enabled: true
      strict-mode: false
      ignore-tables:
        - users
        - roles
    dataauth:
      enabled: true
      strict-mode: false
      cache:
        expire-seconds: 60  # 开发环境短缓存
```

### 生产环境配置

```yaml
# application-prod.yml
spring:
  datasource:
    url: *****************************************************************************************************
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}

handthing:
  id:
    snowflake:
      worker-id: ${WORKER_ID:1}
      datacenter-id: ${DATACENTER_ID:1}
    pool:
      enabled: true
      size: 500
      threshold: 100
      statistics-enabled: true
  datalayer:
    enabled: true
    pagination:
      default-page-size: 10
      max-page-size: 1000
    tenant:
      enabled: true
      strict-mode: true
      validate-consistency: true
    dataauth:
      enabled: true
      strict-mode: true
      cache:
        type: redis
        expire-seconds: 600
        statistics-enabled: true
```

### 集群环境配置

```yaml
# 节点1配置
handthing:
  id:
    snowflake:
      worker-id: 1
      datacenter-id: 1

# 节点2配置
handthing:
  id:
    snowflake:
      worker-id: 2
      datacenter-id: 1

# 节点3配置
handthing:
  id:
    snowflake:
      worker-id: 3
      datacenter-id: 1
```

## 🔍 配置验证

框架会在启动时自动验证配置的有效性，如果配置有误会抛出异常并给出详细的错误信息。

### 常见配置错误

1. **雪花算法配置错误**：
   ```
   IllegalArgumentException: Worker ID must be between 0 and 31
   ```

2. **分页配置错误**：
   ```
   IllegalArgumentException: Default page size cannot be greater than max page size
   ```

3. **租户配置错误**：
   ```
   IllegalArgumentException: Tenant ID column cannot be null or empty
   ```

## 📊 配置优先级

配置的优先级从高到低：

1. **命令行参数** (`--handthing.id.snowflake.worker-id=1`)
2. **环境变量** (`HANDTHING_ID_SNOWFLAKE_WORKER_ID=1`)
3. **application.yml/properties**
4. **默认值**

## 🔧 动态配置

部分配置支持运行时动态修改（通过Spring Boot Actuator）：

- 缓存配置
- 日志级别
- 统计开关

```bash
# 动态修改缓存过期时间
curl -X POST http://localhost:8080/actuator/env \
  -H "Content-Type: application/json" \
  -d '{"name":"handthing.datalayer.dataauth.cache.expire-seconds","value":"600"}'
```

---

📝 **注意**：配置修改后需要重启应用才能生效（除非明确标注支持动态配置）。
