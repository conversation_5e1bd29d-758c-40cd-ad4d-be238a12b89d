package cn.com.handthing.starter.datalayer.service.impl;

import cn.com.handthing.starter.datalayer.entity.BaseEntity;
import cn.com.handthing.starter.datalayer.mapper.BaseMapper;
import cn.com.handthing.starter.datalayer.service.BaseService;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * 基础Service实现类
 * <p>
 * 实现BaseService接口，提供基础业务操作的默认实现。
 * 集成MyBatis-Plus的ServiceImpl功能。
 * </p>
 *
 * @param <M> Mapper类型
 * @param <T> 实体类型
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
public abstract class BaseServiceImpl<M extends BaseMapper<T>, T> implements BaseService<M, T> {
    
    /**
     * Mapper实例
     */
    @Autowired
    protected M mapper;
    
    @Override
    public M getMapper() {
        return mapper;
    }
    
    // ========================================
    // 查询操作
    // ========================================
    
    @Override
    public Optional<T> getById(Serializable id) {
        return Optional.ofNullable(mapper.selectByIdEnhanced(id));
    }
    
    @Override
    public T findById(Serializable id) {
        return mapper.selectByIdEnhanced(id);
    }
    
    @Override
    public List<T> listByIds(Collection<? extends Serializable> idList) {
        return mapper.selectBatchIdsEnhanced(idList);
    }
    
    @Override
    public List<T> list() {
        return mapper.selectList(null);
    }
    
    @Override
    public List<T> list(Wrapper<T> queryWrapper) {
        return mapper.selectList(queryWrapper);
    }
    
    @Override
    public Optional<T> getOne(Wrapper<T> queryWrapper) {
        return Optional.ofNullable(mapper.selectFirst(queryWrapper));
    }
    
    @Override
    public IPage<T> page(IPage<T> page, Wrapper<T> queryWrapper) {
        return mapper.selectPageEnhanced(page, queryWrapper);
    }
    
    @Override
    public IPage<T> page(long current, long size) {
        return page(new Page<>(current, size), null);
    }
    
    @Override
    public IPage<T> page(long current, long size, Wrapper<T> queryWrapper) {
        return page(new Page<>(current, size), queryWrapper);
    }
    
    @Override
    public long count() {
        return mapper.selectCountEnhanced(null);
    }
    
    @Override
    public long count(Wrapper<T> queryWrapper) {
        return mapper.selectCountEnhanced(queryWrapper);
    }
    
    @Override
    public boolean exists(Serializable id) {
        return mapper.exists(id);
    }
    
    @Override
    public boolean exists(Wrapper<T> queryWrapper) {
        return mapper.exists(queryWrapper);
    }
    
    // ========================================
    // 保存操作
    // ========================================
    
    @Override
    public boolean save(T entity) {
        if (entity == null) {
            return false;
        }
        
        try {
            // 预处理
            preProcess(entity);
            
            // 如果是BaseEntity，调用预插入处理
            if (entity instanceof BaseEntity) {
                ((BaseEntity) entity).preInsert();
            }
            
            // 执行插入
            int result = mapper.insert(entity);
            
            // 如果是BaseEntity，调用后插入处理
            if (entity instanceof BaseEntity) {
                ((BaseEntity) entity).postInsert();
            }
            
            // 后处理
            postProcess(entity);
            
            return result > 0;
            
        } catch (Exception e) {
            log.error("Failed to save entity: {}", entity, e);
            throw new RuntimeException("Failed to save entity", e);
        }
    }
    
    @Override
    public boolean saveBatch(Collection<T> entityList) {
        return saveBatch(entityList, 1000);
    }
    
    @Override
    public boolean saveBatch(Collection<T> entityList, int batchSize) {
        if (entityList == null || entityList.isEmpty()) {
            return true;
        }
        
        try {
            // 分批处理
            List<T> list = entityList instanceof List ? (List<T>) entityList : List.copyOf(entityList);
            
            for (int i = 0; i < list.size(); i += batchSize) {
                int end = Math.min(i + batchSize, list.size());
                List<T> batch = list.subList(i, end);
                
                for (T entity : batch) {
                    if (!save(entity)) {
                        return false;
                    }
                }
            }
            
            return true;
            
        } catch (Exception e) {
            log.error("Failed to save batch entities", e);
            throw new RuntimeException("Failed to save batch entities", e);
        }
    }
    
    @Override
    public boolean saveOrUpdate(T entity) {
        if (entity == null) {
            return false;
        }
        
        // 判断是否为新实体
        if (isNewEntity(entity)) {
            return save(entity);
        } else {
            return updateById(entity);
        }
    }
    
    @Override
    public boolean saveOrUpdateBatch(Collection<T> entityList) {
        if (entityList == null || entityList.isEmpty()) {
            return true;
        }
        
        for (T entity : entityList) {
            if (!saveOrUpdate(entity)) {
                return false;
            }
        }
        
        return true;
    }
    
    // ========================================
    // 更新操作
    // ========================================
    
    @Override
    public boolean updateById(T entity) {
        if (entity == null) {
            return false;
        }
        
        try {
            // 预处理
            preProcess(entity);
            
            // 如果是BaseEntity，调用预更新处理
            if (entity instanceof BaseEntity) {
                ((BaseEntity) entity).preUpdate();
            }
            
            // 执行更新
            int result = mapper.updateByIdSafely(entity);
            
            // 如果是BaseEntity，调用后更新处理
            if (entity instanceof BaseEntity) {
                ((BaseEntity) entity).postUpdate();
            }
            
            // 后处理
            postProcess(entity);
            
            return result > 0;
            
        } catch (Exception e) {
            log.error("Failed to update entity: {}", entity, e);
            throw new RuntimeException("Failed to update entity", e);
        }
    }
    
    @Override
    public boolean update(T entity, Wrapper<T> updateWrapper) {
        if (entity == null) {
            return false;
        }
        
        try {
            preProcess(entity);
            int result = mapper.updateEnhanced(entity, updateWrapper);
            postProcess(entity);
            return result > 0;
        } catch (Exception e) {
            log.error("Failed to update entity with wrapper: {}", entity, e);
            throw new RuntimeException("Failed to update entity", e);
        }
    }
    
    @Override
    public boolean updateBatchById(Collection<T> entityList) {
        return updateBatchById(entityList, 1000);
    }
    
    @Override
    public boolean updateBatchById(Collection<T> entityList, int batchSize) {
        if (entityList == null || entityList.isEmpty()) {
            return true;
        }
        
        try {
            List<T> list = entityList instanceof List ? (List<T>) entityList : List.copyOf(entityList);
            
            for (int i = 0; i < list.size(); i += batchSize) {
                int end = Math.min(i + batchSize, list.size());
                List<T> batch = list.subList(i, end);
                
                for (T entity : batch) {
                    if (!updateById(entity)) {
                        return false;
                    }
                }
            }
            
            return true;
            
        } catch (Exception e) {
            log.error("Failed to update batch entities", e);
            throw new RuntimeException("Failed to update batch entities", e);
        }
    }
    
    // ========================================
    // 删除操作
    // ========================================
    
    @Override
    public boolean removeById(Serializable id) {
        if (id == null) {
            return false;
        }
        
        try {
            int result = mapper.deleteByIdSafely(id);
            return result > 0;
        } catch (Exception e) {
            log.error("Failed to remove entity by id: {}", id, e);
            throw new RuntimeException("Failed to remove entity", e);
        }
    }
    
    @Override
    public boolean removeByIds(Collection<? extends Serializable> idList) {
        if (idList == null || idList.isEmpty()) {
            return true;
        }
        
        try {
            int result = mapper.deleteBatchIdsSafely(idList);
            return result > 0;
        } catch (Exception e) {
            log.error("Failed to remove entities by ids: {}", idList, e);
            throw new RuntimeException("Failed to remove entities", e);
        }
    }
    
    @Override
    public boolean remove(Wrapper<T> queryWrapper) {
        if (queryWrapper == null) {
            return false;
        }
        
        try {
            int result = mapper.delete(queryWrapper);
            return result > 0;
        } catch (Exception e) {
            log.error("Failed to remove entities with wrapper", e);
            throw new RuntimeException("Failed to remove entities", e);
        }
    }
    
    // ========================================
    // 业务操作
    // ========================================
    
    @Override
    public Optional<T> refresh(T entity) {
        if (entity == null) {
            return Optional.empty();
        }
        
        Serializable id = getEntityId(entity);
        if (id == null) {
            return Optional.empty();
        }
        
        return getById(id);
    }
    
    @Override
    public String getAuditInfo(Serializable id) {
        T entity = findById(id);
        if (entity instanceof BaseEntity) {
            return ((BaseEntity) entity).getAuditSummary();
        }
        return "No audit info available";
    }
    
    @Override
    public List<String> validate(T entity) {
        // 默认实现，子类可以重写
        return List.of();
    }
    
    @Override
    public void preProcess(T entity) {
        // 默认实现为空，子类可以重写
    }
    
    @Override
    public void postProcess(T entity) {
        // 默认实现为空，子类可以重写
    }
    
    // ========================================
    // 辅助方法
    // ========================================
    
    /**
     * 判断是否为新实体
     */
    protected boolean isNewEntity(T entity) {
        if (entity instanceof BaseEntity) {
            return ((BaseEntity) entity).isNew();
        }
        
        // 尝试通过ID字段判断
        Serializable id = getEntityId(entity);
        return id == null;
    }
    
    /**
     * 获取实体的ID值
     */
    protected Serializable getEntityId(T entity) {
        if (entity == null) {
            return null;
        }
        
        try {
            // 尝试通过反射获取ID字段
            Field[] fields = entity.getClass().getDeclaredFields();
            for (Field field : fields) {
                if ("id".equals(field.getName()) || field.getName().endsWith("Id")) {
                    field.setAccessible(true);
                    Object value = field.get(entity);
                    return value instanceof Serializable ? (Serializable) value : null;
                }
            }
        } catch (Exception e) {
            log.debug("Failed to get entity id", e);
        }
        
        return null;
    }
}
