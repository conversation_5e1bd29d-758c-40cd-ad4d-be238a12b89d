package cn.com.handthing.starter.cache.level;

import cn.com.handthing.starter.cache.exception.CacheOperationException;
import cn.com.handthing.starter.cache.level.sync.CacheSyncService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cache.Cache;
import org.springframework.cache.support.SimpleValueWrapper;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * LevelCacheService 单元测试
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@ExtendWith(MockitoExtension.class)
class LevelCacheServiceTest {

    @Mock
    private LevelCacheManager cacheManager;

    @Mock
    private Cache cache;

    @Mock
    private LevelCache levelCache;

    @Mock
    private CacheSyncService syncService;

    private LevelCacheService cacheService;

    @BeforeEach
    void setUp() {
        cacheService = new LevelCacheService(cacheManager);
    }

    @Test
    void testGet_Success() {
        // Given
        String cacheName = "testCache";
        String key = "testKey";
        String value = "testValue";
        SimpleValueWrapper wrapper = new SimpleValueWrapper(value);
        
        when(cacheManager.getCache(cacheName)).thenReturn(cache);
        when(cache.get(key)).thenReturn(wrapper);

        // When
        String result = cacheService.get(cacheName, key);

        // Then
        assertEquals(value, result);
        verify(cacheManager).getCache(cacheName);
        verify(cache).get(key);
    }

    @Test
    void testGet_NotFound() {
        // Given
        String cacheName = "testCache";
        String key = "testKey";
        
        when(cacheManager.getCache(cacheName)).thenReturn(cache);
        when(cache.get(key)).thenReturn(null);

        // When
        String result = cacheService.get(cacheName, key);

        // Then
        assertNull(result);
    }

    @Test
    void testGet_CacheNotFound() {
        // Given
        String cacheName = "testCache";
        String key = "testKey";
        
        when(cacheManager.getCache(cacheName)).thenReturn(null);

        // When & Then
        assertThrows(CacheOperationException.class, () -> {
            cacheService.get(cacheName, key);
        });
    }

    @Test
    void testGetWithType_Success() {
        // Given
        String cacheName = "testCache";
        String key = "testKey";
        String value = "testValue";
        
        when(cacheManager.getCache(cacheName)).thenReturn(cache);
        when(cache.get(key, String.class)).thenReturn(value);

        // When
        String result = cacheService.get(cacheName, key, String.class);

        // Then
        assertEquals(value, result);
        verify(cache).get(key, String.class);
    }

    @Test
    void testPut_Success() {
        // Given
        String cacheName = "testCache";
        String key = "testKey";
        String value = "testValue";
        
        when(cacheManager.getCache(cacheName)).thenReturn(cache);

        // When
        cacheService.put(cacheName, key, value);

        // Then
        verify(cache).put(key, value);
    }

    @Test
    void testEvict_Success() {
        // Given
        String cacheName = "testCache";
        String key = "testKey";
        
        when(cacheManager.getCache(cacheName)).thenReturn(cache);

        // When
        cacheService.evict(cacheName, key);

        // Then
        verify(cache).evict(key);
    }

    @Test
    void testClear_Success() {
        // Given
        String cacheName = "testCache";
        
        when(cacheManager.getCache(cacheName)).thenReturn(cache);

        // When
        cacheService.clear(cacheName);

        // Then
        verify(cache).clear();
    }

    @Test
    void testExists_True() {
        // Given
        String cacheName = "testCache";
        String key = "testKey";
        SimpleValueWrapper wrapper = new SimpleValueWrapper("value");
        
        when(cacheManager.getCache(cacheName)).thenReturn(cache);
        when(cache.get(key)).thenReturn(wrapper);

        // When
        boolean result = cacheService.exists(cacheName, key);

        // Then
        assertTrue(result);
    }

    @Test
    void testExists_False() {
        // Given
        String cacheName = "testCache";
        String key = "testKey";
        
        when(cacheManager.getCache(cacheName)).thenReturn(cache);
        when(cache.get(key)).thenReturn(null);

        // When
        boolean result = cacheService.exists(cacheName, key);

        // Then
        assertFalse(result);
    }

    @Test
    void testGetFromL1_Success() {
        // Given
        String cacheName = "testCache";
        String key = "testKey";
        String value = "testValue";
        Cache l1Cache = mock(Cache.class);
        SimpleValueWrapper wrapper = new SimpleValueWrapper(value);
        
        when(cacheManager.getCache(cacheName)).thenReturn(levelCache);
        when(levelCache.getL1Cache()).thenReturn(l1Cache);
        when(l1Cache.get(key)).thenReturn(wrapper);

        // When
        String result = cacheService.getFromL1(cacheName, key);

        // Then
        assertEquals(value, result);
    }

    @Test
    void testGetFromL2_Success() {
        // Given
        String cacheName = "testCache";
        String key = "testKey";
        String value = "testValue";
        Cache l2Cache = mock(Cache.class);
        SimpleValueWrapper wrapper = new SimpleValueWrapper(value);
        
        when(cacheManager.getCache(cacheName)).thenReturn(levelCache);
        when(levelCache.getL2Cache()).thenReturn(l2Cache);
        when(l2Cache.get(key)).thenReturn(wrapper);

        // When
        String result = cacheService.getFromL2(cacheName, key);

        // Then
        assertEquals(value, result);
    }

    @Test
    void testClearAll_Success() {
        // When
        cacheService.clearAll();

        // Then
        verify(cacheManager).clearAll();
    }

    @Test
    void testGetInstanceId_Success() {
        // Given
        String instanceId = "test-instance-123";
        when(cacheManager.getSyncService()).thenReturn(syncService);
        when(syncService.getInstanceId()).thenReturn(instanceId);

        // When
        String result = cacheService.getInstanceId();

        // Then
        assertEquals(instanceId, result);
    }

    @Test
    void testGetInstanceId_NoSyncService() {
        // Given
        when(cacheManager.getSyncService()).thenReturn(null);

        // When
        String result = cacheService.getInstanceId();

        // Then
        assertNull(result);
    }

    @Test
    void testIsSyncEnabled_True() {
        // Given
        String cacheName = "testCache";
        when(cacheManager.getCache(cacheName)).thenReturn(levelCache);
        when(cacheManager.getSyncService()).thenReturn(syncService);

        // When
        boolean result = cacheService.isSyncEnabled(cacheName);

        // Then
        assertTrue(result);
    }

    @Test
    void testIsSyncEnabled_False() {
        // Given
        String cacheName = "testCache";
        when(cacheManager.getCache(cacheName)).thenReturn(cache); // 非LevelCache
        when(cacheManager.getSyncService()).thenReturn(null);

        // When
        boolean result = cacheService.isSyncEnabled(cacheName);

        // Then
        assertFalse(result);
    }
}