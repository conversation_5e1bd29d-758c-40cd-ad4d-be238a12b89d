package cn.com.handthing.starter.connector.web;

import cn.com.handthing.starter.connector.PlatformType;
import cn.com.handthing.starter.connector.auth.AuthService;
import cn.com.handthing.starter.connector.auth.UnifiedAccessToken;
import cn.com.handthing.starter.connector.auth.UnifiedUserInfo;
import cn.com.handthing.starter.connector.config.AppConnectorProperties;
import cn.com.handthing.starter.connector.event.UserAuthorizedEvent;
import cn.com.handthing.starter.connector.exception.AuthException;
import cn.com.handthing.starter.connector.token.TokenManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * 统一回调处理器
 * <p>
 * 提供统一回调端点：/connector/callback/{platform}，
 * 自动处理code和state参数，委托给相应的AuthProvider完成Token交换，
 * 发布UserAuthorizedEvent事件。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@RestController
@RequestMapping("${handthing.connector.callback-path-prefix:/connector/callback}")
@ConditionalOnProperty(prefix = "handthing.connector", name = "enable-unified-callback", havingValue = "true", matchIfMissing = true)
public class CallbackController {

    private final AuthService authService;
    private final TokenManager tokenManager;
    private final AppConnectorProperties properties;
    private final ApplicationEventPublisher eventPublisher;

    /**
     * 构造函数
     */
    public CallbackController(AuthService authService, TokenManager tokenManager,
                             AppConnectorProperties properties, ApplicationEventPublisher eventPublisher) {
        this.authService = authService;
        this.tokenManager = tokenManager;
        this.properties = properties;
        this.eventPublisher = eventPublisher;
    }

    /**
     * 处理OAuth回调
     *
     * @param platform 平台代码
     * @param code     授权码
     * @param state    状态参数
     * @param error    错误信息（如果有）
     * @param request  HTTP请求
     * @param response HTTP响应
     * @return 处理结果
     */
    @GetMapping("/{platform}")
    public ResponseEntity<Map<String, Object>> handleCallback(
            @PathVariable String platform,
            @RequestParam(required = false) String code,
            @RequestParam(required = false) String state,
            @RequestParam(required = false) String error,
            HttpServletRequest request,
            HttpServletResponse response) {

        log.info("Received callback for platform: {}, code: {}, state: {}, error: {}", 
                platform, code != null ? "***" : null, state, error);

        Map<String, Object> result = new HashMap<>();
        result.put("platform", platform);
        result.put("state", state);

        try {
            // 检查是否有错误
            if (error != null) {
                log.warn("OAuth error for platform: {}, error: {}", platform, error);
                result.put("success", false);
                result.put("error", error);
                result.put("message", "User denied authorization or other OAuth error occurred");
                return ResponseEntity.badRequest().body(result);
            }

            // 检查授权码
            if (code == null || code.trim().isEmpty()) {
                log.warn("Missing authorization code for platform: {}", platform);
                result.put("success", false);
                result.put("error", "missing_code");
                result.put("message", "Authorization code is required");
                return ResponseEntity.badRequest().body(result);
            }

            // 解析平台类型
            PlatformType platformType = PlatformType.fromCode(platform);
            if (platformType == null) {
                log.warn("Unsupported platform: {}", platform);
                result.put("success", false);
                result.put("error", "unsupported_platform");
                result.put("message", "Platform not supported: " + platform);
                return ResponseEntity.badRequest().body(result);
            }

            // 检查平台是否启用
            if (!authService.isPlatformSupported(platformType)) {
                log.warn("Platform not configured: {}", platform);
                result.put("success", false);
                result.put("error", "platform_not_configured");
                result.put("message", "Platform not configured: " + platform);
                return ResponseEntity.badRequest().body(result);
            }

            // 构建回调URL
            String redirectUri = properties.getCallbackUrl(platform);

            // 交换Token
            UnifiedAccessToken accessToken = authService.exchangeToken(platformType, code, state, redirectUri);
            if (accessToken == null) {
                log.error("Failed to exchange token for platform: {}", platform);
                result.put("success", false);
                result.put("error", "token_exchange_failed");
                result.put("message", "Failed to exchange authorization code for access token");
                return ResponseEntity.internalServerError().body(result);
            }

            // 获取用户信息
            UnifiedUserInfo userInfo = authService.getUserInfo(platformType, accessToken.getAccessToken());
            if (userInfo == null) {
                log.error("Failed to get user info for platform: {}", platform);
                result.put("success", false);
                result.put("error", "user_info_failed");
                result.put("message", "Failed to retrieve user information");
                return ResponseEntity.internalServerError().body(result);
            }

            // 存储Token
            String userId = userInfo.getUniqueId();
            tokenManager.storeToken(userId, platformType, accessToken);

            // 发布事件
            if (properties.isEnableEventPublishing()) {
                UserAuthorizedEvent event = new UserAuthorizedEvent(
                        this, platformType, userInfo, accessToken, state, code);
                eventPublisher.publishEvent(event);
                log.debug("Published UserAuthorizedEvent for platform: {}, user: {}", 
                        platform, userInfo.getDisplayName());
            }

            // 构建成功响应
            result.put("success", true);
            result.put("message", "Authorization successful");
            result.put("user", Map.of(
                    "id", userInfo.getUserId(),
                    "uniqueId", userInfo.getUniqueId(),
                    "displayName", userInfo.getDisplayName(),
                    "avatar", userInfo.getAvatar()
            ));
            result.put("token", Map.of(
                    "expiresIn", accessToken.getExpiresIn(),
                    "hasRefreshToken", accessToken.hasRefreshToken(),
                    "scope", accessToken.getScope()
            ));

            log.info("Successfully processed callback for platform: {}, user: {}", 
                    platform, userInfo.getDisplayName());

            return ResponseEntity.ok(result);

        } catch (AuthException e) {
            log.error("Auth error processing callback for platform: {}", platform, e);
            result.put("success", false);
            result.put("error", e.getErrorCode());
            result.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(result);

        } catch (Exception e) {
            log.error("Unexpected error processing callback for platform: {}", platform, e);
            result.put("success", false);
            result.put("error", "internal_error");
            result.put("message", "An unexpected error occurred");
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 获取支持的平台列表
     *
     * @return 支持的平台列表
     */
    @GetMapping("/platforms")
    public ResponseEntity<Map<String, Object>> getSupportedPlatforms() {
        Map<String, Object> result = new HashMap<>();
        result.put("platforms", authService.getSupportedPlatforms().stream()
                .map(platform -> Map.of(
                        "code", platform.getCode(),
                        "name", platform.getDisplayName(),
                        "englishName", platform.getEnglishName(),
                        "callbackUrl", properties.getCallbackUrl(platform.getCode())
                ))
                .toList());
        return ResponseEntity.ok(result);
    }

    /**
     * 健康检查端点
     *
     * @return 健康状态
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> result = new HashMap<>();
        result.put("status", "UP");
        result.put("supportedPlatforms", authService.getSupportedPlatforms().size());
        result.put("callbackPathPrefix", properties.getCallbackPathPrefix());
        result.put("redirectUriPrefix", properties.getRedirectUriPrefix());
        return ResponseEntity.ok(result);
    }
}
