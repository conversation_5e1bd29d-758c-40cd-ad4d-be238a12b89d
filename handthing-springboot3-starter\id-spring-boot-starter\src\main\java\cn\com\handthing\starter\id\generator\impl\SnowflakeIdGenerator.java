package cn.com.handthing.starter.id.generator.impl;

import cn.com.handthing.starter.id.exception.IdGenerationException;
import cn.com.handthing.starter.id.generator.IdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Snowflake算法ID生成器
 * <p>
 * 基于Twitter的Snowflake算法实现，生成64位长整型ID。
 * 作为Long类型字段的默认ID生成器。
 * </p>
 * 
 * <h3>ID结构（64位）：</h3>
 * <pre>
 * +----------+----------+----------+----------+
 * | 1位符号位 | 41位时间戳 | 5位数据中心 | 5位机器ID | 12位序列号 |
 * +----------+----------+----------+----------+
 * </pre>
 * 
 * <h3>特性：</h3>
 * <ul>
 *   <li>趋势递增</li>
 *   <li>不依赖数据库</li>
 *   <li>高性能</li>
 *   <li>分布式唯一</li>
 * </ul>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Component("snowflakeIdGenerator")
@ConditionalOnProperty(prefix = "handthing.id.snowflake", name = "enabled", havingValue = "true", matchIfMissing = true)
public class SnowflakeIdGenerator implements IdGenerator<Long> {
    
    /**
     * 生成器名称
     */
    private static final String GENERATOR_NAME = "SnowflakeIdGenerator";
    
    /**
     * 开始时间戳 (2024-01-01 00:00:00)
     */
    private static final long START_TIMESTAMP = 1704067200000L;
    
    /**
     * 机器ID位数
     */
    private static final long WORKER_ID_BITS = 5L;
    
    /**
     * 数据中心ID位数
     */
    private static final long DATACENTER_ID_BITS = 5L;
    
    /**
     * 序列号位数
     */
    private static final long SEQUENCE_BITS = 12L;
    
    /**
     * 机器ID最大值
     */
    private static final long MAX_WORKER_ID = ~(-1L << WORKER_ID_BITS);
    
    /**
     * 数据中心ID最大值
     */
    private static final long MAX_DATACENTER_ID = ~(-1L << DATACENTER_ID_BITS);
    
    /**
     * 序列号最大值
     */
    private static final long MAX_SEQUENCE = ~(-1L << SEQUENCE_BITS);
    
    /**
     * 机器ID左移位数
     */
    private static final long WORKER_ID_SHIFT = SEQUENCE_BITS;
    
    /**
     * 数据中心ID左移位数
     */
    private static final long DATACENTER_ID_SHIFT = SEQUENCE_BITS + WORKER_ID_BITS;
    
    /**
     * 时间戳左移位数
     */
    private static final long TIMESTAMP_SHIFT = SEQUENCE_BITS + WORKER_ID_BITS + DATACENTER_ID_BITS;
    
    /**
     * 机器ID
     */
    private final long workerId;
    
    /**
     * 数据中心ID
     */
    private final long datacenterId;
    
    /**
     * 序列号
     */
    private long sequence = 0L;
    
    /**
     * 上次生成ID的时间戳
     */
    private long lastTimestamp = -1L;
    
    /**
     * 同步锁
     */
    private final Object lock = new Object();
    
    /**
     * 构造函数
     *
     * @param workerId     机器ID (0-31)
     * @param datacenterId 数据中心ID (0-31)
     */
    public SnowflakeIdGenerator(long workerId, long datacenterId) {
        if (workerId > MAX_WORKER_ID || workerId < 0) {
            throw new IllegalArgumentException(
                    String.format("Worker ID must be between 0 and %d", MAX_WORKER_ID));
        }
        if (datacenterId > MAX_DATACENTER_ID || datacenterId < 0) {
            throw new IllegalArgumentException(
                    String.format("Datacenter ID must be between 0 and %d", MAX_DATACENTER_ID));
        }
        
        this.workerId = workerId;
        this.datacenterId = datacenterId;
        
        log.info("SnowflakeIdGenerator initialized: workerId={}, datacenterId={}", 
                workerId, datacenterId);
    }
    
    /**
     * 默认构造函数
     * <p>
     * 使用默认的workerId=1, datacenterId=1
     * </p>
     */
    public SnowflakeIdGenerator() {
        this(1L, 1L);
    }
    
    @Override
    public Long generate() {
        synchronized (lock) {
            long timestamp = getCurrentTimestamp();
            
            // 检查时钟回拨
            if (timestamp < lastTimestamp) {
                long offset = lastTimestamp - timestamp;
                if (offset <= 5) {
                    // 小幅回拨，等待追上
                    try {
                        Thread.sleep(offset << 1);
                        timestamp = getCurrentTimestamp();
                        if (timestamp < lastTimestamp) {
                            throw new IdGenerationException(GENERATOR_NAME, "CLOCK_BACKWARDS", 
                                    String.format("Clock moved backwards. Refusing to generate id for %d milliseconds", 
                                            lastTimestamp - timestamp));
                        }
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        throw new IdGenerationException(GENERATOR_NAME, "INTERRUPTED", 
                                "Thread interrupted while waiting for clock", e);
                    }
                } else {
                    throw new IdGenerationException(GENERATOR_NAME, "CLOCK_BACKWARDS", 
                            String.format("Clock moved backwards. Refusing to generate id for %d milliseconds", 
                                    lastTimestamp - timestamp));
                }
            }
            
            // 同一毫秒内，序列号递增
            if (lastTimestamp == timestamp) {
                sequence = (sequence + 1) & MAX_SEQUENCE;
                // 序列号溢出，等待下一毫秒
                if (sequence == 0) {
                    timestamp = waitNextMillis(lastTimestamp);
                }
            } else {
                // 不同毫秒，序列号重置
                sequence = 0L;
            }
            
            lastTimestamp = timestamp;
            
            // 组装ID
            return ((timestamp - START_TIMESTAMP) << TIMESTAMP_SHIFT) |
                    (datacenterId << DATACENTER_ID_SHIFT) |
                    (workerId << WORKER_ID_SHIFT) |
                    sequence;
        }
    }
    
    /**
     * 等待下一毫秒
     *
     * @param lastTimestamp 上次时间戳
     * @return 下一毫秒时间戳
     */
    private long waitNextMillis(long lastTimestamp) {
        long timestamp = getCurrentTimestamp();
        while (timestamp <= lastTimestamp) {
            timestamp = getCurrentTimestamp();
        }
        return timestamp;
    }
    
    /**
     * 获取当前时间戳
     *
     * @return 当前时间戳
     */
    private long getCurrentTimestamp() {
        return System.currentTimeMillis();
    }
    
    @Override
    public Class<Long> getIdType() {
        return Long.class;
    }
    
    @Override
    public String getName() {
        return GENERATOR_NAME;
    }
    
    @Override
    public boolean isAvailable() {
        // 检查时钟是否正常
        long currentTime = getCurrentTimestamp();
        return currentTime > START_TIMESTAMP && currentTime >= lastTimestamp;
    }
    
    @Override
    public void warmUp() {
        log.debug("Warming up Snowflake generator...");
        // 预热：生成几个ID
        for (int i = 0; i < 10; i++) {
            generate();
        }
        log.debug("Snowflake generator warmed up successfully");
    }
    
    /**
     * 解析Snowflake ID
     *
     * @param id Snowflake ID
     * @return ID信息
     */
    public SnowflakeIdInfo parseId(long id) {
        long timestamp = (id >> TIMESTAMP_SHIFT) + START_TIMESTAMP;
        long datacenterId = (id >> DATACENTER_ID_SHIFT) & MAX_DATACENTER_ID;
        long workerId = (id >> WORKER_ID_SHIFT) & MAX_WORKER_ID;
        long sequence = id & MAX_SEQUENCE;
        
        return new SnowflakeIdInfo(timestamp, datacenterId, workerId, sequence);
    }
    
    /**
     * 获取生成器配置信息
     *
     * @return 配置信息
     */
    public String getConfigInfo() {
        return String.format("SnowflakeIdGenerator{workerId=%d, datacenterId=%d, sequence=%d, lastTimestamp=%d}", 
                workerId, datacenterId, sequence, lastTimestamp);
    }
    
    /**
     * Snowflake ID信息
     */
    public static class SnowflakeIdInfo {
        private final long timestamp;
        private final long datacenterId;
        private final long workerId;
        private final long sequence;
        
        public SnowflakeIdInfo(long timestamp, long datacenterId, long workerId, long sequence) {
            this.timestamp = timestamp;
            this.datacenterId = datacenterId;
            this.workerId = workerId;
            this.sequence = sequence;
        }
        
        public long getTimestamp() { return timestamp; }
        public long getDatacenterId() { return datacenterId; }
        public long getWorkerId() { return workerId; }
        public long getSequence() { return sequence; }
        
        @Override
        public String toString() {
            return String.format("SnowflakeIdInfo{timestamp=%d, datacenterId=%d, workerId=%d, sequence=%d}", 
                    timestamp, datacenterId, workerId, sequence);
        }
    }
    
    @Override
    public String toString() {
        return getConfigInfo();
    }
}
