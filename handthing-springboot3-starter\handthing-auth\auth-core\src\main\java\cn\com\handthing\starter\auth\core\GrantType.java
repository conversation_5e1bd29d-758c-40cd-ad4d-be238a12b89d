package cn.com.handthing.starter.auth.core;

/**
 * 授权类型枚举
 * <p>
 * 定义系统支持的各种认证授权类型，基于OAuth2.0规范扩展。
 * 支持传统的密码认证、短信验证码认证以及第三方平台认证。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
public enum GrantType {

    /**
     * 密码认证
     * 使用用户名和密码进行认证
     */
    PASSWORD("password", "密码认证"),

    /**
     * 短信验证码认证
     * 使用手机号和短信验证码进行认证
     */
    SMS_CODE("sms_code", "短信验证码认证"),

    /**
     * 企业微信认证
     * 使用企业微信授权码进行认证
     */
    WECOM("wecom", "企业微信认证"),

    /**
     * 钉钉认证
     * 使用钉钉授权码进行认证
     */
    DINGTALK("dingtalk", "钉钉认证"),

    /**
     * 微信认证
     * 使用微信授权码进行认证
     */
    WECHAT("wechat", "微信认证"),

    /**
     * 飞书认证
     * 使用飞书授权码进行认证
     */
    FEISHU("feishu", "飞书认证"),

    /**
     * 抖音认证
     * 使用抖音授权码进行认证
     */
    DOUYIN("douyin", "抖音认证"),

    /**
     * 支付宝认证
     * 使用支付宝授权码进行认证
     */
    ALIPAY("alipay", "支付宝认证"),

    /**
     * 今日头条认证
     * 使用今日头条授权码进行认证
     */
    TOUTIAO("toutiao", "今日头条认证"),

    /**
     * 小红书认证
     * 使用小红书授权码进行认证
     */
    XIAOHONGSHU("xiaohongshu", "小红书认证"),

    /**
     * Bilibili认证
     * 使用Bilibili授权码进行认证
     */
    BILIBILI("bilibili", "Bilibili认证"),

    /**
     * 刷新令牌
     * 使用刷新令牌获取新的访问令牌
     */
    REFRESH_TOKEN("refresh_token", "刷新令牌");

    /**
     * 授权类型代码
     */
    private final String code;

    /**
     * 授权类型描述
     */
    private final String description;

    /**
     * 构造函数
     *
     * @param code        授权类型代码
     * @param description 授权类型描述
     */
    GrantType(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 获取授权类型代码
     *
     * @return 授权类型代码
     */
    public String getCode() {
        return code;
    }

    /**
     * 获取授权类型描述
     *
     * @return 授权类型描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取授权类型
     *
     * @param code 授权类型代码
     * @return 授权类型枚举，如果未找到返回null
     */
    public static GrantType fromCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return null;
        }

        for (GrantType grantType : values()) {
            if (grantType.code.equals(code)) {
                return grantType;
            }
        }

        return null;
    }

    /**
     * 判断是否为第三方认证类型
     *
     * @return 如果是第三方认证类型返回true，否则返回false
     */
    public boolean isThirdParty() {
        return this == WECOM || this == DINGTALK || this == WECHAT || this == FEISHU ||
               this == DOUYIN || this == ALIPAY || this == TOUTIAO || this == XIAOHONGSHU ||
               this == BILIBILI;
    }

    /**
     * 判断是否为内部认证类型
     *
     * @return 如果是内部认证类型返回true，否则返回false
     */
    public boolean isInternal() {
        return this == PASSWORD || this == SMS_CODE;
    }

    @Override
    public String toString() {
        return code;
    }
}
