package cn.com.handthing.starter.crypto.service;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Sha256Digester 的单元测试。
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@DisplayName("SHA-256 Digester Service Tests")
class Sha256DigesterTest {
    
    private static final String BEAN_NAME = "testSha256Digester";
    private static final String SALT = "my-secret-salt-for-sha256";
    private static final String DATA = "password123";

    private MeterRegistry meterRegistry;
    private Sha256Digester digesterWithSalt;
    private Sha256Digester digesterWithoutSalt;

    @BeforeEach
    void setUp() {
        meterRegistry = new SimpleMeterRegistry();
        digesterWithSalt = new Sha256Digester(BEAN_NAME, SALT, meterRegistry);
        digesterWithoutSalt = new Sha256Digester(BEAN_NAME + "NoSalt", null, meterRegistry);
    }

    @Test
    @DisplayName("摘要结果应为64个字符的十六进制字符串")
    void digestShouldReturn64CharHexString() {
        String digested = digesterWithSalt.digest(DATA);
        assertThat(digested).isNotNull().hasSize(64).matches("^[a-f0-9]{64}$");
    }

    @Test
    @DisplayName("matches 方法应能正确验证匹配和不匹配的数据")
    void matchesShouldWorkCorrectly() {
        String digested = digesterWithSalt.digest(DATA);
        
        assertThat(digesterWithSalt.matches(DATA, digested)).isTrue();
        assertThat(digesterWithSalt.matches("wrong-password", digested)).isFalse();
    }

    @Test
    @DisplayName("有盐和无盐的摘要结果应不相同")
    void digestWithAndWithoutSaltShouldBeDifferent() {
        String digestedWithSalt = digesterWithSalt.digest(DATA);
        String digestedWithoutSalt = digesterWithoutSalt.digest(DATA);
        
        assertThat(digestedWithSalt).isNotEqualTo(digestedWithoutSalt);
    }
    
    @Test
    @DisplayName("不同的盐应产生不同的摘要结果")
    void differentSaltsShouldProduceDifferentDigests() {
        Sha256Digester anotherDigester = new Sha256Digester(BEAN_NAME, "another-salt", meterRegistry);
        
        String digest1 = digesterWithSalt.digest(DATA);
        String digest2 = anotherDigester.digest(DATA);
        
        assertThat(digest1).isNotEqualTo(digest2);
    }

    @Test
    @DisplayName("健康自检应能成功通过")
    void selfCheckShouldPass() {
        digesterWithSalt.selfCheck();
        digesterWithoutSalt.selfCheck();
    }
}