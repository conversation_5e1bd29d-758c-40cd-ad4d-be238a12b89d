package cn.com.handthing.starter.connector.auth;

import cn.com.handthing.starter.connector.PlatformType;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 统一用户信息模型
 * <p>
 * 统一不同平台的用户信息格式，包含用户ID、昵称、头像等通用字段，
 * 同时支持平台特定字段的扩展
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UnifiedUserInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 平台类型
     */
    private PlatformType platform;

    /**
     * 平台用户唯一标识
     */
    private String userId;

    /**
     * 开放平台用户标识（OpenID）
     */
    private String openId;

    /**
     * 联合用户标识（UnionID），用于跨应用识别同一用户
     */
    private String unionId;

    /**
     * 用户昵称
     */
    private String nickname;

    /**
     * 用户真实姓名
     */
    private String realName;

    /**
     * 用户头像URL
     */
    private String avatar;

    /**
     * 用户邮箱
     */
    private String email;

    /**
     * 用户手机号
     */
    private String mobile;

    /**
     * 用户性别（1-男性，2-女性，0-未知）
     */
    private Integer gender;

    /**
     * 用户语言
     */
    private String language;

    /**
     * 用户所在国家
     */
    private String country;

    /**
     * 用户所在省份
     */
    private String province;

    /**
     * 用户所在城市
     */
    private String city;

    /**
     * 用户是否已认证
     */
    private Boolean verified;

    /**
     * 用户状态（激活、禁用等）
     */
    private String status;

    /**
     * 用户信息获取时间
     */
    private LocalDateTime fetchedAt;

    /**
     * 平台特定的额外信息
     */
    private Map<String, Object> extraInfo;

    /**
     * 获取显示名称（优先使用真实姓名，其次昵称）
     *
     * @return 显示名称
     */
    public String getDisplayName() {
        if (realName != null && !realName.trim().isEmpty()) {
            return realName;
        }
        if (nickname != null && !nickname.trim().isEmpty()) {
            return nickname;
        }
        return userId;
    }

    /**
     * 获取性别描述
     *
     * @return 性别描述
     */
    public String getGenderDescription() {
        if (gender == null) {
            return "未知";
        }
        switch (gender) {
            case 1:
                return "男";
            case 2:
                return "女";
            default:
                return "未知";
        }
    }

    /**
     * 检查用户信息是否完整（包含基本必要信息）
     *
     * @return 如果信息完整返回true，否则返回false
     */
    public boolean isComplete() {
        return platform != null 
                && userId != null && !userId.trim().isEmpty()
                && (nickname != null && !nickname.trim().isEmpty() 
                    || realName != null && !realName.trim().isEmpty());
    }

    /**
     * 检查是否有联系方式
     *
     * @return 如果有邮箱或手机号返回true，否则返回false
     */
    public boolean hasContactInfo() {
        return (email != null && !email.trim().isEmpty()) 
                || (mobile != null && !mobile.trim().isEmpty());
    }

    /**
     * 获取用户的唯一标识（优先使用unionId，其次openId，最后userId）
     *
     * @return 用户唯一标识
     */
    public String getUniqueId() {
        if (unionId != null && !unionId.trim().isEmpty()) {
            return unionId;
        }
        if (openId != null && !openId.trim().isEmpty()) {
            return openId;
        }
        return userId;
    }

    /**
     * 创建一个基本的用户信息实例
     *
     * @param platform 平台类型
     * @param userId   用户ID
     * @param nickname 用户昵称
     * @return 用户信息实例
     */
    public static UnifiedUserInfo create(PlatformType platform, String userId, String nickname) {
        return UnifiedUserInfo.builder()
                .platform(platform)
                .userId(userId)
                .nickname(nickname)
                .fetchedAt(LocalDateTime.now())
                .build();
    }
}
