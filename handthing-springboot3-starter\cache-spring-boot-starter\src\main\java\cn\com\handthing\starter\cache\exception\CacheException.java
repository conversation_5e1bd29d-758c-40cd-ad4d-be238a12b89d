package cn.com.handthing.starter.cache.exception;

/**
 * 缓存操作异常基类
 * <p>
 * 所有缓存相关的异常都应该继承此类
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
public class CacheException extends RuntimeException {

    /**
     * 构造函数
     *
     * @param message 异常消息
     */
    public CacheException(String message) {
        super(message);
    }

    /**
     * 构造函数
     *
     * @param message 异常消息
     * @param cause   原因异常
     */
    public CacheException(String message, Throwable cause) {
        super(message, cause);
    }

    /**
     * 构造函数
     *
     * @param cause 原因异常
     */
    public CacheException(Throwable cause) {
        super(cause);
    }
}