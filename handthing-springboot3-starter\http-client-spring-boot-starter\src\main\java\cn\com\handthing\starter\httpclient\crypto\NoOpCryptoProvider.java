package cn.com.handthing.starter.httpclient.crypto;

import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.lang.NonNull;

import java.io.IOException;
import java.io.InputStream;

/**
 * 一个“无操作”的加解密实现。
 * <p>
 * 当Spring上下文中没有找到自定义的 RequestEncryptor 或 ResponseDecryptor Bean 时，
 * 此类将作为默认实现注入，以确保加解密流程的完整性，但实际上不执行任何加解密操作。
 */
public class NoOpCryptoProvider implements RequestEncryptor<Object>, ResponseDecryptor {

    /**
     * 不执行任何加密操作，直接返回原始请求体。
     *
     * @param request 原始的HTTP请求对象。
     * @param body    原始的请求体。
     * @return 原始的、未被修改的请求体。
     */
    @Override
    @NonNull
    public Object encrypt(@NonNull HttpRequest request, @NonNull Object body) {
        // Do nothing, just return the original body
        return body;
    }

    /**
     * 不执行任何解密操作，直接返回原始响应的输入流。
     *
     * @param response 原始的、可能被加密的HTTP响应。
     * @return 原始的、未被修改的响应输入流。
     * @throws IOException 如果在获取输入流时发生错误。
     */
    @Override
    @NonNull
    public InputStream decrypt(@NonNull ClientHttpResponse response) throws IOException {
        // Do nothing, just return the original body stream
        return response.getBody();
    }
}