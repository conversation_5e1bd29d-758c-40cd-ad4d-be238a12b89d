D:\code\ai-project\handthing-springboot3-starter\id-spring-boot-starter\src\main\java\cn\com\handthing\starter\id\config\IdAutoConfiguration.java
D:\code\ai-project\handthing-springboot3-starter\id-spring-boot-starter\src\main\java\cn\com\handthing\starter\id\annotation\IdSetter.java
D:\code\ai-project\handthing-springboot3-starter\id-spring-boot-starter\src\main\java\cn\com\handthing\starter\id\registry\IdGeneratorRegistry.java
D:\code\ai-project\handthing-springboot3-starter\id-spring-boot-starter\src\main\java\cn\com\handthing\starter\id\generator\impl\PooledIdGenerator.java
D:\code\ai-project\handthing-springboot3-starter\id-spring-boot-starter\src\main\java\cn\com\handthing\starter\id\exception\IdGenerationException.java
D:\code\ai-project\handthing-springboot3-starter\id-spring-boot-starter\src\main\java\cn\com\handthing\starter\id\config\IdProperties.java
D:\code\ai-project\handthing-springboot3-starter\id-spring-boot-starter\src\main\java\cn\com\handthing\starter\id\generator\IdGenerator.java
D:\code\ai-project\handthing-springboot3-starter\id-spring-boot-starter\src\main\java\cn\com\handthing\starter\id\generator\impl\UuidGenerator.java
D:\code\ai-project\handthing-springboot3-starter\id-spring-boot-starter\src\main\java\cn\com\handthing\starter\id\generator\impl\SnowflakeIdGenerator.java
D:\code\ai-project\handthing-springboot3-starter\id-spring-boot-starter\src\main\java\cn\com\handthing\starter\id\util\IdUtils.java
