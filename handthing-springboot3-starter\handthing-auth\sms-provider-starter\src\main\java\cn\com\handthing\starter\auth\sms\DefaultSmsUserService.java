package cn.com.handthing.starter.auth.sms;

import cn.com.handthing.starter.auth.core.UserInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 默认短信用户服务实现
 * <p>
 * 提供基于内存的短信用户服务实现，主要用于测试和演示。
 * 生产环境建议实现自己的SmsUserService来对接实际的用户数据源。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Service
@ConditionalOnMissingBean(SmsUserService.class)
public class DefaultSmsUserService implements SmsUserService {

    /**
     * 用户存储（按用户ID索引）
     */
    private final Map<String, UserInfo> usersById = new ConcurrentHashMap<>();

    /**
     * 用户存储（按手机号索引）
     */
    private final Map<String, UserInfo> usersByPhone = new ConcurrentHashMap<>();

    /**
     * 登录失败记录
     */
    private final Map<String, LoginFailureRecord> loginFailures = new ConcurrentHashMap<>();

    /**
     * 用户ID生成器
     */
    private final AtomicLong userIdGenerator = new AtomicLong(1000);

    @Override
    public Optional<UserInfo> findByPhone(String phone) {
        return Optional.ofNullable(usersByPhone.get(phone));
    }

    @Override
    public Optional<UserInfo> findById(String userId) {
        return Optional.ofNullable(usersById.get(userId));
    }

    @Override
    public UserInfo createUser(String phone, String nickname) {
        // 检查手机号是否已注册
        if (isPhoneRegistered(phone)) {
            throw new RuntimeException("Phone number already registered: " + phone);
        }

        // 验证参数
        if (!isValidPhoneFormat(phone)) {
            throw new RuntimeException("Invalid phone format: " + phone);
        }

        if (nickname == null || nickname.trim().isEmpty()) {
            nickname = generateDefaultNickname(phone);
        }

        if (!isValidNickname(nickname)) {
            throw new RuntimeException("Invalid nickname: " + nickname);
        }

        // 创建用户
        String userId = String.valueOf(userIdGenerator.incrementAndGet());
        String username = generateDefaultUsername(phone);

        UserInfo userInfo = UserInfo.builder()
                .userId(userId)
                .username(username)
                .nickname(nickname.trim())
                .phone(phone)
                .email(null) // 短信注册用户暂无邮箱
                .roles(getDefaultRoles())
                .permissions(getDefaultPermissions())
                .status(1)
                .enabled(true)
                .accountNonExpired(true)
                .accountNonLocked(true)
                .credentialsNonExpired(true)
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .build();

        // 存储用户
        usersById.put(userId, userInfo);
        usersByPhone.put(phone, userInfo);

        log.info("Created new user: userId={}, phone={}, nickname={}", userId, phone, nickname);
        return userInfo;
    }

    @Override
    public boolean isUserValid(UserInfo userInfo) {
        return userInfo != null && Boolean.TRUE.equals(userInfo.getEnabled());
    }

    @Override
    public boolean isUserLocked(UserInfo userInfo) {
        return userInfo != null && Boolean.FALSE.equals(userInfo.getAccountNonLocked());
    }

    @Override
    public boolean isUserDisabled(UserInfo userInfo) {
        return userInfo != null && Boolean.FALSE.equals(userInfo.getEnabled());
    }

    @Override
    public boolean isAccountExpired(UserInfo userInfo) {
        return userInfo != null && Boolean.FALSE.equals(userInfo.getAccountNonExpired());
    }

    @Override
    public void updateLastLoginInfo(String userId, LocalDateTime loginTime, String ipAddress, String userAgent) {
        UserInfo userInfo = usersById.get(userId);
        if (userInfo != null) {
            userInfo.setLastLoginTime(loginTime);
            userInfo.setLastLoginIp(ipAddress);
            userInfo.setUpdateTime(LocalDateTime.now());
            userInfo.addAttribute("lastUserAgent", userAgent);
            log.debug("Updated last login info for user: {}", userId);
        }
    }

    @Override
    public void recordLoginFailure(String phone, String ipAddress, String reason) {
        LoginFailureRecord record = loginFailures.computeIfAbsent(phone, k -> new LoginFailureRecord());
        record.incrementFailureCount();
        record.setLastFailureTime(LocalDateTime.now());
        record.setLastFailureIp(ipAddress);
        record.setLastFailureReason(reason);

        log.debug("Recorded login failure for phone: {}, count: {}", phone, record.getFailureCount());
    }

    @Override
    public void clearLoginFailures(String phone) {
        loginFailures.remove(phone);
        log.debug("Cleared login failures for phone: {}", phone);
    }

    @Override
    public int getLoginFailureCount(String phone) {
        LoginFailureRecord record = loginFailures.get(phone);
        return record != null ? record.getFailureCount() : 0;
    }

    @Override
    public void lockUser(String userId, String reason, LocalDateTime lockUntil) {
        UserInfo userInfo = usersById.get(userId);
        if (userInfo != null) {
            userInfo.setAccountNonLocked(false);
            userInfo.setUpdateTime(LocalDateTime.now());
            userInfo.addAttribute("lockReason", reason);
            userInfo.addAttribute("lockUntil", lockUntil);
            log.info("Locked user: {}, reason: {}, until: {}", userId, reason, lockUntil);
        }
    }

    @Override
    public void unlockUser(String userId) {
        UserInfo userInfo = usersById.get(userId);
        if (userInfo != null) {
            userInfo.setAccountNonLocked(true);
            userInfo.setUpdateTime(LocalDateTime.now());
            userInfo.getAttributes().remove("lockReason");
            userInfo.getAttributes().remove("lockUntil");
            log.info("Unlocked user: {}", userId);
        }
    }

    /**
     * 登录失败记录
     */
    private static class LoginFailureRecord {
        private int failureCount = 0;
        private LocalDateTime lastFailureTime;
        private String lastFailureIp;
        private String lastFailureReason;

        public void incrementFailureCount() {
            this.failureCount++;
        }

        public int getFailureCount() {
            return failureCount;
        }

        public LocalDateTime getLastFailureTime() {
            return lastFailureTime;
        }

        public void setLastFailureTime(LocalDateTime lastFailureTime) {
            this.lastFailureTime = lastFailureTime;
        }

        public String getLastFailureIp() {
            return lastFailureIp;
        }

        public void setLastFailureIp(String lastFailureIp) {
            this.lastFailureIp = lastFailureIp;
        }

        public String getLastFailureReason() {
            return lastFailureReason;
        }

        public void setLastFailureReason(String lastFailureReason) {
            this.lastFailureReason = lastFailureReason;
        }
    }

    /**
     * 获取统计信息
     *
     * @return 统计信息
     */
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new java.util.HashMap<>();
        stats.put("totalUsers", usersById.size());
        stats.put("totalPhones", usersByPhone.size());
        stats.put("loginFailures", loginFailures.size());
        
        long enabledUsers = usersById.values().stream()
                .filter(user -> Boolean.TRUE.equals(user.getEnabled()))
                .count();
        stats.put("enabledUsers", enabledUsers);
        
        long lockedUsers = usersById.values().stream()
                .filter(user -> Boolean.FALSE.equals(user.getAccountNonLocked()))
                .count();
        stats.put("lockedUsers", lockedUsers);
        
        return stats;
    }

    /**
     * 获取所有用户（用于管理）
     *
     * @return 用户列表
     */
    public java.util.List<UserInfo> getAllUsers() {
        return new java.util.ArrayList<>(usersById.values());
    }

    /**
     * 删除用户（用于测试）
     *
     * @param userId 用户ID
     * @return 如果删除成功返回true，否则返回false
     */
    public boolean deleteUser(String userId) {
        UserInfo userInfo = usersById.remove(userId);
        if (userInfo != null) {
            usersByPhone.remove(userInfo.getPhone());
            loginFailures.remove(userInfo.getPhone());
            log.info("Deleted user: {}", userId);
            return true;
        }
        return false;
    }
}
