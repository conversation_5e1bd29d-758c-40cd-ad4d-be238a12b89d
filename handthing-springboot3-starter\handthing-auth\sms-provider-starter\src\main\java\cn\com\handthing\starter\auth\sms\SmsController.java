package cn.com.handthing.starter.auth.sms;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 短信控制器
 * <p>
 * 提供短信验证码相关的REST API端点，包括发送验证码、验证码校验等功能。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@RestController
@RequestMapping("${handthing.auth.web.auth-path:/auth}/sms")
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "handthing.auth.sms", name = "endpoints-enabled", havingValue = "true", matchIfMissing = true)
public class SmsController {

    private final SmsService smsService;
    private final SmsUserService smsUserService;

    /**
     * 发送短信验证码
     *
     * @param request 发送请求
     * @return 发送结果
     */
    @PostMapping("/send")
    public ResponseEntity<Map<String, Object>> sendSmsCode(@RequestBody Map<String, Object> request) {
        try {
            String phone = (String) request.get("phone");
            String codeType = (String) request.getOrDefault("code_type", "login");

            // 验证参数
            if (phone == null || phone.trim().isEmpty()) {
                Map<String, Object> response = buildErrorResponse("INVALID_PHONE", "手机号不能为空");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
            }

            if (!smsUserService.isValidPhoneFormat(phone)) {
                Map<String, Object> response = buildErrorResponse("INVALID_PHONE_FORMAT", "手机号格式错误");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
            }

            // 检查发送频率限制
            if (!smsService.checkSendLimit(phone)) {
                Map<String, Object> response = buildErrorResponse("SEND_LIMIT_EXCEEDED", "发送过于频繁，请稍后再试");
                return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS).body(response);
            }

            // 检查用户是否存在（针对登录类型）
            if ("login".equals(codeType) && !smsUserService.isPhoneRegistered(phone)) {
                Map<String, Object> response = buildErrorResponse("PHONE_NOT_REGISTERED", "手机号未注册");
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }

            // 生成验证码
            String code = smsService.generateSmsCode(6);

            // 获取短信模板
            String template = smsService.getSmsTemplate(codeType);

            // 发送短信
            SmsService.SmsResult smsResult = smsService.sendSmsCode(phone, code, codeType, template);

            if (smsResult.isSuccess()) {
                Map<String, Object> response = buildSuccessResponse("短信发送成功");
                response.put("request_id", smsResult.getRequestId());
                response.put("expires_in", 300); // 5分钟过期
                
                log.info("SMS code sent successfully to phone: {}, codeType: {}", phone, codeType);
                return ResponseEntity.ok(response);
            } else {
                Map<String, Object> response = buildErrorResponse("SMS_SEND_FAILED", smsResult.getMessage());
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
            }

        } catch (Exception e) {
            log.error("Send SMS code error", e);
            Map<String, Object> response = buildErrorResponse("SMS_SEND_ERROR", "短信发送失败");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 验证短信验证码
     *
     * @param request 验证请求
     * @return 验证结果
     */
    @PostMapping("/verify")
    public ResponseEntity<Map<String, Object>> verifySmsCode(@RequestBody Map<String, Object> request) {
        try {
            String phone = (String) request.get("phone");
            String code = (String) request.get("code");
            String codeType = (String) request.getOrDefault("code_type", "login");

            // 验证参数
            if (phone == null || phone.trim().isEmpty()) {
                Map<String, Object> response = buildErrorResponse("INVALID_PHONE", "手机号不能为空");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
            }

            if (code == null || code.trim().isEmpty()) {
                Map<String, Object> response = buildErrorResponse("INVALID_CODE", "验证码不能为空");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
            }

            // 验证验证码
            boolean isValid = smsService.verifySmsCode(phone, code, codeType);

            if (isValid) {
                Map<String, Object> response = buildSuccessResponse("验证码验证成功");
                log.info("SMS code verified successfully for phone: {}", phone);
                return ResponseEntity.ok(response);
            } else {
                Map<String, Object> response = buildErrorResponse("INVALID_SMS_CODE", "验证码错误或已过期");
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
            }

        } catch (Exception e) {
            log.error("Verify SMS code error", e);
            Map<String, Object> response = buildErrorResponse("SMS_VERIFY_ERROR", "验证码验证失败");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 获取发送统计信息
     *
     * @param phone 手机号
     * @return 统计信息
     */
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getSmsStats(@RequestParam String phone) {
        try {
            if (!smsUserService.isValidPhoneFormat(phone)) {
                Map<String, Object> response = buildErrorResponse("INVALID_PHONE_FORMAT", "手机号格式错误");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
            }

            int todayCount = smsService.getTodaySendCount(phone);
            boolean canSend = smsService.checkSendLimit(phone);

            Map<String, Object> response = buildSuccessResponse("获取统计信息成功");
            response.put("today_send_count", todayCount);
            response.put("can_send", canSend);
            response.put("daily_limit", 10);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Get SMS stats error", e);
            Map<String, Object> response = buildErrorResponse("SMS_STATS_ERROR", "获取统计信息失败");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 构建成功响应
     *
     * @param message 消息
     * @return 响应Map
     */
    private Map<String, Object> buildSuccessResponse(String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", message);
        response.put("timestamp", getCurrentTimestamp());
        return response;
    }

    /**
     * 构建错误响应
     *
     * @param errorCode        错误代码
     * @param errorDescription 错误描述
     * @return 响应Map
     */
    private Map<String, Object> buildErrorResponse(String errorCode, String errorDescription) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("error", errorCode);
        response.put("error_description", errorDescription);
        response.put("timestamp", getCurrentTimestamp());
        return response;
    }

    /**
     * 获取当前时间戳字符串
     *
     * @return 格式化的时间戳字符串
     */
    private String getCurrentTimestamp() {
        return LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
    }
}
