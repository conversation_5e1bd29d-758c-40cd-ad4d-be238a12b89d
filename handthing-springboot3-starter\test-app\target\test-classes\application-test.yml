spring:
  application:
    name: handthing-test-app

server:
  port: 0  # 使用随机端口进行测试

# 测试环境日志配置
logging:
  level:
    root: INFO
    cn.com.handthing: DEBUG
    org.springframework.security: DEBUG
  custom:
    enable-json-format: false
    max-history: 7

# HandThing 配置
handthing:
  # API 文档配置
  api-doc:
    enabled: true
    title: "HandThing Test App API Documentation"
    description: "HandThing Spring Boot 3 Starters 测试应用的 API 文档"
    version: "1.0.0-SNAPSHOT"
    auth:
      enabled: true
      username: "admin"
      password: "handthing123"

  # 分布式日志配置
  distributed-log:
    enabled: true
    app-name: handthing-test-app
    json-format: false
    max-history: 7
    log-path: ./test-logs

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info
  endpoint:
    health:
      show-details: always