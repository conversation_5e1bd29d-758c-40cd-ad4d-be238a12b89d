package cn.com.handthing.starter.auth.thirdparty;

import cn.com.handthing.starter.auth.core.UserInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 默认企业微信用户服务实现
 * <p>
 * 提供基于内存的企业微信用户服务实现，主要用于测试和演示。
 * 生产环境建议实现自己的WecomUserService来对接实际的用户数据源。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Service
@ConditionalOnMissingBean(WecomUserService.class)
public class DefaultWecomUserService implements WecomUserService {

    /**
     * 用户存储（按用户ID索引）
     */
    private final Map<String, UserInfo> usersById = new ConcurrentHashMap<>();

    /**
     * 企业微信用户映射（企业微信用户ID -> 用户ID）
     */
    private final Map<String, String> wecomUserMapping = new ConcurrentHashMap<>();

    /**
     * 手机号映射（手机号 -> 用户ID）
     */
    private final Map<String, String> mobileMapping = new ConcurrentHashMap<>();

    /**
     * 邮箱映射（邮箱 -> 用户ID）
     */
    private final Map<String, String> emailMapping = new ConcurrentHashMap<>();

    /**
     * 登录失败记录
     */
    private final Map<String, LoginFailureRecord> loginFailures = new ConcurrentHashMap<>();

    /**
     * 用户ID生成器
     */
    private final AtomicLong userIdGenerator = new AtomicLong(2000);

    @Override
    public Optional<UserInfo> findByWecomUserId(String wecomUserId, String corpId) {
        String key = buildWecomUserKey(wecomUserId, corpId);
        String userId = wecomUserMapping.get(key);
        return userId != null ? Optional.ofNullable(usersById.get(userId)) : Optional.empty();
    }

    @Override
    public Optional<UserInfo> findById(String userId) {
        return Optional.ofNullable(usersById.get(userId));
    }

    @Override
    public Optional<UserInfo> findByMobile(String mobile) {
        String userId = mobileMapping.get(mobile);
        return userId != null ? Optional.ofNullable(usersById.get(userId)) : Optional.empty();
    }

    @Override
    public Optional<UserInfo> findByEmail(String email) {
        String userId = emailMapping.get(email);
        return userId != null ? Optional.ofNullable(usersById.get(userId)) : Optional.empty();
    }

    @Override
    public UserInfo createUser(WecomApiService.WecomUserDetailResult wecomUserDetail, String corpId, String agentId) {
        // 验证参数
        if (!isValidWecomUserId(wecomUserDetail.getUserId())) {
            throw new RuntimeException("Invalid wecom user id: " + wecomUserDetail.getUserId());
        }

        if (!isValidCorpId(corpId)) {
            throw new RuntimeException("Invalid corp id: " + corpId);
        }

        // 检查是否已存在
        String wecomKey = buildWecomUserKey(wecomUserDetail.getUserId(), corpId);
        if (wecomUserMapping.containsKey(wecomKey)) {
            throw new RuntimeException("Wecom user already exists: " + wecomKey);
        }

        // 创建用户
        String userId = String.valueOf(userIdGenerator.incrementAndGet());
        String username = generateDefaultUsername(wecomUserDetail.getUserId(), corpId);
        String nickname = wecomUserDetail.getName() != null ? wecomUserDetail.getName() : 
                         generateDefaultNickname(wecomUserDetail.getUserId());

        UserInfo userInfo = UserInfo.builder()
                .userId(userId)
                .username(username)
                .nickname(nickname)
                .phone(wecomUserDetail.getMobile())
                .email(wecomUserDetail.getEmail())
                .avatar(wecomUserDetail.getAvatar())
                .roles(getDefaultRoles())
                .permissions(getDefaultPermissions())
                .status(1)
                .enabled(true)
                .accountNonExpired(true)
                .accountNonLocked(true)
                .credentialsNonExpired(true)
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .build();

        // 添加企业微信相关属性
        userInfo.addAttribute("wecom_user_id", wecomUserDetail.getUserId());
        userInfo.addAttribute("corp_id", corpId);
        userInfo.addAttribute("agent_id", agentId);
        userInfo.addAttribute("wecom_name", wecomUserDetail.getName());
        userInfo.addAttribute("wecom_gender", wecomUserDetail.getGender());
        userInfo.addAttribute("wecom_position", wecomUserDetail.getPosition());
        userInfo.addAttribute("wecom_department", wecomUserDetail.getDepartment());
        userInfo.addAttribute("wecom_status", wecomUserDetail.getStatus());

        if (wecomUserDetail.getExtattr() != null) {
            userInfo.addAttribute("wecom_extattr", wecomUserDetail.getExtattr());
        }

        // 存储用户
        usersById.put(userId, userInfo);
        wecomUserMapping.put(wecomKey, userId);

        if (wecomUserDetail.getMobile() != null) {
            mobileMapping.put(wecomUserDetail.getMobile(), userId);
        }

        if (wecomUserDetail.getEmail() != null) {
            emailMapping.put(wecomUserDetail.getEmail(), userId);
        }

        log.info("Created new wecom user: userId={}, wecomUserId={}, corpId={}, name={}", 
                userId, wecomUserDetail.getUserId(), corpId, wecomUserDetail.getName());
        return userInfo;
    }

    @Override
    public boolean bindWecomUser(String userId, String wecomUserId, String corpId, String agentId) {
        UserInfo userInfo = usersById.get(userId);
        if (userInfo == null) {
            log.warn("User not found for binding: {}", userId);
            return false;
        }

        String wecomKey = buildWecomUserKey(wecomUserId, corpId);
        if (wecomUserMapping.containsKey(wecomKey)) {
            log.warn("Wecom user already bound: {}", wecomKey);
            return false;
        }

        // 绑定企业微信用户
        wecomUserMapping.put(wecomKey, userId);
        userInfo.addAttribute("wecom_user_id", wecomUserId);
        userInfo.addAttribute("corp_id", corpId);
        userInfo.addAttribute("agent_id", agentId);
        userInfo.setUpdateTime(LocalDateTime.now());

        log.info("Bound wecom user: userId={}, wecomUserId={}, corpId={}", userId, wecomUserId, corpId);
        return true;
    }

    @Override
    public boolean unbindWecomUser(String userId, String corpId) {
        UserInfo userInfo = usersById.get(userId);
        if (userInfo == null) {
            return false;
        }

        String wecomUserId = userInfo.getAttribute("wecom_user_id", String.class);
        if (wecomUserId == null) {
            return false;
        }

        String wecomKey = buildWecomUserKey(wecomUserId, corpId);
        wecomUserMapping.remove(wecomKey);
        
        userInfo.getAttributes().remove("wecom_user_id");
        userInfo.getAttributes().remove("corp_id");
        userInfo.getAttributes().remove("agent_id");
        userInfo.setUpdateTime(LocalDateTime.now());

        log.info("Unbound wecom user: userId={}, wecomUserId={}, corpId={}", userId, wecomUserId, corpId);
        return true;
    }

    @Override
    public boolean isUserValid(UserInfo userInfo) {
        return userInfo != null && Boolean.TRUE.equals(userInfo.getEnabled());
    }

    @Override
    public boolean isUserLocked(UserInfo userInfo) {
        return userInfo != null && Boolean.FALSE.equals(userInfo.getAccountNonLocked());
    }

    @Override
    public boolean isUserDisabled(UserInfo userInfo) {
        return userInfo != null && Boolean.FALSE.equals(userInfo.getEnabled());
    }

    @Override
    public boolean isAccountExpired(UserInfo userInfo) {
        return userInfo != null && Boolean.FALSE.equals(userInfo.getAccountNonExpired());
    }

    @Override
    public void updateLastLoginInfo(String userId, LocalDateTime loginTime, String ipAddress, String userAgent) {
        UserInfo userInfo = usersById.get(userId);
        if (userInfo != null) {
            userInfo.setLastLoginTime(loginTime);
            userInfo.setLastLoginIp(ipAddress);
            userInfo.setUpdateTime(LocalDateTime.now());
            userInfo.addAttribute("lastUserAgent", userAgent);
            log.debug("Updated last login info for user: {}", userId);
        }
    }

    @Override
    public void recordLoginFailure(String wecomUserId, String corpId, String ipAddress, String reason) {
        String key = buildWecomUserKey(wecomUserId, corpId);
        LoginFailureRecord record = loginFailures.computeIfAbsent(key, k -> new LoginFailureRecord());
        record.incrementFailureCount();
        record.setLastFailureTime(LocalDateTime.now());
        record.setLastFailureIp(ipAddress);
        record.setLastFailureReason(reason);

        log.debug("Recorded login failure for wecom user: {}, count: {}", key, record.getFailureCount());
    }

    @Override
    public void clearLoginFailures(String wecomUserId, String corpId) {
        String key = buildWecomUserKey(wecomUserId, corpId);
        loginFailures.remove(key);
        log.debug("Cleared login failures for wecom user: {}", key);
    }

    @Override
    public int getLoginFailureCount(String wecomUserId, String corpId) {
        String key = buildWecomUserKey(wecomUserId, corpId);
        LoginFailureRecord record = loginFailures.get(key);
        return record != null ? record.getFailureCount() : 0;
    }

    @Override
    public void lockUser(String userId, String reason, LocalDateTime lockUntil) {
        UserInfo userInfo = usersById.get(userId);
        if (userInfo != null) {
            userInfo.setAccountNonLocked(false);
            userInfo.setUpdateTime(LocalDateTime.now());
            userInfo.addAttribute("lockReason", reason);
            userInfo.addAttribute("lockUntil", lockUntil);
            log.info("Locked user: {}, reason: {}, until: {}", userId, reason, lockUntil);
        }
    }

    @Override
    public void unlockUser(String userId) {
        UserInfo userInfo = usersById.get(userId);
        if (userInfo != null) {
            userInfo.setAccountNonLocked(true);
            userInfo.setUpdateTime(LocalDateTime.now());
            userInfo.getAttributes().remove("lockReason");
            userInfo.getAttributes().remove("lockUntil");
            log.info("Unlocked user: {}", userId);
        }
    }

    /**
     * 生成默认昵称
     *
     * @param wecomUserId 企业微信用户ID
     * @return 默认昵称
     */
    private String generateDefaultNickname(String wecomUserId) {
        return "企微用户" + wecomUserId.substring(Math.max(0, wecomUserId.length() - 4));
    }

    /**
     * 登录失败记录
     */
    private static class LoginFailureRecord {
        private int failureCount = 0;
        private LocalDateTime lastFailureTime;
        private String lastFailureIp;
        private String lastFailureReason;

        public void incrementFailureCount() {
            this.failureCount++;
        }

        public int getFailureCount() {
            return failureCount;
        }

        public LocalDateTime getLastFailureTime() {
            return lastFailureTime;
        }

        public void setLastFailureTime(LocalDateTime lastFailureTime) {
            this.lastFailureTime = lastFailureTime;
        }

        public String getLastFailureIp() {
            return lastFailureIp;
        }

        public void setLastFailureIp(String lastFailureIp) {
            this.lastFailureIp = lastFailureIp;
        }

        public String getLastFailureReason() {
            return lastFailureReason;
        }

        public void setLastFailureReason(String lastFailureReason) {
            this.lastFailureReason = lastFailureReason;
        }
    }

    /**
     * 获取统计信息
     *
     * @return 统计信息
     */
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new java.util.HashMap<>();
        stats.put("totalUsers", usersById.size());
        stats.put("wecomUsers", wecomUserMapping.size());
        stats.put("mobileUsers", mobileMapping.size());
        stats.put("emailUsers", emailMapping.size());
        stats.put("loginFailures", loginFailures.size());
        
        long enabledUsers = usersById.values().stream()
                .filter(user -> Boolean.TRUE.equals(user.getEnabled()))
                .count();
        stats.put("enabledUsers", enabledUsers);
        
        return stats;
    }
}
