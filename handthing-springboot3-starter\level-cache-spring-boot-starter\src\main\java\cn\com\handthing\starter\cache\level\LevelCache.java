package cn.com.handthing.starter.cache.level;

import cn.com.handthing.starter.cache.level.sync.CacheSyncMessage;
import cn.com.handthing.starter.cache.level.sync.CacheSyncService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.cache.support.SimpleValueWrapper;

import java.util.concurrent.Callable;

/**
 * 多级缓存实现
 * <p>
 * 实现L1(本地缓存)+L2(远程缓存)的多级缓存架构
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
public class LevelCache implements Cache {

    private final String name;
    private final Cache l1Cache;
    private final Cache l2Cache;
    private final CacheSyncService syncService;
    private final boolean enableSync;

    /**
     * 构造函数
     *
     * @param name        缓存名称
     * @param l1Cache     L1缓存
     * @param l2Cache     L2缓存
     * @param syncService 同步服务
     * @param enableSync  是否启用同步
     */
    public LevelCache(String name, Cache l1Cache, Cache l2Cache, 
                     CacheSyncService syncService, boolean enableSync) {
        this.name = name;
        this.l1Cache = l1Cache;
        this.l2Cache = l2Cache;
        this.syncService = syncService;
        this.enableSync = enableSync;
    }

    @Override
    public String getName() {
        return name;
    }

    @Override
    public Object getNativeCache() {
        return this;
    }

    @Override
    public ValueWrapper get(Object key) {
        String keyStr = String.valueOf(key);
        
        // 1. 先查询L1缓存
        ValueWrapper l1Value = l1Cache.get(key);
        if (l1Value != null) {
            log.debug("L1 cache hit for key: {} in cache: {}", keyStr, name);
            return l1Value;
        }

        // 2. L1未命中，查询L2缓存
        ValueWrapper l2Value = l2Cache.get(key);
        if (l2Value != null) {
            log.debug("L2 cache hit for key: {} in cache: {}", keyStr, name);
            // 3. 将L2的值回写到L1
            try {
                l1Cache.put(key, l2Value.get());
                log.debug("Wrote back to L1 cache for key: {} in cache: {}", keyStr, name);
            } catch (Exception e) {
                log.warn("Failed to write back to L1 cache for key: {} in cache: {}", keyStr, name, e);
            }
            return l2Value;
        }

        log.debug("Cache miss for key: {} in cache: {}", keyStr, name);
        return null;
    }

    @Override
    public <T> T get(Object key, Class<T> type) {
        ValueWrapper wrapper = get(key);
        if (wrapper == null) {
            return null;
        }
        Object value = wrapper.get();
        if (value != null && type != null && !type.isInstance(value)) {
            throw new IllegalStateException("Cached value is not of required type [" + type.getName() + "]: " + value);
        }
        return (T) value;
    }

    @Override
    public <T> T get(Object key, Callable<T> valueLoader) {
        ValueWrapper wrapper = get(key);
        if (wrapper != null) {
            return (T) wrapper.get();
        }

        try {
            T value = valueLoader.call();
            put(key, value);
            return value;
        } catch (Exception e) {
            throw new ValueRetrievalException(key, valueLoader, e);
        }
    }

    @Override
    public void put(Object key, Object value) {
        String keyStr = String.valueOf(key);
        try {
            // 1. 先写L2缓存（远程缓存）
            l2Cache.put(key, value);
            log.debug("Put to L2 cache for key: {} in cache: {}", keyStr, name);

            // 2. 再写L1缓存（本地缓存）
            l1Cache.put(key, value);
            log.debug("Put to L1 cache for key: {} in cache: {}", keyStr, name);

            // 3. 发送同步消息
            if (enableSync && syncService != null) {
                syncService.sendPutMessage(name, keyStr);
            }
        } catch (Exception e) {
            log.error("Failed to put cache value for key: {} in cache: {}", keyStr, name, e);
            throw e;
        }
    }

    @Override
    public ValueWrapper putIfAbsent(Object key, Object value) {
        String keyStr = String.valueOf(key);
        // 检查是否已存在
        ValueWrapper existing = get(key);
        if (existing != null) {
            return existing;
        }
        // 不存在则放入
        put(key, value);
        return null;
    }

    @Override
    public void evict(Object key) {
        String keyStr = String.valueOf(key);
        try {
            // 1. 先删除L2缓存（远程缓存）
            l2Cache.evict(key);
            log.debug("Evicted from L2 cache for key: {} in cache: {}", keyStr, name);

            // 2. 再删除L1缓存（本地缓存）
            l1Cache.evict(key);
            log.debug("Evicted from L1 cache for key: {} in cache: {}", keyStr, name);

            // 3. 发送同步消息
            if (enableSync && syncService != null) {
                syncService.sendEvictMessage(name, keyStr);
            }
        } catch (Exception e) {
            log.error("Failed to evict cache value for key: {} in cache: {}", keyStr, name, e);
            throw e;
        }
    }

    @Override
    public boolean evictIfPresent(Object key) {
        String keyStr = String.valueOf(key);
        // 检查是否存在
        if (get(key) == null) {
            return false;
        }
        // 存在则删除
        evict(key);
        return true;
    }

    @Override
    public void clear() {
        try {
            // 1. 先清空L2缓存（远程缓存）
            l2Cache.clear();
            log.debug("Cleared L2 cache: {}", name);

            // 2. 再清空L1缓存（本地缓存）
            l1Cache.clear();
            log.debug("Cleared L1 cache: {}", name);

            // 3. 发送同步消息
            if (enableSync && syncService != null) {
                syncService.sendClearMessage(name);
            }
        } catch (Exception e) {
            log.error("Failed to clear cache: {}", name, e);
            throw e;
        }
    }

    /**
     * 处理同步消息（仅操作L1缓存，避免循环）
     *
     * @param key       缓存键
     * @param operation 操作类型
     */
    public void handleSyncMessage(String key, String operation) {
        try {
            switch (operation.toUpperCase()) {
                case "EVICT":
                    l1Cache.evict(key);
                    log.debug("Sync evicted from L1 cache for key: {} in cache: {}", key, name);
                    break;
                case "CLEAR":
                    l1Cache.clear();
                    log.debug("Sync cleared L1 cache: {}", name);
                    break;
                case "PUT":
                    // PUT操作通常不需要同步处理，因为下次访问时会自动从L2回写到L1
                    log.debug("Sync put message received for key: {} in cache: {} (no action needed)", key, name);
                    break;
                default:
                    log.warn("Unknown sync operation: {} for key: {} in cache: {}", operation, key, name);
            }
        } catch (Exception e) {
            log.error("Failed to handle sync message for key: {} in cache: {}", key, name, e);
        }
    }

    /**
     * 获取L1缓存
     *
     * @return L1缓存
     */
    public Cache getL1Cache() {
        return l1Cache;
    }

    /**
     * 获取L2缓存
     *
     * @return L2缓存
     */
    public Cache getL2Cache() {
        return l2Cache;
    }
}