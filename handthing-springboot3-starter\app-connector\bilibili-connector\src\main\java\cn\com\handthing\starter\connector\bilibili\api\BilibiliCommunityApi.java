package cn.com.handthing.starter.connector.bilibili.api;

import cn.com.handthing.starter.connector.bilibili.config.BilibiliConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.HashMap;
import java.util.Map;

/**
 * Bilibili社区API
 * <p>
 * 提供Bilibili社区互动相关的API功能，包括关注管理、评论互动、弹幕管理等。
 * 支持个人创作者、企业用户和MCN机构的社区运营功能。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "handthing.connector.bilibili", name = "enabled", havingValue = "true")
public class BilibiliCommunityApi {

    private final BilibiliConfig config;
    private final RestTemplate restTemplate;

    /**
     * 获取粉丝列表
     *
     * @param accessToken 访问令牌
     * @param page        页码
     * @param pageSize    每页大小
     * @return 粉丝列表
     */
    public Map<String, Object> getFollowersList(String accessToken, Integer page, Integer pageSize) {
        try {
            log.debug("Getting Bilibili followers list, page: {}", page);

            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(config.getEffectiveApiBaseUrl() + "/x/relation/followers")
                    .queryParam("access_token", accessToken)
                    .queryParam("pn", page != null ? page : 1)
                    .queryParam("ps", pageSize != null ? pageSize : 20);

            String url = builder.build().toUriString();

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + accessToken);

            HttpEntity<Void> request = new HttpEntity<>(headers);

            Map<String, Object> response = restTemplate.exchange(url, HttpMethod.GET, request, Map.class).getBody();
            log.debug("Bilibili followers list retrieved, response: {}", response);

            return response;

        } catch (Exception e) {
            log.error("Failed to get Bilibili followers list", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", -1);
            errorResponse.put("message", "Failed to get followers list: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 获取关注列表
     *
     * @param accessToken 访问令牌
     * @param page        页码
     * @param pageSize    每页大小
     * @return 关注列表
     */
    public Map<String, Object> getFollowingList(String accessToken, Integer page, Integer pageSize) {
        try {
            log.debug("Getting Bilibili following list, page: {}", page);

            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(config.getEffectiveApiBaseUrl() + "/x/relation/followings")
                    .queryParam("access_token", accessToken)
                    .queryParam("pn", page != null ? page : 1)
                    .queryParam("ps", pageSize != null ? pageSize : 20);

            String url = builder.build().toUriString();

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + accessToken);

            HttpEntity<Void> request = new HttpEntity<>(headers);

            Map<String, Object> response = restTemplate.exchange(url, HttpMethod.GET, request, Map.class).getBody();
            log.debug("Bilibili following list retrieved, response: {}", response);

            return response;

        } catch (Exception e) {
            log.error("Failed to get Bilibili following list", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", -1);
            errorResponse.put("message", "Failed to get following list: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 获取视频评论
     *
     * @param accessToken 访问令牌
     * @param oid         视频AV号
     * @param page        页码
     * @param pageSize    每页大小
     * @return 评论列表
     */
    public Map<String, Object> getVideoComments(String accessToken, Long oid, Integer page, Integer pageSize) {
        try {
            log.debug("Getting Bilibili video comments for oid: {}", oid);

            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(config.getEffectiveApiBaseUrl() + "/x/v2/reply")
                    .queryParam("access_token", accessToken)
                    .queryParam("type", 1) // 1表示视频
                    .queryParam("oid", oid)
                    .queryParam("pn", page != null ? page : 1)
                    .queryParam("ps", pageSize != null ? pageSize : 20);

            String url = builder.build().toUriString();

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + accessToken);

            HttpEntity<Void> request = new HttpEntity<>(headers);

            Map<String, Object> response = restTemplate.exchange(url, HttpMethod.GET, request, Map.class).getBody();
            log.debug("Bilibili video comments retrieved, response: {}", response);

            return response;

        } catch (Exception e) {
            log.error("Failed to get Bilibili video comments", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", -1);
            errorResponse.put("message", "Failed to get video comments: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 发送评论
     *
     * @param accessToken 访问令牌
     * @param oid         视频AV号
     * @param message     评论内容
     * @return 发送结果
     */
    public Map<String, Object> sendComment(String accessToken, Long oid, String message) {
        try {
            log.debug("Sending Bilibili comment to oid: {}", oid);

            String url = config.getEffectiveApiBaseUrl() + "/x/v2/reply/add";

            Map<String, Object> commentData = new HashMap<>();
            commentData.put("access_token", accessToken);
            commentData.put("type", 1); // 1表示视频
            commentData.put("oid", oid);
            commentData.put("message", message);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + accessToken);

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(commentData, headers);

            Map<String, Object> response = restTemplate.postForObject(url, request, Map.class);
            log.debug("Bilibili comment sent, response: {}", response);

            return response;

        } catch (Exception e) {
            log.error("Failed to send Bilibili comment", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", -1);
            errorResponse.put("message", "Failed to send comment: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 获取弹幕列表
     *
     * @param accessToken 访问令牌
     * @param cid         视频CID
     * @return 弹幕列表
     */
    public Map<String, Object> getDanmakuList(String accessToken, Long cid) {
        try {
            log.debug("Getting Bilibili danmaku list for cid: {}", cid);

            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(config.getEffectiveApiBaseUrl() + "/x/v1/dm/list.so")
                    .queryParam("access_token", accessToken)
                    .queryParam("oid", cid);

            String url = builder.build().toUriString();

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + accessToken);

            HttpEntity<Void> request = new HttpEntity<>(headers);

            Map<String, Object> response = restTemplate.exchange(url, HttpMethod.GET, request, Map.class).getBody();
            log.debug("Bilibili danmaku list retrieved, response: {}", response);

            return response;

        } catch (Exception e) {
            log.error("Failed to get Bilibili danmaku list", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", -1);
            errorResponse.put("message", "Failed to get danmaku list: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 发送弹幕
     *
     * @param accessToken 访问令牌
     * @param cid         视频CID
     * @param progress    弹幕时间（毫秒）
     * @param message     弹幕内容
     * @return 发送结果
     */
    public Map<String, Object> sendDanmaku(String accessToken, Long cid, Long progress, String message) {
        try {
            log.debug("Sending Bilibili danmaku to cid: {}", cid);

            String url = config.getEffectiveApiBaseUrl() + "/x/v2/dm/post";

            Map<String, Object> danmakuData = new HashMap<>();
            danmakuData.put("access_token", accessToken);
            danmakuData.put("type", 1); // 1表示普通弹幕
            danmakuData.put("oid", cid);
            danmakuData.put("msg", message);
            danmakuData.put("progress", progress);
            danmakuData.put("color", 16777215); // 白色
            danmakuData.put("fontsize", 25); // 字体大小
            danmakuData.put("pool", 0); // 弹幕池
            danmakuData.put("mode", 1); // 弹幕模式，1为滚动

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + accessToken);

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(danmakuData, headers);

            Map<String, Object> response = restTemplate.postForObject(url, request, Map.class);
            log.debug("Bilibili danmaku sent, response: {}", response);

            return response;

        } catch (Exception e) {
            log.error("Failed to send Bilibili danmaku", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", -1);
            errorResponse.put("message", "Failed to send danmaku: " + e.getMessage());
            return errorResponse;
        }
    }
}
