好的，遵照您的要求，我将基于我们之前确认的需求清单，为您撰写一份正式且详细的设计文档。

这份文档将作为项目开发的权威指南，涵盖了从宏观架构到微观实现的所有关键设计决策。

-----

### **HandThing App Connector Spring Boot Starter**

### **详细设计文档**

| **文档版本** | **V1.0** |
| :--- | :--- |
| **状态** | 草案 (Draft) |
| **作者** | HandThing, Gemini |
| **创建日期** | 2025-07-27 |
| **最后更新** | 2025-07-27 |

-----

### **1. 概述 (Overview)**

#### **1.1. 文档目的**

本文档旨在详细阐述 `app-connector-spring-boot-starter` 的技术架构、模块划分、核心功能、接口设计和实现细节。它将作为开发团队进行编码、测试和后续维护的主要依据。

#### **1.2. 项目背景**

在现代应用开发中，与第三方开放平台（如企业微信、钉钉、抖音等）的集成是常见需求。然而，各平台API迥异，认证流程复杂，导致开发人员需要投入大量重复性工作。本项目旨在创建一个统一的应用连接器，它构建于 `http-client-spring-boot-starter` 之上，为Spring Boot应用提供一个配置简单、使用方便、功能强大的第三方应用集成解决方案。

#### **1.3. 设计目标**

* **统一认证**: 提供一套标准的认证授权框架，兼容OAuth 2.0等多种协议。
* **能力封装**: 对各平台独有的业务API进行封装，提供语义化的Java接口。
* **模块化**: 允许开发者按需引入所需平台的连接器，避免项目臃肿。
* **配置驱动**: 通过`application.yml`完成所有配置，实现“开箱即用”。
* **高扩展性**: 框架设计应易于扩展，支持未来快速集成新的第三方平台。
* **企业级健壮性**: 内置令牌管理、统一异常处理、事件通知等企业级特性。

### **2. 整体架构 (Overall Architecture)**

#### **2.1. 架构分层图**

```
┌────────────────────────────────────────────────────────────┐
│                       业务应用代码 (Your App)                  │
├────────────────────────────────────────────────────────────┤
│  @Autowired                                                │
│  ┌────────────────────┐   ┌──────────────────────────────┐ │
│  │   AuthService      │   │ WeComService, DouyinService, ... │ │
│  │ (统一认证服务)     │   │ (平台专属API服务)                │ │
│  └────────────────────┘   └──────────────────────────────┘ │
└───────────────────────┬────────────────────────────────────┘
                        │
┌───────────────────────▼────────────────────────────────────┐
│      AppConnectorAutoConfiguration (总入口, 按需装配)        │
│          (管理Bean生命周期, 如AuthProvider, TokenManager)    │
├────────────────────────────────────────────────────────────┤
│ ┌──────────────────┬──────────────────┬──────────────────┐ │
│ │ 统一认证授权框架 │   平台API封装层  │  企业级功能模块  │ │
│ │ (策略模式)       │ (外观模式)       │ (Token/Event)    │ │
│ └──────────────────┴──────────────────┴──────────────────┘ │
└───────────────────────┬────────────────────────────────────┘
                        │
┌───────────────────────▼────────────────────────────────────┐
│          http-client-spring-boot-starter (底层通信)          │
└────────────────────────────────────────────────────────────┘
```

#### **2.2. 多模块设计**

项目采用Maven多模块结构，职责划分清晰：

* **`app-connector-core`**: **核心抽象层**。定义所有公共接口、数据模型（POJO）和自定义异常。此模块不依赖Spring，具有高复用性。
* **`app-connector-spring-boot-starter`**: **自动配置层**。实现Spring Boot的自动配置 (`AutoConfiguration`)，负责扫描、创建和装配所有核心服务Bean。
* **`{platform}-connector`** (如 `wecom-connector`, `douyin-connector`): **平台实现层**。提供特定平台的`AuthProvider`策略实现和`{Platform}Service`业务API封装。每个模块都是一个独立的依赖。

#### **2.3. 核心流程：OAuth2.0授权码模式**

1.  **发起授权**: 业务代码调用 `authService.getAuthorizationUrl(PlatformType.WECOM, ...)` 获取授权URL。
2.  **用户跳转**: 应用将用户重定向到该URL，用户在第三方平台完成授权操作。
3.  **平台回调**: 第三方平台携带 `code` 和 `state` 回调至应用在配置中指定的 `redirect-uri`。
4.  **处理回调**: `ConnectorCallbackController` (可选) 或业务自定义的Controller接收回调。
5.  **令牌交换**: Controller调用 `authService.exchangeToken(PlatformType.WECOM, code)`，内部委托给 `WeComAuthProvider`，通过 `HttpClientService` 向平台换取 `AccessToken`。
6.  **令牌存储**: `TokenManager` 将获取到的 `UnifiedAccessToken` 对象存入指定的 `TokenStore` (内存或Redis)。
7.  **获取用户信息**: `AuthService` 继续调用 `WeComAuthProvider` 的 `getUserInfo` 方法获取用户信息。
8.  **发布事件**: 框架发布 `UserAuthorizedEvent`，包含平台类型和用户信息。
9.  **业务处理**: 业务侧的事件监听器 (`@EventListener`) 接收事件，执行登录、注册等后续逻辑。

### **3. 模块详细设计 (Detailed Module Design)**

#### **3.1. `app-connector-core` 模块**

##### 3.1.1. 核心接口

* **`AuthProvider`**:
  ```java
  public interface AuthProvider {
      boolean supports(PlatformType platformType);
      String getAuthorizationUrl(String state, String... scopes);
      UnifiedAccessToken exchangeToken(String code);
      UnifiedAccessToken refreshAccessToken(String refreshToken);
      UnifiedUserInfo getUserInfo(UnifiedAccessToken accessToken);
  }
  ```
* **`TokenStore`**:
  ```java
  public interface TokenStore {
      void save(String key, UnifiedAccessToken token, Duration timeout);
      UnifiedAccessToken get(String key);
      void delete(String key);
  }
  ```

##### 3.1.2. 数据模型 (POJO)

* **`PlatformType` (Enum)**: `WECOM`, `DINGTALK`, `DOUYIN`, `ALIPAY`, etc.
* **`UnifiedAccessToken`**:
    * `String accessToken`: 访问令牌
    * `String refreshToken`: 刷新令牌
    * `Long expiresIn`: 有效期（秒）
    * `String openId`, `String unionId`: 用户唯一标识
    * `String scope`: 授权范围
    * `Map<String, Object> rawResponse`: 原始响应体
* **`UnifiedUserInfo`**:
    * `PlatformType platform`: 平台类型
    * `String openId`, `String unionId`, `String userId`: 用户标识
    * `String nickname`, `String avatarUrl`: 昵称和头像
    * `Map<String, Object> rawResponse`: 原始响应体

##### 3.1.3. 异常体系

* `ConnectorException extends RuntimeException`
    * `AuthException`: 认证失败
    * `TokenExchangeException`: 令牌交换失败
    * `TokenRefreshException`: 令牌刷新失败
    * `ApiCallException`: 业务API调用失败 (包含 `errCode` 和 `errMsg`)

#### **3.2. `app-connector-spring-boot-starter` 模块**

##### 3.2.1. 配置模型 (`@ConfigurationProperties`)

**YAML 结构:**

```yaml
handthing:
  connector:
    redirect-uri-prefix: "http://localhost:8080/auth"
    token-store-type: redis # MEMORY or REDIS
    state-timeout: 10m
    platforms:
      wecom:
        enabled: true
        app-id: "ww..." # corp-id
        secret: "..."
        agent-id: "1000001"
      douyin:
        enabled: true
        app-id: "tt..." # client-key
        secret: "..."
```

**Java 类:**

* `AppConnectorProperties.java`
* `PlatformProperties.java` (基类)
* `WeComPlatformProperties.java` (扩展类)

##### 3.2.2. 自动配置 (`AppConnectorAutoConfiguration`)

* **`@EnableConfigurationProperties(AppConnectorProperties.class)`**
* **`TokenStore` Bean**:
    * `@ConditionalOnProperty(name = "handthing.connector.token-store-type", havingValue = "redis")`
    * `@ConditionalOnClass(RedisOperations.class)`
    * 创建 `RedisTokenStore` Bean。
    * `@ConditionalOnMissingBean` 创建 `InMemoryTokenStore` Bean 作为默认。
* **`TokenManager` Bean**:
    * 注入 `TokenStore` 并创建 `TokenManager`。
* **`AuthService` Bean**:
    * 注入 `List<AuthProvider>` 和 `TokenManager` 并创建 `AuthService`。所有平台实现模块中的 `AuthProvider` Bean 都会被自动收集。
* **平台 `Service` Beans** (e.g., `WeComService`):
    * 通过各平台连接器中的 `@Configuration` 类来创建。
    * 使用 `@ConditionalOnProperty(name = "handthing.connector.platforms.wecom.enabled", havingValue = "true")` 来控制是否启用。
    * 注入 `HttpClientService`, `TokenManager`, 和对应的 `PlatformProperties`。

#### **3.3. 平台实现模块 (以 `wecom-connector` 为例)**

* **`WeComAuthProvider.java`**: 实现 `AuthProvider` 接口，具体调用企业微信的OAuth接口。
* **`WeComService.java`**:
  ```java
  public class WeComService {
      // 注入 HttpClientService, TokenManager, WeComPlatformProperties
      public WeComMessageApi messageApi() { ... }
      public WeComUserApi userApi() { ... }
  }
  ```
* **`WeComMessageApi.java`**: 封装消息推送相关API。
* **`WeComConnectorAutoConfiguration.java`**:
  ```java
  @Configuration
  @ConditionalOnProperty(name = "handthing.connector.platforms.wecom.enabled", havingValue = "true")
  public class WeComConnectorAutoConfiguration {
      @Bean
      public WeComAuthProvider weComAuthProvider(...) { ... }
      @Bean
      public WeComService weComService(...) { ... }
  }
  ```

### **4. 企业级功能设计 (Enterprise Feature Design)**

#### **4.1. 统一令牌管理 (`TokenManager`)**

* **缓存**: `getToken` 方法优先从 `TokenStore` 读取。
* **自动刷新**: 在`getToken`时，如果令牌即将过期（例如，剩余有效期 \< 5分钟），则自动触发刷新流程，并将新令牌存入 `TokenStore`。此过程对调用者透明。
* **Key策略**: `TokenStore` 的 key 格式定义为 `connector:token:{platform}:{principal}` (principal 通常是用户ID或应用自身标识)。

#### **4.2. 统一回调处理 (`ConnectorCallbackController`)**

* 提供一个可选的Controller，通过 `@ConditionalOnProperty` 控制是否启用。
* 监听路径 `/{prefix}/callback/{platform}`。
* 内部逻辑：
    1.  从`HttpServletRequest`中解析 `code` 和 `state`。
    2.  从缓存/Session中验证 `state` 的有效性。
    3.  调用 `authService.exchangeTokenAndGetUserInfo(platform, code)`。
    4.  发布 `UserAuthorizedEvent`。
    5.  重定向到预定义的成功/失败页面。

#### **4.3. 事件驱动机制**

* `UserAuthorizedEvent extends ApplicationEvent`: 携带 `UnifiedUserInfo` 和 `UnifiedAccessToken`。
* `TokenRefreshedEvent extends ApplicationEvent`: 携带 `PlatformType` 和新的 `UnifiedAccessToken`。

### **5. API 使用示例**

**1. 配置 `application.yml`**

```yaml
handthing:
  http-client: # 底层依赖配置
    enabled: true
    logging:
      level: BASIC
  connector:
    token-store-type: memory
    platforms:
      wecom:
        enabled: true
        app-id: "YOUR_CORP_ID"
        secret: "YOUR_SECRET"
        agent-id: "YOUR_AGENT_ID"
```

**2. 业务代码**

```java
@Service
public class MyBusinessService {

    @Autowired
    private AuthService authService;

    @Autowired
    private WeComService weComService;

    // 步骤1: 获取授权URL
    public String getWeComLoginUrl() {
        // state可以是一个随机字符串，用于安全校验
        return authService.getAuthorizationUrl(PlatformType.WECOM, "random-state-123");
    }
    
    // 步骤2: 业务逻辑 (通过监听事件)
    @EventListener
    public void handleUserAuthorization(UserAuthorizedEvent event) {
        if (event.getSource() instanceof UnifiedUserInfo userInfo) {
            if (userInfo.getPlatform() == PlatformType.WECOM) {
                System.out.println("用户 " + userInfo.getNickname() + " 已通过企业微信授权！");
                
                // 步骤3: 直接调用平台API
                sendWelcomeMessage(userInfo.getUserId());
            }
        }
    }
    
    private void sendWelcomeMessage(String userId) {
        try {
            weComService.messageApi().sendText(userId, "欢迎您！感谢您的授权。");
        } catch (ApiCallException e) {
            // 处理API调用异常
            System.err.println("发送欢迎消息失败: " + e.getErrMsg());
        }
    }
}
```

### **6. 附录 (Appendix)**

#### **6.1. 缓存设计 (Redis)**

* **Token Key**: `connector:token:wecom:user123`
* **State Key**: `connector:state:sessionABC`
* **Value**: 序列化后的 `UnifiedAccessToken` 对象或 `state` 字符串。
* **TTL**: Token的TTL应略小于其真实有效期；State的TTL应为短期（如10分钟）。