package cn.com.handthing.starter.id.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * ID自动注入注解
 * <p>
 * 通过在实体字段上声明@IdSetter注解，实现ID的自动注入，对业务代码零侵入。
 * 支持策略模式，可以指定具体的ID生成策略，也可以根据字段类型自动匹配默认策略。
 * </p>
 * 
 * <h3>使用示例：</h3>
 * <pre>
 * &#64;Data
 * public class Product extends BaseEntity {
 *     &#64;IdSetter  // 自动使用Long类型默认生成器（Snowflake）
 *     &#64;TableId
 *     private Long id;
 *     
 *     &#64;IdSetter  // 自动使用String类型默认生成器（UUID）
 *     private String productNo;
 *     
 *     &#64;IdSetter(strategy = "orderNoGenerator")  // 使用自定义生成器
 *     private String orderNo;
 * }
 * </pre>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
@Documented
public @interface IdSetter {
    
    /**
     * 指定要使用的ID生成器策略的Bean名称
     * <p>
     * 如果为空，则使用与字段类型匹配的默认策略：
     * <ul>
     *   <li>String 类型 → UUID生成器</li>
     *   <li>Long 类型 → Snowflake生成器</li>
     * </ul>
     * </p>
     * 
     * @return 策略Bean的名称，默认为空字符串
     */
    String strategy() default "";
    
    /**
     * 是否在字段已有值时跳过ID生成
     * <p>
     * 如果设置为true，当字段已经有值时，不会覆盖现有值。
     * 如果设置为false，总是生成新的ID并覆盖现有值。
     * </p>
     * 
     * @return 是否跳过已有值，默认为true
     */
    boolean skipIfPresent() default true;
    
    /**
     * ID生成失败时的处理策略
     * <p>
     * 定义当ID生成失败时的处理方式：
     * <ul>
     *   <li>IGNORE - 忽略错误，继续执行</li>
     *   <li>LOG_ERROR - 记录错误日志，继续执行</li>
     *   <li>THROW_EXCEPTION - 抛出异常，中断执行</li>
     * </ul>
     * </p>
     * 
     * @return 失败处理策略，默认为LOG_ERROR
     */
    FailureStrategy onFailure() default FailureStrategy.LOG_ERROR;
    
    /**
     * ID生成失败时的处理策略枚举
     */
    enum FailureStrategy {
        /**
         * 忽略错误，继续执行
         */
        IGNORE,
        
        /**
         * 记录错误日志，继续执行
         */
        LOG_ERROR,
        
        /**
         * 抛出异常，中断执行
         */
        THROW_EXCEPTION
    }
}
