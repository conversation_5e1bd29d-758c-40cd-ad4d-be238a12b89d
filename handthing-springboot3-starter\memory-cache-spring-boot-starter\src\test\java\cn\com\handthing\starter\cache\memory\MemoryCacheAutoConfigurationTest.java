package cn.com.handthing.starter.cache.memory;

import cn.com.handthing.starter.cache.memory.config.MemoryCacheProperties;
import org.junit.jupiter.api.Test;
import org.springframework.boot.autoconfigure.AutoConfigurations;
import org.springframework.boot.test.context.runner.ApplicationContextRunner;
import org.springframework.cache.CacheManager;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * MemoryCacheAutoConfiguration 单元测试
 *
 * <AUTHOR>
 * @since V1.0.0
 */
class MemoryCacheAutoConfigurationTest {

    private final ApplicationContextRunner contextRunner = new ApplicationContextRunner()
            .withConfiguration(AutoConfigurations.of(MemoryCacheAutoConfiguration.class));

    @Test
    void testAutoConfiguration_Enabled() {
        contextRunner
                .withPropertyValues("handthing.cache.type=memory")
                .run(context -> {
                    assertThat(context).hasSingleBean(MemoryCacheProperties.class);
                    assertThat(context).hasSingleBean(CacheManager.class);
                    assertThat(context).getBean(CacheManager.class)
                            .isInstanceOf(MemoryCacheManager.class);
                });
    }

    @Test
    void testAutoConfiguration_Disabled() {
        contextRunner
                .withPropertyValues("handthing.cache.type=redis")
                .run(context -> {
                    assertThat(context).doesNotHaveBean(MemoryCacheManager.class);
                });
    }

    @Test
    void testMemoryCacheProperties_Binding() {
        contextRunner
                .withPropertyValues(
                        "handthing.cache.type=memory",
                        "handthing.cache.memory.default-max-size=2000",
                        "handthing.cache.memory.default-ttl=PT15M",
                        "handthing.cache.memory.cleanup-interval=PT10M",
                        "handthing.cache.memory.enable-stats=false",
                        "handthing.cache.memory.caches.users.max-size=500",
                        "handthing.cache.memory.caches.users.ttl=PT5M"
                )
                .run(context -> {
                    MemoryCacheProperties properties = context.getBean(MemoryCacheProperties.class);
                    assertThat(properties.getDefaultMaxSize()).isEqualTo(2000L);
                    assertThat(properties.getDefaultTtl().toMinutes()).isEqualTo(15);
                    assertThat(properties.getCleanupInterval().toMinutes()).isEqualTo(10);
                    assertThat(properties.isEnableStats()).isFalse();
                    
                    MemoryCacheProperties.MemoryCacheSpec userSpec = properties.getCaches().get("users");
                    assertThat(userSpec).isNotNull();
                    assertThat(userSpec.getMaxSize()).isEqualTo(500L);
                    assertThat(userSpec.getTtl().toMinutes()).isEqualTo(5);
                });
    }

    @Test
    void testMemoryCacheManager_Creation() {
        contextRunner
                .withPropertyValues("handthing.cache.type=memory")
                .run(context -> {
                    CacheManager cacheManager = context.getBean(CacheManager.class);
                    assertThat(cacheManager).isInstanceOf(MemoryCacheManager.class);
                    
                    // 测试缓存创建
                    var cache = cacheManager.getCache("testCache");
                    assertThat(cache).isNotNull();
                    assertThat(cache).isInstanceOf(MemoryCache.class);
                    assertThat(cache.getName()).isEqualTo("testCache");
                });
    }
}