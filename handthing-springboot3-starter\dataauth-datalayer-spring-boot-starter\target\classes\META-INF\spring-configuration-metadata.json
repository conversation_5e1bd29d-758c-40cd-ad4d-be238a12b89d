{"groups": [{"name": "handthing.datalayer.dataauth", "type": "cn.com.handthing.starter.dataauth.datalayer.config.DataAuthDatalayerProperties", "sourceType": "cn.com.handthing.starter.dataauth.datalayer.config.DataAuthDatalayerProperties"}, {"name": "handthing.datalayer.dataauth.cache", "type": "cn.com.handthing.starter.dataauth.datalayer.config.DataAuthDatalayerProperties$Cache", "sourceType": "cn.com.handthing.starter.dataauth.datalayer.config.DataAuthDatalayerProperties", "sourceMethod": "public cn.com.handthing.starter.dataauth.datalayer.config.DataAuthDatalayerProperties.Cache getCache() "}, {"name": "handthing.datalayer.dataauth.provider", "type": "cn.com.handthing.starter.dataauth.datalayer.config.DataAuthDatalayerProperties$Provider", "sourceType": "cn.com.handthing.starter.dataauth.datalayer.config.DataAuthDatalayerProperties", "sourceMethod": "public cn.com.handthing.starter.dataauth.datalayer.config.DataAuthDatalayerProperties.Provider getProvider() "}, {"name": "handthing.datalayer.dataauth.provider.dept", "type": "cn.com.handthing.starter.dataauth.datalayer.config.DataAuthDatalayerProperties$Provider$Dept", "sourceType": "cn.com.handthing.starter.dataauth.datalayer.config.DataAuthDatalayerProperties$Provider", "sourceMethod": "public cn.com.handthing.starter.dataauth.datalayer.config.DataAuthDatalayerProperties.Provider.Dept getDept() "}, {"name": "handthing.datalayer.dataauth.provider.role", "type": "cn.com.handthing.starter.dataauth.datalayer.config.DataAuthDatalayerProperties$Provider$Role", "sourceType": "cn.com.handthing.starter.dataauth.datalayer.config.DataAuthDatalayerProperties$Provider", "sourceMethod": "public cn.com.handthing.starter.dataauth.datalayer.config.DataAuthDatalayerProperties.Provider.Role getRole() "}, {"name": "handthing.datalayer.dataauth.provider.user", "type": "cn.com.handthing.starter.dataauth.datalayer.config.DataAuthDatalayerProperties$Provider$User", "sourceType": "cn.com.handthing.starter.dataauth.datalayer.config.DataAuthDatalayerProperties$Provider", "sourceMethod": "public cn.com.handthing.starter.dataauth.datalayer.config.DataAuthDatalayerProperties.Provider.User getUser() "}, {"name": "handthing.datalayer.dataauth.super-admin", "type": "cn.com.handthing.starter.dataauth.datalayer.config.DataAuthDatalayerProperties$SuperAdmin", "sourceType": "cn.com.handthing.starter.dataauth.datalayer.config.DataAuthDatalayerProperties", "sourceMethod": "public cn.com.handthing.starter.dataauth.datalayer.config.DataAuthDatalayerProperties.SuperAdmin getSuperAdmin() "}], "properties": [{"name": "handthing.datalayer.dataauth.aop-enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用AOP拦截器", "sourceType": "cn.com.handthing.starter.dataauth.datalayer.config.DataAuthDatalayerProperties"}, {"name": "handthing.datalayer.dataauth.cache-enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用权限缓存", "sourceType": "cn.com.handthing.starter.dataauth.datalayer.config.DataAuthDatalayerProperties"}, {"name": "handthing.datalayer.dataauth.cache.expire-seconds", "type": "java.lang.Long", "description": "缓存过期时间（秒）", "sourceType": "cn.com.handthing.starter.dataauth.datalayer.config.DataAuthDatalayerProperties$Cache"}, {"name": "handthing.datalayer.dataauth.cache.key-prefix", "type": "java.lang.String", "description": "缓存键前缀", "sourceType": "cn.com.handthing.starter.dataauth.datalayer.config.DataAuthDatalayerProperties$Cache"}, {"name": "handthing.datalayer.dataauth.cache.max-size", "type": "java.lang.Integer", "description": "最大缓存条目数", "sourceType": "cn.com.handthing.starter.dataauth.datalayer.config.DataAuthDatalayerProperties$Cache"}, {"name": "handthing.datalayer.dataauth.cache.statistics-enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用缓存统计", "sourceType": "cn.com.handthing.starter.dataauth.datalayer.config.DataAuthDatalayerProperties$Cache"}, {"name": "handthing.datalayer.dataauth.cache.type", "type": "java.lang.String", "description": "缓存类型", "sourceType": "cn.com.handthing.starter.dataauth.datalayer.config.DataAuthDatalayerProperties$Cache"}, {"name": "handthing.datalayer.dataauth.default-failure-strategy", "type": "java.lang.String", "description": "默认权限失败策略", "sourceType": "cn.com.handthing.starter.dataauth.datalayer.config.DataAuthDatalayerProperties"}, {"name": "handthing.datalayer.dataauth.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用数据权限功能", "sourceType": "cn.com.handthing.starter.dataauth.datalayer.config.DataAuthDatalayerProperties"}, {"name": "handthing.datalayer.dataauth.ignore-classes", "type": "java.util.Set<java.lang.String>", "description": "忽略权限检查的类名", "sourceType": "cn.com.handthing.starter.dataauth.datalayer.config.DataAuthDatalayerProperties"}, {"name": "handthing.datalayer.dataauth.ignore-method-patterns", "type": "java.util.Set<java.lang.String>", "description": "忽略权限检查的方法名模式", "sourceType": "cn.com.handthing.starter.dataauth.datalayer.config.DataAuthDatalayerProperties"}, {"name": "handthing.datalayer.dataauth.ignore-packages", "type": "java.util.Set<java.lang.String>", "description": "忽略权限检查的包路径", "sourceType": "cn.com.handthing.starter.dataauth.datalayer.config.DataAuthDatalayerProperties"}, {"name": "handthing.datalayer.dataauth.provider.dept.default-field", "type": "java.lang.String", "description": "默认部门字段名", "sourceType": "cn.com.handthing.starter.dataauth.datalayer.config.DataAuthDatalayerProperties$Provider$Dept"}, {"name": "handthing.datalayer.dataauth.provider.dept.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用部门权限", "sourceType": "cn.com.handthing.starter.dataauth.datalayer.config.DataAuthDatalayerProperties$Provider$Dept"}, {"name": "handthing.datalayer.dataauth.provider.dept.hierarchy-enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否支持部门层级", "sourceType": "cn.com.handthing.starter.dataauth.datalayer.config.DataAuthDatalayerProperties$Provider$Dept"}, {"name": "handthing.datalayer.dataauth.provider.dept.hierarchy-query", "type": "java.lang.String", "description": "部门层级查询SQL", "sourceType": "cn.com.handthing.starter.dataauth.datalayer.config.DataAuthDatalayerProperties$Provider$Dept"}, {"name": "handthing.datalayer.dataauth.provider.role.default-field", "type": "java.lang.String", "description": "默认角色字段名", "sourceType": "cn.com.handthing.starter.dataauth.datalayer.config.DataAuthDatalayerProperties$Provider$Role"}, {"name": "handthing.datalayer.dataauth.provider.role.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用角色权限", "sourceType": "cn.com.handthing.starter.dataauth.datalayer.config.DataAuthDatalayerProperties$Provider$Role"}, {"name": "handthing.datalayer.dataauth.provider.role.permission-query", "type": "java.lang.String", "description": "角色权限查询SQL", "sourceType": "cn.com.handthing.starter.dataauth.datalayer.config.DataAuthDatalayerProperties$Provider$Role"}, {"name": "handthing.datalayer.dataauth.provider.user.default-field", "type": "java.lang.String", "description": "默认用户字段名", "sourceType": "cn.com.handthing.starter.dataauth.datalayer.config.DataAuthDatalayerProperties$Provider$User"}, {"name": "handthing.datalayer.dataauth.provider.user.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用用户权限", "sourceType": "cn.com.handthing.starter.dataauth.datalayer.config.DataAuthDatalayerProperties$Provider$User"}, {"name": "handthing.datalayer.dataauth.provider.user.user-id-field", "type": "java.lang.String", "description": "用户ID字段名", "sourceType": "cn.com.handthing.starter.dataauth.datalayer.config.DataAuthDatalayerProperties$Provider$User"}, {"name": "handthing.datalayer.dataauth.strict-mode", "type": "java.lang.Bo<PERSON>an", "description": "是否启用严格模式 <p> 严格模式下，权限相关的错误会抛出异常而不是记录日志 </p>", "sourceType": "cn.com.handthing.starter.dataauth.datalayer.config.DataAuthDatalayerProperties"}, {"name": "handthing.datalayer.dataauth.super-admin.check-strategy", "type": "java.lang.String", "description": "超级管理员检查策略", "sourceType": "cn.com.handthing.starter.dataauth.datalayer.config.DataAuthDatalayerProperties$SuperAdmin"}, {"name": "handthing.datalayer.dataauth.super-admin.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用超级管理员跳过权限检查", "sourceType": "cn.com.handthing.starter.dataauth.datalayer.config.DataAuthDatalayerProperties$SuperAdmin"}, {"name": "handthing.datalayer.dataauth.super-admin.roles", "type": "java.util.Set<java.lang.String>", "description": "超级管理员角色名称", "sourceType": "cn.com.handthing.starter.dataauth.datalayer.config.DataAuthDatalayerProperties$SuperAdmin"}, {"name": "handthing.datalayer.dataauth.super-admin.user-ids", "type": "java.util.Set<java.lang.String>", "description": "超级管理员用户ID", "sourceType": "cn.com.handthing.starter.dataauth.datalayer.config.DataAuthDatalayerProperties$SuperAdmin"}], "hints": [], "ignored": {"properties": []}}