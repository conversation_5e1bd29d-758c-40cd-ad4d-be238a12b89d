package cn.com.handthing.core.util;

import java.util.stream.Stream;

/**
 * 字符串工具类
 * 继承自 Apache Commons Lang3，并可按需扩展自定义方法。
 *
 * <AUTHOR>
 * @since V1.0.0
 */
public final class StringUtils {

    private StringUtils() {}

    /**
     * 检查字符序列是否为空白。
     * 空白定义：null, "", " ", "\t", "\n", "\r"
     * @param cs 待检查的字符序列
     * @return 如果为空白则返回 true
     */
    public static boolean isBlank(final CharSequence cs) {
        if (cs == null || cs.isEmpty()) {
            return true;
        }
        for (int i = 0; i < cs.length(); i++) {
            if (!Character.isWhitespace(cs.charAt(i))) {
                return false;
            }
        }
        return true;
    }

    /**
     * 检查字符序列是否不为空白。
     * @param cs 待检查的字符序列
     * @return 如果不为空白则返回 true
     */
    public static boolean isNotBlank(final CharSequence cs) {
        return !isBlank(cs);
    }
    
    /**
     * 检查字符序列是否为空。
     * 空定义：null 或 length() == 0
     * @param cs 待检查的字符序列
     * @return 如果为空则返回 true
     */
    public static boolean isEmpty(final CharSequence cs) {
        return cs == null || cs.isEmpty();
    }

    /**
     * 检查字符序列是否不为空。
     * @param cs 待检查的字符序列
     * @return 如果不为空则返回 true
     */
    public static boolean isNotEmpty(final CharSequence cs) {
        return !isEmpty(cs);
    }
    
    /**
     * 检查任意一个字符序列是否为空白。
     * @param css 待检查的字符序列数组
     * @return 如果有任意一个为空白则返回 true
     */
    public static boolean isAnyBlank(final CharSequence... css) {
        if (css == null || 0 == css.length) {
            return true;
        }
        return Stream.of(css).anyMatch(StringUtils::isBlank);
    }
    
    /**
     * 如果字符串为空白，则返回默认值。
     * @param str 待检查的字符串
     * @param defaultStr 默认字符串
     * @return 原始字符串或默认字符串
     */
    public static String defaultIfBlank(final String str, final String defaultStr) {
        return isBlank(str) ? defaultStr : str;
    }
}