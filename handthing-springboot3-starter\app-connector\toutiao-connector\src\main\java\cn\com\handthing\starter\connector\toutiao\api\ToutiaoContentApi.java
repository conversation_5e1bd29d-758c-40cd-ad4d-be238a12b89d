package cn.com.handthing.starter.connector.toutiao.api;

import cn.com.handthing.starter.connector.toutiao.config.ToutiaoConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 今日头条内容API
 * <p>
 * 提供今日头条内容发布相关的API功能，包括文章发布、视频上传、图片管理等。
 * 支持个人创作者和企业用户的内容创作功能。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "handthing.connector.toutiao", name = "enabled", havingValue = "true")
public class ToutiaoContentApi {

    private final ToutiaoConfig config;
    private final RestTemplate restTemplate;

    /**
     * 发布文章
     *
     * @param accessToken 访问令牌
     * @param title       文章标题
     * @param content     文章内容
     * @param coverImage  封面图片URL
     * @param tags        标签列表
     * @return 发布结果
     */
    public Map<String, Object> publishArticle(String accessToken, String title, String content, 
                                              String coverImage, List<String> tags) {
        try {
            log.debug("Publishing Toutiao article: {}", title);

            String url = config.getEffectiveApiBaseUrl() + "/api/v2/article/create/";

            Map<String, Object> article = new HashMap<>();
            article.put("title", title);
            article.put("content", content);
            article.put("cover_image", coverImage);
            article.put("tags", tags);
            article.put("access_token", accessToken);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(article, headers);

            Map<String, Object> response = restTemplate.postForObject(url, request, Map.class);
            log.debug("Toutiao article published, response: {}", response);

            return response;

        } catch (Exception e) {
            log.error("Failed to publish Toutiao article", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error_code", -1);
            errorResponse.put("description", "Failed to publish article: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 上传图片
     *
     * @param accessToken 访问令牌
     * @param imageData   图片数据（Base64编码）
     * @param imageName   图片名称
     * @return 上传结果
     */
    public Map<String, Object> uploadImage(String accessToken, String imageData, String imageName) {
        try {
            log.debug("Uploading Toutiao image: {}", imageName);

            String url = config.getEffectiveApiBaseUrl() + "/api/v2/image/upload/";

            Map<String, Object> uploadData = new HashMap<>();
            uploadData.put("image", imageData);
            uploadData.put("image_name", imageName);
            uploadData.put("access_token", accessToken);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(uploadData, headers);

            Map<String, Object> response = restTemplate.postForObject(url, request, Map.class);
            log.debug("Toutiao image uploaded, response: {}", response);

            return response;

        } catch (Exception e) {
            log.error("Failed to upload Toutiao image", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error_code", -1);
            errorResponse.put("description", "Failed to upload image: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 发布视频
     *
     * @param accessToken 访问令牌
     * @param title       视频标题
     * @param videoUrl    视频URL
     * @param coverImage  封面图片URL
     * @param description 视频描述
     * @param tags        标签列表
     * @return 发布结果
     */
    public Map<String, Object> publishVideo(String accessToken, String title, String videoUrl, 
                                            String coverImage, String description, List<String> tags) {
        try {
            log.debug("Publishing Toutiao video: {}", title);

            String url = config.getEffectiveApiBaseUrl() + "/api/v2/video/create/";

            Map<String, Object> video = new HashMap<>();
            video.put("title", title);
            video.put("video_url", videoUrl);
            video.put("cover_image", coverImage);
            video.put("description", description);
            video.put("tags", tags);
            video.put("access_token", accessToken);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(video, headers);

            Map<String, Object> response = restTemplate.postForObject(url, request, Map.class);
            log.debug("Toutiao video published, response: {}", response);

            return response;

        } catch (Exception e) {
            log.error("Failed to publish Toutiao video", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error_code", -1);
            errorResponse.put("description", "Failed to publish video: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 获取内容列表
     *
     * @param accessToken 访问令牌
     * @param page        页码
     * @param pageSize    每页大小
     * @param contentType 内容类型（article, video, image）
     * @return 内容列表
     */
    public Map<String, Object> getContentList(String accessToken, Integer page, Integer pageSize, String contentType) {
        try {
            log.debug("Getting Toutiao content list, type: {}, page: {}", contentType, page);

            String url = config.getEffectiveApiBaseUrl() + "/api/v2/content/list/";

            Map<String, Object> params = new HashMap<>();
            params.put("access_token", accessToken);
            params.put("page", page != null ? page : 1);
            params.put("page_size", pageSize != null ? pageSize : 20);
            if (contentType != null && !contentType.trim().isEmpty()) {
                params.put("content_type", contentType);
            }

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(params, headers);

            Map<String, Object> response = restTemplate.postForObject(url, request, Map.class);
            log.debug("Toutiao content list retrieved, response: {}", response);

            return response;

        } catch (Exception e) {
            log.error("Failed to get Toutiao content list", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error_code", -1);
            errorResponse.put("description", "Failed to get content list: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 删除内容
     *
     * @param accessToken 访问令牌
     * @param contentId   内容ID
     * @return 删除结果
     */
    public Map<String, Object> deleteContent(String accessToken, String contentId) {
        try {
            log.debug("Deleting Toutiao content: {}", contentId);

            String url = config.getEffectiveApiBaseUrl() + "/api/v2/content/delete/";

            Map<String, Object> deleteData = new HashMap<>();
            deleteData.put("access_token", accessToken);
            deleteData.put("content_id", contentId);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(deleteData, headers);

            Map<String, Object> response = restTemplate.postForObject(url, request, Map.class);
            log.debug("Toutiao content deleted, response: {}", response);

            return response;

        } catch (Exception e) {
            log.error("Failed to delete Toutiao content", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error_code", -1);
            errorResponse.put("description", "Failed to delete content: " + e.getMessage());
            return errorResponse;
        }
    }
}
