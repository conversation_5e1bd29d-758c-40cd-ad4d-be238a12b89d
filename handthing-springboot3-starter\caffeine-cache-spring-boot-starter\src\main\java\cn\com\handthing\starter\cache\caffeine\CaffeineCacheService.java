package cn.com.handthing.starter.cache.caffeine;

import cn.com.handthing.starter.cache.CacheService;
import cn.com.handthing.starter.cache.CacheStats;
import cn.com.handthing.starter.cache.exception.CacheOperationException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.cache.caffeine.CaffeineCache;

import java.time.Duration;
import java.util.concurrent.Callable;

/**
 * Caffeine缓存服务实现
 * <p>
 * 基于Caffeine的高性能缓存服务，支持统计信息、异步加载等高级特性
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@RequiredArgsConstructor
public class CaffeineCacheService implements CacheService {

    private final CaffeineCacheManager cacheManager;

    @Override
    public <T> T get(String cacheName, String key) {
        try {
            Cache cache = getCache(cacheName);
            Cache.ValueWrapper wrapper = cache.get(key);
            return wrapper != null ? (T) wrapper.get() : null;
        } catch (Exception e) {
            log.error("Failed to get cache value for cacheName: {}, key: {}", cacheName, key, e);
            throw new CacheOperationException("Failed to get cache value", e);
        }
    }

    @Override
    public <T> T get(String cacheName, String key, Class<T> type) {
        try {
            Cache cache = getCache(cacheName);
            return cache.get(key, type);
        } catch (Exception e) {
            log.error("Failed to get cache value for cacheName: {}, key: {}, type: {}", 
                    cacheName, key, type.getName(), e);
            throw new CacheOperationException("Failed to get cache value", e);
        }
    }

    @Override
    public void put(String cacheName, String key, Object value) {
        try {
            Cache cache = getCache(cacheName);
            cache.put(key, value);
            log.debug("Put cache value for cacheName: {}, key: {}", cacheName, key);
        } catch (Exception e) {
            log.error("Failed to put cache value for cacheName: {}, key: {}", cacheName, key, e);
            throw new CacheOperationException("Failed to put cache value", e);
        }
    }

    @Override
    public void put(String cacheName, String key, Object value, Duration ttl) {
        // Caffeine不直接支持单个条目的TTL，使用普通put方法
        // TTL通过配置中的expireAfterWrite或expireAfterAccess控制
        log.debug("Caffeine does not support per-entry TTL, using cache-level expiration policy");
        put(cacheName, key, value);
    }

    @Override
    public void evict(String cacheName, String key) {
        try {
            Cache cache = getCache(cacheName);
            cache.evict(key);
            log.debug("Evicted cache value for cacheName: {}, key: {}", cacheName, key);
        } catch (Exception e) {
            log.error("Failed to evict cache value for cacheName: {}, key: {}", cacheName, key, e);
            throw new CacheOperationException("Failed to evict cache value", e);
        }
    }

    @Override
    public void clear(String cacheName) {
        try {
            Cache cache = getCache(cacheName);
            cache.clear();
            log.debug("Cleared cache for cacheName: {}", cacheName);
        } catch (Exception e) {
            log.error("Failed to clear cache for cacheName: {}", cacheName, e);
            throw new CacheOperationException("Failed to clear cache", e);
        }
    }

    @Override
    public boolean exists(String cacheName, String key) {
        try {
            Cache cache = getCache(cacheName);
            return cache.get(key) != null;
        } catch (Exception e) {
            log.error("Failed to check cache existence for cacheName: {}, key: {}", cacheName, key, e);
            return false;
        }
    }

    @Override
    public CacheStats getStats(String cacheName) {
        try {
            return cacheManager.getCacheStats(cacheName);
        } catch (Exception e) {
            log.error("Failed to get cache stats for cacheName: {}", cacheName, e);
            return null;
        }
    }

    /**
     * 使用Callable加载缓存值（支持Caffeine的异步加载特性）
     *
     * @param cacheName   缓存名称
     * @param key         缓存键
     * @param valueLoader 值加载器
     * @param <T>         值类型
     * @return 缓存值
     */
    public <T> T get(String cacheName, String key, Callable<T> valueLoader) {
        try {
            Cache cache = getCache(cacheName);
            return cache.get(key, valueLoader);
        } catch (Exception e) {
            log.error("Failed to get cache value with loader for cacheName: {}, key: {}", cacheName, key, e);
            throw new CacheOperationException("Failed to get cache value with loader", e);
        }
    }

    /**
     * 如果不存在则放入缓存
     *
     * @param cacheName 缓存名称
     * @param key       缓存键
     * @param value     缓存值
     * @return 如果已存在则返回现有值，否则返回null
     */
    public <T> T putIfAbsent(String cacheName, String key, Object value) {
        try {
            Cache cache = getCache(cacheName);
            Cache.ValueWrapper existing = cache.putIfAbsent(key, value);
            return existing != null ? (T) existing.get() : null;
        } catch (Exception e) {
            log.error("Failed to putIfAbsent cache value for cacheName: {}, key: {}", cacheName, key, e);
            throw new CacheOperationException("Failed to putIfAbsent cache value", e);
        }
    }

    /**
     * 获取缓存大小估计值
     *
     * @param cacheName 缓存名称
     * @return 缓存大小估计值
     */
    public long estimatedSize(String cacheName) {
        try {
            Cache cache = getCache(cacheName);
            if (cache instanceof CaffeineCache) {
                CaffeineCache caffeineCache = (CaffeineCache) cache;
                com.github.benmanes.caffeine.cache.Cache<Object, Object> nativeCache = 
                        (com.github.benmanes.caffeine.cache.Cache<Object, Object>) caffeineCache.getNativeCache();
                return nativeCache.estimatedSize();
            }
            return 0;
        } catch (Exception e) {
            log.error("Failed to get estimated size for cacheName: {}", cacheName, e);
            return 0;
        }
    }

    /**
     * 执行缓存维护操作
     *
     * @param cacheName 缓存名称
     */
    public void cleanUp(String cacheName) {
        try {
            Cache cache = getCache(cacheName);
            if (cache instanceof CaffeineCache) {
                CaffeineCache caffeineCache = (CaffeineCache) cache;
                com.github.benmanes.caffeine.cache.Cache<Object, Object> nativeCache = 
                        (com.github.benmanes.caffeine.cache.Cache<Object, Object>) caffeineCache.getNativeCache();
                nativeCache.cleanUp();
                log.debug("Performed cleanup on cache: {}", cacheName);
            }
        } catch (Exception e) {
            log.error("Failed to cleanup cache for cacheName: {}", cacheName, e);
        }
    }

    /**
     * 获取缓存实例
     *
     * @param cacheName 缓存名称
     * @return 缓存实例
     * @throws CacheOperationException 如果缓存不存在
     */
    private Cache getCache(String cacheName) {
        Cache cache = cacheManager.getCache(cacheName);
        if (cache == null) {
            throw new CacheOperationException("Cache not found: " + cacheName);
        }
        return cache;
    }
}