package cn.com.handthing.starter.dataauth.datalayer.provider.impl;

import cn.com.handthing.starter.dataauth.datalayer.annotation.DataPermission;
import cn.com.handthing.starter.dataauth.datalayer.context.DataPermissionContext;
import cn.com.handthing.starter.dataauth.datalayer.provider.DataPermissionProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 用户权限提供者
 * <p>
 * 实现基于用户的数据权限控制。用户只能访问自己创建或拥有的数据。
 * 通常用于个人数据保护场景。
 * </p>
 * 
 * <h3>支持的字段：</h3>
 * <ul>
 *   <li>create_by - 创建人字段（默认）</li>
 *   <li>user_id - 用户ID字段</li>
 *   <li>owner_id - 拥有者ID字段</li>
 *   <li>自定义字段 - 通过field属性指定</li>
 * </ul>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Component
public class UserPermissionProvider implements DataPermissionProvider {
    
    /**
     * 默认用户字段名
     */
    private static final String DEFAULT_USER_FIELD = "create_by";
    
    @Override
    public boolean supports(DataPermission.DataPermissionType type) {
        return DataPermission.DataPermissionType.USER == type;
    }
    
    @Override
    public String generatePermissionCondition(DataPermission permission, String mappedStatementId) {
        try {
            // 获取当前用户信息
            DataPermissionContext.UserInfo userInfo = getCurrentUserInfo();
            if (userInfo == null) {
                log.warn("No user info found for user permission check");
                return "1 = 0"; // 无用户信息时拒绝访问
            }
            
            String userId = userInfo.getUserId();
            if (userId == null || userId.trim().isEmpty()) {
                log.warn("User ID is empty for user permission check");
                return "1 = 0"; // 无用户ID时拒绝访问
            }
            
            // 获取用户字段名
            String userField = getUserField(permission);
            
            // 根据权限范围生成条件
            return generateConditionByScope(permission, userField, userInfo);
            
        } catch (Exception e) {
            log.error("Failed to generate user permission condition", e);
            return "1 = 0"; // 出错时拒绝访问
        }
    }
    
    /**
     * 根据权限范围生成条件
     * 
     * @param permission 权限配置
     * @param userField  用户字段名
     * @param userInfo   用户信息
     * @return SQL条件
     */
    protected String generateConditionByScope(DataPermission permission, String userField, 
                                            DataPermissionContext.UserInfo userInfo) {
        
        DataPermission.PermissionScope scope = permission.scope();
        String userId = userInfo.getUserId();
        
        switch (scope) {
            case CURRENT:
                return generateCurrentUserCondition(userField, userId, permission.alias());
                
            case CURRENT_AND_CHILDREN:
                // 对于用户权限，CURRENT_AND_CHILDREN 等同于 CURRENT
                return generateCurrentUserCondition(userField, userId, permission.alias());
                
            case ALL:
                return null; // 无限制
                
            case CUSTOM:
                return generateCustomUserCondition(permission, userInfo);
                
            default:
                log.warn("Unsupported permission scope: {}", scope);
                return generateCurrentUserCondition(userField, userId, permission.alias());
        }
    }
    
    /**
     * 生成当前用户条件
     * 
     * @param userField 用户字段名
     * @param userId    用户ID
     * @param alias     表别名
     * @return SQL条件
     */
    protected String generateCurrentUserCondition(String userField, String userId, String alias) {
        String fullFieldName = buildFullFieldName(userField, alias);
        return String.format("%s = '%s'", fullFieldName, escapeSqlValue(userId));
    }
    
    /**
     * 生成自定义用户条件
     * 
     * @param permission 权限配置
     * @param userInfo   用户信息
     * @return SQL条件
     */
    protected String generateCustomUserCondition(DataPermission permission, 
                                               DataPermissionContext.UserInfo userInfo) {
        // TODO: 实现自定义用户权限逻辑
        // 可以根据用户的特殊权限配置生成条件
        // 例如：用户可以访问自己创建的数据以及分配给自己的数据
        
        String expression = permission.expression();
        if (expression != null && !expression.trim().isEmpty()) {
            // 替换表达式中的占位符
            return processCustomExpression(expression, userInfo);
        }
        
        log.debug("Custom user permission not implemented, falling back to current user");
        return generateCurrentUserCondition(getUserField(permission), userInfo.getUserId(), permission.alias());
    }
    
    /**
     * 处理自定义表达式
     * 
     * @param expression 自定义表达式
     * @param userInfo   用户信息
     * @return 处理后的SQL条件
     */
    protected String processCustomExpression(String expression, DataPermissionContext.UserInfo userInfo) {
        // 简单的占位符替换
        String result = expression;
        
        if (userInfo.getUserId() != null) {
            result = result.replace("#{userId}", "'" + escapeSqlValue(userInfo.getUserId()) + "'");
            result = result.replace("#{user.id}", "'" + escapeSqlValue(userInfo.getUserId()) + "'");
        }
        
        if (userInfo.getUsername() != null) {
            result = result.replace("#{username}", "'" + escapeSqlValue(userInfo.getUsername()) + "'");
            result = result.replace("#{user.name}", "'" + escapeSqlValue(userInfo.getUsername()) + "'");
        }
        
        if (userInfo.getDeptId() != null) {
            result = result.replace("#{deptId}", "'" + escapeSqlValue(userInfo.getDeptId()) + "'");
            result = result.replace("#{user.deptId}", "'" + escapeSqlValue(userInfo.getDeptId()) + "'");
        }
        
        return result;
    }
    
    /**
     * 获取用户字段名
     * 
     * @param permission 权限配置
     * @return 用户字段名
     */
    protected String getUserField(DataPermission permission) {
        String field = permission.field();
        return (field == null || field.trim().isEmpty()) ? DEFAULT_USER_FIELD : field.trim();
    }
    
    /**
     * 构建完整字段名（包含表别名）
     * 
     * @param fieldName 字段名
     * @param alias     表别名
     * @return 完整字段名
     */
    protected String buildFullFieldName(String fieldName, String alias) {
        if (alias == null || alias.trim().isEmpty()) {
            return fieldName;
        }
        return alias.trim() + "." + fieldName;
    }
    
    /**
     * 转义SQL值
     * 
     * @param value 原始值
     * @return 转义后的值
     */
    protected String escapeSqlValue(String value) {
        if (value == null) {
            return "";
        }
        // 简单的SQL注入防护，实际项目中应该使用更完善的转义方法
        return value.replace("'", "''");
    }
    
    /**
     * 获取当前用户信息
     * 
     * @return 用户信息
     */
    protected DataPermissionContext.UserInfo getCurrentUserInfo() {
        DataPermissionContext context = DataPermissionContext.getCurrentContext();
        if (context != null) {
            return context.getUserInfo();
        }
        
        // TODO: 从AuthContextHolder或其他用户上下文获取用户信息
        return null;
    }
    
    @Override
    public String validatePermissionConfig(DataPermission permission) {
        StringBuilder errors = new StringBuilder();
        
        // 验证用户字段配置
        String userField = getUserField(permission);
        if (userField.isEmpty()) {
            errors.append("User field cannot be empty; ");
        }
        
        // 验证自定义表达式
        if (permission.scope() == DataPermission.PermissionScope.CUSTOM) {
            if (permission.expression() == null || permission.expression().trim().isEmpty()) {
                errors.append("Custom scope requires expression; ");
            }
        }
        
        return errors.toString();
    }
    
    @Override
    public boolean isAvailable() {
        // 检查是否有用户上下文
        DataPermissionContext context = DataPermissionContext.getCurrentContext();
        if (context == null) {
            return false;
        }
        
        DataPermissionContext.UserInfo userInfo = context.getUserInfo();
        return userInfo != null && userInfo.getUserId() != null;
    }
    
    @Override
    public String getDescription() {
        return "User-based data permission provider that controls data access based on data ownership";
    }
    
    @Override
    public int getOrder() {
        return 200; // 用户权限的优先级
    }
}
