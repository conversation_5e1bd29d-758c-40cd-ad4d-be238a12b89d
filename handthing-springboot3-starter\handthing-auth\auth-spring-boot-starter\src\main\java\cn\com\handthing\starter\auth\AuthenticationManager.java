package cn.com.handthing.starter.auth;

import cn.com.handthing.starter.auth.core.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * 认证管理器
 * <p>
 * 负责协调和管理多个认证提供者，实现"认证总线"的核心功能。
 * 根据请求的授权类型选择合适的认证提供者进行认证处理。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AuthenticationManager {

    private final List<AuthenticationProvider> authenticationProviders;
    private final AuthProperties authProperties;
    private final AuthenticationBus authenticationBus;

    /**
     * 执行认证
     *
     * @param request 认证请求
     * @return 认证响应
     * @throws AuthenticationException 认证异常
     */
    public AuthenticationResponse authenticate(AuthenticationRequest request) throws AuthenticationException {
        log.debug("Starting authentication for grant type: {}", request.getGrantType());

        // 创建认证上下文
        AuthenticationContext context = new AuthenticationContext(request);

        try {
            // 预处理
            preAuthenticate(context);

            // 查找合适的认证提供者
            AuthenticationProvider provider = findAuthenticationProvider(request);
            if (provider == null) {
                throw new AuthenticationException("UNSUPPORTED_GRANT_TYPE", 
                        "Unsupported grant type: " + request.getGrantType());
            }

            context.setProviderName(provider.getProviderName());
            log.debug("Using authentication provider: {}", provider.getProviderName());

            // 执行认证
            AuthenticationResponse response = provider.authenticate(request);

            // 后处理
            postAuthenticate(context, response);

            context.markSuccess(response, null);
            log.info("Authentication successful for user: {}, provider: {}, duration: {}ms",
                    response.getUserId(), provider.getProviderName(), context.getDuration());

            return response;

        } catch (AuthenticationException e) {
            context.markFailure(e.getMessage(), e);
            log.warn("Authentication failed: {}, duration: {}ms", e.getMessage(), context.getDuration());
            
            // 发布认证失败事件
            authenticationBus.publishAuthenticationFailedEvent(context);
            
            throw e;
        } catch (Exception e) {
            context.markFailure("Authentication error", e);
            log.error("Authentication error: {}, duration: {}ms", e.getMessage(), context.getDuration(), e);
            
            // 发布认证错误事件
            authenticationBus.publishAuthenticationErrorEvent(context);
            
            throw new AuthenticationException("AUTHENTICATION_ERROR", "Authentication failed", e);
        } finally {
            // 发布认证完成事件
            authenticationBus.publishAuthenticationCompletedEvent(context);
        }
    }

    /**
     * 验证令牌
     *
     * @param token 访问令牌
     * @return 如果令牌有效返回true，否则返回false
     */
    public boolean validateToken(String token) {
        if (token == null || token.trim().isEmpty()) {
            return false;
        }

        try {
            // 尝试使用所有提供者验证令牌
            for (AuthenticationProvider provider : authenticationProviders) {
                if (provider.validateToken(token)) {
                    log.debug("Token validated by provider: {}", provider.getProviderName());
                    return true;
                }
            }

            log.debug("Token validation failed for all providers");
            return false;

        } catch (Exception e) {
            log.debug("Token validation error: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 刷新令牌
     *
     * @param refreshToken 刷新令牌
     * @return 认证响应
     * @throws AuthenticationException 认证异常
     */
    public AuthenticationResponse refreshToken(String refreshToken) throws AuthenticationException {
        log.debug("Starting token refresh");

        if (refreshToken == null || refreshToken.trim().isEmpty()) {
            throw new AuthenticationException("INVALID_REFRESH_TOKEN", "Refresh token is empty");
        }

        // 尝试使用所有提供者刷新令牌
        for (AuthenticationProvider provider : authenticationProviders) {
            try {
                AuthenticationResponse response = provider.refreshToken(refreshToken);
                if (response != null && response.isSuccess()) {
                    log.info("Token refreshed successfully by provider: {}", provider.getProviderName());
                    return response;
                }
            } catch (Exception e) {
                log.debug("Token refresh failed for provider {}: {}", provider.getProviderName(), e.getMessage());
            }
        }

        throw new AuthenticationException("REFRESH_TOKEN_FAILED", "Failed to refresh token");
    }

    /**
     * 查找认证提供者
     *
     * @param request 认证请求
     * @return 认证提供者，如果未找到返回null
     */
    private AuthenticationProvider findAuthenticationProvider(AuthenticationRequest request) {
        // 首先查找支持该请求的提供者
        List<AuthenticationProvider> supportedProviders = authenticationProviders.stream()
                .filter(provider -> provider.supports(request))
                .filter(AuthenticationProvider::isAvailable)
                .sorted((p1, p2) -> Integer.compare(p1.getPriority(), p2.getPriority()))
                .toList();

        if (supportedProviders.isEmpty()) {
            return null;
        }

        // 如果配置了提供者优先级，按优先级选择
        if (authProperties.getProviderPriority() != null && !authProperties.getProviderPriority().isEmpty()) {
            for (String providerName : authProperties.getProviderPriority()) {
                Optional<AuthenticationProvider> provider = supportedProviders.stream()
                        .filter(p -> providerName.equals(p.getProviderName()))
                        .findFirst();
                if (provider.isPresent()) {
                    return provider.get();
                }
            }
        }

        // 返回第一个支持的提供者
        return supportedProviders.get(0);
    }

    /**
     * 预处理认证请求
     *
     * @param context 认证上下文
     * @throws AuthenticationException 认证异常
     */
    private void preAuthenticate(AuthenticationContext context) throws AuthenticationException {
        AuthenticationRequest request = context.getRequest();

        // 验证请求参数
        if (!request.isValid()) {
            throw new AuthenticationException("INVALID_REQUEST", "Invalid authentication request");
        }

        // IP白名单检查
        if (authProperties.isIpWhitelistEnabled()) {
            String ipAddress = request.getIpAddress();
            if (!authProperties.isIpInWhitelist(ipAddress)) {
                throw new AuthenticationException("IP_NOT_ALLOWED", "IP address not in whitelist: " + ipAddress);
            }
        }

        // IP黑名单检查
        if (authProperties.isIpBlacklistEnabled()) {
            String ipAddress = request.getIpAddress();
            if (authProperties.isIpInBlacklist(ipAddress)) {
                throw new AuthenticationException("IP_BLOCKED", "IP address is blocked: " + ipAddress);
            }
        }

        // 发布认证开始事件
        authenticationBus.publishAuthenticationStartedEvent(context);
    }

    /**
     * 后处理认证响应
     *
     * @param context  认证上下文
     * @param response 认证响应
     * @throws AuthenticationException 认证异常
     */
    private void postAuthenticate(AuthenticationContext context, AuthenticationResponse response) throws AuthenticationException {
        if (response == null) {
            throw new AuthenticationException("NULL_RESPONSE", "Authentication response is null");
        }

        if (!response.isSuccess()) {
            throw new AuthenticationException(response.getErrorCode(), response.getErrorDescription());
        }

        // 发布认证成功事件
        authenticationBus.publishAuthenticationSuccessEvent(context);
    }

    /**
     * 获取支持的授权类型列表
     *
     * @return 授权类型列表
     */
    public List<GrantType> getSupportedGrantTypes() {
        return authenticationProviders.stream()
                .filter(AuthenticationProvider::isAvailable)
                .map(AuthenticationProvider::getSupportedGrantType)
                .distinct()
                .toList();
    }

    /**
     * 获取可用的认证提供者列表
     *
     * @return 认证提供者列表
     */
    public List<AuthenticationProvider> getAvailableProviders() {
        return authenticationProviders.stream()
                .filter(AuthenticationProvider::isAvailable)
                .toList();
    }

    /**
     * 判断是否支持指定的授权类型
     *
     * @param grantType 授权类型
     * @return 如果支持返回true，否则返回false
     */
    public boolean supportsGrantType(GrantType grantType) {
        return authenticationProviders.stream()
                .anyMatch(provider -> provider.getSupportedGrantType() == grantType && provider.isAvailable());
    }
}
