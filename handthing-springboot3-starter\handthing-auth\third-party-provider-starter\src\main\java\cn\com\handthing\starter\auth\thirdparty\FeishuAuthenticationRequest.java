package cn.com.handthing.starter.auth.thirdparty;

import cn.com.handthing.starter.auth.core.AuthenticationRequest;
import cn.com.handthing.starter.auth.core.GrantType;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 飞书认证请求
 * <p>
 * 用于飞书OAuth2认证的请求对象，包含授权码、应用信息等认证信息。
 * 支持飞书网页授权、移动端授权和小程序授权。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FeishuAuthenticationRequest extends AuthenticationRequest {

    /**
     * 飞书授权码
     */
    private String code;

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 应用密钥
     */
    private String appSecret;

    /**
     * 重定向URI
     */
    private String redirectUri;

    /**
     * 状态参数
     */
    private String state;

    /**
     * 授权类型（web-网页授权，mobile-移动端授权，miniprogram-小程序授权）
     */
    private String authType = "web";

    /**
     * 是否自动注册（如果用户不存在）
     */
    private Boolean autoRegister = true;

    /**
     * 语言设置
     */
    private String locale = "zh_cn";

    /**
     * 默认构造函数
     */
    public FeishuAuthenticationRequest() {
        super(GrantType.FEISHU);
    }

    /**
     * 构造函数
     *
     * @param code  授权码
     * @param appId 应用ID
     */
    public FeishuAuthenticationRequest(String code, String appId) {
        super(GrantType.FEISHU);
        this.code = code;
        this.appId = appId;
    }

    /**
     * 构造函数
     *
     * @param code      授权码
     * @param appId     应用ID
     * @param appSecret 应用密钥
     */
    public FeishuAuthenticationRequest(String code, String appId, String appSecret) {
        super(GrantType.FEISHU);
        this.code = code;
        this.appId = appId;
        this.appSecret = appSecret;
    }

    @Override
    public String getAuthenticationIdentifier() {
        return appId;
    }

    @Override
    public Object getCredentials() {
        return code;
    }

    @Override
    public boolean isValid() {
        return super.isValid() && 
               code != null && !code.trim().isEmpty() &&
               appId != null && !appId.trim().isEmpty();
    }

    /**
     * 判断是否为网页授权类型
     *
     * @return 如果是网页授权返回true，否则返回false
     */
    public boolean isWebAuth() {
        return "web".equals(authType);
    }

    /**
     * 判断是否为移动端授权类型
     *
     * @return 如果是移动端授权返回true，否则返回false
     */
    public boolean isMobileAuth() {
        return "mobile".equals(authType);
    }

    /**
     * 判断是否为小程序授权类型
     *
     * @return 如果是小程序授权返回true，否则返回false
     */
    public boolean isMiniProgramAuth() {
        return "miniprogram".equals(authType);
    }

    /**
     * 创建飞书认证请求
     *
     * @param code  授权码
     * @param appId 应用ID
     * @return 飞书认证请求
     */
    public static FeishuAuthenticationRequest of(String code, String appId) {
        return new FeishuAuthenticationRequest(code, appId);
    }

    /**
     * 创建网页授权的飞书认证请求
     *
     * @param code        授权码
     * @param appId       应用ID
     * @param appSecret   应用密钥
     * @param redirectUri 重定向URI
     * @return 飞书认证请求
     */
    public static FeishuAuthenticationRequest forWebAuth(String code, String appId, 
                                                        String appSecret, String redirectUri) {
        FeishuAuthenticationRequest request = new FeishuAuthenticationRequest(code, appId, appSecret);
        request.setAuthType("web");
        request.setRedirectUri(redirectUri);
        return request;
    }

    /**
     * 创建移动端授权的飞书认证请求
     *
     * @param code      授权码
     * @param appId     应用ID
     * @param appSecret 应用密钥
     * @return 飞书认证请求
     */
    public static FeishuAuthenticationRequest forMobileAuth(String code, String appId, String appSecret) {
        FeishuAuthenticationRequest request = new FeishuAuthenticationRequest(code, appId, appSecret);
        request.setAuthType("mobile");
        return request;
    }

    /**
     * 创建小程序授权的飞书认证请求
     *
     * @param code      授权码
     * @param appId     应用ID
     * @param appSecret 应用密钥
     * @return 飞书认证请求
     */
    public static FeishuAuthenticationRequest forMiniProgramAuth(String code, String appId, String appSecret) {
        FeishuAuthenticationRequest request = new FeishuAuthenticationRequest(code, appId, appSecret);
        request.setAuthType("miniprogram");
        return request;
    }

    /**
     * 创建自动注册的飞书认证请求
     *
     * @param code  授权码
     * @param appId 应用ID
     * @return 飞书认证请求
     */
    public static FeishuAuthenticationRequest withAutoRegister(String code, String appId) {
        FeishuAuthenticationRequest request = new FeishuAuthenticationRequest(code, appId);
        request.setAutoRegister(true);
        return request;
    }

    /**
     * 创建指定语言的飞书认证请求
     *
     * @param code   授权码
     * @param appId  应用ID
     * @param locale 语言设置
     * @return 飞书认证请求
     */
    public static FeishuAuthenticationRequest withLocale(String code, String appId, String locale) {
        FeishuAuthenticationRequest request = new FeishuAuthenticationRequest(code, appId);
        request.setLocale(locale);
        return request;
    }

    @Override
    public String toString() {
        return String.format("FeishuAuthenticationRequest{appId='%s', authType='%s', locale='%s', autoRegister=%s}",
                appId, authType, locale, autoRegister);
    }
}
