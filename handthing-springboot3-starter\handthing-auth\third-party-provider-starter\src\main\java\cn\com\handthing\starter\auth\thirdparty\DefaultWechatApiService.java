package cn.com.handthing.starter.auth.thirdparty;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;

import java.time.Duration;

/**
 * 默认微信API服务实现
 * <p>
 * 提供微信API调用的默认实现，包括获取访问令牌、用户信息等功能。
 * 使用WebClient进行HTTP调用，支持超时和错误处理。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnMissingBean(WechatApiService.class)
public class DefaultWechatApiService implements WechatApiService {

    private final WebClient webClient;
    private final ObjectMapper objectMapper;

    /**
     * 微信API基础URL
     */
    private static final String WECHAT_API_BASE_URL = "https://api.weixin.qq.com";

    /**
     * 获取访问令牌URL
     */
    private static final String GET_TOKEN_URL = WECHAT_API_BASE_URL + "/sns/oauth2/access_token";

    /**
     * 刷新访问令牌URL
     */
    private static final String REFRESH_TOKEN_URL = WECHAT_API_BASE_URL + "/sns/oauth2/refresh_token";

    /**
     * 获取用户信息URL
     */
    private static final String GET_USER_INFO_URL = WECHAT_API_BASE_URL + "/sns/userinfo";

    /**
     * 验证访问令牌URL
     */
    private static final String VALIDATE_TOKEN_URL = WECHAT_API_BASE_URL + "/sns/auth";

    /**
     * 默认构造函数
     */
    public DefaultWechatApiService() {
        this.webClient = WebClient.builder()
                .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(1024 * 1024))
                .build();
        this.objectMapper = new ObjectMapper();
    }

    @Override
    public WechatTokenResult getAccessToken(String appId, String appSecret, String code) {
        try {
            log.debug("Getting access token for appId: {}", appId);

            String response = webClient.get()
                    .uri(GET_TOKEN_URL + "?appid={appId}&secret={appSecret}&code={code}&grant_type=authorization_code", 
                         appId, appSecret, code)
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(10))
                    .block();

            JsonNode jsonNode = objectMapper.readTree(response);

            if (jsonNode.has("errcode")) {
                int errcode = jsonNode.get("errcode").asInt();
                String errmsg = jsonNode.get("errmsg").asText();
                log.warn("Failed to get access token for appId: {}, errcode: {}, errmsg: {}", appId, errcode, errmsg);
                return WechatTokenResult.failure(String.valueOf(errcode), errmsg);
            } else {
                String accessToken = jsonNode.get("access_token").asText();
                String refreshToken = jsonNode.get("refresh_token").asText();
                long expiresIn = jsonNode.get("expires_in").asLong();
                String openId = jsonNode.get("openid").asText();
                String scope = jsonNode.get("scope").asText();
                
                log.debug("Access token obtained successfully for appId: {}, openId: {}", appId, openId);
                return WechatTokenResult.success(accessToken, refreshToken, expiresIn, openId, scope);
            }

        } catch (Exception e) {
            log.error("Error getting access token for appId: {}", appId, e);
            return WechatTokenResult.failure("NETWORK_ERROR", "网络请求失败: " + e.getMessage());
        }
    }

    @Override
    public WechatTokenResult refreshAccessToken(String appId, String refreshToken) {
        try {
            log.debug("Refreshing access token for appId: {}", appId);

            String response = webClient.get()
                    .uri(REFRESH_TOKEN_URL + "?appid={appId}&grant_type=refresh_token&refresh_token={refreshToken}", 
                         appId, refreshToken)
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(10))
                    .block();

            JsonNode jsonNode = objectMapper.readTree(response);

            if (jsonNode.has("errcode")) {
                int errcode = jsonNode.get("errcode").asInt();
                String errmsg = jsonNode.get("errmsg").asText();
                log.warn("Failed to refresh access token for appId: {}, errcode: {}, errmsg: {}", appId, errcode, errmsg);
                return WechatTokenResult.failure(String.valueOf(errcode), errmsg);
            } else {
                String accessToken = jsonNode.get("access_token").asText();
                String newRefreshToken = jsonNode.get("refresh_token").asText();
                long expiresIn = jsonNode.get("expires_in").asLong();
                String openId = jsonNode.get("openid").asText();
                String scope = jsonNode.get("scope").asText();
                
                log.debug("Access token refreshed successfully for appId: {}, openId: {}", appId, openId);
                return WechatTokenResult.success(accessToken, newRefreshToken, expiresIn, openId, scope);
            }

        } catch (Exception e) {
            log.error("Error refreshing access token for appId: {}", appId, e);
            return WechatTokenResult.failure("NETWORK_ERROR", "网络请求失败: " + e.getMessage());
        }
    }

    @Override
    public WechatUserResult getUserInfo(String accessToken, String openId, String lang) {
        try {
            log.debug("Getting user info for openId: {}", openId);

            String response = webClient.get()
                    .uri(GET_USER_INFO_URL + "?access_token={accessToken}&openid={openId}&lang={lang}", 
                         accessToken, openId, lang != null ? lang : "zh_CN")
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(10))
                    .block();

            JsonNode jsonNode = objectMapper.readTree(response);

            if (jsonNode.has("errcode")) {
                int errcode = jsonNode.get("errcode").asInt();
                String errmsg = jsonNode.get("errmsg").asText();
                log.warn("Failed to get user info for openId: {}, errcode: {}, errmsg: {}", openId, errcode, errmsg);
                return WechatUserResult.failure(String.valueOf(errcode), errmsg);
            } else {
                WechatUserResult result = WechatUserResult.success();
                
                result.setOpenId(getTextValue(jsonNode, "openid"));
                result.setUnionId(getTextValue(jsonNode, "unionid"));
                result.setNickname(getTextValue(jsonNode, "nickname"));
                result.setSex(getTextValue(jsonNode, "sex"));
                result.setProvince(getTextValue(jsonNode, "province"));
                result.setCity(getTextValue(jsonNode, "city"));
                result.setCountry(getTextValue(jsonNode, "country"));
                result.setHeadImgUrl(getTextValue(jsonNode, "headimgurl"));

                // 处理特权信息
                if (jsonNode.has("privilege")) {
                    JsonNode privilegeNode = jsonNode.get("privilege");
                    if (privilegeNode.isArray()) {
                        String[] privileges = new String[privilegeNode.size()];
                        for (int i = 0; i < privilegeNode.size(); i++) {
                            privileges[i] = privilegeNode.get(i).asText();
                        }
                        result.setPrivilege(privileges);
                    }
                }

                log.debug("User info obtained successfully for openId: {}", openId);
                return result;
            }

        } catch (Exception e) {
            log.error("Error getting user info for openId: {}", openId, e);
            return WechatUserResult.failure("NETWORK_ERROR", "网络请求失败: " + e.getMessage());
        }
    }

    @Override
    public boolean validateAccessToken(String accessToken, String openId) {
        try {
            log.debug("Validating access token for openId: {}", openId);

            String response = webClient.get()
                    .uri(VALIDATE_TOKEN_URL + "?access_token={accessToken}&openid={openId}", accessToken, openId)
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(10))
                    .block();

            JsonNode jsonNode = objectMapper.readTree(response);
            int errcode = jsonNode.get("errcode").asInt();

            if (errcode == 0) {
                log.debug("Access token validation successful for openId: {}", openId);
                return true;
            } else {
                String errmsg = jsonNode.get("errmsg").asText();
                log.warn("Access token validation failed for openId: {}, errcode: {}, errmsg: {}", openId, errcode, errmsg);
                return false;
            }

        } catch (Exception e) {
            log.error("Error validating access token for openId: {}", openId, e);
            return false;
        }
    }

    @Override
    public boolean validateConfig(String appId, String appSecret) {
        try {
            log.debug("Validating wechat config for appId: {}", appId);

            // 微信没有直接的配置验证接口，这里简化处理
            // 实际使用中可以通过获取基础接口调用凭证来验证
            if (appId != null && !appId.trim().isEmpty() && 
                appSecret != null && !appSecret.trim().isEmpty()) {
                log.debug("Wechat config validation successful");
                return true;
            }

            log.warn("Invalid wechat config: appId or appSecret is empty");
            return false;

        } catch (Exception e) {
            log.error("Error validating wechat config", e);
            return false;
        }
    }

    /**
     * 安全地获取JSON节点的文本值
     *
     * @param jsonNode JSON节点
     * @param fieldName 字段名
     * @return 文本值，如果不存在返回null
     */
    private String getTextValue(JsonNode jsonNode, String fieldName) {
        return jsonNode.has(fieldName) ? jsonNode.get(fieldName).asText() : null;
    }

    /**
     * 创建模拟的微信API服务（用于测试）
     *
     * @return 模拟的API服务
     */
    public static WechatApiService createMockService() {
        return new WechatApiService() {
            @Override
            public WechatTokenResult getAccessToken(String appId, String appSecret, String code) {
                // 模拟成功响应
                if ("test_app_id".equals(appId) && "test_app_secret".equals(appSecret) && "test_code".equals(code)) {
                    return WechatTokenResult.success("mock_access_token_" + System.currentTimeMillis(), 
                            "mock_refresh_token_" + System.currentTimeMillis(), 7200L, "test_open_id", "snsapi_userinfo");
                }
                return WechatTokenResult.failure("40029", "invalid code");
            }

            @Override
            public WechatTokenResult refreshAccessToken(String appId, String refreshToken) {
                // 模拟成功响应
                if ("test_app_id".equals(appId) && refreshToken.startsWith("mock_refresh_token")) {
                    return WechatTokenResult.success("mock_access_token_" + System.currentTimeMillis(), 
                            refreshToken, 7200L, "test_open_id", "snsapi_userinfo");
                }
                return WechatTokenResult.failure("40030", "invalid refresh_token");
            }

            @Override
            public WechatUserResult getUserInfo(String accessToken, String openId, String lang) {
                // 模拟成功响应
                if (accessToken.startsWith("mock_access_token") && "test_open_id".equals(openId)) {
                    WechatUserResult result = WechatUserResult.success();
                    result.setOpenId("test_open_id");
                    result.setUnionId("test_union_id");
                    result.setNickname("测试用户");
                    result.setSex("1");
                    result.setProvince("广东");
                    result.setCity("深圳");
                    result.setCountry("中国");
                    result.setHeadImgUrl("http://example.com/avatar.jpg");
                    result.setPrivilege(new String[]{"chinaunicom"});
                    return result;
                }
                return WechatUserResult.failure("40001", "invalid credential");
            }

            @Override
            public boolean validateAccessToken(String accessToken, String openId) {
                return accessToken.startsWith("mock_access_token") && "test_open_id".equals(openId);
            }

            @Override
            public boolean validateConfig(String appId, String appSecret) {
                return "test_app_id".equals(appId) && "test_app_secret".equals(appSecret);
            }
        };
    }
}
