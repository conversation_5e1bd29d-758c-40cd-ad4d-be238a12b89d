package cn.com.handthing.starter.knife4j.configuration;

import cn.com.handthing.starter.apidoc.configuration.ApiDocAutoConfiguration;
import cn.com.handthing.starter.apidoc.properties.ApiDocProperties;
import cn.com.handthing.starter.knife4j.annotation.EnableKnife4j;
import cn.com.handthing.starter.knife4j.properties.Knife4jProperties;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Profile;
import org.springframework.core.annotation.Order;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpStatus;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.provisioning.InMemoryUserDetailsManager;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.ViewControllerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.resource.PathResourceResolver;

import java.io.IOException;

/**
 * Knife4j 自动配置类
 * 
 * <p>基于 api-doc-spring-boot-starter 进行增强，提供 Knife4j 的美观界面和增强功能。
 * 
 * <AUTHOR>
 * @since V1.0.0
 */
@Configuration
@ConditionalOnWebApplication(type = ConditionalOnWebApplication.Type.SERVLET)
@ConditionalOnProperty(prefix = "handthing.api-doc", name = "enabled", havingValue = "true")
@EnableConfigurationProperties(Knife4jProperties.class)
@AutoConfigureAfter(ApiDocAutoConfiguration.class) // 在 ApiDoc 配置之后执行
// 不再导入独立的安全配置类，直接在这里定义
public class Knife4jAutoConfiguration {

    private static final Logger log = LoggerFactory.getLogger(Knife4jAutoConfiguration.class);

    public Knife4jAutoConfiguration() {
        log.info("=== Knife4jAutoConfiguration is being created ===");
    }

    /**
     * 检查配置属性的方法
     */
    @Bean
    public String configurationChecker(Knife4jProperties knife4jProperties) {
        log.info("=== Configuration Checker ===");
        log.info("Knife4j enabled: {}", knife4jProperties.getEnabled());
        log.info("Auth enabled: {}", knife4jProperties.getAuth().isEnabled());
        log.info("Auth username: {}", knife4jProperties.getAuth().getUsername());
        log.info("Auth password: {}", knife4jProperties.getAuth().getPassword());
        log.info("=== End Configuration Checker ===");
        return "config-checked";
    }

    /**
     * 自定义 OpenAPI 配置，支持继承性扩展
     *
     * <p>继承性扩展逻辑：
     * <ul>
     * <li>Knife4jProperties 继承了 ApiDocProperties 的所有基础配置</li>
     * <li>如果 knife4j 配置了自己的值，使用 knife4j 的配置（覆盖继承）</li>
     * <li>如果 knife4j 没有配置，则使用继承的 api-doc 基础配置</li>
     * </ul>
     */
    @Bean
    public OpenAPI knife4jOpenAPI(Knife4jProperties knife4jProperties) {
        // 使用继承性扩展获取配置值
        String title = knife4jProperties.getTitle();
        String description = knife4jProperties.getDescription();
        String version = knife4jProperties.getVersion();

        log.info("Initializing Knife4j OpenAPI configuration with inherited title: {}", title);
        log.info("Knife4j inheritance - title: knife4j='{}', inherited='{}', effective='{}'",
                knife4jProperties.getTitle(), knife4jProperties.getTitle(), title);

        // 使用 Knife4j 扩展的联系人信息
        Knife4jProperties.Knife4jContact knife4jContact = knife4jProperties.getKnife4j().getContact();
        Contact contact = new Contact()
                .name(knife4jContact.getName())
                .email(knife4jContact.getEmail())
                .url(knife4jContact.getUrl());

        // 添加许可证信息
        License license = new License()
                .name(knife4jContact.getLicenseName())
                .url(knife4jContact.getLicenseUrl());

        Info info = new Info()
                .title(title)           // 使用继承性扩展的标题
                .version(version)       // 使用继承性扩展的版本
                .description(description) // 使用继承性扩展的描述
                .contact(contact)
                .license(license);

        OpenAPI openAPI = new OpenAPI().info(info);

        log.info("Knife4j OpenAPI configuration completed. Access URL: /doc.html");
        return openAPI;
    }

    /**
     * Knife4j 增强配置
     */
    @Bean
    public Knife4jEnhancementConfig knife4jEnhancementConfig(Knife4jProperties knife4jProperties) {

        log.info("Configuring Knife4j enhancement features");

        Knife4jEnhancementConfig config = new Knife4jEnhancementConfig();

        // UI 配置
        Knife4jProperties.Ui ui = knife4jProperties.getKnife4j().getUi();
        config.setEnableSearch(ui.isEnableSearch());
        config.setEnableDebug(ui.isEnableDebug());
        config.setEnableExport(ui.isEnableExport());
        config.setEnableGroup(ui.isEnableGroup());
        config.setEnableOpenApi(ui.isEnableOpenApi());
        config.setEnableDynamicParameter(ui.isEnableDynamicParameter());
        config.setTheme(ui.getTheme());

        // 增强功能配置
        Knife4jProperties.Enhancement enhancement = knife4jProperties.getKnife4j().getEnhancement();
        config.setEnableRequestCache(enhancement.isEnableRequestCache());
        config.setEnableFilterMultipartApis(enhancement.isEnableFilterMultipartApis());
        config.setEnableHost(enhancement.isEnableHost());
        config.setEnableSort(enhancement.isEnableSort());
        config.setProductionSecurityEnabled(enhancement.isProductionSecurityEnabled());

        log.info("Knife4j enhancement configuration completed");
        return config;
    }



    /**
     * 配置 Knife4j 相关的 Web 配置
     *
     * <p>完全禁用 swagger-ui 相关路径的访问，强制使用 Knife4j
     */
    @Bean
    public WebMvcConfigurer knife4jWebMvcConfigurer() {
        return new WebMvcConfigurer() {
            @Override
            public void addViewControllers(ViewControllerRegistry registry) {
                // 禁用 swagger-ui.html 访问，返回 404
                registry.addViewController("/swagger-ui.html").setStatusCode(HttpStatus.NOT_FOUND);
                registry.addViewController("/swagger-ui/").setStatusCode(HttpStatus.NOT_FOUND);
                registry.addViewController("/swagger-ui/**").setStatusCode(HttpStatus.NOT_FOUND);

                log.info("Knife4j: Disabled swagger-ui.html access, use /doc.html instead");
            }

            @Override
            public void addResourceHandlers(ResourceHandlerRegistry registry) {
                // 禁用 swagger-ui 资源访问
                registry.addResourceHandler("/swagger-ui/**")
                        .addResourceLocations("classpath:/META-INF/resources/webjars/swagger-ui/")
                        .resourceChain(false)
                        .addResolver(new PathResourceResolver() {
                            @Override
                            protected Resource getResource(String resourcePath, Resource location) throws IOException {
                                // 始终返回 null，禁用所有 swagger-ui 资源访问
                                return null;
                            }
                        });

                log.info("Knife4j: Disabled swagger-ui resources access");
            }
        };
    }

    /**
     * Knife4j 增强配置类
     */
    public static class Knife4jEnhancementConfig {
        private boolean enableSearch = true;
        private boolean enableDebug = true;
        private boolean enableExport = true;
        private boolean enableGroup = true;
        private boolean enableOpenApi = true;
        private boolean enableDynamicParameter = true;
        private String theme = "default";
        private boolean enableRequestCache = true;
        private boolean enableFilterMultipartApis = false;
        private boolean enableHost = true;
        private boolean enableSort = true;
        private boolean productionSecurityEnabled = true;

        // Getters and Setters
        public boolean isEnableSearch() {
            return enableSearch;
        }

        public void setEnableSearch(boolean enableSearch) {
            this.enableSearch = enableSearch;
        }

        public boolean isEnableDebug() {
            return enableDebug;
        }

        public void setEnableDebug(boolean enableDebug) {
            this.enableDebug = enableDebug;
        }

        public boolean isEnableExport() {
            return enableExport;
        }

        public void setEnableExport(boolean enableExport) {
            this.enableExport = enableExport;
        }

        public boolean isEnableGroup() {
            return enableGroup;
        }

        public void setEnableGroup(boolean enableGroup) {
            this.enableGroup = enableGroup;
        }

        public boolean isEnableOpenApi() {
            return enableOpenApi;
        }

        public void setEnableOpenApi(boolean enableOpenApi) {
            this.enableOpenApi = enableOpenApi;
        }

        public boolean isEnableDynamicParameter() {
            return enableDynamicParameter;
        }

        public void setEnableDynamicParameter(boolean enableDynamicParameter) {
            this.enableDynamicParameter = enableDynamicParameter;
        }

        public String getTheme() {
            return theme;
        }

        public void setTheme(String theme) {
            this.theme = theme;
        }

        public boolean isEnableRequestCache() {
            return enableRequestCache;
        }

        public void setEnableRequestCache(boolean enableRequestCache) {
            this.enableRequestCache = enableRequestCache;
        }

        public boolean isEnableFilterMultipartApis() {
            return enableFilterMultipartApis;
        }

        public void setEnableFilterMultipartApis(boolean enableFilterMultipartApis) {
            this.enableFilterMultipartApis = enableFilterMultipartApis;
        }

        public boolean isEnableHost() {
            return enableHost;
        }

        public void setEnableHost(boolean enableHost) {
            this.enableHost = enableHost;
        }

        public boolean isEnableSort() {
            return enableSort;
        }

        public void setEnableSort(boolean enableSort) {
            this.enableSort = enableSort;
        }

        public boolean isProductionSecurityEnabled() {
            return productionSecurityEnabled;
        }

        public void setProductionSecurityEnabled(boolean productionSecurityEnabled) {
            this.productionSecurityEnabled = productionSecurityEnabled;
        }
    }
}