HandThing Auth Spring Boot Starter
详细设计说明书 (V3.0 SaaS增强版)
文档版本

V3.0

状态

定稿 (Final)

作者

HandThing, Gemini

创建日期

2025-07-29

1. 概述 (Overview)
   1.1. 文档目的
   本文档在V2.0的基础上进行重大升级，旨在为auth-starter注入全面的、可配置化的多租户SaaS能力。文档将详细阐述如何将原有的单体应用认证模型演进为一个能够服务于多个隔离租户、且每个租户拥有独立认证策略和配置的企业级认证中心。

1.2. 核心设计演进：从“认证总线”到“租户级认证总线”
设计的核心思想从“构建统一认证流程”演进为 “为每个租户提供一个独立的、可动态配置的认证总线”。这意味着，认证体系的所有决策点，从“是否允许密码登录”到“JWT的有效期”，都将首先查询当前租户的特定配置，如果不存在，再采用系统的全局默认配置。

2. SaaS化核心概念与设计
   2.1. 多租户 (Multi-tenancy)
   系统将采用 共享数据库、共享应用实例 的模式。所有租户的数据（如users, roles, tenant_configs）存储在同一个数据库中，通过一个tenant_id字段进行严格的逻辑隔离。

2.2. 租户识别策略 (Tenant Resolution)
系统必须在处理业务逻辑之前（甚至在Spring Security介入之前）识别出当前请求的tenant_id。

引入TenantResolverFilter: 这是一个优先级非常高的Servlet过滤器，它的唯一职责就是解析HTTP请求并确定tenant_id。

TenantResolver接口: TenantResolverFilter内部将委托给一个TenantResolver策略接口。我们将提供多种默认实现：

SubdomainTenantResolver: 从子域名解析 (如 tenant-a.myapp.com)。

HttpHeaderTenantResolver: 从自定义HTTP头解析 (如 X-Tenant-ID: tenant-a)。

RequestPathTenantResolver: 从URL路径解析 (如 /api/tenant-a/...)。

TenantContextHolder: 与AuthContextHolder类似，这是一个基于ThreadLocal的容器，用于在整个请求生命周期中持有已识别的tenant_id。TenantResolverFilter负责设置和清理它。

2.3. 动态配置中心 (Dynamic Configuration)
为了实现“可配置化”，我们将引入一个租户级的动态配置机制。

数据库模型: 新增tenant_configs表，用于存储每个租户的自定义配置。
| id | tenant_id | config_key | config_value |
| :--- | :--- | :--- | :--- |
| 1 | 101 | auth.grant-types.password.enabled | false |
| 2 | 101 | auth.jwt.access-token-ttl | 15m |
| 3 | 102 | auth.grant-types.wecom.enabled | true |

TenantConfigService服务:

这是一个新的核心服务，负责获取指定租户的配置项。

分层配置读取: getConfig(tenantId, key)的逻辑是：

查询tenant_configs表，看当前租户是否有该key的特定配置。

如果DB中没有，则回退到application.yml中读取全局默认配置。

缓存: 此服务必须被我们之前设计的cache-starter进行重度缓存，以避免频繁的数据库查询。缓存key可以是cache:tenant_config:{tenantId}:{configKey}。

3. 整体架构 (V3.0)
   3.1. 架构与核心流程图 (SaaS版)
   graph TD
   subgraph HTTP请求
   A[Request] --> B(TenantResolverFilter);
   end

   B -- 1. 解析TenantID --> C[TenantContextHolder];
   B --> D{Spring SecurityFilterChain};

   subgraph D [Spring SecurityFilterChain]
   K[JwtAuthenticationFilter]
   end

   subgraph 认证流程 (Login)
   E[TokenEndpoint] --> F{AuthenticationManager};
   F -- 调用 --> G[Providers];
   G -- 7. 认证决策 --> H((TenantConfigService));
   H -- 6. 查询配置 --> I[Cache];
   I -- 未命中 --> J[tenant_configs DB];
   G -- 8. 成功后 --> L{TokenService};
   end

   subgraph API访问流程
   K -- 2. 解析JWT --> L;
   L -- 3. 重建AuthContext(含TenantID) --> M[AuthContextHolder];
   end

   subgraph 业务代码
   M -- 4. 供业务使用 --> N[Controller/Service];
   N -- 5. 获取当前租户ID --> C;
   end

3.2. 关键组件职责变更
AuthContext: tenantId字段现在是强制性的，是上下文的核心。

AuthenticationProvider: 不再直接读取静态配置。它们将注入TenantConfigService。在执行认证前，会先通过tenantConfigService.getBoolean(tenantId, "auth.grant-types.password.enabled")来判断当前租户是否允许此登录方式。

TokenService:

在创建JWT时，必须将从TenantContextHolder中获取的tenant_id作为核心Claim写入令牌。

在解析JWT时，必须验证Claim中的tenant_id，并用它来填充重建的AuthContext。

主应用实现的接口 (UserDetailsService, SmsCodeValidator, etc.):

这些接口的实现必须是租户感知的。它们需要从TenantContextHolder中获取tenantId，并在数据库查询中加入WHERE tenant_id = ?条件。

4. 模块功能清单 (V3.0)
   auth-core 模块
   [ ] 模型定义:

[x] AuthContext: 强化tenantId的核心地位。

[x] TenantContextHolder: 新增，用于持有tenantId。

[ ] 接口定义:

[x] TenantResolver: 新增，定义租户识别策略接口。

auth-spring-boot-starter 模块
[ ] 核心过滤器:

[x] TenantResolverFilter: 新增，用于在请求早期识别租户。

[ ] 核心服务:

[x] TenantConfigService: 新增，提供分层的租户配置读取能力。

[ ] 自动配置:

[x] SaaSAuthAutoConfiguration:

自动装配TenantResolverFilter，并确保其高优先级。

自动装配TenantConfigService，并集成cache-starter。

[ ] 组件增强:

[x] 所有Provider: 注入并使用TenantConfigService进行动态决策。

[x] TokenService: 实现tenantId在JWT中的双向绑定。

5. 接口协定与配置 (SaaS版)
   5.1. 接口协定 (主应用实现)
   cn.com.handthing.starter.auth.core.TenantResolver: 主应用可以选择实现此接口，提供自定义的租户识别逻辑（例如，通过用户输入的邮箱域名@acme.com来查找租户）。

5.2. 统一配置 (application.yml) - 作为全局默认值
application.yml中的所有handthing.auth配置，其语义从“系统唯一配置”转变为“全局默认配置”。

handthing:
auth:
# 租户识别策略的默认实现 (可被主应用的Bean覆盖)
tenant-resolver-strategy: subdomain # 可选: header, path

    # 以下所有配置均为全局默认值，可被租户在DB中的配置覆盖
    jwt:
      secret: "a-very-long-and-secure-secret-key-for-hs256"
      access-token-ttl: 30m
      
    grant-types:
      password:
        enabled: true # 全局默认允许密码登录
      sms-code:
        enabled: false # 全局默认禁止短信登录

示例场景:
如果application.yml中password.enabled为true，但租户A在tenant_configs库中的记录为false。那么，当租户A的用户尝试密码登录时，PasswordAuthenticationProvider会通过TenantConfigService查询到false，从而拒绝该次登录。

通过这一系列升级，我们的 auth-starter 真正从一个强大的单体应用认证工具，演进为了一个灵活、健壮、可满足复杂商业需求的 企业级SaaS认证基座。