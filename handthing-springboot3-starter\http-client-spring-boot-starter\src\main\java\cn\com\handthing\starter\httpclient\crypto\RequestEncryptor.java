package cn.com.handthing.starter.httpclient.crypto;

import org.springframework.http.HttpRequest;
import org.springframework.lang.NonNull;

/**
 * 请求加密器接口。
 * <p>
 * 实现此接口以定义在HTTP请求发送前对其进行加密的逻辑。
 * Starter会自动查找Spring上下文中此接口的Bean，并在相关端点配置启用加密时应用它。
 *
 * @param <T> 加密操作可能需要的上下文对象类型，通常是请求体。
 */
public interface RequestEncryptor<T> {

    /**
     * 对给定的HTTP请求和请求体执行加密操作。
     * <p>
     * 此方法可以修改请求头（例如，添加签名头）和/或转换请求体（例如，加密JSON内容）。
     *
     * @param request 原始的HTTP请求对象。这是一个可变对象，可以直接修改其头部。
     * @param body    原始的请求体。
     * @return 加密后的新请求体。如果请求体没有变化，应返回原始请求体。
     */
    @NonNull
    T encrypt(@NonNull HttpRequest request, @NonNull T body);

}