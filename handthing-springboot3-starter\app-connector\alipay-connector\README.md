# 支付宝连接器 (Alipay Connector)

## 概述

支付宝连接器提供了与支付宝开放平台的集成能力，支持个人开发者和企业用户的支付、小程序、生活服务等功能。通过统一的API接口，可以轻松实现用户认证、支付处理、小程序管理等功能。

## 特性

- ✅ **OAuth2.0认证** - 支持支付宝用户授权登录
- ✅ **支付功能** - 支持当面付、手机网站支付、APP支付等
- ✅ **小程序管理** - 支持小程序开发和管理
- ✅ **生活服务** - 支持芝麻信用、花呗分期等服务
- ✅ **营销工具** - 支持优惠券、红包等营销功能
- ✅ **数据分析** - 获取交易数据和用户行为分析
- ✅ **沙箱支持** - 完整的沙箱环境测试支持

## 快速开始

### 1. 添加依赖

```xml
<dependency>
    <groupId>cn.com.handthing.springboot3.starter</groupId>
    <artifactId>alipay-connector</artifactId>
    <version>1.0.0-SNAPSHOT</version>
</dependency>
```

### 2. 配置文件

```yaml
handthing:
  connector:
    alipay:
      enabled: true
      app-id: "your-app-id"
      private-key: "your-private-key"
      alipay-public-key: "alipay-public-key"
      sandbox: true
      cert-mode: false
      app-cert-path: "classpath:alipay/appCertPublicKey.crt"
      alipay-cert-path: "classpath:alipay/alipayCertPublicKey_RSA2.crt"
      alipay-root-cert-path: "classpath:alipay/alipayRootCert.crt"
      redirect-uri: "http://your-domain.com/callback/alipay"
```

### 3. 基本使用

```java
@RestController
public class AlipayController {
    
    @Autowired
    private AlipayService alipayService;
    
    @Autowired
    private AuthService authService;
    
    // 获取授权URL
    @GetMapping("/alipay/auth")
    public String getAuthUrl() {
        return authService.getAuthorizationUrl(
            PlatformType.ALIPAY, 
            "state", 
            "http://your-domain.com/callback/alipay"
        );
    }
    
    // 创建支付订单
    @PostMapping("/alipay/pay")
    public Map<String, Object> createPayment(@RequestParam String outTradeNo,
                                            @RequestParam String subject,
                                            @RequestParam String totalAmount) {
        return alipayService.payment().createPayment(outTradeNo, subject, totalAmount, "FAST_INSTANT_TRADE_PAY");
    }
    
    // 查询支付状态
    @GetMapping("/alipay/pay/{outTradeNo}")
    public Map<String, Object> queryPayment(@PathVariable String outTradeNo) {
        return alipayService.payment().queryPayment(outTradeNo);
    }
    
    // 获取用户信息
    @GetMapping("/alipay/user")
    public Map<String, Object> getUserInfo(@RequestParam String accessToken) {
        return alipayService.user().getUserInfo(accessToken);
    }
}
```

## 配置说明

| 配置项 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| `enabled` | Boolean | 是 | false | 是否启用支付宝连接器 |
| `app-id` | String | 是 | - | 应用ID |
| `private-key` | String | 是 | - | 应用私钥 |
| `alipay-public-key` | String | 是 | - | 支付宝公钥 |
| `sandbox` | Boolean | 否 | false | 是否使用沙箱环境 |
| `cert-mode` | Boolean | 否 | false | 是否使用证书模式 |
| `app-cert-path` | String | 否 | - | 应用公钥证书路径 |
| `alipay-cert-path` | String | 否 | - | 支付宝公钥证书路径 |
| `alipay-root-cert-path` | String | 否 | - | 支付宝根证书路径 |
| `redirect-uri` | String | 否 | - | OAuth回调地址 |

## API文档

### 支付API (AlipayPaymentApi)

#### 创建支付订单
```java
Map<String, Object> createPayment(String outTradeNo, String subject, String totalAmount, String productCode)
```

#### 查询支付状态
```java
Map<String, Object> queryPayment(String outTradeNo)
```

#### 申请退款
```java
Map<String, Object> refund(String outTradeNo, String refundAmount, String refundReason)
```

#### 查询退款状态
```java
Map<String, Object> queryRefund(String outTradeNo, String outRequestNo)
```

### 用户API (AlipayUserApi)

#### 获取用户信息
```java
Map<String, Object> getUserInfo(String accessToken)
```

#### 获取用户授权信息
```java
Map<String, Object> getUserAuth(String accessToken)
```

### 小程序API (AlipayMiniProgramApi)

#### 获取小程序二维码
```java
Map<String, Object> getQRCode(String accessToken, String page, String query)
```

#### 发送模板消息
```java
Map<String, Object> sendTemplateMessage(String accessToken, String toUserId, String templateId, Map<String, Object> data)
```

## 常见问题

### Q: 如何获取支付宝的AppId和密钥？
A: 登录支付宝开放平台，创建应用后可以获取AppId，然后生成RSA密钥对，上传公钥到平台。

### Q: 沙箱环境和生产环境有什么区别？
A: 沙箱环境用于开发测试，使用虚拟资金；生产环境处理真实交易。

### Q: 什么是证书模式？
A: 证书模式使用公钥证书进行验签，比普通公钥模式更安全，推荐生产环境使用。

### Q: 如何处理支付回调？
A: 实现支付回调接口，验证签名后处理业务逻辑，并返回"success"给支付宝。

## 更多信息

- [支付宝开放平台文档](https://opendocs.alipay.com/)
- [支付API文档](https://opendocs.alipay.com/apis/api_1/alipay.trade.page.pay)
- [小程序开发文档](https://opendocs.alipay.com/mini/developer)
