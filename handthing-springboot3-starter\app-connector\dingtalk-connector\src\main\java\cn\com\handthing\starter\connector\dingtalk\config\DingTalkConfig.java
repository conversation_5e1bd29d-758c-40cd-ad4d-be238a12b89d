package cn.com.handthing.starter.connector.dingtalk.config;

import cn.com.handthing.starter.connector.config.PlatformConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import jakarta.validation.constraints.NotBlank;

/**
 * 钉钉配置类
 * <p>
 * 继承PlatformConfig，包含钉钉特有的配置项，
 * 如应用Key、应用密钥等。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ConfigurationProperties(prefix = "handthing.connector.dingtalk")
public class DingTalkConfig extends PlatformConfig {

    /**
     * 应用Key（AppKey）
     */
    @NotBlank(message = "DingTalk app key cannot be blank")
    private String appKey;

    /**
     * 应用密钥（AppSecret）
     */
    @NotBlank(message = "DingTalk app secret cannot be blank")
    private String appSecret;

    /**
     * 钉钉API基础URL
     */
    private String apiBaseUrl = "https://api.dingtalk.com";

    /**
     * 钉钉授权URL
     */
    private String authUrl = "https://login.dingtalk.com/oauth2/auth";

    /**
     * 是否启用企业内部应用
     */
    private boolean internalApp = true;

    /**
     * 是否启用第三方企业应用
     */
    private boolean thirdPartyApp = false;

    /**
     * 企业CorpId（仅企业内部应用需要）
     */
    private String corpId;

    /**
     * 企业内部应用的AgentId（仅企业内部应用需要）
     */
    private String agentId;

    /**
     * 第三方应用的SuiteKey（仅第三方应用需要）
     */
    private String suiteKey;

    /**
     * 第三方应用的SuiteSecret（仅第三方应用需要）
     */
    private String suiteSecret;

    /**
     * 事件订阅的Token
     */
    private String token;

    /**
     * 事件订阅的EncodingAESKey
     */
    private String encodingAESKey;

    /**
     * 是否启用事件订阅
     */
    private boolean enableCallback = false;

    /**
     * 事件订阅回调URL
     */
    private String callbackUrl;

    @Override
    public boolean isValid() {
        // 钉钉特有的验证逻辑
        boolean baseValid = isEnabled() && 
                appKey != null && !appKey.trim().isEmpty() &&
                appSecret != null && !appSecret.trim().isEmpty();

        if (!baseValid) {
            return false;
        }

        // 如果启用第三方应用，需要额外验证第三方应用相关配置
        if (thirdPartyApp) {
            return suiteKey != null && !suiteKey.trim().isEmpty() &&
                   suiteSecret != null && !suiteSecret.trim().isEmpty();
        }

        // 如果启用企业内部应用，需要验证企业相关配置
        if (internalApp) {
            return corpId != null && !corpId.trim().isEmpty();
        }

        return true;
    }

    /**
     * 获取有效的API基础URL
     *
     * @return API基础URL
     */
    public String getEffectiveApiBaseUrl() {
        return getEffectiveApiBaseUrl(apiBaseUrl);
    }

    /**
     * 获取有效的授权URL
     *
     * @return 授权URL
     */
    public String getEffectiveAuthUrl() {
        return getEffectiveAuthUrl(authUrl);
    }

    /**
     * 检查是否为第三方应用模式
     *
     * @return 如果是第三方应用返回true，否则返回false
     */
    public boolean isThirdPartyMode() {
        return thirdPartyApp && suiteKey != null && !suiteKey.trim().isEmpty();
    }

    /**
     * 检查是否为企业内部应用模式
     *
     * @return 如果是企业内部应用返回true，否则返回false
     */
    public boolean isInternalMode() {
        return internalApp && corpId != null && !corpId.trim().isEmpty();
    }

    /**
     * 检查是否启用事件订阅
     *
     * @return 如果启用事件订阅返回true，否则返回false
     */
    public boolean isCallbackEnabled() {
        return enableCallback && 
               token != null && !token.trim().isEmpty() &&
               encodingAESKey != null && !encodingAESKey.trim().isEmpty();
    }

    /**
     * 获取Token URL
     *
     * @return Token获取URL
     */
    public String getTokenUrl() {
        if (isThirdPartyMode()) {
            return getEffectiveApiBaseUrl() + "/v1.0/oauth2/corpAccessToken";
        } else {
            return getEffectiveApiBaseUrl() + "/v1.0/oauth2/userAccessToken";
        }
    }

    /**
     * 获取用户信息URL
     *
     * @return 用户信息获取URL
     */
    public String getUserInfoUrl() {
        return getEffectiveApiBaseUrl() + "/v1.0/contact/users/me";
    }

    /**
     * 获取部门列表URL
     *
     * @return 部门列表获取URL
     */
    public String getDepartmentListUrl() {
        return getEffectiveApiBaseUrl() + "/v1.0/contact/departments";
    }

    /**
     * 获取发送工作通知URL
     *
     * @return 发送工作通知URL
     */
    public String getSendWorkNoticeUrl() {
        return getEffectiveApiBaseUrl() + "/v1.0/robot/oToMessages/batchSend";
    }

    /**
     * 获取待办事项相关URL前缀
     *
     * @return 待办事项URL前缀
     */
    public String getTodoUrlPrefix() {
        return getEffectiveApiBaseUrl() + "/v1.0/todo";
    }

    /**
     * 获取审批相关URL前缀
     *
     * @return 审批URL前缀
     */
    public String getProcessUrlPrefix() {
        return getEffectiveApiBaseUrl() + "/v1.0/workflow";
    }
}
