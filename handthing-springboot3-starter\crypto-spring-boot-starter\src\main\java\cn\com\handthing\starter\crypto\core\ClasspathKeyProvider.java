package cn.com.handthing.starter.crypto.core;

import cn.com.handthing.starter.crypto.exception.CryptoException;
import cn.com.handthing.starter.crypto.properties.CryptoProperties.KeyStoreConfig;
import org.springframework.util.Assert;

import java.io.IOException;
import java.io.InputStream;

/**
 * 从应用的 Classpath 加载原始密钥字节的 KeyProvider。
 *
 * <AUTHOR>
 * @since V1.0.0
 */
class ClasspathKeyProvider extends AbstractKeyProvider {

    private final byte[] keyBytes;

    public ClasspathKeyProvider(KeyStoreConfig config) {
        super(config);
        Assert.hasText(config.getPath(), "path must not be empty for KeyStoreType.CLASSPATH");

        try (InputStream inputStream = getClass().getClassLoader().getResourceAsStream(config.getPath())) {
            if (inputStream == null) {
                throw new CryptoException("Key file not found in classpath: " + config.getPath());
            }
            this.keyBytes = inputStream.readAllBytes();
            this.ready = true;
        } catch (IOException e) {
            throw new CryptoException("Failed to read key from classpath resource: " + config.getPath(), e);
        }
    }

    @Override
    public byte[] getKeyBytes() {
        return this.keyBytes;
    }
}