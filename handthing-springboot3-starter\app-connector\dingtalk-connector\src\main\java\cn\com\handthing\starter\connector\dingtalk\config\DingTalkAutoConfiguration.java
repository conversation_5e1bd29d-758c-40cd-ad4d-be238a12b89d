package cn.com.handthing.starter.connector.dingtalk.config;

import cn.com.handthing.starter.connector.auth.AuthService;
import cn.com.handthing.starter.connector.auth.DefaultAuthService;
import cn.com.handthing.starter.connector.dingtalk.DingTalkAuthProvider;
import cn.com.handthing.starter.connector.token.TokenManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.web.client.RestTemplate;

import jakarta.annotation.PostConstruct;

/**
 * 钉钉自动配置类
 * <p>
 * 当钉钉配置启用时，自动装配钉钉相关的Bean，
 * 并注册到统一的认证服务中。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@RequiredArgsConstructor
@AutoConfiguration
@EnableConfigurationProperties(DingTalkConfig.class)
@ConditionalOnProperty(prefix = "handthing.connector.dingtalk", name = "enabled", havingValue = "true")
@ConditionalOnClass(AuthService.class)
@ComponentScan(basePackages = "cn.com.handthing.starter.connector.dingtalk")
public class DingTalkAutoConfiguration {



    private final AuthService authService;
    private final DingTalkAuthProvider dingTalkAuthProvider;
    private final TokenManager tokenManager;
    private final DingTalkConfig dingTalkConfig;



    /**
     * 注册钉钉认证提供者到认证服务
     */
    @PostConstruct
    public void registerDingTalkAuthProvider() {
        if (authService instanceof DefaultAuthService) {
            DefaultAuthService defaultAuthService = (DefaultAuthService) authService;
            defaultAuthService.registerAuthProvider(dingTalkAuthProvider);
            tokenManager.registerAuthProvider(dingTalkAuthProvider);

            log.info("Registered DingTalk auth provider - AppKey: {}, CorpId: {}, Valid: {}",
                    dingTalkConfig.getAppKey(),
                    dingTalkConfig.getCorpId(),
                    dingTalkConfig.isValid());
        } else {
            log.warn("AuthService is not DefaultAuthService, cannot register DingTalk auth provider");
        }
    }

    /**
     * 钉钉配置信息Bean
     *
     * @param dingTalkConfig 钉钉配置
     * @return 配置信息
     */
    @Bean
    public DingTalkConfigInfo dingTalkConfigInfo(DingTalkConfig dingTalkConfig) {
        DingTalkConfigInfo configInfo = new DingTalkConfigInfo();
        configInfo.setEnabled(dingTalkConfig.isEnabled());
        configInfo.setValid(dingTalkConfig.isValid());
        configInfo.setAppKey(dingTalkConfig.getAppKey());
        configInfo.setCorpId(dingTalkConfig.getCorpId());
        configInfo.setInternalMode(dingTalkConfig.isInternalMode());
        configInfo.setThirdPartyMode(dingTalkConfig.isThirdPartyMode());
        configInfo.setCallbackEnabled(dingTalkConfig.isCallbackEnabled());
        configInfo.setApiBaseUrl(dingTalkConfig.getEffectiveApiBaseUrl());
        configInfo.setAuthUrl(dingTalkConfig.getEffectiveAuthUrl());
        
        log.info("DingTalk configuration: enabled={}, valid={}, appKey={}, corpId={}, internalMode={}, thirdPartyMode={}", 
                configInfo.isEnabled(), 
                configInfo.isValid(),
                configInfo.getAppKey(),
                configInfo.getCorpId(),
                configInfo.isInternalMode(),
                configInfo.isThirdPartyMode());
        
        return configInfo;
    }

    /**
     * 钉钉配置信息类
     */
    public static class DingTalkConfigInfo {
        private boolean enabled;
        private boolean valid;
        private String appKey;
        private String corpId;
        private boolean internalMode;
        private boolean thirdPartyMode;
        private boolean callbackEnabled;
        private String apiBaseUrl;
        private String authUrl;

        // Getters and Setters
        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public boolean isValid() {
            return valid;
        }

        public void setValid(boolean valid) {
            this.valid = valid;
        }

        public String getAppKey() {
            return appKey;
        }

        public void setAppKey(String appKey) {
            this.appKey = appKey;
        }

        public String getCorpId() {
            return corpId;
        }

        public void setCorpId(String corpId) {
            this.corpId = corpId;
        }

        public boolean isInternalMode() {
            return internalMode;
        }

        public void setInternalMode(boolean internalMode) {
            this.internalMode = internalMode;
        }

        public boolean isThirdPartyMode() {
            return thirdPartyMode;
        }

        public void setThirdPartyMode(boolean thirdPartyMode) {
            this.thirdPartyMode = thirdPartyMode;
        }

        public boolean isCallbackEnabled() {
            return callbackEnabled;
        }

        public void setCallbackEnabled(boolean callbackEnabled) {
            this.callbackEnabled = callbackEnabled;
        }

        public String getApiBaseUrl() {
            return apiBaseUrl;
        }

        public void setApiBaseUrl(String apiBaseUrl) {
            this.apiBaseUrl = apiBaseUrl;
        }

        public String getAuthUrl() {
            return authUrl;
        }

        public void setAuthUrl(String authUrl) {
            this.authUrl = authUrl;
        }

        @Override
        public String toString() {
            return String.format("DingTalkConfigInfo{enabled=%s, valid=%s, appKey='%s', corpId='%s', internalMode=%s, thirdPartyMode=%s, callbackEnabled=%s, apiBaseUrl='%s', authUrl='%s'}",
                    enabled, valid, appKey, corpId, internalMode, thirdPartyMode, callbackEnabled, apiBaseUrl, authUrl);
        }
    }
}
