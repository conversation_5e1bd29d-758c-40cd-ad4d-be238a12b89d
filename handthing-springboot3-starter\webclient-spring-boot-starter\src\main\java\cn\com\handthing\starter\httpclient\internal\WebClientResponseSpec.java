package cn.com.handthing.starter.httpclient.internal;

import cn.com.handthing.starter.httpclient.BodySpec;
import cn.com.handthing.starter.httpclient.ResponseSpec;
import cn.com.handthing.starter.httpclient.crypto.ResponseDecryptor;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpStatus;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.lang.NonNull;
import org.springframework.web.reactive.function.client.ClientResponse;
import reactor.core.publisher.Mono;

import java.util.function.Function;
import java.util.function.Predicate;

/**
 * ResponseSpec 接口的 WebClient 实现。
 *
 * <AUTHOR>
 * @since V1.0.0
 */
public final class WebClientResponseSpec implements ResponseSpec {

    private final org.springframework.web.reactive.function.client.WebClient.ResponseSpec webClientResponseSpec;
    private final ResponseDecryptor responseDecryptor;

    public WebClientResponseSpec(
            org.springframework.web.reactive.function.client.WebClient.ResponseSpec webClientResponseSpec,
            ResponseDecryptor responseDecryptor) {
        this.webClientResponseSpec = webClientResponseSpec;
        this.responseDecryptor = responseDecryptor;
    }

    @Override
    @NonNull
    public ResponseSpec onStatus(@NonNull Predicate<HttpStatus> statusPredicate, 
                                @NonNull Function<ClientHttpResponse, Mono<? extends Throwable>> exceptionFunction) {
        
        // 将 ClientHttpResponse 转换为 WebClient 的 ClientResponse
        org.springframework.web.reactive.function.client.WebClient.ResponseSpec newSpec =
            webClientResponseSpec.onStatus(
                httpStatusCode -> statusPredicate.test(HttpStatus.valueOf(httpStatusCode.value())),
                clientResponse -> {
                    // 创建适配器将 WebClient 的 ClientResponse 转换为 ClientHttpResponse
                    ClientHttpResponse adaptedResponse = new WebClientResponseAdapter(clientResponse);
                    return exceptionFunction.apply(adaptedResponse);
                }
            );
        
        return new WebClientResponseSpec(newSpec, responseDecryptor);
    }

    @Override
    @NonNull
    public <T> BodySpec<T> body(@NonNull Class<T> type) {
        return new WebClientBodySpec<>(webClientResponseSpec.bodyToMono(type), responseDecryptor);
    }



    /**
     * 适配器类，将 WebClient 的 ClientResponse 转换为 ClientHttpResponse
     */
    private static class WebClientResponseAdapter implements ClientHttpResponse {
        
        private final ClientResponse clientResponse;
        
        public WebClientResponseAdapter(ClientResponse clientResponse) {
            this.clientResponse = clientResponse;
        }
        
        @Override
        public HttpStatus getStatusCode() {
            return HttpStatus.valueOf(clientResponse.statusCode().value());
        }
        
        @Override
        public String getStatusText() {
            return HttpStatus.valueOf(clientResponse.statusCode().value()).getReasonPhrase();
        }
        
        @Override
        public void close() {
            // WebClient 的 ClientResponse 不需要手动关闭
        }
        
        @Override
        public java.io.InputStream getBody() throws java.io.IOException {
            // 这个方法在我们的使用场景中不会被调用
            // 因为我们主要使用 Mono 方式处理响应体
            throw new UnsupportedOperationException("Use reactive methods to access response body");
        }
        
        @Override
        public org.springframework.http.HttpHeaders getHeaders() {
            return clientResponse.headers().asHttpHeaders();
        }
    }
}
