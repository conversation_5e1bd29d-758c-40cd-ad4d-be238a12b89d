# HandThing Admin 系统设计文档

## 1. 项目概述

### 1.1 项目背景
HandThing Admin 是基于 handthing-springboot3-starter 框架构建的企业级 SaaS 后台管理系统，旨在为企业提供完整的多租户管理、用户权限控制、工作流审批、系统监控等核心功能。

### 1.2 项目目标
- 构建高可用、高性能的多租户 SaaS 管理平台
- 提供完整的企业级功能模块
- 支持多种认证方式和第三方系统集成
- 实现灵活的权限控制和数据隔离
- 提供丰富的监控和运维能力

### 1.3 核心特性
- **多租户架构**: 支持数据隔离和资源配额管理
- **统一认证**: 支持密码、短信、邮箱、OAuth2.0、SAML、OIDC等多种认证方式
- **细粒度权限**: 支持菜单权限、操作权限、数据权限的精细化控制
- **工作流引擎**: 可视化流程设计和审批管理
- **应用生态**: 支持第三方应用接入和API开放平台
- **实时监控**: 全方位的系统监控和告警机制

## 2. 技术架构

### 2.1 技术栈选型

#### 后端技术栈
- **框架**: Spring Boot 3.2+ + handthing-springboot3-starter
- **数据库**: MySQL 8.0 (主库) + Redis 7.x (缓存)
- **ORM**: MyBatis-Plus 3.5+
- **认证**: handthing-auth (JWT + OAuth2.0 + SAML + OIDC)
- **缓存**: level-cache-spring-boot-starter (多级缓存)
- **消息队列**: RabbitMQ 3.12+
- **任务调度**: XXL-JOB 2.4+
- **文件存储**: 支持本地、阿里云OSS、AWS S3
- **API文档**: knife4j-spring-boot-starter

#### 前端技术栈
- **框架**: Vue 3.3+ + TypeScript
- **UI组件**: Element Plus 2.4+
- **状态管理**: Pinia
- **路由**: Vue Router 4.x
- **构建工具**: Vite 4.x
- **图表**: ECharts 5.x
- **流程设计**: BPMN.js

#### 运维技术栈
- **容器化**: Docker + Kubernetes
- **服务网格**: Istio (可选)
- **监控**: Prometheus + Grafana + AlertManager
- **日志**: ELK Stack (Elasticsearch + Logstash + Kibana)
- **链路追踪**: SkyWalking
- **CI/CD**: GitLab CI/CD + Harbor

### 2.2 系统架构图

```
┌─────────────────────────────────────────────────────────────┐
│                        前端层 (Frontend)                      │
├─────────────────────────────────────────────────────────────┤
│  Vue 3 + Element Plus + TypeScript + Vite                  │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                      API网关层 (Gateway)                     │
├─────────────────────────────────────────────────────────────┤
│  Spring Cloud Gateway + 限流 + 熔断 + 认证                   │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                      应用服务层 (Services)                    │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │ 认证授权服务 │ │ 租户管理服务 │ │ 用户管理服务 │ │ 工作流  │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ │ 服务    │ │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ └─────────┘ │
│  │ 消息中心服务 │ │ 文件存储服务 │ │ 监控告警服务 │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                      数据存储层 (Storage)                     │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │   MySQL     │ │    Redis    │ │  RabbitMQ   │ │ 文件存储 │ │
│  │   (主库)     │ │   (缓存)     │ │  (消息队列)  │ │ (OSS/S3)│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 2.3 分层架构设计

#### 表现层 (Presentation Layer)
- **Web Controller**: 处理HTTP请求，参数验证，响应格式化
- **API接口**: RESTful API设计，统一响应格式
- **异常处理**: 全局异常处理，错误码统一管理
- **参数验证**: 使用Bean Validation进行参数校验

#### 应用服务层 (Application Service Layer)
- **业务编排**: 协调多个领域服务完成复杂业务流程
- **事务管理**: 声明式事务管理，分布式事务处理
- **权限校验**: 基于角色和数据权限的访问控制
- **数据转换**: DTO与领域对象之间的转换

#### 领域服务层 (Domain Service Layer)
- **核心业务逻辑**: 实现业务规则和领域逻辑
- **领域模型**: 充血模型，封装业务行为
- **领域事件**: 发布和处理领域事件
- **业务规则**: 复杂业务规则的封装

#### 基础设施层 (Infrastructure Layer)
- **数据访问**: MyBatis-Plus数据访问，多租户数据隔离
- **缓存管理**: 多级缓存策略，缓存一致性保证
- **外部服务**: 第三方API调用，消息队列集成
- **配置管理**: 动态配置，环境隔离

## 3. 模块设计

### 3.1 模块分组架构

```
handthing-admin/
├── 🔑 认证授权模块组 (Auth Module Group)
│   ├── 统一用户认证 (Unified Authentication)
│   └── 用户与角色权限 (User, Role & Permission)
├── 🏢 租户管理模块组 (Tenant Module Group)
│   ├── 租户管理 (Tenant Management)
│   └── 组织架构管理 (Organizational Structure)
├── ⚙️ 系统管理模块组 (System Module Group)
│   ├── 菜单管理 (Menu Management)
│   ├── 字典与标签管理 (Dictionary & Tag Management)
│   └── 系统管理 (System Settings)
├── 🔄 工作流模块组 (Workflow Module Group)
│   ├── 审批工作流 (Approval Workflow)
│   └── 任务调度中心 (Task Scheduler)
├── 💬 消息通信模块组 (Communication Module Group)
│   ├── 消息中心 (Message Center)
│   └── 文件存储服务 (File Storage Service)
├── 🔗 应用集成模块组 (Integration Module Group)
│   ├── 应用管理 (Application Management)
│   ├── 开放平台 (Open Platform)
│   └── 数据同步 (Data Synchronization)
├── 📊 运营监控模块组 (Operation Module Group)
│   ├── 审计与日志 (Audit & Logging)
│   ├── 系统监控与告警 (System Monitoring & Alerting)
│   └── 计费与订阅管理 (Billing & Subscription)
└── 👤 个人化模块组 (Personalization Module Group)
    └── 个人中心 (Personal Center)
```

### 3.2 项目结构设计

```
handthing-admin/
├── handthing-admin-api/              # API接口层
│   ├── auth-api/                     # 认证授权API
│   ├── tenant-api/                   # 租户管理API
│   ├── user-api/                     # 用户管理API
│   ├── system-api/                   # 系统管理API
│   ├── workflow-api/                 # 工作流API
│   ├── message-api/                  # 消息中心API
│   ├── integration-api/              # 应用集成API
│   ├── operation-api/                # 运营监控API
│   └── personal-api/                 # 个人中心API
├── handthing-admin-application/      # 应用服务层
│   ├── auth-application/             # 认证授权应用服务
│   ├── tenant-application/           # 租户管理应用服务
│   ├── user-application/             # 用户管理应用服务
│   ├── system-application/           # 系统管理应用服务
│   ├── workflow-application/         # 工作流应用服务
│   ├── message-application/          # 消息中心应用服务
│   ├── integration-application/      # 应用集成应用服务
│   ├── operation-application/        # 运营监控应用服务
│   └── personal-application/         # 个人中心应用服务
├── handthing-admin-domain/           # 领域层
│   ├── auth-domain/                  # 认证授权领域
│   ├── tenant-domain/                # 租户管理领域
│   ├── user-domain/                  # 用户管理领域
│   ├── system-domain/                # 系统管理领域
│   ├── workflow-domain/              # 工作流领域
│   ├── message-domain/               # 消息中心领域
│   ├── integration-domain/           # 应用集成领域
│   ├── operation-domain/             # 运营监控领域
│   └── personal-domain/              # 个人中心领域
├── handthing-admin-infrastructure/   # 基础设施层
│   ├── database/                     # 数据库访问
│   ├── cache/                        # 缓存管理
│   ├── message/                      # 消息队列
│   ├── storage/                      # 文件存储
│   ├── external/                     # 外部服务
│   └── config/                       # 配置管理
├── handthing-admin-web/              # Web启动模块
│   ├── controller/                   # 控制器
│   ├── config/                       # 配置类
│   ├── filter/                       # 过滤器
│   ├── interceptor/                  # 拦截器
│   └── HandthingAdminApplication.java # 启动类
└── handthing-admin-common/           # 公共模块
    ├── constant/                     # 常量定义
    ├── enums/                        # 枚举类型
    ├── exception/                    # 异常定义
    ├── util/                         # 工具类
    └── dto/                          # 数据传输对象
```

## 4. 数据库设计

### 4.1 数据库设计原则
- **多租户隔离**: 所有业务表包含tenant_id字段进行逻辑隔离
- **软删除**: 重要数据采用软删除策略，保留deleted字段
- **审计字段**: 包含create_time、update_time、create_by、update_by
- **版本控制**: 重要表包含version字段支持乐观锁
- **索引优化**: 基于查询场景设计合理的索引策略

### 4.2 核心表结构设计

#### 租户相关表
```sql
-- 租户信息表
CREATE TABLE tenant_info (
    id BIGINT PRIMARY KEY,
    tenant_code VARCHAR(50) UNIQUE NOT NULL COMMENT '租户编码',
    tenant_name VARCHAR(100) NOT NULL COMMENT '租户名称',
    company_name VARCHAR(200) COMMENT '公司名称',
    contact_name VARCHAR(50) COMMENT '联系人姓名',
    contact_phone VARCHAR(20) COMMENT '联系人电话',
    contact_email VARCHAR(100) COMMENT '联系人邮箱',
    package_id BIGINT COMMENT '套餐ID',
    status TINYINT DEFAULT 1 COMMENT '状态:1正常,2禁用,3过期',
    expire_time DATETIME COMMENT '到期时间',
    max_users INT DEFAULT 0 COMMENT '最大用户数',
    max_storage BIGINT DEFAULT 0 COMMENT '最大存储空间(字节)',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    create_by BIGINT,
    update_by BIGINT,
    deleted TINYINT DEFAULT 0,
    version INT DEFAULT 1
);

-- 租户套餐表
CREATE TABLE tenant_package (
    id BIGINT PRIMARY KEY,
    package_name VARCHAR(100) NOT NULL COMMENT '套餐名称',
    package_code VARCHAR(50) UNIQUE NOT NULL COMMENT '套餐编码',
    description TEXT COMMENT '套餐描述',
    price_monthly DECIMAL(10,2) COMMENT '月付价格',
    price_yearly DECIMAL(10,2) COMMENT '年付价格',
    max_users INT DEFAULT 0 COMMENT '最大用户数',
    max_storage BIGINT DEFAULT 0 COMMENT '最大存储空间',
    max_api_calls INT DEFAULT 0 COMMENT '每月API调用次数',
    permissions JSON COMMENT '权限配置',
    status TINYINT DEFAULT 1 COMMENT '状态:1启用,0禁用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT DEFAULT 0
);
```

#### 用户权限相关表
```sql
-- 用户信息表
CREATE TABLE user_info (
    id BIGINT PRIMARY KEY,
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    username VARCHAR(50) NOT NULL COMMENT '用户名',
    nickname VARCHAR(50) COMMENT '昵称',
    email VARCHAR(100) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    avatar VARCHAR(500) COMMENT '头像URL',
    password VARCHAR(255) COMMENT '密码',
    salt VARCHAR(50) COMMENT '盐值',
    status TINYINT DEFAULT 1 COMMENT '状态:1正常,0禁用',
    last_login_time DATETIME COMMENT '最后登录时间',
    last_login_ip VARCHAR(50) COMMENT '最后登录IP',
    mfa_enabled TINYINT DEFAULT 0 COMMENT '是否启用MFA',
    mfa_secret VARCHAR(100) COMMENT 'MFA密钥',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    create_by BIGINT,
    update_by BIGINT,
    deleted TINYINT DEFAULT 0,
    version INT DEFAULT 1,
    UNIQUE KEY uk_tenant_username (tenant_id, username),
    INDEX idx_tenant_phone (tenant_id, phone),
    INDEX idx_tenant_email (tenant_id, email)
);

-- 角色信息表
CREATE TABLE role_info (
    id BIGINT PRIMARY KEY,
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    role_code VARCHAR(50) NOT NULL COMMENT '角色编码',
    role_name VARCHAR(100) NOT NULL COMMENT '角色名称',
    description TEXT COMMENT '角色描述',
    data_scope TINYINT DEFAULT 1 COMMENT '数据权限:1全部,2本部门,3本部门及下级,4仅本人,5自定义',
    status TINYINT DEFAULT 1 COMMENT '状态:1启用,0禁用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    create_by BIGINT,
    update_by BIGINT,
    deleted TINYINT DEFAULT 0,
    UNIQUE KEY uk_tenant_code (tenant_id, role_code)
);
```
