# HandThing Admin 系统设计文档

## 1. 项目概述

### 1.1 项目背景
HandThing Admin 是基于 handthing-springboot3-starter 框架构建的企业级 SaaS 后台管理系统，旨在为企业提供完整的多租户管理、用户权限控制、工作流审批、系统监控等核心功能。

### 1.2 项目目标
- 构建高可用、高性能的多租户 SaaS 管理平台
- 提供完整的企业级功能模块
- 支持多种认证方式和第三方系统集成
- 实现灵活的权限控制和数据隔离
- 提供丰富的监控和运维能力

### 1.3 核心特性
- **多租户架构**: 支持数据隔离和资源配额管理
- **统一认证**: 支持密码、短信、邮箱、OAuth2.0、SAML、OIDC等多种认证方式
- **细粒度权限**: 支持菜单权限、操作权限、数据权限的精细化控制
- **工作流引擎**: 可视化流程设计和审批管理
- **应用生态**: 支持第三方应用接入和API开放平台
- **实时监控**: 全方位的系统监控和告警机制

## 2. 技术架构

### 2.1 技术栈选型

#### 后端技术栈
- **父项目**: starter-parent (来自handthing-springboot3-starter，统一依赖管理)
- **框架**: Spring Boot 3.2+ + handthing-springboot3-starter
- **数据库**: MySQL 8.0 (主库) + Redis 7.x (缓存)
- **ORM**: datalayer-spring-boot-starter (来自handthing-springboot3-starter，集成MyBatis-Plus)
- **多租户**: tenant-datalayer-spring-boot-starter (多租户数据隔离)
- **数据权限**: dataauth-datalayer-spring-boot-starter (数据权限控制)
- **认证**: handthing-auth (JWT + OAuth2.0 + SAML + OIDC)
- **缓存**: level-cache-spring-boot-starter (多级缓存)
- **ID生成**: id-spring-boot-starter (分布式ID生成)
- **HTTP客户端**: webclient-spring-boot-starter (WebClient实现)
- **加密**: crypto-spring-boot-starter (加密解密工具)
- **消息队列**: RabbitMQ 3.12+
- **任务调度**: XXL-JOB 2.4+
- **文件存储**: 支持本地、阿里云OSS、AWS S3
- **API文档**: knife4j-spring-boot-starter

#### 前端技术栈
- **框架**: Vue 3.0+ + TypeScript
- **构建工具**: Vite 6.x
- **UI组件**: Ant Design Vue 4.x
- **状态管理**: Pinia
- **路由**: Vue Router 4.x
- **图表**: ECharts 5.x
- **流程设计**: BPMN.js
- **HTTP客户端**: Axios
- **工具库**: Lodash-es

#### 运维技术栈
- **容器化**: Docker + Kubernetes
- **服务网格**: Istio (可选)
- **监控**: Prometheus + Grafana + AlertManager
- **日志**: ELK Stack (Elasticsearch + Logstash + Kibana)
- **链路追踪**: SkyWalking
- **CI/CD**: GitLab CI/CD + Harbor

### 2.2 系统架构图

```
┌─────────────────────────────────────────────────────────────┐
│                        前端层 (Frontend)                      │
├─────────────────────────────────────────────────────────────┤
│  Vue 3 + Element Plus + TypeScript + Vite                  │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                      API网关层 (Gateway)                     │
├─────────────────────────────────────────────────────────────┤
│  Spring Cloud Gateway + 限流 + 熔断 + 认证                   │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                      应用服务层 (Services)                    │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │ 认证授权服务 │ │ 租户管理服务 │ │ 用户管理服务 │ │ 工作流  │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ │ 服务    │ │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ └─────────┘ │
│  │ 消息中心服务 │ │ 文件存储服务 │ │ 监控告警服务 │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                      数据存储层 (Storage)                     │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │   MySQL     │ │    Redis    │ │  RabbitMQ   │ │ 文件存储 │ │
│  │   (主库)     │ │   (缓存)     │ │  (消息队列)  │ │ (OSS/S3)│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 2.3 分层架构设计

#### 表现层 (Presentation Layer)
- **Web Controller**: 处理HTTP请求，参数验证，响应格式化
- **API接口**: RESTful API设计，统一响应格式
- **异常处理**: 全局异常处理，错误码统一管理
- **参数验证**: 使用Bean Validation进行参数校验

#### 应用服务层 (Application Service Layer)
- **业务编排**: 协调多个领域服务完成复杂业务流程
- **事务管理**: 声明式事务管理，分布式事务处理
- **权限校验**: 基于角色和数据权限的访问控制
- **数据转换**: DTO与领域对象之间的转换

#### 领域服务层 (Domain Service Layer)
- **核心业务逻辑**: 实现业务规则和领域逻辑
- **领域模型**: 充血模型，封装业务行为
- **领域事件**: 发布和处理领域事件
- **业务规则**: 复杂业务规则的封装

#### 基础设施层 (Infrastructure Layer)
- **数据访问**: 基于datalayer-spring-boot-starter，集成MyBatis-Plus，支持多租户数据隔离
- **缓存管理**: 基于level-cache-spring-boot-starter，多级缓存策略，缓存一致性保证
- **ID生成**: 基于id-spring-boot-starter，支持雪花算法和UUID生成
- **加密服务**: 基于crypto-spring-boot-starter，提供AES等加密算法
- **HTTP客户端**: 基于webclient-spring-boot-starter，统一外部API调用
- **外部服务**: 第三方API调用，消息队列集成
- **配置管理**: 动态配置，环境隔离

#### handthing-core工具类使用

基于handthing-core替代hutool，提供常用工具类：

```java
// 1. JSON工具类 - 替代hutool的JSONUtil
import cn.com.handthing.core.util.JsonUtils;

@Service
public class UserService {
    public void saveUserConfig(UserInfo user, Map<String, Object> config) {
        // JSON序列化
        String configJson = JsonUtils.toJsonString(config);
        user.setConfig(configJson);

        // JSON反序列化
        Map<String, Object> parsedConfig = JsonUtils.parseObject(configJson, Map.class);
    }
}

// 2. 字符串工具类 - 替代hutool的StrUtil
import cn.com.handthing.core.util.StringUtils;

@Service
public class ValidationService {
    public boolean validateUser(UserInfo user) {
        // 字符串判空
        if (StringUtils.isEmpty(user.getUsername())) {
            return false;
        }

        // 字符串格式化
        String message = StringUtils.format("用户{}登录成功", user.getUsername());

        // 驼峰转下划线
        String tableName = StringUtils.camelToUnderscore("userInfo"); // user_info

        return true;
    }
}

// 3. 日期工具类 - 替代hutool的DateUtil
import cn.com.handthing.core.util.DateUtils;

@Service
public class ReportService {
    public void generateReport() {
        // 日期格式化
        String dateStr = DateUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss");

        // 日期解析
        Date date = DateUtils.parse("2024-01-01", "yyyy-MM-dd");

        // 日期计算
        Date tomorrow = DateUtils.addDays(new Date(), 1);

        // 获取时间戳
        long timestamp = DateUtils.getTimestamp();
    }
}

// 4. 加密工具类 - 替代hutool的SecureUtil
import cn.com.handthing.core.util.CryptoUtils;

@Service
public class SecurityService {
    public String encryptPassword(String password) {
        // MD5加密
        String md5 = CryptoUtils.md5(password);

        // SHA256加密
        String sha256 = CryptoUtils.sha256(password);

        // Base64编码
        String base64 = CryptoUtils.base64Encode(password.getBytes());

        return sha256;
    }
}

// 5. 集合工具类 - handthing-admin-common中补充
package cn.com.handthing.admin.common.util;

import java.util.*;
import java.util.stream.Collectors;

public class CollectionUtils {

    public static boolean isEmpty(Collection<?> collection) {
        return collection == null || collection.isEmpty();
    }

    public static boolean isNotEmpty(Collection<?> collection) {
        return !isEmpty(collection);
    }

    public static <T> List<T> emptyIfNull(List<T> list) {
        return list == null ? Collections.emptyList() : list;
    }

    public static <T> Set<T> emptyIfNull(Set<T> set) {
        return set == null ? Collections.emptySet() : set;
    }

    public static <T> List<List<T>> partition(List<T> list, int size) {
        if (isEmpty(list) || size <= 0) {
            return Collections.emptyList();
        }

        return IntStream.range(0, (list.size() + size - 1) / size)
                .mapToObj(i -> list.subList(i * size, Math.min((i + 1) * size, list.size())))
                .collect(Collectors.toList());
    }
}

// 6. Bean工具类 - handthing-admin-common中补充
package cn.com.handthing.admin.common.util;

import org.springframework.beans.BeanUtils;
import java.util.List;
import java.util.stream.Collectors;

public class BeanCopyUtils {

    public static <T> T copyProperties(Object source, Class<T> targetClass) {
        if (source == null) {
            return null;
        }
        try {
            T target = targetClass.getDeclaredConstructor().newInstance();
            BeanUtils.copyProperties(source, target);
            return target;
        } catch (Exception e) {
            throw new RuntimeException("Bean copy failed", e);
        }
    }

    public static <T> List<T> copyList(List<?> sourceList, Class<T> targetClass) {
        if (CollectionUtils.isEmpty(sourceList)) {
            return Collections.emptyList();
        }
        return sourceList.stream()
                .map(source -> copyProperties(source, targetClass))
                .collect(Collectors.toList());
    }
}

// 7. 验证工具类 - handthing-admin-common中补充
package cn.com.handthing.admin.common.util;

import java.util.regex.Pattern;

public class ValidateUtils {

    private static final Pattern EMAIL_PATTERN =
        Pattern.compile("^[A-Za-z0-9+_.-]+@([A-Za-z0-9.-]+\\.[A-Za-z]{2,})$");

    private static final Pattern PHONE_PATTERN =
        Pattern.compile("^1[3-9]\\d{9}$");

    public static boolean isEmail(String email) {
        return StringUtils.isNotEmpty(email) && EMAIL_PATTERN.matcher(email).matches();
    }

    public static boolean isPhone(String phone) {
        return StringUtils.isNotEmpty(phone) && PHONE_PATTERN.matcher(phone).matches();
    }

    public static boolean isIdCard(String idCard) {
        if (StringUtils.isEmpty(idCard)) {
            return false;
        }
        return idCard.length() == 18 && idCard.matches("^\\d{17}[\\dXx]$");
    }
}
```

#### handthing-springboot3-starter组件使用

##### 数据层组件
```java
// 1. 基础数据层 - datalayer-spring-boot-starter
// 自动配置MyBatis-Plus，提供BaseEntity基类
@Entity
@TableName("user_info")
public class UserInfo extends BaseEntity {
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @IdSetter  // 使用ID生成器自动设置ID
    private String userCode;

    // 其他字段...
}

// 2. 多租户数据层 - tenant-datalayer-spring-boot-starter
// 自动添加租户ID条件，实现数据隔离
@Mapper
public interface UserMapper extends BaseMapper<UserInfo> {
    // 查询时自动添加 tenant_id = ? 条件
    List<UserInfo> selectByDeptId(Long deptId);
}

// 3. 数据权限层 - dataauth-datalayer-spring-boot-starter
// 基于用户角色自动过滤数据
@Service
public class UserService {
    @DataAuth(type = DataAuthType.DEPT) // 只能查看本部门数据
    public List<UserInfo> getUserList() {
        return userMapper.selectList(null);
    }
}
```

##### 缓存组件
```java
// 多级缓存 - level-cache-spring-boot-starter
@Service
public class UserService {
    @Autowired
    private CacheService cacheService;

    public UserInfo getUserById(Long id) {
        String key = "user:" + id;
        return cacheService.get(key, UserInfo.class, () -> {
            // 缓存未命中时的数据加载逻辑
            return userMapper.selectById(id);
        });
    }
}
```

##### 认证组件
```java
// 认证授权 - handthing-auth
@RestController
public class AuthController {
    @Autowired
    private AuthService authService;

    @PostMapping("/login")
    public Result<LoginResponse> login(@RequestBody LoginRequest request) {
        // 支持多种登录方式：密码、短信、OAuth2.0等
        return authService.login(request);
    }
}

// 权限控制
@RestController
@PreAuthorize("hasRole('ADMIN')")
public class UserController {
    @GetMapping("/users")
    @PreAuthorize("hasPermission('user:read')")
    public Result<List<UserInfo>> getUsers() {
        return userService.getUsers();
    }
}
```

##### ID生成组件
```java
// ID生成器 - id-spring-boot-starter
@Service
public class UserService {
    @Autowired
    private IdGenerator idGenerator;

    public void createUser(UserInfo user) {
        // 自动生成分布式ID
        user.setId(idGenerator.nextId());
        // 或使用注解自动设置
        // @IdSetter 注解会自动调用ID生成器
        userMapper.insert(user);
    }
}
```

##### HTTP客户端组件
```java
// HTTP客户端 - webclient-spring-boot-starter
@Service
public class ThirdPartyService {
    @Autowired
    private HttpClientService httpClientService;

    public String callExternalApi(String url, Object request) {
        return httpClientService.post(url, request, String.class);
    }
}
```

##### 加密组件
```java
// 加密服务 - crypto-spring-boot-starter
@Service
public class UserService {
    @Autowired
    private CryptoService cryptoService;

    public void saveUser(UserInfo user) {
        // 敏感信息加密存储
        String encryptedPhone = cryptoService.encrypt(user.getPhone());
        user.setPhone(encryptedPhone);
        userMapper.insert(user);
    }

    public UserInfo getUser(Long id) {
        UserInfo user = userMapper.selectById(id);
        // 解密敏感信息
        String decryptedPhone = cryptoService.decrypt(user.getPhone());
        user.setPhone(decryptedPhone);
        return user;
    }
}
```

## 3. 模块设计

### 3.1 模块分组架构

```
handthing-admin/
├── 🔑 认证授权模块组 (Auth Module Group)
│   ├── 统一用户认证 (Unified Authentication)
│   └── 用户与角色权限 (User, Role & Permission)
├── 🏢 租户管理模块组 (Tenant Module Group)
│   ├── 租户管理 (Tenant Management)
│   └── 组织架构管理 (Organizational Structure)
├── ⚙️ 系统管理模块组 (System Module Group)
│   ├── 菜单管理 (Menu Management)
│   ├── 字典与标签管理 (Dictionary & Tag Management)
│   └── 系统管理 (System Settings)
├── 🔄 工作流模块组 (Workflow Module Group)
│   ├── 审批工作流 (Approval Workflow)
│   └── 任务调度中心 (Task Scheduler)
├── 💬 消息通信模块组 (Communication Module Group)
│   ├── 消息中心 (Message Center)
│   └── 文件存储服务 (File Storage Service)
├── 🔗 应用集成模块组 (Integration Module Group)
│   ├── 应用管理 (Application Management)
│   ├── 开放平台 (Open Platform)
│   └── 数据同步 (Data Synchronization)
├── 📊 运营监控模块组 (Operation Module Group)
│   ├── 审计与日志 (Audit & Logging)
│   ├── 系统监控与告警 (System Monitoring & Alerting)
│   └── 计费与订阅管理 (Billing & Subscription)
└── 👤 个人化模块组 (Personalization Module Group)
    └── 个人中心 (Personal Center)
```

### 3.2 项目结构设计

#### Maven项目结构（基于业务模块划分）
```
handthing-admin/
├── pom.xml                           # 父项目POM，继承starter-parent
├── handthing-admin-common/           # 公共模块
│   ├── pom.xml
│   ├── constant/                     # 常量定义
│   ├── enums/                        # 枚举类型
│   ├── exception/                    # 异常定义
│   ├── util/                         # 工具类(替代hutool)
│   └── dto/                          # 公共数据传输对象
├── handthing-admin-auth/             # 认证授权模块
│   ├── pom.xml
│   ├── auth-api/                     # 认证API接口
│   ├── auth-domain/                  # 认证领域模型
│   ├── auth-application/             # 认证应用服务
│   └── auth-infrastructure/          # 认证基础设施
├── handthing-admin-tenant/           # 租户管理模块
│   ├── pom.xml
│   ├── tenant-api/                   # 租户API接口
│   ├── tenant-domain/                # 租户领域模型
│   ├── tenant-application/           # 租户应用服务
│   └── tenant-infrastructure/        # 租户基础设施
├── handthing-admin-user/             # 用户管理模块
│   ├── pom.xml
│   ├── user-api/                     # 用户API接口
│   ├── user-domain/                  # 用户领域模型
│   ├── user-application/             # 用户应用服务
│   └── user-infrastructure/          # 用户基础设施
├── handthing-admin-system/           # 系统管理模块
│   ├── pom.xml
│   ├── system-api/                   # 系统API接口
│   ├── system-domain/                # 系统领域模型
│   ├── system-application/           # 系统应用服务
│   └── system-infrastructure/        # 系统基础设施
├── handthing-admin-workflow/         # 工作流模块
│   ├── pom.xml
│   ├── workflow-api/                 # 工作流API接口
│   ├── workflow-domain/              # 工作流领域模型
│   ├── workflow-application/         # 工作流应用服务
│   └── workflow-infrastructure/      # 工作流基础设施
├── handthing-admin-message/          # 消息中心模块
│   ├── pom.xml
│   ├── message-api/                  # 消息API接口
│   ├── message-domain/               # 消息领域模型
│   ├── message-application/          # 消息应用服务
│   └── message-infrastructure/       # 消息基础设施
├── handthing-admin-integration/      # 应用集成模块
│   ├── pom.xml
│   ├── integration-api/              # 集成API接口
│   ├── integration-domain/           # 集成领域模型
│   ├── integration-application/      # 集成应用服务
│   └── integration-infrastructure/   # 集成基础设施
├── handthing-admin-operation/        # 运营监控模块
│   ├── pom.xml
│   ├── operation-api/                # 运营API接口
│   ├── operation-domain/             # 运营领域模型
│   ├── operation-application/        # 运营应用服务
│   └── operation-infrastructure/     # 运营基础设施
└── handthing-admin-web/              # Web启动模块
    ├── pom.xml
    ├── controller/                   # 控制器
    ├── config/                       # 配置类
    ├── filter/                       # 过滤器
    ├── interceptor/                  # 拦截器
    └── HandthingAdminApplication.java # 启动类
```

#### Maven依赖配置

##### 父项目POM (handthing-admin/pom.xml)
```xml
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <!-- 继承handthing-springboot3-starter的父项目 -->
    <parent>
        <groupId>cn.com.handthing.springboot3.starter</groupId>
        <artifactId>starter-parent</artifactId>
        <version>${revision}</version>
        <relativePath/>
    </parent>

    <groupId>cn.com.handthing</groupId>
    <artifactId>handthing-admin</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>HandThing Admin</name>
    <description>企业级SaaS后台管理系统</description>

    <modules>
        <module>handthing-admin-common</module>
        <module>handthing-admin-auth</module>
        <module>handthing-admin-tenant</module>
        <module>handthing-admin-user</module>
        <module>handthing-admin-system</module>
        <module>handthing-admin-workflow</module>
        <module>handthing-admin-message</module>
        <module>handthing-admin-integration</module>
        <module>handthing-admin-operation</module>
        <module>handthing-admin-web</module>
    </modules>

    <properties>
        <java.version>17</java.version>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <!-- 内部模块版本 -->
        <handthing-admin.version>1.0.0-SNAPSHOT</handthing-admin.version>

        <!-- 第三方依赖版本 -->
        <xxl-job.version>2.4.0</xxl-job.version>
        <flowable.version>7.0.0</flowable.version>
        <minio.version>8.5.7</minio.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- 内部模块依赖管理 -->
            <dependency>
                <groupId>cn.com.handthing</groupId>
                <artifactId>handthing-admin-common</artifactId>
                <version>${handthing-admin.version}</version>
            </dependency>

            <!-- 业务模块API依赖管理 -->
            <dependency>
                <groupId>cn.com.handthing</groupId>
                <artifactId>handthing-admin-auth-api</artifactId>
                <version>${handthing-admin.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.handthing</groupId>
                <artifactId>handthing-admin-tenant-api</artifactId>
                <version>${handthing-admin.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.handthing</groupId>
                <artifactId>handthing-admin-user-api</artifactId>
                <version>${handthing-admin.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.handthing</groupId>
                <artifactId>handthing-admin-system-api</artifactId>
                <version>${handthing-admin.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.handthing</groupId>
                <artifactId>handthing-admin-workflow-api</artifactId>
                <version>${handthing-admin.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.handthing</groupId>
                <artifactId>handthing-admin-message-api</artifactId>
                <version>${handthing-admin.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.handthing</groupId>
                <artifactId>handthing-admin-integration-api</artifactId>
                <version>${handthing-admin.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.handthing</groupId>
                <artifactId>handthing-admin-operation-api</artifactId>
                <version>${handthing-admin.version}</version>
            </dependency>

            <!-- 第三方依赖管理 -->
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl-job.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-spring-boot-starter</artifactId>
                <version>${flowable.version}</version>
            </dependency>
            <dependency>
                <groupId>io.minio</groupId>
                <artifactId>minio</artifactId>
                <version>${minio.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>
```

##### 公共模块POM (handthing-admin-common/pom.xml)
```xml
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>cn.com.handthing</groupId>
        <artifactId>handthing-admin</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>handthing-admin-common</artifactId>
    <name>HandThing Admin Common</name>
    <description>公共模块，包含工具类、常量、异常等</description>

    <dependencies>
        <!-- handthing-core 核心工具库 -->
        <dependency>
            <groupId>cn.com.handthing.springboot3.starter</groupId>
            <artifactId>handthing-core</artifactId>
        </dependency>

        <!-- Spring Boot Starter -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>

        <!-- Validation -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <!-- Jackson -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>

        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
    </dependencies>
</project>
```

##### 认证模块POM (handthing-admin-auth/pom.xml)
```xml
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>cn.com.handthing</groupId>
        <artifactId>handthing-admin</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>handthing-admin-auth</artifactId>
    <packaging>pom</packaging>
    <name>HandThing Admin Auth</name>
    <description>认证授权模块</description>

    <modules>
        <module>auth-api</module>
        <module>auth-domain</module>
        <module>auth-application</module>
        <module>auth-infrastructure</module>
    </modules>

    <dependencies>
        <!-- 公共模块 -->
        <dependency>
            <groupId>cn.com.handthing</groupId>
            <artifactId>handthing-admin-common</artifactId>
        </dependency>

        <!-- handthing认证starter -->
        <dependency>
            <groupId>cn.com.handthing.springboot3.starter</groupId>
            <artifactId>auth-spring-boot-starter</artifactId>
        </dependency>

        <!-- 数据层starter -->
        <dependency>
            <groupId>cn.com.handthing.springboot3.starter</groupId>
            <artifactId>datalayer-spring-boot-starter</artifactId>
        </dependency>

        <!-- 缓存starter -->
        <dependency>
            <groupId>cn.com.handthing.springboot3.starter</groupId>
            <artifactId>level-cache-spring-boot-starter</artifactId>
        </dependency>
    </dependencies>
</project>
```

##### 租户模块POM (handthing-admin-tenant/pom.xml)
```xml
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>cn.com.handthing</groupId>
        <artifactId>handthing-admin</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>handthing-admin-tenant</artifactId>
    <packaging>pom</packaging>
    <name>HandThing Admin Tenant</name>
    <description>租户管理模块</description>

    <modules>
        <module>tenant-api</module>
        <module>tenant-domain</module>
        <module>tenant-application</module>
        <module>tenant-infrastructure</module>
    </modules>

    <dependencies>
        <!-- 公共模块 -->
        <dependency>
            <groupId>cn.com.handthing</groupId>
            <artifactId>handthing-admin-common</artifactId>
        </dependency>

        <!-- 认证模块API -->
        <dependency>
            <groupId>cn.com.handthing</groupId>
            <artifactId>handthing-admin-auth-api</artifactId>
        </dependency>

        <!-- 多租户数据层starter -->
        <dependency>
            <groupId>cn.com.handthing.springboot3.starter</groupId>
            <artifactId>tenant-datalayer-spring-boot-starter</artifactId>
        </dependency>

        <!-- 数据层starter -->
        <dependency>
            <groupId>cn.com.handthing.springboot3.starter</groupId>
            <artifactId>datalayer-spring-boot-starter</artifactId>
        </dependency>
    </dependencies>
</project>
```

##### Web启动模块POM (handthing-admin-web/pom.xml)
```xml
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>cn.com.handthing</groupId>
        <artifactId>handthing-admin</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>handthing-admin-web</artifactId>
    <name>HandThing Admin Web</name>
    <description>Web启动模块</description>

    <dependencies>
        <!-- 业务模块依赖 -->
        <dependency>
            <groupId>cn.com.handthing</groupId>
            <artifactId>handthing-admin-auth</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.com.handthing</groupId>
            <artifactId>handthing-admin-tenant</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.com.handthing</groupId>
            <artifactId>handthing-admin-user</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.com.handthing</groupId>
            <artifactId>handthing-admin-system</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.com.handthing</groupId>
            <artifactId>handthing-admin-workflow</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.com.handthing</groupId>
            <artifactId>handthing-admin-message</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.com.handthing</groupId>
            <artifactId>handthing-admin-integration</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.com.handthing</groupId>
            <artifactId>handthing-admin-operation</artifactId>
        </dependency>

        <!-- handthing-springboot3-starter 依赖 -->
        <!-- API文档starter -->
        <dependency>
            <groupId>cn.com.handthing.springboot3.starter</groupId>
            <artifactId>knife4j-spring-boot-starter</artifactId>
        </dependency>

        <!-- 分布式日志starter -->
        <dependency>
            <groupId>cn.com.handthing.springboot3.starter</groupId>
            <artifactId>distributed-log-spring-boot-starter</artifactId>
        </dependency>

        <!-- Spring Boot Web -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!-- Spring Boot Actuator -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <!-- 数据库驱动 -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <scope>runtime</scope>
        </dependency>

        <!-- 连接池 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
        </dependency>

        <!-- 任务调度 -->
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
```

#### 模块依赖关系图

```
┌─────────────────────────────────────────────────────────────┐
│                    handthing-admin-web                      │
│                      (Web启动模块)                          │
└─────────────────────────────────────────────────────────────┘
                                │
                ┌───────────────┼───────────────┐
                │               │               │
                ▼               ▼               ▼
┌─────────────────────┐ ┌─────────────────────┐ ┌─────────────────────┐
│ handthing-admin-auth│ │handthing-admin-tenant│ │handthing-admin-user │
│    (认证授权模块)    │ │   (租户管理模块)     │ │   (用户管理模块)     │
└─────────────────────┘ └─────────────────────┘ └─────────────────────┘
                │               │               │
                └───────────────┼───────────────┘
                                │
                ┌───────────────┼───────────────┐
                │               │               │
                ▼               ▼               ▼
┌─────────────────────┐ ┌─────────────────────┐ ┌─────────────────────┐
│handthing-admin-system│ │handthing-admin-workflow│ │handthing-admin-message│
│   (系统管理模块)     │ │    (工作流模块)      │ │   (消息中心模块)     │
└─────────────────────┘ └─────────────────────┘ └─────────────────────┘
                │               │               │
                └───────────────┼───────────────┘
                                │
                ┌───────────────┼───────────────┐
                │               │               │
                ▼               ▼               ▼
┌─────────────────────┐ ┌─────────────────────┐ ┌─────────────────────┐
│handthing-admin-integration│ │handthing-admin-operation│ │handthing-admin-common│
│   (应用集成模块)     │ │   (运营监控模块)     │ │     (公共模块)       │
└─────────────────────┘ └─────────────────────┘ └─────────────────────┘
                │               │               │
                └───────────────┼───────────────┘
                                │
                                ▼
                ┌─────────────────────────────────────┐
                │      handthing-springboot3-starter   │
                │         (基础框架依赖)               │
                └─────────────────────────────────────┘
```

#### 业务模块内部结构

每个业务模块内部采用DDD分层架构：

```
handthing-admin-{module}/
├── {module}-api/                     # API接口层
│   ├── dto/                          # 数据传输对象
│   ├── request/                      # 请求对象
│   ├── response/                     # 响应对象
│   └── service/                      # 接口定义
├── {module}-domain/                  # 领域层
│   ├── entity/                       # 实体对象
│   ├── valueobject/                  # 值对象
│   ├── repository/                   # 仓储接口
│   └── service/                      # 领域服务
├── {module}-application/             # 应用服务层
│   ├── service/                      # 应用服务实现
│   ├── assembler/                    # 对象转换器
│   └── event/                        # 事件处理
└── {module}-infrastructure/          # 基础设施层
    ├── repository/                   # 仓储实现
    ├── mapper/                       # MyBatis Mapper
    ├── config/                       # 配置类
    └── external/                     # 外部服务调用
```

## 4. 数据库设计

### 4.1 数据库设计原则
- **多租户隔离**: 所有业务表包含tenant_id字段进行逻辑隔离
- **软删除**: 重要数据采用软删除策略，保留deleted字段
- **审计字段**: 包含create_time、update_time、create_by、update_by
- **版本控制**: 重要表包含version字段支持乐观锁
- **索引优化**: 基于查询场景设计合理的索引策略

### 4.2 核心表结构设计

#### 租户相关表
```sql
-- 租户信息表
CREATE TABLE tenant_info (
    id BIGINT PRIMARY KEY,
    tenant_code VARCHAR(50) UNIQUE NOT NULL COMMENT '租户编码',
    tenant_name VARCHAR(100) NOT NULL COMMENT '租户名称',
    company_name VARCHAR(200) COMMENT '公司名称',
    contact_name VARCHAR(50) COMMENT '联系人姓名',
    contact_phone VARCHAR(20) COMMENT '联系人电话',
    contact_email VARCHAR(100) COMMENT '联系人邮箱',
    package_id BIGINT COMMENT '套餐ID',
    status TINYINT DEFAULT 1 COMMENT '状态:1正常,2禁用,3过期',
    expire_time DATETIME COMMENT '到期时间',
    max_users INT DEFAULT 0 COMMENT '最大用户数',
    max_storage BIGINT DEFAULT 0 COMMENT '最大存储空间(字节)',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    create_by BIGINT,
    update_by BIGINT,
    deleted TINYINT DEFAULT 0,
    version INT DEFAULT 1
);

-- 租户套餐表
CREATE TABLE tenant_package (
    id BIGINT PRIMARY KEY,
    package_name VARCHAR(100) NOT NULL COMMENT '套餐名称',
    package_code VARCHAR(50) UNIQUE NOT NULL COMMENT '套餐编码',
    description TEXT COMMENT '套餐描述',
    price_monthly DECIMAL(10,2) COMMENT '月付价格',
    price_yearly DECIMAL(10,2) COMMENT '年付价格',
    max_users INT DEFAULT 0 COMMENT '最大用户数',
    max_storage BIGINT DEFAULT 0 COMMENT '最大存储空间',
    max_api_calls INT DEFAULT 0 COMMENT '每月API调用次数',
    permissions JSON COMMENT '权限配置',
    status TINYINT DEFAULT 1 COMMENT '状态:1启用,0禁用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT DEFAULT 0
);
```

#### 用户权限相关表
```sql
-- 用户信息表
CREATE TABLE user_info (
    id BIGINT PRIMARY KEY,
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    username VARCHAR(50) NOT NULL COMMENT '用户名',
    nickname VARCHAR(50) COMMENT '昵称',
    email VARCHAR(100) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    avatar VARCHAR(500) COMMENT '头像URL',
    password VARCHAR(255) COMMENT '密码',
    salt VARCHAR(50) COMMENT '盐值',
    status TINYINT DEFAULT 1 COMMENT '状态:1正常,0禁用',
    last_login_time DATETIME COMMENT '最后登录时间',
    last_login_ip VARCHAR(50) COMMENT '最后登录IP',
    mfa_enabled TINYINT DEFAULT 0 COMMENT '是否启用MFA',
    mfa_secret VARCHAR(100) COMMENT 'MFA密钥',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    create_by BIGINT,
    update_by BIGINT,
    deleted TINYINT DEFAULT 0,
    version INT DEFAULT 1,
    UNIQUE KEY uk_tenant_username (tenant_id, username),
    INDEX idx_tenant_phone (tenant_id, phone),
    INDEX idx_tenant_email (tenant_id, email)
);

-- 角色信息表
CREATE TABLE role_info (
    id BIGINT PRIMARY KEY,
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    role_code VARCHAR(50) NOT NULL COMMENT '角色编码',
    role_name VARCHAR(100) NOT NULL COMMENT '角色名称',
    description TEXT COMMENT '角色描述',
    data_scope TINYINT DEFAULT 1 COMMENT '数据权限:1全部,2本部门,3本部门及下级,4仅本人,5自定义',
    status TINYINT DEFAULT 1 COMMENT '状态:1启用,0禁用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    create_by BIGINT,
    update_by BIGINT,
    deleted TINYINT DEFAULT 0,
    UNIQUE KEY uk_tenant_code (tenant_id, role_code)
);

-- 用户角色关联表
CREATE TABLE user_role (
    id BIGINT PRIMARY KEY,
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    role_id BIGINT NOT NULL COMMENT '角色ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    create_by BIGINT,
    UNIQUE KEY uk_user_role (user_id, role_id),
    INDEX idx_tenant_user (tenant_id, user_id)
);

-- 权限信息表
CREATE TABLE permission_info (
    id BIGINT PRIMARY KEY,
    permission_code VARCHAR(100) NOT NULL COMMENT '权限编码',
    permission_name VARCHAR(100) NOT NULL COMMENT '权限名称',
    permission_type TINYINT NOT NULL COMMENT '权限类型:1菜单,2按钮,3API',
    parent_id BIGINT DEFAULT 0 COMMENT '父权限ID',
    path VARCHAR(200) COMMENT '路由路径',
    component VARCHAR(200) COMMENT '组件路径',
    icon VARCHAR(100) COMMENT '图标',
    sort_order INT DEFAULT 0 COMMENT '排序',
    status TINYINT DEFAULT 1 COMMENT '状态:1启用,0禁用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT DEFAULT 0,
    UNIQUE KEY uk_permission_code (permission_code)
);

-- 角色权限关联表
CREATE TABLE role_permission (
    id BIGINT PRIMARY KEY,
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    role_id BIGINT NOT NULL COMMENT '角色ID',
    permission_id BIGINT NOT NULL COMMENT '权限ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    create_by BIGINT,
    UNIQUE KEY uk_role_permission (role_id, permission_id),
    INDEX idx_tenant_role (tenant_id, role_id)
);
```

#### 组织架构相关表
```sql
-- 部门信息表
CREATE TABLE department_info (
    id BIGINT PRIMARY KEY,
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    dept_code VARCHAR(50) NOT NULL COMMENT '部门编码',
    dept_name VARCHAR(100) NOT NULL COMMENT '部门名称',
    parent_id BIGINT DEFAULT 0 COMMENT '父部门ID',
    leader_id BIGINT COMMENT '部门负责人ID',
    phone VARCHAR(20) COMMENT '联系电话',
    email VARCHAR(100) COMMENT '邮箱',
    sort_order INT DEFAULT 0 COMMENT '排序',
    status TINYINT DEFAULT 1 COMMENT '状态:1正常,0停用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    create_by BIGINT,
    update_by BIGINT,
    deleted TINYINT DEFAULT 0,
    UNIQUE KEY uk_tenant_code (tenant_id, dept_code),
    INDEX idx_tenant_parent (tenant_id, parent_id)
);

-- 职位信息表
CREATE TABLE position_info (
    id BIGINT PRIMARY KEY,
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    position_code VARCHAR(50) NOT NULL COMMENT '职位编码',
    position_name VARCHAR(100) NOT NULL COMMENT '职位名称',
    description TEXT COMMENT '职位描述',
    level_rank INT DEFAULT 0 COMMENT '职级',
    status TINYINT DEFAULT 1 COMMENT '状态:1启用,0禁用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    create_by BIGINT,
    update_by BIGINT,
    deleted TINYINT DEFAULT 0,
    UNIQUE KEY uk_tenant_code (tenant_id, position_code)
);

-- 用户部门关联表
CREATE TABLE user_department (
    id BIGINT PRIMARY KEY,
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    dept_id BIGINT NOT NULL COMMENT '部门ID',
    position_id BIGINT COMMENT '职位ID',
    is_main TINYINT DEFAULT 1 COMMENT '是否主部门:1是,0否',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    create_by BIGINT,
    INDEX idx_tenant_user (tenant_id, user_id),
    INDEX idx_tenant_dept (tenant_id, dept_id)
);
```

#### 系统管理相关表
```sql
-- 菜单信息表
CREATE TABLE menu_info (
    id BIGINT PRIMARY KEY,
    menu_code VARCHAR(100) NOT NULL COMMENT '菜单编码',
    menu_name VARCHAR(100) NOT NULL COMMENT '菜单名称',
    parent_id BIGINT DEFAULT 0 COMMENT '父菜单ID',
    menu_type TINYINT NOT NULL COMMENT '菜单类型:1目录,2菜单,3按钮',
    path VARCHAR(200) COMMENT '路由路径',
    component VARCHAR(200) COMMENT '组件路径',
    icon VARCHAR(100) COMMENT '图标',
    sort_order INT DEFAULT 0 COMMENT '排序',
    visible TINYINT DEFAULT 1 COMMENT '是否可见:1是,0否',
    status TINYINT DEFAULT 1 COMMENT '状态:1启用,0禁用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT DEFAULT 0,
    UNIQUE KEY uk_menu_code (menu_code)
);

-- 字典类型表
CREATE TABLE dict_type (
    id BIGINT PRIMARY KEY,
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    dict_code VARCHAR(100) NOT NULL COMMENT '字典编码',
    dict_name VARCHAR(100) NOT NULL COMMENT '字典名称',
    description TEXT COMMENT '描述',
    status TINYINT DEFAULT 1 COMMENT '状态:1启用,0禁用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    create_by BIGINT,
    update_by BIGINT,
    deleted TINYINT DEFAULT 0,
    UNIQUE KEY uk_tenant_code (tenant_id, dict_code)
);

-- 字典数据表
CREATE TABLE dict_data (
    id BIGINT PRIMARY KEY,
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    dict_type_id BIGINT NOT NULL COMMENT '字典类型ID',
    dict_label VARCHAR(100) NOT NULL COMMENT '字典标签',
    dict_value VARCHAR(100) NOT NULL COMMENT '字典值',
    sort_order INT DEFAULT 0 COMMENT '排序',
    status TINYINT DEFAULT 1 COMMENT '状态:1启用,0禁用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    create_by BIGINT,
    update_by BIGINT,
    deleted TINYINT DEFAULT 0,
    INDEX idx_tenant_type (tenant_id, dict_type_id)
);
```

### 4.3 索引设计策略

#### 主要索引
- **租户隔离索引**: 所有业务表的tenant_id字段建立索引
- **唯一性索引**: 租户内唯一的业务字段建立复合唯一索引
- **查询优化索引**: 基于常用查询条件建立复合索引
- **外键索引**: 关联表的外键字段建立索引

#### 分表策略
- **日志表**: 按月分表 (audit_log_202401, audit_log_202402...)
- **监控数据**: 按天分表 (monitor_data_20240101, monitor_data_20240102...)
- **大数据量表**: 基于租户ID进行分库分表

## 5. API接口设计

### 5.1 API设计规范

#### 统一响应格式
```json
{
    "code": 200,
    "message": "success",
    "data": {},
    "timestamp": 1640995200000,
    "traceId": "abc123def456"
}
```

#### 错误码规范
- **2xx**: 成功响应
- **4xx**: 客户端错误
- **5xx**: 服务端错误

#### 分页响应格式
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "records": [],
        "total": 100,
        "size": 10,
        "current": 1,
        "pages": 10
    }
}
```

### 5.2 核心API接口

#### 认证授权API
```yaml
# 用户登录
POST /api/v1/auth/login
Content-Type: application/json
{
    "username": "admin",
    "password": "123456",
    "captcha": "abcd",
    "captchaKey": "uuid",
    "loginType": "password" # password/sms/email/oauth
}

# 刷新Token
POST /api/v1/auth/refresh
Authorization: Bearer {refresh_token}

# 用户登出
POST /api/v1/auth/logout
Authorization: Bearer {access_token}

# 第三方登录授权URL
GET /api/v1/auth/oauth/{platform}/authorize
Response: {
    "authorizeUrl": "https://oauth.platform.com/authorize?..."
}

# 第三方登录回调
POST /api/v1/auth/oauth/{platform}/callback
{
    "code": "authorization_code",
    "state": "random_state"
}
```

#### 租户管理API
```yaml
# 租户列表
GET /api/v1/tenant/tenants?page=1&size=10&keyword=搜索关键词

# 创建租户
POST /api/v1/tenant/tenants
{
    "tenantName": "测试租户",
    "companyName": "测试公司",
    "contactName": "张三",
    "contactPhone": "13800138000",
    "contactEmail": "<EMAIL>",
    "packageId": 1,
    "expireTime": "2024-12-31 23:59:59",
    "adminUsername": "admin",
    "adminPassword": "123456"
}

# 更新租户
PUT /api/v1/tenant/tenants/{id}
{
    "tenantName": "更新后的租户名称",
    "status": 1
}

# 租户套餐列表
GET /api/v1/tenant/packages

# 创建套餐
POST /api/v1/tenant/packages
{
    "packageName": "企业版",
    "packageCode": "enterprise",
    "description": "企业版套餐",
    "priceMonthly": 999.00,
    "priceYearly": 9999.00,
    "maxUsers": 1000,
    "maxStorage": 107374182400,
    "maxApiCalls": 1000000,
    "permissions": ["user:read", "user:write"]
}
```

#### 用户管理API
```yaml
# 用户列表
GET /api/v1/user/users?page=1&size=10&deptId=1&keyword=张三

# 创建用户
POST /api/v1/user/users
{
    "username": "zhangsan",
    "nickname": "张三",
    "email": "<EMAIL>",
    "phone": "13800138000",
    "password": "123456",
    "deptId": 1,
    "positionId": 1,
    "roleIds": [1, 2]
}

# 批量导入用户
POST /api/v1/user/users/batch-import
Content-Type: multipart/form-data
file: users.xlsx

# 重置密码
PUT /api/v1/user/users/{id}/reset-password
{
    "newPassword": "123456"
}

# 分配角色
PUT /api/v1/user/users/{id}/roles
{
    "roleIds": [1, 2, 3]
}
```

#### 角色权限API
```yaml
# 角色列表
GET /api/v1/role/roles?page=1&size=10

# 创建角色
POST /api/v1/role/roles
{
    "roleCode": "manager",
    "roleName": "部门经理",
    "description": "部门经理角色",
    "dataScope": 2
}

# 权限树
GET /api/v1/role/permissions/tree

# 分配权限
PUT /api/v1/role/roles/{id}/permissions
{
    "permissionIds": [1, 2, 3, 4, 5]
}

# 复制角色
POST /api/v1/role/roles/{id}/copy
{
    "newRoleName": "新角色名称"
}
```

#### 组织架构API
```yaml
# 部门树
GET /api/v1/org/departments/tree

# 创建部门
POST /api/v1/org/departments
{
    "deptCode": "IT001",
    "deptName": "IT部门",
    "parentId": 0,
    "leaderId": 1,
    "phone": "010-12345678",
    "email": "<EMAIL>"
}

# 职位列表
GET /api/v1/org/positions?page=1&size=10

# 创建职位
POST /api/v1/org/positions
{
    "positionCode": "DEV001",
    "positionName": "高级开发工程师",
    "description": "负责系统开发",
    "levelRank": 5
}
```

### 5.3 API安全设计

#### 认证机制
- **JWT Token**: 使用handthing-auth的JWT实现
- **Token刷新**: 支持access_token和refresh_token机制
- **多端登录**: 支持PC、移动端同时登录

#### 权限控制
- **接口权限**: 基于角色的接口访问控制
- **数据权限**: 基于部门层级的数据访问控制
- **字段权限**: 敏感字段的访问控制

#### 安全防护
- **参数校验**: 使用Bean Validation进行参数校验
- **SQL注入防护**: 使用MyBatis-Plus的参数绑定
- **XSS防护**: 输入参数的HTML转义
- **CSRF防护**: 使用CSRF Token
- **限流控制**: 基于用户和IP的接口限流

## 6. 安全设计

### 6.1 认证安全

#### 多因素认证(MFA)
- **TOTP**: 基于时间的一次性密码
- **SMS**: 短信验证码
- **Email**: 邮箱验证码
- **硬件Token**: 支持FIDO2/WebAuthn

#### 密码策略
- **复杂度要求**: 长度、字符类型要求
- **历史密码**: 防止重复使用历史密码
- **密码过期**: 定期强制更换密码
- **账户锁定**: 登录失败次数限制

#### 登录安全
- **异地登录检测**: 基于IP地理位置的异常检测
- **设备指纹**: 基于浏览器指纹的设备识别
- **登录日志**: 详细的登录审计日志
- **强制下线**: 管理员可强制用户下线

### 6.2 数据安全

#### 数据加密
- **传输加密**: HTTPS/TLS 1.3
- **存储加密**: 敏感数据字段加密存储
- **密钥管理**: 使用handthing-crypto进行密钥管理

#### 数据权限
- **行级权限**: 基于部门层级的数据访问控制
- **列级权限**: 敏感字段的访问控制
- **租户隔离**: 严格的多租户数据隔离

#### 数据备份
- **定期备份**: 数据库定期全量和增量备份
- **异地备份**: 跨地域的数据备份
- **恢复测试**: 定期进行数据恢复测试

### 6.3 网络安全

#### 网络隔离
- **DMZ区域**: Web服务器部署在DMZ区
- **内网隔离**: 数据库服务器部署在内网
- **VPC**: 使用虚拟私有云进行网络隔离

#### 防护措施
- **WAF**: Web应用防火墙
- **DDoS防护**: 分布式拒绝服务攻击防护
- **IPS**: 入侵防护系统
- **安全扫描**: 定期进行安全漏洞扫描

### 6.4 配置管理

#### handthing-springboot3-starter配置
```yaml
# application.yml - HandThing Admin 配置
handthing:
  # 核心配置
  core:
    enabled: true

  # ID生成器配置
  id:
    enabled: true
    snowflake:
      enabled: true
      worker-id: 1
      datacenter-id: 1
      pool:
        enabled: true
        size: 1000
        threshold: 100
        batch-size: 100

  # 数据层配置
  datalayer:
    enabled: true
    pagination:
      enabled: true
      default-page-size: 20
      max-page-size: 1000
    auto-fill:
      enabled: true
      create-time-field: "createTime"
      update-time-field: "updateTime"
      create-by-field: "createBy"
      update-by-field: "updateBy"

  # 多租户配置
  tenant:
    enabled: true
    tenant-id-field: "tenantId"
    ignore-tables:
      - "tenant_info"
      - "tenant_package"
      - "permission_info"
      - "menu_info"
    ignore-sqls:
      - "SELECT 1"

  # 数据权限配置
  dataauth:
    enabled: true
    default-strategy: "dept"

  # 缓存配置
  cache:
    enabled: true
    type: "level"  # 多级缓存
    caffeine:
      default-spec: "maximumSize=10000,expireAfterWrite=1h"
      stats-enabled: true
    redis:
      key-prefix: "handthing:admin:"
      ttl: 3600

  # 认证配置
  auth:
    enabled: true
    default-grant-type: "password"
    jwt:
      secret: "${JWT_SECRET:handthing-admin-secret-key-2024}"
      access-token-expiration: 7200    # 2小时
      refresh-token-expiration: 604800 # 7天
    oauth2:
      enabled: true
    saml:
      enabled: true
    oidc:
      enabled: true

  # HTTP客户端配置
  http-client:
    enabled: true
    type: "webclient"
    timeout:
      connect: 5000
      read: 30000
      write: 30000
    pool:
      max-connections: 100
      max-connections-per-route: 20

  # 加密配置
  crypto:
    enabled: true
    default-algorithm: "AES"
    key: "${CRYPTO_KEY:handthing-admin-crypto-key-2024}"

  # 应用连接器配置
  app-connector:
    enabled: true
    platforms:
      wecom:
        enabled: false
        corp-id: "${WECOM_CORP_ID:}"
        corp-secret: "${WECOM_CORP_SECRET:}"
      dingtalk:
        enabled: false
        app-key: "${DINGTALK_APP_KEY:}"
        app-secret: "${DINGTALK_APP_SECRET:}"

  # API文档配置
  knife4j:
    enabled: true
    title: "HandThing Admin API"
    description: "企业级SaaS后台管理系统API文档"
    version: "1.0.0"
    contact:
      name: "HandThing Team"
      email: "<EMAIL>"
```

#### 环境特定配置

##### 开发环境配置 (application-dev.yml)
```yaml
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *******************************************************************************************************************
    username: root
    password: 123456

  redis:
    host: localhost
    port: 6379
    database: 0

handthing:
  tenant:
    enabled: false  # 开发环境关闭多租户
  dataauth:
    enabled: false  # 开发环境关闭数据权限
  cache:
    type: "caffeine"  # 开发环境使用本地缓存

logging:
  level:
    cn.com.handthing: DEBUG
    org.springframework.security: DEBUG
```

##### 生产环境配置 (application-prod.yml)
```yaml
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://${DB_HOST:mysql-cluster}:${DB_PORT:3306}/${DB_NAME:handthing_admin}?useUnicode=true&characterEncoding=utf8&serverTimezone=Asia/Shanghai
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

  redis:
    cluster:
      nodes: ${REDIS_CLUSTER_NODES}
    password: ${REDIS_PASSWORD}
    timeout: 3000
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5

handthing:
  tenant:
    enabled: true
  dataauth:
    enabled: true
  cache:
    type: "level"  # 生产环境使用多级缓存

logging:
  level:
    cn.com.handthing: INFO
    root: WARN
```

## 7. 部署架构

### 7.1 环境规划

#### 环境分离
```
开发环境 (Development)
├── 单机部署
├── 内存数据库 (H2)
├── 本地缓存 (Caffeine)
└── 模拟外部服务

测试环境 (Testing)
├── 容器化部署
├── MySQL 主从
├── Redis 单机
└── 真实外部服务

预生产环境 (Staging)
├── Kubernetes 部署
├── MySQL 集群
├── Redis 集群
└── 生产级配置

生产环境 (Production)
├── Kubernetes 集群
├── MySQL 高可用集群
├── Redis 高可用集群
└── 完整监控体系
```

### 7.2 容器化部署

#### Docker镜像构建
```dockerfile
# Dockerfile
FROM openjdk:17-jre-slim

LABEL maintainer="<EMAIL>"
LABEL version="1.0.0"

# 创建应用目录
WORKDIR /app

# 复制应用文件
COPY target/handthing-admin.jar app.jar

# 创建非root用户
RUN addgroup --system spring && adduser --system spring --ingroup spring
USER spring:spring

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/actuator/health || exit 1

# 启动应用
ENTRYPOINT ["java", "-jar", "app.jar"]
```

#### Kubernetes部署配置
```yaml
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: handthing-admin
  namespace: handthing
spec:
  replicas: 3
  selector:
    matchLabels:
      app: handthing-admin
  template:
    metadata:
      labels:
        app: handthing-admin
    spec:
      containers:
      - name: handthing-admin
        image: handthing/admin:1.0.0
        ports:
        - containerPort: 8080
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "prod"
        - name: MYSQL_HOST
          valueFrom:
            secretKeyRef:
              name: mysql-secret
              key: host
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
```

### 7.3 高可用架构

#### 服务高可用
- **多副本部署**: 每个服务部署多个副本
- **负载均衡**: 使用Nginx/Ingress进行负载均衡
- **健康检查**: 自动检测和剔除不健康实例
- **自动扩缩容**: 基于CPU/内存使用率自动扩缩容

#### 数据库高可用
- **主从复制**: MySQL主从复制，读写分离
- **故障转移**: 自动故障检测和主从切换
- **数据备份**: 定期全量和增量备份
- **监控告警**: 数据库性能和可用性监控

#### 缓存高可用
- **Redis集群**: Redis Cluster模式部署
- **数据分片**: 数据自动分片存储
- **故障转移**: 自动故障检测和切换
- **持久化**: RDB和AOF双重持久化

## 8. 监控与运维

### 8.1 监控体系

#### 应用监控
```yaml
# 监控指标
应用性能监控:
  - JVM内存使用率
  - GC频率和耗时
  - 线程池状态
  - 接口响应时间
  - 接口成功率

业务监控:
  - 用户登录成功率
  - 租户创建数量
  - API调用量
  - 错误日志数量

基础设施监控:
  - CPU使用率
  - 内存使用率
  - 磁盘使用率
  - 网络流量
  - 数据库连接数
```

#### 告警规则
```yaml
# 告警配置
告警规则:
  - 应用响应时间 > 2秒
  - 应用错误率 > 5%
  - CPU使用率 > 80%
  - 内存使用率 > 85%
  - 磁盘使用率 > 90%
  - 数据库连接数 > 80%

告警通知:
  - 邮件通知
  - 短信通知
  - 钉钉/企业微信通知
  - 电话告警(严重告警)
```

### 8.2 日志管理

#### 日志分类
- **应用日志**: 业务操作日志
- **访问日志**: HTTP请求访问日志
- **错误日志**: 异常和错误日志
- **审计日志**: 安全相关操作日志
- **性能日志**: 性能监控日志

#### 日志收集
```yaml
# Filebeat配置
filebeat.inputs:
- type: log
  enabled: true
  paths:
    - /app/logs/*.log
  fields:
    service: handthing-admin
    environment: production
  multiline.pattern: '^\d{4}-\d{2}-\d{2}'
  multiline.negate: true
  multiline.match: after

output.elasticsearch:
  hosts: ["elasticsearch:9200"]
  index: "handthing-admin-%{+yyyy.MM.dd}"
```

### 8.3 运维自动化

#### CI/CD流水线
```yaml
# GitLab CI配置
stages:
  - test
  - build
  - deploy

test:
  stage: test
  script:
    - mvn clean test
  only:
    - merge_requests

build:
  stage: build
  script:
    - mvn clean package
    - docker build -t handthing/admin:$CI_COMMIT_SHA .
    - docker push handthing/admin:$CI_COMMIT_SHA
  only:
    - main

deploy:
  stage: deploy
  script:
    - kubectl set image deployment/handthing-admin handthing-admin=handthing/admin:$CI_COMMIT_SHA
    - kubectl rollout status deployment/handthing-admin
  only:
    - main
```

#### 自动化运维脚本
- **部署脚本**: 自动化部署和回滚
- **备份脚本**: 数据库自动备份
- **监控脚本**: 系统健康检查
- **清理脚本**: 日志和临时文件清理

## 9. 开发规范

### 9.1 代码规范

#### Java代码规范
- **命名规范**: 遵循Java命名约定
- **注释规范**: 类、方法、复杂逻辑必须有注释
- **异常处理**: 统一异常处理机制
- **日志规范**: 统一日志格式和级别

#### 数据库规范
- **表命名**: 小写字母+下划线
- **字段命名**: 小写字母+下划线
- **索引命名**: idx_表名_字段名
- **约束命名**: uk_表名_字段名 (唯一约束)

#### API规范
- **URL设计**: RESTful风格
- **参数验证**: 使用Bean Validation
- **响应格式**: 统一响应格式
- **错误处理**: 统一错误码和错误信息

### 9.2 开发流程

#### Git工作流
```
main分支 (生产环境)
├── develop分支 (开发环境)
│   ├── feature/用户管理 (功能分支)
│   ├── feature/权限管理 (功能分支)
│   └── feature/租户管理 (功能分支)
├── release/v1.0.0 (发布分支)
└── hotfix/紧急修复 (热修复分支)
```

#### 代码审查
- **Pull Request**: 所有代码必须通过PR合并
- **代码审查**: 至少一人审查通过
- **自动化测试**: 单元测试覆盖率 > 80%
- **静态代码分析**: SonarQube代码质量检查

### 9.3 测试策略

#### 测试分层
```
测试金字塔:
├── 单元测试 (70%)
│   ├── Service层测试
│   ├── Repository层测试
│   └── Util工具类测试
├── 集成测试 (20%)
│   ├── API接口测试
│   ├── 数据库集成测试
│   └── 缓存集成测试
└── 端到端测试 (10%)
    ├── 用户场景测试
    ├── 业务流程测试
    └── 性能测试
```

#### 测试工具
- **单元测试**: JUnit 5 + Mockito
- **集成测试**: Spring Boot Test + TestContainers
- **API测试**: RestAssured + WireMock
- **性能测试**: JMeter + Gatling

## 10. 项目计划

### 10.1 开发阶段

#### 第一阶段：基础框架搭建 (4周)
- **Week 1-2**: 项目架构搭建，基础设施配置
- **Week 3-4**: 认证授权模块开发，用户管理基础功能

#### 第二阶段：核心功能开发 (8周)
- **Week 5-6**: 租户管理，组织架构管理
- **Week 7-8**: 角色权限管理，菜单管理
- **Week 9-10**: 系统管理，字典标签管理
- **Week 11-12**: 工作流引擎，审批流程

#### 第三阶段：扩展功能开发 (6周)
- **Week 13-14**: 消息中心，文件存储
- **Week 15-16**: 应用管理，开放平台
- **Week 17-18**: 数据同步，任务调度

#### 第四阶段：运营监控功能 (4周)
- **Week 19-20**: 审计日志，系统监控
- **Week 21-22**: 计费订阅，个人中心

#### 第五阶段：测试优化上线 (4周)
- **Week 23-24**: 系统测试，性能优化
- **Week 25-26**: 部署上线，文档完善

### 10.2 团队配置

#### 开发团队 (8人)
- **架构师**: 1人，负责技术架构设计
- **后端开发**: 4人，负责后端功能开发
- **前端开发**: 2人，负责前端界面开发
- **测试工程师**: 1人，负责测试用例编写和执行

#### 运维团队 (2人)
- **DevOps工程师**: 1人，负责CI/CD和部署
- **运维工程师**: 1人，负责监控和维护

### 10.3 里程碑计划

#### 重要里程碑
- **M1 (Week 4)**: 基础框架完成，认证功能可用
- **M2 (Week 8)**: 核心用户管理功能完成
- **M3 (Week 12)**: 权限管理和工作流完成
- **M4 (Week 18)**: 所有业务功能开发完成
- **M5 (Week 22)**: 运营监控功能完成
- **M6 (Week 26)**: 系统正式上线

#### 风险控制
- **技术风险**: 定期技术评审，及时调整方案
- **进度风险**: 每周进度跟踪，提前识别延期风险
- **质量风险**: 持续集成测试，代码质量门禁
- **人员风险**: 知识分享，关键模块多人熟悉

## 11. 总结

HandThing Admin 系统基于 handthing-springboot3-starter 框架构建，采用现代化的微服务架构和云原生技术栈，为企业提供完整的 SaaS 后台管理解决方案。

### 11.1 核心优势
- **技术先进**: 基于 Spring Boot 3 和 handthing-springboot3-starter 的成熟技术栈
- **架构合理**: 采用 DDD 领域驱动设计和分层架构
- **功能完整**: 涵盖认证、权限、租户、工作流等企业级功能
- **安全可靠**: 多层次的安全防护和数据保护机制
- **高可用**: 支持容器化部署和 Kubernetes 集群管理
- **可扩展**: 模块化设计，支持功能扩展和定制
- **开箱即用**: 基于handthing-springboot3-starter，大量功能开箱即用
- **统一管理**: 通过starter-parent统一依赖版本和配置管理

### 11.2 技术特色
- **多租户架构**: 基于tenant-datalayer-spring-boot-starter的完善租户隔离和资源管理
- **统一认证**: 基于handthing-auth支持密码、短信、OAuth2.0、SAML、OIDC等多种认证方式
- **细粒度权限**: 基于dataauth-datalayer-spring-boot-starter的菜单、操作、数据三级权限控制
- **数据层增强**: 基于datalayer-spring-boot-starter的MyBatis-Plus集成，支持自动填充、分页等
- **分布式ID**: 基于id-spring-boot-starter的雪花算法ID生成，支持池化优化
- **多级缓存**: 基于level-cache-spring-boot-starter的L1本地缓存+L2分布式缓存
- **统一HTTP客户端**: 基于webclient-spring-boot-starter的统一外部服务调用
- **数据加密**: 基于crypto-spring-boot-starter的敏感数据加密存储
- **工作流引擎**: 可视化流程设计和审批管理
- **实时监控**: 全方位的系统监控和告警机制

### 11.3 预期效果
通过 HandThing Admin 系统的建设，将为企业提供：
- **降低开发成本**: 基于handthing-springboot3-starter成熟框架，大量功能开箱即用，减少80%重复开发
- **提升开发效率**: 统一的starter依赖管理，标准化的开发模式，提升50%开发效率
- **提升管理效率**: 统一的后台管理平台，集成化的管理界面
- **保障系统安全**: 基于handthing-auth的完善安全防护体系，多层次安全保障
- **支持业务扩展**: 基于handthing-springboot3-starter的灵活架构设计支持业务快速增长
- **简化运维管理**: 自动化的部署和监控体系，降低运维复杂度
- **统一技术栈**: 基于starter-parent的统一依赖管理，避免版本冲突，提升系统稳定性

---

**© 2024 HandThing. All rights reserved.**
