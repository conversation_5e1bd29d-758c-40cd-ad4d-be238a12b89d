package cn.com.handthing.starter.crypto.core;

/**
 * 非对称加密器接口，同时包含签名和验签功能。
 *
 * <AUTHOR>
 * @since V1.0.0
 */
public interface AsymmetricEncryptor extends CryptoService {

    /**
     * 使用公钥加密给定的明文字符串。
     *
     * @param plaintext 待加密的明文字符串 (UTF-8编码)
     * @return Base64 编码的密文字符串
     */
    String encrypt(String plaintext);

    /**
     * 使用私钥解密给定的密文字符串。
     *
     * @param ciphertext Base64 编码的密文字符串
     * @return 解密后的明文字符串 (UTF-8编码)
     */
    String decrypt(String ciphertext);

    /**
     * 使用私钥对给定的数据进行签名。
     *
     * @param data 待签名的数据 (UTF-8编码)
     * @return Base64 编码的签名字符串
     */
    String sign(String data);

    /**
     * 使用公钥验证签名。
     *
     * @param data      原始数据 (UTF-8编码)
     * @param signature Base64 编码的签名字符串
     * @return 如果签名有效，则返回 true；否则返回 false
     */
    boolean verify(String data, String signature);
}