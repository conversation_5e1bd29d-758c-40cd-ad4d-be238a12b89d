# HandThing认证系统故障排除指南

## 🔧 常见问题与解决方案

### 1. "Unsupported grant type" 错误

#### 问题描述
```
Authentication failed: Unsupported grant type: sms_code
```

#### 根本原因
- 认证提供者未正确注册
- 认证请求类型不匹配
- AuthenticationController创建了通用认证请求而非具体类型

#### 解决方案

**检查认证提供者注册**
```java
@Autowired
private ProviderRegistry providerRegistry;

@PostConstruct
public void checkProviders() {
    List<AuthenticationProvider> providers = providerRegistry.getProviders();
    log.info("已注册的认证提供者: {}", providers);
    
    // 检查特定提供者
    boolean hasSmsProvider = providers.stream()
        .anyMatch(p -> p.supports(GrantType.SMS_CODE));
    log.info("SMS认证提供者已注册: {}", hasSmsProvider);
}
```

**验证认证请求创建**
```java
// 确保创建了正确的认证请求类型
private AuthenticationRequest createSmsAuthenticationRequest(Map<String, Object> loginRequest) {
    try {
        Class<?> clazz = Class.forName("cn.com.handthing.starter.auth.sms.SmsAuthenticationRequest");
        AuthenticationRequest request = (AuthenticationRequest) clazz.getDeclaredConstructor().newInstance();
        
        // 设置手机号和验证码
        clazz.getMethod("setPhone", String.class).invoke(request, (String) loginRequest.get("phone"));
        clazz.getMethod("setSmsCode", String.class).invoke(request, (String) loginRequest.get("sms_code"));
        
        return request;
    } catch (Exception e) {
        log.warn("Failed to create SmsAuthenticationRequest, using generic request", e);
        return createGenericAuthenticationRequest(GrantType.SMS_CODE, loginRequest);
    }
}
```

### 2. "Authentication token is required" 错误

#### 问题描述
```
{
  "success": false,
  "error_code": "AUTHENTICATION_REQUIRED",
  "error_description": "Authentication token is required"
}
```

#### 根本原因
- 前端未正确保存或发送token
- 认证过滤器配置错误
- token已过期或格式错误

#### 解决方案

**检查前端token管理**
```javascript
// 检查token是否存在
const token = localStorage.getItem('access_token');
if (!token) {
    console.error('No access token found');
    window.location.href = '/login';
    return;
}

// 检查token格式
function isValidJWT(token) {
    try {
        const parts = token.split('.');
        return parts.length === 3;
    } catch (e) {
        return false;
    }
}

// 检查token是否过期
function isTokenExpired(token) {
    try {
        const payload = JSON.parse(atob(token.split('.')[1]));
        return payload.exp * 1000 < Date.now();
    } catch (e) {
        return true;
    }
}
```

**验证请求头设置**
```javascript
// 确保正确设置Authorization头
function apiCall(url, options = {}) {
    const token = localStorage.getItem('access_token');
    const headers = {
        'Content-Type': 'application/json',
        ...options.headers
    };
    
    if (token) {
        headers['Authorization'] = `Bearer ${token}`;
        console.log('Token added to request:', token.substring(0, 20) + '...');
    } else {
        console.error('No token available for API call');
    }
    
    return fetch(url, { ...options, headers });
}
```

**检查认证过滤器配置**
```yaml
handthing:
  auth:
    filter:
      enabled: true
      exclude-paths:
        - "/login"
        - "/auth/login"
        - "/auth/sms/send"
        - "/auth/*/auth-url"
        - "/*/callback"
        - "/public/**"
        - "/static/**"
```

### 3. 第三方认证授权URL生成失败

#### 问题描述
```
Failed to generate authorization URL for platform: 企业微信(wecom)
AuthException: Redirect URI is required for WeChat authorization
```

#### 根本原因
- 配置参数不完整
- redirect_uri缺失或格式错误
- 第三方平台配置无效

#### 解决方案

**完善配置参数**
```yaml
handthing:
  auth:
    thirdparty:
      wecom:
        enabled: true
        corp-id: ${WECOM_CORP_ID:test-corp-id}
        agent-id: ${WECOM_AGENT_ID:test-agent-id}
        corp-secret: ${WECOM_CORP_SECRET:test-corp-secret}
        # 确保所有必需参数都有值
```

**验证授权URL请求**
```http
GET /auth/wecom/auth-url?state=test-state&redirect_uri=http://localhost:8081/wecom/callback
```

**检查回调URL配置**
```java
@GetMapping("/auth/wecom/auth-url")
public ResponseEntity<Map<String, Object>> getWecomAuthUrl(
        @RequestParam String state,
        @RequestParam String redirect_uri) {
    
    // 验证redirect_uri格式
    if (!isValidRedirectUri(redirect_uri)) {
        throw new AuthException("Invalid redirect URI format");
    }
    
    // 生成授权URL
    String authUrl = wecomAuthProvider.getAuthorizationUrl(state, redirect_uri);
    
    Map<String, Object> response = new HashMap<>();
    response.put("success", true);
    response.put("auth_url", authUrl);
    
    return ResponseEntity.ok(response);
}
```

### 4. JWT token验证失败

#### 问题描述
```
JWT token validation failed: Invalid signature
```

#### 根本原因
- JWT secret不匹配
- token格式错误
- token已过期
- 时钟偏差问题

#### 解决方案

**检查JWT配置**
```yaml
handthing:
  auth:
    jwt:
      secret: ${JWT_SECRET:handthing-auth-secret}  # 确保secret一致
      issuer: handthing-auth-test
      audience: handthing-app
      access-token-expiration: 7200
      refresh-token-expiration: 604800
```

**验证token内容**
```java
@Component
public class JwtDebugger {
    
    public void debugToken(String token) {
        try {
            String[] parts = token.split("\\.");
            
            // 解析header
            String header = new String(Base64.getDecoder().decode(parts[0]));
            log.info("JWT Header: {}", header);
            
            // 解析payload
            String payload = new String(Base64.getDecoder().decode(parts[1]));
            log.info("JWT Payload: {}", payload);
            
            // 检查过期时间
            JsonNode payloadNode = objectMapper.readTree(payload);
            long exp = payloadNode.get("exp").asLong();
            long now = System.currentTimeMillis() / 1000;
            log.info("Token expires at: {}, current time: {}, expired: {}", 
                exp, now, exp < now);
                
        } catch (Exception e) {
            log.error("Failed to debug JWT token", e);
        }
    }
}
```

### 5. 认证事件未触发

#### 问题描述
认证事件监听器未收到事件通知

#### 根本原因
- 事件发布被禁用
- 监听器注册失败
- 异步处理问题

#### 解决方案

**启用事件配置**
```yaml
handthing:
  auth:
    events:
      enabled: true
```

**检查事件监听器**
```java
@Component
@Slf4j
public class AuthenticationEventListener {
    
    @EventListener
    @Async
    public void handleAuthenticationStarted(AuthenticationStartedEvent event) {
        log.info("认证开始: 用户={}, 类型={}", 
            event.getUsername(), event.getGrantType());
    }
    
    @EventListener
    @Async
    public void handleAuthenticationSuccess(AuthenticationSuccessEvent event) {
        log.info("认证成功: 用户={}, 耗时={}ms", 
            event.getUsername(), event.getDuration());
    }
    
    @EventListener
    @Async
    public void handleAuthenticationFailure(AuthenticationFailedEvent event) {
        log.warn("认证失败: 用户={}, 错误={}, 耗时={}ms", 
            event.getUsername(), event.getErrorCode(), event.getDuration());
    }
}
```

**验证事件发布**
```java
@Autowired
private ApplicationEventPublisher eventPublisher;

public void testEventPublishing() {
    AuthenticationStartedEvent event = new AuthenticationStartedEvent(
        "test-user", GrantType.PASSWORD, "127.0.0.1");
    eventPublisher.publishEvent(event);
    log.info("Event published: {}", event);
}
```

## 🔍 调试技巧

### 1. 启用详细日志

```yaml
logging:
  level:
    cn.com.handthing.starter.auth: DEBUG
    cn.com.handthing.starter.auth.AuthenticationFilter: TRACE
    cn.com.handthing.starter.auth.AuthenticationManager: DEBUG
    cn.com.handthing.starter.auth.ProviderRegistry: DEBUG
    org.springframework.security: DEBUG
```

### 2. 监控认证指标

```java
@Component
public class AuthenticationMonitor {
    
    private final MeterRegistry meterRegistry;
    
    @EventListener
    public void handleAuthenticationSuccess(AuthenticationSuccessEvent event) {
        Counter.builder("auth.success")
            .tag("grant_type", event.getGrantType().getCode())
            .register(meterRegistry)
            .increment();
            
        Timer.builder("auth.duration")
            .tag("grant_type", event.getGrantType().getCode())
            .register(meterRegistry)
            .record(event.getDuration(), TimeUnit.MILLISECONDS);
    }
    
    @EventListener
    public void handleAuthenticationFailure(AuthenticationFailedEvent event) {
        Counter.builder("auth.failure")
            .tag("grant_type", event.getGrantType().getCode())
            .tag("error_code", event.getErrorCode())
            .register(meterRegistry)
            .increment();
    }
}
```

### 3. 健康检查端点

```http
GET /actuator/health
GET /actuator/metrics/auth.success
GET /actuator/metrics/auth.failure
GET /actuator/metrics/auth.duration
```

### 4. 认证流程追踪

```java
@Component
@Slf4j
public class AuthenticationTracer {
    
    @EventListener
    public void traceAuthentication(AuthenticationEvent event) {
        log.info("认证追踪: 事件={}, 用户={}, 时间={}, IP={}", 
            event.getClass().getSimpleName(),
            event.getUsername(),
            event.getTimestamp(),
            event.getIpAddress());
    }
}
```

## 📊 性能优化建议

### 1. 缓存优化

```yaml
handthing:
  auth:
    cache:
      enabled: true
      type: redis
      redis:
        host: localhost
        port: 6379
        timeout: 2000ms
        pool:
          max-active: 20
          max-idle: 10
          min-idle: 5
```

### 2. JWT优化

```yaml
handthing:
  auth:
    jwt:
      # 合理设置过期时间
      access-token-expiration: 7200    # 2小时
      refresh-token-expiration: 604800 # 7天
      # 使用强密钥
      secret: ${JWT_SECRET:$(openssl rand -base64 32)}
```

### 3. 连接池优化

```yaml
spring:
  datasource:
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
```

---

**© 2024 HandThing. All rights reserved.**
