package cn.com.handthing.starter.connector.wecom.api;

import cn.com.handthing.starter.connector.exception.ApiCallException;
import cn.com.handthing.starter.connector.wecom.config.WeComConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

/**
 * 企业微信客户联系API
 * <p>
 * 提供企业微信客户联系功能，包括获取客户列表、管理客户标签等功能。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "handthing.connector.wecom", name = "enabled", havingValue = "true")
public class WeComCustomerApi {

    private final WeComConfig config;
    private final RestTemplate restTemplate;

    /**
     * 获取客户列表
     *
     * @param accessToken 访问令牌
     * @param userId 企业成员的userid
     * @return 客户列表
     * @throws ApiCallException 如果获取失败
     */
    public Object getCustomerList(String accessToken, String userId) throws ApiCallException {
        // TODO: 实现获取客户列表
        log.debug("Getting WeCom customer list for userId: {}", userId);
        throw new UnsupportedOperationException("getCustomerList not implemented yet");
    }

    /**
     * 获取客户详情
     *
     * @param accessToken 访问令牌
     * @param externalUserId 外部联系人的userid
     * @return 客户详情
     * @throws ApiCallException 如果获取失败
     */
    public Object getCustomerDetail(String accessToken, String externalUserId) throws ApiCallException {
        // TODO: 实现获取客户详情
        log.debug("Getting WeCom customer detail for externalUserId: {}", externalUserId);
        throw new UnsupportedOperationException("getCustomerDetail not implemented yet");
    }
}
