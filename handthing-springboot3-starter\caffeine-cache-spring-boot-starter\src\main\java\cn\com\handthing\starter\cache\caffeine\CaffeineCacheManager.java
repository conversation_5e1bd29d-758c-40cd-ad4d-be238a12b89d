package cn.com.handthing.starter.cache.caffeine;

import cn.com.handthing.starter.cache.caffeine.config.CaffeineCacheProperties;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.CaffeineSpec;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.caffeine.CaffeineCache;

import java.util.Collection;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * Caffeine缓存管理器
 * <p>
 * 基于Caffeine高性能缓存库实现的缓存管理器，支持丰富的配置选项和高级特性
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
public class CaffeineCacheManager implements CacheManager {

    private final CaffeineCacheProperties properties;
    private final ConcurrentMap<String, Cache> cacheMap = new ConcurrentHashMap<>();

    /**
     * 构造函数
     *
     * @param properties Caffeine缓存配置属性
     */
    public CaffeineCacheManager(CaffeineCacheProperties properties) {
        this.properties = properties;
        log.info("CaffeineCacheManager initialized with default spec: {}", properties.getDefaultSpec());
    }

    @Override
    public Cache getCache(String name) {
        return cacheMap.computeIfAbsent(name, this::createCache);
    }

    @Override
    public Collection<String> getCacheNames() {
        return cacheMap.keySet();
    }

    /**
     * 创建缓存实例
     *
     * @param name 缓存名称
     * @return 缓存实例
     */
    private Cache createCache(String name) {
        CaffeineCacheProperties.CaffeineCacheSpec spec = properties.getCaches().get(name);
        
        // 构建Caffeine缓存
        Caffeine<Object, Object> builder = buildCaffeineCache(name, spec);
        
        // 创建Spring Cache包装器
        com.github.benmanes.caffeine.cache.Cache<Object, Object> caffeineCache = builder.build();
        CaffeineCache cache = new CaffeineCache(name, caffeineCache);
        
        log.info("Created Caffeine cache: {} with spec: {}", name, getEffectiveSpec(spec));
        return cache;
    }

    /**
     * 构建Caffeine缓存实例
     *
     * @param name 缓存名称
     * @param spec 缓存配置规格
     * @return Caffeine缓存构建器
     */
    private Caffeine<Object, Object> buildCaffeineCache(String name, CaffeineCacheProperties.CaffeineCacheSpec spec) {
        Caffeine<Object, Object> builder;
        
        // 使用配置规格字符串或默认规格
        String effectiveSpec = getEffectiveSpec(spec);
        try {
            builder = Caffeine.from(CaffeineSpec.parse(effectiveSpec));
        } catch (Exception e) {
            log.warn("Failed to parse Caffeine spec '{}' for cache '{}', using default spec", effectiveSpec, name, e);
            builder = Caffeine.from(CaffeineSpec.parse(properties.getDefaultSpec()));
        }

        // 应用额外的配置
        if (spec != null) {
            // 覆盖规格中的配置
            if (spec.getMaximumSize() != null) {
                builder.maximumSize(spec.getMaximumSize());
            }
            if (spec.getInitialCapacity() != null) {
                builder.initialCapacity(spec.getInitialCapacity());
            }
            if (spec.getExpireAfterWrite() != null) {
                builder.expireAfterWrite(spec.getExpireAfterWrite());
            }
            if (spec.getExpireAfterAccess() != null) {
                builder.expireAfterAccess(spec.getExpireAfterAccess());
            }
            if (spec.getRefreshAfterWrite() != null) {
                builder.refreshAfterWrite(spec.getRefreshAfterWrite());
            }

            // 引用类型配置
            if (spec.getEffectiveEnableSoftValues(properties.isEnableSoftValues())) {
                builder.softValues();
            }
            if (spec.getEffectiveEnableWeakKeys(properties.isEnableWeakKeys())) {
                builder.weakKeys();
            }
            if (spec.getEffectiveEnableWeakValues(properties.isEnableWeakValues())) {
                builder.weakValues();
            }

            // 统计信息
            if (spec.getEffectiveEnableStats(properties.isEnableStats())) {
                builder.recordStats();
            }
        } else {
            // 使用全局配置
            if (properties.isEnableSoftValues()) {
                builder.softValues();
            }
            if (properties.isEnableWeakKeys()) {
                builder.weakKeys();
            }
            if (properties.isEnableWeakValues()) {
                builder.weakValues();
            }
            if (properties.isEnableStats()) {
                builder.recordStats();
            }
        }

        return builder;
    }

    /**
     * 获取有效的配置规格字符串
     *
     * @param spec 缓存配置规格
     * @return 有效的配置规格字符串
     */
    private String getEffectiveSpec(CaffeineCacheProperties.CaffeineCacheSpec spec) {
        return spec != null ? spec.getEffectiveSpec(properties.getDefaultSpec()) : properties.getDefaultSpec();
    }

    /**
     * 获取缓存的统计信息
     *
     * @param cacheName 缓存名称
     * @return 缓存统计信息，如果不存在或不支持则返回null
     */
    public cn.com.handthing.starter.cache.CacheStats getCacheStats(String cacheName) {
        Cache cache = cacheMap.get(cacheName);
        if (cache instanceof CaffeineCache) {
            CaffeineCache caffeineCache = (CaffeineCache) cache;
            com.github.benmanes.caffeine.cache.Cache<Object, Object> nativeCache = 
                    (com.github.benmanes.caffeine.cache.Cache<Object, Object>) caffeineCache.getNativeCache();
            
            com.github.benmanes.caffeine.cache.stats.CacheStats stats = nativeCache.stats();
            
            return cn.com.handthing.starter.cache.CacheStats.builder()
                    .hitCount(stats.hitCount())
                    .missCount(stats.missCount())
                    .requestCount(stats.requestCount())
                    .hitRate(stats.hitRate())
                    .missRate(stats.missRate())
                    .size(nativeCache.estimatedSize())
                    .evictionCount(stats.evictionCount())
                    .averageLoadTime(stats.averageLoadPenalty())
                    .build();
        }
        return null;
    }

    /**
     * 清理所有缓存
     */
    public void invalidateAll() {
        for (Cache cache : cacheMap.values()) {
            if (cache instanceof CaffeineCache) {
                CaffeineCache caffeineCache = (CaffeineCache) cache;
                com.github.benmanes.caffeine.cache.Cache<Object, Object> nativeCache = 
                        (com.github.benmanes.caffeine.cache.Cache<Object, Object>) caffeineCache.getNativeCache();
                nativeCache.invalidateAll();
            }
        }
        log.info("Invalidated all Caffeine caches");
    }

    /**
     * 执行缓存维护操作
     */
    public void cleanUp() {
        for (Cache cache : cacheMap.values()) {
            if (cache instanceof CaffeineCache) {
                CaffeineCache caffeineCache = (CaffeineCache) cache;
                com.github.benmanes.caffeine.cache.Cache<Object, Object> nativeCache = 
                        (com.github.benmanes.caffeine.cache.Cache<Object, Object>) caffeineCache.getNativeCache();
                nativeCache.cleanUp();
            }
        }
        log.debug("Performed cleanup on all Caffeine caches");
    }
}