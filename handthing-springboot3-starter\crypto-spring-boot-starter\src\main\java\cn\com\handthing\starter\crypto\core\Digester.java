package cn.com.handthing.starter.crypto.core;

/**
 * 摘要（哈希）处理器接口。
 *
 * <AUTHOR>
 * @since V1.0.0
 */
public interface Digester extends CryptoService {

    /**
     * 计算给定数据的摘要。
     *
     * @param data 待计算摘要的原始数据 (UTF-8编码)
     * @return Hex（十六进制）编码的摘要字符串
     */
    String digest(String data);

    /**
     * 验证给定的原始数据是否与摘要匹配。
     * <p>
     * 此方法会重新计算原始数据的摘要，并与提供的摘要进行比较。
     *
     * @param data         原始数据 (UTF-8编码)
     * @param digestedData Hex（十六进制）编码的摘要字符串
     * @return 如果匹配，则返回 true；否则返回 false
     */
    boolean matches(String data, String digestedData);
}