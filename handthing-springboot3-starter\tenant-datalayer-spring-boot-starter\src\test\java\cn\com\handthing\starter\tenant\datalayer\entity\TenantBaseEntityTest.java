package cn.com.handthing.starter.tenant.datalayer.entity;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

/**
 * TenantBaseEntity测试
 *
 * <AUTHOR>
 * @since V1.0.0
 */
class TenantBaseEntityTest {
    
    /**
     * 测试租户实体类
     */
    static class TestTenantEntity extends TenantBaseEntity {
        private Long id;
        private String name;
        
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
    }
    
    @Test
    @DisplayName("应该正确识别租户归属")
    void shouldIdentifyTenantBelonging() {
        // Given
        TestTenantEntity entity = new TestTenantEntity();
        entity.setTenantId("tenant1");
        
        // When & Then
        assertThat(entity.belongsToTenant("tenant1")).isTrue();
        assertThat(entity.belongsToTenant("tenant2")).isFalse();
        assertThat(entity.belongsToTenant(null)).isFalse();
    }
    
    @Test
    @DisplayName("应该正确识别默认租户")
    void shouldIdentifyDefaultTenant() {
        // Given
        TestTenantEntity entity1 = new TestTenantEntity();
        entity1.setTenantId("default");
        
        TestTenantEntity entity2 = new TestTenantEntity();
        entity2.setTenantId("tenant1");
        
        // When & Then
        assertThat(entity1.isDefaultTenant()).isTrue();
        assertThat(entity2.isDefaultTenant()).isFalse();
    }
    
    @Test
    @DisplayName("应该正确检查租户信息")
    void shouldCheckTenantInfo() {
        // Given
        TestTenantEntity entity1 = new TestTenantEntity();
        entity1.setTenantId("tenant1");
        
        TestTenantEntity entity2 = new TestTenantEntity();
        entity2.setTenantId("");
        
        TestTenantEntity entity3 = new TestTenantEntity();
        entity3.setTenantId(null);
        
        // When & Then
        assertThat(entity1.hasTenant()).isTrue();
        assertThat(entity2.hasTenant()).isFalse();
        assertThat(entity3.hasTenant()).isFalse();
    }
    
    @Test
    @DisplayName("应该生成正确的租户摘要")
    void shouldGenerateCorrectTenantSummary() {
        // Given
        TestTenantEntity entity1 = new TestTenantEntity();
        entity1.setTenantId("tenant1");
        
        TestTenantEntity entity2 = new TestTenantEntity();
        entity2.setTenantId("default");
        
        TestTenantEntity entity3 = new TestTenantEntity();
        entity3.setTenantId(null);
        
        // When
        String summary1 = entity1.getTenantSummary();
        String summary2 = entity2.getTenantSummary();
        String summary3 = entity3.getTenantSummary();
        
        // Then
        assertThat(summary1).contains("tenant1");
        assertThat(summary1).doesNotContain("default=true");
        
        assertThat(summary2).contains("default");
        assertThat(summary2).contains("default=true");
        
        assertThat(summary3).isEqualTo("No tenant");
    }
    
    @Test
    @DisplayName("应该生成完整的审计摘要")
    void shouldGenerateFullAuditSummary() {
        // Given
        TestTenantEntity entity = new TestTenantEntity();
        entity.setTenantId("tenant1");
        entity.setCreateBy("user1");
        entity.setCreateTime(LocalDateTime.now());
        entity.setVersion(1L);
        
        // When
        String summary = entity.getFullAuditSummary();
        
        // Then
        assertThat(summary).contains("Tenant{id='tenant1'}");
        assertThat(summary).contains("createBy=user1");
        assertThat(summary).contains("version=1");
    }
    
    @Test
    @DisplayName("预插入处理应该验证租户ID")
    void shouldValidateTenantIdInPreInsert() {
        // Given
        TestTenantEntity entity1 = new TestTenantEntity();
        entity1.setTenantId("tenant1");
        
        TestTenantEntity entity2 = new TestTenantEntity();
        entity2.setTenantId(null);
        
        // When & Then
        // 有租户ID的实体应该正常处理
        entity1.preInsert();
        
        // 没有租户ID的实体应该抛出异常
        assertThatThrownBy(() -> entity2.preInsert())
                .isInstanceOf(IllegalStateException.class)
                .hasMessageContaining("Tenant ID is required");
    }
    
    @Test
    @DisplayName("预更新处理应该验证租户ID")
    void shouldValidateTenantIdInPreUpdate() {
        // Given
        TestTenantEntity entity1 = new TestTenantEntity();
        entity1.setTenantId("tenant1");
        
        TestTenantEntity entity2 = new TestTenantEntity();
        entity2.setTenantId(null);
        
        // When & Then
        // 有租户ID的实体应该正常处理
        entity1.preUpdate();
        
        // 没有租户ID的实体应该抛出异常
        assertThatThrownBy(() -> entity2.preUpdate())
                .isInstanceOf(IllegalStateException.class)
                .hasMessageContaining("Tenant ID is required");
    }
    
    @Test
    @DisplayName("应该正确验证租户一致性")
    void shouldValidateTenantConsistency() {
        // Given
        TestTenantEntity entity = new TestTenantEntity();
        entity.setTenantId("tenant1");
        
        // When & Then
        assertThat(entity.validateTenantConsistency("tenant1")).isTrue();
        assertThat(entity.validateTenantConsistency("tenant2")).isFalse();
        assertThat(entity.validateTenantConsistency(null)).isFalse();
        
        // 测试null租户ID的情况
        entity.setTenantId(null);
        assertThat(entity.validateTenantConsistency(null)).isTrue();
        assertThat(entity.validateTenantConsistency("tenant1")).isFalse();
    }
    
    @Test
    @DisplayName("设置租户ID内部方法应该正常工作")
    void shouldSetTenantIdInternally() {
        // Given
        TestTenantEntity entity = new TestTenantEntity();
        
        // When
        entity.setTenantIdInternal("tenant1");
        
        // Then
        assertThat(entity.getTenantId()).isEqualTo("tenant1");
        assertThat(entity.hasTenant()).isTrue();
    }
    
    @Test
    @DisplayName("克隆到租户方法应该抛出未实现异常")
    void shouldThrowUnsupportedOperationForCloneToTenant() {
        // Given
        TestTenantEntity entity = new TestTenantEntity();
        entity.setTenantId("tenant1");
        
        // When & Then
        assertThatThrownBy(() -> entity.cloneToTenant("tenant2"))
                .isInstanceOf(UnsupportedOperationException.class)
                .hasMessageContaining("Clone to tenant not implemented");
    }
    
    @Test
    @DisplayName("租户特定的处理方法应该可以被调用")
    void shouldCallTenantSpecificMethods() {
        // Given
        TestTenantEntity entity = new TestTenantEntity();
        entity.setTenantId("tenant1");
        
        // When & Then - 应该不抛出异常
        entity.doTenantPreInsert();
        entity.doTenantPreUpdate();
        entity.doTenantPostInsert();
        entity.doTenantPostUpdate();
    }
    
    @Test
    @DisplayName("后处理方法应该调用租户特定方法")
    void shouldCallTenantMethodsInPostProcessing() {
        // Given
        TestTenantEntity entity = new TestTenantEntity();
        entity.setTenantId("tenant1");
        
        // When & Then - 应该不抛出异常
        entity.postInsert();
        entity.postUpdate();
    }
    
    @Test
    @DisplayName("实体应该继承BaseEntity")
    void shouldExtendBaseEntity() {
        // Given
        TestTenantEntity entity = new TestTenantEntity();
        
        // When & Then
        assertThat(entity).isInstanceOf(cn.com.handthing.starter.datalayer.entity.BaseEntity.class);
    }
}
