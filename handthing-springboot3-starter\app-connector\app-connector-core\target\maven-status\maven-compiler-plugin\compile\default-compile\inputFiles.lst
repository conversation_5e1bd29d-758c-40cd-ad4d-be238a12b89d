D:\code\ai-project\handthing-springboot3-starter\app-connector\app-connector-core\src\main\java\cn\com\handthing\starter\connector\auth\AuthProvider.java
D:\code\ai-project\handthing-springboot3-starter\app-connector\app-connector-core\src\main\java\cn\com\handthing\starter\connector\auth\AuthService.java
D:\code\ai-project\handthing-springboot3-starter\app-connector\app-connector-core\src\main\java\cn\com\handthing\starter\connector\auth\UnifiedAccessToken.java
D:\code\ai-project\handthing-springboot3-starter\app-connector\app-connector-core\src\main\java\cn\com\handthing\starter\connector\auth\UnifiedUserInfo.java
D:\code\ai-project\handthing-springboot3-starter\app-connector\app-connector-core\src\main\java\cn\com\handthing\starter\connector\config\PlatformConfig.java
D:\code\ai-project\handthing-springboot3-starter\app-connector\app-connector-core\src\main\java\cn\com\handthing\starter\connector\event\ConnectorEvent.java
D:\code\ai-project\handthing-springboot3-starter\app-connector\app-connector-core\src\main\java\cn\com\handthing\starter\connector\event\UserAuthorizedEvent.java
D:\code\ai-project\handthing-springboot3-starter\app-connector\app-connector-core\src\main\java\cn\com\handthing\starter\connector\exception\ApiCallException.java
D:\code\ai-project\handthing-springboot3-starter\app-connector\app-connector-core\src\main\java\cn\com\handthing\starter\connector\exception\AuthException.java
D:\code\ai-project\handthing-springboot3-starter\app-connector\app-connector-core\src\main\java\cn\com\handthing\starter\connector\exception\ConnectorException.java
D:\code\ai-project\handthing-springboot3-starter\app-connector\app-connector-core\src\main\java\cn\com\handthing\starter\connector\PlatformType.java
