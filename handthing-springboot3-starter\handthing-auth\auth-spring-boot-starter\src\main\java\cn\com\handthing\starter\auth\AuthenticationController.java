package cn.com.handthing.starter.auth;

import cn.com.handthing.starter.auth.core.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 认证控制器
 * <p>
 * 提供认证相关的REST API端点，包括登录、登出、令牌刷新、用户信息获取等功能。
 * 支持多种认证方式的统一入口。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@RestController
@RequestMapping("${handthing.auth.web.auth-path:/auth}")
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "handthing.auth.web", name = "endpoints-enabled", havingValue = "true", matchIfMissing = true)
public class AuthenticationController {

    private final AuthenticationManager authenticationManager;
    private final ProviderRegistry providerRegistry;
    private final AuthProperties authProperties;
    private final JwtTokenProvider jwtTokenProvider;

    /**
     * 用户登录
     *
     * @param loginRequest 登录请求
     * @param request      HTTP请求
     * @return 认证响应
     */
    @PostMapping("/login")
    public ResponseEntity<Map<String, Object>> login(@RequestBody Map<String, Object> loginRequest,
                                                     HttpServletRequest request) {
        try {
            // 构建认证请求
            AuthenticationRequest authRequest = buildAuthenticationRequest(loginRequest, request);

            // 执行认证
            AuthenticationResponse authResponse = authenticationManager.authenticate(authRequest);

            // 构建响应
            Map<String, Object> response = buildSuccessResponse(authResponse);

            log.info("User login successful: {}", authResponse.getUserId());
            return ResponseEntity.ok(response);

        } catch (AuthenticationException e) {
            log.warn("User login failed: {}", e.getMessage());
            Map<String, Object> response = buildErrorResponse(e.getErrorCode(), e.getMessage());
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
        } catch (Exception e) {
            log.error("Login error", e);
            Map<String, Object> response = buildErrorResponse("LOGIN_ERROR", "Login failed");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 用户登出
     *
     * @param request HTTP请求
     * @return 响应结果
     */
    @PostMapping("/logout")
    public ResponseEntity<Map<String, Object>> logout(HttpServletRequest request) {
        try {
            // 提取令牌
            String token = AuthenticationUtils.extractTokenFromRequest(request, 
                    authProperties.getJwt() != null ? authProperties.getJwt() : new JwtConfig());

            if (token != null) {
                // TODO: 实现令牌黑名单或撤销逻辑
                log.info("User logout: token={}", token.substring(0, Math.min(token.length(), 10)) + "...");
            }

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Logout successful");
            response.put("timestamp", getCurrentTimestamp());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Logout error", e);
            Map<String, Object> response = buildErrorResponse("LOGOUT_ERROR", "Logout failed");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 刷新令牌
     *
     * @param refreshRequest 刷新请求
     * @return 认证响应
     */
    @PostMapping("/refresh")
    public ResponseEntity<Map<String, Object>> refresh(@RequestBody Map<String, Object> refreshRequest) {
        try {
            String refreshToken = (String) refreshRequest.get("refresh_token");
            if (refreshToken == null || refreshToken.trim().isEmpty()) {
                Map<String, Object> response = buildErrorResponse("INVALID_REQUEST", "Refresh token is required");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
            }

            // 刷新令牌
            AuthenticationResponse authResponse = authenticationManager.refreshToken(refreshToken);

            // 构建响应
            Map<String, Object> response = buildSuccessResponse(authResponse);

            log.info("Token refresh successful");
            return ResponseEntity.ok(response);

        } catch (AuthenticationException e) {
            log.warn("Token refresh failed: {}", e.getMessage());
            Map<String, Object> response = buildErrorResponse(e.getErrorCode(), e.getMessage());
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
        } catch (Exception e) {
            log.error("Token refresh error", e);
            Map<String, Object> response = buildErrorResponse("REFRESH_ERROR", "Token refresh failed");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 获取用户信息
     *
     * @param request HTTP请求
     * @return 用户信息
     */
    @GetMapping("/userinfo")
    public ResponseEntity<Map<String, Object>> getUserInfo(HttpServletRequest request) {
        try {
            // 从上下文获取用户信息
            UserContext userContext = UserContextHolder.getContext();
            if (userContext == null || !userContext.isAuthenticated()) {
                Map<String, Object> response = buildErrorResponse("UNAUTHORIZED", "User not authenticated");
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
            }

            UserInfo userInfo = userContext.getUserInfo();
            if (userInfo == null) {
                Map<String, Object> response = buildErrorResponse("USER_NOT_FOUND", "User information not found");
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }

            // 构建用户信息响应
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("user", buildUserInfoResponse(userInfo));
            response.put("timestamp", getCurrentTimestamp());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Get user info error", e);
            Map<String, Object> response = buildErrorResponse("USERINFO_ERROR", "Failed to get user information");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 获取支持的认证方式
     *
     * @return 支持的认证方式列表
     */
    @GetMapping("/grant-types")
    public ResponseEntity<Map<String, Object>> getSupportedGrantTypes() {
        try {
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("grant_types", authenticationManager.getSupportedGrantTypes());
            response.put("providers", providerRegistry.getProviderNames());
            response.put("timestamp", getCurrentTimestamp());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Get grant types error", e);
            Map<String, Object> response = buildErrorResponse("GRANT_TYPES_ERROR", "Failed to get grant types");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 构建认证请求
     *
     * @param loginRequest 登录请求参数
     * @param request      HTTP请求
     * @return 认证请求
     */
    private AuthenticationRequest buildAuthenticationRequest(Map<String, Object> loginRequest, HttpServletRequest request) {
        String grantTypeStr = (String) loginRequest.get("grant_type");
        if (grantTypeStr == null || grantTypeStr.trim().isEmpty()) {
            grantTypeStr = authProperties.getDefaultGrantType();
        }

        GrantType grantType = GrantType.fromCode(grantTypeStr);
        if (grantType == null) {
            throw new AuthenticationException("UNSUPPORTED_GRANT_TYPE", "Unsupported grant type: " + grantTypeStr);
        }

        // 根据授权类型创建对应的认证请求
        AuthenticationRequest authRequest = createSpecificAuthenticationRequest(grantType, loginRequest);

        // 设置通用请求信息
        authRequest.setClientId((String) loginRequest.get("client_id"));
        authRequest.setScope((String) loginRequest.get("scope"));
        authRequest.setIpAddress(AuthenticationUtils.getClientIpAddress(request));
        authRequest.setUserAgent(AuthenticationUtils.getUserAgent(request));

        // 设置扩展参数
        for (Map.Entry<String, Object> entry : loginRequest.entrySet()) {
            String key = entry.getKey();
            if (!"grant_type".equals(key) && !"client_id".equals(key) && !"scope".equals(key)) {
                authRequest.addExtraParam(key, entry.getValue());
            }
        }

        return authRequest;
    }

    /**
     * 根据授权类型创建具体的认证请求
     *
     * @param grantType    授权类型
     * @param loginRequest 登录请求参数
     * @return 具体的认证请求
     */
    private AuthenticationRequest createSpecificAuthenticationRequest(GrantType grantType, Map<String, Object> loginRequest) {
        switch (grantType) {
            case PASSWORD:
                return createPasswordAuthenticationRequest(loginRequest);
            case SMS_CODE:
                return createSmsAuthenticationRequest(loginRequest);
            case WECOM:
                return createWecomAuthenticationRequest(loginRequest);
            case DINGTALK:
                return createDingtalkAuthenticationRequest(loginRequest);
            case WECHAT:
                return createWechatAuthenticationRequest(loginRequest);
            case FEISHU:
                return createFeishuAuthenticationRequest(loginRequest);
            default:
                // 对于其他类型，创建通用认证请求
                return new AuthenticationRequest(grantType) {
                    @Override
                    public String getAuthenticationIdentifier() {
                        return (String) loginRequest.get("username");
                    }

                    @Override
                    public Object getCredentials() {
                        return loginRequest.get("password");
                    }
                };
        }
    }

    /**
     * 创建密码认证请求
     */
    private AuthenticationRequest createPasswordAuthenticationRequest(Map<String, Object> loginRequest) {
        try {
            Class<?> clazz = Class.forName("cn.com.handthing.starter.auth.password.PasswordAuthenticationRequest");
            AuthenticationRequest request = (AuthenticationRequest) clazz.getDeclaredConstructor().newInstance();

            // 设置用户名和密码
            clazz.getMethod("setUsername", String.class).invoke(request, (String) loginRequest.get("username"));
            clazz.getMethod("setPassword", String.class).invoke(request, (String) loginRequest.get("password"));

            // 设置记住我
            Boolean rememberMe = (Boolean) loginRequest.get("remember_me");
            if (rememberMe != null) {
                clazz.getMethod("setRememberMe", Boolean.class).invoke(request, rememberMe);
            }

            return request;
        } catch (Exception e) {
            log.warn("Failed to create PasswordAuthenticationRequest, using generic request", e);
            return createGenericAuthenticationRequest(GrantType.PASSWORD, loginRequest);
        }
    }

    /**
     * 创建短信认证请求
     */
    private AuthenticationRequest createSmsAuthenticationRequest(Map<String, Object> loginRequest) {
        try {
            Class<?> clazz = Class.forName("cn.com.handthing.starter.auth.sms.SmsAuthenticationRequest");
            AuthenticationRequest request = (AuthenticationRequest) clazz.getDeclaredConstructor().newInstance();

            // 设置手机号和验证码
            clazz.getMethod("setPhone", String.class).invoke(request, (String) loginRequest.get("phone"));
            clazz.getMethod("setSmsCode", String.class).invoke(request, (String) loginRequest.get("sms_code"));

            // 设置验证码类型
            String codeType = (String) loginRequest.get("code_type");
            if (codeType != null) {
                clazz.getMethod("setCodeType", String.class).invoke(request, codeType);
            }

            // 设置自动注册
            Boolean autoRegister = (Boolean) loginRequest.get("auto_register");
            if (autoRegister != null) {
                clazz.getMethod("setAutoRegister", Boolean.class).invoke(request, autoRegister);
            }

            // 设置昵称
            String nickname = (String) loginRequest.get("nickname");
            if (nickname != null) {
                clazz.getMethod("setNickname", String.class).invoke(request, nickname);
            }

            return request;
        } catch (Exception e) {
            log.warn("Failed to create SmsAuthenticationRequest, using generic request", e);
            return createGenericAuthenticationRequest(GrantType.SMS_CODE, loginRequest);
        }
    }

    /**
     * 创建企业微信认证请求
     */
    private AuthenticationRequest createWecomAuthenticationRequest(Map<String, Object> loginRequest) {
        try {
            Class<?> clazz = Class.forName("cn.com.handthing.starter.auth.thirdparty.WecomAuthenticationRequest");
            AuthenticationRequest request = (AuthenticationRequest) clazz.getDeclaredConstructor().newInstance();

            // 设置授权码和企业信息
            clazz.getMethod("setCode", String.class).invoke(request, (String) loginRequest.get("code"));
            clazz.getMethod("setCorpId", String.class).invoke(request, (String) loginRequest.get("corp_id"));
            clazz.getMethod("setAgentId", String.class).invoke(request, (String) loginRequest.get("agent_id"));
            clazz.getMethod("setCorpSecret", String.class).invoke(request, (String) loginRequest.get("corp_secret"));

            return request;
        } catch (Exception e) {
            log.warn("Failed to create WecomAuthenticationRequest, using generic request", e);
            return createGenericAuthenticationRequest(GrantType.WECOM, loginRequest);
        }
    }

    /**
     * 创建钉钉认证请求
     */
    private AuthenticationRequest createDingtalkAuthenticationRequest(Map<String, Object> loginRequest) {
        try {
            Class<?> clazz = Class.forName("cn.com.handthing.starter.auth.thirdparty.DingtalkAuthenticationRequest");
            AuthenticationRequest request = (AuthenticationRequest) clazz.getDeclaredConstructor().newInstance();

            // 设置授权码和应用信息
            clazz.getMethod("setCode", String.class).invoke(request, (String) loginRequest.get("code"));
            clazz.getMethod("setAppKey", String.class).invoke(request, (String) loginRequest.get("app_key"));
            clazz.getMethod("setAppSecret", String.class).invoke(request, (String) loginRequest.get("app_secret"));

            return request;
        } catch (Exception e) {
            log.warn("Failed to create DingtalkAuthenticationRequest, using generic request", e);
            return createGenericAuthenticationRequest(GrantType.DINGTALK, loginRequest);
        }
    }

    /**
     * 创建微信认证请求
     */
    private AuthenticationRequest createWechatAuthenticationRequest(Map<String, Object> loginRequest) {
        try {
            Class<?> clazz = Class.forName("cn.com.handthing.starter.auth.thirdparty.WechatAuthenticationRequest");
            AuthenticationRequest request = (AuthenticationRequest) clazz.getDeclaredConstructor().newInstance();

            // 设置授权码和应用信息
            clazz.getMethod("setCode", String.class).invoke(request, (String) loginRequest.get("code"));
            clazz.getMethod("setAppId", String.class).invoke(request, (String) loginRequest.get("app_id"));
            clazz.getMethod("setAppSecret", String.class).invoke(request, (String) loginRequest.get("app_secret"));

            return request;
        } catch (Exception e) {
            log.warn("Failed to create WechatAuthenticationRequest, using generic request", e);
            return createGenericAuthenticationRequest(GrantType.WECHAT, loginRequest);
        }
    }

    /**
     * 创建飞书认证请求
     */
    private AuthenticationRequest createFeishuAuthenticationRequest(Map<String, Object> loginRequest) {
        try {
            Class<?> clazz = Class.forName("cn.com.handthing.starter.auth.thirdparty.FeishuAuthenticationRequest");
            AuthenticationRequest request = (AuthenticationRequest) clazz.getDeclaredConstructor().newInstance();

            // 设置授权码和应用信息
            clazz.getMethod("setCode", String.class).invoke(request, (String) loginRequest.get("code"));
            clazz.getMethod("setAppId", String.class).invoke(request, (String) loginRequest.get("app_id"));
            clazz.getMethod("setAppSecret", String.class).invoke(request, (String) loginRequest.get("app_secret"));

            return request;
        } catch (Exception e) {
            log.warn("Failed to create FeishuAuthenticationRequest, using generic request", e);
            return createGenericAuthenticationRequest(GrantType.FEISHU, loginRequest);
        }
    }

    /**
     * 创建通用认证请求
     */
    private AuthenticationRequest createGenericAuthenticationRequest(GrantType grantType, Map<String, Object> loginRequest) {
        return new AuthenticationRequest(grantType) {
            @Override
            public String getAuthenticationIdentifier() {
                // 根据授权类型返回不同的标识符
                switch (grantType) {
                    case SMS_CODE:
                        return (String) loginRequest.get("phone");
                    case WECOM:
                        return (String) loginRequest.get("corp_id");
                    case DINGTALK:
                        return (String) loginRequest.get("app_key");
                    case WECHAT:
                    case FEISHU:
                        return (String) loginRequest.get("app_id");
                    default:
                        return (String) loginRequest.get("username");
                }
            }

            @Override
            public Object getCredentials() {
                // 根据授权类型返回不同的凭证
                switch (grantType) {
                    case SMS_CODE:
                        return loginRequest.get("sms_code");
                    case WECOM:
                    case DINGTALK:
                    case WECHAT:
                    case FEISHU:
                        return loginRequest.get("code");
                    default:
                        return loginRequest.get("password");
                }
            }
        };
    }

    /**
     * 构建成功响应
     *
     * @param authResponse 认证响应
     * @return 响应Map
     */
    private Map<String, Object> buildSuccessResponse(AuthenticationResponse authResponse) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("access_token", authResponse.getAccessToken());
        response.put("token_type", authResponse.getTokenType());
        response.put("expires_in", authResponse.getExpiresIn());
        response.put("scope", authResponse.getScope());
        response.put("timestamp", getCurrentTimestamp());

        if (authResponse.getRefreshToken() != null) {
            response.put("refresh_token", authResponse.getRefreshToken());
        }

        if (authResponse.getUserId() != null) {
            response.put("user_id", authResponse.getUserId());
        }

        if (authResponse.getUsername() != null) {
            response.put("username", authResponse.getUsername());
        }

        return response;
    }

    /**
     * 构建错误响应
     *
     * @param errorCode        错误代码
     * @param errorDescription 错误描述
     * @return 响应Map
     */
    private Map<String, Object> buildErrorResponse(String errorCode, String errorDescription) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("error", errorCode);
        response.put("error_description", errorDescription);
        response.put("timestamp", getCurrentTimestamp());
        return response;
    }

    /**
     * 获取当前时间戳字符串
     *
     * @return 格式化的时间戳字符串
     */
    private String getCurrentTimestamp() {
        return LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
    }

    /**
     * 构建用户信息响应
     *
     * @param userInfo 用户信息
     * @return 用户信息Map
     */
    private Map<String, Object> buildUserInfoResponse(UserInfo userInfo) {
        Map<String, Object> user = new HashMap<>();
        user.put("user_id", userInfo.getUserId());
        user.put("username", userInfo.getUsername());
        user.put("nickname", userInfo.getNickname());
        user.put("email", AuthenticationUtils.maskEmail(userInfo.getEmail()));
        user.put("phone", AuthenticationUtils.maskPhone(userInfo.getPhone()));
        user.put("avatar", userInfo.getAvatar());
        user.put("roles", userInfo.getRoles());
        user.put("permissions", userInfo.getPermissions());
        user.put("enabled", userInfo.getEnabled());
        user.put("last_login_time", userInfo.getLastLoginTime());
        return user;
    }
}
