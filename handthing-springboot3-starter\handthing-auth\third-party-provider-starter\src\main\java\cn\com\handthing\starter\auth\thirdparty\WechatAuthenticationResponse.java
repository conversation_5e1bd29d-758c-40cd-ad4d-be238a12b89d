package cn.com.handthing.starter.auth.thirdparty;

import cn.com.handthing.starter.auth.core.AuthenticationResponse;
import cn.com.handthing.starter.auth.core.UserInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 微信认证响应
 * <p>
 * 微信认证的响应对象，包含认证结果、用户信息、令牌信息等。
 * 支持新用户注册和老用户登录的不同响应信息。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WechatAuthenticationResponse extends AuthenticationResponse {

    /**
     * 用户信息
     */
    private UserInfo userInfo;

    /**
     * 是否为新注册用户
     */
    private Boolean newUser;

    /**
     * 微信OpenID
     */
    private String openId;

    /**
     * 微信UnionID
     */
    private String unionId;

    /**
     * 微信昵称
     */
    private String nickname;

    /**
     * 微信头像
     */
    private String headImgUrl;

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 授权范围
     */
    private String scope;

    /**
     * 最后登录时间
     */
    private LocalDateTime lastLoginTime;

    /**
     * 最后登录IP
     */
    private String lastLoginIp;

    /**
     * 是否为首次登录
     */
    private Boolean firstLogin;

    /**
     * 默认构造函数
     */
    public WechatAuthenticationResponse() {
        super();
    }

    /**
     * 成功响应构造函数
     *
     * @param accessToken  访问令牌
     * @param refreshToken 刷新令牌
     * @param expiresIn    过期时间
     * @param userInfo     用户信息
     */
    public WechatAuthenticationResponse(String accessToken, String refreshToken, Long expiresIn, UserInfo userInfo) {
        super(accessToken, refreshToken, expiresIn);
        this.userInfo = userInfo;
        if (userInfo != null) {
            setUserId(userInfo.getUserId());
            setUsername(userInfo.getUsername());
        }
    }

    /**
     * 失败响应构造函数
     *
     * @param errorCode        错误代码
     * @param errorDescription 错误描述
     */
    public WechatAuthenticationResponse(String errorCode, String errorDescription) {
        super(errorCode, errorDescription);
    }

    /**
     * 创建成功响应
     *
     * @param accessToken  访问令牌
     * @param refreshToken 刷新令牌
     * @param expiresIn    过期时间
     * @param userInfo     用户信息
     * @return 微信认证响应
     */
    public static WechatAuthenticationResponse success(String accessToken, String refreshToken, 
                                                      Long expiresIn, UserInfo userInfo) {
        return new WechatAuthenticationResponse(accessToken, refreshToken, expiresIn, userInfo);
    }

    /**
     * 创建成功响应（新用户）
     *
     * @param accessToken  访问令牌
     * @param refreshToken 刷新令牌
     * @param expiresIn    过期时间
     * @param userInfo     用户信息
     * @return 微信认证响应
     */
    public static WechatAuthenticationResponse successForNewUser(String accessToken, String refreshToken, 
                                                                Long expiresIn, UserInfo userInfo) {
        WechatAuthenticationResponse response = new WechatAuthenticationResponse(accessToken, refreshToken, expiresIn, userInfo);
        response.setNewUser(true);
        response.setFirstLogin(true);
        return response;
    }

    /**
     * 创建失败响应
     *
     * @param errorCode        错误代码
     * @param errorDescription 错误描述
     * @return 微信认证响应
     */
    public static WechatAuthenticationResponse failure(String errorCode, String errorDescription) {
        return new WechatAuthenticationResponse(errorCode, errorDescription);
    }

    /**
     * 创建授权码无效响应
     *
     * @return 微信认证响应
     */
    public static WechatAuthenticationResponse invalidCode() {
        return failure("INVALID_WECHAT_CODE", "微信授权码无效或已过期");
    }

    /**
     * 创建应用配置错误响应
     *
     * @param appId 应用ID
     * @return 微信认证响应
     */
    public static WechatAuthenticationResponse invalidAppConfig(String appId) {
        return failure("INVALID_APP_CONFIG", "微信应用配置错误: " + appId);
    }

    /**
     * 创建用户不存在响应
     *
     * @param openId 微信OpenID
     * @return 微信认证响应
     */
    public static WechatAuthenticationResponse userNotFound(String openId) {
        return failure("WECHAT_USER_NOT_FOUND", "微信用户不存在: " + openId);
    }

    /**
     * 创建用户被锁定响应
     *
     * @param openId 微信OpenID
     * @return 微信认证响应
     */
    public static WechatAuthenticationResponse userLocked(String openId) {
        return failure("USER_LOCKED", "用户已被锁定: " + openId);
    }

    /**
     * 创建用户被禁用响应
     *
     * @param openId 微信OpenID
     * @return 微信认证响应
     */
    public static WechatAuthenticationResponse userDisabled(String openId) {
        return failure("USER_DISABLED", "用户已被禁用: " + openId);
    }

    /**
     * 创建注册失败响应
     *
     * @param reason 失败原因
     * @return 微信认证响应
     */
    public static WechatAuthenticationResponse registrationFailed(String reason) {
        return failure("REGISTRATION_FAILED", "用户注册失败: " + reason);
    }

    /**
     * 创建API调用失败响应
     *
     * @param reason 失败原因
     * @return 微信认证响应
     */
    public static WechatAuthenticationResponse apiCallFailed(String reason) {
        return failure("WECHAT_API_FAILED", "微信API调用失败: " + reason);
    }

    /**
     * 创建授权范围不足响应
     *
     * @param scope 当前授权范围
     * @return 微信认证响应
     */
    public static WechatAuthenticationResponse insufficientScope(String scope) {
        return failure("INSUFFICIENT_SCOPE", "授权范围不足: " + scope);
    }

    /**
     * 设置用户信息并同步基础字段
     *
     * @param userInfo 用户信息
     */
    public void setUserInfo(UserInfo userInfo) {
        this.userInfo = userInfo;
        if (userInfo != null) {
            setUserId(userInfo.getUserId());
            setUsername(userInfo.getUsername());
            this.lastLoginTime = userInfo.getLastLoginTime();
            this.lastLoginIp = userInfo.getLastLoginIp();
        }
    }

    /**
     * 设置微信用户信息
     *
     * @param openId     微信OpenID
     * @param unionId    微信UnionID
     * @param nickname   微信昵称
     * @param headImgUrl 微信头像
     * @param appId      应用ID
     * @param scope      授权范围
     */
    public void setWechatUserInfo(String openId, String unionId, String nickname, String headImgUrl, 
                                 String appId, String scope) {
        this.openId = openId;
        this.unionId = unionId;
        this.nickname = nickname;
        this.headImgUrl = headImgUrl;
        this.appId = appId;
        this.scope = scope;
    }

    @Override
    public String toString() {
        if (isSuccess()) {
            return String.format("WechatAuthenticationResponse{success=true, userId='%s', openId='%s', appId='%s', newUser=%s, firstLogin=%s}",
                    getUserId(), openId, appId, newUser, firstLogin);
        } else {
            return String.format("WechatAuthenticationResponse{success=false, errorCode='%s', errorDescription='%s'}",
                    getErrorCode(), getErrorDescription());
        }
    }
}
