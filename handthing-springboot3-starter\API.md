# 📖 HandThing DataLayer API 文档

本文档详细介绍了 HandThing DataLayer Spring Boot Starter 系列的所有API接口和使用方法。

## 📋 目录

- [🔑 ID生成器模块](#-id生成器模块)
- [🏛️ 数据层核心模块](#️-数据层核心模块)
- [🏢 租户插件模块](#-租户插件模块)
- [🔐 数据权限插件模块](#-数据权限插件模块)

---

## 🔑 ID生成器模块

### @IdSetter 注解

**功能**：声明式ID自动注入注解

**使用位置**：字段

**属性**：

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| strategy | String | "" | ID生成策略名称，为空时根据字段类型自动选择 |
| skipIfPresent | boolean | true | 如果字段已有值是否跳过生成 |
| onFailure | FailureStrategy | LOG_ERROR | 生成失败时的处理策略 |

**示例**：

```java
public class User extends BaseEntity {
    @IdSetter  // 自动选择策略：Long→Snowflake, String→UUID
    @TableId
    private Long id;
    
    @IdSetter(strategy = "uuid")  // 指定使用UUID策略
    private String userNo;
    
    @IdSetter(skipIfPresent = false)  // 强制生成，即使字段有值
    private String serialNo;
}
```

### IdGenerator 接口

**功能**：ID生成器策略接口

**方法**：

```java
public interface IdGenerator<T> {
    /**
     * 生成ID
     */
    T generate();
    
    /**
     * 批量生成ID
     */
    default List<T> generateBatch(int count) {
        return IntStream.range(0, count)
                .mapToObj(i -> generate())
                .collect(Collectors.toList());
    }
    
    /**
     * 获取生成器名称
     */
    String getName();
    
    /**
     * 获取支持的类型
     */
    Class<T> getType();
}
```

### IdUtils 工具类

**功能**：ID生成工具类

**方法**：

```java
public class IdUtils {
    /**
     * 生成雪花ID
     */
    public static Long generateSnowflakeId()
    
    /**
     * 生成UUID
     */
    public static String generateUuid()
    
    /**
     * 生成短UUID（去掉横线）
     */
    public static String generateShortUuid()
    
    /**
     * 批量生成ID
     */
    public static <T> List<T> generateBatch(String strategy, int count)
    
    /**
     * 根据类型生成ID
     */
    public static <T> T generateByType(Class<T> type)
}
```

---

## 🏛️ 数据层核心模块

### BaseEntity 基础实体

**功能**：提供基础审计字段和通用方法

**字段**：

```java
public abstract class BaseEntity implements Serializable {
    /** 创建人 */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;
    
    /** 创建时间 */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    /** 更新人 */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;
    
    /** 更新时间 */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
    
    /** 版本号（乐观锁） */
    @Version
    private Long version;
    
    /** 逻辑删除标记 */
    @TableLogic
    private Integer deleted;
}
```

**方法**：

```java
/**
 * 检查是否为新实体
 */
public boolean isNew()

/**
 * 检查是否已删除
 */
public boolean isDeleted()

/**
 * 获取审计信息摘要
 */
public String getAuditSummary()

/**
 * 预插入处理
 */
public void preInsert()

/**
 * 预更新处理
 */
public void preUpdate()

/**
 * 后插入处理
 */
public void postInsert()

/**
 * 后更新处理
 */
public void postUpdate()
```

### BaseMapper 基础Mapper

**功能**：扩展MyBatis-Plus的BaseMapper，提供增强的CRUD方法

**继承关系**：

```java
public interface BaseMapper<T> extends com.baomidou.mybatisplus.core.mapper.BaseMapper<T> {
    // 继承MyBatis-Plus的所有基础方法
    // 可以添加自定义的通用方法
}
```

### BaseService 基础Service接口

**功能**：定义基础Service接口

**方法**：

```java
public interface BaseService<T> extends IService<T> {
    /**
     * 根据ID查询（带异常）
     */
    T getByIdWithException(Serializable id);
    
    /**
     * 批量保存或更新
     */
    boolean saveOrUpdateBatch(Collection<T> entityList, int batchSize);
    
    /**
     * 逻辑删除
     */
    boolean removeLogically(Serializable id);
    
    /**
     * 批量逻辑删除
     */
    boolean removeLogicallyByIds(Collection<? extends Serializable> idList);
}
```

### BaseServiceImpl 基础Service实现

**功能**：基础Service实现类，提供通用的业务逻辑

**使用示例**：

```java
@Service
public class UserService extends BaseServiceImpl<UserMapper, User> {
    
    public User createUser(User user) {
        // 自动填充ID、审计字段
        save(user);
        return user;
    }
    
    public User getUserById(Long id) {
        return getByIdWithException(id);  // 不存在时抛出异常
    }
    
    public boolean deleteUser(Long id) {
        return removeLogically(id);  // 逻辑删除
    }
}
```

### DatalayerUtils 工具类

**功能**：数据层通用工具方法

**方法**：

```java
public class DatalayerUtils {
    /**
     * 创建分页对象
     */
    public static <T> IPage<T> createPage(long current, long size)
    
    /**
     * 创建安全的分页对象
     */
    public static <T> IPage<T> createSafePage(Long current, Long size, long defaultSize, long maxSize)
    
    /**
     * 创建查询包装器
     */
    public static <T> QueryWrapper<T> createQueryWrapper()
    
    /**
     * 构建等值查询条件
     */
    public static <T> QueryWrapper<T> eq(QueryWrapper<T> wrapper, String column, Object value)
    
    /**
     * 构建模糊查询条件
     */
    public static <T> QueryWrapper<T> like(QueryWrapper<T> wrapper, String column, String value)
    
    /**
     * 构建动态查询条件
     */
    public static <T> QueryWrapper<T> buildDynamicQuery(Map<String, Object> params)
    
    /**
     * 分批处理集合
     */
    public static <T> void processBatch(List<T> list, int batchSize, BatchProcessor<T> processor)
}
```

---

## 🏢 租户插件模块

### TenantBaseEntity 租户基础实体

**功能**：继承BaseEntity，新增租户字段

**字段**：

```java
public abstract class TenantBaseEntity extends BaseEntity {
    /** 租户ID */
    @TableField(fill = FieldFill.INSERT)
    private String tenantId;
}
```

**方法**：

```java
/**
 * 检查是否属于指定租户
 */
public boolean belongsToTenant(String targetTenantId)

/**
 * 检查是否为默认租户
 */
public boolean isDefaultTenant()

/**
 * 检查是否有租户信息
 */
public boolean hasTenant()

/**
 * 获取租户信息摘要
 */
public String getTenantSummary()

/**
 * 验证租户一致性
 */
public boolean validateTenantConsistency(String contextTenantId)

/**
 * 设置租户ID（内部方法）
 */
public void setTenantIdInternal(String tenantId)
```

### TenantDatalayerUtils 租户工具类

**功能**：租户相关的数据操作工具方法

**方法**：

```java
public class TenantDatalayerUtils {
    /**
     * 获取当前租户ID
     */
    public static String getCurrentTenantId()
    
    /**
     * 检查是否有租户上下文
     */
    public static boolean hasTenantContext()
    
    /**
     * 在指定租户环境中执行操作
     */
    public static void runWithTenant(String tenantId, Runnable runnable)
    
    /**
     * 在指定租户环境中执行操作并返回结果
     */
    public static <T> T callWithTenant(String tenantId, Callable<T> callable)
    
    /**
     * 创建租户查询包装器
     */
    public static <T> QueryWrapper<T> createTenantQueryWrapper(String tenantId)
    
    /**
     * 检查实体是否属于当前租户
     */
    public static boolean belongsToCurrentTenant(TenantBaseEntity entity)
    
    /**
     * 设置实体租户ID为当前租户
     */
    public static void setCurrentTenant(TenantBaseEntity entity)
    
    /**
     * 过滤属于当前租户的实体
     */
    public static <T extends TenantBaseEntity> List<T> filterCurrentTenant(List<T> entities)
}
```

---

## 🔐 数据权限插件模块

### @DataPermission 注解

**功能**：声明式数据权限控制注解

**使用位置**：方法、类

**属性**：

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| type | DataPermissionType | DEPT | 权限类型 |
| field | String | "" | 权限字段名 |
| alias | String | "" | 表别名 |
| expression | String | "" | 自定义权限表达式 |
| enabled | String | "true" | 是否启用权限控制 |
| onFailure | FailureStrategy | FILTER | 权限失败处理策略 |
| ignoreSuperAdmin | boolean | true | 是否忽略超级管理员 |
| scope | PermissionScope | CURRENT | 权限范围 |
| cacheSeconds | int | 300 | 权限缓存时间 |

**权限类型**：

```java
public enum DataPermissionType {
    DEPT,    // 部门权限
    USER,    // 用户权限
    ROLE,    // 角色权限
    ORG,     // 组织权限
    CUSTOM,  // 自定义权限
    NONE     // 无权限
}
```

**权限范围**：

```java
public enum PermissionScope {
    CURRENT,                // 当前范围
    CURRENT_AND_CHILDREN,   // 当前及子级
    ALL,                    // 全部
    CUSTOM                  // 自定义
}
```

**失败策略**：

```java
public enum FailureStrategy {
    FILTER,     // 过滤数据
    EXCEPTION,  // 抛出异常
    LOG,        // 记录日志
    IGNORE      // 忽略
}
```

**示例**：

```java
@Service
public class OrderService extends BaseServiceImpl<OrderMapper, Order> {
    
    @DataPermission(type = DataPermissionType.DEPT)
    public List<Order> findDeptOrders() {
        return list(); // 自动添加部门权限条件
    }
    
    @DataPermission(type = DataPermissionType.USER, field = "create_by")
    public List<Order> findMyOrders() {
        return list(); // 只返回当前用户创建的订单
    }
    
    @DataPermission(
        type = DataPermissionType.CUSTOM,
        expression = "dept_id IN (#{deptIds}) OR create_by = #{userId}",
        scope = PermissionScope.CURRENT_AND_CHILDREN
    )
    public List<Order> findAccessibleOrders() {
        return list(); // 自定义权限条件
    }
}
```

### @IgnoreDataPermission 注解

**功能**：忽略数据权限检查注解

**使用位置**：方法、类

**属性**：

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| reason | String | "" | 忽略权限的原因 |
| onlyForSuperAdmin | boolean | false | 是否仅对超级管理员生效 |
| logIgnore | boolean | true | 是否记录忽略日志 |
| ignoreTypes | DataPermissionType[] | {} | 忽略的权限类型 |
| condition | String | "" | 条件表达式 |

**示例**：

```java
@Service
public class AdminService extends BaseServiceImpl<UserMapper, User> {
    
    @IgnoreDataPermission(reason = "管理员查看所有用户")
    public List<User> findAllUsers() {
        return list(); // 跳过权限检查
    }
    
    @IgnoreDataPermission(onlyForSuperAdmin = true)
    public void deleteAllData() {
        // 只有超级管理员可以执行
    }
}
```

### DataPermissionContext 权限上下文

**功能**：线程安全的权限上下文存储

**方法**：

```java
public class DataPermissionContext {
    /**
     * 获取当前线程的权限上下文
     */
    public static DataPermissionContext getCurrentContext()
    
    /**
     * 设置当前线程的权限上下文
     */
    public static void setCurrentContext(DataPermissionContext context)
    
    /**
     * 清除当前线程的权限上下文
     */
    public static void clearContext()
    
    /**
     * 创建权限上下文
     */
    public static DataPermissionContext create(DataPermission permission)
    
    /**
     * 添加权限参数
     */
    public void addParameter(String key, Object value)
    
    /**
     * 获取权限参数
     */
    public Object getParameter(String key)
}
```

### DataPermissionProvider 权限提供者接口

**功能**：权限策略提供者接口

**方法**：

```java
public interface DataPermissionProvider {
    /**
     * 检查是否支持指定的权限类型
     */
    boolean supports(DataPermissionType type);
    
    /**
     * 生成权限条件SQL片段
     */
    String generatePermissionCondition(DataPermission permission, String mappedStatementId);
    
    /**
     * 获取权限提供者的优先级
     */
    default int getOrder() { return 0; }
    
    /**
     * 验证权限配置
     */
    default String validatePermissionConfig(DataPermission permission) { return null; }
    
    /**
     * 检查权限提供者是否可用
     */
    default boolean isAvailable() { return true; }
}
```

**自定义权限提供者示例**：

```java
@Component
public class CustomPermissionProvider implements DataPermissionProvider {
    
    @Override
    public boolean supports(DataPermissionType type) {
        return DataPermissionType.CUSTOM == type;
    }
    
    @Override
    public String generatePermissionCondition(DataPermission permission, String mappedStatementId) {
        // 实现自定义权限逻辑
        return "custom_field = 'custom_value'";
    }
    
    @Override
    public int getOrder() {
        return 100; // 优先级
    }
}
```

---

## 🔧 配置API

所有模块都支持通过配置文件进行自定义配置，详细配置项请参考 [配置参考文档](./CONFIGURATION.md)。

## 🧪 测试API

框架提供了完整的测试支持，包括：

- **单元测试**：针对各个组件的单元测试
- **集成测试**：完整的集成测试示例
- **性能测试**：ID生成器性能测试

详细的测试示例请参考 [使用示例文档](./EXAMPLES.md)。

---

📝 **注意**：本文档涵盖了主要的API接口，更多详细信息请参考源码注释和JavaDoc文档。
