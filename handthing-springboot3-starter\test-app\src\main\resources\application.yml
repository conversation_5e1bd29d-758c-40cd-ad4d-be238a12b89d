spring:
  application:
    name: handthing-test-app
  profiles:
    active: dev
  jackson:
    # 配置Jackson支持Java 8时间类型
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8

  # 数据库配置
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
    username: sa
    password:
    hikari:
      maximum-pool-size: 10
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

  # H2数据库控制台配置
  h2:
    console:
      enabled: true
      path: /h2-console
      settings:
        web-allow-others: true

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true
        use_sql_comments: true
    open-in-view: false

  # SQL初始化配置
  sql:
    init:
      mode: always
      schema-locations: classpath:schema.sql
      data-locations: classpath:data.sql
      continue-on-error: false

server:
  port: 8081

# 默认配置 - 使用普通文本格式日志
logging:
  level:
    root: INFO
    cn.com.handthing: DEBUG
  custom:
    enable-json-format: false
    max-history: 30

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,loggers
  endpoint:
    health:
      show-details: always
  tracing:
    sampling:
      probability: 1.0

# HandThing 配置
handthing:
  # 缓存配置
  cache:
    type: redis
    redis:
      host: **************
      port: 6379
      password: qwer1234
      database: 0
      timeout: 5000ms
    specs:
      app-connector-tokens:
        ttl: 2h
        max-size: 1000

  # 分布式日志配置 - 暂时禁用
  # distributed-log:
  #   enabled: false

  # HTTP客户端配置
  http-client:
    enabled: true
    connect-timeout: 5000
    read-timeout: 10000

  # 多租户认证配置
  auth:
    saas:
      enabled: true
      default-tenant-id: "default"
      tenant-required: false

      # 租户解析器配置
      tenant:
        resolver:
          strategy: "header"
          multi-resolver: true

          # HTTP头解析配置
          header:
            enabled: true
            name: "X-Tenant-ID"
            fallback-names: ["Tenant-ID", "X-Tenant", "Tenant"]
            case-sensitive: false
            order: 20

          # 子域名解析配置
          subdomain:
            enabled: true
            pattern: "{tenant}.localhost:8081"
            base-domain: "localhost:8081"
            ignore-www: true
            order: 10

          # URL路径解析配置
          path:
            enabled: false
            pattern: "/api/{tenant}/**"
            prefix: "/api"
            remove-prefix: false
            order: 30

      # 缓存配置
      cache:
        enabled: true
        cache-name: "tenant-config"
        ttl: 5m
        max-size: 1000
        enable-stats: true

      # 过滤器配置
      filter:
        enabled: true
        order: -200
        exclude-paths:
          - "/actuator/**"
          - "/error"
          - "/favicon.ico"
          - "/static/**"
          - "/public/**"
          - "/login"
          - "/auth/login"
          - "/h2-console/**"
        log-resolution: true

      # 验证配置
      validation:
        enabled: true
        min-length: 1
        max-length: 64
        pattern: "^[a-zA-Z0-9][a-zA-Z0-9_-]*[a-zA-Z0-9]$|^[a-zA-Z0-9]$"
        allow-empty: false
        blacklist: ["admin", "root", "system", "api", "www"]

      # 监控配置
      monitoring:
        enabled: true
        enable-metrics: true
        enable-health-check: true
        enable-events: true
        retention-period: 7d

    # 启用认证框架
    enabled: true

    # 事件系统
    events-enabled: true

    # 指标收集
    metrics-enabled: true

    # 日志记录
    logging-enabled: true

    # JWT配置
    jwt:
      secret: "handthing-auth-test-secret-key-2024"
      access-token-expiration: PT2H  # 2小时
      refresh-token-expiration: P7D  # 7天
      issuer: "handthing-auth-test"

    # Web配置
    web:
      auth-path: "/auth"
      exclude-paths:
        - "/login"
        - "/auth/login"
        - "/auth/sms/send"
        # 第三方授权URL生成路径
        - "/auth/wecom/auth-url"
        - "/auth/dingtalk/auth-url"
        - "/auth/wechat/auth-url"
        - "/auth/feishu/auth-url"
        # 第三方回调路径
        - "/wecom/callback"
        - "/dingtalk/callback"
        - "/wechat/callback"
        - "/feishu/callback"
        # API统计路径
        - "/auth/grant-types"
        - "/auth/sms/stats"
        - "/auth/wecom/stats"
        - "/auth/dingtalk/stats"
        - "/auth/wechat/stats"
        - "/auth/feishu/stats"
        # 静态资源路径
        - "/css/**"
        - "/js/**"
        - "/images/**"
        - "/favicon.ico"
        - "/error"
        - "/"
        # 数据库控制台
        - "/h2-console/**"
        # 多租户测试API
        - "/api/tenant/**"
        # 静态资源
        - "/*.html"
        - "/*.css"
        - "/*.js"
        - "/tenant-test.html"
      static-paths:
        - "/static/**"
        - "/public/**"
        - "/*.html"
        - "/*.css"
        - "/*.js"
      filter-enabled: true
      filter-order: -100

    # 安全配置
    security:
      cors-enabled: true
      allowed-origins:
        - "http://localhost:8080"
        - "http://127.0.0.1:8080"
      allowed-methods:
        - "GET"
        - "POST"
        - "PUT"
        - "DELETE"
        - "OPTIONS"
      allowed-headers:
        - "*"
      allow-credentials: true
      max-age: PT1H

    # 密码认证配置
    password:
      enabled: true
      priority: 0

    # 短信认证配置
    sms:
      enabled: true
      endpoints-enabled: true

    # 第三方认证配置
    third-party:
      enabled: true

      # 企业微信配置
      wecom:
        enabled: true
        endpoints-enabled: true
        corp-id: "test_corp_id"
        corp-secret: "test_corp_secret"
        agent-id: "test_agent_id"
        redirect-uri: "http://localhost:8080/wecom/callback"
        api-timeout: PT10S
        auto-register: true

      # 钉钉配置
      dingtalk:
        enabled: true
        endpoints-enabled: true
        app-key: "test_app_key"
        app-secret: "test_app_secret"
        redirect-uri: "http://localhost:8081/dingtalk/callback"
        api-timeout: PT10S
        auto-register: true

      # 微信配置
      wechat:
        enabled: true
        endpoints-enabled: true
        app-id: "test_app_id"
        app-secret: "test_app_secret"
        redirect-uri: "http://localhost:8081/wechat/callback"
        api-timeout: PT10S
        auto-register: true

      # 飞书配置
      feishu:
        enabled: true
        endpoints-enabled: true
        app-id: "test_app_id"
        app-secret: "test_app_secret"
        redirect-uri: "http://localhost:8081/feishu/callback"
        api-timeout: PT10S
        auto-register: true

  # App Connector 配置
  connector:
    enabled: true
    redirect-uri-prefix: "http://localhost:8080/auth"

    # 企业微信配置（测试配置）
    wecom:
      enabled: true
      corp-id: "test-corp-id"
      agent-id: "test-agent-id"
      secret: "test-secret"

    # 钉钉配置（测试配置）
    dingtalk:
      enabled: true
      app-key: "test-app-key"
      app-secret: "test-app-secret"
      corp-id: "test-corp-id"

    # 抖音配置（测试配置）
    douyin:
      enabled: true
      client-key: "test-client-key"
      client-secret: "test-client-secret"

    # 支付宝配置（测试配置）
    alipay:
      enabled: true
      app-id: "test-app-id"
      private-key: "test-private-key"
      alipay-public-key: "test-public-key"
      sandbox: true

    # 微信配置（测试配置）
    wechat:
      enabled: true
      app-id: "test-wechat-app-id"
      app-secret: "test-wechat-app-secret"
      account-type: "service"
      mini-program-mode: false
      sandbox-mode: true
      encrypt-message: false

    # 飞书配置（测试配置）
    feishu:
      enabled: true
      app-id: "test-feishu-app-id"
      app-secret: "test-feishu-app-secret"
      app-type: "internal"
      third-party-mode: false
      bot-enabled: false
      lark-mode: false
      encrypt-message: false

    # 今日头条配置（测试配置）
    toutiao:
      enabled: true
      client-key: "test-toutiao-client-key"
      client-secret: "test-toutiao-client-secret"
      app-type: "web"
      enterprise-mode: false
      sandbox-mode: true
      content-publish-enabled: true
      analytics-enabled: true
      user-management-enabled: true

    # 小红书配置（测试配置）
    xiaohongshu:
      enabled: true
      app-key: "test-xiaohongshu-app-key"
      app-secret: "test-xiaohongshu-app-secret"
      app-type: "personal"
      brand-mode: false
      sandbox-mode: true
      content-publish-enabled: true
      community-enabled: true
      analytics-enabled: true
      ecommerce-enabled: false

    # Bilibili配置（测试配置）
    bilibili:
      enabled: true
      app-key: "test-bilibili-app-key"
      app-secret: "test-bilibili-app-secret"
      app-type: "personal"
      mcn-mode: false
      test-mode: true
      video-upload-enabled: true
      live-enabled: false
      analytics-enabled: true
      community-enabled: true

  # API 文档配置（包含 Knife4j 子配置）
  api-doc:
    enabled: true
    title: "HandThing API Documentation"  # 基础标题
    version: "1.0.0"                      # 基础版本
    description: "企业级 API 文档系统"      # 基础描述
    group: "handthing-api"                # 基础分组

