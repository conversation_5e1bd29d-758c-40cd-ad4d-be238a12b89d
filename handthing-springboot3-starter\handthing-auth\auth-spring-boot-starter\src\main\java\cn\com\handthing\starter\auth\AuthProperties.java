package cn.com.handthing.starter.auth;

import cn.com.handthing.starter.auth.core.JwtConfig;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;

/**
 * 认证配置属性
 * <p>
 * 定义认证框架的全局配置属性，包括启用状态、默认设置、安全配置等。
 * 支持多种认证方式的统一配置管理。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
@ConfigurationProperties(prefix = "handthing.auth")
public class AuthProperties {

    /**
     * 是否启用认证框架
     */
    private boolean enabled = true;

    /**
     * 默认授权类型
     */
    private String defaultGrantType = "password";

    /**
     * 是否启用多提供者模式
     */
    private boolean multiProviderEnabled = true;

    /**
     * 提供者优先级列表
     */
    private List<String> providerPriority = new ArrayList<>();

    /**
     * 是否启用认证缓存
     */
    private boolean cacheEnabled = true;

    /**
     * 缓存过期时间
     */
    private Duration cacheExpiration = Duration.ofMinutes(30);

    /**
     * 是否启用认证日志
     */
    private boolean loggingEnabled = true;

    /**
     * 是否启用认证指标
     */
    private boolean metricsEnabled = false;

    /**
     * 是否启用认证事件
     */
    private boolean eventsEnabled = true;

    /**
     * 最大认证失败次数
     */
    private int maxFailureAttempts = 5;

    /**
     * 认证失败锁定时间
     */
    private Duration failureLockDuration = Duration.ofMinutes(15);

    /**
     * 是否启用IP白名单
     */
    private boolean ipWhitelistEnabled = false;

    /**
     * IP白名单
     */
    private List<String> ipWhitelist = new ArrayList<>();

    /**
     * 是否启用IP黑名单
     */
    private boolean ipBlacklistEnabled = false;

    /**
     * IP黑名单
     */
    private List<String> ipBlacklist = new ArrayList<>();

    /**
     * 安全配置
     */
    private Security security = new Security();

    /**
     * Web配置
     */
    private Web web = new Web();

    /**
     * JWT配置
     */
    private JwtConfig jwt = new JwtConfig();

    /**
     * 安全配置
     */
    @Data
    public static class Security {

        /**
         * 是否启用CSRF保护
         */
        private boolean csrfEnabled = false;

        /**
         * 是否启用CORS
         */
        private boolean corsEnabled = true;

        /**
         * 允许的源
         */
        private List<String> allowedOrigins = new ArrayList<>();

        /**
         * 允许的方法
         */
        private List<String> allowedMethods = List.of("GET", "POST", "PUT", "DELETE", "OPTIONS");

        /**
         * 允许的头部
         */
        private List<String> allowedHeaders = List.of("*");

        /**
         * 是否允许凭证
         */
        private boolean allowCredentials = true;

        /**
         * 预检请求缓存时间
         */
        private Duration maxAge = Duration.ofHours(1);

        /**
         * 是否启用安全头部
         */
        private boolean securityHeadersEnabled = true;

        /**
         * 内容安全策略
         */
        private String contentSecurityPolicy = "default-src 'self'";

        /**
         * 是否启用HTTPS重定向
         */
        private boolean httpsRedirectEnabled = false;
    }

    /**
     * Web配置
     */
    @Data
    public static class Web {

        /**
         * 认证端点路径
         */
        private String authPath = "/auth";

        /**
         * 登录端点路径
         */
        private String loginPath = "/auth/login";

        /**
         * 登出端点路径
         */
        private String logoutPath = "/auth/logout";

        /**
         * 刷新令牌端点路径
         */
        private String refreshPath = "/auth/refresh";

        /**
         * 用户信息端点路径
         */
        private String userInfoPath = "/auth/userinfo";

        /**
         * 是否启用认证端点
         */
        private boolean endpointsEnabled = true;

        /**
         * 是否启用Swagger文档
         */
        private boolean swaggerEnabled = false;

        /**
         * 排除路径列表
         */
        private List<String> excludePaths = List.of(
                "/auth/login",
                "/auth/register",
                "/auth/forgot-password",
                "/health",
                "/actuator/**",
                "/swagger-ui/**",
                "/v3/api-docs/**"
        );

        /**
         * 静态资源路径
         */
        private List<String> staticPaths = List.of(
                "/static/**",
                "/public/**",
                "/resources/**",
                "/webjars/**",
                "/favicon.ico"
        );

        /**
         * 是否启用认证过滤器
         */
        private boolean filterEnabled = true;

        /**
         * 过滤器顺序
         */
        private int filterOrder = -100;
    }

    /**
     * 验证配置是否有效
     *
     * @return 如果配置有效返回true，否则返回false
     */
    public boolean isValid() {
        return enabled && defaultGrantType != null && !defaultGrantType.trim().isEmpty();
    }

    /**
     * 获取缓存过期时间（秒）
     *
     * @return 缓存过期时间（秒）
     */
    public long getCacheExpirationSeconds() {
        return cacheExpiration != null ? cacheExpiration.getSeconds() : 1800;
    }

    /**
     * 获取失败锁定时间（秒）
     *
     * @return 失败锁定时间（秒）
     */
    public long getFailureLockDurationSeconds() {
        return failureLockDuration != null ? failureLockDuration.getSeconds() : 900;
    }

    /**
     * 判断IP是否在白名单中
     *
     * @param ip IP地址
     * @return 如果在白名单中返回true，否则返回false
     */
    public boolean isIpInWhitelist(String ip) {
        return !ipWhitelistEnabled || ipWhitelist.contains(ip);
    }

    /**
     * 判断IP是否在黑名单中
     *
     * @param ip IP地址
     * @return 如果在黑名单中返回true，否则返回false
     */
    public boolean isIpInBlacklist(String ip) {
        return ipBlacklistEnabled && ipBlacklist.contains(ip);
    }

    /**
     * 判断路径是否被排除
     *
     * @param path 请求路径
     * @return 如果被排除返回true，否则返回false
     */
    public boolean isPathExcluded(String path) {
        if (path == null || web.excludePaths == null) {
            return false;
        }

        return web.excludePaths.stream().anyMatch(excludePath -> {
            if (excludePath.endsWith("/**")) {
                String prefix = excludePath.substring(0, excludePath.length() - 3);
                return path.startsWith(prefix);
            } else if (excludePath.endsWith("/*")) {
                String prefix = excludePath.substring(0, excludePath.length() - 2);
                return path.startsWith(prefix) && !path.substring(prefix.length()).contains("/");
            } else {
                return path.equals(excludePath);
            }
        });
    }

    @Override
    public String toString() {
        return String.format("AuthProperties{enabled=%s, defaultGrantType='%s', multiProviderEnabled=%s, cacheEnabled=%s}",
                enabled, defaultGrantType, multiProviderEnabled, cacheEnabled);
    }
}
