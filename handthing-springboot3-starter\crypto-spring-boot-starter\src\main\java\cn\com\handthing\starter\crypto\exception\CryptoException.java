package cn.com.handthing.starter.crypto.exception;

/**
 * 自定义密码学操作异常。
 * <p>
 * 这是 Starter 中所有密码学相关操作抛出的顶层运行时异常，
 * 用于封装底层的 checked exceptions (如 GeneralSecurityException)，
 * 简化上层业务代码的异常处理。
 *
 * <AUTHOR>
 * @since V1.0.0
 */
public class CryptoException extends RuntimeException {

    /**
     * 构造一个新的 CryptoException。
     *
     * @param message 异常的详细信息
     */
    public CryptoException(String message) {
        super(message);
    }

    /**
     * 构造一个新的 CryptoException。
     *
     * @param message 异常的详细信息
     * @param cause   异常的根本原因
     */
    public CryptoException(String message, Throwable cause) {
        super(message, cause);
    }
}