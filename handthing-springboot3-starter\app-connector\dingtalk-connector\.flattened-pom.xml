<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>cn.com.handthing.springboot3.starter</groupId>
    <artifactId>starter-parent</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <relativePath>../../starter-parent/pom.xml</relativePath>
  </parent>
  <artifactId>dingtalk-connector</artifactId>
  <version>1.0.0-SNAPSHOT</version>
  <name>HandThing :: App Connector :: DingTalk</name>
  <description>钉钉连接器，提供钉钉OAuth认证和API调用功能</description>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0</url>
    </license>
  </licenses>
  <dependencies>
    <dependency>
      <groupId>cn.com.handthing.springboot3.starter</groupId>
      <artifactId>app-connector-core</artifactId>
      <version>${revision}</version>
    </dependency>
    <dependency>
      <groupId>cn.com.handthing.springboot3.starter</groupId>
      <artifactId>app-connector-spring-boot-starter</artifactId>
      <version>${revision}</version>
    </dependency>
    <dependency>
      <groupId>cn.com.handthing.springboot3.starter</groupId>
      <artifactId>http-client-spring-boot-starter</artifactId>
      <version>${revision}</version>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-autoconfigure</artifactId>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-validation</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>
</project>
