package cn.com.handthing.starter.cache.caffeine.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * Caffeine缓存配置属性
 * <p>
 * 基于Caffeine高性能缓存库的配置
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
@ConfigurationProperties(prefix = "handthing.cache.caffeine")
public class CaffeineCacheProperties {

    /**
     * 是否启用Caffeine缓存
     */
    private boolean enabled = true;

    /**
     * 默认Caffeine配置规格字符串
     * 例如: "initialCapacity=50,maximumSize=500,expireAfterWrite=10m"
     */
    private String defaultSpec = "initialCapacity=50,maximumSize=1000,expireAfterWrite=30m";

    /**
     * 默认过期时间
     */
    private Duration defaultTtl = Duration.ofMinutes(30);

    /**
     * 是否启用统计信息
     */
    private boolean enableStats = true;

    /**
     * 是否启用软引用
     */
    private boolean enableSoftValues = false;

    /**
     * 是否启用弱引用键
     */
    private boolean enableWeakKeys = false;

    /**
     * 是否启用弱引用值
     */
    private boolean enableWeakValues = false;

    /**
     * 各个缓存区域的独立配置
     */
    private Map<String, CaffeineCacheSpec> caches = new HashMap<>();

    /**
     * Caffeine缓存区域配置
     */
    @Data
    public static class CaffeineCacheSpec {

        /**
         * Caffeine配置规格字符串
         */
        private String spec;

        /**
         * 最大缓存条目数
         */
        private Long maximumSize;

        /**
         * 初始容量
         */
        private Integer initialCapacity;

        /**
         * 写入后过期时间
         */
        private Duration expireAfterWrite;

        /**
         * 访问后过期时间
         */
        private Duration expireAfterAccess;

        /**
         * 刷新时间
         */
        private Duration refreshAfterWrite;

        /**
         * 是否启用统计信息
         */
        private Boolean enableStats;

        /**
         * 是否启用软引用值
         */
        private Boolean enableSoftValues;

        /**
         * 是否启用弱引用键
         */
        private Boolean enableWeakKeys;

        /**
         * 是否启用弱引用值
         */
        private Boolean enableWeakValues;

        /**
         * 获取有效的配置规格字符串
         *
         * @param defaultSpec 默认配置规格
         * @return 有效的配置规格字符串
         */
        public String getEffectiveSpec(String defaultSpec) {
            return spec != null ? spec : defaultSpec;
        }

        /**
         * 获取有效的统计信息启用状态
         *
         * @param defaultEnableStats 默认统计信息启用状态
         * @return 有效的统计信息启用状态
         */
        public boolean getEffectiveEnableStats(boolean defaultEnableStats) {
            return enableStats != null ? enableStats : defaultEnableStats;
        }

        /**
         * 获取有效的软引用值启用状态
         *
         * @param defaultEnableSoftValues 默认软引用值启用状态
         * @return 有效的软引用值启用状态
         */
        public boolean getEffectiveEnableSoftValues(boolean defaultEnableSoftValues) {
            return enableSoftValues != null ? enableSoftValues : defaultEnableSoftValues;
        }

        /**
         * 获取有效的弱引用键启用状态
         *
         * @param defaultEnableWeakKeys 默认弱引用键启用状态
         * @return 有效的弱引用键启用状态
         */
        public boolean getEffectiveEnableWeakKeys(boolean defaultEnableWeakKeys) {
            return enableWeakKeys != null ? enableWeakKeys : defaultEnableWeakKeys;
        }

        /**
         * 获取有效的弱引用值启用状态
         *
         * @param defaultEnableWeakValues 默认弱引用值启用状态
         * @return 有效的弱引用值启用状态
         */
        public boolean getEffectiveEnableWeakValues(boolean defaultEnableWeakValues) {
            return enableWeakValues != null ? enableWeakValues : defaultEnableWeakValues;
        }
    }
}