package cn.com.handthing.starter.cache.redis;

import cn.com.handthing.starter.cache.redis.config.RedisCacheProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheWriter;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.JdkSerializationRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

/**
 * Redis缓存管理器
 * <p>
 * 基于Spring Data Redis实现的分布式缓存管理器，支持多种序列化方式和灵活配置
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
public class RedisCacheManager implements CacheManager {

    private final RedisCacheProperties properties;
    private final org.springframework.data.redis.cache.RedisCacheManager delegateManager;

    /**
     * 构造函数
     *
     * @param properties        Redis缓存配置属性
     * @param connectionFactory Redis连接工厂
     */
    public RedisCacheManager(RedisCacheProperties properties, RedisConnectionFactory connectionFactory) {
        this.properties = properties;
        
        // 构建缓存配置映射
        Map<String, RedisCacheConfiguration> cacheConfigurations = new HashMap<>();
        for (Map.Entry<String, RedisCacheProperties.RedisCacheSpec> entry : properties.getCaches().entrySet()) {
            cacheConfigurations.put(entry.getKey(), buildCacheConfiguration(entry.getKey(), entry.getValue()));
        }
        
        // 创建委托的Redis缓存管理器
        this.delegateManager = org.springframework.data.redis.cache.RedisCacheManager.builder(connectionFactory)
                .cacheDefaults(buildCacheConfiguration(null, null))
                .withInitialCacheConfigurations(cacheConfigurations)
                .build();
        
        log.info("RedisCacheManager initialized with keyPrefix: {}, defaultTtl: {}", 
                properties.getKeyPrefix(), properties.getDefaultTtl());
    }

    @Override
    public Cache getCache(String name) {
        return delegateManager.getCache(name);
    }

    @Override
    public Collection<String> getCacheNames() {
        return delegateManager.getCacheNames();
    }



    /**
     * 构建缓存配置
     *
     * @param name 缓存名称
     * @param spec 缓存配置规格
     * @return Redis缓存配置
     */
    private RedisCacheConfiguration buildCacheConfiguration(String name, RedisCacheProperties.RedisCacheSpec spec) {
        RedisCacheConfiguration config = RedisCacheConfiguration.defaultCacheConfig();

        // 设置TTL
        if (spec != null) {
            config = config.entryTtl(spec.getEffectiveTtl(properties.getDefaultTtl()));
        } else {
            config = config.entryTtl(properties.getDefaultTtl());
        }

        // 设置键前缀
        boolean useKeyPrefix = spec != null ? 
                spec.getEffectiveUseKeyPrefix(properties.isUseKeyPrefix()) : 
                properties.isUseKeyPrefix();
        
        if (useKeyPrefix) {
            String keyPrefix = spec != null ? 
                    spec.getEffectiveKeyPrefix(properties.getKeyPrefix()) : 
                    properties.getKeyPrefix();
            config = config.prefixCacheNameWith(keyPrefix);
        } else {
            config = config.disableKeyPrefix();
        }

        // 设置是否允许null值
        boolean allowNullValues = spec != null ? 
                spec.getEffectiveAllowNullValues(properties.isAllowNullValues()) : 
                properties.isAllowNullValues();
        
        if (!allowNullValues) {
            config = config.disableCachingNullValues();
        }

        // 设置序列化
        RedisCacheProperties.Serialization serialization = spec != null ? 
                spec.getEffectiveSerialization(properties.getSerialization()) : 
                properties.getSerialization();
        
        config = config.serializeKeysWith(
                RedisSerializationContext.SerializationPair.fromSerializer(
                        (org.springframework.data.redis.serializer.RedisSerializer<String>) 
                        getKeySerializer(serialization.getKeyType())
                )
        );
        
        config = config.serializeValuesWith(
                RedisSerializationContext.SerializationPair.fromSerializer(
                        (org.springframework.data.redis.serializer.RedisSerializer<Object>) 
                        getValueSerializer(serialization.getValueType())
                )
        );

        return config;
    }

    /**
     * 根据序列化类型获取键序列化器
     *
     * @param type 序列化类型
     * @return 键序列化器
     */
    private org.springframework.data.redis.serializer.RedisSerializer<?> getKeySerializer(
            RedisCacheProperties.Serialization.SerializationType type) {
        switch (type) {
            case STRING:
                return new StringRedisSerializer();
            case JSON:
                return new GenericJackson2JsonRedisSerializer();
            case JDK:
            default:
                return new JdkSerializationRedisSerializer();
        }
    }

    /**
     * 根据序列化类型获取值序列化器
     *
     * @param type 序列化类型
     * @return 值序列化器
     */
    private org.springframework.data.redis.serializer.RedisSerializer<?> getValueSerializer(
            RedisCacheProperties.Serialization.SerializationType type) {
        switch (type) {
            case STRING:
                return new StringRedisSerializer();
            case JSON:
                return new GenericJackson2JsonRedisSerializer();
            case JDK:
            default:
                return new JdkSerializationRedisSerializer();
        }
    }

    /**
     * 获取缓存统计信息
     *
     * @param cacheName 缓存名称
     * @return 缓存统计信息，Redis缓存不直接支持统计信息
     */
    public cn.com.handthing.starter.cache.CacheStats getCacheStats(String cacheName) {
        // Redis缓存不直接支持统计信息，可以通过Redis命令获取一些基本信息
        log.debug("Redis cache does not support built-in statistics for cacheName: {}", cacheName);
        return null;
    }

    /**
     * 清理所有缓存
     */
    public void clear() {
        for (String cacheName : delegateManager.getCacheNames()) {
            Cache cache = delegateManager.getCache(cacheName);
            if (cache != null) {
                cache.clear();
            }
        }
        log.info("Cleared all Redis caches");
    }

    /**
     * 获取委托的Redis缓存管理器
     *
     * @return 委托的Redis缓存管理器
     */
    public org.springframework.data.redis.cache.RedisCacheManager getDelegateManager() {
        return delegateManager;
    }
}