package cn.com.handthing.starter.tenant.datalayer.handler;

import cn.com.handthing.starter.datalayer.handler.DatalayerMetaObjectHandler;
import cn.com.handthing.starter.tenant.context.TenantContextHolder;
import cn.com.handthing.starter.tenant.datalayer.config.TenantDatalayerProperties;
import cn.com.handthing.starter.tenant.datalayer.entity.TenantBaseEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 租户元数据处理器
 * <p>
 * 扩展DatalayerMetaObjectHandler，在insertFill时自动填充tenantId。
 * 支持租户上下文集成和租户一致性验证。
 * </p>
 * 
 * <h3>功能特性：</h3>
 * <ul>
 *   <li>自动填充租户ID</li>
 *   <li>租户上下文集成</li>
 *   <li>租户一致性验证</li>
 *   <li>扩展基础元数据处理</li>
 * </ul>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Component
public class TenantMetaObjectHandler extends DatalayerMetaObjectHandler {
    
    /**
     * 租户数据层配置
     */
    @Autowired
    private TenantDatalayerProperties properties;
    
    /**
     * 插入填充扩展点（重写以支持租户填充）
     */
    @Override
    protected void doInsertFillExtension(MetaObject metaObject) {
        super.doInsertFillExtension(metaObject);
        
        // 填充租户ID
        fillTenantId(metaObject);
        
        // 验证租户一致性
        validateTenantConsistency(metaObject);
    }
    
    /**
     * 更新填充扩展点（重写以支持租户验证）
     */
    @Override
    protected void doUpdateFillExtension(MetaObject metaObject) {
        super.doUpdateFillExtension(metaObject);
        
        // 验证租户一致性
        validateTenantConsistency(metaObject);
    }
    
    /**
     * 填充租户ID
     */
    protected void fillTenantId(MetaObject metaObject) {
        try {
            // 检查是否为租户实体
            Object entity = metaObject.getOriginalObject();
            if (!(entity instanceof TenantBaseEntity)) {
                log.debug("Entity {} is not a tenant entity, skipping tenant ID fill", 
                        entity.getClass().getName());
                return;
            }
            
            // 获取租户ID字段名
            String tenantIdColumn = properties.getTenantIdColumn();
            
            // 检查字段是否已有值
            if (metaObject.hasGetter(tenantIdColumn)) {
                Object currentValue = metaObject.getValue(tenantIdColumn);
                if (currentValue != null) {
                    log.debug("Tenant ID field {} already has value: {}, skipping fill", 
                            tenantIdColumn, currentValue);
                    return;
                }
            }
            
            // 获取当前租户ID
            String currentTenantId = getCurrentTenantId();
            if (currentTenantId == null || currentTenantId.trim().isEmpty()) {
                log.warn("No tenant ID found in context, using default tenant");
                currentTenantId = getDefaultTenantId();
            }
            
            // 填充租户ID
            if (metaObject.hasSetter(tenantIdColumn)) {
                metaObject.setValue(tenantIdColumn, currentTenantId);
                log.debug("Filled tenant ID for entity {}: {}", 
                        entity.getClass().getName(), currentTenantId);
            } else {
                log.warn("No setter found for tenant ID field: {}", tenantIdColumn);
            }
            
        } catch (Exception e) {
            log.error("Failed to fill tenant ID", e);
            
            // 根据配置决定是否抛出异常
            if (properties.isStrictMode()) {
                throw new RuntimeException("Failed to fill tenant ID in strict mode", e);
            }
        }
    }
    
    /**
     * 验证租户一致性
     */
    protected void validateTenantConsistency(MetaObject metaObject) {
        if (!properties.isValidateConsistency()) {
            return;
        }
        
        try {
            // 检查是否为租户实体
            Object entity = metaObject.getOriginalObject();
            if (!(entity instanceof TenantBaseEntity)) {
                return;
            }
            
            TenantBaseEntity tenantEntity = (TenantBaseEntity) entity;
            
            // 获取当前上下文租户ID
            String contextTenantId = getCurrentTenantId();
            if (contextTenantId == null || contextTenantId.trim().isEmpty()) {
                contextTenantId = getDefaultTenantId();
            }
            
            // 验证租户一致性
            if (!tenantEntity.validateTenantConsistency(contextTenantId)) {
                String errorMsg = String.format(
                        "Tenant consistency validation failed: entity tenant ID '%s' does not match context tenant ID '%s'",
                        tenantEntity.getTenantId(), contextTenantId);
                
                log.error(errorMsg);
                
                if (properties.isStrictMode()) {
                    throw new IllegalStateException(errorMsg);
                }
            } else {
                log.debug("Tenant consistency validation passed for entity: {}", 
                        entity.getClass().getName());
            }
            
        } catch (Exception e) {
            log.error("Failed to validate tenant consistency", e);
            
            if (properties.isStrictMode()) {
                throw new RuntimeException("Failed to validate tenant consistency in strict mode", e);
            }
        }
    }
    
    /**
     * 获取当前租户ID
     * 
     * @return 当前租户ID，如果不存在返回null
     */
    protected String getCurrentTenantId() {
        try {
            return TenantContextHolder.getTenantId();
        } catch (Exception e) {
            log.debug("Failed to get tenant ID from TenantContextHolder", e);
            return null;
        }
    }
    
    /**
     * 获取默认租户ID
     * 
     * @return 默认租户ID
     */
    protected String getDefaultTenantId() {
        String defaultTenantId = properties.getDefaultTenantId();
        return defaultTenantId != null ? defaultTenantId : "default";
    }
    
    /**
     * 检查租户上下文是否可用
     * 
     * @return 如果可用返回true，否则返回false
     */
    protected boolean isTenantContextAvailable() {
        try {
            String tenantId = getCurrentTenantId();
            return tenantId != null && !tenantId.trim().isEmpty();
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 获取租户信息摘要
     * 
     * @return 租户信息摘要
     */
    protected String getTenantSummary() {
        try {
            String tenantId = getCurrentTenantId();
            if (tenantId == null || tenantId.trim().isEmpty()) {
                return "No tenant context";
            }
            
            return String.format("Tenant{id='%s', column='%s', strict=%s, validate=%s}", 
                    tenantId, 
                    properties.getTenantIdColumn(),
                    properties.isStrictMode(),
                    properties.isValidateConsistency());
        } catch (Exception e) {
            return "Tenant context error: " + e.getMessage();
        }
    }
    
    /**
     * 强制填充租户ID（用于特殊场景）
     * <p>
     * 注意：此方法会绕过正常的租户上下文，仅在特殊情况下使用
     * </p>
     * 
     * @param metaObject 元对象
     * @param tenantId   租户ID
     */
    public void forceFillTenantId(MetaObject metaObject, String tenantId) {
        if (tenantId == null || tenantId.trim().isEmpty()) {
            log.warn("Force fill tenant ID with empty value, using default");
            tenantId = getDefaultTenantId();
        }
        
        String tenantIdColumn = properties.getTenantIdColumn();
        if (metaObject.hasSetter(tenantIdColumn)) {
            metaObject.setValue(tenantIdColumn, tenantId);
            log.info("Force filled tenant ID: {} -> {}", tenantIdColumn, tenantId);
        } else {
            log.warn("Cannot force fill tenant ID, no setter found for: {}", tenantIdColumn);
        }
    }
    
    /**
     * 清除租户ID（用于特殊场景）
     * <p>
     * 注意：此方法会清除实体的租户ID，可能导致数据安全问题
     * </p>
     * 
     * @param metaObject 元对象
     */
    public void clearTenantId(MetaObject metaObject) {
        String tenantIdColumn = properties.getTenantIdColumn();
        if (metaObject.hasSetter(tenantIdColumn)) {
            metaObject.setValue(tenantIdColumn, null);
            log.warn("Cleared tenant ID for security reasons");
        }
    }
    
    /**
     * 验证配置
     * 
     * @return 验证结果，如果配置有效返回空字符串，否则返回错误信息
     */
    public String validateConfiguration() {
        StringBuilder errors = new StringBuilder();
        
        if (properties == null) {
            errors.append("TenantDatalayerProperties is null; ");
        } else {
            if (properties.getTenantIdColumn() == null || properties.getTenantIdColumn().trim().isEmpty()) {
                errors.append("Tenant ID column is not configured; ");
            }
        }
        
        if (!isTenantContextAvailable() && properties.isStrictMode()) {
            errors.append("Tenant context is not available in strict mode; ");
        }
        
        return errors.toString();
    }
}
