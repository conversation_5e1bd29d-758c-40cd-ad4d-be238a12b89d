package cn.com.handthing.starter.connector.wechat;

import cn.com.handthing.starter.connector.PlatformType;
import cn.com.handthing.starter.connector.auth.AuthProvider;
import cn.com.handthing.starter.connector.auth.UnifiedAccessToken;
import cn.com.handthing.starter.connector.auth.UnifiedUserInfo;
import cn.com.handthing.starter.connector.exception.AuthException;
import cn.com.handthing.starter.connector.wechat.config.WeChatConfig;
import cn.com.handthing.starter.connector.wechat.model.WeChatAccessTokenResponse;
import cn.com.handthing.starter.connector.wechat.model.WeChatUserInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 微信认证提供者
 * <p>
 * 实现微信OAuth2.0认证流程，支持公众号和小程序的用户认证。
 * 提供统一的认证接口，处理授权码交换、Token刷新等操作。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "handthing.connector.wechat", name = "enabled", havingValue = "true")
public class WeChatAuthProvider implements AuthProvider {

    private final WeChatConfig config;
    private final RestTemplate restTemplate;

    @Override
    public PlatformType getPlatformType() {
        return PlatformType.WECHAT;
    }

    @Override
    public String getAuthorizationUrl(String state, String redirectUri) {
        try {
            log.debug("Generating WeChat authorization URL with state: {}, redirectUri: {}", state, redirectUri);

            // 使用配置的回调地址或传入的地址
            String effectiveRedirectUri = redirectUri != null ? redirectUri : config.getRedirectUri();
            if (effectiveRedirectUri == null || effectiveRedirectUri.trim().isEmpty()) {
                throw new AuthException("Redirect URI is required for WeChat authorization");
            }

            // 构建微信授权URL
            String scope = config.isMiniProgramMode() ? "snsapi_base" : "snsapi_userinfo";
            
            return UriComponentsBuilder.fromHttpUrl("https://open.weixin.qq.com/connect/oauth2/authorize")
                    .queryParam("appid", config.getAppId())
                    .queryParam("redirect_uri", URLEncoder.encode(effectiveRedirectUri, StandardCharsets.UTF_8))
                    .queryParam("response_type", "code")
                    .queryParam("scope", scope)
                    .queryParam("state", state)
                    .fragment("wechat_redirect")
                    .build()
                    .toUriString();

        } catch (Exception e) {
            log.error("Failed to generate WeChat authorization URL", e);
            throw new AuthException("Failed to generate authorization URL", e);
        }
    }

    @Override
    public UnifiedAccessToken exchangeToken(String code, String state, String redirectUri) {
        try {
            log.debug("Exchanging WeChat authorization code for access token: {}", code);

            // 构建获取access_token的URL
            String tokenUrl = UriComponentsBuilder.fromHttpUrl(config.getEffectiveApiBaseUrl() + "/sns/oauth2/access_token")
                    .queryParam("appid", config.getAppId())
                    .queryParam("secret", config.getAppSecret())
                    .queryParam("code", code)
                    .queryParam("grant_type", "authorization_code")
                    .build()
                    .toUriString();

            // 发送请求获取access_token
            ResponseEntity<WeChatAccessTokenResponse> response = restTemplate.getForEntity(
                    tokenUrl, WeChatAccessTokenResponse.class);

            WeChatAccessTokenResponse tokenResponse = response.getBody();
            if (tokenResponse == null) {
                throw new AuthException("Empty response from WeChat token endpoint");
            }

            if (tokenResponse.getErrcode() != null && tokenResponse.getErrcode() != 0) {
                throw new AuthException("WeChat token exchange failed: " + tokenResponse.getErrmsg());
            }

            // 转换为统一的访问令牌格式
            Map<String, Object> extraInfo = new HashMap<>();
            extraInfo.put("openId", tokenResponse.getOpenid());
            extraInfo.put("unionId", tokenResponse.getUnionid());

            return UnifiedAccessToken.builder()
                    .accessToken(tokenResponse.getAccessToken())
                    .refreshToken(tokenResponse.getRefreshToken())
                    .expiresIn(tokenResponse.getExpiresIn() != null ? tokenResponse.getExpiresIn().longValue() : 7200L)
                    .scope(tokenResponse.getScope())
                    .platform(PlatformType.WECHAT)
                    .userId(tokenResponse.getOpenid())
                    .extraInfo(extraInfo)
                    .createdAt(LocalDateTime.now())
                    .build();

        } catch (Exception e) {
            log.error("Failed to exchange WeChat authorization code for token", e);
            throw new AuthException("Failed to exchange code for token", e);
        }
    }

    @Override
    public UnifiedAccessToken refreshToken(String refreshToken) {
        try {
            log.debug("Refreshing WeChat access token");

            // 构建刷新token的URL
            String refreshUrl = UriComponentsBuilder.fromHttpUrl(config.getEffectiveApiBaseUrl() + "/sns/oauth2/refresh_token")
                    .queryParam("appid", config.getAppId())
                    .queryParam("grant_type", "refresh_token")
                    .queryParam("refresh_token", refreshToken)
                    .build()
                    .toUriString();

            // 发送请求刷新token
            ResponseEntity<WeChatAccessTokenResponse> response = restTemplate.getForEntity(
                    refreshUrl, WeChatAccessTokenResponse.class);

            WeChatAccessTokenResponse tokenResponse = response.getBody();
            if (tokenResponse == null) {
                throw new AuthException("Empty response from WeChat refresh token endpoint");
            }

            if (tokenResponse.getErrcode() != null && tokenResponse.getErrcode() != 0) {
                throw new AuthException("WeChat token refresh failed: " + tokenResponse.getErrmsg());
            }

            // 转换为统一的访问令牌格式
            Map<String, Object> extraInfo = new HashMap<>();
            extraInfo.put("openId", tokenResponse.getOpenid());
            extraInfo.put("unionId", tokenResponse.getUnionid());

            return UnifiedAccessToken.builder()
                    .accessToken(tokenResponse.getAccessToken())
                    .refreshToken(tokenResponse.getRefreshToken())
                    .expiresIn(tokenResponse.getExpiresIn() != null ? tokenResponse.getExpiresIn().longValue() : 7200L)
                    .scope(tokenResponse.getScope())
                    .platform(PlatformType.WECHAT)
                    .userId(tokenResponse.getOpenid())
                    .extraInfo(extraInfo)
                    .createdAt(LocalDateTime.now())
                    .build();

        } catch (Exception e) {
            log.error("Failed to refresh WeChat access token", e);
            throw new AuthException("Failed to refresh token", e);
        }
    }

    @Override
    public UnifiedUserInfo getUserInfo(String accessToken) {
        try {
            log.debug("Getting WeChat user info with access token");

            // 对于微信，我们需要先获取openId，这里简化处理
            // 在实际应用中，openId应该从token存储中获取
            throw new AuthException("WeChat getUserInfo requires openId parameter. Use getUserInfo(accessToken, openId) instead.");
        } catch (Exception e) {
            log.error("Failed to get WeChat user info", e);
            throw new AuthException("Failed to get user info", e);
        }
    }

    /**
     * 获取用户信息（微信专用，需要openId）
     *
     * @param accessToken 访问令牌
     * @param openId      用户openId
     * @return 用户信息
     */
    public UnifiedUserInfo getUserInfo(String accessToken, String openId) {
        try {
            log.debug("Getting WeChat user info for openId: {}", openId);

            // 如果是基础授权模式，只返回基本信息
            if (config.isMiniProgramMode() || "snsapi_base".equals(getScope())) {
                return UnifiedUserInfo.builder()
                        .openId(openId)
                        .platform(PlatformType.WECHAT)
                        .build();
            }

            // 构建获取用户信息的URL
            String userInfoUrl = UriComponentsBuilder.fromHttpUrl(config.getEffectiveApiBaseUrl() + "/sns/userinfo")
                    .queryParam("access_token", accessToken)
                    .queryParam("openid", openId)
                    .queryParam("lang", "zh_CN")
                    .build()
                    .toUriString();

            // 发送请求获取用户信息
            ResponseEntity<WeChatUserInfo> response = restTemplate.getForEntity(
                    userInfoUrl, WeChatUserInfo.class);

            WeChatUserInfo userInfo = response.getBody();
            if (userInfo == null) {
                throw new AuthException("Empty response from WeChat user info endpoint");
            }

            if (userInfo.getErrcode() != null && userInfo.getErrcode() != 0) {
                throw new AuthException("WeChat get user info failed: " + userInfo.getErrmsg());
            }

            // 转换为统一的用户信息格式
            return UnifiedUserInfo.builder()
                    .openId(userInfo.getOpenid())
                    .unionId(userInfo.getUnionid())
                    .nickname(userInfo.getNickname())
                    .avatar(userInfo.getHeadimgurl())
                    .gender(parseGender(userInfo.getSex()))
                    .country(userInfo.getCountry())
                    .province(userInfo.getProvince())
                    .city(userInfo.getCity())
                    .platform(PlatformType.WECHAT)
                    .build();

        } catch (Exception e) {
            log.error("Failed to get WeChat user info", e);
            throw new AuthException("Failed to get user info", e);
        }
    }

    @Override
    public boolean validateToken(String accessToken) {
        // 微信的token验证需要openId，这里简化处理
        return true;
    }

    /**
     * 验证访问令牌是否有效（微信专用，需要openId）
     *
     * @param accessToken 访问令牌
     * @param openId      用户openId
     * @return 如果令牌有效返回true，否则返回false
     */
    public boolean validateToken(String accessToken, String openId) {
        try {
            log.debug("Validating WeChat access token for openId: {}", openId);

            // 构建验证token的URL
            String validateUrl = UriComponentsBuilder.fromHttpUrl(config.getEffectiveApiBaseUrl() + "/sns/auth")
                    .queryParam("access_token", accessToken)
                    .queryParam("openid", openId)
                    .build()
                    .toUriString();

            // 发送请求验证token
            ResponseEntity<Map> response = restTemplate.getForEntity(validateUrl, Map.class);
            Map<String, Object> result = response.getBody();

            if (result == null) {
                return false;
            }

            Integer errcode = (Integer) result.get("errcode");
            return errcode != null && errcode == 0;

        } catch (Exception e) {
            log.error("Failed to validate WeChat access token", e);
            return false;
        }
    }

    /**
     * 获取授权范围
     */
    private String getScope() {
        return config.isMiniProgramMode() ? "snsapi_base" : "snsapi_userinfo";
    }

    /**
     * 解析性别信息
     */
    private Integer parseGender(Integer sex) {
        if (sex == null) {
            return 0; // 未知
        }
        switch (sex) {
            case 1:
                return 1; // 男性
            case 2:
                return 2; // 女性
            default:
                return 0; // 未知
        }
    }
}
