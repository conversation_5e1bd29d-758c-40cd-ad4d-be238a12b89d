package cn.com.handthing.starter.connector.wechat.config;

import cn.com.handthing.starter.connector.auth.AuthService;
import cn.com.handthing.starter.connector.auth.DefaultAuthService;
import cn.com.handthing.starter.connector.token.TokenManager;
import cn.com.handthing.starter.connector.wechat.WeChatAuthProvider;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.web.client.RestTemplate;

import jakarta.annotation.PostConstruct;

/**
 * 微信连接器自动配置
 * <p>
 * 自动配置微信连接器相关的Bean，包括配置类、认证提供者、服务类等。
 * 当启用微信连接器时，自动注册到认证服务和Token管理器中。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@RequiredArgsConstructor
@AutoConfiguration
@EnableConfigurationProperties(WeChatConfig.class)
@ConditionalOnProperty(prefix = "handthing.connector.wechat", name = "enabled", havingValue = "true")
@ConditionalOnClass(AuthService.class)
@ComponentScan(basePackages = "cn.com.handthing.starter.connector.wechat")
public class WeChatAutoConfiguration {

    private final AuthService authService;
    private final WeChatAuthProvider weChatAuthProvider;
    private final TokenManager tokenManager;
    private final WeChatConfig weChatConfig;



    /**
     * 注册微信认证提供者到认证服务
     */
    @PostConstruct
    public void registerWeChatAuthProvider() {
        if (authService instanceof DefaultAuthService) {
            DefaultAuthService defaultAuthService = (DefaultAuthService) authService;
            defaultAuthService.registerAuthProvider(weChatAuthProvider);
            tokenManager.registerAuthProvider(weChatAuthProvider);
            
            log.info("Registered WeChat auth provider - AppId: {}, AccountType: {}, MiniProgram: {}, Valid: {}", 
                    weChatConfig.getAppId(), 
                    weChatConfig.getAccountType(),
                    weChatConfig.isMiniProgramMode(),
                    weChatConfig.isValid());
        } else {
            log.warn("AuthService is not DefaultAuthService, cannot register WeChat auth provider");
        }
    }

    /**
     * 配置信息日志输出
     */
    @PostConstruct
    public void logConfiguration() {
        log.info("WeChat configuration: enabled=true, valid={}, appId={}, accountType={}, miniProgramMode={}, sandboxMode={}, encryptMessage={}",
                weChatConfig.isValid(),
                weChatConfig.getAppId(),
                weChatConfig.getAccountType(),
                weChatConfig.isMiniProgramMode(),
                weChatConfig.isSandboxMode(),
                weChatConfig.isEncryptMessage());
    }
}
