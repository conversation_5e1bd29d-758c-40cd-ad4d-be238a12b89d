package cn.com.handthing.starter.id.registry;

import cn.com.handthing.starter.id.exception.IdGenerationException;
import cn.com.handthing.starter.id.generator.IdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * ID生成器注册中心
 * <p>
 * 负责管理所有的ID生成器实例，提供统一的访问接口。
 * 在应用启动时自动收集Spring容器中的所有IdGenerator Bean，
 * 并维护策略名称和字段类型的映射关系。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Component
public class IdGeneratorRegistry implements ApplicationContextAware, InitializingBean {
    
    /**
     * Spring应用上下文
     */
    private ApplicationContext applicationContext;
    
    /**
     * 按策略名称索引的生成器映射
     * Key: Bean名称（策略名）
     * Value: ID生成器实例
     */
    private final Map<String, IdGenerator<?>> strategyMap = new ConcurrentHashMap<>();
    
    /**
     * 按字段类型索引的默认生成器映射
     * Key: ID类型（String.class, Long.class等）
     * Value: 默认ID生成器实例
     */
    private final Map<Class<?>, IdGenerator<?>> defaultTypeMap = new ConcurrentHashMap<>();
    
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }
    
    @Override
    public void afterPropertiesSet() throws Exception {
        initializeGenerators();
        logRegisteredGenerators();
    }
    
    /**
     * 初始化所有ID生成器
     */
    private void initializeGenerators() {
        log.info("Initializing ID generators...");
        
        // 获取所有IdGenerator类型的Bean
        Map<String, IdGenerator> generators = applicationContext.getBeansOfType(IdGenerator.class);
        
        for (Map.Entry<String, IdGenerator> entry : generators.entrySet()) {
            String beanName = entry.getKey();
            IdGenerator<?> generator = entry.getValue();
            
            // 注册到策略映射
            strategyMap.put(beanName, generator);
            
            // 如果是默认生成器，注册到类型映射
            registerDefaultGenerator(generator);
            
            // 预热生成器
            try {
                generator.warmUp();
                log.debug("Warmed up generator: {}", beanName);
            } catch (Exception e) {
                log.warn("Failed to warm up generator: {}, error: {}", beanName, e.getMessage());
            }
        }
        
        log.info("Initialized {} ID generators", generators.size());
    }
    
    /**
     * 注册默认生成器
     */
    private void registerDefaultGenerator(IdGenerator<?> generator) {
        // 检查生成器是否标记为默认生成器
        String generatorName = generator.getClass().getSimpleName();
        
        // UUID生成器作为String类型的默认生成器
        if (generatorName.contains("Uuid") || generatorName.contains("UUID")) {
            defaultTypeMap.put(String.class, generator);
            log.debug("Registered UUID generator as default for String type");
        }
        
        // Snowflake生成器作为Long类型的默认生成器
        if (generatorName.contains("Snowflake")) {
            defaultTypeMap.put(Long.class, generator);
            log.debug("Registered Snowflake generator as default for Long type");
        }
    }
    
    /**
     * 根据策略名称获取ID生成器
     *
     * @param strategyName 策略名称（Bean名称）
     * @return ID生成器实例
     * @throws IdGenerationException 当找不到对应的生成器时抛出
     */
    public IdGenerator<?> getGenerator(String strategyName) {
        if (strategyName == null || strategyName.trim().isEmpty()) {
            throw new IdGenerationException("Strategy name cannot be null or empty");
        }
        
        IdGenerator<?> generator = strategyMap.get(strategyName);
        if (generator == null) {
            throw new IdGenerationException(strategyName, "ID_GENERATOR_NOT_FOUND", 
                    "No ID generator found for strategy: " + strategyName);
        }
        
        // 检查生成器是否可用
        if (!generator.isAvailable()) {
            throw new IdGenerationException(strategyName, "ID_GENERATOR_UNAVAILABLE", 
                    "ID generator is not available: " + strategyName);
        }
        
        return generator;
    }
    
    /**
     * 根据字段类型获取默认ID生成器
     *
     * @param fieldType 字段类型
     * @return 默认ID生成器实例
     * @throws IdGenerationException 当找不到对应类型的默认生成器时抛出
     */
    public IdGenerator<?> getGenerator(Class<?> fieldType) {
        if (fieldType == null) {
            throw new IdGenerationException("Field type cannot be null");
        }
        
        IdGenerator<?> generator = defaultTypeMap.get(fieldType);
        if (generator == null) {
            throw new IdGenerationException("DEFAULT_GENERATOR_NOT_FOUND", 
                    "No default ID generator found for type: " + fieldType.getName());
        }
        
        // 检查生成器是否可用
        if (!generator.isAvailable()) {
            throw new IdGenerationException(generator.getName(), "ID_GENERATOR_UNAVAILABLE", 
                    "Default ID generator is not available for type: " + fieldType.getName());
        }
        
        return generator;
    }
    
    /**
     * 手动注册ID生成器
     *
     * @param strategyName 策略名称
     * @param generator    ID生成器实例
     */
    public void registerGenerator(String strategyName, IdGenerator<?> generator) {
        if (strategyName == null || strategyName.trim().isEmpty()) {
            throw new IllegalArgumentException("Strategy name cannot be null or empty");
        }
        if (generator == null) {
            throw new IllegalArgumentException("Generator cannot be null");
        }
        
        strategyMap.put(strategyName, generator);
        log.info("Manually registered ID generator: {} -> {}", strategyName, generator.getClass().getName());
    }
    
    /**
     * 手动注册默认类型生成器
     *
     * @param fieldType 字段类型
     * @param generator ID生成器实例
     */
    public void registerDefaultGenerator(Class<?> fieldType, IdGenerator<?> generator) {
        if (fieldType == null) {
            throw new IllegalArgumentException("Field type cannot be null");
        }
        if (generator == null) {
            throw new IllegalArgumentException("Generator cannot be null");
        }
        
        defaultTypeMap.put(fieldType, generator);
        log.info("Manually registered default ID generator: {} -> {}", 
                fieldType.getName(), generator.getClass().getName());
    }
    
    /**
     * 获取所有已注册的策略名称
     *
     * @return 策略名称集合
     */
    public java.util.Set<String> getRegisteredStrategies() {
        return java.util.Collections.unmodifiableSet(strategyMap.keySet());
    }
    
    /**
     * 获取所有支持的默认类型
     *
     * @return 支持的类型集合
     */
    public java.util.Set<Class<?>> getSupportedTypes() {
        return java.util.Collections.unmodifiableSet(defaultTypeMap.keySet());
    }
    
    /**
     * 检查是否存在指定策略的生成器
     *
     * @param strategyName 策略名称
     * @return 如果存在返回true，否则返回false
     */
    public boolean hasGenerator(String strategyName) {
        return strategyName != null && strategyMap.containsKey(strategyName);
    }
    
    /**
     * 检查是否存在指定类型的默认生成器
     *
     * @param fieldType 字段类型
     * @return 如果存在返回true，否则返回false
     */
    public boolean hasDefaultGenerator(Class<?> fieldType) {
        return fieldType != null && defaultTypeMap.containsKey(fieldType);
    }
    
    /**
     * 记录已注册的生成器信息
     */
    private void logRegisteredGenerators() {
        log.info("=== ID Generator Registry Summary ===");
        log.info("Strategy generators: {}", strategyMap.size());
        strategyMap.forEach((name, generator) -> 
                log.info("  {} -> {}", name, generator.getClass().getName()));
        
        log.info("Default type generators: {}", defaultTypeMap.size());
        defaultTypeMap.forEach((type, generator) -> 
                log.info("  {} -> {}", type.getSimpleName(), generator.getClass().getName()));
        log.info("=====================================");
    }
    
    /**
     * 关闭所有生成器
     */
    public void shutdown() {
        log.info("Shutting down all ID generators...");
        
        strategyMap.values().forEach(generator -> {
            try {
                generator.shutdown();
            } catch (Exception e) {
                log.warn("Failed to shutdown generator: {}, error: {}", 
                        generator.getClass().getName(), e.getMessage());
            }
        });
        
        strategyMap.clear();
        defaultTypeMap.clear();
        
        log.info("All ID generators have been shut down");
    }
}
