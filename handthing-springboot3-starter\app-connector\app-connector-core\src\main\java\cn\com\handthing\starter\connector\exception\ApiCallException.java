package cn.com.handthing.starter.connector.exception;

import cn.com.handthing.starter.connector.PlatformType;

/**
 * API调用异常类
 * <p>
 * 处理API调用失败、限流、服务不可用等错误。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
public class ApiCallException extends ConnectorException {

    private static final long serialVersionUID = 1L;

    /**
     * 常用错误码
     */
    public static final String RATE_LIMIT_EXCEEDED = "RATE_LIMIT_EXCEEDED";
    public static final String SERVICE_UNAVAILABLE = "SERVICE_UNAVAILABLE";
    public static final String INVALID_REQUEST = "INVALID_REQUEST";
    public static final String UNAUTHORIZED = "UNAUTHORIZED";
    public static final String FORBIDDEN = "FORBIDDEN";
    public static final String NOT_FOUND = "NOT_FOUND";
    public static final String INTERNAL_ERROR = "INTERNAL_ERROR";
    public static final String TIMEOUT = "TIMEOUT";
    public static final String QUOTA_EXCEEDED = "QUOTA_EXCEEDED";
    public static final String INVALID_PARAMETER = "INVALID_PARAMETER";

    /**
     * HTTP状态码
     */
    private final Integer httpStatus;

    /**
     * 构造函数
     *
     * @param message 错误消息
     */
    public ApiCallException(String message) {
        super(message);
        this.httpStatus = null;
    }

    /**
     * 构造函数
     *
     * @param message 错误消息
     * @param cause   原因异常
     */
    public ApiCallException(String message, Throwable cause) {
        super(message, cause);
        this.httpStatus = null;
    }

    /**
     * 构造函数
     *
     * @param message   错误消息
     * @param errorCode 错误码
     */
    public ApiCallException(String message, String errorCode) {
        super(message, errorCode);
        this.httpStatus = null;
    }

    /**
     * 构造函数
     *
     * @param message    错误消息
     * @param errorCode  错误码
     * @param httpStatus HTTP状态码
     */
    public ApiCallException(String message, String errorCode, Integer httpStatus) {
        super(message, errorCode);
        this.httpStatus = httpStatus;
    }

    /**
     * 构造函数
     *
     * @param message   错误消息
     * @param errorCode 错误码
     * @param platform  平台类型
     */
    public ApiCallException(String message, String errorCode, PlatformType platform) {
        super(message, errorCode, platform);
        this.httpStatus = null;
    }

    /**
     * 构造函数
     *
     * @param message    错误消息
     * @param errorCode  错误码
     * @param platform   平台类型
     * @param httpStatus HTTP状态码
     */
    public ApiCallException(String message, String errorCode, PlatformType platform, Integer httpStatus) {
        super(message, errorCode, platform);
        this.httpStatus = httpStatus;
    }

    /**
     * 完整构造函数
     *
     * @param message    错误消息
     * @param errorCode  错误码
     * @param platform   平台类型
     * @param httpStatus HTTP状态码
     * @param errorData  额外错误信息
     * @param cause      原因异常
     */
    public ApiCallException(String message, String errorCode, PlatformType platform, Integer httpStatus, Object errorData, Throwable cause) {
        super(message, errorCode, platform, errorData, cause);
        this.httpStatus = httpStatus;
    }

    /**
     * 获取HTTP状态码
     *
     * @return HTTP状态码
     */
    public Integer getHttpStatus() {
        return httpStatus;
    }

    // 静态工厂方法，用于创建常见的API调用异常

    /**
     * 创建限流异常
     *
     * @param platform 平台类型
     * @return API调用异常
     */
    public static ApiCallException rateLimitExceeded(PlatformType platform) {
        return new ApiCallException("Rate limit exceeded", RATE_LIMIT_EXCEEDED, platform, 429);
    }

    /**
     * 创建服务不可用异常
     *
     * @param platform 平台类型
     * @return API调用异常
     */
    public static ApiCallException serviceUnavailable(PlatformType platform) {
        return new ApiCallException("Service temporarily unavailable", SERVICE_UNAVAILABLE, platform, 503);
    }

    /**
     * 创建无效请求异常
     *
     * @param platform 平台类型
     * @param message  错误消息
     * @return API调用异常
     */
    public static ApiCallException invalidRequest(PlatformType platform, String message) {
        return new ApiCallException("Invalid request: " + message, INVALID_REQUEST, platform, 400);
    }

    /**
     * 创建未授权异常
     *
     * @param platform 平台类型
     * @return API调用异常
     */
    public static ApiCallException unauthorized(PlatformType platform) {
        return new ApiCallException("Unauthorized access", UNAUTHORIZED, platform, 401);
    }

    /**
     * 创建禁止访问异常
     *
     * @param platform 平台类型
     * @return API调用异常
     */
    public static ApiCallException forbidden(PlatformType platform) {
        return new ApiCallException("Access forbidden", FORBIDDEN, platform, 403);
    }

    /**
     * 创建资源未找到异常
     *
     * @param platform 平台类型
     * @param resource 资源名称
     * @return API调用异常
     */
    public static ApiCallException notFound(PlatformType platform, String resource) {
        return new ApiCallException("Resource not found: " + resource, NOT_FOUND, platform, 404);
    }

    /**
     * 创建内部错误异常
     *
     * @param platform 平台类型
     * @param cause    原因异常
     * @return API调用异常
     */
    public static ApiCallException internalError(PlatformType platform, Throwable cause) {
        return new ApiCallException("Internal server error", INTERNAL_ERROR, platform, 500, null, cause);
    }

    /**
     * 创建超时异常
     *
     * @param platform 平台类型
     * @return API调用异常
     */
    public static ApiCallException timeout(PlatformType platform) {
        return new ApiCallException("Request timeout", TIMEOUT, platform);
    }

    /**
     * 创建配额超限异常
     *
     * @param platform 平台类型
     * @return API调用异常
     */
    public static ApiCallException quotaExceeded(PlatformType platform) {
        return new ApiCallException("API quota exceeded", QUOTA_EXCEEDED, platform, 429);
    }

    /**
     * 创建无效参数异常
     *
     * @param platform  平台类型
     * @param parameter 参数名称
     * @return API调用异常
     */
    public static ApiCallException invalidParameter(PlatformType platform, String parameter) {
        return new ApiCallException("Invalid parameter: " + parameter, INVALID_PARAMETER, platform, 400);
    }

    @Override
    public String getFormattedMessage() {
        StringBuilder sb = new StringBuilder(super.getFormattedMessage());
        if (httpStatus != null) {
            sb.append(" (HTTP ").append(httpStatus).append(")");
        }
        return sb.toString();
    }
}
