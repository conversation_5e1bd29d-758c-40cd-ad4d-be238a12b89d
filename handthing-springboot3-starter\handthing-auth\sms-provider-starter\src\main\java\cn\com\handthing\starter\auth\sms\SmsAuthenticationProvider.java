package cn.com.handthing.starter.auth.sms;

import cn.com.handthing.starter.auth.core.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * 短信验证码认证提供者
 * <p>
 * 实现基于短信验证码的认证逻辑，支持登录和自动注册功能。
 * 提供完整的短信验证码认证流程，包括验证码校验、用户创建等。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SmsAuthenticationProvider implements AuthenticationProvider {

    private final SmsUserService smsUserService;
    private final SmsService smsService;
    private final JwtTokenProvider jwtTokenProvider;

    @Override
    public GrantType getSupportedGrantType() {
        return GrantType.SMS_CODE;
    }

    @Override
    public boolean supports(AuthenticationRequest request) {
        return request instanceof SmsAuthenticationRequest && 
               request.getGrantType() == GrantType.SMS_CODE;
    }

    @Override
    public AuthenticationResponse authenticate(AuthenticationRequest request) throws AuthenticationException {
        log.debug("Starting SMS authentication for request: {}", request);

        if (!(request instanceof SmsAuthenticationRequest)) {
            throw new AuthenticationException("INVALID_REQUEST", "Invalid authentication request type");
        }

        SmsAuthenticationRequest smsRequest = (SmsAuthenticationRequest) request;

        try {
            // 预处理
            preAuthenticate(smsRequest);

            // 验证短信验证码
            validateSmsCode(smsRequest);

            // 查找或创建用户
            UserInfo userInfo = findOrCreateUser(smsRequest);

            // 验证用户状态
            validateUserStatus(userInfo);

            // 生成令牌
            String accessToken = generateAccessToken(userInfo, smsRequest);
            String refreshToken = generateRefreshToken(userInfo, smsRequest);
            Long expiresIn = 7200L; // 2小时

            // 更新登录信息
            updateLoginInfo(userInfo, smsRequest);

            // 清除登录失败记录
            smsUserService.clearLoginFailures(smsRequest.getPhone());

            // 删除已使用的验证码
            smsService.deleteSmsCode(smsRequest.getPhone(), smsRequest.getCodeType());

            // 创建成功响应
            SmsAuthenticationResponse response;
            if (smsRequest.getAutoRegister() && smsUserService.isFirstLogin(userInfo)) {
                response = SmsAuthenticationResponse.successForNewUser(accessToken, refreshToken, expiresIn, userInfo);
            } else {
                response = SmsAuthenticationResponse.success(accessToken, refreshToken, expiresIn, userInfo);
            }

            // 设置额外信息
            setAdditionalInfo(response, userInfo, smsRequest);

            log.info("SMS authentication successful for phone: {}", smsRequest.getPhone());
            return response;

        } catch (AuthenticationException e) {
            // 记录登录失败
            recordLoginFailure(smsRequest, e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("SMS authentication error", e);
            recordLoginFailure(smsRequest, "System error");
            throw new AuthenticationException("AUTHENTICATION_ERROR", "Authentication failed", e);
        }
    }

    @Override
    public boolean validateToken(String token) {
        return jwtTokenProvider.validateToken(token);
    }

    @Override
    public AuthenticationResponse refreshToken(String refreshToken) throws AuthenticationException {
        try {
            JwtClaims claims = jwtTokenProvider.parseToken(refreshToken);
            
            // 验证是否为刷新令牌
            if (!"refresh_token".equals(claims.getTokenType())) {
                throw new AuthenticationException("INVALID_REFRESH_TOKEN", "Invalid refresh token type");
            }

            // 查找用户
            Optional<UserInfo> userOptional = smsUserService.findById(claims.getSubject());
            if (userOptional.isEmpty()) {
                throw new AuthenticationException("USER_NOT_FOUND", "User not found");
            }

            UserInfo userInfo = userOptional.get();

            // 验证用户状态
            validateUserStatus(userInfo);

            // 生成新的访问令牌
            String newAccessToken = generateAccessToken(userInfo, null);
            Long expiresIn = 7200L;

            return SmsAuthenticationResponse.success(newAccessToken, refreshToken, expiresIn, userInfo);

        } catch (AuthenticationException e) {
            throw e;
        } catch (Exception e) {
            log.error("Refresh token error", e);
            throw new AuthenticationException("REFRESH_TOKEN_ERROR", "Failed to refresh token", e);
        }
    }

    @Override
    public String getProviderName() {
        return "SMS Authentication Provider";
    }

    @Override
    public boolean isAvailable() {
        return true;
    }

    @Override
    public int getPriority() {
        return 1;
    }

    /**
     * 预处理认证请求
     *
     * @param request 短信认证请求
     * @throws AuthenticationException 认证异常
     */
    private void preAuthenticate(SmsAuthenticationRequest request) throws AuthenticationException {
        // 验证请求参数
        if (!request.isValid()) {
            throw new AuthenticationException("INVALID_REQUEST", "Invalid authentication request");
        }

        // 验证手机号格式
        if (!smsUserService.isValidPhoneFormat(request.getPhone())) {
            throw new AuthenticationException("INVALID_PHONE_FORMAT", "Invalid phone number format");
        }
    }

    /**
     * 验证短信验证码
     *
     * @param request 短信认证请求
     * @throws AuthenticationException 认证异常
     */
    private void validateSmsCode(SmsAuthenticationRequest request) throws AuthenticationException {
        if (!smsService.verifySmsCode(request.getPhone(), request.getSmsCode(), request.getCodeType())) {
            throw InvalidCredentialsException.invalidCode();
        }
    }

    /**
     * 查找或创建用户
     *
     * @param request 短信认证请求
     * @return 用户信息
     * @throws AuthenticationException 认证异常
     */
    private UserInfo findOrCreateUser(SmsAuthenticationRequest request) throws AuthenticationException {
        Optional<UserInfo> userOptional = smsUserService.findByPhone(request.getPhone());

        if (userOptional.isPresent()) {
            return userOptional.get();
        }

        // 用户不存在
        if (!request.getAutoRegister()) {
            throw AuthenticationFailedException.userNotFound(request.getPhone());
        }

        // 自动注册新用户
        try {
            String nickname = request.getNickname();
            if (nickname == null || nickname.trim().isEmpty()) {
                nickname = smsUserService.generateDefaultNickname(request.getPhone());
            }

            UserInfo newUser = smsUserService.createUser(request.getPhone(), nickname);
            log.info("Auto-registered new user for phone: {}", request.getPhone());
            return newUser;

        } catch (Exception e) {
            log.error("Failed to create user for phone: {}", request.getPhone(), e);
            throw new AuthenticationException("REGISTRATION_FAILED", "Failed to create user", e);
        }
    }

    /**
     * 验证用户状态
     *
     * @param userInfo 用户信息
     * @throws AuthenticationException 认证异常
     */
    private void validateUserStatus(UserInfo userInfo) throws AuthenticationException {
        // 检查用户是否有效
        if (!smsUserService.isUserValid(userInfo)) {
            throw AuthenticationFailedException.userDisabled(userInfo.getPhone());
        }

        // 检查用户是否被锁定
        if (smsUserService.isUserLocked(userInfo)) {
            throw AuthenticationFailedException.userLocked(userInfo.getPhone());
        }

        // 检查用户是否被禁用
        if (smsUserService.isUserDisabled(userInfo)) {
            throw AuthenticationFailedException.userDisabled(userInfo.getPhone());
        }

        // 检查账户是否过期
        if (smsUserService.isAccountExpired(userInfo)) {
            throw new AuthenticationException("ACCOUNT_EXPIRED", "Account has expired");
        }
    }

    /**
     * 生成访问令牌
     *
     * @param userInfo 用户信息
     * @param request  认证请求
     * @return 访问令牌
     */
    private String generateAccessToken(UserInfo userInfo, SmsAuthenticationRequest request) {
        JwtClaims claims = JwtClaims.builder()
                .subject(userInfo.getUserId())
                .username(userInfo.getUsername())
                .nickname(userInfo.getNickname())
                .email(userInfo.getEmail())
                .phone(userInfo.getPhone())
                .roles(userInfo.getRoles())
                .permissions(userInfo.getPermissions())
                .grantType(GrantType.SMS_CODE.getCode())
                .tokenType("access_token")
                .build();

        if (request != null) {
            claims.setClientId(request.getClientId());
            claims.setScope(request.getScope());
            claims.setIpAddress(request.getIpAddress());
            claims.setUserAgent(request.getUserAgent());
            claims.setDeviceId(request.getDeviceId());
        }

        return jwtTokenProvider.generateAccessToken(claims);
    }

    /**
     * 生成刷新令牌
     *
     * @param userInfo 用户信息
     * @param request  认证请求
     * @return 刷新令牌
     */
    private String generateRefreshToken(UserInfo userInfo, SmsAuthenticationRequest request) {
        JwtClaims claims = JwtClaims.builder()
                .subject(userInfo.getUserId())
                .username(userInfo.getUsername())
                .phone(userInfo.getPhone())
                .grantType(GrantType.SMS_CODE.getCode())
                .tokenType("refresh_token")
                .build();

        if (request != null) {
            claims.setClientId(request.getClientId());
        }

        return jwtTokenProvider.generateRefreshToken(claims);
    }

    /**
     * 更新登录信息
     *
     * @param userInfo 用户信息
     * @param request  认证请求
     */
    private void updateLoginInfo(UserInfo userInfo, SmsAuthenticationRequest request) {
        smsUserService.updateLastLoginInfo(
                userInfo.getUserId(),
                LocalDateTime.now(),
                request.getIpAddress(),
                request.getUserAgent()
        );
    }

    /**
     * 设置额外信息
     *
     * @param response 认证响应
     * @param userInfo 用户信息
     * @param request  认证请求
     */
    private void setAdditionalInfo(SmsAuthenticationResponse response, UserInfo userInfo, 
                                  SmsAuthenticationRequest request) {
        response.setFirstLogin(smsUserService.isFirstLogin(userInfo));
        response.setLastLoginTime(userInfo.getLastLoginTime());
        response.setLastLoginIp(userInfo.getLastLoginIp());
    }

    /**
     * 记录登录失败
     *
     * @param request 认证请求
     * @param reason  失败原因
     */
    private void recordLoginFailure(SmsAuthenticationRequest request, String reason) {
        try {
            smsUserService.recordLoginFailure(request.getPhone(), request.getIpAddress(), reason);
        } catch (Exception e) {
            log.error("Failed to record login failure", e);
        }
    }
}
