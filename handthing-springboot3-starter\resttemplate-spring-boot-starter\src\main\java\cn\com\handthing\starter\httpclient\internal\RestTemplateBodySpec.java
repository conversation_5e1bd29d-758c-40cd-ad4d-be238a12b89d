package cn.com.handthing.starter.httpclient.internal;

import cn.com.handthing.starter.httpclient.BodySpec;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.http.ResponseEntity;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import reactor.core.publisher.Mono;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * BodySpec 接口的 RestTemplate 实现。
 * 
 * @param <T> 期望的响应体类型
 * <AUTHOR>
 * @since V1.0.0
 */
public final class RestTemplateBodySpec<T> implements BodySpec<T> {

    private final ResponseEntity<byte[]> responseEntity;
    private final Class<T> targetType;
    private final ObjectMapper objectMapper = new ObjectMapper();

    public RestTemplateBodySpec(ResponseEntity<byte[]> responseEntity, Class<T> targetType) {
        this.responseEntity = responseEntity;
        this.targetType = targetType;
    }

    @Override
    @Nullable
    public T block() {
        byte[] bodyBytes = responseEntity.getBody();
        if (bodyBytes == null || bodyBytes.length == 0) {
            return null;
        }

        // 如果目标类型是 String，直接返回字符串
        if (targetType == String.class) {
            return targetType.cast(new String(bodyBytes, StandardCharsets.UTF_8));
        }

        // 如果目标类型是 byte[]，直接返回字节数组
        if (targetType == byte[].class) {
            return targetType.cast(bodyBytes);
        }

        // 其他类型使用 Jackson 进行反序列化
        try {
            return objectMapper.readValue(bodyBytes, targetType);
        } catch (IOException e) {
            throw new RuntimeException("Failed to deserialize response body", e);
        }
    }

    @Override
    @NonNull
    public Mono<T> mono() {
        try {
            T result = block();
            return result != null ? Mono.just(result) : Mono.empty();
        } catch (Exception e) {
            return Mono.error(e);
        }
    }
}
