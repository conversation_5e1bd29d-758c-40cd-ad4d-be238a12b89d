package cn.com.handthing.starter.cache.impl;

import cn.com.handthing.starter.cache.CacheService;
import cn.com.handthing.starter.cache.CacheStats;
import cn.com.handthing.starter.cache.exception.CacheOperationException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;

import java.time.Duration;

/**
 * 默认缓存服务实现
 * <p>
 * 基于Spring Cache抽象的默认实现，内部委托给CacheManager处理具体的缓存操作
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@RequiredArgsConstructor
public class DefaultCacheService implements CacheService {

    private final CacheManager cacheManager;

    @Override
    public <T> T get(String cacheName, String key) {
        try {
            Cache cache = getCache(cacheName);
            Cache.ValueWrapper wrapper = cache.get(key);
            return wrapper != null ? (T) wrapper.get() : null;
        } catch (Exception e) {
            log.error("Failed to get cache value for cacheName: {}, key: {}", cacheName, key, e);
            throw new CacheOperationException("Failed to get cache value", e);
        }
    }

    @Override
    public <T> T get(String cacheName, String key, Class<T> type) {
        try {
            Cache cache = getCache(cacheName);
            return cache.get(key, type);
        } catch (Exception e) {
            log.error("Failed to get cache value for cacheName: {}, key: {}, type: {}", 
                    cacheName, key, type.getName(), e);
            throw new CacheOperationException("Failed to get cache value", e);
        }
    }

    @Override
    public void put(String cacheName, String key, Object value) {
        try {
            Cache cache = getCache(cacheName);
            cache.put(key, value);
            log.debug("Put cache value for cacheName: {}, key: {}", cacheName, key);
        } catch (Exception e) {
            log.error("Failed to put cache value for cacheName: {}, key: {}", cacheName, key, e);
            throw new CacheOperationException("Failed to put cache value", e);
        }
    }

    @Override
    public void put(String cacheName, String key, Object value, Duration ttl) {
        // 注意：标准的Spring Cache接口不直接支持TTL
        // 这里先使用普通的put方法，具体的TTL支持需要在具体实现中处理
        log.warn("TTL support depends on specific cache implementation. Using standard put method.");
        put(cacheName, key, value);
    }

    @Override
    public void evict(String cacheName, String key) {
        try {
            Cache cache = getCache(cacheName);
            cache.evict(key);
            log.debug("Evicted cache value for cacheName: {}, key: {}", cacheName, key);
        } catch (Exception e) {
            log.error("Failed to evict cache value for cacheName: {}, key: {}", cacheName, key, e);
            throw new CacheOperationException("Failed to evict cache value", e);
        }
    }

    @Override
    public void clear(String cacheName) {
        try {
            Cache cache = getCache(cacheName);
            cache.clear();
            log.debug("Cleared cache for cacheName: {}", cacheName);
        } catch (Exception e) {
            log.error("Failed to clear cache for cacheName: {}", cacheName, e);
            throw new CacheOperationException("Failed to clear cache", e);
        }
    }

    @Override
    public boolean exists(String cacheName, String key) {
        try {
            Cache cache = getCache(cacheName);
            return cache.get(key) != null;
        } catch (Exception e) {
            log.error("Failed to check cache existence for cacheName: {}, key: {}", cacheName, key, e);
            return false;
        }
    }

    @Override
    public CacheStats getStats(String cacheName) {
        // 基础实现不提供统计信息，具体实现可以重写此方法
        log.debug("Stats not supported in default implementation for cacheName: {}", cacheName);
        return null;
    }

    /**
     * 获取缓存实例
     *
     * @param cacheName 缓存名称
     * @return 缓存实例
     * @throws CacheOperationException 如果缓存不存在
     */
    private Cache getCache(String cacheName) {
        Cache cache = cacheManager.getCache(cacheName);
        if (cache == null) {
            throw new CacheOperationException("Cache not found: " + cacheName);
        }
        return cache;
    }
}