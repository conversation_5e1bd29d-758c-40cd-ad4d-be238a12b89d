package cn.com.handthing.starter.httpclient.config;

import jakarta.validation.constraints.Min;
import java.time.Duration;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

/**
 * HTTP请求重试策略配置属性。
 */
public class RetryProperties {

    /**
     * 是否启用重试功能。
     */
    private boolean enabled = false;

    /**
     * 最大重试次数（不包括首次请求）。
     * 例如，设置为3，则最多会发起 1 (首次) + 3 (重试) = 4 次请求。
     */
    @Min(value = 1, message = "max-attempts must be at least 1")
    private int maxAttempts = 3;

    /**
     * 重试之间的退避策略。
     */
    private BackoffStrategy strategy = BackoffStrategy.EXPONENTIAL;

    /**
     * 基础延迟时间。
     * 对于 FIXED 策略，这是每次重试的固定等待时间。
     * 对于 EXPONENTIAL 策略，这是首次重试的等待时间，后续会指数级增长。
     */
    private Duration delay = Duration.ofMillis(100);

    /**
     * 最大延迟时间。
     * 仅在 EXPONENTIAL 策略下生效，用于限制指数增长的上限。
     */
    private Duration maxDelay = Duration.ofSeconds(2);

    /**
     * 一个HTTP状态码集合。当响应的状态码在此集合中时，将触发重试。
     * 通常用于5xx系列的服务端错误。例如: [500, 502, 503, 504]。
     */
    private Set<Integer> retryOnStatus = new HashSet<>();

    /**
     * 退避策略枚举。
     */
    public enum BackoffStrategy {
        /**
         * 固定延时。每次重试都等待相同的时长 (由 'delay' 属性定义)。
         */
        FIXED,
        /**
         * 指数退避。每次重试的等待时间都会加倍，直到达到 'maxDelay' 上限。
         */
        EXPONENTIAL
    }

    // --- Standard Getters and Setters, equals, hashCode, toString ---

    public RetryProperties() {
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public int getMaxAttempts() {
        return maxAttempts;
    }

    public void setMaxAttempts(int maxAttempts) {
        this.maxAttempts = maxAttempts;
    }

    public BackoffStrategy getStrategy() {
        return strategy;
    }

    public void setStrategy(BackoffStrategy strategy) {
        this.strategy = strategy;
    }

    public Duration getDelay() {
        return delay;
    }

    public void setDelay(Duration delay) {
        this.delay = delay;
    }

    public Duration getMaxDelay() {
        return maxDelay;
    }

    public void setMaxDelay(Duration maxDelay) {
        this.maxDelay = maxDelay;
    }

    public Set<Integer> getRetryOnStatus() {
        return retryOnStatus;
    }

    public void setRetryOnStatus(Set<Integer> retryOnStatus) {
        this.retryOnStatus = retryOnStatus;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        RetryProperties that = (RetryProperties) o;
        return enabled == that.enabled && maxAttempts == that.maxAttempts && strategy == that.strategy && Objects.equals(delay, that.delay) && Objects.equals(maxDelay, that.maxDelay) && Objects.equals(retryOnStatus, that.retryOnStatus);
    }

    @Override
    public int hashCode() {
        return Objects.hash(enabled, maxAttempts, strategy, delay, maxDelay, retryOnStatus);
    }

    @Override
    public String toString() {
        return "RetryProperties{" +
                "enabled=" + enabled +
                ", maxAttempts=" + maxAttempts +
                ", strategy=" + strategy +
                ", delay=" + delay +
                ", maxDelay=" + maxDelay +
                ", retryOnStatus=" + retryOnStatus +
                '}';
    }
}