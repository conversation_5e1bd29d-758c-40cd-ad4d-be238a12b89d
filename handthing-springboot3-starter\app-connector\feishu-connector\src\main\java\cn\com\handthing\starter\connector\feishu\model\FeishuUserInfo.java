package cn.com.handthing.starter.connector.feishu.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 飞书用户信息
 * <p>
 * 飞书OAuth2.0认证获取用户信息的响应数据模型。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
public class FeishuUserInfo {

    /**
     * 错误码，0表示成功
     */
    @JsonProperty("code")
    private Integer code;

    /**
     * 错误信息
     */
    @JsonProperty("msg")
    private String msg;

    /**
     * 响应数据
     */
    @JsonProperty("data")
    private UserData data;

    /**
     * 检查响应是否成功
     *
     * @return 如果成功返回true，否则返回false
     */
    public boolean isSuccess() {
        return code != null && code == 0;
    }

    /**
     * 获取错误描述
     *
     * @return 错误描述
     */
    public String getErrorDescription() {
        if (isSuccess()) {
            return null;
        }
        return String.format("Error %d: %s", code, msg);
    }

    /**
     * 用户数据
     */
    @Data
    public static class UserData {

        /**
         * 用户在应用内的唯一标识
         */
        @JsonProperty("open_id")
        private String openId;

        /**
         * 用户统一标识
         */
        @JsonProperty("union_id")
        private String unionId;

        /**
         * 用户姓名
         */
        @JsonProperty("name")
        private String name;

        /**
         * 用户英文名称
         */
        @JsonProperty("en_name")
        private String enName;

        /**
         * 用户头像URL
         */
        @JsonProperty("avatar_url")
        private String avatarUrl;

        /**
         * 用户头像缩略图URL
         */
        @JsonProperty("avatar_thumb")
        private String avatarThumb;

        /**
         * 用户头像中等尺寸URL
         */
        @JsonProperty("avatar_middle")
        private String avatarMiddle;

        /**
         * 用户头像大尺寸URL
         */
        @JsonProperty("avatar_big")
        private String avatarBig;

        /**
         * 用户邮箱
         */
        @JsonProperty("email")
        private String email;

        /**
         * 用户企业邮箱
         */
        @JsonProperty("enterprise_email")
        private String enterpriseEmail;

        /**
         * 用户手机号
         */
        @JsonProperty("mobile")
        private String mobile;

        /**
         * 用户所在企业标识
         */
        @JsonProperty("tenant_key")
        private String tenantKey;

        /**
         * 用户工号
         */
        @JsonProperty("employee_no")
        private String employeeNo;

        /**
         * 用户ID
         */
        @JsonProperty("user_id")
        private String userId;

        /**
         * 获取显示名称
         *
         * @return 显示名称
         */
        public String getDisplayName() {
            if (name != null && !name.trim().isEmpty()) {
                return name;
            }
            if (enName != null && !enName.trim().isEmpty()) {
                return enName;
            }
            return openId;
        }

        /**
         * 获取最佳头像URL
         *
         * @return 头像URL
         */
        public String getBestAvatarUrl() {
            if (avatarUrl != null && !avatarUrl.trim().isEmpty()) {
                return avatarUrl;
            }
            if (avatarBig != null && !avatarBig.trim().isEmpty()) {
                return avatarBig;
            }
            if (avatarMiddle != null && !avatarMiddle.trim().isEmpty()) {
                return avatarMiddle;
            }
            return avatarThumb;
        }

        /**
         * 获取最佳邮箱
         *
         * @return 邮箱
         */
        public String getBestEmail() {
            if (email != null && !email.trim().isEmpty()) {
                return email;
            }
            return enterpriseEmail;
        }
    }
}
