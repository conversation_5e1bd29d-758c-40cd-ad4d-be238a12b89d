package cn.com.handthing.starter.connector.toutiao.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 今日头条用户信息
 * <p>
 * 今日头条OAuth2.0认证获取用户信息的响应数据模型。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
public class ToutiaoUserInfo {

    /**
     * 错误码，0表示成功
     */
    @JsonProperty("error_code")
    private Integer errorCode;

    /**
     * 错误描述
     */
    @JsonProperty("description")
    private String description;

    /**
     * 响应数据
     */
    @JsonProperty("data")
    private UserData data;

    /**
     * 检查响应是否成功
     *
     * @return 如果成功返回true，否则返回false
     */
    public boolean isSuccess() {
        return errorCode != null && errorCode == 0;
    }

    /**
     * 获取错误描述
     *
     * @return 错误描述
     */
    public String getErrorDescription() {
        if (isSuccess()) {
            return null;
        }
        return String.format("Error %d: %s", errorCode, description);
    }

    /**
     * 用户数据
     */
    @Data
    public static class UserData {

        /**
         * 用户唯一标识
         */
        @JsonProperty("open_id")
        private String openId;

        /**
         * 用户统一标识
         */
        @JsonProperty("union_id")
        private String unionId;

        /**
         * 用户昵称
         */
        @JsonProperty("nickname")
        private String nickname;

        /**
         * 用户头像URL
         */
        @JsonProperty("avatar")
        private String avatar;

        /**
         * 用户性别，1-男，2-女，0-未知
         */
        @JsonProperty("gender")
        private Integer gender;

        /**
         * 国家
         */
        @JsonProperty("country")
        private String country;

        /**
         * 省份
         */
        @JsonProperty("province")
        private String province;

        /**
         * 城市
         */
        @JsonProperty("city")
        private String city;

        /**
         * 用户类型
         * 1: 普通用户
         * 2: 认证用户
         * 3: 企业用户
         */
        @JsonProperty("user_type")
        private Integer userType;

        /**
         * 是否为认证用户
         */
        @JsonProperty("is_verified")
        private Boolean isVerified;

        /**
         * 粉丝数量
         */
        @JsonProperty("followers_count")
        private Long followersCount;

        /**
         * 关注数量
         */
        @JsonProperty("following_count")
        private Long followingCount;

        /**
         * 获取性别描述
         *
         * @return 性别描述
         */
        public String getGenderDescription() {
            if (gender == null) {
                return "未知";
            }
            switch (gender) {
                case 1:
                    return "男";
                case 2:
                    return "女";
                default:
                    return "未知";
            }
        }

        /**
         * 获取用户类型描述
         *
         * @return 用户类型描述
         */
        public String getUserTypeDescription() {
            if (userType == null) {
                return "未知";
            }
            switch (userType) {
                case 1:
                    return "普通用户";
                case 2:
                    return "认证用户";
                case 3:
                    return "企业用户";
                default:
                    return "未知";
            }
        }

        /**
         * 获取完整地址
         *
         * @return 完整地址
         */
        public String getFullAddress() {
            StringBuilder address = new StringBuilder();
            if (country != null && !country.trim().isEmpty()) {
                address.append(country);
            }
            if (province != null && !province.trim().isEmpty()) {
                if (address.length() > 0) {
                    address.append(" ");
                }
                address.append(province);
            }
            if (city != null && !city.trim().isEmpty()) {
                if (address.length() > 0) {
                    address.append(" ");
                }
                address.append(city);
            }
            return address.toString();
        }
    }
}
