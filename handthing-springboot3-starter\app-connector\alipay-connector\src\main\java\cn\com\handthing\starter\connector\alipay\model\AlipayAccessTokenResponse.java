package cn.com.handthing.starter.connector.alipay.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 支付宝访问令牌响应模型
 * <p>
 * 支付宝获取访问令牌API的响应数据结构
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class AlipayAccessTokenResponse {

    /**
     * 系统oauth token响应
     */
    @JsonProperty("alipay_system_oauth_token_response")
    private TokenResponse alipaySystemOauthTokenResponse;

    /**
     * 错误响应
     */
    @JsonProperty("error_response")
    private ErrorResponse errorResponse;

    /**
     * 签名
     */
    @JsonProperty("sign")
    private String sign;

    /**
     * 检查响应是否成功
     *
     * @return 如果成功返回true，否则返回false
     */
    public boolean isSuccess() {
        return alipaySystemOauthTokenResponse != null && 
               alipaySystemOauthTokenResponse.getAccessToken() != null;
    }

    /**
     * 获取访问令牌
     *
     * @return 访问令牌
     */
    public String getAccessToken() {
        return alipaySystemOauthTokenResponse != null ? 
               alipaySystemOauthTokenResponse.getAccessToken() : null;
    }

    /**
     * 获取刷新令牌
     *
     * @return 刷新令牌
     */
    public String getRefreshToken() {
        return alipaySystemOauthTokenResponse != null ? 
               alipaySystemOauthTokenResponse.getRefreshToken() : null;
    }

    /**
     * 获取过期时间
     *
     * @return 过期时间（秒）
     */
    public Long getExpiresIn() {
        return alipaySystemOauthTokenResponse != null ? 
               alipaySystemOauthTokenResponse.getExpiresIn() : null;
    }

    /**
     * 获取刷新令牌过期时间
     *
     * @return 刷新令牌过期时间（秒）
     */
    public Long getReExpiresIn() {
        return alipaySystemOauthTokenResponse != null ? 
               alipaySystemOauthTokenResponse.getReExpiresIn() : null;
    }

    /**
     * 获取用户ID
     *
     * @return 用户ID
     */
    public String getUserId() {
        return alipaySystemOauthTokenResponse != null ? 
               alipaySystemOauthTokenResponse.getUserId() : null;
    }

    /**
     * 获取授权范围
     *
     * @return 授权范围
     */
    public String getScope() {
        return alipaySystemOauthTokenResponse != null ? 
               alipaySystemOauthTokenResponse.getScope() : null;
    }

    /**
     * 获取授权开始时间
     *
     * @return 授权开始时间
     */
    public String getAuthStart() {
        return alipaySystemOauthTokenResponse != null ? 
               alipaySystemOauthTokenResponse.getAuthStart() : null;
    }

    /**
     * 获取错误描述
     *
     * @return 错误描述
     */
    public String getErrorDescription() {
        if (isSuccess()) {
            return "Success";
        }
        if (errorResponse != null) {
            return String.format("Error %s: %s", errorResponse.getCode(), errorResponse.getMsg());
        }
        return "Unknown error";
    }

    /**
     * Token响应数据
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TokenResponse {

        /**
         * 访问令牌
         */
        @JsonProperty("access_token")
        private String accessToken;

        /**
         * 刷新令牌
         */
        @JsonProperty("refresh_token")
        private String refreshToken;

        /**
         * 过期时间（秒）
         */
        @JsonProperty("expires_in")
        private Long expiresIn;

        /**
         * 刷新令牌过期时间（秒）
         */
        @JsonProperty("re_expires_in")
        private Long reExpiresIn;

        /**
         * 用户ID
         */
        @JsonProperty("user_id")
        private String userId;

        /**
         * 授权范围
         */
        @JsonProperty("scope")
        private String scope;

        /**
         * 授权开始时间
         */
        @JsonProperty("auth_start")
        private String authStart;
    }

    /**
     * 错误响应数据
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ErrorResponse {

        /**
         * 错误码
         */
        @JsonProperty("code")
        private String code;

        /**
         * 错误信息
         */
        @JsonProperty("msg")
        private String msg;

        /**
         * 子错误码
         */
        @JsonProperty("sub_code")
        private String subCode;

        /**
         * 子错误信息
         */
        @JsonProperty("sub_msg")
        private String subMsg;
    }
}
