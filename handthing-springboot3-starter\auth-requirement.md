好的，遵照您的指示。我们将前一节讨论的、支持多种登录方式的认证授权体系，固化成一份正式、详尽、可供研发团队直接使用的设计说明书。

这份文档将是该项目的核心蓝图。

-----

### **HandThing Auth Spring Boot Starter**

### **详细设计说明书**

| **文档版本** | **V1.0** |
| :--- | :--- |
| **状态** | 草案 (Draft) |
| **作者** | HandThing, Gemini |
| **创建日期** | 2025-07-28 |

-----

### **1. 概述 (Overview)**

#### **1.1. 文档目的**

本文档旨在详细阐述 `auth-spring-boot-starter` 系列的技术架构、模块设计、核心流程、接口协定和配置方案。它为构建一个支持多种登录方式（账号密码、短信验证码、第三方应用扫码等）的、统一的、可插拔的企业级认证授权中心提供了完整的技术蓝图。

#### **1.2. 项目背景**

在完成了底层通信、外部连接器和缓存系统后，一个健壮的认证授权体系是保障应用安全、串联用户体系的“龙骨”。本项目旨在封装 Spring Security 的复杂性，为现代Web应用（特别是前后端分离和微服务架构）提供一套基于JWT无状态认证的、开箱即用的解决方案。

#### **1.3. 核心设计理念：认证总线 (Authentication Bus)**

我们将构建一个可插拔的、多策略的“认证总线”。不同的登录请求如同不同规格的插头，都可以接入这个总线。总线内部由一个统一的 `AuthenticationManager` 进行调度，根据请求类型（`grant_type`）将其分发给对应的认证策略处理器（`AuthenticationProvider`）。无论采用何种方式认证成功，总线最终都输出一个标准化的JWT（JSON Web Token），实现了认证逻辑的隔离与最终产物的统一。

### **2. 整体架构 (Overall Architecture)**

#### **2.1. 架构与核心流程图**

```mermaid
graph TD
    subgraph 客户端 (Client)
        A[用户发起登录请求] --> B{POST /api/auth/token};
    end

    subgraph auth-spring-boot-starter (认证总线)
        B --> C[统一认证端点 TokenEndpoint];
        C --> D{AuthenticationManager};
        D -- 凭证 --> E[PasswordProvider];
        D -- 凭证 --> F[SmsCodeProvider];
        D -- 凭证 --> G[AppAuthProvider];
        E -- 认证成功 --> H{TokenService};
        F -- 认证成功 --> H;
        G -- 认证成功 --> H;
        H -- 生成JWT --> I[返回JWT给客户端];
    end

    subgraph 主应用 (Your Application)
        E --> J[UserDetailsService实现];
        F --> K[SmsCodeValidator实现];
        G --> L[SocialUserProvisioner实现];
        G --> M[app-connector-starter];
    end

    style C fill:#f9f,stroke:#333,stroke-width:2px
    style D fill:#ccf,stroke:#333,stroke-width:2px
    style H fill:#9cf,stroke:#333,stroke-width:2px
```

* **核心流程**: 所有认证请求汇集到统一端点`B`，`B`根据`grant_type`创建不同凭证，交由`D`处理。`D`轮询所有已注册的Provider(`E`, `F`, `G`)，找到能处理该凭证的Provider执行认证。认证过程中，Provider会调用由**主应用**提供的业务接口(`J`, `K`, `L`)来完成校验。成功后，统一交由`H`生成JWT并返回。

#### **2.2. 模块化设计**

为了实现高度解耦和按需加载，项目将拆分为多个模块：

* **`auth-core`**: **核心抽象**。定义不依赖Spring的纯粹接口、模型和异常，如`TokenService`接口。
* **`auth-spring-boot-starter`**: **核心编排器**。实现Spring Boot自动配置，负责装配`AuthenticationManager`、`JwtAuthenticationFilter`、统一端点和异常处理等核心组件。
* **`password-provider-starter`**: **密码认证**。提供基于`UserDetailsService`的密码认证`Provider`。
* **`sms-provider-starter`**: **短信认证**。提供基于短信验证码的认证`Provider`。
* **`app-auth-provider-starter`**: **第三方认证**。提供所有第三方平台（企微、钉钉等）登录的统一认证`Provider`，强依赖`app-connector-starter`。

### **3. 功能清单与模块依赖关系**

#### **3.1. 模块功能清单 (Checklist)**

##### **`auth-core` 模块**

* [ ] **接口定义**:
   * [ ] `TokenService`: 定义`createTokens(Authentication)`和`parseToken(String)`等方法。
   * [ ] `SmsCodeValidator`: 定义`validate(mobile, code)`方法，由主应用实现。
   * [ ] `SocialUserProvisioner`: 定义`provisionUser(UnifiedUserInfo)`方法，用于对接第三方登录和本地用户体系，由主应用实现。
* [ ] **模型定义**:
   * [ ] `AuthToken`: 包含`accessToken`, `refreshToken`, `tokenType` (`"Bearer"`)等。
   * [ ] `GrantType`: 枚举类，包含`PASSWORD`, `SMS_CODE`, `WECOM`, `DINGTALK`等。
* [ ] **异常体系定义**:
   * [ ] `AuthException`: 认证相关异常的基类。

##### **`auth-spring-boot-starter` 模块**

* [ ] **JWT实现**: 提供`JwtTokenService`，实现`TokenService`接口。
* [ ] **核心过滤器**:
   * [ ] 实现`JwtAuthenticationFilter`，用于解析请求头中的JWT，并构建`Authentication`对象放入`SecurityContext`。
* [ ] **统一认证端点**:
   * [ ] 实现`TokenEndpoint` (`/api/auth/token`)，根据`grant_type`创建不同`Authentication`凭证并分发给`AuthenticationManager`。
* [ ] **自动配置**:
   * [ ] `AuthAutoConfiguration`:
      * [ ] 自动装配`SecurityFilterChain`，集成`JwtAuthenticationFilter`，配置CORS、禁用Session。
      * [ ] 自动装配`AuthenticationManager`，并收集所有`AuthenticationProvider`类型的Bean。
      * [ ] 自动装配`TokenEndpoint`、`JwtTokenService`。
      * [ ] 根据`public-paths`配置，放行匿名访问路径。
   * [ ] `AuthProperties`:
      * [ ] 实现`@ConfigurationProperties`，定义`jwt`、`grant-types`、`public-paths`等配置项。
* [ ] **统一异常处理**:
   * [ ] 实现`AuthenticationEntryPoint`和`AccessDeniedHandler`，用于处理认证、授权失败时返回统一的JSON格式错误。

##### **`password-provider-starter` 模块**

* [ ] 实现`PasswordAuthenticationProvider`，注入`UserDetailsService`和`PasswordEncoder`。
* [ ] 实现`PasswordProviderAutoConfiguration`，当`handthing.auth.grant-types.password.enabled=true`时自动装配`Provider` Bean。

##### **`sms-provider-starter` 模块**

* [ ] 实现`SmsCodeAuthenticationProvider`，注入`SmsCodeValidator`和`UserDetailsService`。
* [ ] 实现`SmsProviderAutoConfiguration`，当`handthing.auth.grant-types.sms-code.enabled=true`时自动装配`Provider` Bean。

##### **`third-party-provider-starter` 模块**

* [ ] 实现`ThirdPartyAuthenticationProvider`，注入`app-connector`的`AuthService`和主应用提供的`SocialUserProvisioner`。
* [ ] 实现`ThirdPartyProviderAutoConfiguration`，当任何一个第三方`grant-type`被启用时，自动装配`Provider` Bean。

-----

#### **3.2. 模块依赖关系图**

```mermaid
graph TD
    A["主应用 (Your App)"]
    B["password-provider-starter"]
    C["sms-provider-starter"]
    D["third-party-provider-starter"]
    E["auth-spring-boot-starter"]
    F["auth-core"]
    G["spring-security-web"]
    H["jjwt (JSON Web Token lib)"]
    I["app-connector-starter"]

    A --> B
    A --> C
    A --> D

    B --> E
    C --> E
    D --> E

    E --> F
    E --> G
    E --> H

    D --> I

    subgraph 外部依赖
        G
        H
        I
    end

    subgraph 本项目模块
        B
        C
        D
        E
        F
    end
```

* **依赖关系说明**:
   * **主应用**根据需要，选择性地依赖一个或多个`provider-starter`。
   * 所有`provider-starter`都依赖于**核心编排器** `auth-spring-boot-starter`。
   * `auth-spring-boot-starter`依赖于**核心抽象** `auth-core`以及Spring Security等外部库。
   * `app-auth-provider-starter`额外依赖于我们之前设计的`app-connector-starter`来完成与第三方平台的通信：
   * `wecom-auth-provider-starter`
   * `dingtalk-auth-provider-starter`
   * `feishu-auth-provider-starter`
   * `wechat-auth-provider-starter`
   * `alipay-auth-provider-starter`
   * `douyin-auth-provider-starter`
   * `kuaishou-auth-provider-starter`
   * `xiaohongshu-auth-provider-starter`
   * `toutiao-auth-provider-starter`
   * `bilibili-auth-provider-starter` 等等。

### **4. 接口协定与配置**

#### **4.1. 核心接口 (由主应用实现)**

* **`org.springframework.security.core.userdetails.UserDetailsService`**: Spring Security标准接口，用于通过用户名/手机号加载用户。
* **`cn.com.handthing.starter.auth.core.SmsCodeValidator`**:
  ```java
  public interface SmsCodeValidator {
      boolean validate(String mobile, String code);
  }
  ```
* **`cn.com.handthing.starter.auth.core.SocialUserProvisioner`**:
  ```java
  public interface SocialUserProvisioner {
      UserDetails provisionUser(UnifiedUserInfo unifiedUserInfo);
  }
  ```

#### **4.2. 统一配置 (`application.yml`)**

```yaml
handthing:
  auth:
    # JWT核心配置
    jwt:
      secret: "a-very-long-and-secure-secret-key-for-hs256"
      access-token-ttl: 30m
      refresh-token-ttl: 7d
      
    # 按需启用登录方式
    grant-types:
      password:
        enabled: true
      sms-code:
        enabled: true
      wecom:
        enabled: true
        
    # 公共访问路径
    public-paths:
      - "/api/auth/token"
      - "/public/**"
      - "/doc.html" # Knife4j UI
```

这份设计说明书为您提供了一个清晰、完整、可执行的开发蓝图。它通过模块化和策略模式，构建了一个既强大又灵活的统一认证授权框架，能够很好地支撑现代企业级应用的需求。

您提出了一个非常关键且富有远见的补充点！一个成熟的认证授权体系，**不仅仅是解决“你是谁”和“你能做什么”的问题，更要解决“如何方便地在业务全程中使用你的信息”这一核心问题**。

这就是“用户上下文 (User Context)”的价值所在。它像一个“隐形的背包”，在用户认证成功后就背在身上，在处理该用户本次请求的整个生命周期中，任何代码层（Controller, Service, DAO）都可以轻松、安全地从中获取信息，而无需层层传递参数。

好的，我们现在将“用户上下文”作为一项核心需求，补充并升级之前的设计说明书。

-----

### **详细设计说明书 (V1.1 - 补充用户上下文需求)**

#### **变更摘要**

* **新增** 核心概念：`AuthContext` (统一上下文模型) 和 `AuthContextHolder` (线程级上下文容器)。
* **升级** `JwtAuthenticationFilter` 和 `TokenService` 的职责，使其负责上下文的创建、传递和清理。
* **新增** “用户上下文”章节，详细阐述其设计与使用。
* **更新** 功能清单和模块依赖关系。

-----

### **3.bis. 用户上下文 (User Context) 核心设计**

#### **3.bis.1. 设计目标**

* **易用性**: 业务代码可以通过一句静态方法调用 (`AuthContextHolder.get()`) 在任何地方获取当前登录用户的所有关键信息。
* **类型安全**: 提供一个强类型的`AuthContext`对象来承载上下文信息，避免使用容易出错的`Map<String, Object>`。
* **数据全面**: 上下文应包含用户ID、用户名、角色、权限，并为多租户场景预留租户ID (`tenantId`)。
* **可扩展**: 允许业务应用向上下文中（并最终到JWT中）添加自定义字段。
* **无侵入**: 上下文的传递对业务代码应完全透明，由框架在后台自动完成。

#### **3.bis.2. 核心组件**

##### **a) `AuthContext` 模型 (`auth-core` 模块)**

这是一个POJO，作为所有上下文信息的标准载体。

```java
public class AuthContext implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户唯一ID
     */
    private Long userId;

    /**
     * 用户名 (登录账号)
     */
    private String username;

    /**
     * 租户ID (关键，用于多租户数据隔离)
     */
    private Long tenantId;

    /**
     * 角色集合
     */
    private Set<String> roles;

    /**
     * 权限标识集合
     */
    private Set<String> permissions;

    /**
     * 扩展属性，用于业务自定义数据
     * 例如: departmentId, userLevel 等
     */
    private Map<String, Object> customAttributes = new HashMap<>();

    // --- Standard Getters and Setters ---
}
```

##### **b) `AuthContextHolder` 容器 (`auth-core` 模块)**

这是一个基于`ThreadLocal`的工具类，用于在当前请求线程中安全地存储和获取`AuthContext`。这是Spring Security `SecurityContextHolder` 的经典模式。

```java
public final class AuthContextHolder {

    private static final ThreadLocal<AuthContext> CONTEXT_HOLDER = new ThreadLocal<>();

    public static void setContext(AuthContext context) {
        Assert.notNull(context, "Only non-null AuthContext instances are permitted");
        CONTEXT_HOLDER.set(context);
    }

    public static AuthContext getContext() {
        return CONTEXT_HOLDER.get();
    }
    
    // 强烈建议提供便捷的getter方法
    public static Long getUserId() {
        return getContext() != null ? getContext().getUserId() : null;
    }

    public static Long getTenantId() {
        return getContext() != null ? getContext().getTenantId() : null;
    }

    public static void clearContext() {
        CONTEXT_HOLDER.remove();
    }
}
```

#### **3.bis.3. 上下文的生命周期**

1.  **创建与填充 (在登录时)**:

   * 当任何一个 `AuthenticationProvider` 认证成功后，它会返回一个包含 `UserDetails` 的 `Authentication` 对象。
   * `TokenService` 在创建JWT之前，会从 `UserDetails` 中提取信息（`userId`, `username`, `roles`等），并**创建**一个完整的 `AuthContext` 对象。
   * **（扩展点）** 如果 `UserDetails` 实现了某个特定接口（如`CustomContextSupplier`），`TokenService`会调用其方法，将返回的自定义数据填充到`AuthContext`的`customAttributes`中。
   * `TokenService` 将 `AuthContext` 中的所有字段作为 **JWT的Claims** 存入令牌。

2.  **传递与注入 (在后续请求中)**:

   * 当一个携带JWT的请求到达时，`JwtAuthenticationFilter` 捕获并解析它。
   * `TokenService.parseToken()` 方法负责验证JWT，并从其Claims中**重建** `AuthContext` 对象。
   * `JwtAuthenticationFilter` 拿到重建的 `AuthContext` 后，立即调用 `AuthContextHolder.setContext(context)` 将其放入当前线程的`ThreadLocal`中。
   * 然后，过滤器链继续执行 `chain.doFilter(request, response)`。

3.  **使用 (在业务代码中)**:

   * 在Controller、Service或任何后续的业务逻辑中，开发者都可以通过 `AuthContextHolder.getContext()` 或更便捷的 `AuthContextHolder.getUserId()` 来获取所需信息，无需任何参数传递。
   * **示例**:
     ```java
     @Service
     public class ProductServiceImpl implements ProductService {
         public Page<Product> findProducts(int page, int size) {
             Long currentTenantId = AuthContextHolder.getTenantId();
             // DAO层可以基于这个tenantId自动添加 "WHERE tenant_id = ?" 条件
             return productDao.findByTenantId(currentTenantId, page, size);
         }
     }
     ```

4.  **清理 (在请求结束时)**:

   * `JwtAuthenticationFilter` **必须** 在 `finally` 块中调用 `AuthContextHolder.clearContext()`。
   * 这确保了线程被线程池复用时，不会将上一个请求的上下文信息泄露给下一个请求，避免了严重的安全问题和数据错乱。

-----

### **4. 功能清单与模块依赖关系 (V1.1 更新)**

#### **4.1. 模块功能清单 (补充)**

##### **`auth-core` 模块**

* [x] **模型定义**:
   * [x] **`AuthContext.java`**: 定义统一上下文模型。
   * [x] **`AuthContextHolder.java`**: 定义`ThreadLocal`容器。
* [ ] **接口定义**:
   * [ ] `CustomContextSupplier`: (可选) 定义一个接口，`UserDetails`可实现此接口来提供自定义上下文数据。

##### **`auth-spring-boot-starter` 模块**

* [x] **JWT实现 (`JwtTokenService`)**:
   * [x] **升级**: `createTokens` 方法需增加从 `Authentication` 构建 `AuthContext` 并将其写入JWT Claims的逻辑。
   * [x] **升级**: `parseToken` 方法需增加从JWT Claims重建 `AuthContext` 的逻辑。
* [x] **核心过滤器 (`JwtAuthenticationFilter`)**:
   * [x] **升级**: 在解析Token成功后，必须调用 `AuthContextHolder.setContext()`。
   * [x] **升级**: 在 `finally` 块中，必须调用 `AuthContextHolder.clearContext()`。

-----

通过以上补充，我们的`auth-starter`不仅是一个认证工具，更是一个为业务开发提供了极大便利的应用级基础设施。它将用户身份和租户信息等核心上下文安全、透明地注入到整个业务流程中，是构建多租户SaaS平台等复杂应用的坚实基础。