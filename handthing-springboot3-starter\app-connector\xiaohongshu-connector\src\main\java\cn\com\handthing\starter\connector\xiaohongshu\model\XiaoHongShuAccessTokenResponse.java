package cn.com.handthing.starter.connector.xiaohongshu.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 小红书访问令牌响应
 * <p>
 * 小红书OAuth2.0认证获取访问令牌的响应数据模型。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
public class XiaoHongShuAccessTokenResponse {

    /**
     * 状态码，200表示成功
     */
    @JsonProperty("code")
    private Integer code;

    /**
     * 响应消息
     */
    @JsonProperty("message")
    private String message;

    /**
     * 响应数据
     */
    @JsonProperty("data")
    private TokenData data;

    /**
     * 检查响应是否成功
     *
     * @return 如果成功返回true，否则返回false
     */
    public boolean isSuccess() {
        return code != null && code == 200;
    }

    /**
     * 获取错误信息
     *
     * @return 错误信息
     */
    public String getErrorMessage() {
        if (isSuccess()) {
            return null;
        }
        return String.format("Code %d: %s", code, message);
    }

    /**
     * Token数据
     */
    @Data
    public static class TokenData {

        /**
         * 访问令牌
         */
        @JsonProperty("access_token")
        private String accessToken;

        /**
         * 访问令牌有效期，单位秒
         */
        @JsonProperty("expires_in")
        private Long expiresIn;

        /**
         * 刷新令牌
         */
        @JsonProperty("refresh_token")
        private String refreshToken;

        /**
         * 刷新令牌有效期，单位秒
         */
        @JsonProperty("refresh_expires_in")
        private Long refreshExpiresIn;

        /**
         * 用户唯一标识
         */
        @JsonProperty("open_id")
        private String openId;

        /**
         * 授权范围
         */
        @JsonProperty("scope")
        private String scope;

        /**
         * Token类型，通常为Bearer
         */
        @JsonProperty("token_type")
        private String tokenType;
    }
}
