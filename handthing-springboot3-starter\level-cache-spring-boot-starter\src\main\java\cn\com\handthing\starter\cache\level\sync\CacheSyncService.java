package cn.com.handthing.starter.cache.level.sync;

import cn.com.handthing.starter.cache.level.config.LevelCacheProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;

import java.time.Duration;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * 缓存同步服务
 * <p>
 * 负责多级缓存之间的同步消息发送和接收
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@RequiredArgsConstructor
public class CacheSyncService {

    private final LevelCacheProperties properties;
    private final RedisTemplate<String, Object> redisTemplate;
    private final RedisMessageListenerContainer messageListenerContainer;
    private final String instanceId;

    // 消息去重缓存
    private final Set<String> processedMessages = ConcurrentHashMap.newKeySet();

    /**
     * 发送缓存同步消息
     *
     * @param message 同步消息
     */
    public void sendSyncMessage(CacheSyncMessage message) {
        if (!properties.getSync().isEnabled()) {
            log.debug("Cache sync is disabled, skipping message send");
            return;
        }

        try {
            // 设置发送者ID
            message.setSenderId(instanceId);

            // 发送消息到Redis主题
            redisTemplate.convertAndSend(properties.getSync().getTopic(), message);
            log.debug("Sent cache sync message: {}", message);
        } catch (Exception e) {
            log.error("Failed to send cache sync message: {}", message, e);
        }
    }

    /**
     * 处理接收到的同步消息
     *
     * @param message 同步消息
     * @param handler 消息处理器
     */
    public void handleSyncMessage(CacheSyncMessage message, CacheSyncMessageHandler handler) {
        if (!properties.getSync().isEnabled()) {
            return;
        }

        try {
            // 忽略自己发送的消息
            if (instanceId.equals(message.getSenderId())) {
                log.debug("Ignoring message from self: {}", message.getMessageId());
                return;
            }

            // 消息去重
            if (properties.getSync().isEnableDeduplication() && 
                !isMessageProcessable(message.getMessageId())) {
                log.debug("Duplicate message ignored: {}", message.getMessageId());
                return;
            }

            // 处理消息
            handler.handle(message);
            log.debug("Processed cache sync message: {}", message);
        } catch (Exception e) {
            log.error("Failed to handle cache sync message: {}", message, e);
        }
    }

    /**
     * 订阅缓存同步主题
     *
     * @param handler 消息处理器
     */
    public void subscribe(CacheSyncMessageHandler handler) {
        if (!properties.getSync().isEnabled()) {
            log.info("Cache sync is disabled, skipping subscription");
            return;
        }

        try {
            ChannelTopic topic = new ChannelTopic(properties.getSync().getTopic());
            messageListenerContainer.addMessageListener((message, pattern) -> {
                try {
                    Object messageObj = redisTemplate.getValueSerializer().deserialize(message.getBody());
                    if (messageObj instanceof CacheSyncMessage) {
                        handleSyncMessage((CacheSyncMessage) messageObj, handler);
                    }
                } catch (Exception e) {
                    log.error("Failed to deserialize sync message", e);
                }
            }, topic);

            log.info("Subscribed to cache sync topic: {}", properties.getSync().getTopic());
        } catch (Exception e) {
            log.error("Failed to subscribe to cache sync topic", e);
        }
    }

    /**
     * 检查消息是否可处理（去重检查）
     *
     * @param messageId 消息ID
     * @return 是否可处理
     */
    private boolean isMessageProcessable(String messageId) {
        if (processedMessages.contains(messageId)) {
            return false;
        }

        // 添加到已处理消息集合
        processedMessages.add(messageId);

        // 定时清理过期的消息ID（简单实现）
        if (processedMessages.size() > 10000) {
            processedMessages.clear();
        }

        return true;
    }

    /**
     * 发送删除同步消息
     *
     * @param cacheName 缓存名称
     * @param key       缓存键
     */
    public void sendEvictMessage(String cacheName, String key) {
        CacheSyncMessage message = CacheSyncMessage.evict(instanceId, cacheName, key);
        sendSyncMessage(message);
    }

    /**
     * 发送清空同步消息
     *
     * @param cacheName 缓存名称
     */
    public void sendClearMessage(String cacheName) {
        CacheSyncMessage message = CacheSyncMessage.clear(instanceId, cacheName);
        sendSyncMessage(message);
    }

    /**
     * 发送更新同步消息
     *
     * @param cacheName 缓存名称
     * @param key       缓存键
     */
    public void sendPutMessage(String cacheName, String key) {
        CacheSyncMessage message = CacheSyncMessage.put(instanceId, cacheName, key);
        sendSyncMessage(message);
    }

    /**
     * 获取实例ID
     *
     * @return 实例ID
     */
    public String getInstanceId() {
        return instanceId;
    }
}