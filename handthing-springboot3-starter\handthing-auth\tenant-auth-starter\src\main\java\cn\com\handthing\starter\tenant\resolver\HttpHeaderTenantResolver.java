package cn.com.handthing.starter.tenant.resolver;

import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;

/**
 * HTTP头租户解析器
 * <p>
 * 从自定义HTTP头解析租户ID，支持多个备选头字段。
 * 例如：X-Tenant-ID: tenant-a
 * </p>
 *
 * <AUTHOR>
 * @since V3.0.0
 */
public class HttpHeaderTenantResolver implements TenantResolver {

    private static final Logger log = LoggerFactory.getLogger(HttpHeaderTenantResolver.class);

    /**
     * 默认的租户头名称
     */
    private static final String DEFAULT_HEADER_NAME = "X-Tenant-ID";

    /**
     * 默认的备选头名称
     */
    private static final List<String> DEFAULT_FALLBACK_HEADERS = Arrays.asList(
            "Tenant-ID", 
            "X-Tenant", 
            "Tenant"
    );

    /**
     * 主要的租户头名称
     */
    private final String primaryHeaderName;

    /**
     * 备选的租户头名称列表
     */
    private final List<String> fallbackHeaderNames;

    /**
     * 是否区分大小写
     */
    private final boolean caseSensitive;

    /**
     * 构造函数（使用默认头名称）
     */
    public HttpHeaderTenantResolver() {
        this(DEFAULT_HEADER_NAME, DEFAULT_FALLBACK_HEADERS, false);
    }

    /**
     * 构造函数（指定主要头名称）
     *
     * @param headerName 主要的租户头名称
     */
    public HttpHeaderTenantResolver(String headerName) {
        this(headerName, DEFAULT_FALLBACK_HEADERS, false);
    }

    /**
     * 构造函数（指定主要头名称和备选头名称）
     *
     * @param headerName         主要的租户头名称
     * @param fallbackHeaders    备选的租户头名称列表
     * @param caseSensitive      是否区分大小写
     */
    public HttpHeaderTenantResolver(String headerName, List<String> fallbackHeaders, boolean caseSensitive) {
        this.primaryHeaderName = headerName != null ? headerName : DEFAULT_HEADER_NAME;
        this.fallbackHeaderNames = fallbackHeaders != null ? fallbackHeaders : DEFAULT_FALLBACK_HEADERS;
        this.caseSensitive = caseSensitive;
        
        log.debug("HttpHeaderTenantResolver initialized with primary header: {}, fallback headers: {}, caseSensitive: {}", 
                  this.primaryHeaderName, this.fallbackHeaderNames, caseSensitive);
    }

    @Override
    public String resolveTenantId(HttpServletRequest request) {
        // 首先尝试主要头名称
        String tenantId = getHeaderValue(request, primaryHeaderName);
        if (StringUtils.hasText(tenantId)) {
            log.debug("Found tenant ID '{}' in primary header '{}'", tenantId, primaryHeaderName);
            return tenantId;
        }

        // 尝试备选头名称
        for (String fallbackHeader : fallbackHeaderNames) {
            tenantId = getHeaderValue(request, fallbackHeader);
            if (StringUtils.hasText(tenantId)) {
                log.debug("Found tenant ID '{}' in fallback header '{}'", tenantId, fallbackHeader);
                return tenantId;
            }
        }

        log.debug("No tenant ID found in any headers");
        return null;
    }

    @Override
    public boolean supports(HttpServletRequest request) {
        // 检查是否存在任何租户相关的头
        if (hasHeader(request, primaryHeaderName)) {
            return true;
        }

        for (String fallbackHeader : fallbackHeaderNames) {
            if (hasHeader(request, fallbackHeader)) {
                return true;
            }
        }

        return false;
    }

    @Override
    public int getOrder() {
        return 20; // 中等优先级
    }

    @Override
    public String getName() {
        return "HttpHeaderTenantResolver";
    }

    @Override
    public String getDescription() {
        return "Resolves tenant ID from HTTP headers: " + primaryHeaderName + 
               (fallbackHeaderNames.isEmpty() ? "" : " (fallback: " + fallbackHeaderNames + ")");
    }

    /**
     * 获取HTTP头的值
     *
     * @param request    HTTP请求
     * @param headerName 头名称
     * @return 头值，如果不存在则返回null
     */
    private String getHeaderValue(HttpServletRequest request, String headerName) {
        if (caseSensitive) {
            return request.getHeader(headerName);
        } else {
            // 不区分大小写的查找
            java.util.Enumeration<String> headerNames = request.getHeaderNames();
            while (headerNames.hasMoreElements()) {
                String name = headerNames.nextElement();
                if (name.equalsIgnoreCase(headerName)) {
                    return request.getHeader(name);
                }
            }
            return null;
        }
    }

    /**
     * 检查是否存在指定的HTTP头
     *
     * @param request    HTTP请求
     * @param headerName 头名称
     * @return 如果存在返回true，否则返回false
     */
    private boolean hasHeader(HttpServletRequest request, String headerName) {
        if (caseSensitive) {
            return request.getHeader(headerName) != null;
        } else {
            // 不区分大小写的查找
            java.util.Enumeration<String> headerNames = request.getHeaderNames();
            while (headerNames.hasMoreElements()) {
                String name = headerNames.nextElement();
                if (name.equalsIgnoreCase(headerName)) {
                    return true;
                }
            }
            return false;
        }
    }

    /**
     * 创建基于单个头名称的解析器
     *
     * @param headerName 头名称
     * @return HTTP头租户解析器实例
     */
    public static HttpHeaderTenantResolver forHeader(String headerName) {
        return new HttpHeaderTenantResolver(headerName);
    }

    /**
     * 创建基于多个头名称的解析器
     *
     * @param primaryHeader   主要头名称
     * @param fallbackHeaders 备选头名称
     * @return HTTP头租户解析器实例
     */
    public static HttpHeaderTenantResolver forHeaders(String primaryHeader, String... fallbackHeaders) {
        return new HttpHeaderTenantResolver(primaryHeader, Arrays.asList(fallbackHeaders), false);
    }

    /**
     * 创建区分大小写的解析器
     *
     * @param headerName 头名称
     * @return HTTP头租户解析器实例
     */
    public static HttpHeaderTenantResolver caseSensitive(String headerName) {
        return new HttpHeaderTenantResolver(headerName, DEFAULT_FALLBACK_HEADERS, true);
    }

    /**
     * 获取当前配置信息
     *
     * @return 配置信息字符串
     */
    public String getConfigInfo() {
        return String.format("HttpHeaderTenantResolver{primary=%s, fallback=%s, caseSensitive=%s}", 
                           primaryHeaderName, fallbackHeaderNames, caseSensitive);
    }

    /**
     * 获取主要头名称
     *
     * @return 主要头名称
     */
    public String getPrimaryHeaderName() {
        return primaryHeaderName;
    }

    /**
     * 获取备选头名称列表
     *
     * @return 备选头名称列表
     */
    public List<String> getFallbackHeaderNames() {
        return fallbackHeaderNames;
    }

    /**
     * 是否区分大小写
     *
     * @return 如果区分大小写返回true，否则返回false
     */
    public boolean isCaseSensitive() {
        return caseSensitive;
    }
}
