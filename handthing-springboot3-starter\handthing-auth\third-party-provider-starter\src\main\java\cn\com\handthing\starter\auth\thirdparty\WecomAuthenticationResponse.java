package cn.com.handthing.starter.auth.thirdparty;

import cn.com.handthing.starter.auth.core.AuthenticationResponse;
import cn.com.handthing.starter.auth.core.UserInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 企业微信认证响应
 * <p>
 * 企业微信认证的响应对象，包含认证结果、用户信息、令牌信息等。
 * 支持新用户注册和老用户登录的不同响应信息。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WecomAuthenticationResponse extends AuthenticationResponse {

    /**
     * 用户信息
     */
    private UserInfo userInfo;

    /**
     * 是否为新注册用户
     */
    private Boolean newUser;

    /**
     * 企业微信用户ID
     */
    private String wecomUserId;

    /**
     * 企业微信用户名
     */
    private String wecomUsername;

    /**
     * 企业微信头像
     */
    private String wecomAvatar;

    /**
     * 企业ID
     */
    private String corpId;

    /**
     * 应用ID
     */
    private String agentId;

    /**
     * 最后登录时间
     */
    private LocalDateTime lastLoginTime;

    /**
     * 最后登录IP
     */
    private String lastLoginIp;

    /**
     * 是否为首次登录
     */
    private Boolean firstLogin;

    /**
     * 默认构造函数
     */
    public WecomAuthenticationResponse() {
        super();
    }

    /**
     * 成功响应构造函数
     *
     * @param accessToken  访问令牌
     * @param refreshToken 刷新令牌
     * @param expiresIn    过期时间
     * @param userInfo     用户信息
     */
    public WecomAuthenticationResponse(String accessToken, String refreshToken, Long expiresIn, UserInfo userInfo) {
        super(accessToken, refreshToken, expiresIn);
        this.userInfo = userInfo;
        if (userInfo != null) {
            setUserId(userInfo.getUserId());
            setUsername(userInfo.getUsername());
        }
    }

    /**
     * 失败响应构造函数
     *
     * @param errorCode        错误代码
     * @param errorDescription 错误描述
     */
    public WecomAuthenticationResponse(String errorCode, String errorDescription) {
        super(errorCode, errorDescription);
    }

    /**
     * 创建成功响应
     *
     * @param accessToken  访问令牌
     * @param refreshToken 刷新令牌
     * @param expiresIn    过期时间
     * @param userInfo     用户信息
     * @return 企业微信认证响应
     */
    public static WecomAuthenticationResponse success(String accessToken, String refreshToken, 
                                                     Long expiresIn, UserInfo userInfo) {
        return new WecomAuthenticationResponse(accessToken, refreshToken, expiresIn, userInfo);
    }

    /**
     * 创建成功响应（新用户）
     *
     * @param accessToken  访问令牌
     * @param refreshToken 刷新令牌
     * @param expiresIn    过期时间
     * @param userInfo     用户信息
     * @return 企业微信认证响应
     */
    public static WecomAuthenticationResponse successForNewUser(String accessToken, String refreshToken, 
                                                               Long expiresIn, UserInfo userInfo) {
        WecomAuthenticationResponse response = new WecomAuthenticationResponse(accessToken, refreshToken, expiresIn, userInfo);
        response.setNewUser(true);
        response.setFirstLogin(true);
        return response;
    }

    /**
     * 创建失败响应
     *
     * @param errorCode        错误代码
     * @param errorDescription 错误描述
     * @return 企业微信认证响应
     */
    public static WecomAuthenticationResponse failure(String errorCode, String errorDescription) {
        return new WecomAuthenticationResponse(errorCode, errorDescription);
    }

    /**
     * 创建授权码无效响应
     *
     * @return 企业微信认证响应
     */
    public static WecomAuthenticationResponse invalidCode() {
        return failure("INVALID_WECOM_CODE", "企业微信授权码无效或已过期");
    }

    /**
     * 创建企业配置错误响应
     *
     * @param corpId 企业ID
     * @return 企业微信认证响应
     */
    public static WecomAuthenticationResponse invalidCorpConfig(String corpId) {
        return failure("INVALID_CORP_CONFIG", "企业微信配置错误: " + corpId);
    }

    /**
     * 创建用户不存在响应
     *
     * @param wecomUserId 企业微信用户ID
     * @return 企业微信认证响应
     */
    public static WecomAuthenticationResponse userNotFound(String wecomUserId) {
        return failure("WECOM_USER_NOT_FOUND", "企业微信用户不存在: " + wecomUserId);
    }

    /**
     * 创建用户被锁定响应
     *
     * @param wecomUserId 企业微信用户ID
     * @return 企业微信认证响应
     */
    public static WecomAuthenticationResponse userLocked(String wecomUserId) {
        return failure("USER_LOCKED", "用户已被锁定: " + wecomUserId);
    }

    /**
     * 创建用户被禁用响应
     *
     * @param wecomUserId 企业微信用户ID
     * @return 企业微信认证响应
     */
    public static WecomAuthenticationResponse userDisabled(String wecomUserId) {
        return failure("USER_DISABLED", "用户已被禁用: " + wecomUserId);
    }

    /**
     * 创建注册失败响应
     *
     * @param reason 失败原因
     * @return 企业微信认证响应
     */
    public static WecomAuthenticationResponse registrationFailed(String reason) {
        return failure("REGISTRATION_FAILED", "用户注册失败: " + reason);
    }

    /**
     * 创建API调用失败响应
     *
     * @param reason 失败原因
     * @return 企业微信认证响应
     */
    public static WecomAuthenticationResponse apiCallFailed(String reason) {
        return failure("WECOM_API_FAILED", "企业微信API调用失败: " + reason);
    }

    /**
     * 设置用户信息并同步基础字段
     *
     * @param userInfo 用户信息
     */
    public void setUserInfo(UserInfo userInfo) {
        this.userInfo = userInfo;
        if (userInfo != null) {
            setUserId(userInfo.getUserId());
            setUsername(userInfo.getUsername());
            this.lastLoginTime = userInfo.getLastLoginTime();
            this.lastLoginIp = userInfo.getLastLoginIp();
        }
    }

    /**
     * 设置企业微信用户信息
     *
     * @param wecomUserId   企业微信用户ID
     * @param wecomUsername 企业微信用户名
     * @param wecomAvatar   企业微信头像
     * @param corpId        企业ID
     * @param agentId       应用ID
     */
    public void setWecomUserInfo(String wecomUserId, String wecomUsername, String wecomAvatar, 
                                String corpId, String agentId) {
        this.wecomUserId = wecomUserId;
        this.wecomUsername = wecomUsername;
        this.wecomAvatar = wecomAvatar;
        this.corpId = corpId;
        this.agentId = agentId;
    }

    @Override
    public String toString() {
        if (isSuccess()) {
            return String.format("WecomAuthenticationResponse{success=true, userId='%s', wecomUserId='%s', corpId='%s', newUser=%s, firstLogin=%s}",
                    getUserId(), wecomUserId, corpId, newUser, firstLogin);
        } else {
            return String.format("WecomAuthenticationResponse{success=false, errorCode='%s', errorDescription='%s'}",
                    getErrorCode(), getErrorDescription());
        }
    }
}
