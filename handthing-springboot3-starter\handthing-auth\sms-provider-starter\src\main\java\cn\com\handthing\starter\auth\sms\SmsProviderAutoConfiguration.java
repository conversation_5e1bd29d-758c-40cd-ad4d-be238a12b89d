package cn.com.handthing.starter.auth.sms;

import cn.com.handthing.starter.auth.core.JwtTokenProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;

/**
 * 短信提供者自动配置
 * <p>
 * 负责短信认证提供者相关组件的自动配置，包括短信服务、用户服务、认证提供者等。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@AutoConfiguration
@ConditionalOnProperty(prefix = "handthing.auth.sms", name = "enabled", havingValue = "true", matchIfMissing = true)
public class SmsProviderAutoConfiguration {

    /**
     * 默认短信服务
     *
     * @return 短信服务
     */
    @Bean
    @ConditionalOnMissingBean
    public SmsService smsService() {
        log.info("Creating DefaultSmsService");
        return new DefaultSmsService();
    }

    /**
     * 默认短信用户服务
     *
     * @return 短信用户服务
     */
    @Bean
    @ConditionalOnMissingBean
    public SmsUserService smsUserService() {
        log.info("Creating DefaultSmsUserService");
        return new DefaultSmsUserService();
    }

    /**
     * 短信认证提供者
     *
     * @param smsUserService   短信用户服务
     * @param smsService       短信服务
     * @param jwtTokenProvider JWT令牌提供者
     * @return 短信认证提供者
     */
    @Bean
    @ConditionalOnMissingBean
    public SmsAuthenticationProvider smsAuthenticationProvider(
            SmsUserService smsUserService,
            SmsService smsService,
            JwtTokenProvider jwtTokenProvider) {
        
        log.info("Creating SmsAuthenticationProvider");
        return new SmsAuthenticationProvider(smsUserService, smsService, jwtTokenProvider);
    }

    /**
     * 短信控制器
     *
     * @param smsService     短信服务
     * @param smsUserService 短信用户服务
     * @return 短信控制器
     */
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(prefix = "handthing.auth.sms", name = "endpoints-enabled", havingValue = "true", matchIfMissing = true)
    public SmsController smsController(SmsService smsService, SmsUserService smsUserService) {
        log.info("Creating SmsController");
        return new SmsController(smsService, smsUserService);
    }
}
