package cn.com.handthing.starter.id.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * ID生成器配置属性
 * <p>
 * 提供ID生成器相关的配置选项，包括雪花算法参数、池化配置等。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
@ConfigurationProperties(prefix = "handthing.id")
public class IdProperties {
    
    /**
     * 是否启用ID生成器
     */
    private boolean enabled = true;
    
    /**
     * 雪花算法配置
     */
    private Snowflake snowflake = new Snowflake();
    
    /**
     * UUID配置
     */
    private Uuid uuid = new Uuid();
    
    /**
     * 雪花算法配置
     */
    @Data
    public static class Snowflake {
        
        /**
         * 是否启用雪花算法生成器
         */
        private boolean enabled = true;
        
        /**
         * 机器ID (0-31)
         */
        private long workerId = 1L;
        
        /**
         * 数据中心ID (0-31)
         */
        private long datacenterId = 1L;
        
        /**
         * 池化配置
         */
        private Pool pool = new Pool();
        
        /**
         * 池化配置
         */
        @Data
        public static class Pool {
            
            /**
             * 是否启用池化
             */
            private boolean enabled = false;
            
            /**
             * 缓冲池大小
             */
            private int size = 200;
            
            /**
             * 当池中ID少于此值时，触发异步批量填充
             */
            private int threshold = 50;
            
            /**
             * 每次填充的数量
             */
            private int batchSize = 100;
        }
    }
    
    /**
     * UUID配置
     */
    @Data
    public static class Uuid {
        
        /**
         * 是否启用UUID生成器
         */
        private boolean enabled = true;
        
        /**
         * 是否包含连字符
         */
        private boolean includeHyphens = false;
        
        /**
         * 是否转换为大写
         */
        private boolean upperCase = false;
    }
    
    /**
     * 验证配置的有效性
     */
    public void validate() {
        if (snowflake.enabled) {
            validateSnowflakeConfig();
        }
        
        if (snowflake.pool.enabled) {
            validatePoolConfig();
        }
    }
    
    /**
     * 验证雪花算法配置
     */
    private void validateSnowflakeConfig() {
        if (snowflake.workerId < 0 || snowflake.workerId > 31) {
            throw new IllegalArgumentException("Snowflake worker ID must be between 0 and 31");
        }
        
        if (snowflake.datacenterId < 0 || snowflake.datacenterId > 31) {
            throw new IllegalArgumentException("Snowflake datacenter ID must be between 0 and 31");
        }
    }
    
    /**
     * 验证池化配置
     */
    private void validatePoolConfig() {
        Snowflake.Pool pool = snowflake.pool;
        
        if (pool.size <= 0) {
            throw new IllegalArgumentException("Pool size must be positive");
        }
        
        if (pool.threshold <= 0 || pool.threshold >= pool.size) {
            throw new IllegalArgumentException("Pool threshold must be positive and less than pool size");
        }
        
        if (pool.batchSize <= 0 || pool.batchSize > pool.size) {
            throw new IllegalArgumentException("Pool batch size must be positive and not greater than pool size");
        }
    }
    
    /**
     * 获取配置摘要
     *
     * @return 配置摘要字符串
     */
    public String getConfigSummary() {
        StringBuilder sb = new StringBuilder();
        sb.append("IdProperties{");
        sb.append("enabled=").append(enabled);
        
        if (snowflake.enabled) {
            sb.append(", snowflake={enabled=").append(snowflake.enabled);
            sb.append(", workerId=").append(snowflake.workerId);
            sb.append(", datacenterId=").append(snowflake.datacenterId);
            
            if (snowflake.pool.enabled) {
                sb.append(", pool={enabled=").append(snowflake.pool.enabled);
                sb.append(", size=").append(snowflake.pool.size);
                sb.append(", threshold=").append(snowflake.pool.threshold);
                sb.append(", batchSize=").append(snowflake.pool.batchSize);
                sb.append("}");
            }
            sb.append("}");
        }
        
        if (uuid.enabled) {
            sb.append(", uuid={enabled=").append(uuid.enabled);
            sb.append(", includeHyphens=").append(uuid.includeHyphens);
            sb.append(", upperCase=").append(uuid.upperCase);
            sb.append("}");
        }
        
        sb.append("}");
        return sb.toString();
    }
}
