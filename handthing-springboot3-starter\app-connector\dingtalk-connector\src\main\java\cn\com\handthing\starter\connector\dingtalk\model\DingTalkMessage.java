package cn.com.handthing.starter.connector.dingtalk.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 钉钉消息模型
 * <p>
 * 钉钉消息相关的数据结构
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DingTalkMessage {

    /**
     * 机器人代码
     */
    private String robotCode;

    /**
     * 接收用户ID列表
     */
    private String[] userIds;

    /**
     * 消息模板Key
     */
    private String msgKey;

    /**
     * 消息参数
     */
    private Object msgParam;

    /**
     * 消息发送结果
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class SendResult {

        /**
         * 请求ID
         */
        @JsonProperty("requestId")
        private String requestId;

        /**
         * 处理结果
         */
        @JsonProperty("result")
        private Boolean result;

        /**
         * 错误码
         */
        @JsonProperty("code")
        private String code;

        /**
         * 错误信息
         */
        @JsonProperty("message")
        private String message;

        /**
         * 检查响应是否成功
         *
         * @return 如果成功返回true，否则返回false
         */
        public boolean isSuccess() {
            return result != null && result;
        }

        /**
         * 获取错误描述
         *
         * @return 错误描述
         */
        public String getErrorDescription() {
            if (isSuccess()) {
                return "Success";
            }
            return String.format("Error %s: %s", code, message);
        }
    }

    /**
     * 文本消息参数
     */
    @Data
    public static class TextMsgParam {
        private String content;
    }

    /**
     * 链接消息参数
     */
    @Data
    public static class LinkMsgParam {
        private String title;
        private String text;
        private String messageUrl;
        private String picUrl;
    }

    /**
     * 卡片消息参数
     */
    @Data
    public static class ActionCardMsgParam {
        private String title;
        private String text;
        private String singleTitle;
        @JsonProperty("singleURL")
        private String singleUrl;
    }

    /**
     * Markdown消息参数
     */
    @Data
    public static class MarkdownMsgParam {
        private String title;
        private String text;
    }
}
