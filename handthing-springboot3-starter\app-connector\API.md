# HandThing App Connector API 文档

## 概述

本文档详细描述了HandThing App Connector Framework的核心API接口，包括认证、消息发送、用户管理等功能。

## 核心接口

### 1. AuthService - 统一认证服务

#### 获取授权URL
```java
String getAuthorizationUrl(PlatformType platform, String state, String redirectUri)
```

**参数说明:**
- `platform`: 平台类型 (WECOM, DINGTALK, DOUYIN, ALIPAY, WECHAT, FEISHU, TOUTIAO, XIAOHONGSHU, BILIBILI)
- `state`: 状态参数，用于防CSRF攻击
- `redirectUri`: 授权回调地址

**返回值:** 授权URL字符串

**示例:**
```java
String authUrl = authService.getAuthorizationUrl(
    PlatformType.WECHAT, 
    "random-state", 
    "http://your-domain.com/callback"
);
```

#### 交换访问令牌
```java
UnifiedAccessToken exchangeToken(PlatformType platform, String code, String state, String redirectUri)
```

**参数说明:**
- `platform`: 平台类型
- `code`: 授权码
- `state`: 状态参数
- `redirectUri`: 回调地址

**返回值:** 统一访问令牌对象

#### 刷新访问令牌
```java
UnifiedAccessToken refreshToken(PlatformType platform, String refreshToken)
```

#### 获取用户信息
```java
UnifiedUserInfo getUserInfo(PlatformType platform, String accessToken)
```

#### 验证令牌
```java
boolean validateToken(PlatformType platform, String accessToken)
```

### 2. TokenManager - Token管理器

#### 存储Token
```java
void storeToken(String userId, PlatformType platform, UnifiedAccessToken token)
```

#### 获取Token
```java
UnifiedAccessToken getToken(String userId, PlatformType platform)
```

#### 刷新Token
```java
UnifiedAccessToken refreshToken(String userId, PlatformType platform)
```

#### 删除Token
```java
void removeToken(String userId, PlatformType platform)
```

## 平台特定API

### 企业微信 (WeComService)

#### 消息API
```java
// 发送文本消息
Map<String, Object> sendTextMessage(String accessToken, String userId, String content)

// 发送图片消息
Map<String, Object> sendImageMessage(String accessToken, String userId, String mediaId)

// 发送文件消息
Map<String, Object> sendFileMessage(String accessToken, String userId, String mediaId)

// 发送卡片消息
Map<String, Object> sendCardMessage(String accessToken, String userId, Map<String, Object> cardContent)
```

#### 用户API
```java
// 获取用户信息
Map<String, Object> getUserInfo(String accessToken, String userId)

// 获取用户列表
Map<String, Object> getUserList(String accessToken, String departmentId, Boolean fetchChild)

// 获取部门列表
Map<String, Object> getDepartmentList(String accessToken, String departmentId)
```

### 钉钉 (DingTalkService)

#### 消息API
```java
// 发送工作通知
Map<String, Object> sendWorkNotification(String accessToken, String userId, String content)

// 发送群消息
Map<String, Object> sendGroupMessage(String accessToken, String chatId, String content)

// 发送卡片消息
Map<String, Object> sendCardMessage(String accessToken, String userId, Map<String, Object> cardContent)
```

#### 审批API
```java
// 创建审批实例
Map<String, Object> createApproval(String accessToken, Map<String, Object> approvalData)

// 获取审批详情
Map<String, Object> getApprovalDetail(String accessToken, String processInstanceId)

// 获取审批列表
Map<String, Object> getApprovalList(String accessToken, String processCode, Integer offset, Integer size)
```

### 抖音 (DouyinService)

#### 视频API
```java
// 上传视频
Map<String, Object> uploadVideo(String accessToken, String title, String videoUrl, String cover, List<String> tags)

// 获取视频列表
Map<String, Object> getVideoList(String accessToken, Integer page, Integer pageSize)

// 获取视频详情
Map<String, Object> getVideoDetail(String accessToken, String videoId)

// 删除视频
Map<String, Object> deleteVideo(String accessToken, String videoId)
```

#### 数据分析API
```java
// 获取视频数据
Map<String, Object> getVideoAnalytics(String accessToken, String videoId)

// 获取用户数据
Map<String, Object> getUserAnalytics(String accessToken, LocalDate startDate, LocalDate endDate)

// 获取粉丝数据
Map<String, Object> getFansAnalytics(String accessToken)
```

### 支付宝 (AlipayService)

#### 支付API
```java
// 创建支付订单
Map<String, Object> createPayment(String outTradeNo, String subject, String totalAmount, String productCode)

// 查询支付状态
Map<String, Object> queryPayment(String outTradeNo)

// 申请退款
Map<String, Object> refund(String outTradeNo, String refundAmount, String refundReason)

// 查询退款状态
Map<String, Object> queryRefund(String outTradeNo, String outRequestNo)
```

#### 用户API
```java
// 获取用户信息
Map<String, Object> getUserInfo(String accessToken)

// 获取用户授权信息
Map<String, Object> getUserAuth(String accessToken)
```

### 微信 (WeChatService)

#### 消息API
```java
// 发送文本消息
Map<String, Object> sendTextMessage(String accessToken, String openId, String content)

// 发送图片消息
Map<String, Object> sendImageMessage(String accessToken, String openId, String mediaId)

// 发送模板消息
Map<String, Object> sendTemplateMessage(String accessToken, String openId, String templateId, Map<String, Object> data)

// 发送客服消息
Map<String, Object> sendCustomMessage(String accessToken, String openId, String msgType, Map<String, Object> content)
```

#### 用户API
```java
// 获取用户信息
Map<String, Object> getUserInfo(String accessToken, String openId)

// 获取用户列表
Map<String, Object> getUserList(String accessToken, String nextOpenId)

// 设置用户标签
Map<String, Object> setUserTag(String accessToken, String openId, Integer tagId)

// 创建用户标签
Map<String, Object> createTag(String accessToken, String tagName)
```

### 飞书 (FeishuService)

#### 消息API
```java
// 发送文本消息
Map<String, Object> sendTextMessage(String accessToken, String receiveId, String receiveIdType, String content)

// 发送富文本消息
Map<String, Object> sendRichTextMessage(String accessToken, String receiveId, String receiveIdType, Map<String, Object> richText)

// 发送卡片消息
Map<String, Object> sendCardMessage(String accessToken, String receiveId, String receiveIdType, Map<String, Object> cardContent)

// 发送图片消息
Map<String, Object> sendImageMessage(String accessToken, String receiveId, String receiveIdType, String imageKey)
```

#### 用户API
```java
// 获取用户信息
Map<String, Object> getUserInfo(String accessToken, String userId, String userIdType)

// 获取用户列表
Map<String, Object> getUserList(String accessToken, String departmentId, Integer pageSize, String pageToken)

// 获取部门信息
Map<String, Object> getDepartmentInfo(String accessToken, String departmentId)

// 获取部门列表
Map<String, Object> getDepartmentList(String accessToken, String parentDepartmentId, Boolean fetchChild, Integer pageSize, String pageToken)
```

## 数据模型

### UnifiedAccessToken
```java
public class UnifiedAccessToken {
    private String accessToken;        // 访问令牌
    private String refreshToken;       // 刷新令牌
    private Long expiresIn;           // 过期时间(秒)
    private String scope;             // 授权范围
    private PlatformType platform;    // 平台类型
    private String userId;            // 用户ID
    private Map<String, Object> extraInfo; // 额外信息
    private LocalDateTime createdAt;   // 创建时间
}
```

### UnifiedUserInfo
```java
public class UnifiedUserInfo {
    private String openId;            // 开放ID
    private String unionId;           // 联合ID
    private String nickname;          // 昵称
    private String avatar;            // 头像URL
    private Integer gender;           // 性别
    private String country;           // 国家
    private String province;          // 省份
    private String city;              // 城市
    private PlatformType platform;    // 平台类型
}
```

## 错误处理

### 异常类型
- `AuthException` - 认证相关异常
- `PlatformException` - 平台相关异常
- `TokenException` - Token相关异常
- `ConfigException` - 配置相关异常

### 错误响应格式
```json
{
  "code": -1,
  "message": "Error description",
  "data": null,
  "timestamp": "2023-12-01T10:00:00Z"
}
```

## 最佳实践

### 1. Token管理
- 使用TokenManager统一管理Token
- 启用自动刷新机制
- 合理设置缓存时间

### 2. 错误处理
- 捕获并处理平台特定异常
- 实现重试机制
- 记录详细的错误日志

### 3. 性能优化
- 使用连接池管理HTTP连接
- 启用缓存机制
- 异步处理非关键操作

### 4. 安全考虑
- 加密存储敏感信息
- 验证回调请求签名
- 实现访问频率限制
