package cn.com.handthing.starter.connector.dingtalk;

import cn.com.handthing.starter.connector.PlatformType;
import cn.com.handthing.starter.connector.auth.AuthProvider;
import cn.com.handthing.starter.connector.auth.UnifiedAccessToken;
import cn.com.handthing.starter.connector.auth.UnifiedUserInfo;
import cn.com.handthing.starter.connector.dingtalk.config.DingTalkConfig;
import cn.com.handthing.starter.connector.dingtalk.model.DingTalkAccessTokenResponse;
import cn.com.handthing.starter.connector.dingtalk.model.DingTalkUserInfoResponse;
import cn.com.handthing.starter.connector.exception.AuthException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 钉钉认证提供者
 * <p>
 * 实现钉钉OAuth2.0认证流程，包括授权URL生成、Token交换、用户信息获取等功能。
 * 支持钉钉扫码登录和网页授权。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "handthing.connector.dingtalk", name = "enabled", havingValue = "true")
public class DingTalkAuthProvider implements AuthProvider {

    private final DingTalkConfig config;
    private final RestTemplate restTemplate;

    // 钉钉API端点
    private static final String AUTH_URL = "https://login.dingtalk.com/oauth2/auth";
    private static final String TOKEN_URL = "https://api.dingtalk.com/v1.0/oauth2/userAccessToken";
    private static final String USER_INFO_URL = "https://api.dingtalk.com/v1.0/contact/users/me";

    @Override
    public PlatformType getPlatformType() {
        return PlatformType.DINGTALK;
    }

    @Override
    public String getAuthorizationUrl(String state, String redirectUri) throws AuthException {
        try {
            String effectiveRedirectUri = config.getEffectiveRedirectUri(redirectUri);
            String effectiveScope = config.getEffectiveScope("openid");
            
            return UriComponentsBuilder.fromHttpUrl(AUTH_URL)
                    .queryParam("response_type", "code")
                    .queryParam("client_id", config.getAppKey())
                    .queryParam("scope", effectiveScope)
                    .queryParam("state", state != null ? state : "")
                    .queryParam("redirect_uri", URLEncoder.encode(effectiveRedirectUri, StandardCharsets.UTF_8))
                    .queryParam("prompt", "consent")
                    .build()
                    .toUriString();
                    
        } catch (Exception e) {
            log.error("Failed to generate DingTalk authorization URL", e);
            throw AuthException.configurationError(PlatformType.DINGTALK, "Failed to generate authorization URL: " + e.getMessage());
        }
    }

    @Override
    public UnifiedAccessToken exchangeToken(String code, String state, String redirectUri) throws AuthException {
        try {
            // 1. 使用授权码获取用户访问令牌
            DingTalkAccessTokenResponse tokenResponse = getUserAccessToken(code);
            
            // 2. 构建统一访问令牌
            return UnifiedAccessToken.builder()
                    .platform(PlatformType.DINGTALK)
                    .accessToken(tokenResponse.getAccessToken())
                    .refreshToken(tokenResponse.getRefreshToken())
                    .tokenType("Bearer")
                    .expiresIn(tokenResponse.getExpiresIn())
                    .scope(tokenResponse.getScope())
                    .createdAt(LocalDateTime.now())
                    .expiresAt(LocalDateTime.now().plusSeconds(tokenResponse.getExpiresIn()))
                    .extraInfo(Map.of(
                            "corpId", tokenResponse.getCorpId() != null ? tokenResponse.getCorpId() : ""
                    ))
                    .build();
                    
        } catch (Exception e) {
            log.error("Failed to exchange DingTalk token for code: {}", code, e);
            if (e instanceof AuthException) {
                throw e;
            }
            throw AuthException.networkError(PlatformType.DINGTALK, e);
        }
    }

    @Override
    public UnifiedAccessToken refreshToken(String refreshToken) throws AuthException {
        try {
            // 构建刷新令牌请求
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("clientId", config.getAppKey());
            requestBody.put("clientSecret", config.getAppSecret());
            requestBody.put("refreshToken", refreshToken);
            requestBody.put("grantType", "refresh_token");
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);
            
            ResponseEntity<DingTalkAccessTokenResponse> response = restTemplate.postForEntity(
                    TOKEN_URL, request, DingTalkAccessTokenResponse.class);
            
            DingTalkAccessTokenResponse tokenResponse = response.getBody();
            
            if (tokenResponse == null || !tokenResponse.isSuccess()) {
                throw new AuthException("Failed to refresh token: " + 
                        (tokenResponse != null ? tokenResponse.getMessage() : "No response"),
                        AuthException.REFRESH_TOKEN_EXPIRED, PlatformType.DINGTALK);
            }
            
            return UnifiedAccessToken.builder()
                    .platform(PlatformType.DINGTALK)
                    .accessToken(tokenResponse.getAccessToken())
                    .refreshToken(tokenResponse.getRefreshToken())
                    .tokenType("Bearer")
                    .expiresIn(tokenResponse.getExpiresIn())
                    .scope(tokenResponse.getScope())
                    .createdAt(LocalDateTime.now())
                    .expiresAt(LocalDateTime.now().plusSeconds(tokenResponse.getExpiresIn()))
                    .build();
                    
        } catch (Exception e) {
            log.error("Failed to refresh DingTalk token", e);
            if (e instanceof AuthException) {
                throw e;
            }
            throw AuthException.networkError(PlatformType.DINGTALK, e);
        }
    }

    @Override
    public UnifiedUserInfo getUserInfo(String accessToken) throws AuthException {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(accessToken);
            headers.set("x-acs-dingtalk-access-token", accessToken);
            
            HttpEntity<Void> request = new HttpEntity<>(headers);
            
            ResponseEntity<DingTalkUserInfoResponse> response = restTemplate.exchange(
                    USER_INFO_URL, HttpMethod.GET, request, DingTalkUserInfoResponse.class);
            
            DingTalkUserInfoResponse userInfo = response.getBody();
            
            if (userInfo == null) {
                throw new AuthException("Failed to get user info: No response",
                        AuthException.INVALID_TOKEN, PlatformType.DINGTALK);
            }
            
            return UnifiedUserInfo.builder()
                    .platform(PlatformType.DINGTALK)
                    .userId(userInfo.getUnionId())
                    .openId(userInfo.getOpenId())
                    .unionId(userInfo.getUnionId())
                    .nickname(userInfo.getNick())
                    .realName(userInfo.getNick())
                    .avatar(userInfo.getAvatarUrl())
                    .email(userInfo.getEmail())
                    .mobile(userInfo.getMobile())
                    .status("active")
                    .fetchedAt(LocalDateTime.now())
                    .extraInfo(Map.of(
                            "stateCode", userInfo.getStateCode() != null ? userInfo.getStateCode() : ""
                    ))
                    .build();
                    
        } catch (Exception e) {
            log.error("Failed to get DingTalk user info", e);
            if (e instanceof AuthException) {
                throw e;
            }
            throw AuthException.networkError(PlatformType.DINGTALK, e);
        }
    }

    @Override
    public boolean supportsRefreshToken() {
        return true; // 钉钉支持刷新令牌
    }

    @Override
    public String getDefaultScope() {
        return "openid";
    }

    /**
     * 获取用户访问令牌
     */
    private DingTalkAccessTokenResponse getUserAccessToken(String code) throws AuthException {
        try {
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("clientId", config.getAppKey());
            requestBody.put("clientSecret", config.getAppSecret());
            requestBody.put("code", code);
            requestBody.put("grantType", "authorization_code");
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);
            
            ResponseEntity<DingTalkAccessTokenResponse> response = restTemplate.postForEntity(
                    TOKEN_URL, request, DingTalkAccessTokenResponse.class);
            
            DingTalkAccessTokenResponse tokenResponse = response.getBody();
            
            if (tokenResponse == null || !tokenResponse.isSuccess()) {
                throw new AuthException("Failed to get access token: " + 
                        (tokenResponse != null ? tokenResponse.getMessage() : "No response"),
                        AuthException.INVALID_CODE, PlatformType.DINGTALK);
            }
            
            return tokenResponse;
            
        } catch (Exception e) {
            log.error("Failed to get DingTalk access token for code: {}", code, e);
            throw AuthException.networkError(PlatformType.DINGTALK, e);
        }
    }
}
