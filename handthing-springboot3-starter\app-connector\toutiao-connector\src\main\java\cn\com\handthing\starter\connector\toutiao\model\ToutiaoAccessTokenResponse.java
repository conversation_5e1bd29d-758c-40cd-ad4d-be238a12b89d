package cn.com.handthing.starter.connector.toutiao.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 今日头条访问令牌响应
 * <p>
 * 今日头条OAuth2.0认证获取访问令牌的响应数据模型。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
public class ToutiaoAccessTokenResponse {

    /**
     * 错误码，0表示成功
     */
    @JsonProperty("error_code")
    private Integer errorCode;

    /**
     * 错误描述
     */
    @JsonProperty("description")
    private String description;

    /**
     * 响应数据
     */
    @JsonProperty("data")
    private TokenData data;

    /**
     * 检查响应是否成功
     *
     * @return 如果成功返回true，否则返回false
     */
    public boolean isSuccess() {
        return errorCode != null && errorCode == 0;
    }

    /**
     * 获取错误描述
     *
     * @return 错误描述
     */
    public String getErrorDescription() {
        if (isSuccess()) {
            return null;
        }
        return String.format("Error %d: %s", errorCode, description);
    }

    /**
     * Token数据
     */
    @Data
    public static class TokenData {

        /**
         * 访问令牌
         */
        @JsonProperty("access_token")
        private String accessToken;

        /**
         * 访问令牌有效期，单位秒
         */
        @JsonProperty("expires_in")
        private Long expiresIn;

        /**
         * 刷新令牌
         */
        @JsonProperty("refresh_token")
        private String refreshToken;

        /**
         * 刷新令牌有效期，单位秒
         */
        @JsonProperty("refresh_expires_in")
        private Long refreshExpiresIn;

        /**
         * 用户唯一标识
         */
        @JsonProperty("open_id")
        private String openId;

        /**
         * 授权范围
         */
        @JsonProperty("scope")
        private String scope;

        /**
         * Token类型，通常为Bearer
         */
        @JsonProperty("token_type")
        private String tokenType;
    }
}
