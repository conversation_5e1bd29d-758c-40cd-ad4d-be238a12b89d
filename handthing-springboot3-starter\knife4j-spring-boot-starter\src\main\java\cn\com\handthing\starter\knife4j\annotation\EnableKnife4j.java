package cn.com.handthing.starter.knife4j.annotation;

import cn.com.handthing.starter.knife4j.configuration.Knife4jAutoConfiguration;
import cn.com.handthing.starter.knife4j.configuration.Knife4jSecurityConfiguration;
import org.springframework.context.annotation.Import;

import java.lang.annotation.*;

/**
 * 启用 HandThing Knife4j 功能
 * 
 * <p>在 Spring Boot 启动类上添加此注解，即可为项目集成基于 Knife4j 的增强版 API 文档。
 * 
 * <p>此注解会自动启用以下功能：
 * <ul>
 *   <li>Knife4j 美观的 UI 界面</li>
 *   <li>在线 API 调试功能</li>
 *   <li>文档导出功能</li>
 *   <li>接口搜索和分组</li>
 *   <li>继承 api-doc-spring-boot-starter 的所有功能</li>
 * </ul>
 * 
 * <p>使用示例：
 * <pre>{@code
 * @SpringBootApplication
 * @EnableKnife4j
 * public class Application {
 *     public static void main(String[] args) {
 *         SpringApplication.run(Application.class, args);
 *     }
 * }
 * }</pre>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
@Import({Knife4jSecurityConfiguration.class, Knife4jAutoConfiguration.class})
public @interface EnableKnife4j {
}