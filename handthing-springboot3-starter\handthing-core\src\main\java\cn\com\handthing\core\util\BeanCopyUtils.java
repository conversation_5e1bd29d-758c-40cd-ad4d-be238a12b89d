package cn.com.handthing.core.util;

import org.springframework.beans.BeanUtils;
import java.io.*;

/**
 * 对象拷贝工具类
 *
 * <AUTHOR>
 * @since V1.0.0
 */
public final class BeanCopyUtils {

    private BeanCopyUtils() {}

    /**
     * <b>浅拷贝</b>: 基于 Spring 的 BeanUtils，性能高，适用于属性均为基本类型或不可变对象的场景。
     * @param source 源对象
     * @param target 目标对象
     */
    public static void shallowCopy(Object source, Object target) {
        if (source == null || target == null) {
            return;
        }
        BeanUtils.copyProperties(source, target);
    }
    
    /**
     * <b>深拷贝 (基于 Jackson)</b>: 性能较高，通用性强，不要求对象实现 Serializable 接口。
     * <p><b>注意</b>: 此方法无法处理循环引用的对象图，且要求对象的属性有公有的 getter/setter 或被 Jackson 注解标记。
     * @param source 源对象
     * @return 深拷贝后的新对象
     */
    @SuppressWarnings("unchecked")
    public static <T> T deepCopyByJson(T source) {
        if (source == null) {
            return null;
        }
        // 由于 source 本身有类型信息，可以直接使用 source.getClass()
        String json = JsonUtils.toJson(source);
        return (T) JsonUtils.fromJson(json, source.getClass());
    }

    /**
     * <b>深拷贝 (基于 JDK 序列化)</b>: 能够处理复杂的对象图（包括循环引用），但性能相对较低，且要求所有相关对象都必须实现 {@link java.io.Serializable} 接口。
     * @param source 源对象
     * @return 深拷贝后的新对象
     */
    @SuppressWarnings("unchecked")
    public static <T extends Serializable> T deepCopyBySerialization(T source) {
        if (source == null) {
            return null;
        }
        try {
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            ObjectOutputStream oos = new ObjectOutputStream(bos);
            oos.writeObject(source);
            ByteArrayInputStream bis = new ByteArrayInputStream(bos.toByteArray());
            ObjectInputStream ois = new ObjectInputStream(bis);
            return (T) ois.readObject();
        } catch (IOException | ClassNotFoundException e) {
            // 在工具类中，将受检异常包装为运行时异常是常见做法
            throw new RuntimeException("Deep copy by serialization failed", e);
        }
    }
}
