package cn.com.handthing.starter.auth;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 认证Web MVC配置器
 * <p>
 * 配置Spring MVC相关的认证设置，包括拦截器注册、CORS配置等。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@RequiredArgsConstructor
public class AuthWebMvcConfigurer implements WebMvcConfigurer {

    private final AuthenticationInterceptor authenticationInterceptor;
    private final AuthProperties authProperties;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        log.info("Registering authentication interceptor");
        registry.addInterceptor(authenticationInterceptor)
                .addPathPatterns("/**")
                .excludePathPatterns(authProperties.getWeb().getExcludePaths())
                .excludePathPatterns(authProperties.getWeb().getStaticPaths());
    }

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        if (authProperties.getSecurity().isCorsEnabled()) {
            log.info("Configuring CORS settings");
            
            registry.addMapping("/**")
                    .allowedOriginPatterns(authProperties.getSecurity().getAllowedOrigins().toArray(new String[0]))
                    .allowedMethods(authProperties.getSecurity().getAllowedMethods().toArray(new String[0]))
                    .allowedHeaders(authProperties.getSecurity().getAllowedHeaders().toArray(new String[0]))
                    .allowCredentials(authProperties.getSecurity().isAllowCredentials())
                    .maxAge(authProperties.getSecurity().getMaxAge().getSeconds());
        }
    }
}
