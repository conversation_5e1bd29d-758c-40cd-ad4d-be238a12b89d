# HandThing认证授权体系测试指南

## 🎯 概述

这是HandThing认证授权体系的完整测试指南，展示了密码认证、短信验证码认证和企业微信认证的集成和使用。

## 🚀 快速开始

### 启动应用

```bash
# 在项目根目录执行
mvn spring-boot:run -pl test-app
```

应用将在 **http://localhost:8081** 启动

### 访问地址

- **主页**: http://localhost:8081/
- **登录页面**: http://localhost:8081/login
- **认证首页**: http://localhost:8081/auth-home
- **API文档**: http://localhost:8081/doc.html

## 🔐 支持的认证方式

### 1. 密码认证 (PASSWORD)

**测试账号**:
- `admin` / `admin123` (管理员角色)
- `user` / `user123` (普通用户角色)
- `test` / `test123` (测试用户角色)

**测试步骤**:
1. 访问 http://localhost:8081/login
2. 选择"密码登录"标签
3. 输入测试账号（admin/admin123）
4. 点击登录，验证登录成功

**API测试**:
```bash
curl -X POST http://localhost:8081/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "grant_type": "password",
    "username": "admin",
    "password": "admin123"
  }'
```

### 2. 短信验证码认证 (SMS_CODE)

**功能特性**:
- 支持登录和注册验证码
- 自动注册新用户
- 发送频率限制（1分钟内限制重发，每日最多10次）
- 验证码有效期5分钟

**测试步骤**:
1. 访问 http://localhost:8081/login
2. 选择"短信登录"标签
3. 输入手机号（如：13800138000）
4. 点击"发送验证码"
5. 查看控制台日志获取验证码
6. 输入验证码并登录

**API测试**:
```bash
# 1. 发送验证码
curl -X POST http://localhost:8081/auth/sms/send \
  -H "Content-Type: application/json" \
  -d '{
    "phone": "13800138000",
    "code_type": "login"
  }'

# 2. 短信登录
curl -X POST http://localhost:8081/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "grant_type": "sms_code",
    "phone": "13800138000",
    "sms_code": "123456",
    "auto_register": true
  }'
```

### 3. 企业微信认证 (WECOM)

**功能特性**:
- 支持网页授权和扫码登录
- 自动获取用户详细信息
- 支持用户绑定和自动注册
- 完整的OAuth2流程

**测试步骤**:
1. 访问 http://localhost:8081/login
2. 选择"企业微信"标签
3. 点击"企业微信登录"
4. 系统生成授权URL（测试环境）
5. 模拟授权回调
6. 验证用户信息获取

**API测试**:
```bash
# 1. 生成授权URL
curl -X POST http://localhost:8081/auth/wecom/auth-url \
  -H "Content-Type: application/json" \
  -d '{
    "corp_id": "test_corp_id",
    "agent_id": "test_agent_id",
    "redirect_uri": "http://localhost:8081/wecom/callback",
    "auth_type": "web"
  }'

# 2. 企业微信登录（获取授权码后）
curl -X POST http://localhost:8081/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "grant_type": "wecom",
    "code": "authorization_code",
    "corp_id": "test_corp_id",
    "corp_secret": "test_corp_secret",
    "agent_id": "test_agent_id",
    "auto_register": true
  }'
```

## 🛠️ 主要API接口

### 认证相关API

| 接口 | 方法 | 描述 |
|------|------|------|
| `/auth/login` | POST | 统一登录接口 |
| `/auth/logout` | POST | 登出接口 |
| `/auth/refresh` | POST | 刷新令牌 |
| `/auth/userinfo` | GET | 获取用户信息 |
| `/auth/grant-types` | GET | 获取支持的认证方式 |

### 短信相关API

| 接口 | 方法 | 描述 |
|------|------|------|
| `/auth/sms/send` | POST | 发送短信验证码 |
| `/auth/sms/verify` | POST | 验证短信验证码 |
| `/auth/sms/stats` | GET | 获取发送统计信息 |

### 企业微信相关API

| 接口 | 方法 | 描述 |
|------|------|------|
| `/auth/wecom/auth-url` | POST | 生成企业微信授权URL |
| `/auth/wecom/validate-config` | POST | 验证企业微信配置 |
| `/auth/wecom/user-info` | POST | 获取企业微信用户信息 |
| `/auth/wecom/stats` | GET | 获取企业微信统计信息 |

### 测试API

| 接口 | 方法 | 描述 |
|------|------|------|
| `/protected` | GET | 测试受保护资源 |
| `/admin` | GET | 测试管理员资源 |
| `/api/current-user` | GET | 获取当前用户信息 |

## 🎨 Web界面功能

### 登录页面
- 多标签认证方式选择
- 密码登录（用户名/密码）
- 短信登录（手机号/验证码）
- 企业微信登录（一键授权）
- API测试区域

### 个人信息页面
- 基本信息展示
- 权限信息（角色、权限）
- 登录信息（认证方式、时间、IP）
- 功能测试按钮

## 🧪 完整测试流程

### 1. 密码认证测试
1. 访问登录页面
2. 使用admin/admin123登录
3. 验证管理员权限
4. 测试受保护资源访问

### 2. 短信认证测试
1. 访问登录页面
2. 输入手机号发送验证码
3. 查看控制台获取验证码
4. 完成短信登录
5. 验证自动注册功能

### 3. 企业微信认证测试
1. 访问登录页面
2. 点击企业微信登录
3. 模拟授权流程
4. 验证用户信息获取

### 4. 权限测试
1. 使用不同角色用户登录
2. 测试受保护资源访问
3. 测试管理员资源访问
4. 验证权限控制效果

## 📊 测试结果验证

### 成功指标
- ✅ 登录成功，获得JWT令牌
- ✅ 用户信息正确显示
- ✅ 权限控制生效
- ✅ 令牌刷新正常
- ✅ 登出功能正常

### 日志监控
查看控制台日志，验证：
- 认证提供者加载
- 用户认证流程
- 令牌生成和验证
- 权限检查过程

## 🎉 总结

通过这个测试应用，您可以：

1. **体验多种认证方式** - 密码、短信、企业微信
2. **验证统一认证接口** - 一套API支持所有认证方式
3. **测试权限控制** - 角色和权限的有效性
4. **了解集成方法** - 为实际项目提供参考

HandThing认证授权体系提供了完整、灵活、易用的认证解决方案！
