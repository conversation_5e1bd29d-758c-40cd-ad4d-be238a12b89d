package cn.com.handthing.starter.crypto.service;

import cn.com.handthing.starter.crypto.core.Digester;
import cn.com.handthing.starter.crypto.exception.CryptoException;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import org.bouncycastle.crypto.digests.SM3Digest;
import org.bouncycastle.util.encoders.Hex;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.Objects;

/**
 * 使用国密 SM3 算法的摘要处理器实现。
 *
 * <AUTHOR>
 * @since V1.0.0
 */
public class Sm3Digester extends AbstractCryptoService implements Digester {

    private final String salt;
    private final MeterRegistry meterRegistry;

    public Sm3Digester(String beanName, String salt, MeterRegistry meterRegistry) {
        super(beanName);
        // salt can be null
        this.salt = salt;
        this.meterRegistry = Objects.requireNonNull(meterRegistry, "MeterRegistry must not be null");
    }

    @Override
    public String digest(String data) {
        return Timer.builder("crypto.operations.duration")
                .tag("processor", this.beanName)
                .tag("operation", "digest")
                .register(meterRegistry)
                .record(() -> {
                    try {
                        SM3Digest digest = new SM3Digest();
                        String dataToDigest = (salt != null) ? data + salt : data;
                        byte[] dataBytes = dataToDigest.getBytes(StandardCharsets.UTF_8);
                        digest.update(dataBytes, 0, dataBytes.length);
                        byte[] hash = new byte[digest.getDigestSize()];
                        digest.doFinal(hash, 0);
                        return Hex.toHexString(hash);
                    } catch (Exception e) {
                        throw new CryptoException("SM3 digest failed for processor: " + beanName, e);
                    }
                });
    }

    @Override
    public boolean matches(String data, String digestedData) {
        String newDigest = digest(data);
        // Use a time-constant comparison to prevent timing attacks
        return MessageDigest.isEqual(newDigest.getBytes(StandardCharsets.US_ASCII), digestedData.toLowerCase().getBytes(StandardCharsets.US_ASCII));
    }

    @Override
    public void selfCheck() {
        // Digesting is a deterministic operation, no special check needed.
    }
}