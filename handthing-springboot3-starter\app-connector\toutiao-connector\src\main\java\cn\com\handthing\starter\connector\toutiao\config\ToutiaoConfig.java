package cn.com.handthing.starter.connector.toutiao.config;

import cn.com.handthing.starter.connector.config.PlatformConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 今日头条连接器配置
 * <p>
 * 支持今日头条开放平台的配置，包括ClientKey、ClientSecret等认证信息。
 * 支持内容发布、数据分析等功能的配置。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ConfigurationProperties(prefix = "handthing.connector.toutiao")
public class ToutiaoConfig extends PlatformConfig {

    /**
     * 今日头条应用ID (Client Key)
     */
    private String clientKey;

    /**
     * 今日头条应用密钥 (Client Secret)
     */
    private String clientSecret;

    /**
     * 应用类型
     * web: 网页应用
     * mobile: 移动应用
     * server: 服务端应用
     */
    private String appType = "web";

    /**
     * 是否为企业应用模式
     */
    private boolean enterpriseMode = false;

    /**
     * 是否启用内容发布功能
     */
    private boolean contentPublishEnabled = true;

    /**
     * 是否启用数据分析功能
     */
    private boolean analyticsEnabled = true;

    /**
     * 是否启用用户管理功能
     */
    private boolean userManagementEnabled = true;

    /**
     * 今日头条API基础URL
     */
    private String apiBaseUrl = "https://open.toutiao.com";

    /**
     * 是否使用沙箱环境
     */
    private boolean sandboxMode = false;

    /**
     * 沙箱环境API基础URL
     */
    private String sandboxApiBaseUrl = "https://open-sandbox.toutiao.com";

    /**
     * 授权范围
     */
    private String scope = "user_info,content.write,data.read";

    /**
     * 获取实际使用的API基础URL
     *
     * @return API基础URL
     */
    public String getEffectiveApiBaseUrl() {
        return sandboxMode ? sandboxApiBaseUrl : apiBaseUrl;
    }

    /**
     * 验证配置是否有效
     *
     * @return 如果配置有效返回true，否则返回false
     */
    @Override
    public boolean isValid() {
        if (!super.isValid()) {
            return false;
        }

        // 检查必需的配置项
        if (clientKey == null || clientKey.trim().isEmpty()) {
            return false;
        }

        if (clientSecret == null || clientSecret.trim().isEmpty()) {
            return false;
        }

        return true;
    }

    /**
     * 获取配置描述信息
     *
     * @return 配置描述
     */
    public String getConfigDescription() {
        return String.format("Toutiao Config - ClientKey: %s, AppType: %s, Enterprise: %s, Sandbox: %s",
                clientKey, appType, enterpriseMode, sandboxMode);
    }

    /**
     * 是否为网页应用
     *
     * @return 如果是网页应用返回true，否则返回false
     */
    public boolean isWebApp() {
        return "web".equals(appType);
    }

    /**
     * 是否为移动应用
     *
     * @return 如果是移动应用返回true，否则返回false
     */
    public boolean isMobileApp() {
        return "mobile".equals(appType);
    }

    /**
     * 是否为服务端应用
     *
     * @return 如果是服务端应用返回true，否则返回false
     */
    public boolean isServerApp() {
        return "server".equals(appType);
    }

    /**
     * 是否启用了内容发布功能
     *
     * @return 如果启用了内容发布功能返回true，否则返回false
     */
    public boolean isContentPublishAvailable() {
        return contentPublishEnabled && scope != null && scope.contains("content.write");
    }

    /**
     * 是否启用了数据分析功能
     *
     * @return 如果启用了数据分析功能返回true，否则返回false
     */
    public boolean isAnalyticsAvailable() {
        return analyticsEnabled && scope != null && scope.contains("data.read");
    }

    /**
     * 是否启用了用户管理功能
     *
     * @return 如果启用了用户管理功能返回true，否则返回false
     */
    public boolean isUserManagementAvailable() {
        return userManagementEnabled && scope != null && scope.contains("user_info");
    }
}
