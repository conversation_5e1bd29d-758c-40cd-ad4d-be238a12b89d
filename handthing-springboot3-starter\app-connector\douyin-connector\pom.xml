<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>cn.com.handthing.springboot3.starter</groupId>
        <artifactId>starter-parent</artifactId>
        <version>${revision}</version>
        <relativePath>../../starter-parent/pom.xml</relativePath>
    </parent>

    <artifactId>douyin-connector</artifactId>

    <name>HandThing :: App Connector :: Douyin</name>
    <description>抖音连接器，提供抖音OAuth认证和API调用功能</description>

    <dependencies>
        <!-- App Connector Core -->
        <dependency>
            <groupId>cn.com.handthing.springboot3.starter</groupId>
            <artifactId>app-connector-core</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- App Connector Spring Boot Starter -->
        <dependency>
            <groupId>cn.com.handthing.springboot3.starter</groupId>
            <artifactId>app-connector-spring-boot-starter</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- HTTP Client Spring Boot Starter -->
        <dependency>
            <groupId>cn.com.handthing.springboot3.starter</groupId>
            <artifactId>http-client-spring-boot-starter</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- Spring Boot Starter Web -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!-- Spring Boot Auto Configuration -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-autoconfigure</artifactId>
        </dependency>

        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- Jackson for JSON processing -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>

        <!-- Validation -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <!-- Test Dependencies -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

</project>
