package cn.com.handthing.starter.cache.memory;

import cn.com.handthing.starter.cache.CacheStats;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.cache.Cache;

import java.time.Duration;
import java.util.concurrent.Callable;

import static org.junit.jupiter.api.Assertions.*;

/**
 * MemoryCache 单元测试
 *
 * <AUTHOR>
 * @since V1.0.0
 */
class MemoryCacheTest {

    private MemoryCache cache;

    @BeforeEach
    void setUp() {
        cache = new MemoryCache("testCache", 100, Duration.ofMinutes(5), true);
    }

    @Test
    void testGetName() {
        assertEquals("testCache", cache.getName());
    }

    @Test
    void testGetNativeCache() {
        assertNotNull(cache.getNativeCache());
    }

    @Test
    void testPutAndGet() {
        String key = "testKey";
        String value = "testValue";

        cache.put(key, value);
        Cache.ValueWrapper wrapper = cache.get(key);

        assertNotNull(wrapper);
        assertEquals(value, wrapper.get());
    }

    @Test
    void testGetWithType() {
        String key = "testKey";
        String value = "testValue";

        cache.put(key, value);
        String result = cache.get(key, String.class);

        assertEquals(value, result);
    }

    @Test
    void testGetWithTypeWrongType() {
        String key = "testKey";
        String value = "testValue";

        cache.put(key, value);

        assertThrows(IllegalStateException.class, () -> {
            cache.get(key, Integer.class);
        });
    }

    @Test
    void testGetWithCallable() throws Exception {
        String key = "testKey";
        String value = "testValue";

        Callable<String> valueLoader = () -> value;
        String result = cache.get(key, valueLoader);

        assertEquals(value, result);
        // 验证缓存中已存在
        assertEquals(value, cache.get(key, String.class));
    }

    @Test
    void testPutIfAbsent() {
        String key = "testKey";
        String value1 = "testValue1";
        String value2 = "testValue2";

        // 第一次放入
        Cache.ValueWrapper result1 = cache.putIfAbsent(key, value1);
        assertNull(result1);
        assertEquals(value1, cache.get(key, String.class));

        // 第二次放入，应该返回已存在的值
        Cache.ValueWrapper result2 = cache.putIfAbsent(key, value2);
        assertNotNull(result2);
        assertEquals(value1, result2.get());
        assertEquals(value1, cache.get(key, String.class));
    }

    @Test
    void testEvict() {
        String key = "testKey";
        String value = "testValue";

        cache.put(key, value);
        assertNotNull(cache.get(key));

        cache.evict(key);
        assertNull(cache.get(key));
    }

    @Test
    void testEvictIfPresent() {
        String key = "testKey";
        String value = "testValue";

        // 删除不存在的键
        assertFalse(cache.evictIfPresent(key));

        // 删除存在的键
        cache.put(key, value);
        assertTrue(cache.evictIfPresent(key));
        assertNull(cache.get(key));
    }

    @Test
    void testClear() {
        cache.put("key1", "value1");
        cache.put("key2", "value2");

        assertNotNull(cache.get("key1"));
        assertNotNull(cache.get("key2"));

        cache.clear();

        assertNull(cache.get("key1"));
        assertNull(cache.get("key2"));
    }

    @Test
    void testStats() {
        String key = "testKey";
        String value = "testValue";

        // 初始统计
        CacheStats stats = cache.getStats();
        assertNotNull(stats);
        assertEquals(0, stats.getHitCount());
        assertEquals(0, stats.getMissCount());

        // 缓存未命中
        cache.get(key);
        stats = cache.getStats();
        assertEquals(0, stats.getHitCount());
        assertEquals(1, stats.getMissCount());

        // 缓存命中
        cache.put(key, value);
        cache.get(key);
        stats = cache.getStats();
        assertEquals(1, stats.getHitCount());
        assertEquals(1, stats.getMissCount());
        assertEquals(0.5, stats.getHitRate(), 0.01);
    }

    @Test
    void testMaxSizeLimit() {
        MemoryCache smallCache = new MemoryCache("smallCache", 2, Duration.ofMinutes(5), true);

        smallCache.put("key1", "value1");
        smallCache.put("key2", "value2");
        smallCache.put("key3", "value3"); // 应该触发驱逐

        // 应该只有2个条目
        int existingCount = 0;
        if (smallCache.get("key1") != null) existingCount++;
        if (smallCache.get("key2") != null) existingCount++;
        if (smallCache.get("key3") != null) existingCount++;

        assertEquals(2, existingCount);
    }

    @Test
    void testCleanupExpired() throws InterruptedException {
        MemoryCache shortTtlCache = new MemoryCache("shortTtlCache", 100, Duration.ofMillis(100), true);

        shortTtlCache.put("key1", "value1");
        assertNotNull(shortTtlCache.get("key1"));

        // 等待过期
        Thread.sleep(150);

        // 手动清理
        shortTtlCache.cleanupExpired();

        // 验证已清理
        assertNull(shortTtlCache.get("key1"));
    }
}