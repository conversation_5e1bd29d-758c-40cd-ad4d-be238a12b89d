package cn.com.handthing.starter.connector.feishu.config;

import cn.com.handthing.starter.connector.config.PlatformConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 飞书连接器配置
 * <p>
 * 支持飞书企业应用的配置，包括AppID、AppSecret等认证信息。
 * 支持企业内部应用和第三方应用的不同模式。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ConfigurationProperties(prefix = "handthing.connector.feishu")
public class FeishuConfig extends PlatformConfig {

    /**
     * 飞书应用ID (App ID)
     */
    private String appId;

    /**
     * 飞书应用密钥 (App Secret)
     */
    private String appSecret;

    /**
     * 飞书企业ID (Enterprise ID)
     */
    private String enterpriseId;

    /**
     * 应用类型
     * internal: 企业内部应用
     * third_party: 第三方应用
     */
    private String appType = "internal";

    /**
     * 是否为第三方应用模式
     */
    private boolean thirdPartyMode = false;

    /**
     * 是否启用机器人功能
     */
    private boolean botEnabled = false;

    /**
     * 机器人密钥 (Bot Secret)
     */
    private String botSecret;

    /**
     * 消息加密密钥 (Encrypt Key)
     */
    private String encryptKey;

    /**
     * 消息验证Token (Verification Token)
     */
    private String verificationToken;

    /**
     * 是否启用消息加密
     */
    private boolean encryptMessage = false;

    /**
     * 飞书API基础URL
     */
    private String apiBaseUrl = "https://open.feishu.cn";

    /**
     * 是否使用海外版飞书 (Lark)
     */
    private boolean larkMode = false;

    /**
     * 海外版飞书API基础URL
     */
    private String larkApiBaseUrl = "https://open.larksuite.com";

    /**
     * 获取实际使用的API基础URL
     *
     * @return API基础URL
     */
    public String getEffectiveApiBaseUrl() {
        return larkMode ? larkApiBaseUrl : apiBaseUrl;
    }

    /**
     * 验证配置是否有效
     *
     * @return 如果配置有效返回true，否则返回false
     */
    @Override
    public boolean isValid() {
        if (!super.isValid()) {
            return false;
        }

        // 检查必需的配置项
        if (appId == null || appId.trim().isEmpty()) {
            return false;
        }

        if (appSecret == null || appSecret.trim().isEmpty()) {
            return false;
        }

        return true;
    }

    /**
     * 获取配置描述信息
     *
     * @return 配置描述
     */
    public String getConfigDescription() {
        return String.format("Feishu Config - AppId: %s, AppType: %s, ThirdParty: %s, Bot: %s, Lark: %s",
                appId, appType, thirdPartyMode, botEnabled, larkMode);
    }

    /**
     * 是否为企业内部应用
     *
     * @return 如果是企业内部应用返回true，否则返回false
     */
    public boolean isInternalApp() {
        return "internal".equals(appType);
    }

    /**
     * 是否为第三方应用
     *
     * @return 如果是第三方应用返回true，否则返回false
     */
    public boolean isThirdPartyApp() {
        return "third_party".equals(appType) || thirdPartyMode;
    }

    /**
     * 是否启用了机器人功能
     *
     * @return 如果启用了机器人功能返回true，否则返回false
     */
    public boolean isBotAvailable() {
        return botEnabled && botSecret != null && !botSecret.trim().isEmpty();
    }

    /**
     * 是否启用了消息加密
     *
     * @return 如果启用了消息加密返回true，否则返回false
     */
    public boolean isEncryptionEnabled() {
        return encryptMessage && encryptKey != null && !encryptKey.trim().isEmpty();
    }
}
