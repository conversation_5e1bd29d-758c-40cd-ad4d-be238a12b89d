package cn.com.handthing.starter.httpclient;

import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

/**
 * 请求执行器接口。
 * <p>
 * 这是 Fluent API 的第二步，用于在执行请求前对其进行详细配置，例如添加请求头、设置请求体等。
 * 这是一个链式调用的接口，所有配置方法都返回自身。
 */
public interface RequestExecutor {

    /**
     * 添加一个HTTP请求头。
     *
     * @param name   请求头的名称。
     * @param value  请求头的值。
     * @return 当前请求执行器实例，用于链式调用。
     */
    @NonNull
    RequestExecutor header(@NonNull String name, @NonNull String value);

    /**
     * 设置请求体。
     * <p>
     * 对于 POST 和 PUT 请求，通常需要设置此项。
     * body对象会被合适的 {@code HttpMessageConverter} 序列化。
     *
     * @param body 要作为请求体发送的对象 (可以为 null)。
     * @return 当前请求执行器实例。
     */
    @NonNull
    RequestExecutor body(@Nullable Object body);

    /**
     * 执行HTTP请求，并准备处理响应。
     * <p>
     * 这是请求配置阶段的终止操作，调用此方法后，请求将被发送。
     *
     * @return 一个响应规格处理器 ({@link ResponseSpec})，用于处理服务器返回的响应。
     */
    @NonNull
    ResponseSpec retrieve();

}