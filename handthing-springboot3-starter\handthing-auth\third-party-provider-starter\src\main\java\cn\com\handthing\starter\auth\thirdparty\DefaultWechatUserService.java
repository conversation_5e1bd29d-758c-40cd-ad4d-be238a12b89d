package cn.com.handthing.starter.auth.thirdparty;

import cn.com.handthing.starter.auth.core.UserInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 默认微信用户服务实现
 * <p>
 * 提供基于内存的微信用户服务实现，主要用于测试和演示。
 * 生产环境建议实现自己的WechatUserService来对接实际的用户数据源。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Service
@ConditionalOnMissingBean(WechatUserService.class)
public class DefaultWechatUserService implements WechatUserService {

    /**
     * 用户存储（按用户ID索引）
     */
    private final Map<String, UserInfo> usersById = new ConcurrentHashMap<>();

    /**
     * 微信OpenID用户映射（OpenID -> 用户ID）
     */
    private final Map<String, String> wechatOpenIdMapping = new ConcurrentHashMap<>();

    /**
     * 微信UnionID用户映射（UnionID -> 用户ID）
     */
    private final Map<String, String> wechatUnionIdMapping = new ConcurrentHashMap<>();

    /**
     * 手机号映射（手机号 -> 用户ID）
     */
    private final Map<String, String> mobileMapping = new ConcurrentHashMap<>();

    /**
     * 邮箱映射（邮箱 -> 用户ID）
     */
    private final Map<String, String> emailMapping = new ConcurrentHashMap<>();

    /**
     * 登录失败记录
     */
    private final Map<String, LoginFailureRecord> loginFailures = new ConcurrentHashMap<>();

    /**
     * 用户ID生成器
     */
    private final AtomicLong userIdGenerator = new AtomicLong(4000);

    @Override
    public Optional<UserInfo> findByWechatOpenId(String openId, String appId) {
        String key = buildWechatUserKey(openId, appId);
        String userId = wechatOpenIdMapping.get(key);
        return userId != null ? Optional.ofNullable(usersById.get(userId)) : Optional.empty();
    }

    @Override
    public Optional<UserInfo> findByWechatUnionId(String unionId) {
        String userId = wechatUnionIdMapping.get(unionId);
        return userId != null ? Optional.ofNullable(usersById.get(userId)) : Optional.empty();
    }

    @Override
    public Optional<UserInfo> findById(String userId) {
        return Optional.ofNullable(usersById.get(userId));
    }

    @Override
    public Optional<UserInfo> findByMobile(String mobile) {
        String userId = mobileMapping.get(mobile);
        return userId != null ? Optional.ofNullable(usersById.get(userId)) : Optional.empty();
    }

    @Override
    public Optional<UserInfo> findByEmail(String email) {
        String userId = emailMapping.get(email);
        return userId != null ? Optional.ofNullable(usersById.get(userId)) : Optional.empty();
    }

    @Override
    public UserInfo createUser(WechatApiService.WechatUserResult wechatUserInfo, String appId) {
        // 验证参数
        if (!isValidWechatOpenId(wechatUserInfo.getOpenId())) {
            throw new RuntimeException("Invalid wechat openId: " + wechatUserInfo.getOpenId());
        }

        if (!isValidAppId(appId)) {
            throw new RuntimeException("Invalid app id: " + appId);
        }

        // 检查是否已存在
        String wechatKey = buildWechatUserKey(wechatUserInfo.getOpenId(), appId);
        if (wechatOpenIdMapping.containsKey(wechatKey)) {
            throw new RuntimeException("Wechat user already exists: " + wechatKey);
        }

        // 创建用户
        String userId = String.valueOf(userIdGenerator.incrementAndGet());
        String username = generateDefaultUsername(wechatUserInfo.getOpenId(), appId);
        String nickname = wechatUserInfo.getNickname() != null ? wechatUserInfo.getNickname() : 
                         generateDefaultNickname(wechatUserInfo.getOpenId());

        UserInfo userInfo = UserInfo.builder()
                .userId(userId)
                .username(username)
                .nickname(nickname)
                .avatar(wechatUserInfo.getHeadImgUrl())
                .roles(getDefaultRoles())
                .permissions(getDefaultPermissions())
                .status(1)
                .enabled(true)
                .accountNonExpired(true)
                .accountNonLocked(true)
                .credentialsNonExpired(true)
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .build();

        // 添加微信相关属性
        userInfo.addAttribute("wechat_open_id", wechatUserInfo.getOpenId());
        userInfo.addAttribute("wechat_union_id", wechatUserInfo.getUnionId());
        userInfo.addAttribute("app_id", appId);
        userInfo.addAttribute("wechat_nickname", wechatUserInfo.getNickname());
        userInfo.addAttribute("wechat_sex", wechatUserInfo.getSex());
        userInfo.addAttribute("wechat_sex_desc", getSexDescription(wechatUserInfo.getSex()));
        userInfo.addAttribute("wechat_province", wechatUserInfo.getProvince());
        userInfo.addAttribute("wechat_city", wechatUserInfo.getCity());
        userInfo.addAttribute("wechat_country", wechatUserInfo.getCountry());
        userInfo.addAttribute("wechat_address", buildAddress(wechatUserInfo.getCountry(), 
                wechatUserInfo.getProvince(), wechatUserInfo.getCity()));
        userInfo.addAttribute("wechat_head_img_url", wechatUserInfo.getHeadImgUrl());
        userInfo.addAttribute("wechat_privilege", wechatUserInfo.getPrivilege());

        // 存储用户
        usersById.put(userId, userInfo);
        wechatOpenIdMapping.put(wechatKey, userId);

        // 如果有UnionID，也建立映射
        if (wechatUserInfo.getUnionId() != null && !wechatUserInfo.getUnionId().trim().isEmpty()) {
            wechatUnionIdMapping.put(wechatUserInfo.getUnionId(), userId);
        }

        log.info("Created new wechat user: userId={}, openId={}, appId={}, nickname={}", 
                userId, wechatUserInfo.getOpenId(), appId, wechatUserInfo.getNickname());
        return userInfo;
    }

    @Override
    public boolean bindWechatUser(String userId, String openId, String unionId, String appId) {
        UserInfo userInfo = usersById.get(userId);
        if (userInfo == null) {
            log.warn("User not found for binding: {}", userId);
            return false;
        }

        String wechatKey = buildWechatUserKey(openId, appId);
        if (wechatOpenIdMapping.containsKey(wechatKey)) {
            log.warn("Wechat user already bound: {}", wechatKey);
            return false;
        }

        // 绑定微信用户
        wechatOpenIdMapping.put(wechatKey, userId);
        userInfo.addAttribute("wechat_open_id", openId);
        userInfo.addAttribute("app_id", appId);
        
        if (unionId != null && !unionId.trim().isEmpty()) {
            wechatUnionIdMapping.put(unionId, userId);
            userInfo.addAttribute("wechat_union_id", unionId);
        }
        
        userInfo.setUpdateTime(LocalDateTime.now());

        log.info("Bound wechat user: userId={}, openId={}, unionId={}, appId={}", userId, openId, unionId, appId);
        return true;
    }

    @Override
    public boolean unbindWechatUser(String userId, String appId) {
        UserInfo userInfo = usersById.get(userId);
        if (userInfo == null) {
            return false;
        }

        String openId = userInfo.getAttribute("wechat_open_id", String.class);
        String unionId = userInfo.getAttribute("wechat_union_id", String.class);
        
        if (openId == null) {
            return false;
        }

        String wechatKey = buildWechatUserKey(openId, appId);
        wechatOpenIdMapping.remove(wechatKey);
        
        if (unionId != null) {
            wechatUnionIdMapping.remove(unionId);
        }
        
        userInfo.getAttributes().remove("wechat_open_id");
        userInfo.getAttributes().remove("wechat_union_id");
        userInfo.getAttributes().remove("app_id");
        userInfo.setUpdateTime(LocalDateTime.now());

        log.info("Unbound wechat user: userId={}, openId={}, unionId={}, appId={}", userId, openId, unionId, appId);
        return true;
    }

    @Override
    public boolean isUserValid(UserInfo userInfo) {
        return userInfo != null && Boolean.TRUE.equals(userInfo.getEnabled());
    }

    @Override
    public boolean isUserLocked(UserInfo userInfo) {
        return userInfo != null && Boolean.FALSE.equals(userInfo.getAccountNonLocked());
    }

    @Override
    public boolean isUserDisabled(UserInfo userInfo) {
        return userInfo != null && Boolean.FALSE.equals(userInfo.getEnabled());
    }

    @Override
    public boolean isAccountExpired(UserInfo userInfo) {
        return userInfo != null && Boolean.FALSE.equals(userInfo.getAccountNonExpired());
    }

    @Override
    public void updateLastLoginInfo(String userId, LocalDateTime loginTime, String ipAddress, String userAgent) {
        UserInfo userInfo = usersById.get(userId);
        if (userInfo != null) {
            userInfo.setLastLoginTime(loginTime);
            userInfo.setLastLoginIp(ipAddress);
            userInfo.setUpdateTime(LocalDateTime.now());
            userInfo.addAttribute("lastUserAgent", userAgent);
            log.debug("Updated last login info for user: {}", userId);
        }
    }

    @Override
    public void recordLoginFailure(String openId, String appId, String ipAddress, String reason) {
        String key = buildWechatUserKey(openId, appId);
        LoginFailureRecord record = loginFailures.computeIfAbsent(key, k -> new LoginFailureRecord());
        record.incrementFailureCount();
        record.setLastFailureTime(LocalDateTime.now());
        record.setLastFailureIp(ipAddress);
        record.setLastFailureReason(reason);

        log.debug("Recorded login failure for wechat user: {}, count: {}", key, record.getFailureCount());
    }

    @Override
    public void clearLoginFailures(String openId, String appId) {
        String key = buildWechatUserKey(openId, appId);
        loginFailures.remove(key);
        log.debug("Cleared login failures for wechat user: {}", key);
    }

    @Override
    public int getLoginFailureCount(String openId, String appId) {
        String key = buildWechatUserKey(openId, appId);
        LoginFailureRecord record = loginFailures.get(key);
        return record != null ? record.getFailureCount() : 0;
    }

    @Override
    public void lockUser(String userId, String reason, LocalDateTime lockUntil) {
        UserInfo userInfo = usersById.get(userId);
        if (userInfo != null) {
            userInfo.setAccountNonLocked(false);
            userInfo.setUpdateTime(LocalDateTime.now());
            userInfo.addAttribute("lockReason", reason);
            userInfo.addAttribute("lockUntil", lockUntil);
            log.info("Locked user: {}, reason: {}, until: {}", userId, reason, lockUntil);
        }
    }

    @Override
    public void unlockUser(String userId) {
        UserInfo userInfo = usersById.get(userId);
        if (userInfo != null) {
            userInfo.setAccountNonLocked(true);
            userInfo.setUpdateTime(LocalDateTime.now());
            userInfo.getAttributes().remove("lockReason");
            userInfo.getAttributes().remove("lockUntil");
            log.info("Unlocked user: {}", userId);
        }
    }

    /**
     * 生成默认昵称
     *
     * @param openId 微信OpenID
     * @return 默认昵称
     */
    private String generateDefaultNickname(String openId) {
        return "微信用户" + openId.substring(Math.max(0, openId.length() - 4));
    }

    /**
     * 登录失败记录
     */
    private static class LoginFailureRecord {
        private int failureCount = 0;
        private LocalDateTime lastFailureTime;
        private String lastFailureIp;
        private String lastFailureReason;

        public void incrementFailureCount() {
            this.failureCount++;
        }

        public int getFailureCount() {
            return failureCount;
        }

        public LocalDateTime getLastFailureTime() {
            return lastFailureTime;
        }

        public void setLastFailureTime(LocalDateTime lastFailureTime) {
            this.lastFailureTime = lastFailureTime;
        }

        public String getLastFailureIp() {
            return lastFailureIp;
        }

        public void setLastFailureIp(String lastFailureIp) {
            this.lastFailureIp = lastFailureIp;
        }

        public String getLastFailureReason() {
            return lastFailureReason;
        }

        public void setLastFailureReason(String lastFailureReason) {
            this.lastFailureReason = lastFailureReason;
        }
    }

    /**
     * 获取统计信息
     *
     * @return 统计信息
     */
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new java.util.HashMap<>();
        stats.put("totalUsers", usersById.size());
        stats.put("wechatOpenIdUsers", wechatOpenIdMapping.size());
        stats.put("wechatUnionIdUsers", wechatUnionIdMapping.size());
        stats.put("mobileUsers", mobileMapping.size());
        stats.put("emailUsers", emailMapping.size());
        stats.put("loginFailures", loginFailures.size());
        
        long enabledUsers = usersById.values().stream()
                .filter(user -> Boolean.TRUE.equals(user.getEnabled()))
                .count();
        stats.put("enabledUsers", enabledUsers);
        
        return stats;
    }
}
