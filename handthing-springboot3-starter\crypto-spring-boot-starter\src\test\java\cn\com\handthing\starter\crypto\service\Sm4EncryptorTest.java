package cn.com.handthing.starter.crypto.service;

import cn.com.handthing.starter.crypto.core.KeyProvider;
import cn.com.handthing.starter.crypto.exception.CryptoException;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import javax.crypto.spec.SecretKeySpec;
import java.security.Key;
import java.util.Base64;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;

/**
 * SM4 加密器的单元测试
 * 
 * <AUTHOR>
 * @version 1.0.0-SNAPSHOT
 * @since 2025-07-25
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
@DisplayName("SM4 Encryptor Service Tests")
class Sm4EncryptorTest {

    private static final String BEAN_NAME = "testSm4Encryptor";
    private static final byte[] SM4_KEY_BYTES = new byte[16]; // 128-bit key for SM4
    private static final Key SM4_KEY = new SecretKeySpec(SM4_KEY_BYTES, "SM4");
    private static final String PLAINTEXT = "Hello, HandThing SM4 Crypto!";
    private static final String CHINESE_TEXT = "你好，国密SM4算法测试！";

    @Mock
    private KeyProvider keyProvider;

    private MeterRegistry meterRegistry;
    private Sm4Encryptor sm4Encryptor;

    @BeforeEach
    void setUp() throws Exception {
        meterRegistry = new SimpleMeterRegistry();
        
        // Mock KeyProvider behavior
        when(keyProvider.getKeyBytes()).thenReturn(SM4_KEY_BYTES);
        when(keyProvider.getKey()).thenReturn(SM4_KEY);
        
        sm4Encryptor = new Sm4Encryptor(BEAN_NAME, keyProvider, meterRegistry);
    }

    @Nested
    @DisplayName("基本功能测试")
    class BasicFunctionTests {
        
        @Test
        @DisplayName("应能成功加密和解密英文数据")
        void shouldEncryptAndDecryptEnglishTextSuccessfully() {
            String encryptedText = sm4Encryptor.encrypt(PLAINTEXT);
            assertThat(encryptedText).isNotNull().isNotEqualTo(PLAINTEXT);

            String decryptedText = sm4Encryptor.decrypt(encryptedText);
            assertThat(decryptedText).isEqualTo(PLAINTEXT);
        }
        
        @Test
        @DisplayName("应能成功加密和解密中文数据")
        void shouldEncryptAndDecryptChineseTextSuccessfully() {
            String encryptedText = sm4Encryptor.encrypt(CHINESE_TEXT);
            assertThat(encryptedText).isNotNull().isNotEqualTo(CHINESE_TEXT);

            String decryptedText = sm4Encryptor.decrypt(encryptedText);
            assertThat(decryptedText).isEqualTo(CHINESE_TEXT);
        }

        @Test
        @DisplayName("健康自检应能成功通过")
        void selfCheckShouldPass() {
            // selfCheck 方法不应抛出任何异常
            sm4Encryptor.selfCheck();
        }
    }

    @Nested
    @DisplayName("异常处理测试")
    class ExceptionHandlingTests {
        
        @Test
        @DisplayName("当 KeyProvider 为 null 时构造函数应抛出异常")
        void constructorShouldThrowExceptionForNullKeyProvider() {
            assertThatThrownBy(() -> new Sm4Encryptor(BEAN_NAME, null, meterRegistry))
                    .isInstanceOf(NullPointerException.class);
        }

        @Test
        @DisplayName("当初始化时 KeyProvider 抛出异常应能正确处理")
        void shouldHandleExceptionDuringInitialization() throws Exception {
            doThrow(new RuntimeException("Key loading failed")).when(keyProvider).checkReady();

            assertThatThrownBy(() -> new Sm4Encryptor(BEAN_NAME, keyProvider, meterRegistry))
                    .isInstanceOf(CryptoException.class)
                    .hasMessageContaining("Failed to initialize SM4 key");
        }

        @Test
        @DisplayName("加密空字符串应正常处理")
        void shouldHandleEmptyStringEncryption() {
            String emptyText = "";
            String encrypted = sm4Encryptor.encrypt(emptyText);
            String decrypted = sm4Encryptor.decrypt(encrypted);
            
            assertThat(decrypted).isEqualTo(emptyText);
        }

        @Test
        @DisplayName("解密无效数据应抛出异常")
        void shouldThrowExceptionForInvalidDecryption() {
            String invalidCiphertext = "invalid-base64-data";
            
            assertThatThrownBy(() -> sm4Encryptor.decrypt(invalidCiphertext))
                    .isInstanceOf(CryptoException.class);
        }
    }

    @Nested
    @DisplayName("性能指标测试")
    class MetricsTests {
        
        @Test
        @DisplayName("加密和解密操作应记录性能指标")
        void operationsShouldRecordMetrics() {
            // 执行操作
            String encrypted = sm4Encryptor.encrypt(PLAINTEXT);
            sm4Encryptor.decrypt(encrypted);

            // 验证加密指标
            Timer encryptTimer = meterRegistry.find("crypto.operations.duration")
                    .tag("processor", BEAN_NAME)
                    .tag("operation", "encrypt")
                    .timer();
            assertThat(encryptTimer).isNotNull();
            assertThat(encryptTimer.count()).isEqualTo(1);

            // 验证解密指标
            Timer decryptTimer = meterRegistry.find("crypto.operations.duration")
                    .tag("processor", BEAN_NAME)
                    .tag("operation", "decrypt")
                    .timer();
            assertThat(decryptTimer).isNotNull();
            assertThat(decryptTimer.count()).isEqualTo(1);
        }
    }

    @Nested
    @DisplayName("国密特性测试")
    class GMSpecificTests {
        
        @Test
        @DisplayName("SM4算法标准兼容性测试")
        void testSM4StandardCompliance() {
            // 测试SM4算法是否符合国密标准
            String plaintext = "国密SM4算法标准测试";
            
            String ciphertext = sm4Encryptor.encrypt(plaintext);
            String decrypted = sm4Encryptor.decrypt(ciphertext);
            
            assertThat(decrypted).isEqualTo(plaintext);
            
            // 验证密文格式
            assertThat(ciphertext).isNotNull();
            assertThat(ciphertext.length()).isGreaterThan(0);
            
            // 验证密文是Base64编码
            try {
                Base64.getDecoder().decode(ciphertext);
                // 如果没有抛出异常，说明是有效的Base64编码
            } catch (IllegalArgumentException e) {
                throw new AssertionError("密文不是有效的Base64编码", e);
            }
        }

        @Test
        @DisplayName("SM4密钥长度验证")
        void testSM4KeyLength() {
            // SM4 使用 128 位密钥
            assertThat(SM4_KEY_BYTES.length).isEqualTo(16); // 128 bits = 16 bytes
        }
    }

    @Nested
    @DisplayName("边界条件测试")
    class BoundaryConditionTests {
        
        @Test
        @DisplayName("长文本加解密测试")
        void testLongTextEncryption() {
            StringBuilder longText = new StringBuilder();
            for (int i = 0; i < 1000; i++) {
                longText.append("这是一个很长的中文测试文本，用于验证SM4算法处理长文本的能力。");
            }
            
            String plaintext = longText.toString();
            String encrypted = sm4Encryptor.encrypt(plaintext);
            String decrypted = sm4Encryptor.decrypt(encrypted);
            
            assertThat(decrypted).isEqualTo(plaintext);
        }

        @Test
        @DisplayName("特殊字符加解密测试")
        void testSpecialCharactersEncryption() {
            String specialText = "!@#$%^&*()_+-=[]{}|;':\",./<>?`~测试特殊字符🚀🔐";
            
            String encrypted = sm4Encryptor.encrypt(specialText);
            String decrypted = sm4Encryptor.decrypt(encrypted);
            
            assertThat(decrypted).isEqualTo(specialText);
        }
    }

    @Nested
    @DisplayName("性能测试")
    class PerformanceTests {
        
        @Test
        @DisplayName("批量加解密性能测试")
        void testBatchEncryptionPerformance() {
            String plaintext = "Performance test data for SM4 encryption.";
            
            long startTime = System.currentTimeMillis();
            
            // 执行1000次加解密操作
            for (int i = 0; i < 1000; i++) {
                String ciphertext = sm4Encryptor.encrypt(plaintext);
                String decrypted = sm4Encryptor.decrypt(ciphertext);
                assertThat(decrypted).isEqualTo(plaintext);
            }
            
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            // 性能断言：1000次操作应该在合理时间内完成（比如10秒）
            assertThat(duration).isLessThan(10000);
        }
    }
}
