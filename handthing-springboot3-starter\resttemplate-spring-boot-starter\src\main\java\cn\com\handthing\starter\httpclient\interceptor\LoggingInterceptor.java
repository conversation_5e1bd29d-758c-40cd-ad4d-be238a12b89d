package cn.com.handthing.starter.httpclient.interceptor;

import cn.com.handthing.starter.httpclient.config.LoggingProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;

/**
 * RestTemplate 日志拦截器
 * 
 * <AUTHOR>
 * @since V1.0.0
 */
public class LoggingInterceptor implements ClientHttpRequestInterceptor {

    private static final Logger log = LoggerFactory.getLogger(LoggingInterceptor.class);
    
    private final LoggingProperties loggingProperties;

    public LoggingInterceptor(LoggingProperties loggingProperties) {
        this.loggingProperties = loggingProperties;
    }

    @Override
    public ClientHttpResponse intercept(
            HttpRequest request, 
            byte[] body, 
            ClientHttpRequestExecution execution) throws IOException {
        
        // 记录请求日志
        logRequest(request, body);
        
        long startTime = System.currentTimeMillis();
        
        // 执行请求
        ClientHttpResponse response = execution.execute(request, body);
        
        long endTime = System.currentTimeMillis();
        
        // 包装响应以便记录日志
        BufferingClientHttpResponseWrapper responseWrapper = new BufferingClientHttpResponseWrapper(response);
        
        // 记录响应日志
        logResponse(responseWrapper, endTime - startTime);
        
        return responseWrapper;
    }

    private void logRequest(HttpRequest request, byte[] body) {
        LoggingProperties.LogLevel level = loggingProperties.getLevel();
        
        if (level == LoggingProperties.LogLevel.BASIC || 
            level == LoggingProperties.LogLevel.HEADERS || 
            level == LoggingProperties.LogLevel.FULL) {
            log.info("HTTP Request: {} {}", request.getMethod(), request.getURI());
        }

        if (level == LoggingProperties.LogLevel.HEADERS || level == LoggingProperties.LogLevel.FULL) {
            log.info("Request Headers: {}", request.getHeaders());
        }

        if (level == LoggingProperties.LogLevel.FULL && body != null && body.length > 0) {
            String bodyStr = new String(body, StandardCharsets.UTF_8);
            if (bodyStr.length() > 1024) {
                bodyStr = bodyStr.substring(0, 1024) + "... (truncated)";
            }
            log.info("Request Body: {}", bodyStr);
        }
    }

    private void logResponse(BufferingClientHttpResponseWrapper response, long duration) throws IOException {
        LoggingProperties.LogLevel level = loggingProperties.getLevel();
        
        if (level == LoggingProperties.LogLevel.BASIC || 
            level == LoggingProperties.LogLevel.HEADERS || 
            level == LoggingProperties.LogLevel.FULL) {
            log.info("HTTP Response: {} ({}ms)", response.getStatusCode(), duration);
        }

        if (level == LoggingProperties.LogLevel.HEADERS || level == LoggingProperties.LogLevel.FULL) {
            log.info("Response Headers: {}", response.getHeaders());
        }

        if (level == LoggingProperties.LogLevel.FULL) {
            String bodyStr = response.getBodyAsString();
            if (bodyStr.length() > 1024) {
                bodyStr = bodyStr.substring(0, 1024) + "... (truncated)";
            }
            log.info("Response Body: {}", bodyStr);
        }
    }

    /**
     * 缓冲响应包装器，允许多次读取响应体
     */
    private static class BufferingClientHttpResponseWrapper implements ClientHttpResponse {
        private final ClientHttpResponse response;
        private byte[] body;

        public BufferingClientHttpResponseWrapper(ClientHttpResponse response) {
            this.response = response;
        }

        @Override
        public org.springframework.http.HttpStatusCode getStatusCode() throws IOException {
            return response.getStatusCode();
        }

        @Override
        public String getStatusText() throws IOException {
            return response.getStatusText();
        }

        @Override
        public void close() {
            response.close();
        }

        @Override
        public InputStream getBody() throws IOException {
            if (body == null) {
                body = response.getBody().readAllBytes();
            }
            return new ByteArrayInputStream(body);
        }

        @Override
        public org.springframework.http.HttpHeaders getHeaders() {
            return response.getHeaders();
        }

        public String getBodyAsString() throws IOException {
            if (body == null) {
                body = response.getBody().readAllBytes();
            }
            return new String(body, StandardCharsets.UTF_8);
        }
    }
}
