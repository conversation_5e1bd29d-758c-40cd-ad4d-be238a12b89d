package cn.com.handthing.starter.tenant.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.Arrays;
import java.util.List;

/**
 * 租户解析器配置属性
 * <p>
 * 配置租户解析策略的相关参数，支持子域名、HTTP头、URL路径等多种解析方式。
 * </p>
 *
 * <AUTHOR>
 * @since V3.0.0
 */
@Data
@ConfigurationProperties(prefix = "handthing.auth.tenant.resolver")
public class TenantResolverProperties {

    /**
     * 租户解析策略
     * 可选值：subdomain, header, path, custom
     */
    private String strategy = "header";

    /**
     * 是否启用多解析器
     * 如果为true，将按优先级尝试所有配置的解析器
     */
    private boolean multiResolver = true;

    /**
     * 子域名解析配置
     */
    private SubdomainConfig subdomain = new SubdomainConfig();

    /**
     * HTTP头解析配置
     */
    private HeaderConfig header = new HeaderConfig();

    /**
     * URL路径解析配置
     */
    private PathConfig path = new PathConfig();

    /**
     * 子域名解析配置
     */
    @Data
    public static class SubdomainConfig {

        /**
         * 是否启用子域名解析
         */
        private boolean enabled = false;

        /**
         * 子域名模式
         * 使用{tenant}作为占位符，例如：{tenant}.myapp.com
         */
        private String pattern = "{tenant}.myapp.com";

        /**
         * 基础域名
         * 如果指定，只有匹配此域名的请求才会进行租户解析
         */
        private String baseDomain;

        /**
         * 是否忽略www前缀
         */
        private boolean ignoreWww = true;

        /**
         * 解析器优先级
         */
        private int order = 10;
    }

    /**
     * HTTP头解析配置
     */
    @Data
    public static class HeaderConfig {

        /**
         * 是否启用HTTP头解析
         */
        private boolean enabled = true;

        /**
         * 主要的租户头名称
         */
        private String name = "X-Tenant-ID";

        /**
         * 备选的租户头名称列表
         */
        private List<String> fallbackNames = Arrays.asList("Tenant-ID", "X-Tenant", "Tenant");

        /**
         * 是否区分大小写
         */
        private boolean caseSensitive = false;

        /**
         * 解析器优先级
         */
        private int order = 20;
    }

    /**
     * URL路径解析配置
     */
    @Data
    public static class PathConfig {

        /**
         * 是否启用URL路径解析
         */
        private boolean enabled = false;

        /**
         * 路径模式
         * 使用{tenant}作为占位符，例如：/api/{tenant}/**
         */
        private String pattern = "/api/{tenant}/**";

        /**
         * 路径前缀
         */
        private String prefix = "/api";

        /**
         * 是否移除路径前缀
         * 如果为true，会尝试从请求路径中移除租户前缀
         */
        private boolean removePrefix = false;

        /**
         * 解析器优先级
         */
        private int order = 30;
    }

    /**
     * 获取启用的解析策略列表
     *
     * @return 启用的解析策略列表
     */
    public List<String> getEnabledStrategies() {
        List<String> strategies = new java.util.ArrayList<>();
        
        if (subdomain.enabled) {
            strategies.add("subdomain");
        }
        if (header.enabled) {
            strategies.add("header");
        }
        if (path.enabled) {
            strategies.add("path");
        }
        
        return strategies;
    }

    /**
     * 检查是否启用了任何解析策略
     *
     * @return 如果启用了任何解析策略返回true，否则返回false
     */
    public boolean hasEnabledStrategy() {
        return subdomain.enabled || header.enabled || path.enabled;
    }

    /**
     * 检查指定的策略是否启用
     *
     * @param strategy 策略名称
     * @return 如果启用返回true，否则返回false
     */
    public boolean isStrategyEnabled(String strategy) {
        if (strategy == null) {
            return false;
        }
        
        switch (strategy.toLowerCase()) {
            case "subdomain":
                return subdomain.enabled;
            case "header":
                return header.enabled;
            case "path":
                return path.enabled;
            default:
                return false;
        }
    }

    /**
     * 启用指定的策略
     *
     * @param strategy 策略名称
     */
    public void enableStrategy(String strategy) {
        if (strategy == null) {
            return;
        }
        
        switch (strategy.toLowerCase()) {
            case "subdomain":
                subdomain.enabled = true;
                break;
            case "header":
                header.enabled = true;
                break;
            case "path":
                path.enabled = true;
                break;
        }
    }

    /**
     * 禁用指定的策略
     *
     * @param strategy 策略名称
     */
    public void disableStrategy(String strategy) {
        if (strategy == null) {
            return;
        }
        
        switch (strategy.toLowerCase()) {
            case "subdomain":
                subdomain.enabled = false;
                break;
            case "header":
                header.enabled = false;
                break;
            case "path":
                path.enabled = false;
                break;
        }
    }

    /**
     * 获取配置摘要
     *
     * @return 配置摘要字符串
     */
    public String getConfigSummary() {
        StringBuilder sb = new StringBuilder();
        sb.append("TenantResolverProperties{");
        sb.append("strategy='").append(strategy).append("'");
        sb.append(", multiResolver=").append(multiResolver);
        
        List<String> enabledStrategies = getEnabledStrategies();
        if (!enabledStrategies.isEmpty()) {
            sb.append(", enabled=").append(enabledStrategies);
        }
        
        sb.append("}");
        return sb.toString();
    }

    @Override
    public String toString() {
        return getConfigSummary();
    }
}
