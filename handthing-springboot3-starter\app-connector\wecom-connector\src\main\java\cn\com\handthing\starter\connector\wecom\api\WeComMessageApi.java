package cn.com.handthing.starter.connector.wecom.api;

import cn.com.handthing.starter.connector.exception.ApiCallException;
import cn.com.handthing.starter.connector.wecom.config.WeComConfig;
import cn.com.handthing.starter.connector.wecom.model.WeComMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * 企业微信消息API
 * <p>
 * 提供企业微信消息发送功能，支持发送文本、图片、文件等消息类型，
 * 支持群发和单发。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "handthing.connector.wecom", name = "enabled", havingValue = "true")
public class WeComMessageApi {

    private final WeComConfig config;
    private final RestTemplate restTemplate;

    /**
     * 发送文本消息
     *
     * @param accessToken 访问令牌
     * @param toUser      接收用户ID（多个用户用|分隔，@all表示全部用户）
     * @param content     消息内容
     * @return 发送结果
     * @throws ApiCallException 如果发送失败
     */
    public WeComMessage.SendResult sendTextMessage(String accessToken, String toUser, String content) throws ApiCallException {
        Map<String, Object> message = new HashMap<>();
        message.put("touser", toUser);
        message.put("msgtype", "text");
        message.put("agentid", config.getAgentId());
        
        Map<String, String> text = new HashMap<>();
        text.put("content", content);
        message.put("text", text);
        
        return sendMessage(accessToken, message);
    }

    /**
     * 发送图片消息
     *
     * @param accessToken 访问令牌
     * @param toUser      接收用户ID
     * @param mediaId     图片媒体文件ID
     * @return 发送结果
     * @throws ApiCallException 如果发送失败
     */
    public WeComMessage.SendResult sendImageMessage(String accessToken, String toUser, String mediaId) throws ApiCallException {
        Map<String, Object> message = new HashMap<>();
        message.put("touser", toUser);
        message.put("msgtype", "image");
        message.put("agentid", config.getAgentId());
        
        Map<String, String> image = new HashMap<>();
        image.put("media_id", mediaId);
        message.put("image", image);
        
        return sendMessage(accessToken, message);
    }

    /**
     * 发送文件消息
     *
     * @param accessToken 访问令牌
     * @param toUser      接收用户ID
     * @param mediaId     文件媒体文件ID
     * @return 发送结果
     * @throws ApiCallException 如果发送失败
     */
    public WeComMessage.SendResult sendFileMessage(String accessToken, String toUser, String mediaId) throws ApiCallException {
        Map<String, Object> message = new HashMap<>();
        message.put("touser", toUser);
        message.put("msgtype", "file");
        message.put("agentid", config.getAgentId());
        
        Map<String, String> file = new HashMap<>();
        file.put("media_id", mediaId);
        message.put("file", file);
        
        return sendMessage(accessToken, message);
    }

    /**
     * 发送卡片消息
     *
     * @param accessToken 访问令牌
     * @param toUser      接收用户ID
     * @param title       卡片标题
     * @param description 卡片描述
     * @param url         点击跳转URL
     * @param btnText     按钮文字
     * @return 发送结果
     * @throws ApiCallException 如果发送失败
     */
    public WeComMessage.SendResult sendCardMessage(String accessToken, String toUser, String title, 
                                                  String description, String url, String btnText) throws ApiCallException {
        Map<String, Object> message = new HashMap<>();
        message.put("touser", toUser);
        message.put("msgtype", "textcard");
        message.put("agentid", config.getAgentId());
        
        Map<String, String> textcard = new HashMap<>();
        textcard.put("title", title);
        textcard.put("description", description);
        textcard.put("url", url);
        textcard.put("btntxt", btnText);
        message.put("textcard", textcard);
        
        return sendMessage(accessToken, message);
    }

    /**
     * 发送Markdown消息
     *
     * @param accessToken 访问令牌
     * @param toUser      接收用户ID
     * @param content     Markdown内容
     * @return 发送结果
     * @throws ApiCallException 如果发送失败
     */
    public WeComMessage.SendResult sendMarkdownMessage(String accessToken, String toUser, String content) throws ApiCallException {
        Map<String, Object> message = new HashMap<>();
        message.put("touser", toUser);
        message.put("msgtype", "markdown");
        message.put("agentid", config.getAgentId());
        
        Map<String, String> markdown = new HashMap<>();
        markdown.put("content", content);
        message.put("markdown", markdown);
        
        return sendMessage(accessToken, message);
    }

    /**
     * 发送消息到部门
     *
     * @param accessToken 访问令牌
     * @param toParty     接收部门ID（多个部门用|分隔）
     * @param content     消息内容
     * @return 发送结果
     * @throws ApiCallException 如果发送失败
     */
    public WeComMessage.SendResult sendTextMessageToParty(String accessToken, String toParty, String content) throws ApiCallException {
        Map<String, Object> message = new HashMap<>();
        message.put("toparty", toParty);
        message.put("msgtype", "text");
        message.put("agentid", config.getAgentId());
        
        Map<String, String> text = new HashMap<>();
        text.put("content", content);
        message.put("text", text);
        
        return sendMessage(accessToken, message);
    }

    /**
     * 发送消息到标签
     *
     * @param accessToken 访问令牌
     * @param toTag       接收标签ID（多个标签用|分隔）
     * @param content     消息内容
     * @return 发送结果
     * @throws ApiCallException 如果发送失败
     */
    public WeComMessage.SendResult sendTextMessageToTag(String accessToken, String toTag, String content) throws ApiCallException {
        Map<String, Object> message = new HashMap<>();
        message.put("totag", toTag);
        message.put("msgtype", "text");
        message.put("agentid", config.getAgentId());
        
        Map<String, String> text = new HashMap<>();
        text.put("content", content);
        message.put("text", text);
        
        return sendMessage(accessToken, message);
    }

    /**
     * 发送消息的通用方法
     *
     * @param accessToken 访问令牌
     * @param message     消息内容
     * @return 发送结果
     * @throws ApiCallException 如果发送失败
     */
    private WeComMessage.SendResult sendMessage(String accessToken, Map<String, Object> message) throws ApiCallException {
        try {
            String url = config.getSendMessageUrl() + "?access_token=" + accessToken;
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(message, headers);
            
            ResponseEntity<WeComMessage.SendResult> response = restTemplate.postForEntity(url, request, WeComMessage.SendResult.class);
            WeComMessage.SendResult result = response.getBody();
            
            if (result == null) {
                throw ApiCallException.internalError(cn.com.handthing.starter.connector.PlatformType.WECOM, 
                        new RuntimeException("No response from WeCom API"));
            }
            
            if (!result.isSuccess()) {
                throw new ApiCallException("Failed to send message: " + result.getErrorDescription(),
                        "SEND_MESSAGE_FAILED", cn.com.handthing.starter.connector.PlatformType.WECOM);
            }
            
            log.debug("Successfully sent WeCom message to: {}", message.get("touser"));
            return result;
            
        } catch (Exception e) {
            log.error("Failed to send WeCom message", e);
            if (e instanceof ApiCallException) {
                throw e;
            }
            throw new ApiCallException("Network error occurred", "NETWORK_ERROR",
                    cn.com.handthing.starter.connector.PlatformType.WECOM, null, null, e);
        }
    }
}
