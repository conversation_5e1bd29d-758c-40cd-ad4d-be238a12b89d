{"groups": [{"name": "handthing.api-doc", "type": "cn.com.handthing.starter.apidoc.properties.ApiDocProperties", "sourceType": "cn.com.handthing.starter.apidoc.properties.ApiDocProperties"}, {"name": "handthing.api-doc.auth", "type": "cn.com.handthing.starter.apidoc.properties.ApiDocProperties$Auth", "sourceType": "cn.com.handthing.starter.apidoc.properties.ApiDocProperties", "sourceMethod": "public cn.com.handthing.starter.apidoc.properties.ApiDocProperties.Auth getAuth() "}], "properties": [{"name": "handthing.api-doc.auth.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否为 API 文档页面启用基础认证。", "sourceType": "cn.com.handthing.starter.apidoc.properties.ApiDocProperties$Auth"}, {"name": "handthing.api-doc.auth.password", "type": "java.lang.String", "description": "登录密码。", "sourceType": "cn.com.handthing.starter.apidoc.properties.ApiDocProperties$Auth"}, {"name": "handthing.api-doc.auth.username", "type": "java.lang.String", "description": "登录用户名。", "sourceType": "cn.com.handthing.starter.apidoc.properties.ApiDocProperties$Auth"}, {"name": "handthing.api-doc.description", "type": "java.lang.String", "description": "API 文档描述。", "sourceType": "cn.com.handthing.starter.apidoc.properties.ApiDocProperties"}, {"name": "handthing.api-doc.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用 API 文档功能。 此配置优先级高于 @EnableApiDoc 注解。如果为 false，则文档功能强制关闭。", "sourceType": "cn.com.handthing.starter.apidoc.properties.ApiDocProperties"}, {"name": "handthing.api-doc.group", "type": "java.lang.String", "description": "API 分组名称。", "sourceType": "cn.com.handthing.starter.apidoc.properties.ApiDocProperties"}, {"name": "handthing.api-doc.title", "type": "java.lang.String", "description": "API 文档标题。", "sourceType": "cn.com.handthing.starter.apidoc.properties.ApiDocProperties"}, {"name": "handthing.api-doc.version", "type": "java.lang.String", "description": "API 版本号。", "sourceType": "cn.com.handthing.starter.apidoc.properties.ApiDocProperties"}], "hints": [], "ignored": {"properties": []}}