package cn.com.handthing.test.controller;

import cn.com.handthing.starter.tenant.context.TenantContext;
import cn.com.handthing.starter.tenant.context.TenantContextHolder;
import cn.com.handthing.starter.tenant.service.TenantConfigService;
import cn.com.handthing.starter.tenant.service.TenantConfigStats;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 多租户测试控制器
 * <p>
 * 用于测试和演示多租户功能，包括租户上下文获取、配置管理等。
 * </p>
 *
 * <AUTHOR>
 * @since V3.0.0
 */
@RestController
@RequestMapping("/api/tenant")
public class TenantTestController {

    @Autowired
    private TenantConfigService tenantConfigService;

    /**
     * 获取当前租户信息
     *
     * @return 租户信息
     */
    @GetMapping("/current")
    public Map<String, Object> getCurrentTenant() {
        Map<String, Object> result = new HashMap<>();
        
        // 获取租户上下文
        TenantContext context = TenantContextHolder.getContext();
        if (context != null) {
            result.put("tenantId", context.getTenantId());
            result.put("tenantName", context.getTenantName());
            result.put("tenantStatus", context.getStatus());
            result.put("isActive", context.isActive());
            result.put("attributes", context.getAttributeKeys());
            result.put("contextInfo", context.toString());
        } else {
            result.put("message", "No tenant context found");
        }
        
        // 获取租户ID（兼容方式）
        String tenantId = TenantContextHolder.getTenantId();
        result.put("resolvedTenantId", tenantId);
        result.put("hasTenantId", TenantContextHolder.hasTenantId());
        result.put("contextExists", TenantContextHolder.hasContext());
        
        return result;
    }

    /**
     * 获取租户配置
     *
     * @param configKey 配置键（可选）
     * @return 租户配置
     */
    @GetMapping("/config")
    public Map<String, Object> getTenantConfig(@RequestParam(required = false) String configKey) {
        String tenantId = TenantContextHolder.getTenantIdOrDefault();
        Map<String, Object> result = new HashMap<>();
        result.put("tenantId", tenantId);
        
        if (configKey != null && !configKey.trim().isEmpty()) {
            // 获取指定配置
            String configValue = tenantConfigService.getConfigValue(tenantId, configKey);
            result.put("configKey", configKey);
            result.put("configValue", configValue);
            
            // 尝试获取不同类型的值
            try {
                Integer intValue = tenantConfigService.getConfigValue(tenantId, configKey, Integer.class);
                result.put("intValue", intValue);
            } catch (Exception ignored) {}
            
            try {
                Boolean boolValue = tenantConfigService.getConfigValue(tenantId, configKey, Boolean.class);
                result.put("boolValue", boolValue);
            } catch (Exception ignored) {}
        } else {
            // 获取所有配置
            Map<String, String> configMap = tenantConfigService.getEnabledConfigMap(tenantId);
            result.put("allConfigs", configMap);
            result.put("configCount", configMap.size());
        }
        
        return result;
    }

    /**
     * 设置租户配置
     *
     * @param configKey   配置键
     * @param configValue 配置值
     * @return 操作结果
     */
    @PostMapping("/config")
    public Map<String, Object> setTenantConfig(@RequestParam String configKey, 
                                               @RequestParam String configValue) {
        String tenantId = TenantContextHolder.getTenantIdOrDefault();
        Map<String, Object> result = new HashMap<>();
        
        try {
            tenantConfigService.setConfig(tenantId, configKey, configValue);
            result.put("success", true);
            result.put("message", "Configuration updated successfully");
            result.put("tenantId", tenantId);
            result.put("configKey", configKey);
            result.put("configValue", configValue);
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "Failed to update configuration: " + e.getMessage());
            result.put("error", e.getClass().getSimpleName());
        }
        
        return result;
    }

    /**
     * 获取租户统计信息
     *
     * @return 租户统计信息
     */
    @GetMapping("/stats")
    public Map<String, Object> getTenantStats() {
        String tenantId = TenantContextHolder.getTenantIdOrDefault();
        Map<String, Object> result = new HashMap<>();
        
        try {
            TenantConfigStats stats = tenantConfigService.getConfigStats(tenantId);
            result.put("success", true);
            result.put("tenantId", tenantId);
            result.put("stats", stats);
            
            // 添加额外信息
            result.put("tenantExists", tenantConfigService.tenantExists(tenantId));
            result.put("tenantName", tenantConfigService.getTenantName(tenantId));
            result.put("tenantStatus", tenantConfigService.getTenantStatus(tenantId));
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "Failed to get tenant stats: " + e.getMessage());
            result.put("error", e.getClass().getSimpleName());
        }
        
        return result;
    }

    /**
     * 获取所有租户列表
     *
     * @return 租户列表
     */
    @GetMapping("/list")
    public Map<String, Object> getAllTenants() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            List<String> tenantIds = tenantConfigService.getAllTenantIds();
            result.put("success", true);
            result.put("tenantIds", tenantIds);
            result.put("tenantCount", tenantIds.size());
            
            // 获取每个租户的基本信息
            Map<String, Object> tenantInfos = new HashMap<>();
            for (String tenantId : tenantIds) {
                Map<String, Object> tenantInfo = new HashMap<>();
                tenantInfo.put("name", tenantConfigService.getTenantName(tenantId));
                tenantInfo.put("status", tenantConfigService.getTenantStatus(tenantId));
                tenantInfo.put("exists", tenantConfigService.tenantExists(tenantId));
                tenantInfos.put(tenantId, tenantInfo);
            }
            result.put("tenantInfos", tenantInfos);
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "Failed to get tenant list: " + e.getMessage());
            result.put("error", e.getClass().getSimpleName());
        }
        
        return result;
    }

    /**
     * 测试租户切换
     *
     * @param targetTenantId 目标租户ID
     * @return 切换结果
     */
    @PostMapping("/switch")
    public Map<String, Object> switchTenant(@RequestParam String targetTenantId) {
        Map<String, Object> result = new HashMap<>();
        String originalTenantId = TenantContextHolder.getTenantId();
        
        try {
            // 验证目标租户是否存在
            if (!tenantConfigService.tenantExists(targetTenantId)) {
                result.put("success", false);
                result.put("message", "Target tenant does not exist: " + targetTenantId);
                return result;
            }
            
            // 执行租户切换操作
            TenantContextHolder.runWithTenant(targetTenantId, () -> {
                // 在新租户上下文中执行操作
                String currentTenant = TenantContextHolder.getTenantId();
                String tenantName = tenantConfigService.getTenantName(currentTenant);
                
                result.put("success", true);
                result.put("message", "Tenant switched successfully");
                result.put("originalTenantId", originalTenantId);
                result.put("currentTenantId", currentTenant);
                result.put("currentTenantName", tenantName);
                
                // 获取一些配置作为验证
                String jwtSecret = tenantConfigService.getConfigValue(currentTenant, "jwt.secret");
                result.put("jwtSecretLength", jwtSecret != null ? jwtSecret.length() : 0);
                
                Integer tokenExpiration = tenantConfigService.getConfigValue(currentTenant, "jwt.access.token.expiration", Integer.class);
                result.put("tokenExpiration", tokenExpiration);
            });
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "Failed to switch tenant: " + e.getMessage());
            result.put("error", e.getClass().getSimpleName());
        }
        
        // 验证当前租户是否恢复
        String finalTenantId = TenantContextHolder.getTenantId();
        result.put("finalTenantId", finalTenantId);
        result.put("tenantRestored", originalTenantId != null ? originalTenantId.equals(finalTenantId) : finalTenantId == null);
        
        return result;
    }

    /**
     * 清除租户配置缓存
     *
     * @return 操作结果
     */
    @PostMapping("/cache/clear")
    public Map<String, Object> clearCache() {
        String tenantId = TenantContextHolder.getTenantIdOrDefault();
        Map<String, Object> result = new HashMap<>();
        
        try {
            tenantConfigService.clearCache(tenantId);
            result.put("success", true);
            result.put("message", "Cache cleared successfully");
            result.put("tenantId", tenantId);
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "Failed to clear cache: " + e.getMessage());
            result.put("error", e.getClass().getSimpleName());
        }
        
        return result;
    }

    /**
     * 健康检查
     *
     * @return 健康状态
     */
    @GetMapping("/health")
    public Map<String, Object> health() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String tenantId = TenantContextHolder.getTenantIdOrDefault();
            boolean tenantExists = tenantConfigService.tenantExists(tenantId);
            
            result.put("status", "UP");
            result.put("tenantId", tenantId);
            result.put("tenantExists", tenantExists);
            result.put("contextHolder", "OK");
            result.put("configService", "OK");
            result.put("timestamp", System.currentTimeMillis());
            
            if (tenantExists) {
                String tenantName = tenantConfigService.getTenantName(tenantId);
                result.put("tenantName", tenantName);
            }
            
        } catch (Exception e) {
            result.put("status", "DOWN");
            result.put("error", e.getMessage());
            result.put("exception", e.getClass().getSimpleName());
        }
        
        return result;
    }
}
