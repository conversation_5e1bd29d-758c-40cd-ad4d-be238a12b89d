package cn.com.handthing.starter.crypto.service;

import cn.com.handthing.starter.crypto.core.Digester;
import cn.com.handthing.starter.crypto.exception.CryptoException;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.HexFormat;
import java.util.Objects;

/**
 * 使用 SHA-256 算法的摘要处理器实现。
 *
 * <AUTHOR>
 * @since V1.0.0
 */
public class Sha256Digester extends AbstractCryptoService implements Digester {

    private final String salt;
    private final MeterRegistry meterRegistry;

    public Sha256Digester(String beanName, String salt, MeterRegistry meterRegistry) {
        super(beanName);
        // salt can be null
        this.salt = salt;
        this.meterRegistry = Objects.requireNonNull(meterRegistry, "MeterRegistry must not be null");
    }

    @Override
    public String digest(String data) {
         return Timer.builder("crypto.operations.duration")
                .tag("processor", this.beanName)
                .tag("operation", "digest")
                .register(meterRegistry)
                .record(() -> {
                    try {
                        MessageDigest digest = MessageDigest.getInstance("SHA-256");
                        String dataToDigest = (salt != null) ? data + salt : data;
                        byte[] hash = digest.digest(dataToDigest.getBytes(StandardCharsets.UTF_8));
                        return HexFormat.of().formatHex(hash);
                    } catch (Exception e) {
                        throw new CryptoException("SHA-256 digest failed for processor: " + beanName, e);
                    }
                });
    }

    @Override
    public boolean matches(String data, String digestedData) {
        String newDigest = digest(data);
        return MessageDigest.isEqual(newDigest.getBytes(StandardCharsets.US_ASCII), digestedData.getBytes(StandardCharsets.US_ASCII));
    }

    @Override
    public void selfCheck() {
        // Digesting is a deterministic operation, no special check needed.
    }
}