{"groups": [{"name": "handthing.id", "type": "cn.com.handthing.starter.id.config.IdProperties", "sourceType": "cn.com.handthing.starter.id.config.IdProperties"}, {"name": "handthing.id.snowflake", "type": "cn.com.handthing.starter.id.config.IdProperties$Snowflake", "sourceType": "cn.com.handthing.starter.id.config.IdProperties", "sourceMethod": "public cn.com.handthing.starter.id.config.IdProperties.Snowflake getSnowflake() "}, {"name": "handthing.id.snowflake.pool", "type": "cn.com.handthing.starter.id.config.IdProperties$Snowflake$Pool", "sourceType": "cn.com.handthing.starter.id.config.IdProperties$Snowflake", "sourceMethod": "public cn.com.handthing.starter.id.config.IdProperties.Snowflake.Pool getPool() "}, {"name": "handthing.id.uuid", "type": "cn.com.handthing.starter.id.config.IdProperties$Uuid", "sourceType": "cn.com.handthing.starter.id.config.IdProperties", "sourceMethod": "public cn.com.handthing.starter.id.config.IdProperties.Uuid getUuid() "}], "properties": [{"name": "handthing.id.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用ID生成器", "sourceType": "cn.com.handthing.starter.id.config.IdProperties"}, {"name": "handthing.id.snowflake.datacenter-id", "type": "java.lang.Long", "description": "数据中心ID (0-31)", "sourceType": "cn.com.handthing.starter.id.config.IdProperties$Snowflake"}, {"name": "handthing.id.snowflake.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用雪花算法生成器", "sourceType": "cn.com.handthing.starter.id.config.IdProperties$Snowflake"}, {"name": "handthing.id.snowflake.pool.batch-size", "type": "java.lang.Integer", "description": "每次填充的数量", "sourceType": "cn.com.handthing.starter.id.config.IdProperties$Snowflake$Pool"}, {"name": "handthing.id.snowflake.pool.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用池化", "sourceType": "cn.com.handthing.starter.id.config.IdProperties$Snowflake$Pool"}, {"name": "handthing.id.snowflake.pool.size", "type": "java.lang.Integer", "description": "缓冲池大小", "sourceType": "cn.com.handthing.starter.id.config.IdProperties$Snowflake$Pool"}, {"name": "handthing.id.snowflake.pool.threshold", "type": "java.lang.Integer", "description": "当池中ID少于此值时，触发异步批量填充", "sourceType": "cn.com.handthing.starter.id.config.IdProperties$Snowflake$Pool"}, {"name": "handthing.id.snowflake.worker-id", "type": "java.lang.Long", "description": "机器ID (0-31)", "sourceType": "cn.com.handthing.starter.id.config.IdProperties$Snowflake"}, {"name": "handthing.id.uuid.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用UUID生成器", "sourceType": "cn.com.handthing.starter.id.config.IdProperties$Uuid"}, {"name": "handthing.id.uuid.include-hyphens", "type": "java.lang.Bo<PERSON>an", "description": "是否包含连字符", "sourceType": "cn.com.handthing.starter.id.config.IdProperties$Uuid"}, {"name": "handthing.id.uuid.upper-case", "type": "java.lang.Bo<PERSON>an", "description": "是否转换为大写", "sourceType": "cn.com.handthing.starter.id.config.IdProperties$Uuid"}], "hints": [], "ignored": {"properties": []}}