package cn.com.handthing.starter.cache.impl;

import cn.com.handthing.starter.cache.CacheService;
import cn.com.handthing.starter.cache.exception.CacheOperationException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;

import java.time.Duration;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * DefaultCacheService 单元测试
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@ExtendWith(MockitoExtension.class)
class DefaultCacheServiceTest {

    @Mock
    private CacheManager cacheManager;

    @Mock
    private Cache cache;

    @Mock
    private Cache.ValueWrapper valueWrapper;

    private CacheService cacheService;

    @BeforeEach
    void setUp() {
        cacheService = new DefaultCacheService(cacheManager);
    }

    @Test
    void testGet_Success() {
        // Given
        String cacheName = "testCache";
        String key = "testKey";
        String expectedValue = "testValue";
        
        when(cacheManager.getCache(cacheName)).thenReturn(cache);
        when(cache.get(key)).thenReturn(valueWrapper);
        when(valueWrapper.get()).thenReturn(expectedValue);

        // When
        String result = cacheService.get(cacheName, key);

        // Then
        assertEquals(expectedValue, result);
        verify(cacheManager).getCache(cacheName);
        verify(cache).get(key);
    }

    @Test
    void testGet_NotFound() {
        // Given
        String cacheName = "testCache";
        String key = "testKey";
        
        when(cacheManager.getCache(cacheName)).thenReturn(cache);
        when(cache.get(key)).thenReturn(null);

        // When
        String result = cacheService.get(cacheName, key);

        // Then
        assertNull(result);
    }

    @Test
    void testGet_CacheNotFound() {
        // Given
        String cacheName = "nonExistentCache";
        String key = "testKey";
        
        when(cacheManager.getCache(cacheName)).thenReturn(null);

        // When & Then
        assertThrows(CacheOperationException.class, () -> {
            cacheService.get(cacheName, key);
        });
    }

    @Test
    void testGetWithType_Success() {
        // Given
        String cacheName = "testCache";
        String key = "testKey";
        String expectedValue = "testValue";
        
        when(cacheManager.getCache(cacheName)).thenReturn(cache);
        when(cache.get(key, String.class)).thenReturn(expectedValue);

        // When
        String result = cacheService.get(cacheName, key, String.class);

        // Then
        assertEquals(expectedValue, result);
        verify(cache).get(key, String.class);
    }

    @Test
    void testPut_Success() {
        // Given
        String cacheName = "testCache";
        String key = "testKey";
        String value = "testValue";
        
        when(cacheManager.getCache(cacheName)).thenReturn(cache);

        // When
        cacheService.put(cacheName, key, value);

        // Then
        verify(cache).put(key, value);
    }

    @Test
    void testPutWithTtl_Success() {
        // Given
        String cacheName = "testCache";
        String key = "testKey";
        String value = "testValue";
        Duration ttl = Duration.ofMinutes(5);
        
        when(cacheManager.getCache(cacheName)).thenReturn(cache);

        // When
        cacheService.put(cacheName, key, value, ttl);

        // Then
        // 由于基础实现不支持TTL，应该调用普通的put方法
        verify(cache).put(key, value);
    }

    @Test
    void testEvict_Success() {
        // Given
        String cacheName = "testCache";
        String key = "testKey";
        
        when(cacheManager.getCache(cacheName)).thenReturn(cache);

        // When
        cacheService.evict(cacheName, key);

        // Then
        verify(cache).evict(key);
    }

    @Test
    void testClear_Success() {
        // Given
        String cacheName = "testCache";
        
        when(cacheManager.getCache(cacheName)).thenReturn(cache);

        // When
        cacheService.clear(cacheName);

        // Then
        verify(cache).clear();
    }

    @Test
    void testExists_True() {
        // Given
        String cacheName = "testCache";
        String key = "testKey";
        
        when(cacheManager.getCache(cacheName)).thenReturn(cache);
        when(cache.get(key)).thenReturn(valueWrapper);

        // When
        boolean result = cacheService.exists(cacheName, key);

        // Then
        assertTrue(result);
    }

    @Test
    void testExists_False() {
        // Given
        String cacheName = "testCache";
        String key = "testKey";
        
        when(cacheManager.getCache(cacheName)).thenReturn(cache);
        when(cache.get(key)).thenReturn(null);

        // When
        boolean result = cacheService.exists(cacheName, key);

        // Then
        assertFalse(result);
    }

    @Test
    void testGetStats_ReturnsNull() {
        // Given
        String cacheName = "testCache";

        // When
        var stats = cacheService.getStats(cacheName);

        // Then
        assertNull(stats);
    }
}