package cn.com.handthing.starter.httpclient.exception;

/**
 * 所有在HTTP请求执行期间发生的运行时异常的基类。
 * <p>
 * 这是对更具体异常（如连接超时、读取超时、重试失败）的抽象。
 */
public class HttpRequestException extends HandthingHttpException {

    /**
     * 使用指定的详细消息构造一个新的请求时异常。
     *
     * @param message 详细消息。
     */
    public HttpRequestException(String message) {
        super(message);
    }

    /**
     * 使用指定的详细消息和原因构造一个新的请求时异常。
     *
     * @param message 详细消息。
     * @param cause   原因，通常是底层HTTP客户端抛出的原生异常。
     */
    public HttpRequestException(String message, Throwable cause) {
        super(message, cause);
    }
}