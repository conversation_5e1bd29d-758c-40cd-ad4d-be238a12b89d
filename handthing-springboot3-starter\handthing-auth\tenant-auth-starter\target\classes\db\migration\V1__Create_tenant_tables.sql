-- HandThing多租户认证系统数据库初始化脚本
-- 版本: V3.0.0
-- 作者: HandThing
-- 创建时间: 2025-07-29

-- ========================================
-- 租户配置表
-- ========================================
CREATE TABLE tenant_configs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    tenant_id VARCHAR(64) NOT NULL COMMENT '租户ID',
    config_key VARCHAR(128) NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_type VARCHAR(32) DEFAULT 'STRING' COMMENT '配置类型：STRING, INTEGER, BOOLEAN, JSON等',
    description VARCHAR(255) COMMENT '配置描述',
    enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    sensitive BOOLEAN DEFAULT FALSE COMMENT '是否敏感信息（用于日志脱敏）',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(64) COMMENT '创建者',
    updated_by VARCHAR(64) COMMENT '更新者',
    version BIGINT DEFAULT 0 COMMENT '版本号（乐观锁）'
) COMMENT='租户配置表';

-- 创建唯一索引
CREATE UNIQUE INDEX uk_tenant_config ON tenant_configs (tenant_id, config_key);

-- 创建普通索引
CREATE INDEX idx_tenant_id ON tenant_configs (tenant_id);
CREATE INDEX idx_config_key ON tenant_configs (config_key);
CREATE INDEX idx_enabled ON tenant_configs (enabled);
CREATE INDEX idx_sensitive ON tenant_configs (sensitive);
CREATE INDEX idx_created_at ON tenant_configs (created_at);
CREATE INDEX idx_updated_at ON tenant_configs (updated_at);

-- ========================================
-- 租户信息表（可选）
-- ========================================
CREATE TABLE tenants (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    tenant_id VARCHAR(64) UNIQUE NOT NULL COMMENT '租户ID',
    tenant_name VARCHAR(128) NOT NULL COMMENT '租户名称',
    tenant_code VARCHAR(64) COMMENT '租户代码',
    status VARCHAR(32) DEFAULT 'ACTIVE' COMMENT '租户状态：ACTIVE, DISABLED, SUSPENDED, TRIAL, EXPIRED, MAINTENANCE',
    description TEXT COMMENT '租户描述',
    contact_email VARCHAR(255) COMMENT '联系邮箱',
    contact_phone VARCHAR(32) COMMENT '联系电话',
    contact_person VARCHAR(128) COMMENT '联系人',
    domain VARCHAR(255) COMMENT '租户域名',
    logo_url VARCHAR(512) COMMENT '租户Logo URL',
    theme_config JSON COMMENT '主题配置',
    feature_flags JSON COMMENT '功能开关配置',
    quota_config JSON COMMENT '配额配置',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(64) COMMENT '创建者',
    updated_by VARCHAR(64) COMMENT '更新者',
    version BIGINT DEFAULT 0 COMMENT '版本号（乐观锁）'
) COMMENT='租户信息表';

-- 创建索引
CREATE INDEX idx_tenant_status ON tenants (status);
CREATE INDEX idx_tenant_domain ON tenants (domain);
CREATE INDEX idx_tenant_created_at ON tenants (created_at);

-- ========================================
-- 插入默认数据
-- ========================================

-- 插入默认租户
INSERT INTO tenants (tenant_id, tenant_name, status, description, created_by) VALUES 
('default', '默认租户', 'ACTIVE', '系统默认租户，用于单租户模式或作为后备租户', 'system');

-- 插入默认租户的基础配置
INSERT INTO tenant_configs (tenant_id, config_key, config_value, config_type, description, created_by) VALUES 
-- 基础配置
('default', 'tenant.name', '默认租户', 'STRING', '租户显示名称', 'system'),
('default', 'tenant.status', 'ACTIVE', 'STRING', '租户状态', 'system'),
('default', 'tenant.description', '系统默认租户', 'STRING', '租户描述信息', 'system'),

-- 认证配置
('default', 'auth.default.grant.type', 'password', 'STRING', '默认认证类型', 'system'),
('default', 'auth.multi.provider.enabled', 'true', 'BOOLEAN', '是否启用多认证提供者', 'system'),

-- JWT配置
('default', 'jwt.secret', 'handthing-auth-default-secret-key-2024', 'PASSWORD', 'JWT签名密钥', 'system'),
('default', 'jwt.issuer', 'handthing-auth', 'STRING', 'JWT签发者', 'system'),
('default', 'jwt.audience', 'handthing-app', 'STRING', 'JWT受众', 'system'),
('default', 'jwt.access.token.expiration', '7200', 'INTEGER', '访问令牌过期时间（秒）', 'system'),
('default', 'jwt.refresh.token.expiration', '604800', 'INTEGER', '刷新令牌过期时间（秒）', 'system'),

-- 密码认证配置
('default', 'password.auth.enabled', 'true', 'BOOLEAN', '是否启用密码认证', 'system'),
('default', 'password.min.length', '6', 'INTEGER', '密码最小长度', 'system'),
('default', 'password.max.length', '32', 'INTEGER', '密码最大长度', 'system'),
('default', 'password.require.digit', 'false', 'BOOLEAN', '是否要求密码包含数字', 'system'),
('default', 'password.require.letter', 'false', 'BOOLEAN', '是否要求密码包含字母', 'system'),
('default', 'password.require.special', 'false', 'BOOLEAN', '是否要求密码包含特殊字符', 'system'),

-- 短信认证配置
('default', 'sms.auth.enabled', 'true', 'BOOLEAN', '是否启用短信认证', 'system'),
('default', 'sms.code.length', '6', 'INTEGER', '短信验证码长度', 'system'),
('default', 'sms.code.expiration', '300', 'INTEGER', '短信验证码过期时间（秒）', 'system'),
('default', 'sms.send.interval', '60', 'INTEGER', '短信发送间隔（秒）', 'system'),
('default', 'sms.max.send.count', '10', 'INTEGER', '每日最大发送次数', 'system'),
('default', 'sms.provider', 'default', 'STRING', '短信服务提供商', 'system'),

-- 第三方认证配置（默认禁用）
('default', 'wecom.auth.enabled', 'false', 'BOOLEAN', '是否启用企业微信认证', 'system'),
('default', 'dingtalk.auth.enabled', 'false', 'BOOLEAN', '是否启用钉钉认证', 'system'),
('default', 'wechat.auth.enabled', 'false', 'BOOLEAN', '是否启用微信认证', 'system'),
('default', 'feishu.auth.enabled', 'false', 'BOOLEAN', '是否启用飞书认证', 'system'),

-- 缓存配置
('default', 'cache.enabled', 'true', 'BOOLEAN', '是否启用缓存', 'system'),
('default', 'cache.type', 'caffeine', 'STRING', '缓存类型', 'system'),
('default', 'cache.ttl', '300', 'INTEGER', '缓存生存时间（秒）', 'system'),

-- 安全配置
('default', 'security.max.login.attempts', '5', 'INTEGER', '登录失败最大次数', 'system'),
('default', 'security.lockout.duration', '1800', 'INTEGER', '账户锁定时间（秒）', 'system'),
('default', 'security.ip.whitelist.enabled', 'false', 'BOOLEAN', '是否启用IP白名单', 'system'),
('default', 'security.ip.whitelist', '[]', 'JSON_ARRAY', 'IP白名单列表', 'system');

-- 插入测试租户（用于演示）
INSERT INTO tenants (tenant_id, tenant_name, status, description, domain, created_by) VALUES 
('demo', '演示租户', 'ACTIVE', '用于演示多租户功能的测试租户', 'demo.handthing.com', 'system'),
('test', '测试租户', 'TRIAL', '用于测试的租户，试用状态', 'test.handthing.com', 'system');

-- 为演示租户插入配置（继承默认配置但有所不同）
INSERT INTO tenant_configs (tenant_id, config_key, config_value, config_type, description, created_by) VALUES 
-- 演示租户配置
('demo', 'tenant.name', '演示租户', 'STRING', '租户显示名称', 'system'),
('demo', 'tenant.status', 'ACTIVE', 'STRING', '租户状态', 'system'),
('demo', 'jwt.secret', 'demo-tenant-jwt-secret-key-2024', 'PASSWORD', 'JWT签名密钥', 'system'),
('demo', 'jwt.access.token.expiration', '3600', 'INTEGER', '访问令牌过期时间（秒）', 'system'),
('demo', 'password.min.length', '8', 'INTEGER', '密码最小长度', 'system'),
('demo', 'password.require.digit', 'true', 'BOOLEAN', '是否要求密码包含数字', 'system'),
('demo', 'wecom.auth.enabled', 'true', 'BOOLEAN', '是否启用企业微信认证', 'system'),
('demo', 'wecom.corp.id', 'demo-corp-id', 'STRING', '企业微信企业ID', 'system'),

-- 测试租户配置
('test', 'tenant.name', '测试租户', 'STRING', '租户显示名称', 'system'),
('test', 'tenant.status', 'TRIAL', 'STRING', '租户状态', 'system'),
('test', 'jwt.secret', 'test-tenant-jwt-secret-key-2024', 'PASSWORD', 'JWT签名密钥', 'system'),
('test', 'jwt.access.token.expiration', '1800', 'INTEGER', '访问令牌过期时间（秒）', 'system'),
('test', 'password.min.length', '6', 'INTEGER', '密码最小长度', 'system'),
('test', 'sms.max.send.count', '5', 'INTEGER', '每日最大发送次数', 'system');

-- ========================================
-- 创建视图（可选）
-- ========================================

-- 租户配置视图（包含租户信息）
CREATE VIEW v_tenant_configs AS
SELECT 
    tc.id,
    tc.tenant_id,
    t.tenant_name,
    t.status as tenant_status,
    tc.config_key,
    tc.config_value,
    tc.config_type,
    tc.description,
    tc.enabled,
    tc.sensitive,
    tc.created_at,
    tc.updated_at,
    tc.created_by,
    tc.updated_by,
    tc.version
FROM tenant_configs tc
LEFT JOIN tenants t ON tc.tenant_id = t.tenant_id;

-- 活跃租户配置视图
CREATE VIEW v_active_tenant_configs AS
SELECT * FROM v_tenant_configs 
WHERE enabled = TRUE AND (tenant_status = 'ACTIVE' OR tenant_status = 'TRIAL');

-- ========================================
-- 创建存储过程（可选）
-- ========================================

DELIMITER //

-- 获取租户配置值的存储过程
CREATE PROCEDURE GetTenantConfig(
    IN p_tenant_id VARCHAR(64),
    IN p_config_key VARCHAR(128),
    OUT p_config_value TEXT
)
BEGIN
    DECLARE v_value TEXT DEFAULT NULL;
    
    -- 首先尝试获取租户特定配置
    SELECT config_value INTO v_value
    FROM tenant_configs 
    WHERE tenant_id = p_tenant_id 
      AND config_key = p_config_key 
      AND enabled = TRUE
    LIMIT 1;
    
    -- 如果没有找到，尝试获取默认租户配置
    IF v_value IS NULL THEN
        SELECT config_value INTO v_value
        FROM tenant_configs 
        WHERE tenant_id = 'default' 
          AND config_key = p_config_key 
          AND enabled = TRUE
        LIMIT 1;
    END IF;
    
    SET p_config_value = v_value;
END //

-- 批量复制租户配置的存储过程
CREATE PROCEDURE CopyTenantConfigs(
    IN p_source_tenant_id VARCHAR(64),
    IN p_target_tenant_id VARCHAR(64),
    IN p_overwrite BOOLEAN,
    OUT p_copied_count INT
)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE v_config_key VARCHAR(128);
    DECLARE v_config_value TEXT;
    DECLARE v_config_type VARCHAR(32);
    DECLARE v_description VARCHAR(255);
    DECLARE v_sensitive BOOLEAN;
    DECLARE v_count INT DEFAULT 0;
    
    DECLARE config_cursor CURSOR FOR
        SELECT config_key, config_value, config_type, description, sensitive
        FROM tenant_configs
        WHERE tenant_id = p_source_tenant_id AND enabled = TRUE;
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    OPEN config_cursor;
    
    read_loop: LOOP
        FETCH config_cursor INTO v_config_key, v_config_value, v_config_type, v_description, v_sensitive;
        
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        -- 检查目标租户是否已有此配置
        IF p_overwrite OR NOT EXISTS (
            SELECT 1 FROM tenant_configs 
            WHERE tenant_id = p_target_tenant_id AND config_key = v_config_key
        ) THEN
            INSERT INTO tenant_configs (
                tenant_id, config_key, config_value, config_type, 
                description, enabled, sensitive, created_by
            ) VALUES (
                p_target_tenant_id, v_config_key, v_config_value, v_config_type,
                v_description, TRUE, v_sensitive, 'system'
            ) ON DUPLICATE KEY UPDATE
                config_value = v_config_value,
                config_type = v_config_type,
                description = v_description,
                sensitive = v_sensitive,
                updated_at = CURRENT_TIMESTAMP,
                updated_by = 'system';
            
            SET v_count = v_count + 1;
        END IF;
    END LOOP;
    
    CLOSE config_cursor;
    SET p_copied_count = v_count;
END //

DELIMITER ;

-- ========================================
-- 创建触发器（可选）
-- ========================================

-- 租户配置更新时间触发器
DELIMITER //
CREATE TRIGGER tr_tenant_configs_update_time
    BEFORE UPDATE ON tenant_configs
    FOR EACH ROW
BEGIN
    SET NEW.updated_at = CURRENT_TIMESTAMP;
    SET NEW.version = OLD.version + 1;
END //
DELIMITER ;

-- ========================================
-- 权限设置（根据实际需要调整）
-- ========================================

-- 创建专用数据库用户（示例）
-- CREATE USER 'handthing_auth'@'%' IDENTIFIED BY 'your_password_here';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON tenant_configs TO 'handthing_auth'@'%';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON tenants TO 'handthing_auth'@'%';
-- GRANT SELECT ON v_tenant_configs TO 'handthing_auth'@'%';
-- GRANT SELECT ON v_active_tenant_configs TO 'handthing_auth'@'%';
-- GRANT EXECUTE ON PROCEDURE GetTenantConfig TO 'handthing_auth'@'%';
-- GRANT EXECUTE ON PROCEDURE CopyTenantConfigs TO 'handthing_auth'@'%';
-- FLUSH PRIVILEGES;

-- ========================================
-- 脚本执行完成
-- ========================================
SELECT 'HandThing多租户认证系统数据库初始化完成' AS message;
