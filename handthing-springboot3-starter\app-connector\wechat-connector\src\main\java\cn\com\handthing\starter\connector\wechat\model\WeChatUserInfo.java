package cn.com.handthing.starter.connector.wechat.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 微信用户信息
 * <p>
 * 微信OAuth2.0认证获取用户信息的响应数据模型。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
public class WeChatUserInfo {

    /**
     * 用户唯一标识
     */
    @JsonProperty("openid")
    private String openid;

    /**
     * 用户统一标识
     */
    @JsonProperty("unionid")
    private String unionid;

    /**
     * 用户昵称
     */
    @JsonProperty("nickname")
    private String nickname;

    /**
     * 用户性别，值为1时是男性，值为2时是女性，值为0时是未知
     */
    @JsonProperty("sex")
    private Integer sex;

    /**
     * 用户个人资料填写的省份
     */
    @JsonProperty("province")
    private String province;

    /**
     * 普通用户个人资料填写的城市
     */
    @JsonProperty("city")
    private String city;

    /**
     * 国家，如中国为CN
     */
    @JsonProperty("country")
    private String country;

    /**
     * 用户头像，最后一个数值代表正方形头像大小（有0、46、64、96、132数值可选，0代表640*640正方形头像）
     */
    @JsonProperty("headimgurl")
    private String headimgurl;

    /**
     * 用户特权信息，json 数组，如微信沃卡用户为（chinaunicom）
     */
    @JsonProperty("privilege")
    private List<String> privilege;

    /**
     * 语言
     */
    @JsonProperty("language")
    private String language;

    /**
     * 错误码
     */
    @JsonProperty("errcode")
    private Integer errcode;

    /**
     * 错误信息
     */
    @JsonProperty("errmsg")
    private String errmsg;

    /**
     * 检查响应是否成功
     *
     * @return 如果成功返回true，否则返回false
     */
    public boolean isSuccess() {
        return errcode == null || errcode == 0;
    }

    /**
     * 获取错误描述
     *
     * @return 错误描述
     */
    public String getErrorDescription() {
        if (isSuccess()) {
            return null;
        }
        return String.format("Error %d: %s", errcode, errmsg);
    }

    /**
     * 获取性别描述
     *
     * @return 性别描述
     */
    public String getGenderDescription() {
        if (sex == null) {
            return "未知";
        }
        switch (sex) {
            case 1:
                return "男";
            case 2:
                return "女";
            default:
                return "未知";
        }
    }

    /**
     * 获取完整地址
     *
     * @return 完整地址
     */
    public String getFullAddress() {
        StringBuilder address = new StringBuilder();
        if (country != null && !country.trim().isEmpty()) {
            address.append(country);
        }
        if (province != null && !province.trim().isEmpty()) {
            if (address.length() > 0) {
                address.append(" ");
            }
            address.append(province);
        }
        if (city != null && !city.trim().isEmpty()) {
            if (address.length() > 0) {
                address.append(" ");
            }
            address.append(city);
        }
        return address.toString();
    }
}
