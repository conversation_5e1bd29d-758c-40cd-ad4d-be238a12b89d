package cn.com.handthing.starter.connector.wecom;

import cn.com.handthing.starter.connector.wecom.api.WeComContactApi;
import cn.com.handthing.starter.connector.wecom.api.WeComCustomerApi;
import cn.com.handthing.starter.connector.wecom.api.WeComMessageApi;
import cn.com.handthing.starter.connector.wecom.config.WeComConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

/**
 * 企业微信服务主入口
 * <p>
 * 聚合各个API服务，提供统一的企业微信服务访问入口。
 * 包含消息API、通讯录API、客户联系API等功能模块。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "handthing.connector.wecom", name = "enabled", havingValue = "true")
public class WeComService {

    private final WeComConfig config;
    private final WeComMessageApi messageApi;
    private final WeComContactApi contactApi;
    private final WeComCustomerApi customerApi;

    /**
     * 获取消息API
     *
     * @return 消息API实例
     */
    public WeComMessageApi message() {
        return messageApi;
    }

    /**
     * 获取通讯录API
     *
     * @return 通讯录API实例
     */
    public WeComContactApi contact() {
        return contactApi;
    }

    /**
     * 获取客户联系API
     *
     * @return 客户联系API实例
     */
    public WeComCustomerApi customer() {
        return customerApi;
    }

    /**
     * 获取企业微信配置
     *
     * @return 配置实例
     */
    public WeComConfig getConfig() {
        return config;
    }

    /**
     * 检查服务是否可用
     *
     * @return 如果服务可用返回true，否则返回false
     */
    public boolean isAvailable() {
        return config.isEnabled() && config.isValid();
    }

    /**
     * 获取服务状态信息
     *
     * @return 服务状态信息
     */
    public ServiceStatus getStatus() {
        ServiceStatus status = new ServiceStatus();
        status.setEnabled(config.isEnabled());
        status.setValid(config.isValid());
        status.setCorpId(config.getCorpId());
        status.setAgentId(config.getAgentId());
        status.setThirdPartyMode(config.isThirdPartyMode());
        status.setCallbackEnabled(config.isCallbackEnabled());
        status.setApiBaseUrl(config.getEffectiveApiBaseUrl());
        return status;
    }

    /**
     * 服务状态信息类
     */
    public static class ServiceStatus {
        private boolean enabled;
        private boolean valid;
        private String corpId;
        private String agentId;
        private boolean thirdPartyMode;
        private boolean callbackEnabled;
        private String apiBaseUrl;

        // Getters and Setters
        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public boolean isValid() {
            return valid;
        }

        public void setValid(boolean valid) {
            this.valid = valid;
        }

        public String getCorpId() {
            return corpId;
        }

        public void setCorpId(String corpId) {
            this.corpId = corpId;
        }

        public String getAgentId() {
            return agentId;
        }

        public void setAgentId(String agentId) {
            this.agentId = agentId;
        }

        public boolean isThirdPartyMode() {
            return thirdPartyMode;
        }

        public void setThirdPartyMode(boolean thirdPartyMode) {
            this.thirdPartyMode = thirdPartyMode;
        }

        public boolean isCallbackEnabled() {
            return callbackEnabled;
        }

        public void setCallbackEnabled(boolean callbackEnabled) {
            this.callbackEnabled = callbackEnabled;
        }

        public String getApiBaseUrl() {
            return apiBaseUrl;
        }

        public void setApiBaseUrl(String apiBaseUrl) {
            this.apiBaseUrl = apiBaseUrl;
        }

        @Override
        public String toString() {
            return String.format("WeComServiceStatus{enabled=%s, valid=%s, corpId='%s', agentId='%s', thirdPartyMode=%s, callbackEnabled=%s, apiBaseUrl='%s'}",
                    enabled, valid, corpId, agentId, thirdPartyMode, callbackEnabled, apiBaseUrl);
        }
    }
}
