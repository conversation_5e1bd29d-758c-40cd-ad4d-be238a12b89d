package cn.com.handthing.starter.tenant.context;

/**
 * 租户状态枚举
 * <p>
 * 定义租户的各种状态，用于控制租户的访问权限和功能可用性。
 * </p>
 *
 * <AUTHOR>
 * @since V3.0.0
 */
public enum TenantStatus {

    /**
     * 活跃状态 - 租户正常运行，所有功能可用
     */
    ACTIVE("ACTIVE", "活跃", "租户正常运行，所有功能可用"),

    /**
     * 禁用状态 - 租户被禁用，无法访问任何功能
     */
    DISABLED("DISABLED", "禁用", "租户被禁用，无法访问任何功能"),

    /**
     * 暂停状态 - 租户被暂停，部分功能受限
     */
    SUSPENDED("SUSPENDED", "暂停", "租户被暂停，部分功能受限"),

    /**
     * 试用状态 - 租户处于试用期，功能可能受限
     */
    TRIAL("TRIAL", "试用", "租户处于试用期，功能可能受限"),

    /**
     * 过期状态 - 租户已过期，需要续费
     */
    EXPIRED("EXPIRED", "过期", "租户已过期，需要续费"),

    /**
     * 维护状态 - 租户正在维护，暂时不可用
     */
    MAINTENANCE("MAINTENANCE", "维护", "租户正在维护，暂时不可用");

    /**
     * 状态代码
     */
    private final String code;

    /**
     * 状态名称
     */
    private final String name;

    /**
     * 状态描述
     */
    private final String description;

    TenantStatus(String code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    /**
     * 获取状态代码
     *
     * @return 状态代码
     */
    public String getCode() {
        return code;
    }

    /**
     * 获取状态名称
     *
     * @return 状态名称
     */
    public String getName() {
        return name;
    }

    /**
     * 获取状态描述
     *
     * @return 状态描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * 检查是否为活跃状态
     *
     * @return 如果是活跃状态返回true，否则返回false
     */
    public boolean isActive() {
        return this == ACTIVE;
    }

    /**
     * 检查是否为禁用状态
     *
     * @return 如果是禁用状态返回true，否则返回false
     */
    public boolean isDisabled() {
        return this == DISABLED;
    }

    /**
     * 检查是否为暂停状态
     *
     * @return 如果是暂停状态返回true，否则返回false
     */
    public boolean isSuspended() {
        return this == SUSPENDED;
    }

    /**
     * 检查是否为试用状态
     *
     * @return 如果是试用状态返回true，否则返回false
     */
    public boolean isTrial() {
        return this == TRIAL;
    }

    /**
     * 检查是否为过期状态
     *
     * @return 如果是过期状态返回true，否则返回false
     */
    public boolean isExpired() {
        return this == EXPIRED;
    }

    /**
     * 检查是否为维护状态
     *
     * @return 如果是维护状态返回true，否则返回false
     */
    public boolean isMaintenance() {
        return this == MAINTENANCE;
    }

    /**
     * 检查租户是否可以进行认证
     * <p>
     * 只有活跃状态和试用状态的租户可以进行认证
     * </p>
     *
     * @return 如果可以认证返回true，否则返回false
     */
    public boolean canAuthenticate() {
        return this == ACTIVE || this == TRIAL;
    }

    /**
     * 检查租户是否可以访问功能
     * <p>
     * 活跃状态和试用状态的租户可以访问功能
     * </p>
     *
     * @return 如果可以访问返回true，否则返回false
     */
    public boolean canAccess() {
        return this == ACTIVE || this == TRIAL;
    }

    /**
     * 根据状态代码获取枚举值
     *
     * @param code 状态代码
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static TenantStatus fromCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return null;
        }

        for (TenantStatus status : values()) {
            if (status.code.equalsIgnoreCase(code.trim())) {
                return status;
            }
        }

        return null;
    }

    /**
     * 根据状态名称获取枚举值
     *
     * @param name 状态名称
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static TenantStatus fromName(String name) {
        if (name == null || name.trim().isEmpty()) {
            return null;
        }

        for (TenantStatus status : values()) {
            if (status.name.equalsIgnoreCase(name.trim())) {
                return status;
            }
        }

        return null;
    }

    /**
     * 获取所有可认证的状态
     *
     * @return 可认证的状态数组
     */
    public static TenantStatus[] getAuthenticatableStatuses() {
        return new TenantStatus[]{ACTIVE, TRIAL};
    }

    /**
     * 获取所有可访问的状态
     *
     * @return 可访问的状态数组
     */
    public static TenantStatus[] getAccessibleStatuses() {
        return new TenantStatus[]{ACTIVE, TRIAL};
    }

    @Override
    public String toString() {
        return code;
    }
}
