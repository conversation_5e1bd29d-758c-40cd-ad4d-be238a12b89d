package cn.com.handthing.starter.connector.douyin;

import cn.com.handthing.starter.connector.PlatformType;
import cn.com.handthing.starter.connector.auth.AuthProvider;
import cn.com.handthing.starter.connector.auth.UnifiedAccessToken;
import cn.com.handthing.starter.connector.auth.UnifiedUserInfo;
import cn.com.handthing.starter.connector.douyin.config.DouyinConfig;
import cn.com.handthing.starter.connector.douyin.model.DouyinAccessTokenResponse;
import cn.com.handthing.starter.connector.douyin.model.DouyinUserInfoResponse;
import cn.com.handthing.starter.connector.exception.AuthException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 抖音认证提供者
 * <p>
 * 实现抖音OAuth2.0认证流程，包括授权URL生成、Token交换、用户信息获取等功能。
 * 支持抖音开放平台的网页授权和移动端授权。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "handthing.connector.douyin", name = "enabled", havingValue = "true")
public class DouyinAuthProvider implements AuthProvider {

    private final DouyinConfig config;
    private final RestTemplate restTemplate;

    // 抖音API端点
    private static final String AUTH_URL = "https://open.douyin.com/platform/oauth/connect";
    private static final String TOKEN_URL = "https://open.douyin.com/oauth/access_token";
    private static final String REFRESH_TOKEN_URL = "https://open.douyin.com/oauth/refresh_token";
    private static final String USER_INFO_URL = "https://open.douyin.com/oauth/userinfo";

    @Override
    public PlatformType getPlatformType() {
        return PlatformType.DOUYIN;
    }

    @Override
    public String getAuthorizationUrl(String state, String redirectUri) throws AuthException {
        try {
            String effectiveRedirectUri = config.getEffectiveRedirectUri(redirectUri);
            String effectiveScope = config.getEffectiveScope("user_info");
            
            return UriComponentsBuilder.fromHttpUrl(AUTH_URL)
                    .queryParam("client_key", config.getClientKey())
                    .queryParam("response_type", "code")
                    .queryParam("scope", effectiveScope)
                    .queryParam("redirect_uri", URLEncoder.encode(effectiveRedirectUri, StandardCharsets.UTF_8))
                    .queryParam("state", state != null ? state : "")
                    .build()
                    .toUriString();
                    
        } catch (Exception e) {
            log.error("Failed to generate Douyin authorization URL", e);
            throw AuthException.configurationError(PlatformType.DOUYIN, "Failed to generate authorization URL: " + e.getMessage());
        }
    }

    @Override
    public UnifiedAccessToken exchangeToken(String code, String state, String redirectUri) throws AuthException {
        try {
            // 1. 使用授权码获取访问令牌
            DouyinAccessTokenResponse tokenResponse = getAccessToken(code);
            
            // 2. 构建统一访问令牌
            return UnifiedAccessToken.builder()
                    .platform(PlatformType.DOUYIN)
                    .accessToken(tokenResponse.getAccessToken())
                    .refreshToken(tokenResponse.getRefreshToken())
                    .tokenType("Bearer")
                    .expiresIn(tokenResponse.getExpiresIn())
                    .scope(tokenResponse.getScope())
                    .createdAt(LocalDateTime.now())
                    .expiresAt(LocalDateTime.now().plusSeconds(tokenResponse.getExpiresIn()))
                    .extraInfo(Map.of(
                            "openId", tokenResponse.getOpenId() != null ? tokenResponse.getOpenId() : "",
                            "refreshExpiresIn", tokenResponse.getRefreshExpiresIn() != null ? tokenResponse.getRefreshExpiresIn() : 0L
                    ))
                    .build();
                    
        } catch (Exception e) {
            log.error("Failed to exchange Douyin token for code: {}", code, e);
            if (e instanceof AuthException) {
                throw e;
            }
            throw AuthException.networkError(PlatformType.DOUYIN, e);
        }
    }

    @Override
    public UnifiedAccessToken refreshToken(String refreshToken) throws AuthException {
        try {
            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
            params.add("client_key", config.getClientKey());
            params.add("client_secret", config.getClientSecret());
            params.add("refresh_token", refreshToken);
            params.add("grant_type", "refresh_token");
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            
            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);
            
            ResponseEntity<DouyinAccessTokenResponse> response = restTemplate.postForEntity(
                    REFRESH_TOKEN_URL, request, DouyinAccessTokenResponse.class);
            
            DouyinAccessTokenResponse tokenResponse = response.getBody();
            
            if (tokenResponse == null || !tokenResponse.isSuccess()) {
                throw new AuthException("Failed to refresh token: " + 
                        (tokenResponse != null ? tokenResponse.getErrorDescription() : "No response"),
                        AuthException.REFRESH_TOKEN_EXPIRED, PlatformType.DOUYIN);
            }
            
            return UnifiedAccessToken.builder()
                    .platform(PlatformType.DOUYIN)
                    .accessToken(tokenResponse.getAccessToken())
                    .refreshToken(tokenResponse.getRefreshToken())
                    .tokenType("Bearer")
                    .expiresIn(tokenResponse.getExpiresIn())
                    .scope(tokenResponse.getScope())
                    .createdAt(LocalDateTime.now())
                    .expiresAt(LocalDateTime.now().plusSeconds(tokenResponse.getExpiresIn()))
                    .build();
                    
        } catch (Exception e) {
            log.error("Failed to refresh Douyin token", e);
            if (e instanceof AuthException) {
                throw e;
            }
            throw AuthException.networkError(PlatformType.DOUYIN, e);
        }
    }

    @Override
    public UnifiedUserInfo getUserInfo(String accessToken) throws AuthException {
        try {
            String url = UriComponentsBuilder.fromHttpUrl(USER_INFO_URL)
                    .queryParam("access_token", accessToken)
                    .build()
                    .toUriString();
            
            ResponseEntity<DouyinUserInfoResponse> response = restTemplate.getForEntity(url, DouyinUserInfoResponse.class);
            DouyinUserInfoResponse userInfo = response.getBody();
            
            if (userInfo == null || !userInfo.isSuccess()) {
                throw new AuthException("Failed to get user info: " + 
                        (userInfo != null ? userInfo.getErrorDescription() : "No response"),
                        AuthException.INVALID_TOKEN, PlatformType.DOUYIN);
            }
            
            DouyinUserInfoResponse.UserData userData = userInfo.getData();
            
            return UnifiedUserInfo.builder()
                    .platform(PlatformType.DOUYIN)
                    .userId(userData.getOpenId())
                    .openId(userData.getOpenId())
                    .unionId(userData.getUnionId())
                    .nickname(userData.getNickname())
                    .avatar(userData.getAvatar())
                    .gender(parseGender(userData.getGender()))
                    .country(userData.getCountry())
                    .province(userData.getProvince())
                    .city(userData.getCity())
                    .status("active")
                    .fetchedAt(LocalDateTime.now())
                    .extraInfo(Map.of(
                            "avatarLarger", userData.getAvatarLarger() != null ? userData.getAvatarLarger() : "",
                            "captcha", userData.getCaptcha() != null ? userData.getCaptcha() : "",
                            "eAccountRole", userData.getEAccountRole() != null ? userData.getEAccountRole() : ""
                    ))
                    .build();
                    
        } catch (Exception e) {
            log.error("Failed to get Douyin user info", e);
            if (e instanceof AuthException) {
                throw e;
            }
            throw AuthException.networkError(PlatformType.DOUYIN, e);
        }
    }

    @Override
    public boolean supportsRefreshToken() {
        return true; // 抖音支持刷新令牌
    }

    @Override
    public String getDefaultScope() {
        return "user_info";
    }

    /**
     * 获取访问令牌
     */
    private DouyinAccessTokenResponse getAccessToken(String code) throws AuthException {
        try {
            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
            params.add("client_key", config.getClientKey());
            params.add("client_secret", config.getClientSecret());
            params.add("code", code);
            params.add("grant_type", "authorization_code");
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            
            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);
            
            ResponseEntity<DouyinAccessTokenResponse> response = restTemplate.postForEntity(
                    TOKEN_URL, request, DouyinAccessTokenResponse.class);
            
            DouyinAccessTokenResponse tokenResponse = response.getBody();
            
            if (tokenResponse == null || !tokenResponse.isSuccess()) {
                throw new AuthException("Failed to get access token: " + 
                        (tokenResponse != null ? tokenResponse.getErrorDescription() : "No response"),
                        AuthException.INVALID_CODE, PlatformType.DOUYIN);
            }
            
            return tokenResponse;
            
        } catch (Exception e) {
            log.error("Failed to get Douyin access token for code: {}", code, e);
            throw AuthException.networkError(PlatformType.DOUYIN, e);
        }
    }

    /**
     * 解析性别
     */
    private Integer parseGender(Integer gender) {
        if (gender == null) return 0;
        // 抖音：0-未知，1-男，2-女
        return gender;
    }
}
