package cn.com.handthing.starter.auth.thirdparty;

import java.util.Map;

/**
 * 企业微信API服务接口
 * <p>
 * 定义企业微信API调用的核心接口，包括获取访问令牌、用户信息等。
 * 业务系统可以实现此接口来对接企业微信的具体API。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
public interface WecomApiService {

    /**
     * 获取企业微信访问令牌
     *
     * @param corpId     企业ID
     * @param corpSecret 企业密钥
     * @return 访问令牌结果
     */
    WecomTokenResult getAccessToken(String corpId, String corpSecret);

    /**
     * 根据授权码获取用户信息
     *
     * @param accessToken 访问令牌
     * @param code        授权码
     * @return 用户信息结果
     */
    WecomUserResult getUserInfoByCode(String accessToken, String code);

    /**
     * 根据用户ID获取用户详细信息
     *
     * @param accessToken 访问令牌
     * @param userId      用户ID
     * @return 用户详细信息结果
     */
    WecomUserDetailResult getUserDetail(String accessToken, String userId);

    /**
     * 验证企业微信配置
     *
     * @param corpId     企业ID
     * @param corpSecret 企业密钥
     * @param agentId    应用ID
     * @return 如果配置有效返回true，否则返回false
     */
    boolean validateConfig(String corpId, String corpSecret, String agentId);

    /**
     * 企业微信令牌结果
     */
    class WecomTokenResult {
        private boolean success;
        private String accessToken;
        private Long expiresIn;
        private String errorCode;
        private String errorMessage;

        public WecomTokenResult(boolean success, String accessToken, Long expiresIn) {
            this.success = success;
            this.accessToken = accessToken;
            this.expiresIn = expiresIn;
        }

        public WecomTokenResult(boolean success, String errorCode, String errorMessage) {
            this.success = success;
            this.errorCode = errorCode;
            this.errorMessage = errorMessage;
        }

        public boolean isSuccess() {
            return success;
        }

        public String getAccessToken() {
            return accessToken;
        }

        public Long getExpiresIn() {
            return expiresIn;
        }

        public String getErrorCode() {
            return errorCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public static WecomTokenResult success(String accessToken, Long expiresIn) {
            return new WecomTokenResult(true, accessToken, expiresIn);
        }

        public static WecomTokenResult failure(String errorCode, String errorMessage) {
            return new WecomTokenResult(false, errorCode, errorMessage);
        }

        @Override
        public String toString() {
            return String.format("WecomTokenResult{success=%s, accessToken='%s', expiresIn=%d, errorCode='%s', errorMessage='%s'}",
                    success, accessToken != null ? accessToken.substring(0, Math.min(accessToken.length(), 10)) + "..." : null, 
                    expiresIn, errorCode, errorMessage);
        }
    }

    /**
     * 企业微信用户结果
     */
    class WecomUserResult {
        private boolean success;
        private String userId;
        private String deviceId;
        private String userType;
        private String errorCode;
        private String errorMessage;

        public WecomUserResult(boolean success, String userId, String deviceId, String userType) {
            this.success = success;
            this.userId = userId;
            this.deviceId = deviceId;
            this.userType = userType;
        }

        public WecomUserResult(boolean success, String errorCode, String errorMessage) {
            this.success = success;
            this.errorCode = errorCode;
            this.errorMessage = errorMessage;
        }

        public boolean isSuccess() {
            return success;
        }

        public String getUserId() {
            return userId;
        }

        public String getDeviceId() {
            return deviceId;
        }

        public String getUserType() {
            return userType;
        }

        public String getErrorCode() {
            return errorCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public static WecomUserResult success(String userId, String deviceId, String userType) {
            return new WecomUserResult(true, userId, deviceId, userType);
        }

        public static WecomUserResult failure(String errorCode, String errorMessage) {
            return new WecomUserResult(false, errorCode, errorMessage);
        }

        @Override
        public String toString() {
            return String.format("WecomUserResult{success=%s, userId='%s', deviceId='%s', userType='%s', errorCode='%s', errorMessage='%s'}",
                    success, userId, deviceId, userType, errorCode, errorMessage);
        }
    }

    /**
     * 企业微信用户详细信息结果
     */
    class WecomUserDetailResult {
        private boolean success;
        private String userId;
        private String name;
        private String mobile;
        private String gender;
        private String email;
        private String avatar;
        private String status;
        private String[] department;
        private String position;
        private Map<String, Object> extattr;
        private String errorCode;
        private String errorMessage;

        public WecomUserDetailResult(boolean success) {
            this.success = success;
        }

        public WecomUserDetailResult(boolean success, String errorCode, String errorMessage) {
            this.success = success;
            this.errorCode = errorCode;
            this.errorMessage = errorMessage;
        }

        public boolean isSuccess() {
            return success;
        }

        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getMobile() {
            return mobile;
        }

        public void setMobile(String mobile) {
            this.mobile = mobile;
        }

        public String getGender() {
            return gender;
        }

        public void setGender(String gender) {
            this.gender = gender;
        }

        public String getEmail() {
            return email;
        }

        public void setEmail(String email) {
            this.email = email;
        }

        public String getAvatar() {
            return avatar;
        }

        public void setAvatar(String avatar) {
            this.avatar = avatar;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String[] getDepartment() {
            return department;
        }

        public void setDepartment(String[] department) {
            this.department = department;
        }

        public String getPosition() {
            return position;
        }

        public void setPosition(String position) {
            this.position = position;
        }

        public Map<String, Object> getExtattr() {
            return extattr;
        }

        public void setExtattr(Map<String, Object> extattr) {
            this.extattr = extattr;
        }

        public String getErrorCode() {
            return errorCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public static WecomUserDetailResult success() {
            return new WecomUserDetailResult(true);
        }

        public static WecomUserDetailResult failure(String errorCode, String errorMessage) {
            return new WecomUserDetailResult(false, errorCode, errorMessage);
        }

        @Override
        public String toString() {
            return String.format("WecomUserDetailResult{success=%s, userId='%s', name='%s', mobile='%s', email='%s', errorCode='%s', errorMessage='%s'}",
                    success, userId, name, mobile, email, errorCode, errorMessage);
        }
    }
}
