package cn.com.handthing.starter.auth.thirdparty;

import java.util.Map;

/**
 * 飞书API服务接口
 * <p>
 * 定义飞书API调用的核心接口，包括获取访问令牌、用户信息等。
 * 业务系统可以实现此接口来对接飞书的具体API。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
public interface FeishuApiService {

    /**
     * 获取应用访问令牌
     *
     * @param appId     应用ID
     * @param appSecret 应用密钥
     * @return 应用访问令牌结果
     */
    FeishuAppTokenResult getAppAccessToken(String appId, String appSecret);

    /**
     * 根据授权码获取用户访问令牌
     *
     * @param appAccessToken 应用访问令牌
     * @param code           授权码
     * @return 用户访问令牌结果
     */
    FeishuUserTokenResult getUserAccessToken(String appAccessToken, String code);

    /**
     * 获取用户信息
     *
     * @param userAccessToken 用户访问令牌
     * @return 用户信息结果
     */
    FeishuUserInfoResult getUserInfo(String userAccessToken);

    /**
     * 刷新用户访问令牌
     *
     * @param appAccessToken  应用访问令牌
     * @param refreshToken    刷新令牌
     * @return 用户访问令牌结果
     */
    FeishuUserTokenResult refreshUserAccessToken(String appAccessToken, String refreshToken);

    /**
     * 验证飞书应用配置
     *
     * @param appId     应用ID
     * @param appSecret 应用密钥
     * @return 如果配置有效返回true，否则返回false
     */
    boolean validateConfig(String appId, String appSecret);

    /**
     * 飞书应用令牌结果
     */
    class FeishuAppTokenResult {
        private boolean success;
        private String appAccessToken;
        private Long expire;
        private String tenantKey;
        private String errorCode;
        private String errorMessage;

        public FeishuAppTokenResult(boolean success, String appAccessToken, Long expire, String tenantKey) {
            this.success = success;
            this.appAccessToken = appAccessToken;
            this.expire = expire;
            this.tenantKey = tenantKey;
        }

        public FeishuAppTokenResult(boolean success, String errorCode, String errorMessage) {
            this.success = success;
            this.errorCode = errorCode;
            this.errorMessage = errorMessage;
        }

        public boolean isSuccess() {
            return success;
        }

        public String getAppAccessToken() {
            return appAccessToken;
        }

        public Long getExpire() {
            return expire;
        }

        public String getTenantKey() {
            return tenantKey;
        }

        public String getErrorCode() {
            return errorCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public static FeishuAppTokenResult success(String appAccessToken, Long expire, String tenantKey) {
            return new FeishuAppTokenResult(true, appAccessToken, expire, tenantKey);
        }

        public static FeishuAppTokenResult failure(String errorCode, String errorMessage) {
            return new FeishuAppTokenResult(false, errorCode, errorMessage);
        }

        @Override
        public String toString() {
            return String.format("FeishuAppTokenResult{success=%s, tenantKey='%s', expire=%d, errorCode='%s', errorMessage='%s'}",
                    success, tenantKey, expire, errorCode, errorMessage);
        }
    }

    /**
     * 飞书用户令牌结果
     */
    class FeishuUserTokenResult {
        private boolean success;
        private String accessToken;
        private String refreshToken;
        private Long expiresIn;
        private String tokenType;
        private String scope;
        private String errorCode;
        private String errorMessage;

        public FeishuUserTokenResult(boolean success, String accessToken, String refreshToken, 
                                    Long expiresIn, String tokenType, String scope) {
            this.success = success;
            this.accessToken = accessToken;
            this.refreshToken = refreshToken;
            this.expiresIn = expiresIn;
            this.tokenType = tokenType;
            this.scope = scope;
        }

        public FeishuUserTokenResult(boolean success, String errorCode, String errorMessage) {
            this.success = success;
            this.errorCode = errorCode;
            this.errorMessage = errorMessage;
        }

        public boolean isSuccess() {
            return success;
        }

        public String getAccessToken() {
            return accessToken;
        }

        public String getRefreshToken() {
            return refreshToken;
        }

        public Long getExpiresIn() {
            return expiresIn;
        }

        public String getTokenType() {
            return tokenType;
        }

        public String getScope() {
            return scope;
        }

        public String getErrorCode() {
            return errorCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public static FeishuUserTokenResult success(String accessToken, String refreshToken, 
                                                   Long expiresIn, String tokenType, String scope) {
            return new FeishuUserTokenResult(true, accessToken, refreshToken, expiresIn, tokenType, scope);
        }

        public static FeishuUserTokenResult failure(String errorCode, String errorMessage) {
            return new FeishuUserTokenResult(false, errorCode, errorMessage);
        }

        @Override
        public String toString() {
            return String.format("FeishuUserTokenResult{success=%s, tokenType='%s', scope='%s', expiresIn=%d, errorCode='%s', errorMessage='%s'}",
                    success, tokenType, scope, expiresIn, errorCode, errorMessage);
        }
    }

    /**
     * 飞书用户信息结果
     */
    class FeishuUserInfoResult {
        private boolean success;
        private String userId;
        private String unionId;
        private String openId;
        private String name;
        private String enName;
        private String nickname;
        private String email;
        private String mobile;
        private String avatar;
        private String status;
        private String employeeType;
        private String[] departmentIds;
        private String tenantKey;
        private Map<String, Object> customAttrs;
        private String errorCode;
        private String errorMessage;

        public FeishuUserInfoResult(boolean success) {
            this.success = success;
        }

        public FeishuUserInfoResult(boolean success, String errorCode, String errorMessage) {
            this.success = success;
            this.errorCode = errorCode;
            this.errorMessage = errorMessage;
        }

        public boolean isSuccess() {
            return success;
        }

        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public String getUnionId() {
            return unionId;
        }

        public void setUnionId(String unionId) {
            this.unionId = unionId;
        }

        public String getOpenId() {
            return openId;
        }

        public void setOpenId(String openId) {
            this.openId = openId;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getEnName() {
            return enName;
        }

        public void setEnName(String enName) {
            this.enName = enName;
        }

        public String getNickname() {
            return nickname;
        }

        public void setNickname(String nickname) {
            this.nickname = nickname;
        }

        public String getEmail() {
            return email;
        }

        public void setEmail(String email) {
            this.email = email;
        }

        public String getMobile() {
            return mobile;
        }

        public void setMobile(String mobile) {
            this.mobile = mobile;
        }

        public String getAvatar() {
            return avatar;
        }

        public void setAvatar(String avatar) {
            this.avatar = avatar;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getEmployeeType() {
            return employeeType;
        }

        public void setEmployeeType(String employeeType) {
            this.employeeType = employeeType;
        }

        public String[] getDepartmentIds() {
            return departmentIds;
        }

        public void setDepartmentIds(String[] departmentIds) {
            this.departmentIds = departmentIds;
        }

        public String getTenantKey() {
            return tenantKey;
        }

        public void setTenantKey(String tenantKey) {
            this.tenantKey = tenantKey;
        }

        public Map<String, Object> getCustomAttrs() {
            return customAttrs;
        }

        public void setCustomAttrs(Map<String, Object> customAttrs) {
            this.customAttrs = customAttrs;
        }

        public String getErrorCode() {
            return errorCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public static FeishuUserInfoResult success() {
            return new FeishuUserInfoResult(true);
        }

        public static FeishuUserInfoResult failure(String errorCode, String errorMessage) {
            return new FeishuUserInfoResult(false, errorCode, errorMessage);
        }

        @Override
        public String toString() {
            return String.format("FeishuUserInfoResult{success=%s, userId='%s', unionId='%s', name='%s', email='%s', errorCode='%s', errorMessage='%s'}",
                    success, userId, unionId, name, email, errorCode, errorMessage);
        }
    }
}
