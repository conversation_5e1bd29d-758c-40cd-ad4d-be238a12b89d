package cn.com.handthing.starter.httpclient.filter;

import cn.com.handthing.starter.httpclient.config.CryptoProperties;
import cn.com.handthing.starter.httpclient.crypto.RequestEncryptor;
import cn.com.handthing.starter.httpclient.crypto.ResponseDecryptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpMethod;
import org.springframework.web.reactive.function.client.ClientRequest;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.ExchangeFilterFunction;
import org.springframework.web.reactive.function.client.ExchangeFunction;
import reactor.core.publisher.Mono;

/**
 * WebClient 加密过滤器
 * 
 * <AUTHOR>
 * @since V1.0.0
 */
public class WebClientCryptoFilter implements ExchangeFilterFunction {

    private static final Logger log = LoggerFactory.getLogger(WebClientCryptoFilter.class);
    
    private final CryptoProperties cryptoProperties;
    private final RequestEncryptor<?> requestEncryptor;
    private final ResponseDecryptor responseDecryptor;

    public WebClientCryptoFilter(
            CryptoProperties cryptoProperties,
            RequestEncryptor<?> requestEncryptor,
            ResponseDecryptor responseDecryptor) {
        this.cryptoProperties = cryptoProperties;
        this.requestEncryptor = requestEncryptor;
        this.responseDecryptor = responseDecryptor;
    }

    @Override
    public Mono<ClientResponse> filter(ClientRequest request, ExchangeFunction next) {
        if (!cryptoProperties.isEnabled()) {
            return next.exchange(request);
        }

        // 处理请求加密
        Mono<ClientRequest> encryptedRequestMono = encryptRequest(request);
        
        return encryptedRequestMono
                .flatMap(next::exchange)
                .flatMap(this::decryptResponse);
    }

    private Mono<ClientRequest> encryptRequest(ClientRequest request) {
        // 只对有请求体的方法进行加密
        if (request.method() != HttpMethod.POST && 
            request.method() != HttpMethod.PUT && 
            request.method() != HttpMethod.PATCH) {
            return Mono.just(request);
        }

        if (requestEncryptor == null) {
            return Mono.just(request);
        }

        try {
            // 这里简化处理，实际应用中可能需要更复杂的请求体提取和加密逻辑
            log.debug("Request encryption is enabled but not implemented for WebClient in this example");
            
            // 在实际实现中，这里应该：
            // 1. 提取请求体
            // 2. 加密请求体
            // 3. 重新构建请求
            
            return Mono.just(request);
            
        } catch (Exception e) {
            log.error("Failed to encrypt request", e);
            return Mono.just(request);
        }
    }

    private Mono<ClientResponse> decryptResponse(ClientResponse response) {
        if (responseDecryptor == null) {
            return Mono.just(response);
        }

        try {
            // 检查响应是否需要解密
            String contentType = response.headers().contentType()
                    .map(mediaType -> mediaType.toString())
                    .orElse("");
            
            if (!contentType.contains("application/json") && !contentType.contains("text/")) {
                // 只解密文本类型的响应
                return Mono.just(response);
            }

            log.debug("Response decryption is enabled but not implemented for WebClient in this example");
            
            // 在实际实现中，这里应该：
            // 1. 读取响应体
            // 2. 解密响应体
            // 3. 重新构建响应
            
            return Mono.just(response);
            
        } catch (Exception e) {
            log.error("Failed to decrypt response", e);
            return Mono.just(response);
        }
    }
}
