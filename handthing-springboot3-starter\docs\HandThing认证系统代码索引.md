# HandThing认证系统代码索引

## 📁 项目结构总览

```
handthing-springboot3-starter-cache/
├── handthing-auth/                           # 认证系统根模块
│   ├── auth-core/                           # 认证核心模块
│   ├── auth-spring-boot-starter/           # 认证启动器
│   ├── password-provider-starter/          # 密码认证提供者
│   ├── sms-provider-starter/              # 短信认证提供者
│   └── third-party-provider-starter/      # 第三方认证提供者
├── app-connector/                          # 应用连接器
│   └── app-connector-core/                # 连接器核心
├── cache-spring-boot-starter/             # 缓存启动器
├── caffeine-cache-spring-boot-starter/    # Caffeine缓存启动器
├── handthing-core/                        # 核心工具库
├── starter-parent/                        # 父模块
├── test-app/                             # 测试应用
└── docs/                                 # 文档目录
```

## 🔐 认证系统核心模块

### 📦 auth-core (认证核心)

#### 核心接口
| 类名 | 路径 | 描述 |
|------|------|------|
| `AuthenticationProvider` | `auth-core/src/main/java/.../core/AuthenticationProvider.java` | 认证提供者接口 |
| `AuthenticationRequest` | `auth-core/src/main/java/.../core/AuthenticationRequest.java` | 认证请求基类 |
| `AuthenticationResponse` | `auth-core/src/main/java/.../core/AuthenticationResponse.java` | 认证响应基类 |
| `AuthenticationContext` | `auth-core/src/main/java/.../core/AuthenticationContext.java` | 认证上下文 |
| `UserInfo` | `auth-core/src/main/java/.../core/UserInfo.java` | 用户信息 |
| `JwtClaims` | `auth-core/src/main/java/.../core/JwtClaims.java` | JWT声明 |

#### 枚举类型
| 类名 | 路径 | 描述 |
|------|------|------|
| `GrantType` | `auth-core/src/main/java/.../core/GrantType.java` | 授权类型枚举 |
| `AuthenticationStatus` | `auth-core/src/main/java/.../core/AuthenticationStatus.java` | 认证状态枚举 |

#### 异常类
| 类名 | 路径 | 描述 |
|------|------|------|
| `AuthenticationException` | `auth-core/src/main/java/.../core/AuthenticationException.java` | 认证异常 |
| `TokenExpiredException` | `auth-core/src/main/java/.../core/TokenExpiredException.java` | Token过期异常 |

### 📦 auth-spring-boot-starter (认证启动器)

#### 核心组件
| 类名 | 路径 | 描述 |
|------|------|------|
| `AuthenticationManager` | `auth-spring-boot-starter/src/main/java/.../AuthenticationManager.java` | 认证管理器 |
| `AuthenticationController` | `auth-spring-boot-starter/src/main/java/.../AuthenticationController.java` | 认证控制器 |
| `AuthenticationFilter` | `auth-spring-boot-starter/src/main/java/.../AuthenticationFilter.java` | 认证过滤器 |
| `AuthenticationInterceptor` | `auth-spring-boot-starter/src/main/java/.../AuthenticationInterceptor.java` | 认证拦截器 |
| `ProviderRegistry` | `auth-spring-boot-starter/src/main/java/.../ProviderRegistry.java` | 提供者注册表 |

#### 配置类
| 类名 | 路径 | 描述 |
|------|------|------|
| `AuthAutoConfiguration` | `auth-spring-boot-starter/src/main/java/.../config/AuthAutoConfiguration.java` | 认证自动配置 |
| `AuthProperties` | `auth-spring-boot-starter/src/main/java/.../config/AuthProperties.java` | 认证配置属性 |
| `JwtProperties` | `auth-spring-boot-starter/src/main/java/.../config/JwtProperties.java` | JWT配置属性 |

#### JWT组件
| 类名 | 路径 | 描述 |
|------|------|------|
| `JwtTokenGenerator` | `auth-spring-boot-starter/src/main/java/.../jwt/JwtTokenGenerator.java` | JWT生成器 |
| `JwtTokenValidator` | `auth-spring-boot-starter/src/main/java/.../jwt/JwtTokenValidator.java` | JWT验证器 |
| `JwtTokenParser` | `auth-spring-boot-starter/src/main/java/.../jwt/JwtTokenParser.java` | JWT解析器 |

#### 事件系统
| 类名 | 路径 | 描述 |
|------|------|------|
| `AuthenticationBus` | `auth-spring-boot-starter/src/main/java/.../event/AuthenticationBus.java` | 认证事件总线 |
| `AuthenticationStartedEvent` | `auth-spring-boot-starter/src/main/java/.../event/AuthenticationStartedEvent.java` | 认证开始事件 |
| `AuthenticationSuccessEvent` | `auth-spring-boot-starter/src/main/java/.../event/AuthenticationSuccessEvent.java` | 认证成功事件 |
| `AuthenticationFailedEvent` | `auth-spring-boot-starter/src/main/java/.../event/AuthenticationFailedEvent.java` | 认证失败事件 |
| `AuthenticationCompletedEvent` | `auth-spring-boot-starter/src/main/java/.../event/AuthenticationCompletedEvent.java` | 认证完成事件 |

### 📦 password-provider-starter (密码认证)

#### 核心组件
| 类名 | 路径 | 描述 |
|------|------|------|
| `PasswordAuthenticationProvider` | `password-provider-starter/src/main/java/.../PasswordAuthenticationProvider.java` | 密码认证提供者 |
| `PasswordAuthenticationRequest` | `password-provider-starter/src/main/java/.../PasswordAuthenticationRequest.java` | 密码认证请求 |
| `PasswordUserService` | `password-provider-starter/src/main/java/.../PasswordUserService.java` | 密码用户服务 |

#### 配置类
| 类名 | 路径 | 描述 |
|------|------|------|
| `PasswordAuthAutoConfiguration` | `password-provider-starter/src/main/java/.../config/PasswordAuthAutoConfiguration.java` | 密码认证自动配置 |
| `PasswordAuthProperties` | `password-provider-starter/src/main/java/.../config/PasswordAuthProperties.java` | 密码认证配置 |

### 📦 sms-provider-starter (短信认证)

#### 核心组件
| 类名 | 路径 | 描述 |
|------|------|------|
| `SmsAuthenticationProvider` | `sms-provider-starter/src/main/java/.../SmsAuthenticationProvider.java` | 短信认证提供者 |
| `SmsAuthenticationRequest` | `sms-provider-starter/src/main/java/.../SmsAuthenticationRequest.java` | 短信认证请求 |
| `SmsService` | `sms-provider-starter/src/main/java/.../SmsService.java` | 短信服务接口 |
| `DefaultSmsService` | `sms-provider-starter/src/main/java/.../DefaultSmsService.java` | 默认短信服务 |
| `SmsUserService` | `sms-provider-starter/src/main/java/.../SmsUserService.java` | 短信用户服务 |

#### 配置类
| 类名 | 路径 | 描述 |
|------|------|------|
| `SmsAuthAutoConfiguration` | `sms-provider-starter/src/main/java/.../config/SmsAuthAutoConfiguration.java` | 短信认证自动配置 |
| `SmsAuthProperties` | `sms-provider-starter/src/main/java/.../config/SmsAuthProperties.java` | 短信认证配置 |

### 📦 third-party-provider-starter (第三方认证)

#### 企业微信认证
| 类名 | 路径 | 描述 |
|------|------|------|
| `WecomAuthenticationProvider` | `third-party-provider-starter/src/main/java/.../WecomAuthenticationProvider.java` | 企业微信认证提供者 |
| `WecomAuthenticationRequest` | `third-party-provider-starter/src/main/java/.../WecomAuthenticationRequest.java` | 企业微信认证请求 |
| `WecomApiService` | `third-party-provider-starter/src/main/java/.../WecomApiService.java` | 企业微信API服务 |
| `WecomTokenResult` | `third-party-provider-starter/src/main/java/.../WecomTokenResult.java` | 企业微信Token结果 |
| `WecomUserResult` | `third-party-provider-starter/src/main/java/.../WecomUserResult.java` | 企业微信用户结果 |

#### 钉钉认证
| 类名 | 路径 | 描述 |
|------|------|------|
| `DingtalkAuthenticationProvider` | `third-party-provider-starter/src/main/java/.../DingtalkAuthenticationProvider.java` | 钉钉认证提供者 |
| `DingtalkAuthenticationRequest` | `third-party-provider-starter/src/main/java/.../DingtalkAuthenticationRequest.java` | 钉钉认证请求 |
| `DingtalkApiService` | `third-party-provider-starter/src/main/java/.../DingtalkApiService.java` | 钉钉API服务 |

#### 微信认证
| 类名 | 路径 | 描述 |
|------|------|------|
| `WechatAuthenticationProvider` | `third-party-provider-starter/src/main/java/.../WechatAuthenticationProvider.java` | 微信认证提供者 |
| `WechatAuthenticationRequest` | `third-party-provider-starter/src/main/java/.../WechatAuthenticationRequest.java` | 微信认证请求 |
| `WechatApiService` | `third-party-provider-starter/src/main/java/.../WechatApiService.java` | 微信API服务 |

#### 飞书认证
| 类名 | 路径 | 描述 |
|------|------|------|
| `FeishuAuthenticationProvider` | `third-party-provider-starter/src/main/java/.../FeishuAuthenticationProvider.java` | 飞书认证提供者 |
| `FeishuAuthenticationRequest` | `third-party-provider-starter/src/main/java/.../FeishuAuthenticationRequest.java` | 飞书认证请求 |
| `FeishuApiService` | `third-party-provider-starter/src/main/java/.../FeishuApiService.java` | 飞书API服务 |

#### 配置类
| 类名 | 路径 | 描述 |
|------|------|------|
| `ThirdPartyAuthAutoConfiguration` | `third-party-provider-starter/src/main/java/.../config/ThirdPartyAuthAutoConfiguration.java` | 第三方认证自动配置 |
| `ThirdPartyAuthProperties` | `third-party-provider-starter/src/main/java/.../config/ThirdPartyAuthProperties.java` | 第三方认证配置 |
| `WecomProperties` | `third-party-provider-starter/src/main/java/.../config/WecomProperties.java` | 企业微信配置 |
| `DingtalkProperties` | `third-party-provider-starter/src/main/java/.../config/DingtalkProperties.java` | 钉钉配置 |
| `WechatProperties` | `third-party-provider-starter/src/main/java/.../config/WechatProperties.java` | 微信配置 |
| `FeishuProperties` | `third-party-provider-starter/src/main/java/.../config/FeishuProperties.java` | 飞书配置 |

## 🔗 应用连接器模块

### 📦 app-connector-core

#### 核心接口
| 类名 | 路径 | 描述 |
|------|------|------|
| `AuthProvider` | `app-connector-core/src/main/java/.../auth/AuthProvider.java` | 认证提供者策略接口 |
| `PlatformConnector` | `app-connector-core/src/main/java/.../PlatformConnector.java` | 平台连接器接口 |

#### 枚举类型
| 类名 | 路径 | 描述 |
|------|------|------|
| `PlatformType` | `app-connector-core/src/main/java/.../PlatformType.java` | 平台类型枚举 |

## 💾 缓存系统模块

### 📦 cache-spring-boot-starter

#### 核心组件
| 类名 | 路径 | 描述 |
|------|------|------|
| `CacheService` | `cache-spring-boot-starter/src/main/java/.../CacheService.java` | 缓存服务接口 |
| `CacheManager` | `cache-spring-boot-starter/src/main/java/.../CacheManager.java` | 缓存管理器 |

### 📦 caffeine-cache-spring-boot-starter

#### 核心组件
| 类名 | 路径 | 描述 |
|------|------|------|
| `CaffeineCacheService` | `caffeine-cache-spring-boot-starter/src/main/java/.../CaffeineCacheService.java` | Caffeine缓存服务 |
| `CaffeineCacheAutoConfiguration` | `caffeine-cache-spring-boot-starter/src/main/java/.../config/CaffeineCacheAutoConfiguration.java` | Caffeine缓存自动配置 |

## 🛠️ 核心工具库

### 📦 handthing-core

#### 工具类
| 类名 | 路径 | 描述 |
|------|------|------|
| `JsonUtils` | `handthing-core/src/main/java/.../JsonUtils.java` | JSON工具类 |
| `DateUtils` | `handthing-core/src/main/java/.../DateUtils.java` | 日期工具类 |
| `StringUtils` | `handthing-core/src/main/java/.../StringUtils.java` | 字符串工具类 |
| `CryptoUtils` | `handthing-core/src/main/java/.../CryptoUtils.java` | 加密工具类 |

## 🧪 测试应用

### 📦 test-app

#### 控制器
| 类名 | 路径 | 描述 |
|------|------|------|
| `TestController` | `test-app/src/main/java/.../TestController.java` | 测试控制器 |
| `AppConnectorTestController` | `test-app/src/main/java/.../AppConnectorTestController.java` | 应用连接器测试控制器 |

#### 配置文件
| 文件名 | 路径 | 描述 |
|--------|------|------|
| `application.yml` | `test-app/src/main/resources/application.yml` | 主配置文件 |
| `login.html` | `test-app/src/main/resources/templates/login.html` | 登录页面模板 |
| `index.html` | `test-app/src/main/resources/templates/index.html` | 首页模板 |

## 📋 配置文件索引

### Maven配置
| 文件名 | 路径 | 描述 |
|--------|------|------|
| `pom.xml` | `./pom.xml` | 根模块POM |
| `pom.xml` | `starter-parent/pom.xml` | 父模块POM |
| `pom.xml` | `handthing-auth/pom.xml` | 认证系统POM |
| `pom.xml` | `handthing-auth/auth-core/pom.xml` | 认证核心POM |
| `pom.xml` | `handthing-auth/auth-spring-boot-starter/pom.xml` | 认证启动器POM |

### Spring Boot配置
| 文件名 | 路径 | 描述 |
|--------|------|------|
| `spring.factories` | `auth-spring-boot-starter/src/main/resources/META-INF/spring.factories` | 自动配置声明 |
| `additional-spring-configuration-metadata.json` | `auth-spring-boot-starter/src/main/resources/META-INF/additional-spring-configuration-metadata.json` | 配置元数据 |

## 📚 文档索引

### 主要文档
| 文件名 | 路径 | 描述 |
|--------|------|------|
| `HandThing认证系统完整文档.md` | `docs/HandThing认证系统完整文档.md` | 完整系统文档 |
| `HandThing认证系统故障排除指南.md` | `docs/HandThing认证系统故障排除指南.md` | 故障排除指南 |
| `HandThing认证系统最佳实践指南.md` | `docs/HandThing认证系统最佳实践指南.md` | 最佳实践指南 |
| `HandThing认证系统代码索引.md` | `docs/HandThing认证系统代码索引.md` | 代码索引文档 |
| `README-认证系统.md` | `README-认证系统.md` | 项目README |
| `CHANGELOG-认证系统.md` | `CHANGELOG-认证系统.md` | 变更日志 |

## 🔍 关键API索引

### 认证API端点

#### 认证相关
| 端点 | 方法 | 描述 | 控制器方法 |
|------|------|------|-----------|
| `/auth/login` | POST | 统一登录接口 | `AuthenticationController.login()` |
| `/auth/refresh` | POST | Token刷新 | `AuthenticationController.refreshToken()` |
| `/auth/logout` | POST | 登出 | `AuthenticationController.logout()` |
| `/auth/grant-types` | GET | 获取支持的认证类型 | `AuthenticationController.getSupportedGrantTypes()` |

#### 短信认证
| 端点 | 方法 | 描述 | 控制器方法 |
|------|------|------|-----------|
| `/auth/sms/send` | POST | 发送短信验证码 | `AuthenticationController.sendSmsCode()` |
| `/auth/sms/stats` | GET | 短信统计信息 | `AuthenticationController.getSmsStats()` |

#### 第三方认证
| 端点 | 方法 | 描述 | 控制器方法 |
|------|------|------|-----------|
| `/auth/wecom/auth-url` | GET | 企业微信授权URL | `AuthenticationController.getWecomAuthUrl()` |
| `/auth/dingtalk/auth-url` | GET | 钉钉授权URL | `AuthenticationController.getDingtalkAuthUrl()` |
| `/auth/wechat/auth-url` | GET | 微信授权URL | `AuthenticationController.getWechatAuthUrl()` |
| `/auth/feishu/auth-url` | GET | 飞书授权URL | `AuthenticationController.getFeishuAuthUrl()` |

#### 回调端点
| 端点 | 方法 | 描述 | 控制器方法 |
|------|------|------|-----------|
| `/wecom/callback` | GET | 企业微信回调 | `ThirdPartyCallbackController.wecomCallback()` |
| `/dingtalk/callback` | GET | 钉钉回调 | `ThirdPartyCallbackController.dingtalkCallback()` |
| `/wechat/callback` | GET | 微信回调 | `ThirdPartyCallbackController.wechatCallback()` |
| `/feishu/callback` | GET | 飞书回调 | `ThirdPartyCallbackController.feishuCallback()` |

### 核心方法索引

#### AuthenticationManager核心方法
```java
// 执行认证
public AuthenticationResponse authenticate(AuthenticationRequest request)

// 验证Token
public boolean validateToken(String token)

// 查找认证提供者
private AuthenticationProvider findAuthenticationProvider(AuthenticationRequest request)

// 预处理认证
private void preAuthenticate(AuthenticationContext context)

// 后处理认证
private void postAuthenticate(AuthenticationContext context, AuthenticationResponse response)
```

#### AuthenticationProvider核心方法
```java
// 获取支持的授权类型
GrantType getSupportedGrantType()

// 判断是否支持指定的认证请求
boolean supports(AuthenticationRequest request)

// 执行认证
AuthenticationResponse authenticate(AuthenticationRequest request)

// 验证令牌
boolean validateToken(String token)

// 刷新令牌
AuthenticationResponse refreshToken(String refreshToken)
```

#### JwtTokenGenerator核心方法
```java
// 生成访问令牌
public String generateAccessToken(UserInfo userInfo, JwtClaims claims)

// 生成刷新令牌
public String generateRefreshToken(UserInfo userInfo)

// 解析令牌
public JwtClaims parseToken(String token)

// 验证令牌
public boolean validateToken(String token)
```

## 🎯 设计模式应用

### 策略模式 (Strategy Pattern)
- **应用场景**: 认证提供者选择
- **核心类**: `AuthenticationProvider`
- **实现类**: `PasswordAuthenticationProvider`, `SmsAuthenticationProvider`, `WecomAuthenticationProvider`等

### 工厂模式 (Factory Pattern)
- **应用场景**: 认证请求创建
- **核心方法**: `AuthenticationController.createSpecificAuthenticationRequest()`
- **产品类**: `PasswordAuthenticationRequest`, `SmsAuthenticationRequest`等

### 观察者模式 (Observer Pattern)
- **应用场景**: 认证事件处理
- **核心类**: `AuthenticationBus`
- **事件类**: `AuthenticationStartedEvent`, `AuthenticationSuccessEvent`等

### 模板方法模式 (Template Method Pattern)
- **应用场景**: 认证流程控制
- **核心类**: `AuthenticationManager`
- **模板方法**: `authenticate()`, `preAuthenticate()`, `postAuthenticate()`

### 建造者模式 (Builder Pattern)
- **应用场景**: 认证上下文构建
- **核心类**: `AuthenticationContext`
- **建造者**: `AuthenticationContext.Builder`

## 🔧 配置属性索引

### 认证基础配置
```yaml
handthing.auth.enabled                    # 是否启用认证
handthing.auth.default-grant-type         # 默认认证类型
handthing.auth.multi-provider-enabled     # 是否启用多认证提供者
```

### JWT配置
```yaml
handthing.auth.jwt.secret                 # JWT密钥
handthing.auth.jwt.issuer                 # JWT签发者
handthing.auth.jwt.audience               # JWT受众
handthing.auth.jwt.access-token-expiration # 访问令牌过期时间
handthing.auth.jwt.refresh-token-expiration # 刷新令牌过期时间
```

### 过滤器配置
```yaml
handthing.auth.filter.enabled             # 是否启用认证过滤器
handthing.auth.filter.order               # 过滤器顺序
handthing.auth.filter.exclude-paths       # 排除路径列表
```

### 缓存配置
```yaml
handthing.auth.cache.enabled              # 是否启用缓存
handthing.auth.cache.cache-name           # 缓存名称
handthing.auth.cache.type                 # 缓存类型(redis/caffeine)
```

### 短信认证配置
```yaml
handthing.auth.sms.enabled                # 是否启用短信认证
handthing.auth.sms.code-length            # 验证码长度
handthing.auth.sms.code-expiration        # 验证码过期时间
handthing.auth.sms.max-send-count         # 最大发送次数
handthing.auth.sms.send-interval          # 发送间隔
```

### 第三方认证配置
```yaml
# 企业微信
handthing.auth.thirdparty.wecom.enabled
handthing.auth.thirdparty.wecom.corp-id
handthing.auth.thirdparty.wecom.agent-id
handthing.auth.thirdparty.wecom.corp-secret

# 钉钉
handthing.auth.thirdparty.dingtalk.enabled
handthing.auth.thirdparty.dingtalk.app-key
handthing.auth.thirdparty.dingtalk.app-secret
handthing.auth.thirdparty.dingtalk.corp-id

# 微信
handthing.auth.thirdparty.wechat.enabled
handthing.auth.thirdparty.wechat.app-id
handthing.auth.thirdparty.wechat.app-secret

# 飞书
handthing.auth.thirdparty.feishu.enabled
handthing.auth.thirdparty.feishu.app-id
handthing.auth.thirdparty.feishu.app-secret
```

## 📊 监控指标索引

### 认证指标
| 指标名称 | 类型 | 描述 |
|----------|------|------|
| `handthing.auth.success` | Counter | 认证成功次数 |
| `handthing.auth.failure` | Counter | 认证失败次数 |
| `handthing.auth.duration` | Timer | 认证耗时 |
| `handthing.auth.active.sessions` | Gauge | 活跃会话数 |

### 缓存指标
| 指标名称 | 类型 | 描述 |
|----------|------|------|
| `handthing.cache.hits` | Counter | 缓存命中次数 |
| `handthing.cache.misses` | Counter | 缓存未命中次数 |
| `handthing.cache.evictions` | Counter | 缓存驱逐次数 |

### 健康检查端点
| 端点 | 描述 |
|------|------|
| `/actuator/health` | 整体健康状态 |
| `/actuator/health/auth` | 认证系统健康状态 |
| `/actuator/metrics` | 所有指标 |
| `/actuator/metrics/handthing.auth.success` | 认证成功指标 |

---

**© 2024 HandThing. All rights reserved.**
