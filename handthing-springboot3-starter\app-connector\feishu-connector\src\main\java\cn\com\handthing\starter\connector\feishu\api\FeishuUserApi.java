package cn.com.handthing.starter.connector.feishu.api;

import cn.com.handthing.starter.connector.feishu.config.FeishuConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.HashMap;
import java.util.Map;

/**
 * 飞书用户API
 * <p>
 * 提供飞书用户管理相关的API功能，包括用户信息获取、通讯录管理等。
 * 支持企业内部应用的用户管理功能。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "handthing.connector.feishu", name = "enabled", havingValue = "true")
public class FeishuUserApi {

    private final FeishuConfig config;
    private final RestTemplate restTemplate;

    /**
     * 获取用户信息
     *
     * @param accessToken 访问令牌
     * @param userId      用户ID
     * @param userIdType  用户ID类型（open_id、user_id、union_id等）
     * @return 用户信息
     */
    public Map<String, Object> getUserInfo(String accessToken, String userId, String userIdType) {
        try {
            log.debug("Getting Feishu user info for userId: {}", userId);

            String url = UriComponentsBuilder.fromHttpUrl(config.getEffectiveApiBaseUrl() + "/open-apis/contact/v3/users/" + userId)
                    .queryParam("user_id_type", userIdType != null ? userIdType : "open_id")
                    .build()
                    .toUriString();

            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", "Bearer " + accessToken);

            HttpEntity<Void> request = new HttpEntity<>(headers);

            Map<String, Object> response = restTemplate.exchange(url, HttpMethod.GET, request, Map.class).getBody();
            log.debug("Feishu user info retrieved, response: {}", response);

            return response;

        } catch (Exception e) {
            log.error("Failed to get Feishu user info", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", -1);
            errorResponse.put("msg", "Failed to get user info: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 获取用户列表
     *
     * @param accessToken 访问令牌
     * @param departmentId 部门ID，获取该部门下的用户列表
     * @param pageSize    分页大小
     * @param pageToken   分页标记
     * @return 用户列表
     */
    public Map<String, Object> getUserList(String accessToken, String departmentId, Integer pageSize, String pageToken) {
        try {
            log.debug("Getting Feishu user list for department: {}", departmentId);

            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(config.getEffectiveApiBaseUrl() + "/open-apis/contact/v3/users")
                    .queryParam("user_id_type", "open_id")
                    .queryParam("department_id_type", "open_department_id");

            if (departmentId != null && !departmentId.trim().isEmpty()) {
                builder.queryParam("department_id", departmentId);
            }
            if (pageSize != null && pageSize > 0) {
                builder.queryParam("page_size", pageSize);
            }
            if (pageToken != null && !pageToken.trim().isEmpty()) {
                builder.queryParam("page_token", pageToken);
            }

            String url = builder.build().toUriString();

            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", "Bearer " + accessToken);

            HttpEntity<Void> request = new HttpEntity<>(headers);

            Map<String, Object> response = restTemplate.exchange(url, HttpMethod.GET, request, Map.class).getBody();
            log.debug("Feishu user list retrieved, response: {}", response);

            return response;

        } catch (Exception e) {
            log.error("Failed to get Feishu user list", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", -1);
            errorResponse.put("msg", "Failed to get user list: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 获取部门信息
     *
     * @param accessToken 访问令牌
     * @param departmentId 部门ID
     * @return 部门信息
     */
    public Map<String, Object> getDepartmentInfo(String accessToken, String departmentId) {
        try {
            log.debug("Getting Feishu department info for departmentId: {}", departmentId);

            String url = UriComponentsBuilder.fromHttpUrl(config.getEffectiveApiBaseUrl() + "/open-apis/contact/v3/departments/" + departmentId)
                    .queryParam("department_id_type", "open_department_id")
                    .build()
                    .toUriString();

            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", "Bearer " + accessToken);

            HttpEntity<Void> request = new HttpEntity<>(headers);

            Map<String, Object> response = restTemplate.exchange(url, HttpMethod.GET, request, Map.class).getBody();
            log.debug("Feishu department info retrieved, response: {}", response);

            return response;

        } catch (Exception e) {
            log.error("Failed to get Feishu department info", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", -1);
            errorResponse.put("msg", "Failed to get department info: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 获取部门列表
     *
     * @param accessToken 访问令牌
     * @param parentDepartmentId 父部门ID，获取该部门下的子部门列表
     * @param fetchChild  是否递归获取所有子部门
     * @param pageSize    分页大小
     * @param pageToken   分页标记
     * @return 部门列表
     */
    public Map<String, Object> getDepartmentList(String accessToken, String parentDepartmentId, Boolean fetchChild, Integer pageSize, String pageToken) {
        try {
            log.debug("Getting Feishu department list for parent: {}", parentDepartmentId);

            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(config.getEffectiveApiBaseUrl() + "/open-apis/contact/v3/departments")
                    .queryParam("department_id_type", "open_department_id");

            if (parentDepartmentId != null && !parentDepartmentId.trim().isEmpty()) {
                builder.queryParam("parent_department_id", parentDepartmentId);
            }
            if (fetchChild != null) {
                builder.queryParam("fetch_child", fetchChild);
            }
            if (pageSize != null && pageSize > 0) {
                builder.queryParam("page_size", pageSize);
            }
            if (pageToken != null && !pageToken.trim().isEmpty()) {
                builder.queryParam("page_token", pageToken);
            }

            String url = builder.build().toUriString();

            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", "Bearer " + accessToken);

            HttpEntity<Void> request = new HttpEntity<>(headers);

            Map<String, Object> response = restTemplate.exchange(url, HttpMethod.GET, request, Map.class).getBody();
            log.debug("Feishu department list retrieved, response: {}", response);

            return response;

        } catch (Exception e) {
            log.error("Failed to get Feishu department list", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", -1);
            errorResponse.put("msg", "Failed to get department list: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 搜索用户
     *
     * @param accessToken 访问令牌
     * @param query       搜索关键词
     * @param pageSize    分页大小
     * @param pageToken   分页标记
     * @return 搜索结果
     */
    public Map<String, Object> searchUsers(String accessToken, String query, Integer pageSize, String pageToken) {
        try {
            log.debug("Searching Feishu users with query: {}", query);

            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(config.getEffectiveApiBaseUrl() + "/open-apis/contact/v3/users/find_by_department")
                    .queryParam("user_id_type", "open_id")
                    .queryParam("department_id_type", "open_department_id");

            if (query != null && !query.trim().isEmpty()) {
                builder.queryParam("query", query);
            }
            if (pageSize != null && pageSize > 0) {
                builder.queryParam("page_size", pageSize);
            }
            if (pageToken != null && !pageToken.trim().isEmpty()) {
                builder.queryParam("page_token", pageToken);
            }

            String url = builder.build().toUriString();

            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", "Bearer " + accessToken);

            HttpEntity<Void> request = new HttpEntity<>(headers);

            Map<String, Object> response = restTemplate.exchange(url, HttpMethod.GET, request, Map.class).getBody();
            log.debug("Feishu user search completed, response: {}", response);

            return response;

        } catch (Exception e) {
            log.error("Failed to search Feishu users", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", -1);
            errorResponse.put("msg", "Failed to search users: " + e.getMessage());
            return errorResponse;
        }
    }
}
