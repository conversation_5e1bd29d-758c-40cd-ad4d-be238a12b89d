package cn.com.handthing.starter.connector.alipay.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Map;

/**
 * 支付宝小程序模型
 * <p>
 * 支付宝小程序相关的数据结构
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class AlipayMiniProgram {

    /**
     * 模板消息
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TemplateMessage {

        /**
         * 接收用户ID
         */
        @JsonProperty("to_user_id")
        private String toUserId;

        /**
         * 模板ID
         */
        @JsonProperty("template_id")
        private String templateId;

        /**
         * 跳转页面
         */
        @JsonProperty("page")
        private String page;

        /**
         * 模板数据
         */
        @JsonProperty("data")
        private Map<String, TemplateData> data;

        /**
         * 模板数据项
         */
        @Data
        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class TemplateData {

            /**
             * 数据值
             */
            @JsonProperty("value")
            private String value;

            /**
             * 数据颜色
             */
            @JsonProperty("color")
            private String color;
        }
    }

    /**
     * 模板消息发送结果
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TemplateMessageResult {

        /**
         * 消息ID
         */
        @JsonProperty("msg_id")
        private String msgId;

        /**
         * 检查发送是否成功
         *
         * @return 如果发送成功返回true，否则返回false
         */
        public boolean isSuccess() {
            return msgId != null && !msgId.trim().isEmpty();
        }
    }

    /**
     * 二维码请求
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class QrCodeRequest {

        /**
         * 页面路径
         */
        @JsonProperty("page")
        private String page;

        /**
         * 页面参数
         */
        @JsonProperty("query")
        private String query;

        /**
         * 二维码宽度
         */
        @JsonProperty("width")
        private Integer width;

        /**
         * 是否自动配置线条颜色
         */
        @JsonProperty("auto_color")
        private Boolean autoColor;

        /**
         * 线条颜色
         */
        @JsonProperty("line_color")
        private LineColor lineColor;

        /**
         * 线条颜色
         */
        @Data
        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class LineColor {

            /**
             * 红色值
             */
            @JsonProperty("r")
            private Integer r;

            /**
             * 绿色值
             */
            @JsonProperty("g")
            private Integer g;

            /**
             * 蓝色值
             */
            @JsonProperty("b")
            private Integer b;
        }
    }

    /**
     * 二维码生成结果
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class QrCodeResult {

        /**
         * 二维码图片Base64
         */
        @JsonProperty("qr_code_image")
        private String qrCodeImage;

        /**
         * 二维码URL
         */
        @JsonProperty("qr_code_url")
        private String qrCodeUrl;

        /**
         * 检查生成是否成功
         *
         * @return 如果生成成功返回true，否则返回false
         */
        public boolean isSuccess() {
            return (qrCodeImage != null && !qrCodeImage.trim().isEmpty()) ||
                   (qrCodeUrl != null && !qrCodeUrl.trim().isEmpty());
        }
    }

    /**
     * 小程序基础信息
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class AppInfo {

        /**
         * 小程序ID
         */
        @JsonProperty("app_id")
        private String appId;

        /**
         * 小程序名称
         */
        @JsonProperty("app_name")
        private String appName;

        /**
         * 小程序描述
         */
        @JsonProperty("app_desc")
        private String appDesc;

        /**
         * 小程序图标
         */
        @JsonProperty("app_logo")
        private String appLogo;

        /**
         * 小程序类目
         */
        @JsonProperty("app_category")
        private String appCategory;

        /**
         * 小程序状态
         */
        @JsonProperty("app_status")
        private String appStatus;

        /**
         * 获取状态描述
         *
         * @return 状态描述
         */
        public String getAppStatusDescription() {
            if (appStatus == null) {
                return "未知";
            }
            switch (appStatus) {
                case "ONLINE":
                    return "已上线";
                case "OFFLINE":
                    return "已下线";
                case "AUDITING":
                    return "审核中";
                case "AUDIT_REJECT":
                    return "审核拒绝";
                default:
                    return appStatus;
            }
        }
    }

    /**
     * 数据分析请求
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class AnalysisRequest {

        /**
         * 日期类型
         */
        @JsonProperty("date_type")
        private String dateType;

        /**
         * 开始日期
         */
        @JsonProperty("start_date")
        private String startDate;

        /**
         * 结束日期
         */
        @JsonProperty("end_date")
        private String endDate;

        /**
         * 分析维度
         */
        @JsonProperty("dimension")
        private String dimension;
    }

    /**
     * 数据分析结果
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class AnalysisResult {

        /**
         * 访问用户数
         */
        @JsonProperty("visit_uv")
        private Long visitUv;

        /**
         * 访问次数
         */
        @JsonProperty("visit_pv")
        private Long visitPv;

        /**
         * 新用户数
         */
        @JsonProperty("new_user_count")
        private Long newUserCount;

        /**
         * 人均访问页面数
         */
        @JsonProperty("avg_visit_depth")
        private Double avgVisitDepth;

        /**
         * 平均访问时长
         */
        @JsonProperty("avg_visit_time")
        private Double avgVisitTime;

        /**
         * 获取用户活跃度
         *
         * @return 用户活跃度（百分比）
         */
        public Double getUserActivityRate() {
            if (visitUv == null || visitUv == 0 || newUserCount == null) {
                return 0.0;
            }
            return (newUserCount.doubleValue() / visitUv.doubleValue()) * 100;
        }

        /**
         * 获取页面访问深度
         *
         * @return 页面访问深度
         */
        public String getVisitDepthDescription() {
            if (avgVisitDepth == null) {
                return "无数据";
            }
            if (avgVisitDepth < 2) {
                return "较浅";
            } else if (avgVisitDepth < 5) {
                return "中等";
            } else {
                return "较深";
            }
        }
    }
}
