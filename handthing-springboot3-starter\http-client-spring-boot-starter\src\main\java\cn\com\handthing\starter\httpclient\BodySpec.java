package cn.com.handthing.starter.httpclient;

import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import reactor.core.publisher.Mono;

/**
 * 响应体规格处理器接口。
 * <p>
 * 这是 Fluent API 的最后一步，提供两种终止操作来消费响应体。
 * 用户必须根据其应用程序的线程模型（阻塞式或响应式）选择合适的终止方法。
 *
 * @param <T> 响应体的泛型类型。
 */
public interface BodySpec<T> {

    /**
     * <b>以阻塞方式获取响应体。</b>
     * <p>
     * 此方法会阻塞当前线程，直到响应体完全接收并解码完成。
     * 它是为传统的、同步的业务场景设计的 (例如，在Spring MVC的Controller中)。
     * <p>
     * <b>警告:</b> 绝对不要在响应式环境的非阻塞线程（如Netty的EventLoop线程）上调用此方法，
     * 否则会导致线程阻塞，严重影响性能，并可能引发 {@link IllegalStateException}。
     *
     * @return 解码后的响应体对象，如果响应体为空，则可能为null。
     */
    @Nullable
    T block();

    /**
     * <b>以非阻塞方式（响应式）获取响应体。</b>
     * <p>
     * 此方法立即返回一个 {@link Mono} 发布者，它将在未来某个时刻发出响应体数据。
     * 这是为响应式编程设计的，适用于与WebFlux等框架集成。
     * 你可以通过订阅 (subscribe) 这个 Mono 或将其作为响应式链的一部分来处理结果。
     *
     * @return 一个将发出单个响应体对象的 {@code Mono}。如果响应体为空，它将发出一个完成信号 (onComplete)。
     */
    @NonNull
    Mono<T> mono();
}