package cn.com.handthing.starter.tenant.context;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 租户上下文持有者
 * <p>
 * 基于ThreadLocal的租户上下文持有者，用于在整个请求生命周期中持有已识别的tenant_id。
 * 类似于Spring Security的SecurityContextHolder，提供线程安全的租户信息存储。
 * </p>
 *
 * <AUTHOR>
 * @since V3.0.0
 */
public final class TenantContextHolder {

    private static final Logger log = LoggerFactory.getLogger(TenantContextHolder.class);

    /**
     * 租户上下文的ThreadLocal存储
     */
    private static final ThreadLocal<TenantContext> CONTEXT_HOLDER = new ThreadLocal<>();

    /**
     * 默认租户ID
     */
    private static final String DEFAULT_TENANT_ID = "default";

    private TenantContextHolder() {
        // 工具类，禁止实例化
    }

    /**
     * 获取当前线程的租户上下文
     *
     * @return 租户上下文，如果未设置则返回null
     */
    public static TenantContext getContext() {
        return CONTEXT_HOLDER.get();
    }

    /**
     * 设置当前线程的租户上下文
     *
     * @param context 租户上下文
     */
    public static void setContext(TenantContext context) {
        if (context == null) {
            clearContext();
        } else {
            CONTEXT_HOLDER.set(context);
            if (log.isDebugEnabled()) {
                log.debug("Set tenant context: {}", context);
            }
        }
    }

    /**
     * 清理当前线程的租户上下文
     */
    public static void clearContext() {
        TenantContext context = CONTEXT_HOLDER.get();
        if (context != null && log.isDebugEnabled()) {
            log.debug("Clear tenant context: {}", context);
        }
        CONTEXT_HOLDER.remove();
    }

    /**
     * 获取当前租户ID
     *
     * @return 租户ID，如果未设置则返回null
     */
    public static String getTenantId() {
        TenantContext context = getContext();
        return context != null ? context.getTenantId() : null;
    }

    /**
     * 获取当前租户ID，如果未设置则返回默认值
     *
     * @return 租户ID，如果未设置则返回默认租户ID
     */
    public static String getTenantIdOrDefault() {
        String tenantId = getTenantId();
        return tenantId != null ? tenantId : DEFAULT_TENANT_ID;
    }

    /**
     * 设置当前租户ID
     *
     * @param tenantId 租户ID
     */
    public static void setTenantId(String tenantId) {
        if (tenantId == null || tenantId.trim().isEmpty()) {
            clearContext();
            return;
        }

        TenantContext context = getContext();
        if (context == null) {
            context = new TenantContext(tenantId.trim());
            setContext(context);
        } else {
            context.setTenantId(tenantId.trim());
        }
    }

    /**
     * 检查是否已设置租户上下文
     *
     * @return 如果已设置返回true，否则返回false
     */
    public static boolean hasContext() {
        return getContext() != null;
    }

    /**
     * 检查是否已设置租户ID
     *
     * @return 如果已设置返回true，否则返回false
     */
    public static boolean hasTenantId() {
        return getTenantId() != null;
    }

    /**
     * 获取当前租户名称
     *
     * @return 租户名称，如果未设置则返回null
     */
    public static String getTenantName() {
        TenantContext context = getContext();
        return context != null ? context.getTenantName() : null;
    }

    /**
     * 设置当前租户名称
     *
     * @param tenantName 租户名称
     */
    public static void setTenantName(String tenantName) {
        TenantContext context = getContext();
        if (context != null) {
            context.setTenantName(tenantName);
        }
    }

    /**
     * 获取当前租户状态
     *
     * @return 租户状态，如果未设置则返回null
     */
    public static TenantStatus getTenantStatus() {
        TenantContext context = getContext();
        return context != null ? context.getStatus() : null;
    }

    /**
     * 设置当前租户状态
     *
     * @param status 租户状态
     */
    public static void setTenantStatus(TenantStatus status) {
        TenantContext context = getContext();
        if (context != null) {
            context.setStatus(status);
        }
    }

    /**
     * 检查当前租户是否处于活跃状态
     *
     * @return 如果租户处于活跃状态返回true，否则返回false
     */
    public static boolean isTenantActive() {
        TenantStatus status = getTenantStatus();
        return status == null || status == TenantStatus.ACTIVE;
    }

    /**
     * 创建租户上下文的快照
     * <p>
     * 用于在异步操作中传播租户上下文
     * </p>
     *
     * @return 租户上下文快照，如果未设置则返回null
     */
    public static TenantContext createSnapshot() {
        TenantContext context = getContext();
        return context != null ? context.copy() : null;
    }

    /**
     * 从快照恢复租户上下文
     * <p>
     * 用于在异步操作中恢复租户上下文
     * </p>
     *
     * @param snapshot 租户上下文快照
     */
    public static void restoreFromSnapshot(TenantContext snapshot) {
        setContext(snapshot);
    }

    /**
     * 执行带有指定租户上下文的操作
     * <p>
     * 在操作执行期间临时设置租户上下文，操作完成后恢复原有上下文
     * </p>
     *
     * @param tenantId 租户ID
     * @param runnable 要执行的操作
     */
    public static void runWithTenant(String tenantId, Runnable runnable) {
        TenantContext originalContext = createSnapshot();
        try {
            setTenantId(tenantId);
            runnable.run();
        } finally {
            restoreFromSnapshot(originalContext);
        }
    }

    /**
     * 执行带有指定租户上下文的操作（带返回值）
     * <p>
     * 在操作执行期间临时设置租户上下文，操作完成后恢复原有上下文
     * </p>
     *
     * @param tenantId 租户ID
     * @param supplier 要执行的操作
     * @param <T>      返回值类型
     * @return 操作结果
     */
    public static <T> T callWithTenant(String tenantId, java.util.function.Supplier<T> supplier) {
        TenantContext originalContext = createSnapshot();
        try {
            setTenantId(tenantId);
            return supplier.get();
        } finally {
            restoreFromSnapshot(originalContext);
        }
    }

    /**
     * 获取当前上下文的字符串表示
     *
     * @return 上下文字符串表示
     */
    public static String getContextInfo() {
        TenantContext context = getContext();
        return context != null ? context.toString() : "No tenant context";
    }
}
