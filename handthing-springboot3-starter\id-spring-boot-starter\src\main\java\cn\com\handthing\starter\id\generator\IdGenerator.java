package cn.com.handthing.starter.id.generator;

/**
 * ID生成器策略接口
 * <p>
 * 所有ID生成器都必须实现此接口。采用策略模式设计，支持不同类型的ID生成算法。
 * 泛型设计支持生成不同类型的ID（String、Long等）。
 * </p>
 * 
 * <h3>实现要求：</h3>
 * <ul>
 *   <li>实现类必须是线程安全的</li>
 *   <li>generate()方法不能返回null</li>
 *   <li>生成的ID在合理时间内应该是唯一的</li>
 *   <li>实现类应该注册为Spring Bean</li>
 * </ul>
 * 
 * <h3>使用示例：</h3>
 * <pre>
 * &#64;Component("customIdGenerator")
 * public class CustomIdGenerator implements IdGenerator&lt;String&gt; {
 *     &#64;Override
 *     public String generate() {
 *         return "CUSTOM_" + System.currentTimeMillis();
 *     }
 * }
 * </pre>
 *
 * @param <T> ID的类型，如String、Long等
 * <AUTHOR>
 * @since V1.0.0
 */
public interface IdGenerator<T> {
    
    /**
     * 生成并返回一个新的ID
     * <p>
     * 此方法必须是线程安全的，并且在合理时间内生成的ID应该是唯一的。
     * 实现类不应该返回null，如果生成失败应该抛出异常。
     * </p>
     * 
     * @return 新生成的ID，不能为null
     * @throws IdGenerationException 当ID生成失败时抛出
     */
    T generate();
    
    /**
     * 获取此生成器支持的ID类型
     * <p>
     * 默认实现通过反射获取泛型类型，子类可以重写此方法提供更精确的类型信息。
     * </p>
     * 
     * @return ID类型的Class对象
     */
    default Class<T> getIdType() {
        // 默认实现，子类可以重写
        return null;
    }
    
    /**
     * 获取生成器的名称
     * <p>
     * 用于标识不同的生成器实现，默认返回类的简单名称。
     * 子类可以重写此方法提供更友好的名称。
     * </p>
     * 
     * @return 生成器名称
     */
    default String getName() {
        return this.getClass().getSimpleName();
    }
    
    /**
     * 检查生成器是否可用
     * <p>
     * 用于健康检查，默认返回true。
     * 子类可以重写此方法实现自定义的可用性检查逻辑。
     * </p>
     * 
     * @return 如果生成器可用返回true，否则返回false
     */
    default boolean isAvailable() {
        return true;
    }
    
    /**
     * 预热生成器
     * <p>
     * 在应用启动时调用，用于执行必要的初始化操作。
     * 默认实现为空，子类可以重写此方法实现预热逻辑。
     * </p>
     */
    default void warmUp() {
        // 默认实现为空
    }
    
    /**
     * 关闭生成器
     * <p>
     * 在应用关闭时调用，用于释放资源。
     * 默认实现为空，子类可以重写此方法实现清理逻辑。
     * </p>
     */
    default void shutdown() {
        // 默认实现为空
    }
}
