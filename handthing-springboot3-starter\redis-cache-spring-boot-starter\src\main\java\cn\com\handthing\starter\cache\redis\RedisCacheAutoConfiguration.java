package cn.com.handthing.starter.cache.redis;

import cn.com.handthing.starter.cache.CacheService;
import cn.com.handthing.starter.cache.redis.config.RedisCacheProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cache.CacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

/**
 * Redis缓存自动配置类
 * <p>
 * 当缓存类型配置为redis时，自动配置Redis缓存管理器和服务
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@AutoConfiguration
@EnableConfigurationProperties(RedisCacheProperties.class)
@ConditionalOnClass({RedisConnectionFactory.class, RedisTemplate.class})
@ConditionalOnProperty(prefix = "handthing.cache", name = "type", havingValue = "redis")
public class RedisCacheAutoConfiguration {

    /**
     * 创建Redis缓存管理器
     *
     * @param properties        Redis缓存配置属性
     * @param connectionFactory Redis连接工厂
     * @return Redis缓存管理器
     */
    @Bean
    @Primary
    @ConditionalOnMissingBean(CacheManager.class)
    public RedisCacheManager redisCacheManager(RedisCacheProperties properties,
                                               RedisConnectionFactory connectionFactory) {
        log.info("Creating RedisCacheManager with properties: {}", properties);
        return new RedisCacheManager(properties, connectionFactory);
    }

    /**
     * 创建RedisTemplate
     *
     * @param connectionFactory Redis连接工厂
     * @return RedisTemplate实例
     */
    @Bean
    @ConditionalOnMissingBean(name = "redisTemplate")
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory connectionFactory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);
        
        // 设置序列化器
        StringRedisSerializer stringSerializer = new StringRedisSerializer();
        GenericJackson2JsonRedisSerializer jsonSerializer = new GenericJackson2JsonRedisSerializer();
        
        // 键序列化
        template.setKeySerializer(stringSerializer);
        template.setHashKeySerializer(stringSerializer);
        
        // 值序列化
        template.setValueSerializer(jsonSerializer);
        template.setHashValueSerializer(jsonSerializer);
        
        template.afterPropertiesSet();
        
        log.info("Created RedisTemplate with String key serializer and JSON value serializer");
        return template;
    }

    /**
     * 创建Redis缓存服务
     *
     * @param cacheManager      Redis缓存管理器
     * @param redisTemplate     Redis模板
     * @param connectionFactory Redis连接工厂
     * @return Redis缓存服务
     */
    @Bean
    @Primary
    @ConditionalOnMissingBean(CacheService.class)
    public CacheService redisCacheService(RedisCacheManager cacheManager,
                                          RedisTemplate<String, Object> redisTemplate,
                                          RedisConnectionFactory connectionFactory) {
        log.info("Creating RedisCacheService with CacheManager: {}", 
                cacheManager.getClass().getSimpleName());
        return new RedisCacheService(cacheManager, redisTemplate, connectionFactory);
    }
}