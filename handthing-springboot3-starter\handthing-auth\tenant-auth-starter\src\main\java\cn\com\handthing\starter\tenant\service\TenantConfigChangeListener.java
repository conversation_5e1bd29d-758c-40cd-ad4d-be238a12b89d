package cn.com.handthing.starter.tenant.service;

import cn.com.handthing.starter.tenant.config.TenantConfig;

/**
 * 租户配置变更监听器接口
 * <p>
 * 用于监听租户配置的变更事件，支持配置变更通知机制。
 * 实现类可以在配置变更时执行相应的业务逻辑，如缓存刷新、配置同步等。
 * </p>
 *
 * <AUTHOR>
 * @since V3.0.0
 */
public interface TenantConfigChangeListener {

    /**
     * 配置创建事件
     *
     * @param config 新创建的配置
     */
    default void onConfigCreated(TenantConfig config) {
        // 默认实现为空，子类可选择性重写
    }

    /**
     * 配置更新事件
     *
     * @param oldConfig 更新前的配置
     * @param newConfig 更新后的配置
     */
    default void onConfigUpdated(TenantConfig oldConfig, TenantConfig newConfig) {
        // 默认实现为空，子类可选择性重写
    }

    /**
     * 配置删除事件
     *
     * @param config 被删除的配置
     */
    default void onConfigDeleted(TenantConfig config) {
        // 默认实现为空，子类可选择性重写
    }

    /**
     * 配置启用状态变更事件
     *
     * @param config  配置对象
     * @param enabled 新的启用状态
     */
    default void onConfigEnabledChanged(TenantConfig config, boolean enabled) {
        // 默认实现为空，子类可选择性重写
    }

    /**
     * 批量配置变更事件
     *
     * @param tenantId 租户ID
     * @param changeCount 变更的配置数量
     */
    default void onBatchConfigChanged(String tenantId, int changeCount) {
        // 默认实现为空，子类可选择性重写
    }

    /**
     * 租户配置清空事件
     *
     * @param tenantId 租户ID
     */
    default void onTenantConfigCleared(String tenantId) {
        // 默认实现为空，子类可选择性重写
    }

    /**
     * 获取监听器名称
     *
     * @return 监听器名称
     */
    default String getName() {
        return this.getClass().getSimpleName();
    }

    /**
     * 获取监听器优先级
     * <p>
     * 数值越小优先级越高，默认优先级为100
     * </p>
     *
     * @return 优先级数值
     */
    default int getPriority() {
        return 100;
    }

    /**
     * 检查是否支持指定的租户
     *
     * @param tenantId 租户ID
     * @return 如果支持返回true，否则返回false
     */
    default boolean supports(String tenantId) {
        return true;
    }

    /**
     * 检查是否支持指定的配置键
     *
     * @param configKey 配置键
     * @return 如果支持返回true，否则返回false
     */
    default boolean supportsConfigKey(String configKey) {
        return true;
    }

    /**
     * 检查是否支持指定的租户和配置键
     *
     * @param tenantId  租户ID
     * @param configKey 配置键
     * @return 如果支持返回true，否则返回false
     */
    default boolean supports(String tenantId, String configKey) {
        return supports(tenantId) && supportsConfigKey(configKey);
    }
}
