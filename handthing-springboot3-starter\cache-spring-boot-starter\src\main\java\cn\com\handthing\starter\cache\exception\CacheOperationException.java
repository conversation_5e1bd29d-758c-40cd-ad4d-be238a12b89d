package cn.com.handthing.starter.cache.exception;

/**
 * 缓存操作异常
 * <p>
 * 当缓存操作（如读取、写入、删除）失败时抛出此异常
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
public class CacheOperationException extends CacheException {

    /**
     * 构造函数
     *
     * @param message 异常消息
     */
    public CacheOperationException(String message) {
        super(message);
    }

    /**
     * 构造函数
     *
     * @param message 异常消息
     * @param cause   原因异常
     */
    public CacheOperationException(String message, Throwable cause) {
        super(message, cause);
    }

    /**
     * 构造函数
     *
     * @param cause 原因异常
     */
    public CacheOperationException(Throwable cause) {
        super(cause);
    }
}