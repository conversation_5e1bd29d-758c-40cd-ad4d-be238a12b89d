package cn.com.handthing.starter.connector.wecom;

import cn.com.handthing.starter.connector.PlatformType;
import cn.com.handthing.starter.connector.wecom.config.WeComConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.client.RestTemplate;

import static org.junit.jupiter.api.Assertions.*;

/**
 * WeComAuthProvider 单元测试
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@ExtendWith(MockitoExtension.class)
class WeComAuthProviderTest {

    @Mock
    private RestTemplate restTemplate;

    private WeComConfig config;
    private WeComAuthProvider authProvider;

    @BeforeEach
    void setUp() {
        config = new WeComConfig();
        config.setEnabled(true);
        config.setCorpId("test-corp-id");
        config.setAgentId("test-agent-id");
        config.setSecret("test-secret");
        config.setRedirectUri("https://test.com/callback");
        
        authProvider = new WeComAuthProvider(config, restTemplate);
    }

    @Test
    void testGetPlatformType() {
        assertEquals(PlatformType.WECOM, authProvider.getPlatformType());
    }

    @Test
    void testSupportsRefreshToken() {
        assertFalse(authProvider.supportsRefreshToken());
    }

    @Test
    void testGetDefaultScope() {
        assertEquals("snsapi_base", authProvider.getDefaultScope());
    }

    @Test
    void testGetAuthorizationUrl() {
        String state = "test-state";
        String redirectUri = "https://test.com/callback";
        
        String authUrl = authProvider.getAuthorizationUrl(state, redirectUri);
        
        assertNotNull(authUrl);
        assertTrue(authUrl.contains("appid=" + config.getCorpId()));
        assertTrue(authUrl.contains("agentid=" + config.getAgentId()));
        assertTrue(authUrl.contains("state=" + state));
        assertTrue(authUrl.contains("redirect_uri="));
        assertTrue(authUrl.contains("response_type=code"));
        assertTrue(authUrl.contains("scope=snsapi_base"));
    }

    @Test
    void testGetAuthorizationUrlWithNullState() {
        String redirectUri = "https://test.com/callback";
        
        String authUrl = authProvider.getAuthorizationUrl(null, redirectUri);
        
        assertNotNull(authUrl);
        assertTrue(authUrl.contains("state="));
    }

    @Test
    void testGetAuthorizationUrlWithDefaultRedirectUri() {
        String state = "test-state";
        
        String authUrl = authProvider.getAuthorizationUrl(state, null);
        
        assertNotNull(authUrl);
        assertTrue(authUrl.contains("redirect_uri="));
    }

    @Test
    void testConfigValidation() {
        // 测试有效配置
        assertTrue(config.isValid());
        
        // 测试无效配置
        WeComConfig invalidConfig = new WeComConfig();
        invalidConfig.setEnabled(true);
        assertFalse(invalidConfig.isValid());
        
        invalidConfig.setCorpId("test-corp-id");
        assertFalse(invalidConfig.isValid());
        
        invalidConfig.setAgentId("test-agent-id");
        assertFalse(invalidConfig.isValid());
        
        invalidConfig.setSecret("test-secret");
        assertTrue(invalidConfig.isValid());
    }

    @Test
    void testConfigUrls() {
        assertEquals("https://qyapi.weixin.qq.com", config.getEffectiveApiBaseUrl());
        assertEquals("https://open.weixin.qq.com/connect/oauth2/authorize", config.getEffectiveAuthUrl());
        
        assertNotNull(config.getTokenUrl());
        assertNotNull(config.getUserInfoUrl());
        assertNotNull(config.getUserDetailUrl());
        assertNotNull(config.getSendMessageUrl());
    }

    @Test
    void testThirdPartyMode() {
        assertFalse(config.isThirdPartyMode());
        
        config.setThirdPartyApp(true);
        config.setSuiteId("test-suite-id");
        config.setSuiteSecret("test-suite-secret");
        
        assertTrue(config.isThirdPartyMode());
    }

    @Test
    void testCallbackConfiguration() {
        assertFalse(config.isCallbackEnabled());
        
        config.setEnableCallback(true);
        config.setToken("test-token");
        config.setEncodingAESKey("test-aes-key");
        
        assertTrue(config.isCallbackEnabled());
    }
}
