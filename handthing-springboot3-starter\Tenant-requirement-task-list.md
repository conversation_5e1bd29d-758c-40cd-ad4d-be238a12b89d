# HandThing Auth SaaS多租户增强版 - 任务清单

**项目名称**: HandThing Auth Spring Boot Starter V3.0 SaaS增强版  
**作者**: HandThing  
**版本**: V3.0  
**基于**: 现有的HandThing认证系统V2.0

## 📋 项目概述

将现有的单体应用认证模型演进为支持多个隔离租户、且每个租户拥有独立认证策略和配置的企业级认证中心。核心设计从"构建统一认证流程"演进为"为每个租户提供一个独立的、可动态配置的认证总线"。

## 🎯 核心设计目标

- **多租户支持**: 共享数据库、共享应用实例，通过tenant_id进行严格的逻辑隔离
- **租户识别策略**: 支持子域名、HTTP头、URL路径等多种租户识别方式
- **动态配置中心**: 每个租户可以有独立的认证配置，支持分层配置读取
- **租户级认证总线**: 所有认证决策都基于当前租户的特定配置

## 📦 模块任务清单

### 1. auth-core 模块增强

#### 1.1 模型定义强化 ✅
- [x] **AuthenticationContext**: 强化tenantId的核心地位
  - [x] 添加tenantId字段作为核心标识
  - [x] 修改构造函数支持租户ID
  - [x] 更新相关方法和描述信息

#### 1.2 租户上下文管理 🔄
- [ ] **TenantContextHolder**: 新增，用于持有tenantId
  - [ ] 基于ThreadLocal的租户上下文持有者
  - [ ] 提供设置、获取、清理租户ID的方法
  - [ ] 支持租户上下文的传播和继承

- [ ] **TenantContext**: 新增，租户上下文信息封装
  - [ ] 封装租户ID、租户名称、租户状态等信息
  - [ ] 提供租户信息的验证和格式化方法

#### 1.3 租户识别策略接口 🔄
- [ ] **TenantResolver**: 新增，定义租户识别策略接口
  - [ ] 定义从HTTP请求中解析租户ID的标准接口
  - [ ] 支持多种解析策略的插拔式架构

- [ ] **SubdomainTenantResolver**: 子域名租户解析器
  - [ ] 从子域名解析租户ID (如 tenant-a.myapp.com)
  - [ ] 支持自定义子域名格式和验证规则

- [ ] **HttpHeaderTenantResolver**: HTTP头租户解析器
  - [ ] 从自定义HTTP头解析租户ID (如 X-Tenant-ID: tenant-a)
  - [ ] 支持多个备选头字段

- [ ] **RequestPathTenantResolver**: URL路径租户解析器
  - [ ] 从URL路径解析租户ID (如 /api/tenant-a/...)
  - [ ] 支持自定义路径模式匹配

#### 1.4 租户配置模型 🔄
- [ ] **TenantConfig**: 租户配置实体类
  - [ ] 对应tenant_configs表的实体模型
  - [ ] 支持配置项的类型转换和验证

- [ ] **TenantConfigKey**: 租户配置键枚举
  - [ ] 定义所有支持的租户配置项
  - [ ] 包含配置项的类型、默认值、描述等元信息

### 2. auth-spring-boot-starter 模块增强

#### 2.1 核心过滤器 🔄
- [ ] **TenantResolverFilter**: 新增，用于在请求早期识别租户
  - [ ] 高优先级Servlet过滤器，在Spring Security之前执行
  - [ ] 委托给TenantResolver策略进行租户识别
  - [ ] 设置和清理TenantContextHolder
  - [ ] 处理租户识别失败的情况

#### 2.2 核心服务 🔄
- [ ] **TenantConfigService**: 新增，提供分层的租户配置读取能力
  - [ ] 实现分层配置读取逻辑：租户配置 -> 全局默认配置
  - [ ] 集成cache-starter进行重度缓存
  - [ ] 支持配置项的类型转换和验证
  - [ ] 提供配置变更通知机制

- [ ] **TenantConfigRepository**: 租户配置数据访问层
  - [ ] 提供tenant_configs表的CRUD操作
  - [ ] 支持按租户ID和配置键查询
  - [ ] 支持批量配置操作

#### 2.3 自动配置增强 🔄
- [ ] **SaaSAuthAutoConfiguration**: 新增SaaS认证自动配置
  - [ ] 自动装配TenantResolverFilter，确保高优先级
  - [ ] 自动装配TenantConfigService，集成cache-starter
  - [ ] 根据配置条件装配不同的TenantResolver实现
  - [ ] 配置租户相关的Bean和属性

#### 2.4 组件增强 🔄
- [ ] **所有AuthenticationProvider增强**: 注入并使用TenantConfigService
  - [ ] PasswordAuthenticationProvider: 支持租户级密码策略配置
  - [ ] SmsAuthenticationProvider: 支持租户级短信配置
  - [ ] 第三方认证Provider: 支持租户级第三方平台配置

- [ ] **TokenService增强**: 实现tenantId在JWT中的双向绑定
  - [ ] JWT生成时将tenantId作为核心Claim写入
  - [ ] JWT解析时验证Claim中的tenantId
  - [ ] 重建AuthContext时填充tenantId

#### 2.5 认证管理器增强 🔄
- [ ] **AuthenticationManager增强**: 支持租户感知的认证流程
  - [ ] 在认证前验证租户状态和权限
  - [ ] 根据租户配置选择合适的认证提供者
  - [ ] 在认证上下文中正确设置租户信息

### 3. 数据库支持

#### 3.1 数据库模型 🔄
- [ ] **tenant_configs表设计**
  ```sql
  CREATE TABLE tenant_configs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    tenant_id VARCHAR(64) NOT NULL,
    config_key VARCHAR(128) NOT NULL,
    config_value TEXT,
    config_type VARCHAR(32) DEFAULT 'STRING',
    description VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_tenant_config (tenant_id, config_key),
    INDEX idx_tenant_id (tenant_id)
  );
  ```

- [ ] **租户信息表设计** (可选)
  ```sql
  CREATE TABLE tenants (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    tenant_id VARCHAR(64) UNIQUE NOT NULL,
    tenant_name VARCHAR(128) NOT NULL,
    status VARCHAR(32) DEFAULT 'ACTIVE',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
  );
  ```

#### 3.2 数据访问层 🔄
- [ ] **TenantConfigEntity**: JPA实体类
- [ ] **TenantConfigRepository**: Spring Data JPA仓库
- [ ] **TenantEntity**: 租户信息实体类 (可选)
- [ ] **TenantRepository**: 租户信息仓库 (可选)

### 4. 配置属性增强

#### 4.1 租户解析配置 🔄
- [ ] **TenantResolverProperties**: 租户解析器配置属性
  ```yaml
  handthing:
    auth:
      tenant:
        resolver:
          strategy: subdomain # 可选: header, path, subdomain
          subdomain:
            pattern: "{tenant}.myapp.com"
          header:
            name: "X-Tenant-ID"
            fallback-names: ["Tenant-ID", "X-Tenant"]
          path:
            pattern: "/api/{tenant}/**"
  ```

#### 4.2 SaaS配置增强 🔄
- [ ] **SaaSAuthProperties**: SaaS认证配置属性
  ```yaml
  handthing:
    auth:
      saas:
        enabled: true
        default-tenant-id: "default"
        tenant-required: true
        cache:
          enabled: true
          ttl: 300s
  ```

### 5. 接口协定与集成

#### 5.1 主应用实现接口 🔄
- [ ] **TenantResolver自定义实现**: 主应用可选择实现自定义租户识别逻辑
- [ ] **TenantConfigProvider**: 主应用可提供额外的配置源
- [ ] **TenantValidator**: 主应用可提供租户验证逻辑

#### 5.2 业务接口增强 🔄
- [ ] **UserDetailsService增强**: 支持租户感知的用户查询
- [ ] **SmsCodeValidator增强**: 支持租户感知的短信验证
- [ ] **第三方API服务增强**: 支持租户级的第三方平台配置

### 6. 测试和验证

#### 6.1 单元测试 🔄
- [ ] TenantContextHolder测试
- [ ] TenantResolver各实现类测试
- [ ] TenantConfigService测试
- [ ] 增强后的认证提供者测试

#### 6.2 集成测试 🔄
- [ ] 多租户认证流程端到端测试
- [ ] 租户配置动态加载测试
- [ ] JWT中tenantId的双向绑定测试
- [ ] 缓存机制测试

#### 6.3 测试应用增强 🔄
- [ ] test-app支持多租户配置
- [ ] 提供多租户测试场景
- [ ] 租户配置管理界面 (可选)

### 7. 文档和示例

#### 7.1 技术文档 🔄
- [ ] 多租户架构设计文档
- [ ] 租户配置参考文档
- [ ] 迁移指南 (从V2.0到V3.0)
- [ ] 最佳实践指南

#### 7.2 示例代码 🔄
- [ ] 多租户配置示例
- [ ] 自定义TenantResolver示例
- [ ] 租户感知的业务代码示例

## 🚀 开发优先级

### 阶段一：核心基础设施 (高优先级)
1. TenantContextHolder和TenantContext
2. TenantResolver接口和基本实现
3. TenantResolverFilter
4. AuthenticationContext增强

### 阶段二：配置中心 (高优先级)
1. TenantConfigService和相关数据模型
2. 数据库表设计和实体类
3. 分层配置读取逻辑
4. 缓存集成

### 阶段三：认证增强 (中优先级)
1. 所有认证提供者的租户感知增强
2. TokenService的JWT双向绑定
3. AuthenticationManager增强

### 阶段四：集成和测试 (中优先级)
1. 自动配置完善
2. 测试应用增强
3. 单元测试和集成测试

### 阶段五：文档和优化 (低优先级)
1. 技术文档编写
2. 示例代码开发
3. 性能优化和监控

## 📝 注意事项

1. **向后兼容性**: 确保V3.0版本对现有V2.0用户保持向后兼容
2. **性能考虑**: 租户识别和配置读取不应显著影响认证性能
3. **安全性**: 确保租户间的严格隔离，防止数据泄露
4. **可扩展性**: 设计应支持未来更多的租户识别策略和配置类型
5. **监控和日志**: 增强日志记录，支持按租户进行监控和审计

---

**© 2024 HandThing. All rights reserved.**
