package cn.com.handthing.starter.auth;

import cn.com.handthing.starter.auth.core.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.Ordered;
import org.springframework.web.filter.OncePerRequestFilter;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;

/**
 * 认证过滤器
 * <p>
 * 负责处理HTTP请求的认证逻辑，从请求中提取JWT令牌并验证。
 * 如果令牌有效，则设置用户上下文；如果无效，则返回认证错误。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@RequiredArgsConstructor
public class AuthenticationFilter extends OncePerRequestFilter implements Ordered {

    private final JwtTokenProvider jwtTokenProvider;
    private final AuthProperties authProperties;

    @Override
    public int getOrder() {
        return authProperties.getWeb().getFilterOrder();
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, 
                                   FilterChain filterChain) throws ServletException, IOException {
        
        String requestPath = request.getRequestURI();
        
        try {
            // 检查是否需要跳过认证
            if (shouldSkipAuthentication(requestPath)) {
                log.debug("Skipping authentication for path: {}", requestPath);
                filterChain.doFilter(request, response);
                return;
            }

            // 提取令牌
            String token = AuthenticationUtils.extractTokenFromRequest(request, 
                    authProperties.getJwt() != null ? authProperties.getJwt() : new JwtConfig());

            if (token == null || token.trim().isEmpty()) {
                log.debug("No token found in request for path: {}", requestPath);
                handleAuthenticationError(response, "MISSING_TOKEN", "Authentication token is required");
                return;
            }

            // 验证令牌
            if (!jwtTokenProvider.validateToken(token)) {
                log.debug("Invalid token for path: {}", requestPath);
                handleAuthenticationError(response, "INVALID_TOKEN", "Authentication token is invalid or expired");
                return;
            }

            // 解析令牌
            JwtClaims claims = jwtTokenProvider.parseToken(token);
            
            // 创建用户上下文
            UserContext userContext = createUserContext(claims, token, request);
            
            // 设置用户上下文
            UserContextHolder.setContext(userContext);
            
            log.debug("Authentication successful for user: {} on path: {}", 
                    claims.getUsername(), requestPath);

            // 继续处理请求
            filterChain.doFilter(request, response);

        } catch (TokenExpiredException e) {
            log.debug("Token expired for path: {}", requestPath);
            handleAuthenticationError(response, "TOKEN_EXPIRED", "Authentication token has expired");
        } catch (AuthenticationException e) {
            log.debug("Authentication failed for path: {}: {}", requestPath, e.getMessage());
            handleAuthenticationError(response, e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            log.error("Authentication error for path: {}", requestPath, e);
            handleAuthenticationError(response, "AUTHENTICATION_ERROR", "Authentication failed");
        } finally {
            // 清理用户上下文
            UserContextHolder.clearContext();
        }
    }

    /**
     * 判断是否应该跳过认证
     *
     * @param requestPath 请求路径
     * @return 如果应该跳过返回true，否则返回false
     */
    private boolean shouldSkipAuthentication(String requestPath) {
        // 检查排除路径
        if (authProperties.isPathExcluded(requestPath)) {
            return true;
        }

        // 检查静态资源路径
        for (String staticPath : authProperties.getWeb().getStaticPaths()) {
            if (pathMatches(requestPath, staticPath)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 路径匹配
     *
     * @param requestPath 请求路径
     * @param pattern     匹配模式
     * @return 如果匹配返回true，否则返回false
     */
    private boolean pathMatches(String requestPath, String pattern) {
        if (pattern.endsWith("/**")) {
            String prefix = pattern.substring(0, pattern.length() - 3);
            return requestPath.startsWith(prefix);
        } else if (pattern.endsWith("/*")) {
            String prefix = pattern.substring(0, pattern.length() - 2);
            return requestPath.startsWith(prefix) && !requestPath.substring(prefix.length()).contains("/");
        } else {
            return requestPath.equals(pattern);
        }
    }

    /**
     * 创建用户上下文
     *
     * @param claims  JWT声明
     * @param token   访问令牌
     * @param request HTTP请求
     * @return 用户上下文
     */
    private UserContext createUserContext(JwtClaims claims, String token, HttpServletRequest request) {
        // 创建用户信息
        UserInfo userInfo = UserInfo.builder()
                .userId(claims.getSubject())
                .username(claims.getUsername())
                .nickname(claims.getNickname())
                .email(claims.getEmail())
                .phone(claims.getPhone())
                .roles(claims.getRoles())
                .permissions(claims.getPermissions())
                .enabled(true)
                .accountNonExpired(true)
                .accountNonLocked(true)
                .credentialsNonExpired(true)
                .build();

        // 创建用户上下文实现
        return new DefaultUserContext(userInfo, claims, token, request);
    }

    /**
     * 处理认证错误
     *
     * @param response         HTTP响应
     * @param errorCode        错误代码
     * @param errorDescription 错误描述
     * @throws IOException IO异常
     */
    private void handleAuthenticationError(HttpServletResponse response, String errorCode, String errorDescription) 
            throws IOException {
        
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType("application/json;charset=UTF-8");
        
        String errorJson = String.format(
                "{\"success\":false,\"error\":\"%s\",\"error_description\":\"%s\",\"timestamp\":\"%s\"}",
                errorCode, errorDescription, LocalDateTime.now()
        );
        
        response.getWriter().write(errorJson);
        response.getWriter().flush();
    }

    /**
     * 默认用户上下文实现
     */
    private static class DefaultUserContext implements UserContext {
        
        private final UserInfo userInfo;
        private final JwtClaims jwtClaims;
        private final String accessToken;
        private final String ipAddress;
        private final String userAgent;
        private final LocalDateTime authenticationTime;

        public DefaultUserContext(UserInfo userInfo, JwtClaims jwtClaims, String accessToken, HttpServletRequest request) {
            this.userInfo = userInfo;
            this.jwtClaims = jwtClaims;
            this.accessToken = accessToken;
            this.ipAddress = AuthenticationUtils.getClientIpAddress(request);
            this.userAgent = AuthenticationUtils.getUserAgent(request);
            this.authenticationTime = LocalDateTime.now();
        }

        @Override
        public UserInfo getUserInfo() {
            return userInfo;
        }

        @Override
        public String getAccessToken() {
            return accessToken;
        }

        @Override
        public String getRefreshToken() {
            return null; // 刷新令牌通常不在请求中传递
        }

        @Override
        public String getTokenType() {
            return jwtClaims.getTokenType();
        }

        @Override
        public String getScope() {
            return jwtClaims.getScope();
        }

        @Override
        public GrantType getGrantType() {
            String grantTypeStr = jwtClaims.getGrantType();
            return grantTypeStr != null ? GrantType.fromCode(grantTypeStr) : null;
        }

        @Override
        public String getClientId() {
            return jwtClaims.getClientId();
        }

        @Override
        public String getSessionId() {
            return jwtClaims.getSessionId();
        }

        @Override
        public String getIpAddress() {
            return ipAddress;
        }

        @Override
        public String getUserAgent() {
            return userAgent;
        }

        @Override
        public String getDeviceId() {
            return jwtClaims.getDeviceId();
        }

        @Override
        public LocalDateTime getAuthenticationTime() {
            return authenticationTime;
        }

        @Override
        public LocalDateTime getExpirationTime() {
            return jwtClaims.getExpiration();
        }

        @Override
        public java.util.Map<String, Object> getAttributes() {
            return jwtClaims.getCustomClaims();
        }

        @Override
        public Object getAttribute(String key) {
            return jwtClaims.getCustomClaim(key);
        }

        @Override
        public <T> T getAttribute(String key, Class<T> clazz) {
            return jwtClaims.getCustomClaim(key, clazz);
        }

        @Override
        public boolean isAuthenticated() {
            return userInfo != null && jwtClaims.isActive();
        }

        @Override
        public boolean isExpired() {
            return jwtClaims.isExpired();
        }
    }
}
