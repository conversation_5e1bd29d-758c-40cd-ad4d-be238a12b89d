package cn.com.handthing.starter.auth.core;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 认证请求抽象类
 * <p>
 * 定义认证请求的基础结构，包含授权类型、客户端信息、时间戳等通用字段。
 * 各种具体的认证方式需要继承此类并添加特定的认证参数。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
public abstract class AuthenticationRequest {

    /**
     * 授权类型
     */
    private GrantType grantType;

    /**
     * 客户端ID
     */
    private String clientId;

    /**
     * 客户端密钥
     */
    private String clientSecret;

    /**
     * 请求范围
     */
    private String scope;

    /**
     * 请求时间戳
     */
    private LocalDateTime timestamp;

    /**
     * 请求IP地址
     */
    private String ipAddress;

    /**
     * 用户代理
     */
    private String userAgent;

    /**
     * 设备ID
     */
    private String deviceId;

    /**
     * 扩展参数
     */
    private Map<String, Object> extraParams;

    /**
     * 默认构造函数
     */
    protected AuthenticationRequest() {
        this.timestamp = LocalDateTime.now();
        this.extraParams = new HashMap<>();
    }

    /**
     * 构造函数
     *
     * @param grantType 授权类型
     */
    protected AuthenticationRequest(GrantType grantType) {
        this();
        this.grantType = grantType;
    }

    /**
     * 添加扩展参数
     *
     * @param key   参数键
     * @param value 参数值
     */
    public void addExtraParam(String key, Object value) {
        if (extraParams == null) {
            extraParams = new HashMap<>();
        }
        extraParams.put(key, value);
    }

    /**
     * 获取扩展参数
     *
     * @param key 参数键
     * @return 参数值
     */
    public Object getExtraParam(String key) {
        return extraParams != null ? extraParams.get(key) : null;
    }

    /**
     * 获取扩展参数（指定类型）
     *
     * @param key   参数键
     * @param clazz 参数类型
     * @param <T>   泛型类型
     * @return 参数值
     */
    @SuppressWarnings("unchecked")
    public <T> T getExtraParam(String key, Class<T> clazz) {
        Object value = getExtraParam(key);
        if (value != null && clazz.isInstance(value)) {
            return (T) value;
        }
        return null;
    }

    /**
     * 验证请求参数的有效性
     * <p>
     * 子类可以重写此方法来实现特定的验证逻辑
     * </p>
     *
     * @return 如果参数有效返回true，否则返回false
     */
    public boolean isValid() {
        return grantType != null;
    }

    /**
     * 获取认证标识
     * <p>
     * 用于标识认证请求的唯一性，子类可以重写此方法
     * </p>
     *
     * @return 认证标识
     */
    public abstract String getAuthenticationIdentifier();

    /**
     * 获取认证凭证
     * <p>
     * 用于获取认证所需的凭证信息，子类必须实现此方法
     * </p>
     *
     * @return 认证凭证
     */
    public abstract Object getCredentials();

    /**
     * 获取请求描述
     *
     * @return 请求描述
     */
    public String getDescription() {
        return String.format("%s authentication request from %s", 
                grantType != null ? grantType.getDescription() : "Unknown", 
                ipAddress != null ? ipAddress : "Unknown IP");
    }

    @Override
    public String toString() {
        return String.format("AuthenticationRequest{grantType=%s, clientId='%s', scope='%s', timestamp=%s, ipAddress='%s'}",
                grantType, clientId, scope, timestamp, ipAddress);
    }
}
