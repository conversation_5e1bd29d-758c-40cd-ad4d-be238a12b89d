package cn.com.handthing.starter.crypto.service;

import cn.com.handthing.starter.crypto.core.KeyProvider;
import cn.com.handthing.starter.crypto.core.SymmetricEncryptor;
import cn.com.handthing.starter.crypto.exception.CryptoException;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.security.Security;
import java.util.Base64;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 使用国密 SM4 算法的对称加密器实现。
 *
 * <AUTHOR>
 * @since V1.0.0
 */
public class Sm4Encryptor extends AbstractCryptoService implements SymmetricEncryptor {

    static {
        // 动态添加 BouncyCastleProvider，如果尚未添加
        if (Security.getProvider(BouncyCastleProvider.PROVIDER_NAME) == null) {
            Security.addProvider(new BouncyCastleProvider());
        }
    }

    private final KeyProvider keyProvider;
    private final MeterRegistry meterRegistry;
    private final AtomicReference<SecretKeySpec> secretKeySpecRef = new AtomicReference<>();

    public Sm4Encryptor(String beanName, KeyProvider keyProvider, MeterRegistry meterRegistry) {
        super(beanName);
        this.keyProvider = Objects.requireNonNull(keyProvider, "KeyProvider must not be null");
        this.meterRegistry = Objects.requireNonNull(meterRegistry, "MeterRegistry must not be null");
        init();
    }

    private void init() {
        try {
            keyProvider.checkReady();
            Key key = keyProvider.getKey();
            this.secretKeySpecRef.set(new SecretKeySpec(key.getEncoded(), "SM4"));
        } catch (Exception e) {
            throw new CryptoException("Failed to initialize SM4 key for processor: " + beanName, e);
        }
    }
    
    @Override
    public String encrypt(String plaintext) {
        return Timer.builder("crypto.operations.duration")
                .tag("processor", this.beanName)
                .tag("operation", "encrypt")
                .register(meterRegistry)
                .record(() -> {
                    try {
                        Cipher cipher = Cipher.getInstance("SM4/ECB/PKCS5Padding", BouncyCastleProvider.PROVIDER_NAME);
                        cipher.init(Cipher.ENCRYPT_MODE, secretKeySpecRef.get());
                        byte[] encrypted = cipher.doFinal(plaintext.getBytes(StandardCharsets.UTF_8));
                        return Base64.getEncoder().encodeToString(encrypted);
                    } catch (Exception e) {
                        throw new CryptoException("SM4 encryption failed for processor: " + beanName, e);
                    }
                });
    }

    @Override
    public String decrypt(String ciphertext) {
        return Timer.builder("crypto.operations.duration")
                .tag("processor", this.beanName)
                .tag("operation", "decrypt")
                .register(meterRegistry)
                .record(() -> {
                    try {
                        Cipher cipher = Cipher.getInstance("SM4/ECB/PKCS5Padding", BouncyCastleProvider.PROVIDER_NAME);
                        cipher.init(Cipher.DECRYPT_MODE, secretKeySpecRef.get());
                        byte[] original = cipher.doFinal(Base64.getDecoder().decode(ciphertext));
                        return new String(original, StandardCharsets.UTF_8);
                    } catch (Exception e) {
                        throw new CryptoException("SM4 decryption failed for processor: " + beanName, e);
                    }
                });
    }

    @Override
    public void selfCheck() throws CryptoException {
        String testData = "HandThing-SM4-SelfCheck";
        String encrypted = this.encrypt(testData);
        String decrypted = this.decrypt(encrypted);
        if (!testData.equals(decrypted)) {
            throw new IllegalStateException("Self-check failed for SM4 processor: " + beanName);
        }
    }
}