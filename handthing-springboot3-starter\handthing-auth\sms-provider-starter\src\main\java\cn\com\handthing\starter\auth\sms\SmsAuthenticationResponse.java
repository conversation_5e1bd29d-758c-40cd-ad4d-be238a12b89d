package cn.com.handthing.starter.auth.sms;

import cn.com.handthing.starter.auth.core.AuthenticationResponse;
import cn.com.handthing.starter.auth.core.UserInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 短信验证码认证响应
 * <p>
 * 短信验证码认证的响应对象，包含认证结果、用户信息、令牌信息等。
 * 支持新用户注册和老用户登录的不同响应信息。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SmsAuthenticationResponse extends AuthenticationResponse {

    /**
     * 用户信息
     */
    private UserInfo userInfo;

    /**
     * 是否为新注册用户
     */
    private Boolean newUser;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 最后登录时间
     */
    private LocalDateTime lastLoginTime;

    /**
     * 最后登录IP
     */
    private String lastLoginIp;

    /**
     * 是否为首次登录
     */
    private Boolean firstLogin;

    /**
     * 默认构造函数
     */
    public SmsAuthenticationResponse() {
        super();
    }

    /**
     * 成功响应构造函数
     *
     * @param accessToken  访问令牌
     * @param refreshToken 刷新令牌
     * @param expiresIn    过期时间
     * @param userInfo     用户信息
     */
    public SmsAuthenticationResponse(String accessToken, String refreshToken, Long expiresIn, UserInfo userInfo) {
        super(accessToken, refreshToken, expiresIn);
        this.userInfo = userInfo;
        if (userInfo != null) {
            setUserId(userInfo.getUserId());
            setUsername(userInfo.getUsername());
            this.phone = userInfo.getPhone();
        }
    }

    /**
     * 失败响应构造函数
     *
     * @param errorCode        错误代码
     * @param errorDescription 错误描述
     */
    public SmsAuthenticationResponse(String errorCode, String errorDescription) {
        super(errorCode, errorDescription);
    }

    /**
     * 创建成功响应
     *
     * @param accessToken  访问令牌
     * @param refreshToken 刷新令牌
     * @param expiresIn    过期时间
     * @param userInfo     用户信息
     * @return 短信认证响应
     */
    public static SmsAuthenticationResponse success(String accessToken, String refreshToken, 
                                                   Long expiresIn, UserInfo userInfo) {
        return new SmsAuthenticationResponse(accessToken, refreshToken, expiresIn, userInfo);
    }

    /**
     * 创建成功响应（新用户）
     *
     * @param accessToken  访问令牌
     * @param refreshToken 刷新令牌
     * @param expiresIn    过期时间
     * @param userInfo     用户信息
     * @return 短信认证响应
     */
    public static SmsAuthenticationResponse successForNewUser(String accessToken, String refreshToken, 
                                                             Long expiresIn, UserInfo userInfo) {
        SmsAuthenticationResponse response = new SmsAuthenticationResponse(accessToken, refreshToken, expiresIn, userInfo);
        response.setNewUser(true);
        response.setFirstLogin(true);
        return response;
    }

    /**
     * 创建失败响应
     *
     * @param errorCode        错误代码
     * @param errorDescription 错误描述
     * @return 短信认证响应
     */
    public static SmsAuthenticationResponse failure(String errorCode, String errorDescription) {
        return new SmsAuthenticationResponse(errorCode, errorDescription);
    }

    /**
     * 创建手机号不存在响应
     *
     * @param phone 手机号
     * @return 短信认证响应
     */
    public static SmsAuthenticationResponse phoneNotFound(String phone) {
        return failure("PHONE_NOT_FOUND", "手机号未注册: " + maskPhone(phone));
    }

    /**
     * 创建验证码错误响应
     *
     * @return 短信认证响应
     */
    public static SmsAuthenticationResponse invalidSmsCode() {
        return failure("INVALID_SMS_CODE", "短信验证码错误或已过期");
    }

    /**
     * 创建验证码过期响应
     *
     * @return 短信认证响应
     */
    public static SmsAuthenticationResponse smsCodeExpired() {
        return failure("SMS_CODE_EXPIRED", "短信验证码已过期");
    }

    /**
     * 创建手机号格式错误响应
     *
     * @param phone 手机号
     * @return 短信认证响应
     */
    public static SmsAuthenticationResponse invalidPhoneFormat(String phone) {
        return failure("INVALID_PHONE_FORMAT", "手机号格式错误: " + phone);
    }

    /**
     * 创建用户被锁定响应
     *
     * @param phone 手机号
     * @return 短信认证响应
     */
    public static SmsAuthenticationResponse userLocked(String phone) {
        return failure("USER_LOCKED", "用户已被锁定: " + maskPhone(phone));
    }

    /**
     * 创建用户被禁用响应
     *
     * @param phone 手机号
     * @return 短信认证响应
     */
    public static SmsAuthenticationResponse userDisabled(String phone) {
        return failure("USER_DISABLED", "用户已被禁用: " + maskPhone(phone));
    }

    /**
     * 创建注册失败响应
     *
     * @param reason 失败原因
     * @return 短信认证响应
     */
    public static SmsAuthenticationResponse registrationFailed(String reason) {
        return failure("REGISTRATION_FAILED", "用户注册失败: " + reason);
    }

    /**
     * 掩码处理手机号
     *
     * @param phone 手机号
     * @return 掩码后的手机号
     */
    private static String maskPhone(String phone) {
        if (phone == null || phone.length() != 11) {
            return phone;
        }
        return phone.substring(0, 3) + "****" + phone.substring(7);
    }

    /**
     * 获取掩码后的手机号
     *
     * @return 掩码后的手机号
     */
    public String getMaskedPhone() {
        return maskPhone(phone);
    }

    /**
     * 设置用户信息并同步基础字段
     *
     * @param userInfo 用户信息
     */
    public void setUserInfo(UserInfo userInfo) {
        this.userInfo = userInfo;
        if (userInfo != null) {
            setUserId(userInfo.getUserId());
            setUsername(userInfo.getUsername());
            this.phone = userInfo.getPhone();
            this.lastLoginTime = userInfo.getLastLoginTime();
            this.lastLoginIp = userInfo.getLastLoginIp();
        }
    }

    @Override
    public String toString() {
        if (isSuccess()) {
            return String.format("SmsAuthenticationResponse{success=true, userId='%s', phone='%s', newUser=%s, firstLogin=%s}",
                    getUserId(), getMaskedPhone(), newUser, firstLogin);
        } else {
            return String.format("SmsAuthenticationResponse{success=false, errorCode='%s', errorDescription='%s'}",
                    getErrorCode(), getErrorDescription());
        }
    }
}
