package cn.com.handthing.starter.connector.toutiao.api;

import cn.com.handthing.starter.connector.toutiao.config.ToutiaoConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 今日头条数据分析API
 * <p>
 * 提供今日头条数据分析相关的API功能，包括内容数据、用户数据、收益数据等。
 * 支持个人创作者和企业用户的数据分析需求。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "handthing.connector.toutiao", name = "enabled", havingValue = "true")
public class ToutiaoAnalyticsApi {

    private final ToutiaoConfig config;
    private final RestTemplate restTemplate;

    /**
     * 获取内容数据统计
     *
     * @param accessToken 访问令牌
     * @param contentId   内容ID
     * @param startDate   开始日期
     * @param endDate     结束日期
     * @return 内容数据统计
     */
    public Map<String, Object> getContentStats(String accessToken, String contentId, LocalDate startDate, LocalDate endDate) {
        try {
            log.debug("Getting Toutiao content stats for content: {}", contentId);

            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(config.getEffectiveApiBaseUrl() + "/api/v2/data/content/")
                    .queryParam("access_token", accessToken)
                    .queryParam("content_id", contentId);

            if (startDate != null) {
                builder.queryParam("start_date", startDate.format(DateTimeFormatter.ISO_LOCAL_DATE));
            }
            if (endDate != null) {
                builder.queryParam("end_date", endDate.format(DateTimeFormatter.ISO_LOCAL_DATE));
            }

            String url = builder.build().toUriString();

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Void> request = new HttpEntity<>(headers);

            Map<String, Object> response = restTemplate.exchange(url, HttpMethod.GET, request, Map.class).getBody();
            log.debug("Toutiao content stats retrieved, response: {}", response);

            return response;

        } catch (Exception e) {
            log.error("Failed to get Toutiao content stats", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error_code", -1);
            errorResponse.put("description", "Failed to get content stats: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 获取用户数据统计
     *
     * @param accessToken 访问令牌
     * @param startDate   开始日期
     * @param endDate     结束日期
     * @return 用户数据统计
     */
    public Map<String, Object> getUserStats(String accessToken, LocalDate startDate, LocalDate endDate) {
        try {
            log.debug("Getting Toutiao user stats");

            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(config.getEffectiveApiBaseUrl() + "/api/v2/data/user/")
                    .queryParam("access_token", accessToken);

            if (startDate != null) {
                builder.queryParam("start_date", startDate.format(DateTimeFormatter.ISO_LOCAL_DATE));
            }
            if (endDate != null) {
                builder.queryParam("end_date", endDate.format(DateTimeFormatter.ISO_LOCAL_DATE));
            }

            String url = builder.build().toUriString();

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Void> request = new HttpEntity<>(headers);

            Map<String, Object> response = restTemplate.exchange(url, HttpMethod.GET, request, Map.class).getBody();
            log.debug("Toutiao user stats retrieved, response: {}", response);

            return response;

        } catch (Exception e) {
            log.error("Failed to get Toutiao user stats", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error_code", -1);
            errorResponse.put("description", "Failed to get user stats: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 获取收益数据
     *
     * @param accessToken 访问令牌
     * @param startDate   开始日期
     * @param endDate     结束日期
     * @return 收益数据
     */
    public Map<String, Object> getRevenueStats(String accessToken, LocalDate startDate, LocalDate endDate) {
        try {
            log.debug("Getting Toutiao revenue stats");

            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(config.getEffectiveApiBaseUrl() + "/api/v2/data/revenue/")
                    .queryParam("access_token", accessToken);

            if (startDate != null) {
                builder.queryParam("start_date", startDate.format(DateTimeFormatter.ISO_LOCAL_DATE));
            }
            if (endDate != null) {
                builder.queryParam("end_date", endDate.format(DateTimeFormatter.ISO_LOCAL_DATE));
            }

            String url = builder.build().toUriString();

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Void> request = new HttpEntity<>(headers);

            Map<String, Object> response = restTemplate.exchange(url, HttpMethod.GET, request, Map.class).getBody();
            log.debug("Toutiao revenue stats retrieved, response: {}", response);

            return response;

        } catch (Exception e) {
            log.error("Failed to get Toutiao revenue stats", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error_code", -1);
            errorResponse.put("description", "Failed to get revenue stats: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 获取热门内容排行
     *
     * @param accessToken 访问令牌
     * @param contentType 内容类型（article, video）
     * @param period      时间周期（day, week, month）
     * @param limit       返回数量限制
     * @return 热门内容排行
     */
    public Map<String, Object> getTrendingContent(String accessToken, String contentType, String period, Integer limit) {
        try {
            log.debug("Getting Toutiao trending content, type: {}, period: {}", contentType, period);

            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(config.getEffectiveApiBaseUrl() + "/api/v2/data/trending/")
                    .queryParam("access_token", accessToken);

            if (contentType != null && !contentType.trim().isEmpty()) {
                builder.queryParam("content_type", contentType);
            }
            if (period != null && !period.trim().isEmpty()) {
                builder.queryParam("period", period);
            }
            if (limit != null && limit > 0) {
                builder.queryParam("limit", limit);
            }

            String url = builder.build().toUriString();

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Void> request = new HttpEntity<>(headers);

            Map<String, Object> response = restTemplate.exchange(url, HttpMethod.GET, request, Map.class).getBody();
            log.debug("Toutiao trending content retrieved, response: {}", response);

            return response;

        } catch (Exception e) {
            log.error("Failed to get Toutiao trending content", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error_code", -1);
            errorResponse.put("description", "Failed to get trending content: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 获取粉丝画像数据
     *
     * @param accessToken 访问令牌
     * @return 粉丝画像数据
     */
    public Map<String, Object> getFansProfile(String accessToken) {
        try {
            log.debug("Getting Toutiao fans profile");

            String url = UriComponentsBuilder.fromHttpUrl(config.getEffectiveApiBaseUrl() + "/api/v2/data/fans/profile/")
                    .queryParam("access_token", accessToken)
                    .build()
                    .toUriString();

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Void> request = new HttpEntity<>(headers);

            Map<String, Object> response = restTemplate.exchange(url, HttpMethod.GET, request, Map.class).getBody();
            log.debug("Toutiao fans profile retrieved, response: {}", response);

            return response;

        } catch (Exception e) {
            log.error("Failed to get Toutiao fans profile", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error_code", -1);
            errorResponse.put("description", "Failed to get fans profile: " + e.getMessage());
            return errorResponse;
        }
    }
}
