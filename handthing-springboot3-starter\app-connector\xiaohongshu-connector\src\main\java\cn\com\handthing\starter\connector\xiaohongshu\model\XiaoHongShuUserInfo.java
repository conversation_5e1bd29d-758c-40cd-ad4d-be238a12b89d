package cn.com.handthing.starter.connector.xiaohongshu.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 小红书用户信息
 * <p>
 * 小红书OAuth2.0认证获取用户信息的响应数据模型。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
public class XiaoHongShuUserInfo {

    /**
     * 状态码，200表示成功
     */
    @JsonProperty("code")
    private Integer code;

    /**
     * 响应消息
     */
    @JsonProperty("message")
    private String message;

    /**
     * 响应数据
     */
    @JsonProperty("data")
    private UserData data;

    /**
     * 检查响应是否成功
     *
     * @return 如果成功返回true，否则返回false
     */
    public boolean isSuccess() {
        return code != null && code == 200;
    }

    /**
     * 获取错误信息
     *
     * @return 错误信息
     */
    public String getErrorMessage() {
        if (isSuccess()) {
            return null;
        }
        return String.format("Code %d: %s", code, message);
    }

    /**
     * 用户数据
     */
    @Data
    public static class UserData {

        /**
         * 用户唯一标识
         */
        @JsonProperty("open_id")
        private String openId;

        /**
         * 用户统一标识
         */
        @JsonProperty("union_id")
        private String unionId;

        /**
         * 用户昵称
         */
        @JsonProperty("nickname")
        private String nickname;

        /**
         * 用户头像URL
         */
        @JsonProperty("avatar")
        private String avatar;

        /**
         * 用户性别，1-男，2-女，0-未知
         */
        @JsonProperty("gender")
        private Integer gender;

        /**
         * 国家
         */
        @JsonProperty("country")
        private String country;

        /**
         * 省份
         */
        @JsonProperty("province")
        private String province;

        /**
         * 城市
         */
        @JsonProperty("city")
        private String city;

        /**
         * 用户类型
         * 1: 普通用户
         * 2: 认证用户
         * 3: 品牌方
         */
        @JsonProperty("user_type")
        private Integer userType;

        /**
         * 是否为认证用户
         */
        @JsonProperty("is_verified")
        private Boolean isVerified;

        /**
         * 粉丝数量
         */
        @JsonProperty("followers_count")
        private Long followersCount;

        /**
         * 关注数量
         */
        @JsonProperty("following_count")
        private Long followingCount;

        /**
         * 笔记数量
         */
        @JsonProperty("notes_count")
        private Long notesCount;

        /**
         * 获赞数量
         */
        @JsonProperty("likes_count")
        private Long likesCount;

        /**
         * 个人简介
         */
        @JsonProperty("bio")
        private String bio;

        /**
         * 获取性别描述
         *
         * @return 性别描述
         */
        public String getGenderDescription() {
            if (gender == null) {
                return "未知";
            }
            switch (gender) {
                case 1:
                    return "男";
                case 2:
                    return "女";
                default:
                    return "未知";
            }
        }

        /**
         * 获取用户类型描述
         *
         * @return 用户类型描述
         */
        public String getUserTypeDescription() {
            if (userType == null) {
                return "未知";
            }
            switch (userType) {
                case 1:
                    return "普通用户";
                case 2:
                    return "认证用户";
                case 3:
                    return "品牌方";
                default:
                    return "未知";
            }
        }

        /**
         * 获取完整地址
         *
         * @return 完整地址
         */
        public String getFullAddress() {
            StringBuilder address = new StringBuilder();
            if (country != null && !country.trim().isEmpty()) {
                address.append(country);
            }
            if (province != null && !province.trim().isEmpty()) {
                if (address.length() > 0) {
                    address.append(" ");
                }
                address.append(province);
            }
            if (city != null && !city.trim().isEmpty()) {
                if (address.length() > 0) {
                    address.append(" ");
                }
                address.append(city);
            }
            return address.toString();
        }
    }
}
