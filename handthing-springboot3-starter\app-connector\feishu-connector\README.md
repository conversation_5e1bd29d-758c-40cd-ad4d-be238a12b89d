# 飞书连接器 (<PERSON><PERSON><PERSON> Connector)

## 概述

飞书连接器提供了与飞书开放平台的集成能力，支持企业内部应用和第三方应用的开发。通过统一的API接口，可以轻松实现用户认证、消息发送、文档协作、通讯录管理等功能。

## 特性

- ✅ **OAuth2.0认证** - 支持飞书用户授权登录
- ✅ **消息发送** - 支持文本、富文本、卡片、图片等消息类型
- ✅ **通讯录管理** - 获取用户信息、部门信息、组织架构
- ✅ **文档协作** - 支持文档、表格、演示文稿的操作
- ✅ **机器人支持** - 支持群机器人和应用机器人
- ✅ **海外版支持** - 支持Lark(海外版飞书)
- ✅ **多环境支持** - 支持开发、测试、生产环境配置

## 快速开始

### 1. 添加依赖

```xml
<dependency>
    <groupId>cn.com.handthing.springboot3.starter</groupId>
    <artifactId>feishu-connector</artifactId>
    <version>1.0.0-SNAPSHOT</version>
</dependency>
```

### 2. 配置文件

```yaml
handthing:
  connector:
    feishu:
      enabled: true
      app-id: "your-app-id"
      app-secret: "your-app-secret"
      app-type: "internal"  # internal/third_party
      third-party-mode: false
      bot-enabled: false
      bot-secret: "your-bot-secret"
      lark-mode: false
      encrypt-message: false
      encrypt-key: "your-encrypt-key"
      verification-token: "your-verification-token"
      redirect-uri: "http://your-domain.com/callback/feishu"
```

### 3. 基本使用

```java
@RestController
public class FeishuController {
    
    @Autowired
    private FeishuService feishuService;
    
    @Autowired
    private AuthService authService;
    
    // 获取授权URL
    @GetMapping("/feishu/auth")
    public String getAuthUrl() {
        return authService.getAuthorizationUrl(
            PlatformType.FEISHU, 
            "state", 
            "http://your-domain.com/callback/feishu"
        );
    }
    
    // 发送文本消息
    @PostMapping("/feishu/message/text")
    public Map<String, Object> sendTextMessage(@RequestParam String accessToken,
                                              @RequestParam String receiveId,
                                              @RequestParam String content) {
        return feishuService.message().sendTextMessage(accessToken, receiveId, "open_id", content);
    }
    
    // 发送富文本消息
    @PostMapping("/feishu/message/rich")
    public Map<String, Object> sendRichTextMessage(@RequestParam String accessToken,
                                                   @RequestParam String receiveId,
                                                   @RequestParam String title,
                                                   @RequestParam String content) {
        Map<String, Object> richText = feishuService.message().createSimpleRichText(title, content);
        return feishuService.message().sendRichTextMessage(accessToken, receiveId, "open_id", richText);
    }
    
    // 获取用户信息
    @GetMapping("/feishu/user/{userId}")
    public Map<String, Object> getUserInfo(@RequestParam String accessToken,
                                          @PathVariable String userId) {
        return feishuService.user().getUserInfo(accessToken, userId, "open_id");
    }
}
```

## 配置说明

| 配置项 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| `enabled` | Boolean | 是 | false | 是否启用飞书连接器 |
| `app-id` | String | 是 | - | 飞书应用ID |
| `app-secret` | String | 是 | - | 飞书应用密钥 |
| `app-type` | String | 否 | internal | 应用类型(internal/third_party) |
| `third-party-mode` | Boolean | 否 | false | 是否为第三方应用模式 |
| `bot-enabled` | Boolean | 否 | false | 是否启用机器人功能 |
| `bot-secret` | String | 否 | - | 机器人密钥 |
| `lark-mode` | Boolean | 否 | false | 是否为海外版飞书模式 |
| `encrypt-message` | Boolean | 否 | false | 是否启用消息加密 |
| `encrypt-key` | String | 否 | - | 消息加密密钥 |
| `verification-token` | String | 否 | - | 消息验证Token |
| `redirect-uri` | String | 否 | - | OAuth回调地址 |

## API文档

### 消息API (FeishuMessageApi)

#### 发送文本消息
```java
Map<String, Object> sendTextMessage(String accessToken, String receiveId, String receiveIdType, String content)
```

#### 发送富文本消息
```java
Map<String, Object> sendRichTextMessage(String accessToken, String receiveId, String receiveIdType, Map<String, Object> richText)
```

#### 发送卡片消息
```java
Map<String, Object> sendCardMessage(String accessToken, String receiveId, String receiveIdType, Map<String, Object> cardContent)
```

#### 发送图片消息
```java
Map<String, Object> sendImageMessage(String accessToken, String receiveId, String receiveIdType, String imageKey)
```

### 用户API (FeishuUserApi)

#### 获取用户信息
```java
Map<String, Object> getUserInfo(String accessToken, String userId, String userIdType)
```

#### 获取用户列表
```java
Map<String, Object> getUserList(String accessToken, String departmentId, Integer pageSize, String pageToken)
```

#### 获取部门信息
```java
Map<String, Object> getDepartmentInfo(String accessToken, String departmentId)
```

#### 获取部门列表
```java
Map<String, Object> getDepartmentList(String accessToken, String parentDepartmentId, Boolean fetchChild, Integer pageSize, String pageToken)
```

## 常见问题

### Q: 如何获取飞书的AppId和AppSecret？
A: 登录飞书开放平台，在"应用管理"中创建应用，即可获取AppId和AppSecret。

### Q: 企业内部应用和第三方应用有什么区别？
A: 企业内部应用只能在本企业内使用，第三方应用可以发布到应用商店供其他企业安装。

### Q: 如何使用海外版飞书(Lark)？
A: 设置lark-mode为true，系统会自动切换到Lark的API地址。

### Q: 如何处理消息加密？
A: 启用encrypt-message并配置encrypt-key，系统会自动处理消息的加密和解密。

## 更多信息

- [飞书开放平台文档](https://open.feishu.cn/document/)
- [消息与群组API](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message/create)
- [通讯录API](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/contact-v3/user/get)
