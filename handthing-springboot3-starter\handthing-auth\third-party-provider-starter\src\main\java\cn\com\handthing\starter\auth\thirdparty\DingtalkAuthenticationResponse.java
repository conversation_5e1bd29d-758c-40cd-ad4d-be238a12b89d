package cn.com.handthing.starter.auth.thirdparty;

import cn.com.handthing.starter.auth.core.AuthenticationResponse;
import cn.com.handthing.starter.auth.core.UserInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 钉钉认证响应
 * <p>
 * 钉钉认证的响应对象，包含认证结果、用户信息、令牌信息等。
 * 支持新用户注册和老用户登录的不同响应信息。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DingtalkAuthenticationResponse extends AuthenticationResponse {

    /**
     * 用户信息
     */
    private UserInfo userInfo;

    /**
     * 是否为新注册用户
     */
    private Boolean newUser;

    /**
     * 钉钉用户ID
     */
    private String dingtalkUserId;

    /**
     * 钉钉用户名
     */
    private String dingtalkUsername;

    /**
     * 钉钉头像
     */
    private String dingtalkAvatar;

    /**
     * 应用Key
     */
    private String appKey;

    /**
     * 企业ID
     */
    private String corpId;

    /**
     * 最后登录时间
     */
    private LocalDateTime lastLoginTime;

    /**
     * 最后登录IP
     */
    private String lastLoginIp;

    /**
     * 是否为首次登录
     */
    private Boolean firstLogin;

    /**
     * 默认构造函数
     */
    public DingtalkAuthenticationResponse() {
        super();
    }

    /**
     * 成功响应构造函数
     *
     * @param accessToken  访问令牌
     * @param refreshToken 刷新令牌
     * @param expiresIn    过期时间
     * @param userInfo     用户信息
     */
    public DingtalkAuthenticationResponse(String accessToken, String refreshToken, Long expiresIn, UserInfo userInfo) {
        super(accessToken, refreshToken, expiresIn);
        this.userInfo = userInfo;
        if (userInfo != null) {
            setUserId(userInfo.getUserId());
            setUsername(userInfo.getUsername());
        }
    }

    /**
     * 失败响应构造函数
     *
     * @param errorCode        错误代码
     * @param errorDescription 错误描述
     */
    public DingtalkAuthenticationResponse(String errorCode, String errorDescription) {
        super(errorCode, errorDescription);
    }

    /**
     * 创建成功响应
     *
     * @param accessToken  访问令牌
     * @param refreshToken 刷新令牌
     * @param expiresIn    过期时间
     * @param userInfo     用户信息
     * @return 钉钉认证响应
     */
    public static DingtalkAuthenticationResponse success(String accessToken, String refreshToken, 
                                                        Long expiresIn, UserInfo userInfo) {
        return new DingtalkAuthenticationResponse(accessToken, refreshToken, expiresIn, userInfo);
    }

    /**
     * 创建成功响应（新用户）
     *
     * @param accessToken  访问令牌
     * @param refreshToken 刷新令牌
     * @param expiresIn    过期时间
     * @param userInfo     用户信息
     * @return 钉钉认证响应
     */
    public static DingtalkAuthenticationResponse successForNewUser(String accessToken, String refreshToken, 
                                                                  Long expiresIn, UserInfo userInfo) {
        DingtalkAuthenticationResponse response = new DingtalkAuthenticationResponse(accessToken, refreshToken, expiresIn, userInfo);
        response.setNewUser(true);
        response.setFirstLogin(true);
        return response;
    }

    /**
     * 创建失败响应
     *
     * @param errorCode        错误代码
     * @param errorDescription 错误描述
     * @return 钉钉认证响应
     */
    public static DingtalkAuthenticationResponse failure(String errorCode, String errorDescription) {
        return new DingtalkAuthenticationResponse(errorCode, errorDescription);
    }

    /**
     * 创建授权码无效响应
     *
     * @return 钉钉认证响应
     */
    public static DingtalkAuthenticationResponse invalidCode() {
        return failure("INVALID_DINGTALK_CODE", "钉钉授权码无效或已过期");
    }

    /**
     * 创建应用配置错误响应
     *
     * @param appKey 应用Key
     * @return 钉钉认证响应
     */
    public static DingtalkAuthenticationResponse invalidAppConfig(String appKey) {
        return failure("INVALID_APP_CONFIG", "钉钉应用配置错误: " + appKey);
    }

    /**
     * 创建用户不存在响应
     *
     * @param dingtalkUserId 钉钉用户ID
     * @return 钉钉认证响应
     */
    public static DingtalkAuthenticationResponse userNotFound(String dingtalkUserId) {
        return failure("DINGTALK_USER_NOT_FOUND", "钉钉用户不存在: " + dingtalkUserId);
    }

    /**
     * 创建用户被锁定响应
     *
     * @param dingtalkUserId 钉钉用户ID
     * @return 钉钉认证响应
     */
    public static DingtalkAuthenticationResponse userLocked(String dingtalkUserId) {
        return failure("USER_LOCKED", "用户已被锁定: " + dingtalkUserId);
    }

    /**
     * 创建用户被禁用响应
     *
     * @param dingtalkUserId 钉钉用户ID
     * @return 钉钉认证响应
     */
    public static DingtalkAuthenticationResponse userDisabled(String dingtalkUserId) {
        return failure("USER_DISABLED", "用户已被禁用: " + dingtalkUserId);
    }

    /**
     * 创建注册失败响应
     *
     * @param reason 失败原因
     * @return 钉钉认证响应
     */
    public static DingtalkAuthenticationResponse registrationFailed(String reason) {
        return failure("REGISTRATION_FAILED", "用户注册失败: " + reason);
    }

    /**
     * 创建API调用失败响应
     *
     * @param reason 失败原因
     * @return 钉钉认证响应
     */
    public static DingtalkAuthenticationResponse apiCallFailed(String reason) {
        return failure("DINGTALK_API_FAILED", "钉钉API调用失败: " + reason);
    }

    /**
     * 设置用户信息并同步基础字段
     *
     * @param userInfo 用户信息
     */
    public void setUserInfo(UserInfo userInfo) {
        this.userInfo = userInfo;
        if (userInfo != null) {
            setUserId(userInfo.getUserId());
            setUsername(userInfo.getUsername());
            this.lastLoginTime = userInfo.getLastLoginTime();
            this.lastLoginIp = userInfo.getLastLoginIp();
        }
    }

    /**
     * 设置钉钉用户信息
     *
     * @param dingtalkUserId   钉钉用户ID
     * @param dingtalkUsername 钉钉用户名
     * @param dingtalkAvatar   钉钉头像
     * @param appKey           应用Key
     * @param corpId           企业ID
     */
    public void setDingtalkUserInfo(String dingtalkUserId, String dingtalkUsername, String dingtalkAvatar, 
                                   String appKey, String corpId) {
        this.dingtalkUserId = dingtalkUserId;
        this.dingtalkUsername = dingtalkUsername;
        this.dingtalkAvatar = dingtalkAvatar;
        this.appKey = appKey;
        this.corpId = corpId;
    }

    @Override
    public String toString() {
        if (isSuccess()) {
            return String.format("DingtalkAuthenticationResponse{success=true, userId='%s', dingtalkUserId='%s', appKey='%s', newUser=%s, firstLogin=%s}",
                    getUserId(), dingtalkUserId, appKey, newUser, firstLogin);
        } else {
            return String.format("DingtalkAuthenticationResponse{success=false, errorCode='%s', errorDescription='%s'}",
                    getErrorCode(), getErrorDescription());
        }
    }
}
