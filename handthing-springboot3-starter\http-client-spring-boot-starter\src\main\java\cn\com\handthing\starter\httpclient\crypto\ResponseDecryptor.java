package cn.com.handthing.starter.httpclient.crypto;

import org.springframework.http.client.ClientHttpResponse;
import org.springframework.lang.NonNull;

import java.io.IOException;
import java.io.InputStream;

/**
 * 响应解密器接口。
 * <p>
 * 实现此接口以定义在收到HTTP响应后对其进行解密的逻辑。
 * Starter会自动查找Spring上下文中此接口的Bean，并在相关端点配置启用加密时应用它。
 */
public interface ResponseDecryptor {

    /**
     * 对给定的HTTP响应执行解密操作。
     * <p>
     * 此方法通常会读取原始响应的输入流，进行解密，然后返回一个新的包含解密后数据的输入流。
     *
     * @param response 原始的、可能被加密的HTTP响应。
     * @return 包含解密后数据的输入流。如果响应未被加密或无需解密，应返回原始的响应输入流。
     * @throws IOException 如果在读取或处理流时发生I/O错误。
     */
    @NonNull
    InputStream decrypt(@NonNull ClientHttpResponse response) throws IOException;
}