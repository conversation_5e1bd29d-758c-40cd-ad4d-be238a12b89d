package cn.com.handthing.starter.datalayer.entity;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * BaseEntity测试
 *
 * <AUTHOR>
 * @since V1.0.0
 */
class BaseEntityTest {
    
    /**
     * 测试实体类
     */
    static class TestEntity extends BaseEntity {
        private Long id;
        private String name;
        
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
    }
    
    @Test
    @DisplayName("新实体应该被正确识别")
    void shouldIdentifyNewEntity() {
        // Given
        TestEntity entity = new TestEntity();
        
        // When & Then
        assertThat(entity.isNew()).isTrue();
        assertThat(entity.getCreateTime()).isNull();
    }
    
    @Test
    @DisplayName("已存在实体应该被正确识别")
    void shouldIdentifyExistingEntity() {
        // Given
        TestEntity entity = new TestEntity();
        entity.setCreateTime(LocalDateTime.now());
        
        // When & Then
        assertThat(entity.isNew()).isFalse();
        assertThat(entity.getCreateTime()).isNotNull();
    }
    
    @Test
    @DisplayName("应该正确识别删除状态")
    void shouldIdentifyDeletedStatus() {
        // Given
        TestEntity entity = new TestEntity();
        
        // When & Then - 未删除
        entity.setDeleted(0);
        assertThat(entity.isDeleted()).isFalse();
        
        // When & Then - 已删除
        entity.setDeleted(1);
        assertThat(entity.isDeleted()).isTrue();
        
        // When & Then - null状态
        entity.setDeleted(null);
        assertThat(entity.isDeleted()).isFalse();
    }
    
    @Test
    @DisplayName("应该生成正确的审计摘要")
    void shouldGenerateCorrectAuditSummary() {
        // Given
        TestEntity entity = new TestEntity();
        LocalDateTime now = LocalDateTime.now();
        
        entity.setCreateBy("user1");
        entity.setCreateTime(now);
        entity.setUpdateBy("user2");
        entity.setUpdateTime(now);
        entity.setVersion(1L);
        entity.setDeleted(0);
        
        // When
        String summary = entity.getAuditSummary();
        
        // Then
        assertThat(summary).contains("createBy=user1");
        assertThat(summary).contains("updateBy=user2");
        assertThat(summary).contains("version=1");
        assertThat(summary).contains("deleted=0");
        assertThat(summary).startsWith("Audit{");
        assertThat(summary).endsWith("}");
    }
    
    @Test
    @DisplayName("空审计信息应该生成基本摘要")
    void shouldGenerateBasicSummaryForEmptyAudit() {
        // Given
        TestEntity entity = new TestEntity();
        
        // When
        String summary = entity.getAuditSummary();
        
        // Then
        assertThat(summary).isEqualTo("Audit{}");
    }
    
    @Test
    @DisplayName("部分审计信息应该生成正确摘要")
    void shouldGenerateCorrectSummaryForPartialAudit() {
        // Given
        TestEntity entity = new TestEntity();
        entity.setCreateBy("user1");
        entity.setVersion(2L);
        
        // When
        String summary = entity.getAuditSummary();
        
        // Then
        assertThat(summary).contains("createBy=user1");
        assertThat(summary).contains("version=2");
        assertThat(summary).doesNotContain("createTime");
        assertThat(summary).doesNotContain("updateBy");
    }
    
    @Test
    @DisplayName("预处理方法应该可以被调用")
    void shouldCallPreProcessMethods() {
        // Given
        TestEntity entity = new TestEntity();
        
        // When & Then - 应该不抛出异常
        entity.preInsert();
        entity.preUpdate();
        entity.postInsert();
        entity.postUpdate();
    }
    
    @Test
    @DisplayName("实体应该实现Serializable")
    void shouldImplementSerializable() {
        // Given
        TestEntity entity = new TestEntity();
        
        // When & Then
        assertThat(entity).isInstanceOf(java.io.Serializable.class);
    }
    
    @Test
    @DisplayName("equals和hashCode应该正常工作")
    void shouldWorkWithEqualsAndHashCode() {
        // Given
        TestEntity entity1 = new TestEntity();
        TestEntity entity2 = new TestEntity();
        
        entity1.setCreateBy("user1");
        entity1.setVersion(1L);
        
        entity2.setCreateBy("user1");
        entity2.setVersion(1L);
        
        // When & Then
        assertThat(entity1).isEqualTo(entity2);
        assertThat(entity1.hashCode()).isEqualTo(entity2.hashCode());
    }
    
    @Test
    @DisplayName("toString应该包含基础信息")
    void shouldIncludeBasicInfoInToString() {
        // Given
        TestEntity entity = new TestEntity();
        entity.setCreateBy("user1");

        // When
        String toString = entity.toString();

        // Then
        assertThat(toString).contains("BaseEntity"); // Lombok生成的toString使用父类名
        assertThat(toString).contains("createBy");
        assertThat(toString).contains("user1");
    }
}
