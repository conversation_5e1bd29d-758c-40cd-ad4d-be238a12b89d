# HandThing认证系统最佳实践指南

## 🛡️ 安全最佳实践

### 1. JWT Token安全

#### 强密钥管理
```yaml
handthing:
  auth:
    jwt:
      # 使用环境变量存储密钥
      secret: ${JWT_SECRET}
      # 或使用随机生成的强密钥
      secret: ${JWT_SECRET:$(openssl rand -base64 32)}
```

#### 合理的过期时间
```yaml
handthing:
  auth:
    jwt:
      # 短期access token
      access-token-expiration: 7200    # 2小时
      # 长期refresh token
      refresh-token-expiration: 604800 # 7天
      # 生产环境建议更短
      # access-token-expiration: 1800  # 30分钟
```

#### Token刷新策略
```javascript
class TokenManager {
    static async refreshTokenIfNeeded() {
        const token = this.getAccessToken();
        
        // 提前5分钟刷新token
        if (this.willExpireSoon(token, 300)) {
            try {
                await this.refreshToken();
            } catch (error) {
                // 刷新失败，跳转登录
                this.clearTokens();
                window.location.href = '/login';
            }
        }
    }
    
    static willExpireSoon(token, seconds) {
        if (!token) return true;
        
        try {
            const payload = JSON.parse(atob(token.split('.')[1]));
            const expirationTime = payload.exp * 1000;
            const currentTime = Date.now();
            const timeUntilExpiration = expirationTime - currentTime;
            
            return timeUntilExpiration < (seconds * 1000);
        } catch (e) {
            return true;
        }
    }
}
```

### 2. 认证过滤器配置

#### 精确的排除路径
```yaml
handthing:
  auth:
    filter:
      exclude-paths:
        # 登录相关
        - "/login"
        - "/auth/login"
        - "/auth/sms/send"
        - "/auth/*/auth-url"
        - "/*/callback"
        
        # 公共资源
        - "/public/**"
        - "/static/**"
        - "/css/**"
        - "/js/**"
        - "/images/**"
        - "/favicon.ico"
        
        # 健康检查
        - "/actuator/health"
        - "/actuator/info"
        
        # API文档
        - "/swagger-ui/**"
        - "/v3/api-docs/**"
        
        # 错误页面
        - "/error"
        - "/"
```

#### 安全头设置
```java
@Configuration
public class SecurityConfig {
    
    @Bean
    public FilterRegistrationBean<SecurityHeadersFilter> securityHeadersFilter() {
        FilterRegistrationBean<SecurityHeadersFilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(new SecurityHeadersFilter());
        registration.addUrlPatterns("/*");
        registration.setOrder(Ordered.HIGHEST_PRECEDENCE);
        return registration;
    }
}

public class SecurityHeadersFilter implements Filter {
    
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, 
                        FilterChain chain) throws IOException, ServletException {
        
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        
        // 防止XSS攻击
        httpResponse.setHeader("X-Content-Type-Options", "nosniff");
        httpResponse.setHeader("X-Frame-Options", "DENY");
        httpResponse.setHeader("X-XSS-Protection", "1; mode=block");
        
        // HTTPS强制
        httpResponse.setHeader("Strict-Transport-Security", 
            "max-age=31536000; includeSubDomains");
        
        // CSP策略
        httpResponse.setHeader("Content-Security-Policy", 
            "default-src 'self'; script-src 'self' 'unsafe-inline'");
        
        chain.doFilter(request, response);
    }
}
```

### 3. 密码安全

#### 强密码策略
```java
@Component
public class PasswordValidator {
    
    private static final String PASSWORD_PATTERN = 
        "^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[@#$%^&+=])(?=\\S+$).{8,}$";
    
    private static final Pattern pattern = Pattern.compile(PASSWORD_PATTERN);
    
    public boolean isValid(String password) {
        return pattern.matcher(password).matches();
    }
    
    public List<String> getPasswordRequirements() {
        return Arrays.asList(
            "至少8个字符",
            "包含至少一个数字",
            "包含至少一个小写字母",
            "包含至少一个大写字母",
            "包含至少一个特殊字符(@#$%^&+=)",
            "不能包含空格"
        );
    }
}
```

#### 密码加密存储
```java
@Service
public class PasswordService {
    
    private final PasswordEncoder passwordEncoder = new BCryptPasswordEncoder(12);
    
    public String encodePassword(String rawPassword) {
        return passwordEncoder.encode(rawPassword);
    }
    
    public boolean matches(String rawPassword, String encodedPassword) {
        return passwordEncoder.matches(rawPassword, encodedPassword);
    }
}
```

## 🚀 性能最佳实践

### 1. 缓存策略

#### 多级缓存配置
```yaml
handthing:
  auth:
    cache:
      enabled: true
      # 本地缓存配置
      caffeine:
        maximum-size: 10000
        expire-after-write: 300s
        expire-after-access: 300s
      
      # Redis分布式缓存
      redis:
        enabled: true
        host: ${REDIS_HOST:localhost}
        port: ${REDIS_PORT:6379}
        database: 0
        timeout: 2000ms
        pool:
          max-active: 20
          max-idle: 10
          min-idle: 5
```

#### 缓存键策略
```java
@Component
public class CacheKeyGenerator {
    
    public String generateUserCacheKey(String username) {
        return "auth:user:" + username;
    }
    
    public String generateTokenCacheKey(String tokenId) {
        return "auth:token:" + tokenId;
    }
    
    public String generateSmsCodeCacheKey(String phone, String codeType) {
        return "auth:sms:" + phone + ":" + codeType;
    }
}
```

### 2. 连接池优化

#### 数据库连接池
```yaml
spring:
  datasource:
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      leak-detection-threshold: 60000
```

#### Redis连接池
```yaml
spring:
  data:
    redis:
      lettuce:
        pool:
          max-active: 20
          max-idle: 10
          min-idle: 5
          max-wait: 2000ms
```

### 3. 异步处理

#### 异步事件处理
```java
@Configuration
@EnableAsync
public class AsyncConfig {
    
    @Bean(name = "authEventExecutor")
    public Executor authEventExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("auth-event-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }
}

@Component
public class AsyncAuthenticationEventListener {
    
    @Async("authEventExecutor")
    @EventListener
    public void handleAuthenticationSuccess(AuthenticationSuccessEvent event) {
        // 异步处理认证成功事件
        log.info("异步处理认证成功事件: {}", event.getUsername());
        
        // 更新用户最后登录时间
        userService.updateLastLoginTime(event.getUsername());
        
        // 记录登录日志
        auditService.recordLoginEvent(event);
    }
}
```

## 📊 监控最佳实践

### 1. 指标监控

#### 自定义指标
```java
@Component
public class AuthenticationMetrics {
    
    private final MeterRegistry meterRegistry;
    private final Counter authSuccessCounter;
    private final Counter authFailureCounter;
    private final Timer authDurationTimer;
    private final Gauge activeSessionsGauge;
    
    public AuthenticationMetrics(MeterRegistry meterRegistry, SessionManager sessionManager) {
        this.meterRegistry = meterRegistry;
        
        this.authSuccessCounter = Counter.builder("handthing.auth.success")
            .description("认证成功次数")
            .register(meterRegistry);
            
        this.authFailureCounter = Counter.builder("handthing.auth.failure")
            .description("认证失败次数")
            .register(meterRegistry);
            
        this.authDurationTimer = Timer.builder("handthing.auth.duration")
            .description("认证耗时")
            .register(meterRegistry);
            
        this.activeSessionsGauge = Gauge.builder("handthing.auth.active.sessions")
            .description("活跃会话数")
            .register(meterRegistry, sessionManager, SessionManager::getActiveSessionCount);
    }
    
    @EventListener
    public void handleAuthenticationSuccess(AuthenticationSuccessEvent event) {
        authSuccessCounter.increment(
            Tags.of("grant_type", event.getGrantType().getCode(),
                   "user_type", event.getUserInfo().getUserType()));
        
        authDurationTimer.record(event.getDuration(), TimeUnit.MILLISECONDS);
    }
    
    @EventListener
    public void handleAuthenticationFailure(AuthenticationFailedEvent event) {
        authFailureCounter.increment(
            Tags.of("grant_type", event.getGrantType().getCode(),
                   "error_code", event.getErrorCode(),
                   "ip_address", getIpRegion(event.getIpAddress())));
    }
}
```

#### 健康检查
```java
@Component
public class AuthenticationHealthIndicator implements HealthIndicator {
    
    private final ProviderRegistry providerRegistry;
    private final CacheService cacheService;
    
    @Override
    public Health health() {
        Health.Builder builder = new Health.Builder();
        
        try {
            // 检查认证提供者
            List<AuthenticationProvider> providers = providerRegistry.getProviders();
            builder.withDetail("providers.count", providers.size());
            builder.withDetail("providers.types", 
                providers.stream()
                    .map(p -> p.getClass().getSimpleName())
                    .collect(Collectors.toList()));
            
            // 检查缓存服务
            boolean cacheHealthy = cacheService.isHealthy();
            builder.withDetail("cache.healthy", cacheHealthy);
            
            // 检查JWT配置
            boolean jwtConfigured = jwtTokenValidator.isConfigured();
            builder.withDetail("jwt.configured", jwtConfigured);
            
            if (providers.isEmpty() || !cacheHealthy || !jwtConfigured) {
                return builder.down().build();
            }
            
            return builder.up().build();
            
        } catch (Exception e) {
            return builder.down(e).build();
        }
    }
}
```

### 2. 日志最佳实践

#### 结构化日志
```java
@Component
@Slf4j
public class AuthenticationLogger {
    
    private final ObjectMapper objectMapper;
    
    @EventListener
    public void logAuthenticationEvent(AuthenticationEvent event) {
        try {
            Map<String, Object> logData = new HashMap<>();
            logData.put("event_type", event.getClass().getSimpleName());
            logData.put("username", event.getUsername());
            logData.put("grant_type", event.getGrantType().getCode());
            logData.put("ip_address", event.getIpAddress());
            logData.put("user_agent", event.getUserAgent());
            logData.put("timestamp", event.getTimestamp());
            
            if (event instanceof AuthenticationSuccessEvent) {
                AuthenticationSuccessEvent successEvent = (AuthenticationSuccessEvent) event;
                logData.put("duration_ms", successEvent.getDuration());
                logData.put("user_id", successEvent.getUserInfo().getUserId());
            } else if (event instanceof AuthenticationFailedEvent) {
                AuthenticationFailedEvent failedEvent = (AuthenticationFailedEvent) event;
                logData.put("error_code", failedEvent.getErrorCode());
                logData.put("error_message", failedEvent.getErrorMessage());
                logData.put("duration_ms", failedEvent.getDuration());
            }
            
            String logJson = objectMapper.writeValueAsString(logData);
            
            if (event instanceof AuthenticationFailedEvent) {
                log.warn("Authentication event: {}", logJson);
            } else {
                log.info("Authentication event: {}", logJson);
            }
            
        } catch (Exception e) {
            log.error("Failed to log authentication event", e);
        }
    }
}
```

#### 敏感信息脱敏
```java
@Component
public class LogSanitizer {
    
    public String sanitizeUsername(String username) {
        if (username == null || username.length() <= 3) {
            return "***";
        }
        return username.substring(0, 2) + "***" + username.substring(username.length() - 1);
    }
    
    public String sanitizePhone(String phone) {
        if (phone == null || phone.length() <= 7) {
            return "***";
        }
        return phone.substring(0, 3) + "****" + phone.substring(phone.length() - 4);
    }
    
    public String sanitizeIpAddress(String ipAddress) {
        if (ipAddress == null) {
            return "unknown";
        }
        
        String[] parts = ipAddress.split("\\.");
        if (parts.length == 4) {
            return parts[0] + "." + parts[1] + ".***." + parts[3];
        }
        
        return "***";
    }
}
```

## 🔄 部署最佳实践

### 1. 环境配置

#### 生产环境配置
```yaml
# application-prod.yml
handthing:
  auth:
    jwt:
      secret: ${JWT_SECRET}  # 从环境变量获取
      access-token-expiration: 1800   # 30分钟
      refresh-token-expiration: 86400 # 1天
    
    filter:
      exclude-paths:
        - "/actuator/health"  # 只暴露必要的健康检查
    
    cache:
      type: redis
      redis:
        host: ${REDIS_HOST}
        port: ${REDIS_PORT}
        password: ${REDIS_PASSWORD}
        ssl: true
        
logging:
  level:
    cn.com.handthing.starter.auth: INFO  # 生产环境使用INFO级别
    org.springframework.security: WARN
```

#### 容器化部署
```dockerfile
FROM openjdk:21-jre-slim

# 创建应用用户
RUN groupadd -r appuser && useradd -r -g appuser appuser

# 复制应用
COPY target/app.jar /app/app.jar
RUN chown appuser:appuser /app/app.jar

# 切换到非root用户
USER appuser

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:8080/actuator/health || exit 1

# 启动应用
ENTRYPOINT ["java", "-jar", "/app/app.jar"]
```

### 2. 安全加固

#### HTTPS强制
```yaml
server:
  ssl:
    enabled: true
    key-store: ${SSL_KEYSTORE_PATH}
    key-store-password: ${SSL_KEYSTORE_PASSWORD}
    key-store-type: PKCS12
  
  # 重定向HTTP到HTTPS
  port: 8443
  
management:
  server:
    ssl:
      enabled: true
```

#### 网络安全
```yaml
# 限制管理端点访问
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
      base-path: /actuator
  endpoint:
    health:
      show-details: when-authorized
```

---

**© 2024 HandThing. All rights reserved.**
