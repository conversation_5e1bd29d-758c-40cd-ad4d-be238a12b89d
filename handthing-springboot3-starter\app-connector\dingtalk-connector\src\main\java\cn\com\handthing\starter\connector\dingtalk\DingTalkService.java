package cn.com.handthing.starter.connector.dingtalk;

import cn.com.handthing.starter.connector.dingtalk.api.DingTalkContactApi;
import cn.com.handthing.starter.connector.dingtalk.api.DingTalkMessageApi;
import cn.com.handthing.starter.connector.dingtalk.api.DingTalkTodoApi;
import cn.com.handthing.starter.connector.dingtalk.config.DingTalkConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

/**
 * 钉钉服务主入口
 * <p>
 * 聚合各个API服务，提供统一的钉钉服务访问入口。
 * 包含工作通知API、通讯录API、待办事项API等功能模块。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "handthing.connector.dingtalk", name = "enabled", havingValue = "true")
public class DingTalkService {

    private final DingTalkConfig config;
    private final DingTalkMessageApi messageApi;
    private final DingTalkContactApi contactApi;
    private final DingTalkTodoApi todoApi;

    /**
     * 获取工作通知API
     *
     * @return 工作通知API实例
     */
    public DingTalkMessageApi message() {
        return messageApi;
    }

    /**
     * 获取通讯录API
     *
     * @return 通讯录API实例
     */
    public DingTalkContactApi contact() {
        return contactApi;
    }

    /**
     * 获取待办事项API
     *
     * @return 待办事项API实例
     */
    public DingTalkTodoApi todo() {
        return todoApi;
    }

    /**
     * 获取钉钉配置
     *
     * @return 配置实例
     */
    public DingTalkConfig getConfig() {
        return config;
    }

    /**
     * 检查服务是否可用
     *
     * @return 如果服务可用返回true，否则返回false
     */
    public boolean isAvailable() {
        return config.isEnabled() && config.isValid();
    }

    /**
     * 获取服务状态信息
     *
     * @return 服务状态信息
     */
    public ServiceStatus getStatus() {
        ServiceStatus status = new ServiceStatus();
        status.setEnabled(config.isEnabled());
        status.setValid(config.isValid());
        status.setAppKey(config.getAppKey());
        status.setCorpId(config.getCorpId());
        status.setInternalMode(config.isInternalMode());
        status.setThirdPartyMode(config.isThirdPartyMode());
        status.setCallbackEnabled(config.isCallbackEnabled());
        status.setApiBaseUrl(config.getEffectiveApiBaseUrl());
        return status;
    }

    /**
     * 服务状态信息类
     */
    public static class ServiceStatus {
        private boolean enabled;
        private boolean valid;
        private String appKey;
        private String corpId;
        private boolean internalMode;
        private boolean thirdPartyMode;
        private boolean callbackEnabled;
        private String apiBaseUrl;

        // Getters and Setters
        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public boolean isValid() {
            return valid;
        }

        public void setValid(boolean valid) {
            this.valid = valid;
        }

        public String getAppKey() {
            return appKey;
        }

        public void setAppKey(String appKey) {
            this.appKey = appKey;
        }

        public String getCorpId() {
            return corpId;
        }

        public void setCorpId(String corpId) {
            this.corpId = corpId;
        }

        public boolean isInternalMode() {
            return internalMode;
        }

        public void setInternalMode(boolean internalMode) {
            this.internalMode = internalMode;
        }

        public boolean isThirdPartyMode() {
            return thirdPartyMode;
        }

        public void setThirdPartyMode(boolean thirdPartyMode) {
            this.thirdPartyMode = thirdPartyMode;
        }

        public boolean isCallbackEnabled() {
            return callbackEnabled;
        }

        public void setCallbackEnabled(boolean callbackEnabled) {
            this.callbackEnabled = callbackEnabled;
        }

        public String getApiBaseUrl() {
            return apiBaseUrl;
        }

        public void setApiBaseUrl(String apiBaseUrl) {
            this.apiBaseUrl = apiBaseUrl;
        }

        @Override
        public String toString() {
            return String.format("DingTalkServiceStatus{enabled=%s, valid=%s, appKey='%s', corpId='%s', internalMode=%s, thirdPartyMode=%s, callbackEnabled=%s, apiBaseUrl='%s'}",
                    enabled, valid, appKey, corpId, internalMode, thirdPartyMode, callbackEnabled, apiBaseUrl);
        }
    }
}
