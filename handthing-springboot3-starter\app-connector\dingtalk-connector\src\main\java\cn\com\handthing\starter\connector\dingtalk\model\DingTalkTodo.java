package cn.com.handthing.starter.connector.dingtalk.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 钉钉待办事项模型
 * <p>
 * 钉钉待办事项的数据结构
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DingTalkTodo {

    /**
     * 待办事项ID
     */
    @JsonProperty("id")
    private String id;

    /**
     * 待办事项标题
     */
    @JsonProperty("subject")
    private String subject;

    /**
     * 待办事项描述
     */
    @JsonProperty("description")
    private String description;

    /**
     * 待办事项状态
     */
    @JsonProperty("status")
    private String status;

    /**
     * 创建者用户ID
     */
    @JsonProperty("creatorId")
    private String creatorId;

    /**
     * 执行者用户ID
     */
    @JsonProperty("executorIds")
    private String[] executorIds;

    /**
     * 参与者用户ID
     */
    @JsonProperty("participantIds")
    private String[] participantIds;

    /**
     * 截止时间
     */
    @JsonProperty("dueTime")
    private Long dueTime;

    /**
     * 创建时间
     */
    @JsonProperty("createdTime")
    private Long createdTime;

    /**
     * 修改时间
     */
    @JsonProperty("modifiedTime")
    private Long modifiedTime;

    /**
     * 完成时间
     */
    @JsonProperty("finishTime")
    private Long finishTime;

    /**
     * 优先级
     */
    @JsonProperty("priority")
    private Integer priority;

    /**
     * 来源信息
     */
    @JsonProperty("sourceId")
    private String sourceId;

    /**
     * 检查待办事项是否已完成
     *
     * @return 如果已完成返回true，否则返回false
     */
    public boolean isCompleted() {
        return "DONE".equals(status);
    }

    /**
     * 检查待办事项是否已过期
     *
     * @return 如果已过期返回true，否则返回false
     */
    public boolean isOverdue() {
        if (dueTime == null || isCompleted()) {
            return false;
        }
        return System.currentTimeMillis() > dueTime;
    }

    /**
     * 获取优先级描述
     *
     * @return 优先级描述
     */
    public String getPriorityDescription() {
        if (priority == null) {
            return "普通";
        }
        switch (priority) {
            case 10:
                return "低";
            case 20:
                return "普通";
            case 30:
                return "高";
            case 40:
                return "紧急";
            default:
                return "普通";
        }
    }

    /**
     * 获取状态描述
     *
     * @return 状态描述
     */
    public String getStatusDescription() {
        if (status == null) {
            return "未知";
        }
        switch (status) {
            case "NEW":
                return "新建";
            case "RUNNING":
                return "进行中";
            case "DONE":
                return "已完成";
            case "REJECTED":
                return "已拒绝";
            default:
                return status;
        }
    }
}
