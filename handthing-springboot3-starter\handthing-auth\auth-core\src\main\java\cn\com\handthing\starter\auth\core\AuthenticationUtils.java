package cn.com.handthing.starter.auth.core;

import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.UUID;
import java.util.regex.Pattern;

/**
 * 认证工具类
 * <p>
 * 提供认证相关的工具方法，包括令牌提取、IP地址获取、随机字符串生成等。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
public class AuthenticationUtils {

    private static final SecureRandom SECURE_RANDOM = new SecureRandom();
    private static final String ALPHANUMERIC = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    private static final Pattern EMAIL_PATTERN = Pattern.compile("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$");
    private static final Pattern PHONE_PATTERN = Pattern.compile("^1[3-9]\\d{9}$");

    /**
     * 私有构造函数，防止实例化
     */
    private AuthenticationUtils() {
    }

    /**
     * 从HTTP请求中提取令牌
     *
     * @param request   HTTP请求
     * @param jwtConfig JWT配置
     * @return 令牌，如果未找到返回null
     */
    public static String extractTokenFromRequest(HttpServletRequest request, JwtConfig jwtConfig) {
        if (request == null || jwtConfig == null) {
            return null;
        }

        // 从请求头中提取
        String token = extractTokenFromHeader(request, jwtConfig);
        if (token != null) {
            return token;
        }

        // 从请求参数中提取（如果允许）
        if (jwtConfig.isAllowUrlParameter()) {
            return extractTokenFromParameter(request, jwtConfig);
        }

        return null;
    }

    /**
     * 从请求头中提取令牌
     *
     * @param request   HTTP请求
     * @param jwtConfig JWT配置
     * @return 令牌，如果未找到返回null
     */
    public static String extractTokenFromHeader(HttpServletRequest request, JwtConfig jwtConfig) {
        String headerValue = request.getHeader(jwtConfig.getHeaderName());
        if (headerValue != null && !headerValue.trim().isEmpty()) {
            return jwtConfig.extractToken(headerValue);
        }
        return null;
    }

    /**
     * 从请求参数中提取令牌
     *
     * @param request   HTTP请求
     * @param jwtConfig JWT配置
     * @return 令牌，如果未找到返回null
     */
    public static String extractTokenFromParameter(HttpServletRequest request, JwtConfig jwtConfig) {
        String parameterValue = request.getParameter(jwtConfig.getParameterName());
        if (parameterValue != null && !parameterValue.trim().isEmpty()) {
            return jwtConfig.extractToken(parameterValue);
        }
        return null;
    }

    /**
     * 获取当前HTTP请求
     *
     * @return HTTP请求，如果不在Web环境中返回null
     */
    public static HttpServletRequest getCurrentRequest() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            return attributes != null ? attributes.getRequest() : null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取客户端IP地址
     *
     * @param request HTTP请求
     * @return IP地址
     */
    public static String getClientIpAddress(HttpServletRequest request) {
        if (request == null) {
            return "unknown";
        }

        String[] headers = {
                "X-Forwarded-For",
                "X-Real-IP",
                "Proxy-Client-IP",
                "WL-Proxy-Client-IP",
                "HTTP_CLIENT_IP",
                "HTTP_X_FORWARDED_FOR"
        };

        for (String header : headers) {
            String ip = request.getHeader(header);
            if (ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip)) {
                // 多级代理的情况，取第一个IP
                if (ip.contains(",")) {
                    ip = ip.split(",")[0].trim();
                }
                return ip;
            }
        }

        return request.getRemoteAddr();
    }

    /**
     * 获取用户代理
     *
     * @param request HTTP请求
     * @return 用户代理
     */
    public static String getUserAgent(HttpServletRequest request) {
        if (request == null) {
            return "unknown";
        }
        String userAgent = request.getHeader("User-Agent");
        return userAgent != null ? userAgent : "unknown";
    }

    /**
     * 生成随机字符串
     *
     * @param length 长度
     * @return 随机字符串
     */
    public static String generateRandomString(int length) {
        StringBuilder sb = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            sb.append(ALPHANUMERIC.charAt(SECURE_RANDOM.nextInt(ALPHANUMERIC.length())));
        }
        return sb.toString();
    }

    /**
     * 生成随机字节数组
     *
     * @param length 长度
     * @return 随机字节数组
     */
    public static byte[] generateRandomBytes(int length) {
        byte[] bytes = new byte[length];
        SECURE_RANDOM.nextBytes(bytes);
        return bytes;
    }

    /**
     * 生成Base64编码的随机字符串
     *
     * @param length 字节长度
     * @return Base64编码的随机字符串
     */
    public static String generateRandomBase64String(int length) {
        byte[] bytes = generateRandomBytes(length);
        return Base64.getEncoder().encodeToString(bytes);
    }

    /**
     * 生成UUID
     *
     * @return UUID字符串
     */
    public static String generateUUID() {
        return UUID.randomUUID().toString();
    }

    /**
     * 生成不带连字符的UUID
     *
     * @return UUID字符串（不带连字符）
     */
    public static String generateSimpleUUID() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 验证邮箱格式
     *
     * @param email 邮箱地址
     * @return 如果格式正确返回true，否则返回false
     */
    public static boolean isValidEmail(String email) {
        return email != null && EMAIL_PATTERN.matcher(email).matches();
    }

    /**
     * 验证手机号格式
     *
     * @param phone 手机号
     * @return 如果格式正确返回true，否则返回false
     */
    public static boolean isValidPhone(String phone) {
        return phone != null && PHONE_PATTERN.matcher(phone).matches();
    }

    /**
     * 判断字符串是否为空
     *
     * @param str 字符串
     * @return 如果为空返回true，否则返回false
     */
    public static boolean isEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }

    /**
     * 判断字符串是否不为空
     *
     * @param str 字符串
     * @return 如果不为空返回true，否则返回false
     */
    public static boolean isNotEmpty(String str) {
        return !isEmpty(str);
    }

    /**
     * 安全地比较两个字符串
     *
     * @param a 字符串A
     * @param b 字符串B
     * @return 如果相等返回true，否则返回false
     */
    public static boolean safeEquals(String a, String b) {
        if (a == null && b == null) {
            return true;
        }
        if (a == null || b == null) {
            return false;
        }
        
        // 使用固定时间比较，防止时序攻击
        int length = Math.max(a.length(), b.length());
        boolean result = a.length() == b.length();
        
        for (int i = 0; i < length; i++) {
            char charA = i < a.length() ? a.charAt(i) : 0;
            char charB = i < b.length() ? b.charAt(i) : 0;
            result &= (charA == charB);
        }
        
        return result;
    }

    /**
     * 格式化时间戳
     *
     * @param dateTime 时间
     * @return 格式化的时间字符串
     */
    public static String formatDateTime(LocalDateTime dateTime) {
        if (dateTime == null) {
            return null;
        }
        return dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    /**
     * 掩码处理敏感信息
     *
     * @param sensitive 敏感信息
     * @param keepStart 保留开始字符数
     * @param keepEnd   保留结束字符数
     * @return 掩码后的字符串
     */
    public static String maskSensitive(String sensitive, int keepStart, int keepEnd) {
        if (isEmpty(sensitive)) {
            return sensitive;
        }
        
        int length = sensitive.length();
        if (length <= keepStart + keepEnd) {
            return "*".repeat(length);
        }
        
        String start = sensitive.substring(0, keepStart);
        String end = sensitive.substring(length - keepEnd);
        String middle = "*".repeat(length - keepStart - keepEnd);
        
        return start + middle + end;
    }

    /**
     * 掩码处理邮箱
     *
     * @param email 邮箱地址
     * @return 掩码后的邮箱
     */
    public static String maskEmail(String email) {
        if (isEmpty(email) || !email.contains("@")) {
            return email;
        }
        
        String[] parts = email.split("@");
        String localPart = parts[0];
        String domainPart = parts[1];
        
        String maskedLocal = maskSensitive(localPart, 1, 1);
        return maskedLocal + "@" + domainPart;
    }

    /**
     * 掩码处理手机号
     *
     * @param phone 手机号
     * @return 掩码后的手机号
     */
    public static String maskPhone(String phone) {
        return maskSensitive(phone, 3, 4);
    }
}
