package cn.com.handthing.starter.connector.xiaohongshu;

import cn.com.handthing.starter.connector.xiaohongshu.api.XiaoHongShuContentApi;
import cn.com.handthing.starter.connector.xiaohongshu.api.XiaoHongShuCommunityApi;
import cn.com.handthing.starter.connector.xiaohongshu.config.XiaoHongShuConfig;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

/**
 * 小红书服务
 * <p>
 * 提供小红书平台的核心服务功能，包括内容发布、社区互动、数据分析等。
 * 支持个人开发者、企业开发者和品牌方的不同业务场景。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "handthing.connector.xiaohongshu", name = "enabled", havingValue = "true")
public class XiaoHongShuService {

    private final XiaoHongShuConfig config;
    private final XiaoHongShuContentApi contentApi;
    private final XiaoHongShuCommunityApi communityApi;

    /**
     * 获取内容API
     *
     * @return 内容API实例
     */
    public XiaoHongShuContentApi content() {
        return contentApi;
    }

    /**
     * 获取社区API
     *
     * @return 社区API实例
     */
    public XiaoHongShuCommunityApi community() {
        return communityApi;
    }

    /**
     * 检查服务是否可用
     *
     * @return 如果可用返回true，否则返回false
     */
    public boolean isAvailable() {
        return config.isEnabled() && config.isValid();
    }

    /**
     * 获取配置信息
     *
     * @return 配置对象
     */
    public XiaoHongShuConfig getConfig() {
        return config;
    }

    /**
     * 获取服务状态
     *
     * @return 服务状态
     */
    public ServiceStatus getStatus() {
        return ServiceStatus.builder()
                .enabled(config.isEnabled())
                .valid(config.isValid())
                .appKey(config.getAppKey())
                .appType(config.getAppType())
                .brandMode(config.isBrandDeveloper())
                .sandboxMode(config.isSandboxMode())
                .contentPublishEnabled(config.isContentPublishAvailable())
                .communityEnabled(config.isCommunityAvailable())
                .analyticsEnabled(config.isAnalyticsAvailable())
                .ecommerceEnabled(config.isEcommerceAvailable())
                .apiBaseUrl(config.getEffectiveApiBaseUrl())
                .build();
    }

    /**
     * 服务状态信息
     */
    @Data
    @lombok.Builder
    public static class ServiceStatus {
        /**
         * 是否启用
         */
        private boolean enabled;

        /**
         * 配置是否有效
         */
        private boolean valid;

        /**
         * 应用Key
         */
        private String appKey;

        /**
         * 应用类型
         */
        private String appType;

        /**
         * 是否为品牌方模式
         */
        private boolean brandMode;

        /**
         * 是否为沙箱模式
         */
        private boolean sandboxMode;

        /**
         * 是否启用内容发布功能
         */
        private boolean contentPublishEnabled;

        /**
         * 是否启用社区互动功能
         */
        private boolean communityEnabled;

        /**
         * 是否启用数据分析功能
         */
        private boolean analyticsEnabled;

        /**
         * 是否启用电商功能
         */
        private boolean ecommerceEnabled;

        /**
         * API基础URL
         */
        private String apiBaseUrl;

        @Override
        public String toString() {
            return String.format("XiaoHongShuServiceStatus{enabled=%s, valid=%s, appKey='%s', appType='%s', " +
                            "brandMode=%s, sandboxMode=%s, contentPublishEnabled=%s, communityEnabled=%s, " +
                            "analyticsEnabled=%s, ecommerceEnabled=%s, apiBaseUrl='%s'}",
                    enabled, valid, appKey, appType, brandMode, sandboxMode, 
                    contentPublishEnabled, communityEnabled, analyticsEnabled, ecommerceEnabled, apiBaseUrl);
        }
    }
}
