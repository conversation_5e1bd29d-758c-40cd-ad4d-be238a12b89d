<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HandThing 多租户测试页面</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .tenant-selector {
            margin-bottom: 20px;
            padding: 15px;
            background: #ecf0f1;
            border-radius: 5px;
        }
        .tenant-selector label {
            font-weight: bold;
            margin-right: 10px;
        }
        .tenant-selector select {
            padding: 8px 12px;
            border: 1px solid #bdc3c7;
            border-radius: 4px;
            font-size: 14px;
        }
        .api-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
        }
        .api-section h3 {
            color: #34495e;
            margin-top: 0;
        }
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #2980b9;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-left: 4px solid #3498db;
            border-radius: 0 4px 4px 0;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .error {
            border-left-color: #e74c3c;
            background: #fdf2f2;
        }
        .success {
            border-left-color: #27ae60;
            background: #f0f9f4;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏢 HandThing 多租户系统测试</h1>
        
        <div class="tenant-selector">
            <label for="tenantSelect">选择租户:</label>
            <select id="tenantSelect">
                <option value="">默认 (无头部)</option>
                <option value="default">default - 默认租户</option>
                <option value="demo">demo - 演示租户</option>
                <option value="test">test - 测试租户</option>
            </select>
            <span id="currentTenant" style="margin-left: 20px; font-weight: bold; color: #27ae60;"></span>
        </div>

        <div class="api-section">
            <h3>🏥 系统健康检查</h3>
            <button onclick="callAPI('/api/tenant/health', 'healthResult')">检查健康状态</button>
            <div id="healthResult" class="result"></div>
        </div>

        <div class="api-section">
            <h3>🏠 当前租户信息</h3>
            <button onclick="callAPI('/api/tenant/current', 'currentResult')">获取当前租户</button>
            <div id="currentResult" class="result"></div>
        </div>

        <div class="api-section">
            <h3>⚙️ 租户配置</h3>
            <button onclick="callAPI('/api/tenant/config', 'configResult')">获取所有配置</button>
            <button onclick="callAPI('/api/tenant/config?configKey=jwt.secret', 'configResult')">获取JWT密钥</button>
            <button onclick="callAPI('/api/tenant/config?configKey=tenant.name', 'configResult')">获取租户名称</button>
            <div id="configResult" class="result"></div>
        </div>

        <div class="api-section">
            <h3>📊 租户统计</h3>
            <button onclick="callAPI('/api/tenant/stats', 'statsResult')">获取统计信息</button>
            <div id="statsResult" class="result"></div>
        </div>

        <div class="api-section">
            <h3>📋 租户列表</h3>
            <button onclick="callAPI('/api/tenant/list', 'listResult')">获取所有租户</button>
            <div id="listResult" class="result"></div>
        </div>

        <div class="api-section">
            <h3>🔄 租户切换测试</h3>
            <button onclick="testTenantSwitch('demo')">切换到演示租户</button>
            <button onclick="testTenantSwitch('test')">切换到测试租户</button>
            <button onclick="testTenantSwitch('default')">切换到默认租户</button>
            <div id="switchResult" class="result"></div>
        </div>
    </div>

    <script>
        function getSelectedTenant() {
            return document.getElementById('tenantSelect').value;
        }

        function updateCurrentTenant() {
            const tenant = getSelectedTenant();
            const display = document.getElementById('currentTenant');
            if (tenant) {
                display.textContent = `当前测试租户: ${tenant}`;
                display.style.color = '#27ae60';
            } else {
                display.textContent = '当前测试租户: 默认 (无头部)';
                display.style.color = '#7f8c8d';
            }
        }

        function callAPI(url, resultId) {
            const resultDiv = document.getElementById(resultId);
            resultDiv.textContent = '请求中...';
            resultDiv.className = 'result';

            const headers = {};
            const tenant = getSelectedTenant();
            if (tenant) {
                headers['X-Tenant-ID'] = tenant;
            }

            fetch(url, {
                method: 'GET',
                headers: headers
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                resultDiv.textContent = JSON.stringify(data, null, 2);
                resultDiv.className = 'result success';
            })
            .catch(error => {
                resultDiv.textContent = `错误: ${error.message}`;
                resultDiv.className = 'result error';
            });
        }

        function testTenantSwitch(targetTenant) {
            const resultDiv = document.getElementById('switchResult');
            resultDiv.textContent = `正在切换到租户: ${targetTenant}...`;
            resultDiv.className = 'result';

            const headers = {};
            const currentTenant = getSelectedTenant();
            if (currentTenant) {
                headers['X-Tenant-ID'] = currentTenant;
            }

            fetch(`/api/tenant/switch?targetTenantId=${targetTenant}`, {
                method: 'POST',
                headers: headers
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                resultDiv.textContent = JSON.stringify(data, null, 2);
                resultDiv.className = 'result success';
            })
            .catch(error => {
                resultDiv.textContent = `错误: ${error.message}`;
                resultDiv.className = 'result error';
            });
        }

        // 监听租户选择变化
        document.getElementById('tenantSelect').addEventListener('change', updateCurrentTenant);
        
        // 初始化显示
        updateCurrentTenant();
    </script>
</body>
</html>
