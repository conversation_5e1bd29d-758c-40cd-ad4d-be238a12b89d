package cn.com.handthing.starter.connector.alipay;

import cn.com.handthing.starter.connector.PlatformType;
import cn.com.handthing.starter.connector.alipay.config.AlipayConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.client.RestTemplate;

import static org.junit.jupiter.api.Assertions.*;

/**
 * AlipayAuthProvider 单元测试
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@ExtendWith(MockitoExtension.class)
class AlipayAuthProviderTest {

    @Mock
    private RestTemplate restTemplate;

    private AlipayConfig config;
    private AlipayAuthProvider authProvider;

    @BeforeEach
    void setUp() {
        config = new AlipayConfig();
        config.setEnabled(true);
        config.setAppId("test-app-id");
        config.setPrivateKey("test-private-key");
        config.setAlipayPublicKey("test-alipay-public-key");
        config.setRedirectUri("https://test.com/callback");
        
        authProvider = new AlipayAuthProvider(config, restTemplate);
    }

    @Test
    void testGetPlatformType() {
        assertEquals(PlatformType.ALIPAY, authProvider.getPlatformType());
    }

    @Test
    void testSupportsRefreshToken() {
        assertTrue(authProvider.supportsRefreshToken());
    }

    @Test
    void testGetDefaultScope() {
        assertEquals("auth_user", authProvider.getDefaultScope());
    }

    @Test
    void testGetAuthorizationUrl() {
        String state = "test-state";
        String redirectUri = "https://test.com/callback";
        
        String authUrl = authProvider.getAuthorizationUrl(state, redirectUri);
        
        assertNotNull(authUrl);
        assertTrue(authUrl.contains("app_id=" + config.getAppId()));
        assertTrue(authUrl.contains("state=" + state));
        assertTrue(authUrl.contains("redirect_uri="));
        assertTrue(authUrl.contains("scope=auth_user"));
    }

    @Test
    void testGetAuthorizationUrlWithNullState() {
        String redirectUri = "https://test.com/callback";
        
        String authUrl = authProvider.getAuthorizationUrl(null, redirectUri);
        
        assertNotNull(authUrl);
        assertTrue(authUrl.contains("state="));
    }

    @Test
    void testGetAuthorizationUrlWithDefaultRedirectUri() {
        String state = "test-state";
        
        String authUrl = authProvider.getAuthorizationUrl(state, null);
        
        assertNotNull(authUrl);
        assertTrue(authUrl.contains("redirect_uri="));
    }

    @Test
    void testConfigValidation() {
        // 测试有效配置
        assertTrue(config.isValid());
        
        // 测试无效配置
        AlipayConfig invalidConfig = new AlipayConfig();
        invalidConfig.setEnabled(true);
        assertFalse(invalidConfig.isValid());
        
        invalidConfig.setAppId("test-app-id");
        assertFalse(invalidConfig.isValid());
        
        invalidConfig.setPrivateKey("test-private-key");
        assertFalse(invalidConfig.isValid());
        
        invalidConfig.setAlipayPublicKey("test-alipay-public-key");
        assertTrue(invalidConfig.isValid());
    }

    @Test
    void testConfigUrls() {
        assertEquals("https://openapi.alipay.com/gateway.do", config.getEffectiveGatewayUrl());
        assertEquals("https://openauth.alipay.com/oauth2/publicAppAuthorize.htm", config.getEffectiveAuthUrl());
        
        assertNotNull(config.getTokenUrl());
        assertNotNull(config.getUserInfoUrl());
        assertNotNull(config.getPaymentUrlPrefix());
        assertNotNull(config.getMiniProgramUrlPrefix());
    }

    @Test
    void testSandboxMode() {
        assertFalse(config.isSandboxMode());
        
        config.setSandbox(true);
        assertTrue(config.isSandboxMode());
        
        assertEquals("https://openapi.alipaydev.com/gateway.do", config.getEffectiveGatewayUrl());
        assertEquals("https://openauth.alipaydev.com/oauth2/publicAppAuthorize.htm", config.getEffectiveAuthUrl());
    }

    @Test
    void testCertMode() {
        assertFalse(config.isCertMode());
        
        config.setEnableCert(true);
        config.setAppCertPath("test-app-cert-path");
        config.setAlipayRootCertPath("test-root-cert-path");
        config.setAlipayCertPath("test-alipay-cert-path");
        
        assertTrue(config.isCertMode());
    }

    @Test
    void testNotifyConfiguration() {
        assertFalse(config.isNotifyEnabled());
        
        config.setEnableNotify(true);
        config.setNotifyUrl("https://test.com/notify");
        
        assertTrue(config.isNotifyEnabled());
    }

    @Test
    void testConfigValidationWithCertMode() {
        config.setEnableCert(true);
        assertFalse(config.isValid()); // 缺少证书配置
        
        config.setAppCertPath("test-app-cert-path");
        config.setAlipayRootCertPath("test-root-cert-path");
        config.setAlipayCertPath("test-alipay-cert-path");
        
        assertTrue(config.isValid());
    }

    @Test
    void testSignType() {
        assertEquals("RSA2", config.getSignType());
        
        config.setSignType("RSA");
        assertEquals("RSA", config.getSignType());
    }
}
