package cn.com.handthing.starter.connector.dingtalk.api;

import cn.com.handthing.starter.connector.dingtalk.config.DingTalkConfig;
import cn.com.handthing.starter.connector.dingtalk.model.DingTalkMessage;
import cn.com.handthing.starter.connector.exception.ApiCallException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * 钉钉工作通知API
 * <p>
 * 提供钉钉工作通知发送功能，支持发送文本、链接、卡片等消息类型。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "handthing.connector.dingtalk", name = "enabled", havingValue = "true")
public class DingTalkMessageApi {

    private final DingTalkConfig config;
    private final RestTemplate restTemplate;

    /**
     * 发送文本工作通知
     *
     * @param accessToken 访问令牌
     * @param userIds     接收用户ID列表
     * @param content     消息内容
     * @return 发送结果
     * @throws ApiCallException 如果发送失败
     */
    public DingTalkMessage.SendResult sendTextMessage(String accessToken, String[] userIds, String content) throws ApiCallException {
        Map<String, Object> message = new HashMap<>();
        message.put("robotCode", config.getAppKey());
        message.put("userIds", userIds);
        message.put("msgKey", "sampleText");
        
        Map<String, String> msgParam = new HashMap<>();
        msgParam.put("content", content);
        message.put("msgParam", msgParam);
        
        return sendMessage(accessToken, message);
    }

    /**
     * 发送链接工作通知
     *
     * @param accessToken 访问令牌
     * @param userIds     接收用户ID列表
     * @param title       链接标题
     * @param text        链接描述
     * @param messageUrl  链接地址
     * @param picUrl      图片地址
     * @return 发送结果
     * @throws ApiCallException 如果发送失败
     */
    public DingTalkMessage.SendResult sendLinkMessage(String accessToken, String[] userIds, 
                                                     String title, String text, String messageUrl, String picUrl) throws ApiCallException {
        Map<String, Object> message = new HashMap<>();
        message.put("robotCode", config.getAppKey());
        message.put("userIds", userIds);
        message.put("msgKey", "sampleLink");
        
        Map<String, String> msgParam = new HashMap<>();
        msgParam.put("title", title);
        msgParam.put("text", text);
        msgParam.put("messageUrl", messageUrl);
        msgParam.put("picUrl", picUrl);
        message.put("msgParam", msgParam);
        
        return sendMessage(accessToken, message);
    }

    /**
     * 发送卡片工作通知
     *
     * @param accessToken 访问令牌
     * @param userIds     接收用户ID列表
     * @param title       卡片标题
     * @param markdown    卡片内容（Markdown格式）
     * @param singleTitle 按钮标题
     * @param singleUrl   按钮链接
     * @return 发送结果
     * @throws ApiCallException 如果发送失败
     */
    public DingTalkMessage.SendResult sendCardMessage(String accessToken, String[] userIds, 
                                                     String title, String markdown, String singleTitle, String singleUrl) throws ApiCallException {
        Map<String, Object> message = new HashMap<>();
        message.put("robotCode", config.getAppKey());
        message.put("userIds", userIds);
        message.put("msgKey", "sampleActionCard");
        
        Map<String, String> msgParam = new HashMap<>();
        msgParam.put("title", title);
        msgParam.put("text", markdown);
        msgParam.put("singleTitle", singleTitle);
        msgParam.put("singleURL", singleUrl);
        message.put("msgParam", msgParam);
        
        return sendMessage(accessToken, message);
    }

    /**
     * 发送Markdown工作通知
     *
     * @param accessToken 访问令牌
     * @param userIds     接收用户ID列表
     * @param title       标题
     * @param text        Markdown内容
     * @return 发送结果
     * @throws ApiCallException 如果发送失败
     */
    public DingTalkMessage.SendResult sendMarkdownMessage(String accessToken, String[] userIds, 
                                                         String title, String text) throws ApiCallException {
        Map<String, Object> message = new HashMap<>();
        message.put("robotCode", config.getAppKey());
        message.put("userIds", userIds);
        message.put("msgKey", "sampleMarkdown");
        
        Map<String, String> msgParam = new HashMap<>();
        msgParam.put("title", title);
        msgParam.put("text", text);
        message.put("msgParam", msgParam);
        
        return sendMessage(accessToken, message);
    }

    /**
     * 发送消息的通用方法
     *
     * @param accessToken 访问令牌
     * @param message     消息内容
     * @return 发送结果
     * @throws ApiCallException 如果发送失败
     */
    private DingTalkMessage.SendResult sendMessage(String accessToken, Map<String, Object> message) throws ApiCallException {
        try {
            String url = config.getSendWorkNoticeUrl();
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("x-acs-dingtalk-access-token", accessToken);
            
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(message, headers);
            
            ResponseEntity<DingTalkMessage.SendResult> response = restTemplate.postForEntity(url, request, DingTalkMessage.SendResult.class);
            DingTalkMessage.SendResult result = response.getBody();
            
            if (result == null) {
                throw new ApiCallException("No response from DingTalk API", "SEND_MESSAGE_FAILED", 
                        cn.com.handthing.starter.connector.PlatformType.DINGTALK, null, null, null);
            }
            
            if (!result.isSuccess()) {
                throw new ApiCallException("Failed to send message: " + result.getErrorDescription(),
                        "SEND_MESSAGE_FAILED", cn.com.handthing.starter.connector.PlatformType.DINGTALK);
            }
            
            log.debug("Successfully sent DingTalk message to: {}", message.get("userIds"));
            return result;
            
        } catch (Exception e) {
            log.error("Failed to send DingTalk message", e);
            if (e instanceof ApiCallException) {
                throw e;
            }
            throw new ApiCallException("Network error occurred", "NETWORK_ERROR", 
                    cn.com.handthing.starter.connector.PlatformType.DINGTALK, null, null, e);
        }
    }
}
