D:\code\ai-project\handthing-springboot3-starter\handthing-auth\third-party-provider-starter\src\main\java\cn\com\handthing\starter\auth\thirdparty\DefaultDingtalkApiService.java
D:\code\ai-project\handthing-springboot3-starter\handthing-auth\third-party-provider-starter\src\main\java\cn\com\handthing\starter\auth\thirdparty\DefaultDingtalkUserService.java
D:\code\ai-project\handthing-springboot3-starter\handthing-auth\third-party-provider-starter\src\main\java\cn\com\handthing\starter\auth\thirdparty\DefaultFeishuApiService.java
D:\code\ai-project\handthing-springboot3-starter\handthing-auth\third-party-provider-starter\src\main\java\cn\com\handthing\starter\auth\thirdparty\DefaultFeishuUserService.java
D:\code\ai-project\handthing-springboot3-starter\handthing-auth\third-party-provider-starter\src\main\java\cn\com\handthing\starter\auth\thirdparty\DefaultWechatApiService.java
D:\code\ai-project\handthing-springboot3-starter\handthing-auth\third-party-provider-starter\src\main\java\cn\com\handthing\starter\auth\thirdparty\DefaultWechatUserService.java
D:\code\ai-project\handthing-springboot3-starter\handthing-auth\third-party-provider-starter\src\main\java\cn\com\handthing\starter\auth\thirdparty\DefaultWecomApiService.java
D:\code\ai-project\handthing-springboot3-starter\handthing-auth\third-party-provider-starter\src\main\java\cn\com\handthing\starter\auth\thirdparty\DefaultWecomUserService.java
D:\code\ai-project\handthing-springboot3-starter\handthing-auth\third-party-provider-starter\src\main\java\cn\com\handthing\starter\auth\thirdparty\DingtalkApiService.java
D:\code\ai-project\handthing-springboot3-starter\handthing-auth\third-party-provider-starter\src\main\java\cn\com\handthing\starter\auth\thirdparty\DingtalkAuthenticationProvider.java
D:\code\ai-project\handthing-springboot3-starter\handthing-auth\third-party-provider-starter\src\main\java\cn\com\handthing\starter\auth\thirdparty\DingtalkAuthenticationRequest.java
D:\code\ai-project\handthing-springboot3-starter\handthing-auth\third-party-provider-starter\src\main\java\cn\com\handthing\starter\auth\thirdparty\DingtalkAuthenticationResponse.java
D:\code\ai-project\handthing-springboot3-starter\handthing-auth\third-party-provider-starter\src\main\java\cn\com\handthing\starter\auth\thirdparty\DingtalkController.java
D:\code\ai-project\handthing-springboot3-starter\handthing-auth\third-party-provider-starter\src\main\java\cn\com\handthing\starter\auth\thirdparty\DingtalkUserService.java
D:\code\ai-project\handthing-springboot3-starter\handthing-auth\third-party-provider-starter\src\main\java\cn\com\handthing\starter\auth\thirdparty\FeishuApiService.java
D:\code\ai-project\handthing-springboot3-starter\handthing-auth\third-party-provider-starter\src\main\java\cn\com\handthing\starter\auth\thirdparty\FeishuAuthenticationProvider.java
D:\code\ai-project\handthing-springboot3-starter\handthing-auth\third-party-provider-starter\src\main\java\cn\com\handthing\starter\auth\thirdparty\FeishuAuthenticationRequest.java
D:\code\ai-project\handthing-springboot3-starter\handthing-auth\third-party-provider-starter\src\main\java\cn\com\handthing\starter\auth\thirdparty\FeishuAuthenticationResponse.java
D:\code\ai-project\handthing-springboot3-starter\handthing-auth\third-party-provider-starter\src\main\java\cn\com\handthing\starter\auth\thirdparty\FeishuController.java
D:\code\ai-project\handthing-springboot3-starter\handthing-auth\third-party-provider-starter\src\main\java\cn\com\handthing\starter\auth\thirdparty\FeishuUserService.java
D:\code\ai-project\handthing-springboot3-starter\handthing-auth\third-party-provider-starter\src\main\java\cn\com\handthing\starter\auth\thirdparty\ResponseUtils.java
D:\code\ai-project\handthing-springboot3-starter\handthing-auth\third-party-provider-starter\src\main\java\cn\com\handthing\starter\auth\thirdparty\ThirdPartyAuthProperties.java
D:\code\ai-project\handthing-springboot3-starter\handthing-auth\third-party-provider-starter\src\main\java\cn\com\handthing\starter\auth\thirdparty\ThirdPartyProviderAutoConfiguration.java
D:\code\ai-project\handthing-springboot3-starter\handthing-auth\third-party-provider-starter\src\main\java\cn\com\handthing\starter\auth\thirdparty\WechatApiService.java
D:\code\ai-project\handthing-springboot3-starter\handthing-auth\third-party-provider-starter\src\main\java\cn\com\handthing\starter\auth\thirdparty\WechatAuthenticationProvider.java
D:\code\ai-project\handthing-springboot3-starter\handthing-auth\third-party-provider-starter\src\main\java\cn\com\handthing\starter\auth\thirdparty\WechatAuthenticationRequest.java
D:\code\ai-project\handthing-springboot3-starter\handthing-auth\third-party-provider-starter\src\main\java\cn\com\handthing\starter\auth\thirdparty\WechatAuthenticationResponse.java
D:\code\ai-project\handthing-springboot3-starter\handthing-auth\third-party-provider-starter\src\main\java\cn\com\handthing\starter\auth\thirdparty\WechatController.java
D:\code\ai-project\handthing-springboot3-starter\handthing-auth\third-party-provider-starter\src\main\java\cn\com\handthing\starter\auth\thirdparty\WechatUserService.java
D:\code\ai-project\handthing-springboot3-starter\handthing-auth\third-party-provider-starter\src\main\java\cn\com\handthing\starter\auth\thirdparty\WecomApiService.java
D:\code\ai-project\handthing-springboot3-starter\handthing-auth\third-party-provider-starter\src\main\java\cn\com\handthing\starter\auth\thirdparty\WecomAuthenticationProvider.java
D:\code\ai-project\handthing-springboot3-starter\handthing-auth\third-party-provider-starter\src\main\java\cn\com\handthing\starter\auth\thirdparty\WecomAuthenticationRequest.java
D:\code\ai-project\handthing-springboot3-starter\handthing-auth\third-party-provider-starter\src\main\java\cn\com\handthing\starter\auth\thirdparty\WecomAuthenticationResponse.java
D:\code\ai-project\handthing-springboot3-starter\handthing-auth\third-party-provider-starter\src\main\java\cn\com\handthing\starter\auth\thirdparty\WecomController.java
D:\code\ai-project\handthing-springboot3-starter\handthing-auth\third-party-provider-starter\src\main\java\cn\com\handthing\starter\auth\thirdparty\WecomUserService.java
