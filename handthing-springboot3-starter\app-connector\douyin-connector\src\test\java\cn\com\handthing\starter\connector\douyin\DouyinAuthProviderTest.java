package cn.com.handthing.starter.connector.douyin;

import cn.com.handthing.starter.connector.PlatformType;
import cn.com.handthing.starter.connector.douyin.config.DouyinConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.client.RestTemplate;

import static org.junit.jupiter.api.Assertions.*;

/**
 * DouyinAuthProvider 单元测试
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@ExtendWith(MockitoExtension.class)
class DouyinAuthProviderTest {

    @Mock
    private RestTemplate restTemplate;

    private DouyinConfig config;
    private DouyinAuthProvider authProvider;

    @BeforeEach
    void setUp() {
        config = new DouyinConfig();
        config.setEnabled(true);
        config.setClientKey("test-client-key");
        config.setClientSecret("test-client-secret");
        config.setRedirectUri("https://test.com/callback");
        
        authProvider = new DouyinAuthProvider(config, restTemplate);
    }

    @Test
    void testGetPlatformType() {
        assertEquals(PlatformType.DOUYIN, authProvider.getPlatformType());
    }

    @Test
    void testSupportsRefreshToken() {
        assertTrue(authProvider.supportsRefreshToken());
    }

    @Test
    void testGetDefaultScope() {
        assertEquals("user_info", authProvider.getDefaultScope());
    }

    @Test
    void testGetAuthorizationUrl() {
        String state = "test-state";
        String redirectUri = "https://test.com/callback";
        
        String authUrl = authProvider.getAuthorizationUrl(state, redirectUri);
        
        assertNotNull(authUrl);
        assertTrue(authUrl.contains("client_key=" + config.getClientKey()));
        assertTrue(authUrl.contains("state=" + state));
        assertTrue(authUrl.contains("redirect_uri="));
        assertTrue(authUrl.contains("response_type=code"));
        assertTrue(authUrl.contains("scope=user_info"));
    }

    @Test
    void testGetAuthorizationUrlWithNullState() {
        String redirectUri = "https://test.com/callback";
        
        String authUrl = authProvider.getAuthorizationUrl(null, redirectUri);
        
        assertNotNull(authUrl);
        assertTrue(authUrl.contains("state="));
    }

    @Test
    void testGetAuthorizationUrlWithDefaultRedirectUri() {
        String state = "test-state";
        
        String authUrl = authProvider.getAuthorizationUrl(state, null);
        
        assertNotNull(authUrl);
        assertTrue(authUrl.contains("redirect_uri="));
    }

    @Test
    void testConfigValidation() {
        // 测试有效配置
        assertTrue(config.isValid());
        
        // 测试无效配置
        DouyinConfig invalidConfig = new DouyinConfig();
        invalidConfig.setEnabled(true);
        assertFalse(invalidConfig.isValid());
        
        invalidConfig.setClientKey("test-client-key");
        assertFalse(invalidConfig.isValid());
        
        invalidConfig.setClientSecret("test-client-secret");
        assertTrue(invalidConfig.isValid());
    }

    @Test
    void testConfigUrls() {
        assertEquals("https://open.douyin.com", config.getEffectiveApiBaseUrl());
        assertEquals("https://open.douyin.com/platform/oauth/connect", config.getEffectiveAuthUrl());
        
        assertNotNull(config.getTokenUrl());
        assertNotNull(config.getRefreshTokenUrl());
        assertNotNull(config.getUserInfoUrl());
        assertNotNull(config.getVideoUrlPrefix());
        assertNotNull(config.getDataUrlPrefix());
    }

    @Test
    void testEnterpriseMode() {
        assertFalse(config.isEnterpriseMode());
        
        config.setEnterprise(true);
        config.setEnterpriseId("test-enterprise-id");
        
        assertTrue(config.isEnterpriseMode());
    }

    @Test
    void testMiniProgramMode() {
        assertFalse(config.isMiniProgramMode());
        
        config.setMiniProgram(true);
        config.setMiniProgramAppId("test-mini-app-id");
        config.setMiniProgramSecret("test-mini-secret");
        
        assertTrue(config.isMiniProgramMode());
    }

    @Test
    void testWebhookConfiguration() {
        assertFalse(config.isWebhookEnabled());
        
        config.setEnableWebhook(true);
        config.setWebhookToken("test-webhook-token");
        config.setWebhookSecret("test-webhook-secret");
        
        assertTrue(config.isWebhookEnabled());
    }

    @Test
    void testConfigValidationWithEnterpriseMode() {
        config.setEnterprise(true);
        assertFalse(config.isValid()); // 缺少enterpriseId
        
        config.setEnterpriseId("test-enterprise-id");
        assertTrue(config.isValid());
    }

    @Test
    void testConfigValidationWithMiniProgramMode() {
        config.setMiniProgram(true);
        assertFalse(config.isValid()); // 缺少小程序配置
        
        config.setMiniProgramAppId("test-mini-app-id");
        assertFalse(config.isValid()); // 缺少小程序密钥
        
        config.setMiniProgramSecret("test-mini-secret");
        assertTrue(config.isValid());
    }
}
