{"groups": [{"name": "handthing.cache", "type": "cn.com.handthing.starter.cache.config.CacheProperties", "sourceType": "cn.com.handthing.starter.cache.config.CacheProperties"}], "properties": [{"name": "handthing.cache.allow-null-values", "type": "java.lang.Bo<PERSON>an", "description": "是否允许缓存null值", "sourceType": "cn.com.handthing.starter.cache.config.CacheProperties"}, {"name": "handthing.cache.default-ttl", "type": "java.time.Duration", "description": "默认过期时间", "sourceType": "cn.com.handthing.starter.cache.config.CacheProperties"}, {"name": "handthing.cache.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用缓存功能", "sourceType": "cn.com.handthing.starter.cache.config.CacheProperties"}, {"name": "handthing.cache.specs", "type": "java.util.Map<java.lang.String,cn.com.handthing.starter.cache.config.CacheProperties$CacheSpec>", "description": "缓存区域配置映射 key: 缓存区域名称 value: 缓存区域配置", "sourceType": "cn.com.handthing.starter.cache.config.CacheProperties"}, {"name": "handthing.cache.type", "type": "java.lang.String", "description": "缓存类型 (memory, caffeine, redis, level)", "sourceType": "cn.com.handthing.starter.cache.config.CacheProperties"}], "hints": [], "ignored": {"properties": []}}