package cn.com.handthing.starter.auth.thirdparty;

import cn.com.handthing.core.util.JsonUtils;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 响应工具类
 * <p>
 * 提供统一的响应构建方法，使用handthing-core的JsonUtils解决Jackson序列化问题。
 * 支持LocalDateTime等Java 8时间类型的正确序列化。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
public class ResponseUtils {

    /**
     * 获取配置好的ObjectMapper实例
     * <p>
     * 使用handthing-core的JsonUtils提供的ObjectMapper，
     * 已经配置了Java 8时间类型支持和其他优化。
     * </p>
     *
     * @return 支持Java 8时间类型的ObjectMapper
     */
    public static ObjectMapper getObjectMapper() {
        return JsonUtils.getMapper();
    }

    /**
     * 构建成功响应
     * <p>
     * 使用LocalDateTime作为时间戳，会被handthing-core的JsonUtils
     * 正确序列化为"yyyy-MM-dd HH:mm:ss"格式。
     * </p>
     *
     * @param message 消息
     * @return 响应Map
     */
    public static Map<String, Object> buildSuccessResponse(String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", message);
        response.put("timestamp", LocalDateTime.now());
        return response;
    }

    /**
     * 构建错误响应
     * <p>
     * 使用LocalDateTime作为时间戳，会被handthing-core的JsonUtils
     * 正确序列化为"yyyy-MM-dd HH:mm:ss"格式。
     * </p>
     *
     * @param errorCode        错误代码
     * @param errorDescription 错误描述
     * @return 响应Map
     */
    public static Map<String, Object> buildErrorResponse(String errorCode, String errorDescription) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("error", errorCode);
        response.put("error_description", errorDescription);
        response.put("timestamp", LocalDateTime.now());
        return response;
    }

    /**
     * 将对象转换为JSON字符串
     * <p>
     * 使用handthing-core的JsonUtils进行序列化，
     * 支持LocalDateTime等Java 8时间类型。
     * </p>
     *
     * @param obj 待转换的对象
     * @return JSON字符串
     */
    public static String toJson(Object obj) {
        return JsonUtils.toJson(obj);
    }

    /**
     * 将JSON字符串转换为对象
     * <p>
     * 使用handthing-core的JsonUtils进行反序列化，
     * 支持LocalDateTime等Java 8时间类型。
     * </p>
     *
     * @param json  JSON字符串
     * @param clazz 目标类型
     * @param <T>   泛型类型
     * @return 转换后的对象
     */
    public static <T> T fromJson(String json, Class<T> clazz) {
        return JsonUtils.fromJson(json, clazz);
    }
}
