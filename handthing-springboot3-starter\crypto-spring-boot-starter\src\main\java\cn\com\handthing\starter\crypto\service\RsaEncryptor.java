package cn.com.handthing.starter.crypto.service;

import cn.com.handthing.starter.crypto.core.AsymmetricEncryptor;
import cn.com.handthing.starter.crypto.core.KeyProvider;
import cn.com.handthing.starter.crypto.exception.CryptoException;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;

import javax.crypto.Cipher;
import java.nio.charset.StandardCharsets;
import java.security.KeyPair;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.Signature;
import java.util.Base64;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 使用 RSA 算法的非对称加密器实现。
 *
 * <AUTHOR>
 * @since V1.0.0
 */
public class RsaEncryptor extends AbstractCryptoService implements AsymmetricEncryptor {

    private final KeyProvider publicKeyProvider;
    private final KeyProvider privateKeyProvider;
    private final MeterRegistry meterRegistry;
    private final AtomicReference<KeyPair> keyPairRef = new AtomicReference<>();

    private static final String SIGNATURE_ALGORITHM = "SHA256withRSA";
    private static final String CIPHER_ALGORITHM = "RSA/ECB/PKCS1Padding";

    public RsaEncryptor(String beanName, KeyProvider publicKeyProvider, KeyProvider privateKeyProvider, MeterRegistry meterRegistry) {
        super(beanName);
        this.publicKeyProvider = Objects.requireNonNull(publicKeyProvider, "PublicKeyProvider must not be null");
        this.privateKeyProvider = Objects.requireNonNull(privateKeyProvider, "PrivateKeyProvider must not be null");
        this.meterRegistry = Objects.requireNonNull(meterRegistry, "MeterRegistry must not be null");
        init();
    }

    private void init() {
        try {
            publicKeyProvider.checkReady();
            privateKeyProvider.checkReady();
            KeyPair keyPair = privateKeyProvider.getKeyPair();
            this.keyPairRef.set(keyPair);
        } catch (Exception e) {
            throw new CryptoException("Failed to initialize RSA key pair for processor: " + beanName, e);
        }
    }

    @Override
    public String encrypt(String plaintext) {
        return Timer.builder("crypto.operations.duration").tag("processor", beanName).tag("operation", "encrypt").register(meterRegistry)
                .record(() -> {
                    try {
                        PublicKey publicKey = keyPairRef.get().getPublic();
                        Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM);
                        cipher.init(Cipher.ENCRYPT_MODE, publicKey);
                        byte[] encrypted = cipher.doFinal(plaintext.getBytes(StandardCharsets.UTF_8));
                        return Base64.getEncoder().encodeToString(encrypted);
                    } catch (Exception e) {
                        throw new CryptoException("RSA encryption failed for processor: " + beanName, e);
                    }
                });
    }

    @Override
    public String decrypt(String ciphertext) {
        return Timer.builder("crypto.operations.duration").tag("processor", beanName).tag("operation", "decrypt").register(meterRegistry)
                .record(() -> {
                    try {
                        PrivateKey privateKey = keyPairRef.get().getPrivate();
                        Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM);
                        cipher.init(Cipher.DECRYPT_MODE, privateKey);
                        byte[] original = cipher.doFinal(Base64.getDecoder().decode(ciphertext));
                        return new String(original, StandardCharsets.UTF_8);
                    } catch (Exception e) {
                        throw new CryptoException("RSA decryption failed for processor: " + beanName, e);
                    }
                });
    }

    @Override
    public String sign(String data) {
        return Timer.builder("crypto.operations.duration").tag("processor", beanName).tag("operation", "sign").register(meterRegistry)
                .record(() -> {
                    try {
                        PrivateKey privateKey = keyPairRef.get().getPrivate();
                        Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
                        signature.initSign(privateKey);
                        signature.update(data.getBytes(StandardCharsets.UTF_8));
                        byte[] signatureBytes = signature.sign();
                        return Base64.getEncoder().encodeToString(signatureBytes);
                    } catch (Exception e) {
                        throw new CryptoException("RSA signing failed for processor: " + beanName, e);
                    }
                });
    }

    @Override
    public boolean verify(String data, String signatureString) {
        return Timer.builder("crypto.operations.duration").tag("processor", beanName).tag("operation", "verify").register(meterRegistry)
                .record(() -> {
                    try {
                        PublicKey publicKey = keyPairRef.get().getPublic();
                        Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
                        signature.initVerify(publicKey);
                        signature.update(data.getBytes(StandardCharsets.UTF_8));
                        return signature.verify(Base64.getDecoder().decode(signatureString));
                    } catch (Exception e) {
                        // For verification, returning false is often sufficient rather than throwing an exception.
                        return false;
                    }
                });
    }

    @Override
    public void selfCheck() throws CryptoException {
        String testData = "HandThing-RSA-SelfCheck";
        String signature = this.sign(testData);
        if (!this.verify(testData, signature)) {
            throw new IllegalStateException("Self-check failed for RSA processor: " + beanName);
        }
        String encrypted = this.encrypt(testData);
        if (!testData.equals(this.decrypt(encrypted))) {
            throw new IllegalStateException("Encryption/Decryption self-check failed for RSA processor: " + beanName);
        }
    }
}