package cn.com.handthing.starter.connector.wechat;

import cn.com.handthing.starter.connector.wechat.api.WeChatMessageApi;
import cn.com.handthing.starter.connector.wechat.api.WeChatUserApi;
import cn.com.handthing.starter.connector.wechat.config.WeChatConfig;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

/**
 * 微信服务
 * <p>
 * 提供微信平台的核心服务功能，包括消息发送、用户管理等。
 * 支持公众号和小程序的不同业务场景。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "handthing.connector.wechat", name = "enabled", havingValue = "true")
public class WeChatService {

    private final WeChatConfig config;
    private final WeChatMessageApi messageApi;
    private final WeChatUserApi userApi;

    /**
     * 获取消息API
     *
     * @return 消息API实例
     */
    public WeChatMessageApi message() {
        return messageApi;
    }

    /**
     * 获取用户API
     *
     * @return 用户API实例
     */
    public WeChatUserApi user() {
        return userApi;
    }

    /**
     * 检查服务是否可用
     *
     * @return 如果可用返回true，否则返回false
     */
    public boolean isAvailable() {
        return config.isEnabled() && config.isValid();
    }

    /**
     * 获取配置信息
     *
     * @return 配置对象
     */
    public WeChatConfig getConfig() {
        return config;
    }

    /**
     * 获取服务状态
     *
     * @return 服务状态
     */
    public ServiceStatus getStatus() {
        return ServiceStatus.builder()
                .enabled(config.isEnabled())
                .valid(config.isValid())
                .appId(config.getAppId())
                .accountType(config.getAccountType())
                .miniProgramMode(config.isMiniProgramMode())
                .sandboxMode(config.isSandboxMode())
                .encryptMessage(config.isEncryptMessage())
                .apiBaseUrl(config.getEffectiveApiBaseUrl())
                .build();
    }

    /**
     * 服务状态信息
     */
    @Data
    @lombok.Builder
    public static class ServiceStatus {
        /**
         * 是否启用
         */
        private boolean enabled;

        /**
         * 配置是否有效
         */
        private boolean valid;

        /**
         * 应用ID
         */
        private String appId;

        /**
         * 账号类型
         */
        private String accountType;

        /**
         * 是否为小程序模式
         */
        private boolean miniProgramMode;

        /**
         * 是否为沙箱模式
         */
        private boolean sandboxMode;

        /**
         * 是否启用消息加密
         */
        private boolean encryptMessage;

        /**
         * API基础URL
         */
        private String apiBaseUrl;

        @Override
        public String toString() {
            return String.format("WeChatServiceStatus{enabled=%s, valid=%s, appId='%s', accountType='%s', " +
                            "miniProgramMode=%s, sandboxMode=%s, encryptMessage=%s, apiBaseUrl='%s'}",
                    enabled, valid, appId, accountType, miniProgramMode, sandboxMode, encryptMessage, apiBaseUrl);
        }
    }
}
