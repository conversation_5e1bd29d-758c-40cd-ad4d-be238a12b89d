package cn.com.handthing.starter.cache.caffeine;

import cn.com.handthing.starter.cache.CacheService;
import cn.com.handthing.starter.cache.caffeine.config.CaffeineCacheProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cache.CacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;

/**
 * Caffeine缓存自动配置类
 * <p>
 * 当缓存类型配置为caffeine时，自动配置Caffeine缓存管理器和服务
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@AutoConfiguration
@EnableConfigurationProperties(CaffeineCacheProperties.class)
@ConditionalOnClass(com.github.benmanes.caffeine.cache.Caffeine.class)
@ConditionalOnProperty(prefix = "handthing.cache", name = "type", havingValue = "caffeine")
public class CaffeineCacheAutoConfiguration {

    /**
     * 创建Caffeine缓存管理器
     *
     * @param properties Caffeine缓存配置属性
     * @return Caffeine缓存管理器
     */
    @Bean
    @Primary
    @ConditionalOnMissingBean(CacheManager.class)
    public CaffeineCacheManager caffeineCacheManager(CaffeineCacheProperties properties) {
        log.info("Creating CaffeineCacheManager with properties: {}", properties);
        return new CaffeineCacheManager(properties);
    }

    /**
     * 创建Caffeine缓存服务
     *
     * @param cacheManager Caffeine缓存管理器
     * @return Caffeine缓存服务
     */
    @Bean
    @Primary
    @ConditionalOnMissingBean(CacheService.class)
    public CacheService caffeineCacheService(CaffeineCacheManager cacheManager) {
        log.info("Creating CaffeineCacheService with CacheManager: {}", 
                cacheManager.getClass().getSimpleName());
        return new CaffeineCacheService(cacheManager);
    }
}