# 更新日志

本文档记录了 HandThing 多租户认证系统的所有重要变更。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
并且本项目遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [未发布]

### 计划中
- 支持基于JWT的租户信息传递
- 添加租户配额管理功能
- 支持租户数据迁移工具
- 添加租户监控仪表板
- 支持多数据源租户隔离

## [1.0.0] - 2025-07-29

### 新增
- 🎉 **首次发布** HandThing 多租户认证系统
- ✨ **核心功能**
  - 多租户上下文管理（基于ThreadLocal）
  - HTTP头部租户解析器（支持X-Tenant-ID等多种头部）
  - 租户配置服务（支持动态配置管理）
  - 租户解析过滤器（自动租户识别和上下文设置）
  - Spring Boot自动配置支持

- 🗄️ **数据模型**
  - 租户信息表（tenants）
  - 租户配置表（tenant_configs）
  - 完整的数据库索引和约束

- 🔧 **配置管理**
  - 支持多种配置类型（STRING、INTEGER、BOOLEAN、PASSWORD）
  - 配置缓存机制（基于Caffeine）
  - 敏感配置保护
  - 配置变更事件通知

- 🌐 **API接口**
  - `/api/tenant/health` - 系统健康检查
  - `/api/tenant/current` - 当前租户信息
  - `/api/tenant/config` - 租户配置管理
  - `/api/tenant/list` - 租户列表
  - `/api/tenant/stats` - 租户统计信息

- 🔍 **监控和运维**
  - 健康检查端点
  - 指标监控支持
  - 完整的日志记录
  - 租户上下文追踪

- 🛡️ **安全特性**
  - 租户数据完全隔离
  - 输入验证和安全防护
  - 敏感信息脱敏
  - 防止跨租户数据访问

- 🚀 **性能优化**
  - 基于ThreadLocal的高效上下文管理
  - 配置缓存减少数据库查询
  - 连接池优化
  - 自动上下文清理机制

- 📚 **文档和测试**
  - 完整的API文档
  - 架构设计文档
  - 部署指南
  - 开发指南
  - 多租户测试页面
  - 单元测试和集成测试

### 技术特性
- **Spring Boot 3.5+** 支持
- **JDK 17+** 兼容
- **MySQL/PostgreSQL/H2** 数据库支持
- **JPA/Hibernate** 数据访问
- **Caffeine** 缓存支持
- **Maven** 构建工具

### 配置示例
```yaml
handthing:
  auth:
    saas:
      enabled: true
      default-tenant: default
      tenant-required: false
      multi-tenant: true
      cache: true
      
      filter:
        enabled: true
        order: -200
        exclude-paths:
          - "/actuator/**"
          - "/static/**"
      
      resolver:
        http-header:
          enabled: true
          primary-header: X-Tenant-ID
```

### 使用示例
```java
// 获取当前租户ID
String tenantId = TenantContextHolder.getCurrentTenantId();

// 获取租户配置
@Autowired
private TenantConfigService configService;
String jwtSecret = configService.getConfig("jwt.secret");

// 自定义租户解析器
@Component
public class CustomTenantResolver implements TenantResolver {
    @Override
    public String resolveTenantId(HttpServletRequest request) {
        return extractTenantFromDomain(request.getServerName());
    }
}
```

### 数据库支持
- **MySQL 8.0+**
- **PostgreSQL 13+**
- **H2** (开发测试)

### 部署支持
- **Docker** 容器化部署
- **Kubernetes** 集群部署
- **传统服务器** 部署

### 已知问题
- 暂无已知问题

### 迁移指南
这是首次发布，无需迁移。

---

## 版本说明

### 版本号规则
本项目遵循 [语义化版本](https://semver.org/lang/zh-CN/) 规范：

- **主版本号**：当你做了不兼容的 API 修改
- **次版本号**：当你做了向下兼容的功能性新增
- **修订号**：当你做了向下兼容的问题修正

### 变更类型
- `新增` - 新功能
- `变更` - 对现有功能的变更
- `弃用` - 即将移除的功能
- `移除` - 已移除的功能
- `修复` - 问题修复
- `安全` - 安全相关的修复

### 发布周期
- **主版本**：根据需要发布，通常包含重大架构变更
- **次版本**：每月发布，包含新功能和改进
- **修订版本**：根据需要发布，主要是bug修复

### 支持政策
- **当前版本**：完全支持，包括新功能和bug修复
- **前一个主版本**：仅提供安全更新和关键bug修复
- **更早版本**：不再提供支持

### 升级建议
- **修订版本**：建议立即升级，通常只包含bug修复
- **次版本**：建议在测试后升级，包含新功能但保持向下兼容
- **主版本**：需要仔细评估，可能包含破坏性变更

---

## 贡献指南

### 如何贡献变更日志
1. 在 `[未发布]` 部分添加你的变更
2. 使用正确的变更类型标签
3. 提供清晰的变更描述
4. 包含相关的Issue或PR链接

### 变更描述格式
```markdown
- 变更类型：简短的变更描述 [#issue-number](link)
```

示例：
```markdown
- 新增：支持基于域名的租户解析 [#123](https://github.com/handthing/issues/123)
- 修复：修复租户上下文内存泄漏问题 [#124](https://github.com/handthing/issues/124)
```

---

## 联系我们

- **项目主页**：[GitHub](https://github.com/handthing/handthing-springboot3-starter)
- **问题反馈**：[Issues](https://github.com/handthing/handthing-springboot3-starter/issues)
- **功能请求**：[Feature Requests](https://github.com/handthing/handthing-springboot3-starter/discussions)
- **官方文档**：[HandThing Docs](https://docs.handthing.com)

感谢所有贡献者的支持！ 🙏
