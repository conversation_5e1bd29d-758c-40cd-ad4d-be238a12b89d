package cn.com.handthing.starter.datalayer.exception;

/**
 * 数据层异常基类
 * <p>
 * 数据层操作过程中发生错误时抛出此异常。
 * 这是一个运行时异常，调用方可以选择是否捕获处理。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
public class DatalayerException extends RuntimeException {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 错误代码
     */
    private final String errorCode;
    
    /**
     * 操作类型
     */
    private final String operation;
    
    /**
     * 构造函数
     *
     * @param message 错误信息
     */
    public DatalayerException(String message) {
        super(message);
        this.errorCode = null;
        this.operation = null;
    }
    
    /**
     * 构造函数
     *
     * @param message 错误信息
     * @param cause   原因异常
     */
    public DatalayerException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = null;
        this.operation = null;
    }
    
    /**
     * 构造函数
     *
     * @param errorCode 错误代码
     * @param message   错误信息
     */
    public DatalayerException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
        this.operation = null;
    }
    
    /**
     * 构造函数
     *
     * @param errorCode 错误代码
     * @param message   错误信息
     * @param cause     原因异常
     */
    public DatalayerException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.operation = null;
    }
    
    /**
     * 构造函数
     *
     * @param errorCode 错误代码
     * @param operation 操作类型
     * @param message   错误信息
     */
    public DatalayerException(String errorCode, String operation, String message) {
        super(message);
        this.errorCode = errorCode;
        this.operation = operation;
    }
    
    /**
     * 构造函数
     *
     * @param errorCode 错误代码
     * @param operation 操作类型
     * @param message   错误信息
     * @param cause     原因异常
     */
    public DatalayerException(String errorCode, String operation, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.operation = operation;
    }
    
    /**
     * 获取错误代码
     *
     * @return 错误代码
     */
    public String getErrorCode() {
        return errorCode;
    }
    
    /**
     * 获取操作类型
     *
     * @return 操作类型
     */
    public String getOperation() {
        return operation;
    }
    
    /**
     * 获取详细错误信息
     *
     * @return 详细错误信息
     */
    public String getDetailMessage() {
        StringBuilder sb = new StringBuilder();
        
        if (errorCode != null) {
            sb.append("ErrorCode: ").append(errorCode);
        }
        
        if (operation != null) {
            if (sb.length() > 0) {
                sb.append(", ");
            }
            sb.append("Operation: ").append(operation);
        }
        
        if (sb.length() > 0) {
            sb.append(", ");
        }
        sb.append("Message: ").append(getMessage());
        
        return sb.toString();
    }
    
    @Override
    public String toString() {
        return "DatalayerException{" +
                "errorCode='" + errorCode + '\'' +
                ", operation='" + operation + '\'' +
                ", message='" + getMessage() + '\'' +
                '}';
    }
}
