package cn.com.handthing.starter.auth.thirdparty;

import cn.com.handthing.starter.auth.core.AuthenticationRequest;
import cn.com.handthing.starter.auth.core.GrantType;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 微信认证请求
 * <p>
 * 用于微信OAuth2认证的请求对象，包含授权码、应用信息等认证信息。
 * 支持微信公众号网页授权、微信开放平台扫码登录和小程序授权。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WechatAuthenticationRequest extends AuthenticationRequest {

    /**
     * 微信授权码
     */
    private String code;

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 应用密钥
     */
    private String appSecret;

    /**
     * 重定向URI
     */
    private String redirectUri;

    /**
     * 状态参数
     */
    private String state;

    /**
     * 授权类型（web-网页授权，scan-扫码登录，miniprogram-小程序）
     */
    private String authType = "web";

    /**
     * 授权范围（snsapi_base-基础授权，snsapi_userinfo-用户信息授权）
     */
    private String scope = "snsapi_userinfo";

    /**
     * 是否自动注册（如果用户不存在）
     */
    private Boolean autoRegister = true;

    /**
     * 语言设置
     */
    private String lang = "zh_CN";

    /**
     * 默认构造函数
     */
    public WechatAuthenticationRequest() {
        super(GrantType.WECHAT);
    }

    /**
     * 构造函数
     *
     * @param code  授权码
     * @param appId 应用ID
     */
    public WechatAuthenticationRequest(String code, String appId) {
        super(GrantType.WECHAT);
        this.code = code;
        this.appId = appId;
    }

    /**
     * 构造函数
     *
     * @param code      授权码
     * @param appId     应用ID
     * @param appSecret 应用密钥
     */
    public WechatAuthenticationRequest(String code, String appId, String appSecret) {
        super(GrantType.WECHAT);
        this.code = code;
        this.appId = appId;
        this.appSecret = appSecret;
    }

    @Override
    public String getAuthenticationIdentifier() {
        return appId;
    }

    @Override
    public Object getCredentials() {
        return code;
    }

    @Override
    public boolean isValid() {
        return super.isValid() && 
               code != null && !code.trim().isEmpty() &&
               appId != null && !appId.trim().isEmpty();
    }

    /**
     * 判断是否为网页授权类型
     *
     * @return 如果是网页授权返回true，否则返回false
     */
    public boolean isWebAuth() {
        return "web".equals(authType);
    }

    /**
     * 判断是否为扫码登录类型
     *
     * @return 如果是扫码登录返回true，否则返回false
     */
    public boolean isScanAuth() {
        return "scan".equals(authType);
    }

    /**
     * 判断是否为小程序授权类型
     *
     * @return 如果是小程序授权返回true，否则返回false
     */
    public boolean isMiniProgramAuth() {
        return "miniprogram".equals(authType);
    }

    /**
     * 判断是否为基础授权
     *
     * @return 如果是基础授权返回true，否则返回false
     */
    public boolean isBasicScope() {
        return "snsapi_base".equals(scope);
    }

    /**
     * 判断是否为用户信息授权
     *
     * @return 如果是用户信息授权返回true，否则返回false
     */
    public boolean isUserInfoScope() {
        return "snsapi_userinfo".equals(scope);
    }

    /**
     * 创建微信认证请求
     *
     * @param code  授权码
     * @param appId 应用ID
     * @return 微信认证请求
     */
    public static WechatAuthenticationRequest of(String code, String appId) {
        return new WechatAuthenticationRequest(code, appId);
    }

    /**
     * 创建网页授权的微信认证请求
     *
     * @param code        授权码
     * @param appId       应用ID
     * @param appSecret   应用密钥
     * @param redirectUri 重定向URI
     * @return 微信认证请求
     */
    public static WechatAuthenticationRequest forWebAuth(String code, String appId, 
                                                        String appSecret, String redirectUri) {
        WechatAuthenticationRequest request = new WechatAuthenticationRequest(code, appId, appSecret);
        request.setAuthType("web");
        request.setRedirectUri(redirectUri);
        return request;
    }

    /**
     * 创建扫码登录的微信认证请求
     *
     * @param code      授权码
     * @param appId     应用ID
     * @param appSecret 应用密钥
     * @return 微信认证请求
     */
    public static WechatAuthenticationRequest forScanAuth(String code, String appId, String appSecret) {
        WechatAuthenticationRequest request = new WechatAuthenticationRequest(code, appId, appSecret);
        request.setAuthType("scan");
        return request;
    }

    /**
     * 创建小程序授权的微信认证请求
     *
     * @param code      授权码
     * @param appId     应用ID
     * @param appSecret 应用密钥
     * @return 微信认证请求
     */
    public static WechatAuthenticationRequest forMiniProgramAuth(String code, String appId, String appSecret) {
        WechatAuthenticationRequest request = new WechatAuthenticationRequest(code, appId, appSecret);
        request.setAuthType("miniprogram");
        return request;
    }

    /**
     * 创建基础授权的微信认证请求
     *
     * @param code  授权码
     * @param appId 应用ID
     * @return 微信认证请求
     */
    public static WechatAuthenticationRequest withBasicScope(String code, String appId) {
        WechatAuthenticationRequest request = new WechatAuthenticationRequest(code, appId);
        request.setScope("snsapi_base");
        return request;
    }

    /**
     * 创建用户信息授权的微信认证请求
     *
     * @param code  授权码
     * @param appId 应用ID
     * @return 微信认证请求
     */
    public static WechatAuthenticationRequest withUserInfoScope(String code, String appId) {
        WechatAuthenticationRequest request = new WechatAuthenticationRequest(code, appId);
        request.setScope("snsapi_userinfo");
        return request;
    }

    /**
     * 创建自动注册的微信认证请求
     *
     * @param code  授权码
     * @param appId 应用ID
     * @return 微信认证请求
     */
    public static WechatAuthenticationRequest withAutoRegister(String code, String appId) {
        WechatAuthenticationRequest request = new WechatAuthenticationRequest(code, appId);
        request.setAutoRegister(true);
        return request;
    }

    @Override
    public String toString() {
        return String.format("WechatAuthenticationRequest{appId='%s', authType='%s', scope='%s', autoRegister=%s}",
                appId, authType, scope, autoRegister);
    }
}
