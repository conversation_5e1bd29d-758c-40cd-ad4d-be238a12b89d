{"groups": [{"name": "handthing.connector.toutiao", "type": "cn.com.handthing.starter.connector.toutiao.config.ToutiaoConfig", "sourceType": "cn.com.handthing.starter.connector.toutiao.config.ToutiaoConfig"}], "properties": [{"name": "handthing.connector.toutiao.analytics-enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用数据分析功能", "sourceType": "cn.com.handthing.starter.connector.toutiao.config.ToutiaoConfig"}, {"name": "handthing.connector.toutiao.api-base-url", "type": "java.lang.String", "description": "今日头条API基础URL", "sourceType": "cn.com.handthing.starter.connector.toutiao.config.ToutiaoConfig"}, {"name": "handthing.connector.toutiao.app-type", "type": "java.lang.String", "description": "应用类型 web: 网页应用 mobile: 移动应用 server: 服务端应用", "sourceType": "cn.com.handthing.starter.connector.toutiao.config.ToutiaoConfig"}, {"name": "handthing.connector.toutiao.auth-url", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.toutiao.config.ToutiaoConfig"}, {"name": "handthing.connector.toutiao.client-id", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.toutiao.config.ToutiaoConfig"}, {"name": "handthing.connector.toutiao.client-key", "type": "java.lang.String", "description": "今日头条应用ID (Client Key)", "sourceType": "cn.com.handthing.starter.connector.toutiao.config.ToutiaoConfig"}, {"name": "handthing.connector.toutiao.client-secret", "type": "java.lang.String", "description": "今日头条应用密钥 (Client Secret)", "sourceType": "cn.com.handthing.starter.connector.toutiao.config.ToutiaoConfig"}, {"name": "handthing.connector.toutiao.connect-timeout", "type": "java.lang.Integer", "sourceType": "cn.com.handthing.starter.connector.toutiao.config.ToutiaoConfig"}, {"name": "handthing.connector.toutiao.content-publish-enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用内容发布功能", "sourceType": "cn.com.handthing.starter.connector.toutiao.config.ToutiaoConfig"}, {"name": "handthing.connector.toutiao.enabled", "type": "java.lang.Bo<PERSON>an", "sourceType": "cn.com.handthing.starter.connector.toutiao.config.ToutiaoConfig"}, {"name": "handthing.connector.toutiao.enterprise-mode", "type": "java.lang.Bo<PERSON>an", "description": "是否为企业应用模式", "sourceType": "cn.com.handthing.starter.connector.toutiao.config.ToutiaoConfig"}, {"name": "handthing.connector.toutiao.extra-config", "type": "java.util.Map<java.lang.String,java.lang.Object>", "sourceType": "cn.com.handthing.starter.connector.toutiao.config.ToutiaoConfig"}, {"name": "handthing.connector.toutiao.max-retries", "type": "java.lang.Integer", "sourceType": "cn.com.handthing.starter.connector.toutiao.config.ToutiaoConfig"}, {"name": "handthing.connector.toutiao.read-timeout", "type": "java.lang.Integer", "sourceType": "cn.com.handthing.starter.connector.toutiao.config.ToutiaoConfig"}, {"name": "handthing.connector.toutiao.redirect-uri", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.toutiao.config.ToutiaoConfig"}, {"name": "handthing.connector.toutiao.sandbox", "type": "java.lang.Bo<PERSON>an", "sourceType": "cn.com.handthing.starter.connector.toutiao.config.ToutiaoConfig"}, {"name": "handthing.connector.toutiao.sandbox-api-base-url", "type": "java.lang.String", "description": "沙箱环境API基础URL", "sourceType": "cn.com.handthing.starter.connector.toutiao.config.ToutiaoConfig"}, {"name": "handthing.connector.toutiao.sandbox-mode", "type": "java.lang.Bo<PERSON>an", "description": "是否使用沙箱环境", "sourceType": "cn.com.handthing.starter.connector.toutiao.config.ToutiaoConfig"}, {"name": "handthing.connector.toutiao.scope", "type": "java.lang.String", "description": "授权范围", "sourceType": "cn.com.handthing.starter.connector.toutiao.config.ToutiaoConfig"}, {"name": "handthing.connector.toutiao.token-url", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.toutiao.config.ToutiaoConfig"}, {"name": "handthing.connector.toutiao.user-info-url", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.toutiao.config.ToutiaoConfig"}, {"name": "handthing.connector.toutiao.user-management-enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用用户管理功能", "sourceType": "cn.com.handthing.starter.connector.toutiao.config.ToutiaoConfig"}], "hints": [], "ignored": {"properties": []}}