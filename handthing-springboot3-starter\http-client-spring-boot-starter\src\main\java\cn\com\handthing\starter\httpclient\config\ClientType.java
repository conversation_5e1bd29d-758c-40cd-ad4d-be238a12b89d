package cn.com.handthing.starter.httpclient.config;

/**
 * 定义了HTTP客户端的具体实现类型。
 * <p>
 * 用于 {@code handthing.http-client.client-type} 配置项。
 */
public enum ClientType {

    /**
     * 自动检测。
     * <p>
     * 默认值。如果类路径下存在 WebClient，则优先使用 WebClient；否则，如果存在 RestTemplate，则使用 RestTemplate。
     */
    AUTO,

    /**
     * 强制使用 WebClient (非阻塞) 实现。
     * <p>
     * 需要项目中存在 {@code handthing-webclient-http-client-starter} 依赖。
     */
    WEBCLIENT,

    /**
     * 强制使用 RestTemplate (阻塞) 实现。
     * <p>
     * 需要项目中存在 {@code handthing-resttemplate-http-client-starter} 依赖。
     */
    RESTTEMPLATE
}