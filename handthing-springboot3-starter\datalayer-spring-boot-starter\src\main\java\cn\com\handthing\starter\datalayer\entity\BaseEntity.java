package cn.com.handthing.starter.datalayer.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.Version;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 基础实体类
 * <p>
 * 提供所有实体的公共字段，包括审计字段、逻辑删除、乐观锁等。
 * 不包含tenantId字段，租户相关功能由插件模块扩展。
 * </p>
 * 
 * <h3>公共字段：</h3>
 * <ul>
 *   <li>createBy - 创建人</li>
 *   <li>createTime - 创建时间</li>
 *   <li>updateBy - 更新人</li>
 *   <li>updateTime - 更新时间</li>
 *   <li>version - 版本号（乐观锁）</li>
 *   <li>deleted - 逻辑删除标记</li>
 * </ul>
 * 
 * <h3>使用示例：</h3>
 * <pre>
 * &#64;Data
 * &#64;EqualsAndHashCode(callSuper = true)
 * &#64;TableName("products")
 * public class Product extends BaseEntity {
 *     &#64;IdSetter
 *     &#64;TableId
 *     private Long id;
 *     
 *     private String name;
 *     private BigDecimal price;
 * }
 * </pre>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
@EqualsAndHashCode
public abstract class BaseEntity implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 创建人
     * <p>
     * 在插入时自动填充，从AuthContextHolder获取当前用户信息
     * </p>
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;
    
    /**
     * 创建时间
     * <p>
     * 在插入时自动填充当前时间
     * </p>
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    /**
     * 更新人
     * <p>
     * 在插入和更新时自动填充，从AuthContextHolder获取当前用户信息
     * </p>
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;
    
    /**
     * 更新时间
     * <p>
     * 在插入和更新时自动填充当前时间
     * </p>
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
    
    /**
     * 版本号（乐观锁）
     * <p>
     * 用于乐观锁控制，每次更新时自动递增
     * </p>
     */
    @Version
    @TableField(fill = FieldFill.INSERT)
    private Long version;
    
    /**
     * 逻辑删除标记
     * <p>
     * 0-未删除，1-已删除
     * 使用逻辑删除，不会真正删除数据库记录
     * </p>
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private Integer deleted;
    
    /**
     * 检查实体是否为新实体
     * <p>
     * 子类可以重写此方法来定义新实体的判断逻辑
     * </p>
     * 
     * @return 如果是新实体返回true，否则返回false
     */
    public boolean isNew() {
        return createTime == null;
    }
    
    /**
     * 检查实体是否已被逻辑删除
     * 
     * @return 如果已删除返回true，否则返回false
     */
    public boolean isDeleted() {
        return deleted != null && deleted == 1;
    }
    
    /**
     * 获取实体的审计信息摘要
     * 
     * @return 审计信息字符串
     */
    public String getAuditSummary() {
        StringBuilder sb = new StringBuilder();
        sb.append("Audit{");
        
        if (createBy != null) {
            sb.append("createBy=").append(createBy);
        }
        
        if (createTime != null) {
            if (sb.length() > 6) sb.append(", ");
            sb.append("createTime=").append(createTime);
        }
        
        if (updateBy != null) {
            if (sb.length() > 6) sb.append(", ");
            sb.append("updateBy=").append(updateBy);
        }
        
        if (updateTime != null) {
            if (sb.length() > 6) sb.append(", ");
            sb.append("updateTime=").append(updateTime);
        }
        
        if (version != null) {
            if (sb.length() > 6) sb.append(", ");
            sb.append("version=").append(version);
        }
        
        if (deleted != null) {
            if (sb.length() > 6) sb.append(", ");
            sb.append("deleted=").append(deleted);
        }
        
        sb.append("}");
        return sb.toString();
    }
    
    /**
     * 预插入处理
     * <p>
     * 子类可以重写此方法来实现自定义的预插入逻辑
     * </p>
     */
    public void preInsert() {
        // 默认实现为空，子类可以重写
    }
    
    /**
     * 预更新处理
     * <p>
     * 子类可以重写此方法来实现自定义的预更新逻辑
     * </p>
     */
    public void preUpdate() {
        // 默认实现为空，子类可以重写
    }
    
    /**
     * 后插入处理
     * <p>
     * 子类可以重写此方法来实现自定义的后插入逻辑
     * </p>
     */
    public void postInsert() {
        // 默认实现为空，子类可以重写
    }
    
    /**
     * 后更新处理
     * <p>
     * 子类可以重写此方法来实现自定义的后更新逻辑
     * </p>
     */
    public void postUpdate() {
        // 默认实现为空，子类可以重写
    }
}
