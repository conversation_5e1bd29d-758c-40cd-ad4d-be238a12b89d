package cn.com.handthing.starter.tenant.service;

import cn.com.handthing.starter.tenant.config.TenantConfig;
import cn.com.handthing.starter.tenant.config.TenantConfigKey;
import cn.com.handthing.starter.tenant.config.TenantConfigType;
import cn.com.handthing.starter.tenant.context.TenantStatus;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 租户配置服务接口
 * <p>
 * 提供分层的租户配置读取能力，实现分层配置读取逻辑：租户配置 -> 全局默认配置。
 * 集成cache-starter进行重度缓存，支持配置项的类型转换和验证，提供配置变更通知机制。
 * </p>
 *
 * <AUTHOR>
 * @since V3.0.0
 */
public interface TenantConfigService {

    // ========== 基础配置操作 ==========

    /**
     * 获取租户配置值（字符串类型）
     *
     * @param tenantId  租户ID
     * @param configKey 配置键
     * @return 配置值，如果不存在则返回null
     */
    String getConfigValue(String tenantId, String configKey);

    /**
     * 获取租户配置值（指定类型）
     *
     * @param tenantId  租户ID
     * @param configKey 配置键
     * @param clazz     目标类型
     * @param <T>       泛型类型
     * @return 配置值，如果不存在则返回null
     */
    <T> T getConfigValue(String tenantId, String configKey, Class<T> clazz);

    /**
     * 获取租户配置值（带默认值）
     *
     * @param tenantId     租户ID
     * @param configKey    配置键
     * @param defaultValue 默认值
     * @return 配置值，如果不存在则返回默认值
     */
    String getConfigValue(String tenantId, String configKey, String defaultValue);

    /**
     * 获取租户配置值（指定类型，带默认值）
     *
     * @param tenantId     租户ID
     * @param configKey    配置键
     * @param clazz        目标类型
     * @param defaultValue 默认值
     * @param <T>          泛型类型
     * @return 配置值，如果不存在则返回默认值
     */
    <T> T getConfigValue(String tenantId, String configKey, Class<T> clazz, T defaultValue);

    /**
     * 获取租户配置值（使用枚举键）
     *
     * @param tenantId  租户ID
     * @param configKey 配置键枚举
     * @return 配置值，如果不存在则返回枚举定义的默认值
     */
    String getConfigValue(String tenantId, TenantConfigKey configKey);

    /**
     * 获取租户配置值（使用枚举键，指定类型）
     *
     * @param tenantId  租户ID
     * @param configKey 配置键枚举
     * @param clazz     目标类型
     * @param <T>       泛型类型
     * @return 配置值，如果不存在则返回枚举定义的默认值
     */
    <T> T getConfigValue(String tenantId, TenantConfigKey configKey, Class<T> clazz);

    // ========== 配置对象操作 ==========

    /**
     * 获取租户配置对象
     *
     * @param tenantId  租户ID
     * @param configKey 配置键
     * @return 配置对象，如果不存在则返回空
     */
    Optional<TenantConfig> getConfig(String tenantId, String configKey);

    /**
     * 获取租户的所有配置
     *
     * @param tenantId 租户ID
     * @return 配置列表
     */
    List<TenantConfig> getAllConfigs(String tenantId);

    /**
     * 获取租户的所有启用配置
     *
     * @param tenantId 租户ID
     * @return 启用的配置列表
     */
    List<TenantConfig> getEnabledConfigs(String tenantId);

    /**
     * 获取租户配置映射
     *
     * @param tenantId 租户ID
     * @return 配置键值映射
     */
    Map<String, String> getConfigMap(String tenantId);

    /**
     * 获取租户配置映射（仅启用的配置）
     *
     * @param tenantId 租户ID
     * @return 启用的配置键值映射
     */
    Map<String, String> getEnabledConfigMap(String tenantId);

    // ========== 配置设置操作 ==========

    /**
     * 设置租户配置
     *
     * @param tenantId    租户ID
     * @param configKey   配置键
     * @param configValue 配置值
     */
    void setConfig(String tenantId, String configKey, String configValue);

    /**
     * 设置租户配置（指定类型）
     *
     * @param tenantId    租户ID
     * @param configKey   配置键
     * @param configValue 配置值
     * @param configType  配置类型
     */
    void setConfig(String tenantId, String configKey, String configValue, TenantConfigType configType);

    /**
     * 设置租户配置（使用枚举键）
     *
     * @param tenantId    租户ID
     * @param configKey   配置键枚举
     * @param configValue 配置值
     */
    void setConfig(String tenantId, TenantConfigKey configKey, String configValue);

    /**
     * 批量设置租户配置
     *
     * @param tenantId   租户ID
     * @param configMap  配置映射
     */
    void setConfigs(String tenantId, Map<String, String> configMap);

    /**
     * 保存或更新租户配置
     *
     * @param config 配置对象
     * @return 保存后的配置对象
     */
    TenantConfig saveConfig(TenantConfig config);

    /**
     * 批量保存或更新租户配置
     *
     * @param configs 配置对象列表
     * @return 保存后的配置对象列表
     */
    List<TenantConfig> saveConfigs(List<TenantConfig> configs);

    // ========== 配置删除操作 ==========

    /**
     * 删除租户配置
     *
     * @param tenantId  租户ID
     * @param configKey 配置键
     * @return 如果删除成功返回true，否则返回false
     */
    boolean deleteConfig(String tenantId, String configKey);

    /**
     * 删除租户的所有配置
     *
     * @param tenantId 租户ID
     * @return 删除的配置数量
     */
    long deleteAllConfigs(String tenantId);

    /**
     * 批量删除租户配置
     *
     * @param tenantId   租户ID
     * @param configKeys 配置键列表
     * @return 删除的配置数量
     */
    long deleteConfigs(String tenantId, List<String> configKeys);

    // ========== 租户管理操作 ==========

    /**
     * 检查租户是否存在
     *
     * @param tenantId 租户ID
     * @return 如果存在返回true，否则返回false
     */
    boolean tenantExists(String tenantId);

    /**
     * 获取租户名称
     *
     * @param tenantId 租户ID
     * @return 租户名称，如果不存在则返回null
     */
    String getTenantName(String tenantId);

    /**
     * 获取租户状态
     *
     * @param tenantId 租户ID
     * @return 租户状态，如果不存在则返回null
     */
    TenantStatus getTenantStatus(String tenantId);

    /**
     * 设置租户状态
     *
     * @param tenantId 租户ID
     * @param status   租户状态
     */
    void setTenantStatus(String tenantId, TenantStatus status);

    /**
     * 获取所有租户ID
     *
     * @return 租户ID列表
     */
    List<String> getAllTenantIds();

    // ========== 配置验证操作 ==========

    /**
     * 验证配置值
     *
     * @param configKey   配置键
     * @param configValue 配置值
     * @param configType  配置类型
     * @return 如果有效返回true，否则返回false
     */
    boolean validateConfig(String configKey, String configValue, TenantConfigType configType);

    /**
     * 验证租户配置
     *
     * @param config 配置对象
     * @return 如果有效返回true，否则返回false
     */
    boolean validateConfig(TenantConfig config);

    // ========== 缓存管理操作 ==========

    /**
     * 清除租户配置缓存
     *
     * @param tenantId 租户ID
     */
    void clearCache(String tenantId);

    /**
     * 清除指定配置的缓存
     *
     * @param tenantId  租户ID
     * @param configKey 配置键
     */
    void clearCache(String tenantId, String configKey);

    /**
     * 清除所有配置缓存
     */
    void clearAllCache();

    /**
     * 刷新租户配置缓存
     *
     * @param tenantId 租户ID
     */
    void refreshCache(String tenantId);

    // ========== 配置监听操作 ==========

    /**
     * 添加配置变更监听器
     *
     * @param listener 配置变更监听器
     */
    void addConfigChangeListener(TenantConfigChangeListener listener);

    /**
     * 移除配置变更监听器
     *
     * @param listener 配置变更监听器
     */
    void removeConfigChangeListener(TenantConfigChangeListener listener);

    // ========== 配置统计操作 ==========

    /**
     * 获取租户配置统计信息
     *
     * @param tenantId 租户ID
     * @return 配置统计信息
     */
    TenantConfigStats getConfigStats(String tenantId);

    /**
     * 获取全局配置统计信息
     *
     * @return 全局配置统计信息
     */
    Map<String, TenantConfigStats> getAllConfigStats();

    // ========== 配置导入导出操作 ==========

    /**
     * 导出租户配置
     *
     * @param tenantId        租户ID
     * @param includeSensitive 是否包含敏感配置
     * @return 配置JSON字符串
     */
    String exportConfigs(String tenantId, boolean includeSensitive);

    /**
     * 导入租户配置
     *
     * @param tenantId   租户ID
     * @param configJson 配置JSON字符串
     * @param overwrite  是否覆盖已存在的配置
     * @return 导入的配置数量
     */
    int importConfigs(String tenantId, String configJson, boolean overwrite);

    /**
     * 复制租户配置
     *
     * @param sourceTenantId 源租户ID
     * @param targetTenantId 目标租户ID
     * @param overwrite      是否覆盖已存在的配置
     * @return 复制的配置数量
     */
    int copyConfigs(String sourceTenantId, String targetTenantId, boolean overwrite);
}
