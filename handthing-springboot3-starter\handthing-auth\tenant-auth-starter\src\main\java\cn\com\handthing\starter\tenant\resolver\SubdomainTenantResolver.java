package cn.com.handthing.starter.tenant.resolver;

import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 子域名租户解析器
 * <p>
 * 从子域名解析租户ID，支持自定义子域名格式和验证规则。
 * 例如：tenant-a.myapp.com -> tenant-a
 * </p>
 *
 * <AUTHOR>
 * @since V3.0.0
 */
public class SubdomainTenantResolver implements TenantResolver {

    private static final Logger log = LoggerFactory.getLogger(SubdomainTenantResolver.class);

    /**
     * 默认子域名模式：{tenant}.domain.com
     */
    private static final String DEFAULT_PATTERN = "^([a-zA-Z0-9][a-zA-Z0-9_-]*[a-zA-Z0-9]|[a-zA-Z0-9])\\..*";

    /**
     * 子域名模式
     */
    private final Pattern subdomainPattern;

    /**
     * 基础域名
     */
    private final String baseDomain;

    /**
     * 是否忽略www前缀
     */
    private final boolean ignoreWww;

    /**
     * 构造函数（使用默认模式）
     */
    public SubdomainTenantResolver() {
        this(DEFAULT_PATTERN, null, true);
    }

    /**
     * 构造函数（指定基础域名）
     *
     * @param baseDomain 基础域名，例如 "myapp.com"
     */
    public SubdomainTenantResolver(String baseDomain) {
        this(DEFAULT_PATTERN, baseDomain, true);
    }

    /**
     * 构造函数（指定模式和基础域名）
     *
     * @param pattern    子域名模式
     * @param baseDomain 基础域名
     * @param ignoreWww  是否忽略www前缀
     */
    public SubdomainTenantResolver(String pattern, String baseDomain, boolean ignoreWww) {
        this.subdomainPattern = Pattern.compile(pattern);
        this.baseDomain = baseDomain;
        this.ignoreWww = ignoreWww;
        
        log.debug("SubdomainTenantResolver initialized with pattern: {}, baseDomain: {}, ignoreWww: {}", 
                  pattern, baseDomain, ignoreWww);
    }

    @Override
    public String resolveTenantId(HttpServletRequest request) {
        String host = getHost(request);
        if (!StringUtils.hasText(host)) {
            log.debug("No host header found in request");
            return null;
        }

        log.debug("Resolving tenant from host: {}", host);

        // 移除端口号
        String domain = removePort(host);
        
        // 忽略www前缀
        if (ignoreWww && domain.startsWith("www.")) {
            domain = domain.substring(4);
        }

        // 如果指定了基础域名，检查是否匹配
        if (baseDomain != null && !domain.endsWith("." + baseDomain)) {
            log.debug("Host {} does not match base domain {}", domain, baseDomain);
            return null;
        }

        // 使用正则表达式提取租户ID
        Matcher matcher = subdomainPattern.matcher(domain);
        if (matcher.matches() && matcher.groupCount() >= 1) {
            String tenantId = matcher.group(1);
            log.debug("Extracted tenant ID: {} from host: {}", tenantId, host);
            return tenantId;
        }

        log.debug("No tenant ID found in host: {}", host);
        return null;
    }

    @Override
    public boolean supports(HttpServletRequest request) {
        String host = getHost(request);
        return StringUtils.hasText(host);
    }

    @Override
    public int getOrder() {
        return 10; // 高优先级
    }

    @Override
    public String getName() {
        return "SubdomainTenantResolver";
    }

    @Override
    public String getDescription() {
        return "Resolves tenant ID from subdomain in Host header";
    }

    /**
     * 获取Host头
     *
     * @param request HTTP请求
     * @return Host头值
     */
    private String getHost(HttpServletRequest request) {
        // 优先使用X-Forwarded-Host（代理环境）
        String host = request.getHeader("X-Forwarded-Host");
        if (StringUtils.hasText(host)) {
            // 如果有多个值，取第一个
            return host.split(",")[0].trim();
        }

        // 使用标准Host头
        return request.getHeader("Host");
    }

    /**
     * 移除端口号
     *
     * @param host 主机名（可能包含端口号）
     * @return 移除端口号后的主机名
     */
    private String removePort(String host) {
        if (host == null) {
            return null;
        }

        int colonIndex = host.lastIndexOf(':');
        if (colonIndex > 0) {
            // 检查是否是IPv6地址
            if (host.startsWith("[") && host.contains("]:")) {
                // IPv6地址格式：[::1]:8080
                return host.substring(0, host.lastIndexOf("]:") + 1);
            } else {
                // IPv4地址或域名格式：localhost:8080
                return host.substring(0, colonIndex);
            }
        }

        return host;
    }

    /**
     * 创建基于模式的子域名解析器
     *
     * @param pattern 子域名模式，使用{tenant}作为占位符
     * @return 子域名解析器实例
     */
    public static SubdomainTenantResolver fromPattern(String pattern) {
        if (pattern == null || pattern.trim().isEmpty()) {
            return new SubdomainTenantResolver();
        }

        // 将{tenant}占位符转换为正则表达式
        String regexPattern = pattern.replace("{tenant}", "([a-zA-Z0-9][a-zA-Z0-9_-]*[a-zA-Z0-9]|[a-zA-Z0-9])");
        
        // 转义其他特殊字符
        regexPattern = regexPattern.replace(".", "\\.");
        
        // 确保匹配整个字符串
        if (!regexPattern.startsWith("^")) {
            regexPattern = "^" + regexPattern;
        }
        if (!regexPattern.endsWith("$")) {
            regexPattern = regexPattern + "$";
        }

        return new SubdomainTenantResolver(regexPattern, null, true);
    }

    /**
     * 创建基于基础域名的子域名解析器
     *
     * @param baseDomain 基础域名
     * @return 子域名解析器实例
     */
    public static SubdomainTenantResolver forBaseDomain(String baseDomain) {
        return new SubdomainTenantResolver(baseDomain);
    }

    /**
     * 获取当前配置信息
     *
     * @return 配置信息字符串
     */
    public String getConfigInfo() {
        return String.format("SubdomainTenantResolver{pattern=%s, baseDomain=%s, ignoreWww=%s}", 
                           subdomainPattern.pattern(), baseDomain, ignoreWww);
    }
}
