package cn.com.handthing.starter.cache.level;

import cn.com.handthing.starter.cache.CacheService;
import cn.com.handthing.starter.cache.CacheStats;
import cn.com.handthing.starter.cache.exception.CacheOperationException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;

import java.time.Duration;

/**
 * 多级缓存服务实现
 * <p>
 * 基于多级缓存架构的缓存服务，提供L1+L2缓存的统一访问接口
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@RequiredArgsConstructor
public class LevelCacheService implements CacheService {

    private final LevelCacheManager cacheManager;

    @Override
    public <T> T get(String cacheName, String key) {
        try {
            Cache cache = getCache(cacheName);
            Cache.ValueWrapper wrapper = cache.get(key);
            return wrapper != null ? (T) wrapper.get() : null;
        } catch (Exception e) {
            log.error("Failed to get cache value for cacheName: {}, key: {}", cacheName, key, e);
            throw new CacheOperationException("Failed to get cache value", e);
        }
    }

    @Override
    public <T> T get(String cacheName, String key, Class<T> type) {
        try {
            Cache cache = getCache(cacheName);
            return cache.get(key, type);
        } catch (Exception e) {
            log.error("Failed to get cache value for cacheName: {}, key: {}, type: {}", 
                    cacheName, key, type.getName(), e);
            throw new CacheOperationException("Failed to get cache value", e);
        }
    }

    @Override
    public void put(String cacheName, String key, Object value) {
        try {
            Cache cache = getCache(cacheName);
            cache.put(key, value);
            log.debug("Put cache value for cacheName: {}, key: {}", cacheName, key);
        } catch (Exception e) {
            log.error("Failed to put cache value for cacheName: {}, key: {}", cacheName, key, e);
            throw new CacheOperationException("Failed to put cache value", e);
        }
    }

    @Override
    public void put(String cacheName, String key, Object value, Duration ttl) {
        // 多级缓存的TTL由各级缓存的配置控制
        log.debug("Level cache TTL is controlled by individual cache configurations");
        put(cacheName, key, value);
    }

    @Override
    public void evict(String cacheName, String key) {
        try {
            Cache cache = getCache(cacheName);
            cache.evict(key);
            log.debug("Evicted cache value for cacheName: {}, key: {}", cacheName, key);
        } catch (Exception e) {
            log.error("Failed to evict cache value for cacheName: {}, key: {}", cacheName, key, e);
            throw new CacheOperationException("Failed to evict cache value", e);
        }
    }

    @Override
    public void clear(String cacheName) {
        try {
            Cache cache = getCache(cacheName);
            cache.clear();
            log.debug("Cleared cache for cacheName: {}", cacheName);
        } catch (Exception e) {
            log.error("Failed to clear cache for cacheName: {}", cacheName, e);
            throw new CacheOperationException("Failed to clear cache", e);
        }
    }

    @Override
    public boolean exists(String cacheName, String key) {
        try {
            Cache cache = getCache(cacheName);
            return cache.get(key) != null;
        } catch (Exception e) {
            log.error("Failed to check cache existence for cacheName: {}, key: {}", cacheName, key, e);
            return false;
        }
    }

    @Override
    public CacheStats getStats(String cacheName) {
        try {
            return cacheManager.getCacheStats(cacheName);
        } catch (Exception e) {
            log.error("Failed to get cache stats for cacheName: {}", cacheName, e);
            return null;
        }
    }

    /**
     * 获取L1缓存值
     *
     * @param cacheName 缓存名称
     * @param key       缓存键
     * @param <T>       值类型
     * @return L1缓存值
     */
    public <T> T getFromL1(String cacheName, String key) {
        try {
            Cache cache = getCache(cacheName);
            if (cache instanceof LevelCache) {
                LevelCache levelCache = (LevelCache) cache;
                Cache.ValueWrapper wrapper = levelCache.getL1Cache().get(key);
                return wrapper != null ? (T) wrapper.get() : null;
            }
            return null;
        } catch (Exception e) {
            log.error("Failed to get L1 cache value for cacheName: {}, key: {}", cacheName, key, e);
            return null;
        }
    }

    /**
     * 获取L2缓存值
     *
     * @param cacheName 缓存名称
     * @param key       缓存键
     * @param <T>       值类型
     * @return L2缓存值
     */
    public <T> T getFromL2(String cacheName, String key) {
        try {
            Cache cache = getCache(cacheName);
            if (cache instanceof LevelCache) {
                LevelCache levelCache = (LevelCache) cache;
                Cache.ValueWrapper wrapper = levelCache.getL2Cache().get(key);
                return wrapper != null ? (T) wrapper.get() : null;
            }
            return null;
        } catch (Exception e) {
            log.error("Failed to get L2 cache value for cacheName: {}, key: {}", cacheName, key, e);
            return null;
        }
    }

    /**
     * 清空所有缓存
     */
    public void clearAll() {
        try {
            cacheManager.clearAll();
            log.info("Cleared all level caches");
        } catch (Exception e) {
            log.error("Failed to clear all caches", e);
            throw new CacheOperationException("Failed to clear all caches", e);
        }
    }

    /**
     * 获取同步服务实例ID
     *
     * @return 实例ID
     */
    public String getInstanceId() {
        if (cacheManager.getSyncService() != null) {
            return cacheManager.getSyncService().getInstanceId();
        }
        return null;
    }

    /**
     * 检查缓存是否启用同步
     *
     * @param cacheName 缓存名称
     * @return 是否启用同步
     */
    public boolean isSyncEnabled(String cacheName) {
        try {
            Cache cache = getCache(cacheName);
            if (cache instanceof LevelCache) {
                return cacheManager.getSyncService() != null;
            }
            return false;
        } catch (Exception e) {
            log.error("Failed to check sync status for cacheName: {}", cacheName, e);
            return false;
        }
    }

    /**
     * 获取缓存实例
     *
     * @param cacheName 缓存名称
     * @return 缓存实例
     * @throws CacheOperationException 如果缓存不存在
     */
    private Cache getCache(String cacheName) {
        Cache cache = cacheManager.getCache(cacheName);
        if (cache == null) {
            throw new CacheOperationException("Cache not found: " + cacheName);
        }
        return cache;
    }
}