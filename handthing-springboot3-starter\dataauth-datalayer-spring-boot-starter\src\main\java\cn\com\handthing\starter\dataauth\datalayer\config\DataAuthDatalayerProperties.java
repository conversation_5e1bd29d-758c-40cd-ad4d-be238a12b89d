package cn.com.handthing.starter.dataauth.datalayer.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashSet;
import java.util.Set;

/**
 * 数据权限配置属性
 * <p>
 * 提供数据权限相关的配置选项，包括权限策略、缓存配置等。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
@ConfigurationProperties(prefix = "handthing.datalayer.dataauth")
public class DataAuthDatalayerProperties {
    
    /**
     * 是否启用数据权限功能
     */
    private boolean enabled = true;
    
    /**
     * 是否启用AOP拦截器
     */
    private boolean aopEnabled = true;
    
    /**
     * 是否启用严格模式
     * <p>
     * 严格模式下，权限相关的错误会抛出异常而不是记录日志
     * </p>
     */
    private boolean strictMode = false;
    
    /**
     * 默认权限失败策略
     */
    private String defaultFailureStrategy = "FILTER";
    
    /**
     * 是否启用权限缓存
     */
    private boolean cacheEnabled = true;
    
    /**
     * 权限缓存配置
     */
    private Cache cache = new Cache();
    
    /**
     * 超级管理员配置
     */
    private SuperAdmin superAdmin = new SuperAdmin();
    
    /**
     * 忽略权限检查的包路径
     */
    private Set<String> ignorePackages = new HashSet<>();
    
    /**
     * 忽略权限检查的类名
     */
    private Set<String> ignoreClasses = new HashSet<>();
    
    /**
     * 忽略权限检查的方法名模式
     */
    private Set<String> ignoreMethodPatterns = new HashSet<>();
    
    /**
     * 权限提供者配置
     */
    private Provider provider = new Provider();
    
    /**
     * 权限缓存配置
     */
    @Data
    public static class Cache {
        
        /**
         * 缓存类型
         */
        private String type = "memory";
        
        /**
         * 缓存过期时间（秒）
         */
        private long expireSeconds = 300;
        
        /**
         * 最大缓存条目数
         */
        private int maxSize = 1000;
        
        /**
         * 缓存键前缀
         */
        private String keyPrefix = "dataauth:";
        
        /**
         * 是否启用缓存统计
         */
        private boolean statisticsEnabled = false;
    }
    
    /**
     * 超级管理员配置
     */
    @Data
    public static class SuperAdmin {
        
        /**
         * 是否启用超级管理员跳过权限检查
         */
        private boolean enabled = true;
        
        /**
         * 超级管理员角色名称
         */
        private Set<String> roles = new HashSet<>();
        
        /**
         * 超级管理员用户ID
         */
        private Set<String> userIds = new HashSet<>();
        
        /**
         * 超级管理员检查策略
         */
        private String checkStrategy = "role";
    }
    
    /**
     * 权限提供者配置
     */
    @Data
    public static class Provider {
        
        /**
         * 部门权限配置
         */
        private Dept dept = new Dept();
        
        /**
         * 用户权限配置
         */
        private User user = new User();
        
        /**
         * 角色权限配置
         */
        private Role role = new Role();
        
        /**
         * 部门权限配置
         */
        @Data
        public static class Dept {
            
            /**
             * 是否启用部门权限
             */
            private boolean enabled = true;
            
            /**
             * 默认部门字段名
             */
            private String defaultField = "dept_id";
            
            /**
             * 是否支持部门层级
             */
            private boolean hierarchyEnabled = true;
            
            /**
             * 部门层级查询SQL
             */
            private String hierarchyQuery = "";
        }
        
        /**
         * 用户权限配置
         */
        @Data
        public static class User {
            
            /**
             * 是否启用用户权限
             */
            private boolean enabled = true;
            
            /**
             * 默认用户字段名
             */
            private String defaultField = "create_by";
            
            /**
             * 用户ID字段名
             */
            private String userIdField = "user_id";
        }
        
        /**
         * 角色权限配置
         */
        @Data
        public static class Role {
            
            /**
             * 是否启用角色权限
             */
            private boolean enabled = true;
            
            /**
             * 默认角色字段名
             */
            private String defaultField = "role_id";
            
            /**
             * 角色权限查询SQL
             */
            private String permissionQuery = "";
        }
    }
    
    /**
     * 初始化默认配置
     */
    public void initDefaults() {
        // 添加默认忽略包
        if (ignorePackages.isEmpty()) {
            ignorePackages.add("cn.com.handthing.starter.dataauth");
            ignorePackages.add("org.springframework");
            ignorePackages.add("com.baomidou.mybatisplus");
        }
        
        // 添加默认忽略类
        if (ignoreClasses.isEmpty()) {
            ignoreClasses.add("*Controller");
            ignoreClasses.add("*Config*");
            ignoreClasses.add("*Test*");
        }
        
        // 添加默认忽略方法模式
        if (ignoreMethodPatterns.isEmpty()) {
            ignoreMethodPatterns.add("get*");
            ignoreMethodPatterns.add("is*");
            ignoreMethodPatterns.add("has*");
            ignoreMethodPatterns.add("toString");
            ignoreMethodPatterns.add("hashCode");
            ignoreMethodPatterns.add("equals");
        }
        
        // 添加默认超级管理员角色
        if (superAdmin.roles.isEmpty()) {
            superAdmin.roles.add("SUPER_ADMIN");
            superAdmin.roles.add("SYSTEM_ADMIN");
        }
    }
    
    /**
     * 验证配置的有效性
     */
    public void validate() {
        if (enabled) {
            validateCacheConfig();
            validateProviderConfig();
        }
    }
    
    /**
     * 验证缓存配置
     */
    private void validateCacheConfig() {
        if (cacheEnabled) {
            if (cache.expireSeconds <= 0) {
                throw new IllegalArgumentException("Cache expire seconds must be positive");
            }
            
            if (cache.maxSize <= 0) {
                throw new IllegalArgumentException("Cache max size must be positive");
            }
            
            if (cache.keyPrefix == null || cache.keyPrefix.trim().isEmpty()) {
                throw new IllegalArgumentException("Cache key prefix cannot be null or empty");
            }
        }
    }
    
    /**
     * 验证提供者配置
     */
    private void validateProviderConfig() {
        if (provider.dept.enabled) {
            if (provider.dept.defaultField == null || provider.dept.defaultField.trim().isEmpty()) {
                throw new IllegalArgumentException("Dept default field cannot be null or empty");
            }
        }
        
        if (provider.user.enabled) {
            if (provider.user.defaultField == null || provider.user.defaultField.trim().isEmpty()) {
                throw new IllegalArgumentException("User default field cannot be null or empty");
            }
        }
        
        if (provider.role.enabled) {
            if (provider.role.defaultField == null || provider.role.defaultField.trim().isEmpty()) {
                throw new IllegalArgumentException("Role default field cannot be null or empty");
            }
        }
    }
    
    /**
     * 检查是否应该忽略指定的类
     * 
     * @param className 类名
     * @return 如果应该忽略返回true，否则返回false
     */
    public boolean shouldIgnoreClass(String className) {
        if (className == null || className.trim().isEmpty()) {
            return false;
        }
        
        // 检查包路径
        for (String ignorePackage : ignorePackages) {
            if (className.startsWith(ignorePackage)) {
                return true;
            }
        }
        
        // 检查类名模式
        for (String ignoreClass : ignoreClasses) {
            if (matchesPattern(className, ignoreClass)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 检查是否应该忽略指定的方法
     * 
     * @param methodName 方法名
     * @return 如果应该忽略返回true，否则返回false
     */
    public boolean shouldIgnoreMethod(String methodName) {
        if (methodName == null || methodName.trim().isEmpty()) {
            return false;
        }
        
        for (String pattern : ignoreMethodPatterns) {
            if (matchesPattern(methodName, pattern)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 检查字符串是否匹配模式
     * 
     * @param str     字符串
     * @param pattern 模式（支持*通配符）
     * @return 如果匹配返回true，否则返回false
     */
    private boolean matchesPattern(String str, String pattern) {
        if (pattern.equals("*")) {
            return true;
        }
        
        if (pattern.endsWith("*")) {
            String prefix = pattern.substring(0, pattern.length() - 1);
            return str.startsWith(prefix);
        }
        
        if (pattern.startsWith("*")) {
            String suffix = pattern.substring(1);
            return str.endsWith(suffix);
        }
        
        return str.equals(pattern);
    }
    
    /**
     * 获取配置摘要
     *
     * @return 配置摘要字符串
     */
    public String getConfigSummary() {
        StringBuilder sb = new StringBuilder();
        sb.append("DataAuthDatalayerProperties{");
        sb.append("enabled=").append(enabled);
        
        if (enabled) {
            sb.append(", aopEnabled=").append(aopEnabled);
            sb.append(", strictMode=").append(strictMode);
            sb.append(", defaultFailureStrategy='").append(defaultFailureStrategy).append("'");
            sb.append(", cacheEnabled=").append(cacheEnabled);
            
            if (cacheEnabled) {
                sb.append(", cache={type='").append(cache.type).append("'");
                sb.append(", expireSeconds=").append(cache.expireSeconds);
                sb.append(", maxSize=").append(cache.maxSize);
                sb.append("}");
            }
            
            sb.append(", superAdmin={enabled=").append(superAdmin.enabled);
            sb.append(", roles=").append(superAdmin.roles.size());
            sb.append(", userIds=").append(superAdmin.userIds.size());
            sb.append("}");
            
            sb.append(", provider={dept=").append(provider.dept.enabled);
            sb.append(", user=").append(provider.user.enabled);
            sb.append(", role=").append(provider.role.enabled);
            sb.append("}");
            
            sb.append(", ignorePackages=").append(ignorePackages.size());
            sb.append(", ignoreClasses=").append(ignoreClasses.size());
            sb.append(", ignoreMethodPatterns=").append(ignoreMethodPatterns.size());
        }
        
        sb.append("}");
        return sb.toString();
    }
}
