package cn.com.handthing.starter.tenant.service;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;

/**
 * 租户配置统计信息
 * <p>
 * 封装租户配置的统计数据，包括配置数量、类型分布、更新时间等信息。
 * 用于监控和管理租户配置的使用情况。
 * </p>
 *
 * <AUTHOR>
 * @since V3.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TenantConfigStats {

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 总配置数量
     */
    @Builder.Default
    private long totalCount = 0;

    /**
     * 启用配置数量
     */
    @Builder.Default
    private long enabledCount = 0;

    /**
     * 禁用配置数量
     */
    @Builder.Default
    private long disabledCount = 0;

    /**
     * 敏感配置数量
     */
    @Builder.Default
    private long sensitiveCount = 0;

    /**
     * 字符串类型配置数量
     */
    @Builder.Default
    private long stringCount = 0;

    /**
     * 整数类型配置数量
     */
    @Builder.Default
    private long integerCount = 0;

    /**
     * 布尔类型配置数量
     */
    @Builder.Default
    private long booleanCount = 0;

    /**
     * JSON类型配置数量
     */
    @Builder.Default
    private long jsonCount = 0;

    /**
     * 密码类型配置数量
     */
    @Builder.Default
    private long passwordCount = 0;

    /**
     * 其他类型配置数量
     */
    @Builder.Default
    private long otherCount = 0;

    /**
     * 最后更新时间
     */
    private LocalDateTime lastUpdatedTime;

    /**
     * 统计时间
     */
    @Builder.Default
    private LocalDateTime statsTime = LocalDateTime.now();

    /**
     * 构造函数（基本统计）
     *
     * @param tenantId      租户ID
     * @param totalCount    总配置数量
     * @param enabledCount  启用配置数量
     * @param disabledCount 禁用配置数量
     * @param sensitiveCount 敏感配置数量
     */
    public TenantConfigStats(String tenantId, long totalCount, long enabledCount, 
                           long disabledCount, long sensitiveCount) {
        this.tenantId = tenantId;
        this.totalCount = totalCount;
        this.enabledCount = enabledCount;
        this.disabledCount = disabledCount;
        this.sensitiveCount = sensitiveCount;
        this.statsTime = LocalDateTime.now();
    }

    /**
     * 获取启用配置比例
     *
     * @return 启用配置比例（0-1之间）
     */
    public double getEnabledRatio() {
        return totalCount > 0 ? (double) enabledCount / totalCount : 0.0;
    }

    /**
     * 获取禁用配置比例
     *
     * @return 禁用配置比例（0-1之间）
     */
    public double getDisabledRatio() {
        return totalCount > 0 ? (double) disabledCount / totalCount : 0.0;
    }

    /**
     * 获取敏感配置比例
     *
     * @return 敏感配置比例（0-1之间）
     */
    public double getSensitiveRatio() {
        return totalCount > 0 ? (double) sensitiveCount / totalCount : 0.0;
    }

    /**
     * 检查是否有配置
     *
     * @return 如果有配置返回true，否则返回false
     */
    public boolean hasConfigs() {
        return totalCount > 0;
    }

    /**
     * 检查是否有启用的配置
     *
     * @return 如果有启用的配置返回true，否则返回false
     */
    public boolean hasEnabledConfigs() {
        return enabledCount > 0;
    }

    /**
     * 检查是否有敏感配置
     *
     * @return 如果有敏感配置返回true，否则返回false
     */
    public boolean hasSensitiveConfigs() {
        return sensitiveCount > 0;
    }

    /**
     * 增加配置数量
     *
     * @param count 增加的数量
     */
    public void addTotalCount(long count) {
        this.totalCount += count;
    }

    /**
     * 增加启用配置数量
     *
     * @param count 增加的数量
     */
    public void addEnabledCount(long count) {
        this.enabledCount += count;
    }

    /**
     * 增加禁用配置数量
     *
     * @param count 增加的数量
     */
    public void addDisabledCount(long count) {
        this.disabledCount += count;
    }

    /**
     * 增加敏感配置数量
     *
     * @param count 增加的数量
     */
    public void addSensitiveCount(long count) {
        this.sensitiveCount += count;
    }

    /**
     * 增加指定类型的配置数量
     *
     * @param typeName 类型名称
     * @param count    增加的数量
     */
    public void addTypeCount(String typeName, long count) {
        if (typeName == null) {
            return;
        }

        switch (typeName.toUpperCase()) {
            case "STRING":
                this.stringCount += count;
                break;
            case "INTEGER":
            case "LONG":
                this.integerCount += count;
                break;
            case "BOOLEAN":
                this.booleanCount += count;
                break;
            case "JSON":
            case "JSON_ARRAY":
                this.jsonCount += count;
                break;
            case "PASSWORD":
                this.passwordCount += count;
                break;
            default:
                this.otherCount += count;
                break;
        }
    }

    /**
     * 重置统计数据
     */
    public void reset() {
        this.totalCount = 0;
        this.enabledCount = 0;
        this.disabledCount = 0;
        this.sensitiveCount = 0;
        this.stringCount = 0;
        this.integerCount = 0;
        this.booleanCount = 0;
        this.jsonCount = 0;
        this.passwordCount = 0;
        this.otherCount = 0;
        this.lastUpdatedTime = null;
        this.statsTime = LocalDateTime.now();
    }

    /**
     * 合并其他统计数据
     *
     * @param other 其他统计数据
     */
    public void merge(TenantConfigStats other) {
        if (other == null) {
            return;
        }

        this.totalCount += other.totalCount;
        this.enabledCount += other.enabledCount;
        this.disabledCount += other.disabledCount;
        this.sensitiveCount += other.sensitiveCount;
        this.stringCount += other.stringCount;
        this.integerCount += other.integerCount;
        this.booleanCount += other.booleanCount;
        this.jsonCount += other.jsonCount;
        this.passwordCount += other.passwordCount;
        this.otherCount += other.otherCount;

        // 更新最后更新时间为较新的时间
        if (this.lastUpdatedTime == null || 
            (other.lastUpdatedTime != null && other.lastUpdatedTime.isAfter(this.lastUpdatedTime))) {
            this.lastUpdatedTime = other.lastUpdatedTime;
        }

        this.statsTime = LocalDateTime.now();
    }

    /**
     * 创建副本
     *
     * @return 统计数据副本
     */
    public TenantConfigStats copy() {
        return TenantConfigStats.builder()
                .tenantId(this.tenantId)
                .tenantName(this.tenantName)
                .totalCount(this.totalCount)
                .enabledCount(this.enabledCount)
                .disabledCount(this.disabledCount)
                .sensitiveCount(this.sensitiveCount)
                .stringCount(this.stringCount)
                .integerCount(this.integerCount)
                .booleanCount(this.booleanCount)
                .jsonCount(this.jsonCount)
                .passwordCount(this.passwordCount)
                .otherCount(this.otherCount)
                .lastUpdatedTime(this.lastUpdatedTime)
                .statsTime(this.statsTime)
                .build();
    }

    /**
     * 格式化统计信息
     *
     * @return 格式化的统计信息字符串
     */
    public String format() {
        StringBuilder sb = new StringBuilder();
        sb.append("TenantConfigStats{");
        sb.append("tenant='").append(tenantId).append("'");
        sb.append(", total=").append(totalCount);
        sb.append(", enabled=").append(enabledCount);
        sb.append(", disabled=").append(disabledCount);
        sb.append(", sensitive=").append(sensitiveCount);
        
        if (totalCount > 0) {
            sb.append(", enabledRatio=").append(String.format("%.1f%%", getEnabledRatio() * 100));
        }
        
        sb.append(", statsTime=").append(statsTime);
        sb.append("}");
        return sb.toString();
    }

    @Override
    public String toString() {
        return format();
    }
}
