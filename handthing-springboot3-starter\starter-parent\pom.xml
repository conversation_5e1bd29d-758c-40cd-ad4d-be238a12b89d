<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <!-- 继承 Spring Boot 的官方 Parent, 获取所有 Spring Boot 的依赖管理和默认配置 -->
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.5.3</version>
        <relativePath/>
    </parent>

    <!-- 定义所有子模块的 GroupId -->
    <groupId>cn.com.handthing.springboot3.starter</groupId>
    <artifactId>starter-parent</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>

    <name>HandThing :: Starter Parent</name>
    <description>所有 HandThing Spring Boot 3 Starter 的父依赖</description>

    <properties>
        <!-- 项目版本属性, 引用根 POM 中定义的 revision -->
        <project.version>${revision}</project.version>
        <revision>1.0.0-SNAPSHOT</revision>
        <!-- Java 版本 -->
        <java.version>17</java.version>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <!-- 插件版本 -->
        <flatten-maven-plugin.version>1.6.0</flatten-maven-plugin.version>
    </properties>

    <!--
      统一管理所有模块会用到的依赖版本。
      子模块在 <dependencies> 中引入时无需指定 <version>。
    -->
    <dependencyManagement>
        <dependencies>
            <!-- spring-cloud 版本管理 -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>2025.0.0</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- 内部模块管理 -->
            <dependency>
                <groupId>cn.com.handthing.springboot3.starter</groupId>
                <artifactId>handthing-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.handthing.springboot3.starter</groupId>
                <artifactId>distributed-log-spring-boot-starter</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.handthing.springboot3.starter</groupId>
                <artifactId>api-doc-spring-boot-starter</artifactId>
                <version>${project.version}</version>
            </dependency>
<!--            <dependency>-->
<!--                <groupId>cn.com.handthing.springboot3.starter</groupId>-->
<!--                <artifactId>message-spring-boot-starter</artifactId>-->
<!--                <version>${project.version}</version>-->
<!--            </dependency>-->

<!--            &lt;!&ndash; 基础集成层 &ndash;&gt;-->
<!--            <dependency>-->
<!--                <groupId>cn.com.handthing.springboot3.starter</groupId>-->
<!--                <artifactId>wechat-spring-boot-starter</artifactId>-->
<!--                <version>${project.version}</version>-->
<!--            </dependency>-->
<!--            <dependency>-->
<!--                <groupId>cn.com.handthing.springboot3.starter</groupId>-->
<!--                <artifactId>wecom-spring-boot-starter</artifactId>-->
<!--                <version>${project.version}</version>-->
<!--            </dependency>-->
<!--            <dependency>-->
<!--                <groupId>cn.com.handthing.springboot3.starter</groupId>-->
<!--                <artifactId>dingtalk-spring-boot-starter</artifactId>-->
<!--                <version>${project.version}</version>-->
<!--            </dependency>-->
<!--            <dependency>-->
<!--                <groupId>cn.com.handthing.springboot3.starter</groupId>-->
<!--                <artifactId>feishu-spring-boot-starter</artifactId>-->
<!--                <version>${project.version}</version>-->
<!--            </dependency>-->

<!--            &lt;!&ndash; 适配层 &ndash;&gt;-->
<!--            <dependency>-->
<!--                <groupId>cn.com.handthing.springboot3.starter</groupId>-->
<!--                <artifactId>wechat-message-spring-boot-starter</artifactId>-->
<!--                <version>${project.version}</version>-->
<!--            </dependency>-->
<!--            <dependency>-->
<!--                <groupId>cn.com.handthing.springboot3.starter</groupId>-->
<!--                <artifactId>wecom-message-spring-boot-starter</artifactId>-->
<!--                <version>${project.version}</version>-->
<!--            </dependency>-->
<!--            <dependency>-->
<!--                <groupId>cn.com.handthing.springboot3.starter</groupId>-->
<!--                <artifactId>dingtalk-message-spring-boot-starter</artifactId>-->
<!--                <version>${project.version}</version>-->
<!--            </dependency>-->
<!--            <dependency>-->
<!--                <groupId>cn.com.handthing.springboot3.starter</groupId>-->
<!--                <artifactId>feishu-message-spring-boot-starter</artifactId>-->
<!--                <version>${project.version}</version>-->
<!--            </dependency>-->

            <!-- 认证相关依赖版本管理 -->
            <dependency>
                <groupId>cn.com.handthing.springboot3.starter</groupId>
                <artifactId>auth-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.handthing.springboot3.starter</groupId>
                <artifactId>auth-spring-boot-starter</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.handthing.springboot3.starter</groupId>
                <artifactId>password-provider-starter</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.handthing.springboot3.starter</groupId>
                <artifactId>sms-provider-starter</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.handthing.springboot3.starter</groupId>
                <artifactId>third-party-provider-starter</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.handthing.springboot3.starter</groupId>
                <artifactId>tenant-auth-starter</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- 缓存相关依赖版本管理 -->
            <dependency>
                <groupId>cn.com.handthing.springboot3.starter</groupId>
                <artifactId>cache-spring-boot-starter</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.handthing.springboot3.starter</groupId>
                <artifactId>memory-cache-spring-boot-starter</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.handthing.springboot3.starter</groupId>
                <artifactId>caffeine-cache-spring-boot-starter</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.handthing.springboot3.starter</groupId>
                <artifactId>redis-cache-spring-boot-starter</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.handthing.springboot3.starter</groupId>
                <artifactId>level-cache-spring-boot-starter</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- 第三方依赖版本管理 -->
            <dependency>
                <groupId>com.github.ben-manes.caffeine</groupId>
                <artifactId>caffeine</artifactId>
                <version>3.1.8</version>
            </dependency>
            <dependency>
                <groupId>io.github.resilience4j</groupId>
                <artifactId>resilience4j-circuitbreaker</artifactId>
                <version>2.2.0</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.retry</groupId>
                <artifactId>spring-retry</artifactId>
                <version>2.0.5</version>
            </dependency>

            <!-- 日志相关依赖版本管理 -->
            <dependency>
                <groupId>net.logstash.logback</groupId>
                <artifactId>logstash-logback-encoder</artifactId>
                <version>7.4</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.janino</groupId>
                <artifactId>janino</artifactId>
                <version>3.1.12</version>
            </dependency>

            <!-- API 文档相关依赖版本管理 -->
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
                <version>2.5.0</version>
            </dependency>

            <!-- 加密相关依赖版本管理 -->
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk18on</artifactId>
                <version>1.78.1</version>
            </dependency>
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcpkix-jdk18on</artifactId>
                <version>1.78.1</version>
            </dependency>

            <dependency>
                <groupId>org.testcontainers</groupId>
                <artifactId>junit-jupiter</artifactId>
                <version>1.20.1</version>
            </dependency>

            <!-- MyBatis-Plus -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>3.5.5</version>
            </dependency>

            <!-- HandThing 内部模块版本管理 -->
            <dependency>
                <groupId>cn.com.handthing.springboot3.starter</groupId>
                <artifactId>id-spring-boot-starter</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.com.handthing.springboot3.starter</groupId>
                <artifactId>datalayer-spring-boot-starter</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.com.handthing.springboot3.starter</groupId>
                <artifactId>tenant-datalayer-spring-boot-starter</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.com.handthing.springboot3.starter</groupId>
                <artifactId>dataauth-datalayer-spring-boot-starter</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- ... 后续在此处添加其他模块和第三方库的版本管理 ... -->
        </dependencies>
    </dependencyManagement>

    <build>
        <!--
          直接在 <plugins> 中定义, 使得所有子模块自动继承并激活该插件。
          无需在子模块中重复配置。
        -->
        <plugins>
            <!--
              flatten-maven-plugin: 生成整洁的、对使用者友好的 pom.xml,
              移除继承关系并解析所有变量。
            -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>${flatten-maven-plugin.version}</version>
                <configuration>
                    <updatePomFile>true</updatePomFile>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                </configuration>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals><goal>flatten</goal></goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals><goal>clean</goal></goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>