package cn.com.handthing.starter.connector.wechat.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 微信访问令牌响应
 * <p>
 * 微信OAuth2.0认证获取访问令牌的响应数据模型。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
public class WeChatAccessTokenResponse {

    /**
     * 访问令牌
     */
    @JsonProperty("access_token")
    private String accessToken;

    /**
     * 访问令牌有效期，单位秒
     */
    @JsonProperty("expires_in")
    private Integer expiresIn;

    /**
     * 刷新令牌
     */
    @JsonProperty("refresh_token")
    private String refreshToken;

    /**
     * 用户唯一标识
     */
    @JsonProperty("openid")
    private String openid;

    /**
     * 用户统一标识（在同一个微信开放平台账号下的应用，同一用户的unionid是唯一的）
     */
    @JsonProperty("unionid")
    private String unionid;

    /**
     * 授权作用域
     */
    @JsonProperty("scope")
    private String scope;

    /**
     * 错误码
     */
    @JsonProperty("errcode")
    private Integer errcode;

    /**
     * 错误信息
     */
    @JsonProperty("errmsg")
    private String errmsg;

    /**
     * 检查响应是否成功
     *
     * @return 如果成功返回true，否则返回false
     */
    public boolean isSuccess() {
        return errcode == null || errcode == 0;
    }

    /**
     * 获取错误描述
     *
     * @return 错误描述
     */
    public String getErrorDescription() {
        if (isSuccess()) {
            return null;
        }
        return String.format("Error %d: %s", errcode, errmsg);
    }
}
