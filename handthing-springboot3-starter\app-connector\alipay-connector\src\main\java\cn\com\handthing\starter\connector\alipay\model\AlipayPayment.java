package cn.com.handthing.starter.connector.alipay.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 支付宝支付模型
 * <p>
 * 支付宝支付相关的数据结构
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class AlipayPayment {

    /**
     * 商户订单号
     */
    @JsonProperty("out_trade_no")
    private String outTradeNo;

    /**
     * 订单总金额
     */
    @JsonProperty("total_amount")
    private BigDecimal totalAmount;

    /**
     * 订单标题
     */
    @JsonProperty("subject")
    private String subject;

    /**
     * 订单描述
     */
    @JsonProperty("body")
    private String body;

    /**
     * 买家支付宝账号
     */
    @JsonProperty("buyer_id")
    private String buyerId;

    /**
     * 卖家支付宝用户ID
     */
    @JsonProperty("seller_id")
    private String sellerId;

    /**
     * 超时时间
     */
    @JsonProperty("timeout_express")
    private String timeoutExpress;

    /**
     * 产品码
     */
    @JsonProperty("product_code")
    private String productCode;

    /**
     * 支付结果
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class PayResult {

        /**
         * 支付宝交易号
         */
        @JsonProperty("trade_no")
        private String tradeNo;

        /**
         * 商户订单号
         */
        @JsonProperty("out_trade_no")
        private String outTradeNo;

        /**
         * 交易状态
         */
        @JsonProperty("trade_status")
        private String tradeStatus;

        /**
         * 交易金额
         */
        @JsonProperty("total_amount")
        private BigDecimal totalAmount;

        /**
         * 实收金额
         */
        @JsonProperty("receipt_amount")
        private BigDecimal receiptAmount;

        /**
         * 买家付款金额
         */
        @JsonProperty("buyer_pay_amount")
        private BigDecimal buyerPayAmount;

        /**
         * 交易创建时间
         */
        @JsonProperty("gmt_create")
        private String gmtCreate;

        /**
         * 交易付款时间
         */
        @JsonProperty("gmt_payment")
        private String gmtPayment;

        /**
         * 检查支付是否成功
         *
         * @return 如果支付成功返回true，否则返回false
         */
        public boolean isSuccess() {
            return "TRADE_SUCCESS".equals(tradeStatus);
        }

        /**
         * 获取交易状态描述
         *
         * @return 交易状态描述
         */
        public String getTradeStatusDescription() {
            if (tradeStatus == null) {
                return "未知";
            }
            switch (tradeStatus) {
                case "WAIT_BUYER_PAY":
                    return "交易创建，等待买家付款";
                case "TRADE_CLOSED":
                    return "未付款交易超时关闭，或支付完成后全额退款";
                case "TRADE_SUCCESS":
                    return "交易支付成功";
                case "TRADE_FINISHED":
                    return "交易结束，不可退款";
                default:
                    return tradeStatus;
            }
        }
    }

    /**
     * 创建结果
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class CreateResult {

        /**
         * 支付宝交易号
         */
        @JsonProperty("trade_no")
        private String tradeNo;

        /**
         * 商户订单号
         */
        @JsonProperty("out_trade_no")
        private String outTradeNo;

        /**
         * 检查创建是否成功
         *
         * @return 如果创建成功返回true，否则返回false
         */
        public boolean isSuccess() {
            return tradeNo != null && !tradeNo.trim().isEmpty();
        }
    }

    /**
     * 预创建结果
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class PrecreateResult {

        /**
         * 商户订单号
         */
        @JsonProperty("out_trade_no")
        private String outTradeNo;

        /**
         * 二维码内容
         */
        @JsonProperty("qr_code")
        private String qrCode;

        /**
         * 检查预创建是否成功
         *
         * @return 如果预创建成功返回true，否则返回false
         */
        public boolean isSuccess() {
            return qrCode != null && !qrCode.trim().isEmpty();
        }
    }

    /**
     * 查询结果
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class QueryResult extends PayResult {
        // 继承PayResult的所有字段
    }

    /**
     * 关闭结果
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class CloseResult {

        /**
         * 支付宝交易号
         */
        @JsonProperty("trade_no")
        private String tradeNo;

        /**
         * 商户订单号
         */
        @JsonProperty("out_trade_no")
        private String outTradeNo;

        /**
         * 检查关闭是否成功
         *
         * @return 如果关闭成功返回true，否则返回false
         */
        public boolean isSuccess() {
            return tradeNo != null && !tradeNo.trim().isEmpty();
        }
    }

    /**
     * 退款请求
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class RefundRequest {

        /**
         * 商户订单号
         */
        @JsonProperty("out_trade_no")
        private String outTradeNo;

        /**
         * 支付宝交易号
         */
        @JsonProperty("trade_no")
        private String tradeNo;

        /**
         * 退款金额
         */
        @JsonProperty("refund_amount")
        private BigDecimal refundAmount;

        /**
         * 退款原因
         */
        @JsonProperty("refund_reason")
        private String refundReason;

        /**
         * 退款请求号
         */
        @JsonProperty("out_request_no")
        private String outRequestNo;
    }

    /**
     * 退款结果
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class RefundResult {

        /**
         * 支付宝交易号
         */
        @JsonProperty("trade_no")
        private String tradeNo;

        /**
         * 商户订单号
         */
        @JsonProperty("out_trade_no")
        private String outTradeNo;

        /**
         * 退款金额
         */
        @JsonProperty("refund_fee")
        private BigDecimal refundFee;

        /**
         * 退款时间
         */
        @JsonProperty("gmt_refund_pay")
        private String gmtRefundPay;

        /**
         * 检查退款是否成功
         *
         * @return 如果退款成功返回true，否则返回false
         */
        public boolean isSuccess() {
            return refundFee != null && refundFee.compareTo(BigDecimal.ZERO) > 0;
        }
    }

    /**
     * 退款查询结果
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class RefundQueryResult extends RefundResult {
        // 继承RefundResult的所有字段
    }
}
