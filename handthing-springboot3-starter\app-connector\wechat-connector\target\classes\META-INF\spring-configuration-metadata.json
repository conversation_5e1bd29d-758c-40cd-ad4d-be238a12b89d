{"groups": [{"name": "handthing.connector.wechat", "type": "cn.com.handthing.starter.connector.wechat.config.WeChatConfig", "sourceType": "cn.com.handthing.starter.connector.wechat.config.WeChatConfig"}], "properties": [{"name": "handthing.connector.wechat.account-type", "type": "java.lang.String", "description": "微信公众号类型 subscription: 订阅号 service: 服务号 enterprise: 企业号 miniprogram: 小程序", "sourceType": "cn.com.handthing.starter.connector.wechat.config.WeChatConfig"}, {"name": "handthing.connector.wechat.api-base-url", "type": "java.lang.String", "description": "微信API基础URL", "sourceType": "cn.com.handthing.starter.connector.wechat.config.WeChatConfig"}, {"name": "handthing.connector.wechat.app-id", "type": "java.lang.String", "description": "微信应用ID (AppID)", "sourceType": "cn.com.handthing.starter.connector.wechat.config.WeChatConfig"}, {"name": "handthing.connector.wechat.app-secret", "type": "java.lang.String", "description": "微信应用密钥 (AppSecret)", "sourceType": "cn.com.handthing.starter.connector.wechat.config.WeChatConfig"}, {"name": "handthing.connector.wechat.auth-url", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.wechat.config.WeChatConfig"}, {"name": "handthing.connector.wechat.client-id", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.wechat.config.WeChatConfig"}, {"name": "handthing.connector.wechat.client-secret", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.wechat.config.WeChatConfig"}, {"name": "handthing.connector.wechat.connect-timeout", "type": "java.lang.Integer", "sourceType": "cn.com.handthing.starter.connector.wechat.config.WeChatConfig"}, {"name": "handthing.connector.wechat.enabled", "type": "java.lang.Bo<PERSON>an", "sourceType": "cn.com.handthing.starter.connector.wechat.config.WeChatConfig"}, {"name": "handthing.connector.wechat.encoding-a-e-s-key", "type": "java.lang.String", "description": "消息加密密钥 (EncodingAESKey)", "sourceType": "cn.com.handthing.starter.connector.wechat.config.WeChatConfig"}, {"name": "handthing.connector.wechat.encrypt-message", "type": "java.lang.Bo<PERSON>an", "description": "是否启用消息加密", "sourceType": "cn.com.handthing.starter.connector.wechat.config.WeChatConfig"}, {"name": "handthing.connector.wechat.extra-config", "type": "java.util.Map<java.lang.String,java.lang.Object>", "sourceType": "cn.com.handthing.starter.connector.wechat.config.WeChatConfig"}, {"name": "handthing.connector.wechat.max-retries", "type": "java.lang.Integer", "sourceType": "cn.com.handthing.starter.connector.wechat.config.WeChatConfig"}, {"name": "handthing.connector.wechat.mini-program-mode", "type": "java.lang.Bo<PERSON>an", "description": "是否为小程序模式", "sourceType": "cn.com.handthing.starter.connector.wechat.config.WeChatConfig"}, {"name": "handthing.connector.wechat.original-id", "type": "java.lang.String", "description": "微信公众号原始ID", "sourceType": "cn.com.handthing.starter.connector.wechat.config.WeChatConfig"}, {"name": "handthing.connector.wechat.read-timeout", "type": "java.lang.Integer", "sourceType": "cn.com.handthing.starter.connector.wechat.config.WeChatConfig"}, {"name": "handthing.connector.wechat.redirect-uri", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.wechat.config.WeChatConfig"}, {"name": "handthing.connector.wechat.sandbox", "type": "java.lang.Bo<PERSON>an", "sourceType": "cn.com.handthing.starter.connector.wechat.config.WeChatConfig"}, {"name": "handthing.connector.wechat.sandbox-api-base-url", "type": "java.lang.String", "description": "沙箱环境API基础URL", "sourceType": "cn.com.handthing.starter.connector.wechat.config.WeChatConfig"}, {"name": "handthing.connector.wechat.sandbox-mode", "type": "java.lang.Bo<PERSON>an", "description": "是否使用沙箱环境", "sourceType": "cn.com.handthing.starter.connector.wechat.config.WeChatConfig"}, {"name": "handthing.connector.wechat.scope", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.wechat.config.WeChatConfig"}, {"name": "handthing.connector.wechat.token", "type": "java.lang.String", "description": "消息校验Token", "sourceType": "cn.com.handthing.starter.connector.wechat.config.WeChatConfig"}, {"name": "handthing.connector.wechat.token-url", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.wechat.config.WeChatConfig"}, {"name": "handthing.connector.wechat.user-info-url", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.connector.wechat.config.WeChatConfig"}], "hints": [], "ignored": {"properties": []}}