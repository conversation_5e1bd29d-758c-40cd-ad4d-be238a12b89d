package cn.com.handthing.starter.cache.caffeine;

import cn.com.handthing.starter.cache.CacheStats;
import cn.com.handthing.starter.cache.caffeine.config.CaffeineCacheProperties;
import cn.com.handthing.starter.cache.exception.CacheOperationException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.Duration;
import java.util.concurrent.Callable;

import static org.junit.jupiter.api.Assertions.*;

/**
 * CaffeineCacheService 单元测试
 *
 * <AUTHOR>
 * @since V1.0.0
 */
class CaffeineCacheServiceTest {

    private CaffeineCacheService cacheService;
    private CaffeineCacheManager cacheManager;

    @BeforeEach
    void setUp() {
        CaffeineCacheProperties properties = new CaffeineCacheProperties();
        properties.setDefaultSpec("initialCapacity=10,maximumSize=100,expireAfterWrite=10m");
        properties.setEnableStats(true);
        
        cacheManager = new CaffeineCacheManager(properties);
        cacheService = new CaffeineCacheService(cacheManager);
    }

    @Test
    void testPutAndGet() {
        String cacheName = "testCache";
        String key = "testKey";
        String value = "testValue";

        cacheService.put(cacheName, key, value);
        String result = cacheService.get(cacheName, key);

        assertEquals(value, result);
    }

    @Test
    void testGetWithType() {
        String cacheName = "testCache";
        String key = "testKey";
        String value = "testValue";

        cacheService.put(cacheName, key, value);
        String result = cacheService.get(cacheName, key, String.class);

        assertEquals(value, result);
    }

    @Test
    void testGetWithCallable() {
        String cacheName = "testCache";
        String key = "testKey";
        String value = "testValue";

        Callable<String> valueLoader = () -> value;
        String result = cacheService.get(cacheName, key, valueLoader);

        assertEquals(value, result);
        // 验证缓存中已存在
        assertEquals(value, cacheService.get(cacheName, key));
    }

    @Test
    void testPutWithTtl() {
        String cacheName = "testCache";
        String key = "testKey";
        String value = "testValue";
        Duration ttl = Duration.ofMinutes(5);

        // Caffeine不支持单个条目TTL，但方法应该正常工作
        assertDoesNotThrow(() -> {
            cacheService.put(cacheName, key, value, ttl);
        });

        assertEquals(value, cacheService.get(cacheName, key));
    }

    @Test
    void testPutIfAbsent() {
        String cacheName = "testCache";
        String key = "testKey";
        String value1 = "testValue1";
        String value2 = "testValue2";

        // 第一次放入
        String result1 = cacheService.putIfAbsent(cacheName, key, value1);
        assertNull(result1);
        assertEquals(value1, cacheService.get(cacheName, key));

        // 第二次放入，应该返回已存在的值
        String result2 = cacheService.putIfAbsent(cacheName, key, value2);
        assertEquals(value1, result2);
        assertEquals(value1, cacheService.get(cacheName, key));
    }

    @Test
    void testEvict() {
        String cacheName = "testCache";
        String key = "testKey";
        String value = "testValue";

        cacheService.put(cacheName, key, value);
        assertNotNull(cacheService.get(cacheName, key));

        cacheService.evict(cacheName, key);
        assertNull(cacheService.get(cacheName, key));
    }

    @Test
    void testClear() {
        String cacheName = "testCache";
        
        cacheService.put(cacheName, "key1", "value1");
        cacheService.put(cacheName, "key2", "value2");

        assertNotNull(cacheService.get(cacheName, "key1"));
        assertNotNull(cacheService.get(cacheName, "key2"));

        cacheService.clear(cacheName);

        assertNull(cacheService.get(cacheName, "key1"));
        assertNull(cacheService.get(cacheName, "key2"));
    }

    @Test
    void testExists() {
        String cacheName = "testCache";
        String key = "testKey";
        String value = "testValue";

        assertFalse(cacheService.exists(cacheName, key));

        cacheService.put(cacheName, key, value);
        assertTrue(cacheService.exists(cacheName, key));

        cacheService.evict(cacheName, key);
        assertFalse(cacheService.exists(cacheName, key));
    }

    @Test
    void testGetStats() {
        String cacheName = "testCache";
        String key = "testKey";
        String value = "testValue";

        // 触发一些缓存操作
        cacheService.get(cacheName, key); // miss
        cacheService.put(cacheName, key, value);
        cacheService.get(cacheName, key); // hit

        CacheStats stats = cacheService.getStats(cacheName);
        assertNotNull(stats);
        assertTrue(stats.getHitCount() >= 0);
        assertTrue(stats.getMissCount() >= 0);
        assertTrue(stats.getRequestCount() >= 0);
    }

    @Test
    void testEstimatedSize() {
        String cacheName = "testCache";

        assertEquals(0, cacheService.estimatedSize(cacheName));

        cacheService.put(cacheName, "key1", "value1");
        cacheService.put(cacheName, "key2", "value2");

        assertTrue(cacheService.estimatedSize(cacheName) >= 2);
    }

    @Test
    void testCleanUp() {
        String cacheName = "testCache";
        
        cacheService.put(cacheName, "key1", "value1");
        
        // cleanup操作应该正常执行
        assertDoesNotThrow(() -> {
            cacheService.cleanUp(cacheName);
        });
        
        // 数据应该仍然存在
        assertEquals("value1", cacheService.get(cacheName, "key1"));
    }

    @Test
    void testGetNonExistentCache() {
        // 获取不存在的缓存应该自动创建
        assertDoesNotThrow(() -> {
            cacheService.get("nonExistentCache", "key");
        });
    }
}