package cn.com.handthing.starter.auth;

import cn.com.handthing.starter.auth.core.UserContext;
import cn.com.handthing.starter.auth.core.UserContextHolder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.servlet.HandlerInterceptor;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 认证拦截器
 * <p>
 * 在Spring MVC层面进行认证检查和用户上下文管理。
 * 配合认证过滤器使用，提供更细粒度的认证控制。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@RequiredArgsConstructor
public class AuthenticationInterceptor implements HandlerInterceptor {

    private final AuthProperties authProperties;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String requestPath = request.getRequestURI();
        
        // 检查是否需要跳过认证
        if (authProperties.isPathExcluded(requestPath)) {
            log.debug("Skipping authentication check for excluded path: {}", requestPath);
            return true;
        }

        // 检查用户上下文
        UserContext userContext = UserContextHolder.getContext();
        if (userContext == null || !userContext.isAuthenticated()) {
            log.debug("User not authenticated for path: {}", requestPath);
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            response.setContentType("application/json;charset=UTF-8");
            response.getWriter().write("{\"success\":false,\"error\":\"UNAUTHORIZED\",\"error_description\":\"User not authenticated\"}");
            return false;
        }

        // 检查令牌是否过期
        if (userContext.isExpired()) {
            log.debug("User token expired for path: {}", requestPath);
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            response.setContentType("application/json;charset=UTF-8");
            response.getWriter().write("{\"success\":false,\"error\":\"TOKEN_EXPIRED\",\"error_description\":\"Authentication token has expired\"}");
            return false;
        }

        log.debug("Authentication check passed for user: {} on path: {}", 
                userContext.getUsername(), requestPath);
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        // 清理用户上下文（如果需要）
        // UserContextHolder.clearContext();
    }
}
