package cn.com.handthing.starter.httpclient.config;

import java.util.Objects;

/**
 * 仅适用于 RestTemplate 实现的特定配置。
 * 当且仅当使用 handthing-resttemplate-http-client-starter 时，这些配置才会生效。
 */
public class RestTemplateSpecificProperties {

    /**
     * 选择 RestTemplate 底层的 ClientHttpRequestFactory 实现。
     */
    private FactoryType factoryType = FactoryType.DEFAULT;

    /**
     * 连接池最大总连接数。
     * 仅在 factory-type 为 HTTP_COMPONENTS 时生效。
     */
    private int maxConnTotal = 200;

    /**
     * 每个路由（即每个主机:端口）的最大连接数。
     * 仅在 factory-type 为 HTTP_COMPONENTS 时生效。
     */
    private int maxConnPerRoute = 50;


    public enum FactoryType {
        /**
         * 使用 Spring Boot 默认的 ClientHttpRequestFactory。
         */
        DEFAULT,
        /**
         * 使用 Apache HttpClient5 作为底层实现，支持更高级的连接池管理。
         * 需要用户在项目中手动添加 org.apache.httpcomponents.client5:httpclient5 依赖。
         */
        HTTP_COMPONENTS
    }

    // --- Standard Getters and Setters, equals, hashCode, toString ---

    public RestTemplateSpecificProperties() {
    }

    public FactoryType getFactoryType() {
        return factoryType;
    }

    public void setFactoryType(FactoryType factoryType) {
        this.factoryType = factoryType;
    }

    public int getMaxConnTotal() {
        return maxConnTotal;
    }

    public void setMaxConnTotal(int maxConnTotal) {
        this.maxConnTotal = maxConnTotal;
    }

    public int getMaxConnPerRoute() {
        return maxConnPerRoute;
    }

    public void setMaxConnPerRoute(int maxConnPerRoute) {
        this.maxConnPerRoute = maxConnPerRoute;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        RestTemplateSpecificProperties that = (RestTemplateSpecificProperties) o;
        return maxConnTotal == that.maxConnTotal && maxConnPerRoute == that.maxConnPerRoute && factoryType == that.factoryType;
    }

    @Override
    public int hashCode() {
        return Objects.hash(factoryType, maxConnTotal, maxConnPerRoute);
    }

    @Override
    public String toString() {
        return "RestTemplateSpecificProperties{" +
                "factoryType=" + factoryType +
                ", maxConnTotal=" + maxConnTotal +
                ", maxConnPerRoute=" + maxConnPerRoute +
                '}';
    }
}