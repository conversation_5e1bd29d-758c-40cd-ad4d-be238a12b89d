package cn.com.handthing.core.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Jackson JSON 工具类 (最终版)
 *
 * <p>特性:
 * <ul>
 * <li>线程安全，可全局共享 ObjectMapper 实例。</li>
 * <li><b>解决数值精度问题</b>: 自动将 Long, Double, Float, BigDecimal 类型序列化为字符串。</li>
 * <li><b>统一时间格式</b>:
 * <ul>
 * <li>{@code LocalDateTime}, {@code java.util.Date} -> "yyyy-MM-dd HH:mm:ss"</li>
 * <li>{@code LocalDate} -> "yyyy-MM-dd"</li>
 * </ul>
 * </li>
 * <li><b>健壮的反序列化</b>: 忽略 JSON 中多余的未知字段，避免因接口变更导致反序列化失败。</li>
 * <li><b>支持复杂泛型</b>: 提供 TypeReference 用于反序列化 List, Map 等复杂泛型类型。</li>
 * <li><b>集合对象支持</b>: 提供专门的 List、Set、Map 序列化和反序列化方法。</li>
 * <li><b>集合转换工具</b>: 提供集合类型之间的转换方法。</li>
 * </ul>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
public final class JsonUtils {

    private static final Logger log = LoggerFactory.getLogger(JsonUtils.class);

    private JsonUtils() {}

    private static class ObjectMapperHolder {
        private static final ObjectMapper INSTANCE = new ObjectMapper();
        
        private static final String DATETIME_PATTERN = "yyyy-MM-dd HH:mm:ss";
        private static final String DATE_PATTERN = "yyyy-MM-dd";
        
        static {
            // 1. 配置 Java 8 时间模块
            JavaTimeModule javaTimeModule = new JavaTimeModule();
            // 配置序列化和反序列化格式
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DATETIME_PATTERN);
            javaTimeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(dateTimeFormatter));
            javaTimeModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(dateTimeFormatter));
            
            DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern(DATE_PATTERN);
            javaTimeModule.addSerializer(LocalDate.class, new LocalDateSerializer(dateFormatter));
            javaTimeModule.addDeserializer(LocalDate.class, new LocalDateDeserializer(dateFormatter));
            INSTANCE.registerModule(javaTimeModule);
            
            // 2. 配置 java.util.Date 的序列化格式
            SimpleDateFormat dateFormat = new SimpleDateFormat(DATETIME_PATTERN);
            dateFormat.setTimeZone(TimeZone.getDefault()); // 建议设置时区
            INSTANCE.setDateFormat(dateFormat);

            // 3. 解决数值类型在前端精度丢失的问题，将指定类型序列化为字符串
            SimpleModule simpleModule = new SimpleModule();
            simpleModule.addSerializer(Long.class, ToStringSerializer.instance);
            simpleModule.addSerializer(Long.TYPE, ToStringSerializer.instance);
            simpleModule.addSerializer(Double.class, ToStringSerializer.instance);
            simpleModule.addSerializer(Double.TYPE, ToStringSerializer.instance);
            simpleModule.addSerializer(Float.class, ToStringSerializer.instance);
            simpleModule.addSerializer(Float.TYPE, ToStringSerializer.instance);
            simpleModule.addSerializer(BigDecimal.class, ToStringSerializer.instance);
            INSTANCE.registerModule(simpleModule);

            // 4. 反序列化时，忽略在 JSON 中存在但 Java 对象中不存在的属性，增强兼容性
            INSTANCE.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        }
    }

    /**
     * 获取一个预配置的、线程安全的 Jackson ObjectMapper 实例。
     * @return ObjectMapper 单例
     */
    public static ObjectMapper getMapper() {
        return ObjectMapperHolder.INSTANCE;
    }
    
    /**
     * 将对象序列化为 JSON 字符串。
     * 如果发生异常，记录错误并返回 null。
     * @param obj 待序列化的对象
     * @return JSON 字符串或 null
     */
    public static String toJson(Object obj) {
        if (obj == null) {
            return null;
        }
        try {
            return getMapper().writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            log.error("Failed to serialize object to JSON string. Object: {}", obj, e);
            return null;
        }
    }
    
    /**
     * 将对象序列化为格式化（美化）的 JSON 字符串。
     * @param obj 待序列化的对象
     * @return 格式化的 JSON 字符串或 null
     */
    public static String toPrettyJson(Object obj) {
        if (obj == null) {
            return null;
        }
        try {
            return getMapper().writerWithDefaultPrettyPrinter().writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            log.error("Failed to serialize object to pretty JSON string. Object: {}", obj, e);
            return null;
        }
    }

    /**
     * 将 JSON 字符串反序列化为简单 Java 对象。
     * <p>支持将 "null" 字符串或 null 变量安全地转换成 null 对象。
     * @param json JSON 字符串
     * @param clazz 目标对象的 Class
     * @return 目标对象实例或 null
     */
    public static <T> T fromJson(String json, Class<T> clazz) {
        if (StringUtils.isBlank(json) || clazz == null) {
            return null;
        }
        // Jackson 默认能将 "null" 字符串正确反序列化为 null
        try {
            return getMapper().readValue(json, clazz);
        } catch (IOException e) {
            log.error("Failed to deserialize JSON string to object. JSON: {}, Class: {}", json, clazz.getName(), e);
            return null;
        }
    }

    /**
     * 将 JSON 字符串反序列化为复杂的泛型对象，如 List<User> 或 Map<String, User>。
     * <p>使用示例: {@code List<User> users = JsonUtils.fromJson(json, new TypeReference<>() {});}
     * @param json JSON 字符串
     * @param typeRef 包含泛型信息的 TypeReference
     * @return 目标对象实例或 null
     */
    public static <T> T fromJson(String json, TypeReference<T> typeRef) {
        if (StringUtils.isBlank(json) || typeRef == null) {
            return null;
        }
        try {
            return getMapper().readValue(json, typeRef);
        } catch (IOException e) {
            log.error("Failed to deserialize JSON string to generic object. JSON: {}, Type: {}", json, typeRef.getType(), e);
            return null;
        }
    }

    // ==================== 集合对象序列化和反序列化方法 ====================

    /**
     * 将 List 对象序列化为 JSON 字符串。
     * @param list 待序列化的 List 对象
     * @return JSON 字符串或 null
     */
    public static String toJsonList(List<?> list) {
        return toJson(list);
    }

    /**
     * 将 JSON 字符串反序列化为 List 对象。
     * <p>使用示例: {@code List<String> list = JsonUtils.fromJsonToList(json, String.class);}
     * @param json JSON 字符串
     * @param elementClass List 元素的 Class 类型
     * @return List 对象或 null
     */
    public static <T> List<T> fromJsonToList(String json, Class<T> elementClass) {
        if (StringUtils.isBlank(json) || elementClass == null) {
            return null;
        }
        try {
            return getMapper().readValue(json,
                getMapper().getTypeFactory().constructCollectionType(List.class, elementClass));
        } catch (IOException e) {
            log.error("Failed to deserialize JSON string to List. JSON: {}, ElementClass: {}", json, elementClass.getName(), e);
            return null;
        }
    }

    /**
     * 将 JSON 字符串反序列化为 List 对象（使用 TypeReference）。
     * <p>使用示例: {@code List<User> users = JsonUtils.fromJsonToList(json, new TypeReference<List<User>>() {});}
     * @param json JSON 字符串
     * @param typeRef 包含泛型信息的 TypeReference
     * @return List 对象或 null
     */
    public static <T> List<T> fromJsonToList(String json, TypeReference<List<T>> typeRef) {
        return fromJson(json, typeRef);
    }

    /**
     * 将 Set 对象序列化为 JSON 字符串。
     * @param set 待序列化的 Set 对象
     * @return JSON 字符串或 null
     */
    public static String toJsonSet(Set<?> set) {
        return toJson(set);
    }

    /**
     * 将 JSON 字符串反序列化为 Set 对象。
     * <p>使用示例: {@code Set<String> set = JsonUtils.fromJsonToSet(json, String.class);}
     * @param json JSON 字符串
     * @param elementClass Set 元素的 Class 类型
     * @return Set 对象或 null
     */
    public static <T> Set<T> fromJsonToSet(String json, Class<T> elementClass) {
        if (StringUtils.isBlank(json) || elementClass == null) {
            return null;
        }
        try {
            return getMapper().readValue(json,
                getMapper().getTypeFactory().constructCollectionType(Set.class, elementClass));
        } catch (IOException e) {
            log.error("Failed to deserialize JSON string to Set. JSON: {}, ElementClass: {}", json, elementClass.getName(), e);
            return null;
        }
    }

    /**
     * 将 JSON 字符串反序列化为 Set 对象（使用 TypeReference）。
     * <p>使用示例: {@code Set<User> users = JsonUtils.fromJsonToSet(json, new TypeReference<Set<User>>() {});}
     * @param json JSON 字符串
     * @param typeRef 包含泛型信息的 TypeReference
     * @return Set 对象或 null
     */
    public static <T> Set<T> fromJsonToSet(String json, TypeReference<Set<T>> typeRef) {
        return fromJson(json, typeRef);
    }

    /**
     * 将 Map 对象序列化为 JSON 字符串。
     * @param map 待序列化的 Map 对象
     * @return JSON 字符串或 null
     */
    public static String toJsonMap(Map<?, ?> map) {
        return toJson(map);
    }

    /**
     * 将 JSON 字符串反序列化为 Map 对象。
     * <p>使用示例: {@code Map<String, Object> map = JsonUtils.fromJsonToMap(json, String.class, Object.class);}
     * @param json JSON 字符串
     * @param keyClass Map 键的 Class 类型
     * @param valueClass Map 值的 Class 类型
     * @return Map 对象或 null
     */
    public static <K, V> Map<K, V> fromJsonToMap(String json, Class<K> keyClass, Class<V> valueClass) {
        if (StringUtils.isBlank(json) || keyClass == null || valueClass == null) {
            return null;
        }
        try {
            return getMapper().readValue(json,
                getMapper().getTypeFactory().constructMapType(Map.class, keyClass, valueClass));
        } catch (IOException e) {
            log.error("Failed to deserialize JSON string to Map. JSON: {}, KeyClass: {}, ValueClass: {}",
                json, keyClass.getName(), valueClass.getName(), e);
            return null;
        }
    }

    /**
     * 将 JSON 字符串反序列化为 Map 对象（使用 TypeReference）。
     * <p>使用示例: {@code Map<String, User> map = JsonUtils.fromJsonToMap(json, new TypeReference<Map<String, User>>() {});}
     * @param json JSON 字符串
     * @param typeRef 包含泛型信息的 TypeReference
     * @return Map 对象或 null
     */
    public static <K, V> Map<K, V> fromJsonToMap(String json, TypeReference<Map<K, V>> typeRef) {
        return fromJson(json, typeRef);
    }

    // ==================== 集合转换工具方法 ====================

    /**
     * 将 List 转换为 Set，去除重复元素。
     * @param list 源 List
     * @return 转换后的 Set 或 null
     */
    public static <T> Set<T> listToSet(List<T> list) {
        if (list == null) {
            return null;
        }
        return new LinkedHashSet<>(list);
    }

    /**
     * 将 Set 转换为 List。
     * @param set 源 Set
     * @return 转换后的 List 或 null
     */
    public static <T> List<T> setToList(Set<T> set) {
        if (set == null) {
            return null;
        }
        return new ArrayList<>(set);
    }

    /**
     * 将数组转换为 List。
     * @param array 源数组
     * @return 转换后的 List 或 null
     */
    public static <T> List<T> arrayToList(T[] array) {
        if (array == null) {
            return null;
        }
        return Arrays.asList(array);
    }

    /**
     * 将 List 转换为数组。
     * @param list 源 List
     * @param arrayClass 数组元素的 Class 类型
     * @return 转换后的数组或 null
     */
    @SuppressWarnings("unchecked")
    public static <T> T[] listToArray(List<T> list, Class<T> arrayClass) {
        if (list == null || arrayClass == null) {
            return null;
        }
        return list.toArray((T[]) java.lang.reflect.Array.newInstance(arrayClass, list.size()));
    }

    /**
     * 过滤 List 中的 null 元素。
     * @param list 源 List
     * @return 过滤后的 List 或 null
     */
    public static <T> List<T> filterNullFromList(List<T> list) {
        if (list == null) {
            return null;
        }
        return list.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 过滤 Set 中的 null 元素。
     * @param set 源 Set
     * @return 过滤后的 Set 或 null
     */
    public static <T> Set<T> filterNullFromSet(Set<T> set) {
        if (set == null) {
            return null;
        }
        return set.stream().filter(Objects::nonNull).collect(Collectors.toSet());
    }
}