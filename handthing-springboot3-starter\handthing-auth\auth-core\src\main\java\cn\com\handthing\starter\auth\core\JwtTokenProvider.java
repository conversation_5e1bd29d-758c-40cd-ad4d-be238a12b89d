package cn.com.handthing.starter.auth.core;

import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.crypto.SecretKey;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.Map;
import java.util.UUID;

/**
 * JWT令牌提供者
 * <p>
 * 负责JWT令牌的生成、解析和验证。
 * 支持访问令牌和刷新令牌的生成，提供完整的JWT生命周期管理。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class JwtTokenProvider {

    private final JwtConfig jwtConfig;

    /**
     * 生成访问令牌
     *
     * @param claims JWT声明
     * @return 访问令牌
     */
    public String generateAccessToken(JwtClaims claims) {
        return generateToken(claims, jwtConfig.getAccessTokenExpirationSeconds(), "access_token");
    }

    /**
     * 生成刷新令牌
     *
     * @param claims JWT声明
     * @return 刷新令牌
     */
    public String generateRefreshToken(JwtClaims claims) {
        return generateToken(claims, jwtConfig.getRefreshTokenExpirationSeconds(), "refresh_token");
    }

    /**
     * 生成令牌
     *
     * @param claims      JWT声明
     * @param expiresIn   过期时间（秒）
     * @param tokenType   令牌类型
     * @return JWT令牌
     */
    public String generateToken(JwtClaims claims, long expiresIn, String tokenType) {
        try {
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime expiration = now.plusSeconds(expiresIn);

            // 设置基础声明
            claims.setIssuer(jwtConfig.getIssuer());
            claims.setAudience(jwtConfig.getAudience());
            claims.setIssuedAt(now);
            claims.setExpiration(expiration);
            claims.setJwtId(UUID.randomUUID().toString());
            claims.setTokenType(tokenType);

            // 构建JWT
            JwtBuilder builder = Jwts.builder()
                    .setSubject(claims.getSubject())
                    .setIssuer(claims.getIssuer())
                    .setAudience(claims.getAudience())
                    .setIssuedAt(toDate(claims.getIssuedAt()))
                    .setExpiration(toDate(claims.getExpiration()))
                    .setId(claims.getJwtId());

            // 添加自定义声明
            Map<String, Object> claimsMap = claims.toMap();
            for (Map.Entry<String, Object> entry : claimsMap.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();
                
                // 跳过标准声明
                if (!isStandardClaim(key) && value != null) {
                    builder.claim(key, value);
                }
            }

            // 签名
            SecretKey key = getSigningKey();
            return builder.signWith(key, SignatureAlgorithm.HS256).compact();

        } catch (Exception e) {
            log.error("Failed to generate JWT token", e);
            throw new AuthenticationException("TOKEN_GENERATION_FAILED", "Failed to generate JWT token", e);
        }
    }

    /**
     * 解析令牌
     *
     * @param token JWT令牌
     * @return JWT声明
     * @throws AuthenticationException 如果令牌无效
     */
    public JwtClaims parseToken(String token) throws AuthenticationException {
        try {
            // 提取纯令牌
            String pureToken = jwtConfig.extractToken(token);
            if (pureToken == null || pureToken.trim().isEmpty()) {
                throw new AuthenticationException("INVALID_TOKEN", "Token is empty");
            }

            // 解析JWT
            SecretKey key = getSigningKey();
            JwtParserBuilder parserBuilder = Jwts.parserBuilder()
                    .setSigningKey(key)
                    .setAllowedClockSkewSeconds(jwtConfig.getClockSkew());

            if (jwtConfig.isValidateIssuer()) {
                parserBuilder.requireIssuer(jwtConfig.getIssuer());
            }

            if (jwtConfig.isValidateAudience()) {
                parserBuilder.requireAudience(jwtConfig.getAudience());
            }

            Claims claims = parserBuilder.build().parseClaimsJws(pureToken).getBody();

            // 转换为JwtClaims
            return convertToJwtClaims(claims);

        } catch (ExpiredJwtException e) {
            log.debug("JWT token expired: {}", e.getMessage());
            throw new TokenExpiredException("JWT token expired");
        } catch (UnsupportedJwtException e) {
            log.debug("Unsupported JWT token: {}", e.getMessage());
            throw new AuthenticationException("INVALID_TOKEN", "Unsupported JWT token", e);
        } catch (MalformedJwtException e) {
            log.debug("Malformed JWT token: {}", e.getMessage());
            throw new AuthenticationException("INVALID_TOKEN", "Malformed JWT token", e);
        } catch (SecurityException e) {
            log.debug("Invalid JWT signature: {}", e.getMessage());
            throw new AuthenticationException("INVALID_TOKEN", "Invalid JWT signature", e);
        } catch (IllegalArgumentException e) {
            log.debug("Invalid JWT token: {}", e.getMessage());
            throw new AuthenticationException("INVALID_TOKEN", "Invalid JWT token", e);
        } catch (Exception e) {
            log.error("Failed to parse JWT token", e);
            throw new AuthenticationException("TOKEN_PARSE_FAILED", "Failed to parse JWT token", e);
        }
    }

    /**
     * 验证令牌
     *
     * @param token JWT令牌
     * @return 如果令牌有效返回true，否则返回false
     */
    public boolean validateToken(String token) {
        try {
            JwtClaims claims = parseToken(token);
            return claims.isActive();
        } catch (Exception e) {
            log.debug("Token validation failed: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 获取令牌剩余有效时间
     *
     * @param token JWT令牌
     * @return 剩余有效时间（秒），如果令牌无效返回0
     */
    public long getTokenRemainingTime(String token) {
        try {
            JwtClaims claims = parseToken(token);
            return claims.getRemainingSeconds();
        } catch (Exception e) {
            log.debug("Failed to get token remaining time: {}", e.getMessage());
            return 0;
        }
    }

    /**
     * 获取签名密钥
     *
     * @return 签名密钥
     */
    private SecretKey getSigningKey() {
        return Keys.hmacShaKeyFor(jwtConfig.getSecret().getBytes());
    }

    /**
     * 转换Claims为JwtClaims
     *
     * @param claims JWT Claims
     * @return JwtClaims对象
     */
    @SuppressWarnings("unchecked")
    private JwtClaims convertToJwtClaims(Claims claims) {
        JwtClaims.JwtClaimsBuilder builder = JwtClaims.builder()
                .subject(claims.getSubject())
                .issuer(claims.getIssuer())
                .audience(claims.getAudience())
                .expiration(toLocalDateTime(claims.getExpiration()))
                .issuedAt(toLocalDateTime(claims.getIssuedAt()))
                .jwtId(claims.getId());

        // 处理自定义声明
        for (Map.Entry<String, Object> entry : claims.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            switch (key) {
                case "username":
                    builder.username((String) value);
                    break;
                case "nickname":
                    builder.nickname((String) value);
                    break;
                case "email":
                    builder.email((String) value);
                    break;
                case "phone":
                    builder.phone((String) value);
                    break;
                case "roles":
                    builder.roles((java.util.List<String>) value);
                    break;
                case "permissions":
                    builder.permissions((java.util.List<String>) value);
                    break;
                case "grant_type":
                    builder.grantType((String) value);
                    break;
                case "client_id":
                    builder.clientId((String) value);
                    break;
                case "scope":
                    builder.scope((String) value);
                    break;
                case "session_id":
                    builder.sessionId((String) value);
                    break;
                case "device_id":
                    builder.deviceId((String) value);
                    break;
                case "ip_address":
                    builder.ipAddress((String) value);
                    break;
                case "user_agent":
                    builder.userAgent((String) value);
                    break;
                case "token_type":
                    builder.tokenType((String) value);
                    break;
                default:
                    // 其他自定义声明
                    if (!isStandardClaim(key)) {
                        builder.customClaims(java.util.Map.of(key, value));
                    }
                    break;
            }
        }

        return builder.build();
    }

    /**
     * 判断是否为标准声明
     *
     * @param claimName 声明名称
     * @return 如果是标准声明返回true，否则返回false
     */
    private boolean isStandardClaim(String claimName) {
        return "sub".equals(claimName) || "iss".equals(claimName) || "aud".equals(claimName) ||
               "exp".equals(claimName) || "nbf".equals(claimName) || "iat".equals(claimName) ||
               "jti".equals(claimName);
    }

    /**
     * LocalDateTime转Date
     *
     * @param localDateTime LocalDateTime
     * @return Date
     */
    private Date toDate(LocalDateTime localDateTime) {
        return localDateTime != null ? 
                Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant()) : null;
    }

    /**
     * Date转LocalDateTime
     *
     * @param date Date
     * @return LocalDateTime
     */
    private LocalDateTime toLocalDateTime(Date date) {
        return date != null ? 
                LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault()) : null;
    }
}
