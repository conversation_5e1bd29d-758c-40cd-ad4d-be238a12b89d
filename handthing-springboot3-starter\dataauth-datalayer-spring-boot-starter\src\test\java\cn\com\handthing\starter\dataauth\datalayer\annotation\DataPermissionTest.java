package cn.com.handthing.starter.dataauth.datalayer.annotation;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.lang.reflect.Method;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * DataPermission注解测试
 *
 * <AUTHOR>
 * @since V1.0.0
 */
class DataPermissionTest {
    
    /**
     * 测试类
     */
    @DataPermission(type = DataPermission.DataPermissionType.DEPT, field = "dept_id")
    static class TestClassWithPermission {
        
        @DataPermission(type = DataPermission.DataPermissionType.USER, field = "create_by")
        public void methodWithPermission() {
        }
        
        @DataPermission(
                type = DataPermission.DataPermissionType.CUSTOM,
                expression = "dept_id IN (#{deptIds})",
                scope = DataPermission.PermissionScope.CURRENT_AND_CHILDREN,
                onFailure = DataPermission.FailureStrategy.EXCEPTION
        )
        public void methodWithCustomPermission() {
        }
        
        public void methodWithoutPermission() {
        }
    }
    
    @Test
    @DisplayName("应该正确读取类级别的DataPermission注解")
    void shouldReadClassLevelDataPermission() throws Exception {
        // Given
        Class<?> clazz = TestClassWithPermission.class;
        
        // When
        DataPermission annotation = clazz.getAnnotation(DataPermission.class);
        
        // Then
        assertThat(annotation).isNotNull();
        assertThat(annotation.type()).isEqualTo(DataPermission.DataPermissionType.DEPT);
        assertThat(annotation.field()).isEqualTo("dept_id");
        assertThat(annotation.scope()).isEqualTo(DataPermission.PermissionScope.CURRENT);
        assertThat(annotation.onFailure()).isEqualTo(DataPermission.FailureStrategy.FILTER);
        assertThat(annotation.ignoreSuperAdmin()).isTrue();
        assertThat(annotation.enabled()).isEqualTo("true");
        assertThat(annotation.cacheSeconds()).isEqualTo(300);
    }
    
    @Test
    @DisplayName("应该正确读取方法级别的DataPermission注解")
    void shouldReadMethodLevelDataPermission() throws Exception {
        // Given
        Method method = TestClassWithPermission.class.getMethod("methodWithPermission");
        
        // When
        DataPermission annotation = method.getAnnotation(DataPermission.class);
        
        // Then
        assertThat(annotation).isNotNull();
        assertThat(annotation.type()).isEqualTo(DataPermission.DataPermissionType.USER);
        assertThat(annotation.field()).isEqualTo("create_by");
        assertThat(annotation.scope()).isEqualTo(DataPermission.PermissionScope.CURRENT);
        assertThat(annotation.onFailure()).isEqualTo(DataPermission.FailureStrategy.FILTER);
    }
    
    @Test
    @DisplayName("应该正确读取自定义权限配置")
    void shouldReadCustomPermissionConfig() throws Exception {
        // Given
        Method method = TestClassWithPermission.class.getMethod("methodWithCustomPermission");
        
        // When
        DataPermission annotation = method.getAnnotation(DataPermission.class);
        
        // Then
        assertThat(annotation).isNotNull();
        assertThat(annotation.type()).isEqualTo(DataPermission.DataPermissionType.CUSTOM);
        assertThat(annotation.expression()).isEqualTo("dept_id IN (#{deptIds})");
        assertThat(annotation.scope()).isEqualTo(DataPermission.PermissionScope.CURRENT_AND_CHILDREN);
        assertThat(annotation.onFailure()).isEqualTo(DataPermission.FailureStrategy.EXCEPTION);
    }
    
    @Test
    @DisplayName("没有注解的方法应该返回null")
    void shouldReturnNullForMethodWithoutAnnotation() throws Exception {
        // Given
        Method method = TestClassWithPermission.class.getMethod("methodWithoutPermission");
        
        // When
        DataPermission annotation = method.getAnnotation(DataPermission.class);
        
        // Then
        assertThat(annotation).isNull();
    }
    
    @Test
    @DisplayName("应该正确验证权限类型枚举")
    void shouldValidateDataPermissionTypeEnum() {
        // When & Then
        DataPermission.DataPermissionType[] types = DataPermission.DataPermissionType.values();
        
        assertThat(types).contains(
                DataPermission.DataPermissionType.DEPT,
                DataPermission.DataPermissionType.USER,
                DataPermission.DataPermissionType.ROLE,
                DataPermission.DataPermissionType.ORG,
                DataPermission.DataPermissionType.CUSTOM,
                DataPermission.DataPermissionType.NONE
        );
    }
    
    @Test
    @DisplayName("应该正确验证失败策略枚举")
    void shouldValidateFailureStrategyEnum() {
        // When & Then
        DataPermission.FailureStrategy[] strategies = DataPermission.FailureStrategy.values();
        
        assertThat(strategies).contains(
                DataPermission.FailureStrategy.FILTER,
                DataPermission.FailureStrategy.EXCEPTION,
                DataPermission.FailureStrategy.LOG,
                DataPermission.FailureStrategy.IGNORE
        );
    }
    
    @Test
    @DisplayName("应该正确验证权限范围枚举")
    void shouldValidatePermissionScopeEnum() {
        // When & Then
        DataPermission.PermissionScope[] scopes = DataPermission.PermissionScope.values();
        
        assertThat(scopes).contains(
                DataPermission.PermissionScope.CURRENT,
                DataPermission.PermissionScope.CURRENT_AND_CHILDREN,
                DataPermission.PermissionScope.ALL,
                DataPermission.PermissionScope.CUSTOM
        );
    }
    
    @Test
    @DisplayName("注解应该支持运行时保留")
    void shouldSupportRuntimeRetention() {
        // Given
        DataPermission annotation = TestClassWithPermission.class.getAnnotation(DataPermission.class);
        
        // When & Then
        assertThat(annotation).isNotNull();
        assertThat(annotation.annotationType()).isEqualTo(DataPermission.class);
    }
    
    @Test
    @DisplayName("注解应该支持方法和类型目标")
    void shouldSupportMethodAndTypeTargets() throws Exception {
        // Given
        Class<?> clazz = TestClassWithPermission.class;
        Method method = clazz.getMethod("methodWithPermission");
        
        // When & Then
        assertThat(clazz.isAnnotationPresent(DataPermission.class)).isTrue();
        assertThat(method.isAnnotationPresent(DataPermission.class)).isTrue();
    }
}
