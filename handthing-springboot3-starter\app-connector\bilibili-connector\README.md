# Bilibili连接器 (Bilibili Connector)

## 概述

Bilibili连接器提供了与Bilibili开放平台的集成能力，支持个人创作者、企业用户和MCN机构的视频内容创作和社区互动。通过统一的API接口，可以轻松实现用户认证、视频上传、弹幕互动、社区管理等功能。

## 特性

- ✅ **OAuth2.0认证** - 支持Bilibili用户授权登录
- ✅ **视频管理** - 支持视频上传、编辑、删除等操作
- ✅ **社区互动** - 支持评论管理、弹幕互动、关注管理
- ✅ **数据统计** - 获取视频数据、用户数据、互动统计
- ✅ **直播功能** - 支持直播间管理和直播数据获取
- ✅ **多用户类型** - 支持个人创作者、企业用户、MCN机构
- ✅ **测试环境** - 完整的测试环境支持

## 快速开始

### 1. 添加依赖

```xml
<dependency>
    <groupId>cn.com.handthing.springboot3.starter</groupId>
    <artifactId>bilibili-connector</artifactId>
    <version>1.0.0-SNAPSHOT</version>
</dependency>
```

### 2. 配置文件

```yaml
handthing:
  connector:
    bilibili:
      enabled: true
      app-key: "your-app-key"
      app-secret: "your-app-secret"
      app-type: "personal"  # personal/enterprise/mcn
      mcn-mode: false
      test-mode: true
      video-upload-enabled: true
      live-enabled: false
      analytics-enabled: true
      community-enabled: true
      scope: "user_info,video.upload,community.read,analytics.read"
      redirect-uri: "http://your-domain.com/callback/bilibili"
```

### 3. 基本使用

```java
@RestController
public class BilibiliController {
    
    @Autowired
    private BilibiliService bilibiliService;
    
    @Autowired
    private AuthService authService;
    
    // 获取授权URL
    @GetMapping("/bilibili/auth")
    public String getAuthUrl() {
        return authService.getAuthorizationUrl(
            PlatformType.BILIBILI, 
            "state", 
            "http://your-domain.com/callback/bilibili"
        );
    }
    
    // 上传视频
    @PostMapping("/bilibili/video/upload")
    public Map<String, Object> uploadVideo(@RequestParam String accessToken,
                                          @RequestParam String title,
                                          @RequestParam String description,
                                          @RequestParam String videoUrl,
                                          @RequestParam String cover,
                                          @RequestParam List<String> tags) {
        return bilibiliService.video().uploadVideo(accessToken, title, description, videoUrl, cover, tags);
    }
    
    // 获取视频列表
    @GetMapping("/bilibili/videos")
    public Map<String, Object> getVideoList(@RequestParam String accessToken,
                                           @RequestParam(defaultValue = "1") Integer page,
                                           @RequestParam(defaultValue = "20") Integer pageSize) {
        return bilibiliService.video().getVideoList(accessToken, page, pageSize);
    }
    
    // 获取粉丝列表
    @GetMapping("/bilibili/followers")
    public Map<String, Object> getFollowers(@RequestParam String accessToken,
                                           @RequestParam(defaultValue = "1") Integer page,
                                           @RequestParam(defaultValue = "20") Integer pageSize) {
        return bilibiliService.community().getFollowersList(accessToken, page, pageSize);
    }
    
    // 发送弹幕
    @PostMapping("/bilibili/danmaku")
    public Map<String, Object> sendDanmaku(@RequestParam String accessToken,
                                          @RequestParam Long cid,
                                          @RequestParam Long progress,
                                          @RequestParam String message) {
        return bilibiliService.community().sendDanmaku(accessToken, cid, progress, message);
    }
}
```

## 配置说明

| 配置项 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| `enabled` | Boolean | 是 | false | 是否启用Bilibili连接器 |
| `app-key` | String | 是 | - | 应用AppKey |
| `app-secret` | String | 是 | - | 应用AppSecret |
| `app-type` | String | 否 | personal | 应用类型(personal/enterprise/mcn) |
| `mcn-mode` | Boolean | 否 | false | 是否为MCN机构模式 |
| `test-mode` | Boolean | 否 | false | 是否使用测试环境 |
| `video-upload-enabled` | Boolean | 否 | true | 是否启用视频上传功能 |
| `live-enabled` | Boolean | 否 | false | 是否启用直播功能 |
| `analytics-enabled` | Boolean | 否 | true | 是否启用数据分析功能 |
| `community-enabled` | Boolean | 否 | true | 是否启用社区互动功能 |
| `scope` | String | 否 | user_info,video.upload,community.read,analytics.read | 授权范围 |
| `redirect-uri` | String | 否 | - | OAuth回调地址 |

## API文档

### 视频API (BilibiliVideoApi)

#### 上传视频
```java
Map<String, Object> uploadVideo(String accessToken, String title, String description, String videoUrl, String cover, List<String> tags)
```

#### 获取视频列表
```java
Map<String, Object> getVideoList(String accessToken, Integer page, Integer pageSize)
```

#### 获取视频详情
```java
Map<String, Object> getVideoDetail(String accessToken, String bvid)
```

#### 删除视频
```java
Map<String, Object> deleteVideo(String accessToken, Long aid)
```

#### 获取视频统计数据
```java
Map<String, Object> getVideoStats(String accessToken, String bvid)
```

#### 编辑视频信息
```java
Map<String, Object> editVideo(String accessToken, Long aid, String title, String description, List<String> tags)
```

### 社区API (BilibiliCommunityApi)

#### 获取粉丝列表
```java
Map<String, Object> getFollowersList(String accessToken, Integer page, Integer pageSize)
```

#### 获取关注列表
```java
Map<String, Object> getFollowingList(String accessToken, Integer page, Integer pageSize)
```

#### 获取视频评论
```java
Map<String, Object> getVideoComments(String accessToken, Long oid, Integer page, Integer pageSize)
```

#### 发送评论
```java
Map<String, Object> sendComment(String accessToken, Long oid, String message)
```

#### 获取弹幕列表
```java
Map<String, Object> getDanmakuList(String accessToken, Long cid)
```

#### 发送弹幕
```java
Map<String, Object> sendDanmaku(String accessToken, Long cid, Long progress, String message)
```

## 常见问题

### Q: 如何获取Bilibili的AppKey和AppSecret？
A: 登录Bilibili开放平台，在"应用管理"中创建应用，即可获取AppKey和AppSecret。

### Q: 个人创作者、企业用户、MCN机构有什么区别？
A: 个人创作者适用于个人UP主，企业用户适用于企业账号，MCN机构有更多的管理和数据权限。

### Q: 如何获取视频上传权限？
A: 需要满足一定的粉丝数量和内容质量要求，在开放平台申请相应权限。

### Q: 弹幕发送有什么限制？
A: 弹幕内容需要符合社区规范，发送频率有限制，需要用户登录状态。

## 更多信息

- [Bilibili开放平台文档](https://openhome.bilibili.com/)
- [视频投稿API](https://openhome.bilibili.com/doc/4/eaf2b83b-8acc-4f59-884b-9370e0a6a640)
- [用户相关API](https://openhome.bilibili.com/doc/4/07ce3f3e-3a0a-4c8d-8a2e-4e3b8c4c5a5a)
