{"groups": [{"name": "handthing.datalayer", "type": "cn.com.handthing.starter.datalayer.config.DatalayerProperties", "sourceType": "cn.com.handthing.starter.datalayer.config.DatalayerProperties"}, {"name": "handthing.datalayer.auto-fill", "type": "cn.com.handthing.starter.datalayer.config.DatalayerProperties$AutoFill", "sourceType": "cn.com.handthing.starter.datalayer.config.DatalayerProperties", "sourceMethod": "public cn.com.handthing.starter.datalayer.config.DatalayerProperties.AutoFill getAutoFill() "}, {"name": "handthing.datalayer.logic-delete", "type": "cn.com.handthing.starter.datalayer.config.DatalayerProperties$LogicDelete", "sourceType": "cn.com.handthing.starter.datalayer.config.DatalayerProperties", "sourceMethod": "public cn.com.handthing.starter.datalayer.config.DatalayerProperties.LogicDelete getLogicDelete() "}, {"name": "handthing.datalayer.optimistic-lock", "type": "cn.com.handthing.starter.datalayer.config.DatalayerProperties$OptimisticLock", "sourceType": "cn.com.handthing.starter.datalayer.config.DatalayerProperties", "sourceMethod": "public cn.com.handthing.starter.datalayer.config.DatalayerProperties.OptimisticLock getOptimisticLock() "}, {"name": "handthing.datalayer.pagination", "type": "cn.com.handthing.starter.datalayer.config.DatalayerProperties$Pagination", "sourceType": "cn.com.handthing.starter.datalayer.config.DatalayerProperties", "sourceMethod": "public cn.com.handthing.starter.datalayer.config.DatalayerProperties.Pagination getPagination() "}], "properties": [{"name": "handthing.datalayer.auto-fill.audit-enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用审计字段自动填充", "sourceType": "cn.com.handthing.starter.datalayer.config.DatalayerProperties$AutoFill"}, {"name": "handthing.datalayer.auto-fill.create-by-field", "type": "java.lang.String", "description": "创建人字段名", "sourceType": "cn.com.handthing.starter.datalayer.config.DatalayerProperties$AutoFill"}, {"name": "handthing.datalayer.auto-fill.create-time-field", "type": "java.lang.String", "description": "创建时间字段名", "sourceType": "cn.com.handthing.starter.datalayer.config.DatalayerProperties$AutoFill"}, {"name": "handthing.datalayer.auto-fill.deleted-field", "type": "java.lang.String", "description": "逻辑删除字段名", "sourceType": "cn.com.handthing.starter.datalayer.config.DatalayerProperties$AutoFill"}, {"name": "handthing.datalayer.auto-fill.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用自动填充", "sourceType": "cn.com.handthing.starter.datalayer.config.DatalayerProperties$AutoFill"}, {"name": "handthing.datalayer.auto-fill.id-setter-enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用@IdSetter注解处理", "sourceType": "cn.com.handthing.starter.datalayer.config.DatalayerProperties$AutoFill"}, {"name": "handthing.datalayer.auto-fill.time-enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用时间字段自动填充", "sourceType": "cn.com.handthing.starter.datalayer.config.DatalayerProperties$AutoFill"}, {"name": "handthing.datalayer.auto-fill.update-by-field", "type": "java.lang.String", "description": "更新人字段名", "sourceType": "cn.com.handthing.starter.datalayer.config.DatalayerProperties$AutoFill"}, {"name": "handthing.datalayer.auto-fill.update-time-field", "type": "java.lang.String", "description": "更新时间字段名", "sourceType": "cn.com.handthing.starter.datalayer.config.DatalayerProperties$AutoFill"}, {"name": "handthing.datalayer.auto-fill.version-field", "type": "java.lang.String", "description": "版本字段名", "sourceType": "cn.com.handthing.starter.datalayer.config.DatalayerProperties$AutoFill"}, {"name": "handthing.datalayer.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用数据层功能", "sourceType": "cn.com.handthing.starter.datalayer.config.DatalayerProperties"}, {"name": "handthing.datalayer.logic-delete.deleted-value", "type": "java.lang.String", "description": "逻辑删除值（已删除）", "sourceType": "cn.com.handthing.starter.datalayer.config.DatalayerProperties$LogicDelete"}, {"name": "handthing.datalayer.logic-delete.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用逻辑删除", "sourceType": "cn.com.handthing.starter.datalayer.config.DatalayerProperties$LogicDelete"}, {"name": "handthing.datalayer.logic-delete.field-name", "type": "java.lang.String", "description": "逻辑删除字段名", "sourceType": "cn.com.handthing.starter.datalayer.config.DatalayerProperties$LogicDelete"}, {"name": "handthing.datalayer.logic-delete.not-deleted-value", "type": "java.lang.String", "description": "逻辑未删除值（未删除）", "sourceType": "cn.com.handthing.starter.datalayer.config.DatalayerProperties$LogicDelete"}, {"name": "handthing.datalayer.optimistic-lock.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用乐观锁", "sourceType": "cn.com.handthing.starter.datalayer.config.DatalayerProperties$OptimisticLock"}, {"name": "handthing.datalayer.optimistic-lock.field-name", "type": "java.lang.String", "description": "版本字段名", "sourceType": "cn.com.handthing.starter.datalayer.config.DatalayerProperties$OptimisticLock"}, {"name": "handthing.datalayer.pagination.default-page-size", "type": "java.lang.Long", "description": "默认页大小", "sourceType": "cn.com.handthing.starter.datalayer.config.DatalayerProperties$Pagination"}, {"name": "handthing.datalayer.pagination.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用分页", "sourceType": "cn.com.handthing.starter.datalayer.config.DatalayerProperties$Pagination"}, {"name": "handthing.datalayer.pagination.max-page-size", "type": "java.lang.Long", "description": "最大页大小", "sourceType": "cn.com.handthing.starter.datalayer.config.DatalayerProperties$Pagination"}, {"name": "handthing.datalayer.pagination.reasonable", "type": "java.lang.Bo<PERSON>an", "description": "是否启用合理化分页（页码小于1时查询第一页，页码大于总页数时查询最后一页）", "sourceType": "cn.com.handthing.starter.datalayer.config.DatalayerProperties$Pagination"}, {"name": "handthing.datalayer.pagination.support-methods-arguments", "type": "java.lang.Bo<PERSON>an", "description": "是否支持接口参数来传递 page-size 和 page-num", "sourceType": "cn.com.handthing.starter.datalayer.config.DatalayerProperties$Pagination"}], "hints": [], "ignored": {"properties": []}}