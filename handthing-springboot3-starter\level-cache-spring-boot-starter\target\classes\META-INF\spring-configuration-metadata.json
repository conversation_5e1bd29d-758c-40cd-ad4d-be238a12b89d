{"groups": [{"name": "handthing.cache.level", "type": "cn.com.handthing.starter.cache.level.config.LevelCacheProperties", "sourceType": "cn.com.handthing.starter.cache.level.config.LevelCacheProperties"}, {"name": "handthing.cache.level.l1", "type": "cn.com.handthing.starter.cache.level.config.LevelCacheProperties$L1Config", "sourceType": "cn.com.handthing.starter.cache.level.config.LevelCacheProperties", "sourceMethod": "public cn.com.handthing.starter.cache.level.config.LevelCacheProperties.L1Config getL1() "}, {"name": "handthing.cache.level.l2", "type": "cn.com.handthing.starter.cache.level.config.LevelCacheProperties$L2Config", "sourceType": "cn.com.handthing.starter.cache.level.config.LevelCacheProperties", "sourceMethod": "public cn.com.handthing.starter.cache.level.config.LevelCacheProperties.L2Config getL2() "}, {"name": "handthing.cache.level.sync", "type": "cn.com.handthing.starter.cache.level.config.LevelCacheProperties$SyncConfig", "sourceType": "cn.com.handthing.starter.cache.level.config.LevelCacheProperties", "sourceMethod": "public cn.com.handthing.starter.cache.level.config.LevelCacheProperties.SyncConfig getSync() "}], "properties": [{"name": "handthing.cache.level.consistency-strategy", "type": "cn.com.handthing.starter.cache.level.config.LevelCacheProperties$ConsistencyStrategy", "description": "一致性策略", "sourceType": "cn.com.handthing.starter.cache.level.config.LevelCacheProperties"}, {"name": "handthing.cache.level.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用多级缓存", "sourceType": "cn.com.handthing.starter.cache.level.config.LevelCacheProperties"}, {"name": "handthing.cache.level.l1.caffeine-spec", "type": "java.lang.String", "description": "Caffeine配置规格字符串", "sourceType": "cn.com.handthing.starter.cache.level.config.LevelCacheProperties$L1Config"}, {"name": "handthing.cache.level.l1.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用L1缓存", "sourceType": "cn.com.handthing.starter.cache.level.config.LevelCacheProperties$L1Config"}, {"name": "handthing.cache.level.l1.max-size", "type": "java.lang.Long", "description": "L1缓存最大条目数", "sourceType": "cn.com.handthing.starter.cache.level.config.LevelCacheProperties$L1Config"}, {"name": "handthing.cache.level.l1.ttl", "type": "java.time.Duration", "description": "L1缓存过期时间", "sourceType": "cn.com.handthing.starter.cache.level.config.LevelCacheProperties$L1Config"}, {"name": "handthing.cache.level.l1.type", "type": "java.lang.String", "description": "L1缓存类型 (caffeine, memory)", "sourceType": "cn.com.handthing.starter.cache.level.config.LevelCacheProperties$L1Config"}, {"name": "handthing.cache.level.l2.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用L2缓存", "sourceType": "cn.com.handthing.starter.cache.level.config.LevelCacheProperties$L2Config"}, {"name": "handthing.cache.level.l2.key-prefix", "type": "java.lang.String", "description": "Redis键前缀", "sourceType": "cn.com.handthing.starter.cache.level.config.LevelCacheProperties$L2Config"}, {"name": "handthing.cache.level.l2.ttl", "type": "java.time.Duration", "description": "L2缓存过期时间", "sourceType": "cn.com.handthing.starter.cache.level.config.LevelCacheProperties$L2Config"}, {"name": "handthing.cache.level.l2.type", "type": "java.lang.String", "description": "L2缓存类型 (redis)", "sourceType": "cn.com.handthing.starter.cache.level.config.LevelCacheProperties$L2Config"}, {"name": "handthing.cache.level.sync.deduplication-window", "type": "java.time.Duration", "description": "消息去重窗口时间", "sourceType": "cn.com.handthing.starter.cache.level.config.LevelCacheProperties$SyncConfig"}, {"name": "handthing.cache.level.sync.enable-deduplication", "type": "java.lang.Bo<PERSON>an", "description": "是否启用消息去重", "sourceType": "cn.com.handthing.starter.cache.level.config.LevelCacheProperties$SyncConfig"}, {"name": "handthing.cache.level.sync.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用缓存同步", "sourceType": "cn.com.handthing.starter.cache.level.config.LevelCacheProperties$SyncConfig"}, {"name": "handthing.cache.level.sync.timeout", "type": "java.time.Duration", "description": "同步消息超时时间", "sourceType": "cn.com.handthing.starter.cache.level.config.LevelCacheProperties$SyncConfig"}, {"name": "handthing.cache.level.sync.topic", "type": "java.lang.String", "description": "同步主题名称", "sourceType": "cn.com.handthing.starter.cache.level.config.LevelCacheProperties$SyncConfig"}], "hints": [], "ignored": {"properties": []}}