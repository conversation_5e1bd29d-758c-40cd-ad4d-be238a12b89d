package cn.com.handthing.test.controller;

import cn.com.handthing.starter.connector.PlatformType;
import cn.com.handthing.starter.connector.auth.AuthService;
import cn.com.handthing.starter.connector.auth.UnifiedAccessToken;
import cn.com.handthing.starter.connector.auth.UnifiedUserInfo;
import cn.com.handthing.starter.connector.token.TokenManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * App Connector 测试控制器
 * <p>
 * 提供App Connector功能的测试接口，包括认证流程测试、
 * Token管理测试等功能。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/test/app-connector")
@RequiredArgsConstructor
@ConditionalOnBean(AuthService.class)
public class AppConnectorTestController {

    private final AuthService authService;
    private final TokenManager tokenManager;

    /**
     * 获取所有支持的平台列表
     *
     * @return 支持的平台列表
     */
    @GetMapping("/platforms")
    public Map<String, Object> getSupportedPlatforms() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            Map<String, Object> platforms = new HashMap<>();
            
            // 检查各个平台的支持情况
            for (PlatformType platform : PlatformType.values()) {
                Map<String, Object> platformInfo = new HashMap<>();
                platformInfo.put("name", platform.name());
                platformInfo.put("displayName", getPlatformDisplayName(platform));
                
                try {
                    // 尝试生成授权URL来检查平台是否可用
                    String authUrl = authService.getAuthorizationUrl(platform, "test-state");
                    platformInfo.put("available", true);
                    platformInfo.put("testAuthUrl", authUrl);
                } catch (Exception e) {
                    platformInfo.put("available", false);
                    platformInfo.put("error", e.getMessage());
                }
                
                platforms.put(platform.name().toLowerCase(), platformInfo);
            }
            
            result.put("success", true);
            result.put("platforms", platforms);
            result.put("totalPlatforms", platforms.size());
            
        } catch (Exception e) {
            log.error("Failed to get supported platforms", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    /**
     * 生成指定平台的授权URL
     *
     * @param platform 平台类型
     * @param state    状态参数
     * @param redirectUri 回调地址
     * @return 授权URL
     */
    @GetMapping("/auth/{platform}")
    public Map<String, Object> getAuthorizationUrl(@PathVariable String platform,
                                                   @RequestParam(required = false) String state,
                                                   @RequestParam(required = false) String redirectUri) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            PlatformType platformType = PlatformType.valueOf(platform.toUpperCase());
            
            if (state == null) {
                state = "test-" + System.currentTimeMillis();
            }
            
            String authUrl = authService.getAuthorizationUrl(platformType, state, redirectUri);
            
            result.put("success", true);
            result.put("platform", platform);
            result.put("authUrl", authUrl);
            result.put("state", state);
            result.put("redirectUri", redirectUri);
            
            log.info("Generated auth URL for platform: {}, state: {}", platform, state);
            
        } catch (IllegalArgumentException e) {
            result.put("success", false);
            result.put("error", "Unsupported platform: " + platform);
        } catch (Exception e) {
            log.error("Failed to generate auth URL for platform: {}", platform, e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    /**
     * 模拟授权回调处理
     *
     * @param platform 平台类型
     * @param code     授权码
     * @param state    状态参数
     * @return 处理结果
     */
    @PostMapping("/callback/{platform}")
    public Map<String, Object> handleCallback(@PathVariable String platform,
                                              @RequestParam String code,
                                              @RequestParam(required = false) String state) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            PlatformType platformType = PlatformType.valueOf(platform.toUpperCase());
            
            // 注意：这里只是模拟回调处理，实际环境中需要真实的授权码
            result.put("success", false);
            result.put("message", "This is a test endpoint. Real callback processing requires valid authorization code from " + getPlatformDisplayName(platformType));
            result.put("platform", platform);
            result.put("code", code);
            result.put("state", state);
            result.put("note", "To test real callback, use the authUrl from /auth/{platform} endpoint and complete OAuth flow");
            
            log.info("Received callback for platform: {}, code: {}, state: {}", platform, code, state);
            
        } catch (IllegalArgumentException e) {
            result.put("success", false);
            result.put("error", "Unsupported platform: " + platform);
        } catch (Exception e) {
            log.error("Failed to handle callback for platform: {}", platform, e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    /**
     * 获取Token管理器状态
     *
     * @return Token管理器状态
     */
    @GetMapping("/token/status")
    public Map<String, Object> getTokenManagerStatus() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            Map<String, Object> status = new HashMap<>();
            status.put("tokenManagerClass", tokenManager.getClass().getSimpleName());
            status.put("available", true);

            // 注意：TokenManager接口可能没有getRegisteredProviders方法
            // 这里只显示基本状态信息
            status.put("note", "TokenManager is available and functional");
            
            result.put("success", true);
            result.put("tokenManager", status);
            
        } catch (Exception e) {
            log.error("Failed to get token manager status", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    /**
     * 测试配置信息
     *
     * @return 配置测试结果
     */
    @GetMapping("/config/test")
    public Map<String, Object> testConfiguration() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            Map<String, Object> configTest = new HashMap<>();
            
            // 测试各个平台的配置
            for (PlatformType platform : PlatformType.values()) {
                Map<String, Object> platformTest = new HashMap<>();
                
                try {
                    // 尝试生成授权URL来验证配置
                    String authUrl = authService.getAuthorizationUrl(platform, "config-test");
                    platformTest.put("configured", true);
                    platformTest.put("authUrlGenerated", true);
                } catch (Exception e) {
                    platformTest.put("configured", false);
                    platformTest.put("error", e.getMessage());
                }
                
                configTest.put(platform.name().toLowerCase(), platformTest);
            }
            
            result.put("success", true);
            result.put("configurationTest", configTest);
            result.put("timestamp", System.currentTimeMillis());
            
        } catch (Exception e) {
            log.error("Failed to test configuration", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    /**
     * 获取平台显示名称
     */
    private String getPlatformDisplayName(PlatformType platform) {
        switch (platform) {
            case WECOM:
                return "企业微信";
            case DINGTALK:
                return "钉钉";
            case DOUYIN:
                return "抖音";
            case ALIPAY:
                return "支付宝";
            default:
                return platform.name();
        }
    }
}
