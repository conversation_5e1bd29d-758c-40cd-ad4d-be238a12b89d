package cn.com.handthing.starter.auth.thirdparty;

import cn.com.handthing.core.util.JsonUtils;
import cn.com.handthing.starter.auth.core.JwtTokenProvider;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.web.reactive.function.client.WebClient;

/**
 * 第三方认证提供者自动配置
 * <p>
 * 负责第三方认证提供者相关组件的自动配置，包括企业微信等第三方平台的认证支持。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@AutoConfiguration
@ConditionalOnProperty(prefix = "handthing.auth.third-party", name = "enabled", havingValue = "true", matchIfMissing = true)
public class ThirdPartyProviderAutoConfiguration {

    /**
     * WebClient配置
     *
     * @return WebClient
     */
    @Bean
    @ConditionalOnMissingBean
    public WebClient webClient() {
        log.info("Creating WebClient for third-party API calls");
        return WebClient.builder()
                .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(1024 * 1024))
                .build();
    }

    /**
     * ObjectMapper配置
     * <p>
     * 使用handthing-core的JsonUtils提供的ObjectMapper，
     * 已经配置了Java 8时间类型支持和其他优化。
     * </p>
     *
     * @return ObjectMapper
     */
    @Bean
    @ConditionalOnMissingBean
    public ObjectMapper objectMapper() {
        log.info("Creating ObjectMapper for JSON processing using handthing-core JsonUtils");
        return JsonUtils.getMapper();
    }

    /**
     * 企业微信API服务
     *
     * @param webClient    WebClient
     * @param objectMapper ObjectMapper
     * @return 企业微信API服务
     */
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(prefix = "handthing.auth.third-party.wecom", name = "enabled", havingValue = "true", matchIfMissing = true)
    public WecomApiService wecomApiService(WebClient webClient, ObjectMapper objectMapper) {
        log.info("Creating DefaultWecomApiService");
        return new DefaultWecomApiService(webClient, objectMapper);
    }

    /**
     * 企业微信用户服务
     *
     * @return 企业微信用户服务
     */
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(prefix = "handthing.auth.third-party.wecom", name = "enabled", havingValue = "true", matchIfMissing = true)
    public WecomUserService wecomUserService() {
        log.info("Creating DefaultWecomUserService");
        return new DefaultWecomUserService();
    }

    /**
     * 企业微信认证提供者
     *
     * @param wecomUserService 企业微信用户服务
     * @param wecomApiService  企业微信API服务
     * @param jwtTokenProvider JWT令牌提供者
     * @return 企业微信认证提供者
     */
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(prefix = "handthing.auth.third-party.wecom", name = "enabled", havingValue = "true", matchIfMissing = true)
    public WecomAuthenticationProvider wecomAuthenticationProvider(
            WecomUserService wecomUserService,
            WecomApiService wecomApiService,
            JwtTokenProvider jwtTokenProvider) {
        
        log.info("Creating WecomAuthenticationProvider");
        return new WecomAuthenticationProvider(wecomUserService, wecomApiService, jwtTokenProvider);
    }

    /**
     * 企业微信控制器
     *
     * @param wecomApiService  企业微信API服务
     * @param wecomUserService 企业微信用户服务
     * @return 企业微信控制器
     */
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(prefix = "handthing.auth.third-party.wecom", name = "endpoints-enabled", havingValue = "true", matchIfMissing = true)
    public WecomController wecomController(WecomApiService wecomApiService, WecomUserService wecomUserService) {
        log.info("Creating WecomController");
        return new WecomController(wecomApiService, wecomUserService);
    }

    /**
     * 钉钉API服务
     *
     * @param webClient    WebClient
     * @param objectMapper ObjectMapper
     * @return 钉钉API服务
     */
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(prefix = "handthing.auth.third-party.dingtalk", name = "enabled", havingValue = "true", matchIfMissing = true)
    public DingtalkApiService dingtalkApiService(WebClient webClient, ObjectMapper objectMapper) {
        log.info("Creating DefaultDingtalkApiService");
        return new DefaultDingtalkApiService(webClient, objectMapper);
    }

    /**
     * 钉钉用户服务
     *
     * @return 钉钉用户服务
     */
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(prefix = "handthing.auth.third-party.dingtalk", name = "enabled", havingValue = "true", matchIfMissing = true)
    public DingtalkUserService dingtalkUserService() {
        log.info("Creating DefaultDingtalkUserService");
        return new DefaultDingtalkUserService();
    }

    /**
     * 钉钉认证提供者
     *
     * @param dingtalkUserService 钉钉用户服务
     * @param dingtalkApiService  钉钉API服务
     * @param jwtTokenProvider    JWT令牌提供者
     * @return 钉钉认证提供者
     */
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(prefix = "handthing.auth.third-party.dingtalk", name = "enabled", havingValue = "true", matchIfMissing = true)
    public DingtalkAuthenticationProvider dingtalkAuthenticationProvider(
            DingtalkUserService dingtalkUserService,
            DingtalkApiService dingtalkApiService,
            JwtTokenProvider jwtTokenProvider) {

        log.info("Creating DingtalkAuthenticationProvider");
        return new DingtalkAuthenticationProvider(dingtalkUserService, dingtalkApiService, jwtTokenProvider);
    }

    /**
     * 钉钉控制器
     *
     * @param dingtalkApiService  钉钉API服务
     * @param dingtalkUserService 钉钉用户服务
     * @return 钉钉控制器
     */
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(prefix = "handthing.auth.third-party.dingtalk", name = "endpoints-enabled", havingValue = "true", matchIfMissing = true)
    public DingtalkController dingtalkController(DingtalkApiService dingtalkApiService, DingtalkUserService dingtalkUserService) {
        log.info("Creating DingtalkController");
        return new DingtalkController(dingtalkApiService, dingtalkUserService);
    }

    /**
     * 微信API服务
     *
     * @param webClient    WebClient
     * @param objectMapper ObjectMapper
     * @return 微信API服务
     */
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(prefix = "handthing.auth.third-party.wechat", name = "enabled", havingValue = "true", matchIfMissing = true)
    public WechatApiService wechatApiService(WebClient webClient, ObjectMapper objectMapper) {
        log.info("Creating DefaultWechatApiService");
        return new DefaultWechatApiService(webClient, objectMapper);
    }

    /**
     * 微信用户服务
     *
     * @return 微信用户服务
     */
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(prefix = "handthing.auth.third-party.wechat", name = "enabled", havingValue = "true", matchIfMissing = true)
    public WechatUserService wechatUserService() {
        log.info("Creating DefaultWechatUserService");
        return new DefaultWechatUserService();
    }

    /**
     * 微信认证提供者
     *
     * @param wechatUserService 微信用户服务
     * @param wechatApiService  微信API服务
     * @param jwtTokenProvider  JWT令牌提供者
     * @return 微信认证提供者
     */
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(prefix = "handthing.auth.third-party.wechat", name = "enabled", havingValue = "true", matchIfMissing = true)
    public WechatAuthenticationProvider wechatAuthenticationProvider(
            WechatUserService wechatUserService,
            WechatApiService wechatApiService,
            JwtTokenProvider jwtTokenProvider) {

        log.info("Creating WechatAuthenticationProvider");
        return new WechatAuthenticationProvider(wechatUserService, wechatApiService, jwtTokenProvider);
    }

    /**
     * 微信控制器
     *
     * @param wechatApiService  微信API服务
     * @param wechatUserService 微信用户服务
     * @return 微信控制器
     */
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(prefix = "handthing.auth.third-party.wechat", name = "endpoints-enabled", havingValue = "true", matchIfMissing = true)
    public WechatController wechatController(WechatApiService wechatApiService, WechatUserService wechatUserService) {
        log.info("Creating WechatController");
        return new WechatController(wechatApiService, wechatUserService);
    }

    /**
     * 飞书API服务
     *
     * @param webClient    WebClient
     * @param objectMapper ObjectMapper
     * @return 飞书API服务
     */
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(prefix = "handthing.auth.third-party.feishu", name = "enabled", havingValue = "true", matchIfMissing = true)
    public FeishuApiService feishuApiService(WebClient webClient, ObjectMapper objectMapper) {
        log.info("Creating DefaultFeishuApiService");
        return new DefaultFeishuApiService(webClient, objectMapper);
    }

    /**
     * 飞书用户服务
     *
     * @return 飞书用户服务
     */
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(prefix = "handthing.auth.third-party.feishu", name = "enabled", havingValue = "true", matchIfMissing = true)
    public FeishuUserService feishuUserService() {
        log.info("Creating DefaultFeishuUserService");
        return new DefaultFeishuUserService();
    }

    /**
     * 飞书认证提供者
     *
     * @param feishuUserService 飞书用户服务
     * @param feishuApiService  飞书API服务
     * @param jwtTokenProvider  JWT令牌提供者
     * @return 飞书认证提供者
     */
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(prefix = "handthing.auth.third-party.feishu", name = "enabled", havingValue = "true", matchIfMissing = true)
    public FeishuAuthenticationProvider feishuAuthenticationProvider(
            FeishuUserService feishuUserService,
            FeishuApiService feishuApiService,
            JwtTokenProvider jwtTokenProvider) {

        log.info("Creating FeishuAuthenticationProvider");
        return new FeishuAuthenticationProvider(feishuUserService, feishuApiService, jwtTokenProvider);
    }

    /**
     * 飞书控制器
     *
     * @param feishuApiService  飞书API服务
     * @param feishuUserService 飞书用户服务
     * @return 飞书控制器
     */
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(prefix = "handthing.auth.third-party.feishu", name = "endpoints-enabled", havingValue = "true", matchIfMissing = true)
    public FeishuController feishuController(FeishuApiService feishuApiService, FeishuUserService feishuUserService) {
        log.info("Creating FeishuController");
        return new FeishuController(feishuApiService, feishuUserService);
    }

    /**
     * 第三方认证配置属性
     *
     * @return 第三方认证配置属性
     */
    @Bean
    @ConditionalOnMissingBean
    public ThirdPartyAuthProperties thirdPartyAuthProperties() {
        log.info("Creating ThirdPartyAuthProperties");
        return new ThirdPartyAuthProperties();
    }
}
