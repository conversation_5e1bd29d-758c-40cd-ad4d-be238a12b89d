{"groups": [{"name": "handthing.crypto", "type": "cn.com.handthing.starter.crypto.properties.CryptoProperties", "sourceType": "cn.com.handthing.starter.crypto.properties.CryptoProperties"}, {"name": "handthing.crypto.observability", "type": "cn.com.handthing.starter.crypto.properties.CryptoProperties$Observability", "sourceType": "cn.com.handthing.starter.crypto.properties.CryptoProperties", "sourceMethod": "public cn.com.handthing.starter.crypto.properties.CryptoProperties.Observability getObservability() "}, {"name": "handthing.crypto.refresh", "type": "cn.com.handthing.starter.crypto.properties.CryptoProperties$Refresh", "sourceType": "cn.com.handthing.starter.crypto.properties.CryptoProperties", "sourceMethod": "public cn.com.handthing.starter.crypto.properties.CryptoProperties.Refresh getRefresh() "}, {"name": "handthing.crypto.tool", "type": "cn.com.handthing.starter.crypto.properties.CryptoProperties$Tool", "sourceType": "cn.com.handthing.starter.crypto.properties.CryptoProperties", "sourceMethod": "public cn.com.handthing.starter.crypto.properties.CryptoProperties.Tool getTool() "}, {"name": "handthing.crypto.tool.generate-key", "type": "cn.com.handthing.starter.crypto.properties.CryptoProperties$Tool$GenerateKey", "sourceType": "cn.com.handthing.starter.crypto.properties.CryptoProperties$Tool", "sourceMethod": "public cn.com.handthing.starter.crypto.properties.CryptoProperties.Tool.GenerateKey getGenerateKey() "}], "properties": [{"name": "handthing.crypto.asymmetric", "type": "java.util.Map<java.lang.String,cn.com.handthing.starter.crypto.properties.CryptoProperties$AsymmetricConfig>", "description": "非对称加密算法配置映射。Key 是用户自定义的处理器名称。", "sourceType": "cn.com.handthing.starter.crypto.properties.CryptoProperties"}, {"name": "handthing.crypto.digest", "type": "java.util.Map<java.lang.String,cn.com.handthing.starter.crypto.properties.CryptoProperties$DigestConfig>", "description": "摘要（哈希）算法配置映射。Key 是用户自定义的处理器名称。", "sourceType": "cn.com.handthing.starter.crypto.properties.CryptoProperties"}, {"name": "handthing.crypto.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用 HandThing Crypto Starter。如果为 false，则不会注册任何 Bean。", "sourceType": "cn.com.handthing.starter.crypto.properties.CryptoProperties"}, {"name": "handthing.crypto.observability.health-check-enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否为 Crypto Starter 启用 Actuator 健康检查端点 (/actuator/health/crypto)。", "sourceType": "cn.com.handthing.starter.crypto.properties.CryptoProperties$Observability"}, {"name": "handthing.crypto.observability.metrics-enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否为 Crypto Starter 启用 Micrometer 性能指标。", "sourceType": "cn.com.handthing.starter.crypto.properties.CryptoProperties$Observability"}, {"name": "handthing.crypto.refresh.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用配置动态刷新。 启用后，当从配置中心（如 Nacos, Consul）获取到新的配置时， 加密器和摘要器实例将被重新创建以应用新配置。 这需要项目中存在 Spring Cloud 上下文环境。", "sourceType": "cn.com.handthing.starter.crypto.properties.CryptoProperties$Refresh"}, {"name": "handthing.crypto.symmetric", "type": "java.util.Map<java.lang.String,cn.com.handthing.starter.crypto.properties.CryptoProperties$SymmetricConfig>", "description": "对称加密算法配置映射。Key 是用户自定义的处理器名称，例如 \"sensitiveDataEncryptor\"。", "sourceType": "cn.com.handthing.starter.crypto.properties.CryptoProperties"}, {"name": "handthing.crypto.tool.generate-key.algorithm", "type": "java.lang.String", "description": "要生成的密钥算法，例如 \"AES\"。设置此属性将激活工具。", "sourceType": "cn.com.handthing.starter.crypto.properties.CryptoProperties$Tool$GenerateKey"}, {"name": "handthing.crypto.tool.generate-key.size", "type": "java.lang.Integer", "description": "要生成的密钥长度（比特）。例如 AES 支持 128, 192, 256。", "sourceType": "cn.com.handthing.starter.crypto.properties.CryptoProperties$Tool$GenerateKey"}], "hints": [], "ignored": {"properties": []}}