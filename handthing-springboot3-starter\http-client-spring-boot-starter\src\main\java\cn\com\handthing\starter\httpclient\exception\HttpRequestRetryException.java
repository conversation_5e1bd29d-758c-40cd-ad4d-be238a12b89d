package cn.com.handthing.starter.httpclient.exception;

/**
 * 表示在达到配置的最大重试次数后，请求仍然失败的异常。
 * <p>
 * 这通常是重试逻辑的最终出口，其 'cause' 属性通常是最后一次尝试失败时抛出的原始异常。
 */
public class HttpRequestRetryException extends HttpRequestException {

    private final int totalAttempts;

    /**
     * 使用指定的详细消息、总尝试次数和最后一次失败的原因构造一个新的重试失败异常。
     *
     * @param message       详细消息。
     * @param totalAttempts 发起的总请求次数 (首次 + 所有重试)。
     * @param cause         最后一次尝试失败时的根本原因。
     */
    public HttpRequestRetryException(String message, int totalAttempts, Throwable cause) {
        super(message, cause);
        this.totalAttempts = totalAttempts;
    }

    /**
     * 获取总共尝试的请求次数。
     *
     * @return 总尝试次数。
     */
    public int getTotalAttempts() {
        return totalAttempts;
    }
}