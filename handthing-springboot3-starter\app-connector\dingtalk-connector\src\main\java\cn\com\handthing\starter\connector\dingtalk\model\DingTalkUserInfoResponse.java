package cn.com.handthing.starter.connector.dingtalk.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 钉钉用户信息响应模型
 * <p>
 * 钉钉获取用户信息API的响应数据结构
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DingTalkUserInfoResponse {

    /**
     * 用户在当前开放应用内的唯一标识
     */
    @JsonProperty("openId")
    private String openId;

    /**
     * 用户在当前开发者企业账号范围内的唯一标识
     */
    @JsonProperty("unionId")
    private String unionId;

    /**
     * 用户昵称
     */
    @JsonProperty("nick")
    private String nick;

    /**
     * 用户头像
     */
    @JsonProperty("avatarUrl")
    private String avatarUrl;

    /**
     * 用户手机号
     */
    @JsonProperty("mobile")
    private String mobile;

    /**
     * 用户邮箱
     */
    @JsonProperty("email")
    private String email;

    /**
     * 用户所在地区的国家或地区代码
     */
    @JsonProperty("stateCode")
    private String stateCode;

    /**
     * 检查响应是否成功
     *
     * @return 如果成功返回true，否则返回false
     */
    public boolean isSuccess() {
        return unionId != null && !unionId.trim().isEmpty();
    }

    /**
     * 获取显示名称
     *
     * @return 显示名称
     */
    public String getDisplayName() {
        if (nick != null && !nick.trim().isEmpty()) {
            return nick;
        }
        return unionId;
    }
}
