package cn.com.handthing.starter.httpclient;

import cn.com.handthing.starter.httpclient.config.HttpClientProperties;
import cn.com.handthing.starter.httpclient.crypto.NoOpCryptoProvider;
import cn.com.handthing.starter.httpclient.crypto.RequestEncryptor;
import cn.com.handthing.starter.httpclient.crypto.ResponseDecryptor;
import cn.com.handthing.starter.httpclient.internal.EndpointConfigResolver;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;

/**
 * HTTP客户端核心模块的自动配置类。
 * <p>
 * 负责注册与具体实现无关的、可被共享的核心Bean。
 */
@AutoConfiguration
@EnableConfigurationProperties(HttpClientProperties.class)
@ConditionalOnProperty(name = "handthing.http-client.enabled", havingValue = "true", matchIfMissing = true)
public class HttpCoreAutoConfiguration {

    /**
     * 创建并注册端点配置解析器 Bean。
     * <p>
     * 此 Bean 负责根据请求URL解析出最终生效的配置。
     *
     * @param properties 绑定的顶层配置属性。
     * @return {@link EndpointConfigResolver} 实例。
     */
    @Bean
    @ConditionalOnMissingBean
    public EndpointConfigResolver handthingEndpointConfigResolver(HttpClientProperties properties) {
        return new EndpointConfigResolver(properties);
    }

    /**
     * 创建并注册一个默认的、无操作的请求加密器 Bean。
     * <p>
     * 当且仅当Spring上下文中不存在任何其他 {@link RequestEncryptor} 类型的Bean时，此Bean才会被创建。
     * 这允许用户通过提供自己的Bean来轻松覆盖默认行为。
     *
     * @return 一个实现了 {@link RequestEncryptor} 的 {@link NoOpCryptoProvider} 实例。
     */
    @Bean
    @ConditionalOnMissingBean(RequestEncryptor.class)
    public RequestEncryptor<?> handthingNoOpRequestEncryptor() {
        return new NoOpCryptoProvider();
    }

    /**
     * 创建并注册一个默认的、无操作的响应解密器 Bean。
     * <p>
     * 当且仅当Spring上下文中不存在任何其他 {@link ResponseDecryptor} 类型的Bean时，此Bean才会被创建。
     *
     * @return 一个实现了 {@link ResponseDecryptor} 的 {@link NoOpCryptoProvider} 实例。
     */
    @Bean
    @ConditionalOnMissingBean(ResponseDecryptor.class)
    public ResponseDecryptor handthingNoOpResponseDecryptor() {
        return new NoOpCryptoProvider();
    }
}