package cn.com.handthing.starter.tenant.datalayer.util;

import cn.com.handthing.starter.datalayer.util.DatalayerUtils;
import cn.com.handthing.starter.tenant.context.TenantContextHolder;
import cn.com.handthing.starter.tenant.datalayer.entity.TenantBaseEntity;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import lombok.extern.slf4j.Slf4j;

import java.util.Collection;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * 租户数据层工具类
 * <p>
 * 提供租户相关的数据操作工具方法，包括租户上下文操作、租户数据查询工具等。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
public final class TenantDatalayerUtils {
    
    private TenantDatalayerUtils() {
        // 工具类，禁止实例化
    }
    
    // ========================================
    // 租户上下文操作
    // ========================================
    
    /**
     * 获取当前租户ID
     *
     * @return 当前租户ID，如果不存在返回null
     */
    public static String getCurrentTenantId() {
        try {
            return TenantContextHolder.getTenantId();
        } catch (Exception e) {
            log.debug("Failed to get current tenant ID", e);
            return null;
        }
    }
    
    /**
     * 检查是否有租户上下文
     *
     * @return 如果有租户上下文返回true，否则返回false
     */
    public static boolean hasTenantContext() {
        String tenantId = getCurrentTenantId();
        return tenantId != null && !tenantId.trim().isEmpty();
    }
    
    /**
     * 检查是否为指定租户
     *
     * @param tenantId 租户ID
     * @return 如果是指定租户返回true，否则返回false
     */
    public static boolean isCurrentTenant(String tenantId) {
        String currentTenantId = getCurrentTenantId();
        return currentTenantId != null && currentTenantId.equals(tenantId);
    }
    
    /**
     * 检查是否为默认租户
     *
     * @return 如果是默认租户返回true，否则返回false
     */
    public static boolean isDefaultTenant() {
        return isCurrentTenant("default");
    }
    
    // ========================================
    // 租户环境执行
    // ========================================
    
    /**
     * 在指定租户环境中执行操作
     *
     * @param tenantId 租户ID
     * @param runnable 要执行的操作
     */
    public static void runWithTenant(String tenantId, Runnable runnable) {
        if (runnable == null) {
            return;
        }
        
        String originalTenantId = getCurrentTenantId();
        try {
            TenantContextHolder.setTenantId(tenantId);
            runnable.run();
        } finally {
            if (originalTenantId != null) {
                TenantContextHolder.setTenantId(originalTenantId);
            } else {
                TenantContextHolder.clearContext();
            }
        }
    }
    
    /**
     * 在指定租户环境中执行操作并返回结果
     *
     * @param tenantId 租户ID
     * @param callable 要执行的操作
     * @param <T>      返回类型
     * @return 操作结果
     * @throws Exception 如果操作失败
     */
    public static <T> T callWithTenant(String tenantId, Callable<T> callable) throws Exception {
        if (callable == null) {
            return null;
        }
        
        String originalTenantId = getCurrentTenantId();
        try {
            TenantContextHolder.setTenantId(tenantId);
            return callable.call();
        } finally {
            if (originalTenantId != null) {
                TenantContextHolder.setTenantId(originalTenantId);
            } else {
                TenantContextHolder.clearContext();
            }
        }
    }
    
    /**
     * 在指定租户环境中执行操作并返回结果（无异常版本）
     *
     * @param tenantId 租户ID
     * @param supplier 要执行的操作
     * @param <T>      返回类型
     * @return 操作结果
     */
    public static <T> T supplyWithTenant(String tenantId, Supplier<T> supplier) {
        if (supplier == null) {
            return null;
        }
        
        String originalTenantId = getCurrentTenantId();
        try {
            TenantContextHolder.setTenantId(tenantId);
            return supplier.get();
        } finally {
            if (originalTenantId != null) {
                TenantContextHolder.setTenantId(originalTenantId);
            } else {
                TenantContextHolder.clearContext();
            }
        }
    }
    
    /**
     * 在默认租户环境中执行操作
     *
     * @param runnable 要执行的操作
     */
    public static void runWithDefaultTenant(Runnable runnable) {
        runWithTenant("default", runnable);
    }
    
    /**
     * 在默认租户环境中执行操作并返回结果
     *
     * @param supplier 要执行的操作
     * @param <T>      返回类型
     * @return 操作结果
     */
    public static <T> T supplyWithDefaultTenant(Supplier<T> supplier) {
        return supplyWithTenant("default", supplier);
    }
    
    // ========================================
    // 租户数据查询工具
    // ========================================
    
    /**
     * 创建租户查询包装器
     *
     * @param tenantId 租户ID
     * @param <T>      实体类型
     * @return 查询包装器
     */
    public static <T> QueryWrapper<T> createTenantQueryWrapper(String tenantId) {
        QueryWrapper<T> wrapper = DatalayerUtils.createQueryWrapper();
        if (tenantId != null && !tenantId.trim().isEmpty()) {
            wrapper.eq("tenant_id", tenantId);
        }
        return wrapper;
    }
    
    /**
     * 创建当前租户查询包装器
     *
     * @param <T> 实体类型
     * @return 查询包装器
     */
    public static <T> QueryWrapper<T> createCurrentTenantQueryWrapper() {
        return createTenantQueryWrapper(getCurrentTenantId());
    }
    
    /**
     * 创建租户更新包装器
     *
     * @param tenantId 租户ID
     * @param <T>      实体类型
     * @return 更新包装器
     */
    public static <T> UpdateWrapper<T> createTenantUpdateWrapper(String tenantId) {
        UpdateWrapper<T> wrapper = DatalayerUtils.createUpdateWrapper();
        if (tenantId != null && !tenantId.trim().isEmpty()) {
            wrapper.eq("tenant_id", tenantId);
        }
        return wrapper;
    }
    
    /**
     * 创建当前租户更新包装器
     *
     * @param <T> 实体类型
     * @return 更新包装器
     */
    public static <T> UpdateWrapper<T> createCurrentTenantUpdateWrapper() {
        return createTenantUpdateWrapper(getCurrentTenantId());
    }
    
    // ========================================
    // 租户实体操作
    // ========================================
    
    /**
     * 检查实体是否属于当前租户
     *
     * @param entity 实体对象
     * @return 如果属于当前租户返回true，否则返回false
     */
    public static boolean belongsToCurrentTenant(TenantBaseEntity entity) {
        if (entity == null) {
            return false;
        }
        
        String currentTenantId = getCurrentTenantId();
        return entity.belongsToTenant(currentTenantId);
    }
    
    /**
     * 验证实体租户一致性
     *
     * @param entity 实体对象
     * @return 如果一致返回true，否则返回false
     */
    public static boolean validateTenantConsistency(TenantBaseEntity entity) {
        if (entity == null) {
            return false;
        }
        
        String currentTenantId = getCurrentTenantId();
        return entity.validateTenantConsistency(currentTenantId);
    }
    
    /**
     * 设置实体租户ID为当前租户
     *
     * @param entity 实体对象
     */
    public static void setCurrentTenant(TenantBaseEntity entity) {
        if (entity != null) {
            String currentTenantId = getCurrentTenantId();
            if (currentTenantId != null) {
                entity.setTenantIdInternal(currentTenantId);
            }
        }
    }
    
    /**
     * 批量设置实体租户ID为当前租户
     *
     * @param entities 实体列表
     */
    public static void setCurrentTenant(Collection<? extends TenantBaseEntity> entities) {
        if (DatalayerUtils.isNotEmpty(entities)) {
            String currentTenantId = getCurrentTenantId();
            if (currentTenantId != null) {
                entities.forEach(entity -> {
                    if (entity != null) {
                        entity.setTenantIdInternal(currentTenantId);
                    }
                });
            }
        }
    }
    
    /**
     * 过滤属于当前租户的实体
     *
     * @param entities 实体列表
     * @param <T>      实体类型
     * @return 过滤后的实体列表
     */
    public static <T extends TenantBaseEntity> List<T> filterCurrentTenant(List<T> entities) {
        if (DatalayerUtils.isEmpty(entities)) {
            return List.of();
        }
        
        String currentTenantId = getCurrentTenantId();
        if (currentTenantId == null) {
            return List.of();
        }
        
        return entities.stream()
                .filter(entity -> entity != null && entity.belongsToTenant(currentTenantId))
                .toList();
    }
    
    /**
     * 按租户分组实体
     *
     * @param entities 实体列表
     * @param <T>      实体类型
     * @return 按租户ID分组的Map
     */
    public static <T extends TenantBaseEntity> java.util.Map<String, List<T>> groupByTenant(List<T> entities) {
        if (DatalayerUtils.isEmpty(entities)) {
            return java.util.Map.of();
        }
        
        return entities.stream()
                .filter(entity -> entity != null && entity.hasTenant())
                .collect(java.util.stream.Collectors.groupingBy(TenantBaseEntity::getTenantId));
    }
    
    // ========================================
    // 租户数据处理
    // ========================================
    
    /**
     * 跨租户执行操作
     *
     * @param tenantIds 租户ID列表
     * @param consumer  对每个租户执行的操作
     */
    public static void forEachTenant(Collection<String> tenantIds, Consumer<String> consumer) {
        if (DatalayerUtils.isEmpty(tenantIds) || consumer == null) {
            return;
        }
        
        String originalTenantId = getCurrentTenantId();
        try {
            for (String tenantId : tenantIds) {
                if (tenantId != null && !tenantId.trim().isEmpty()) {
                    TenantContextHolder.setTenantId(tenantId);
                    consumer.accept(tenantId);
                }
            }
        } finally {
            if (originalTenantId != null) {
                TenantContextHolder.setTenantId(originalTenantId);
            } else {
                TenantContextHolder.clearContext();
            }
        }
    }
    
    /**
     * 跨租户执行操作并收集结果
     *
     * @param tenantIds 租户ID列表
     * @param function  对每个租户执行的操作
     * @param <T>       返回类型
     * @return 结果列表
     */
    public static <T> List<T> mapTenants(Collection<String> tenantIds, Function<String, T> function) {
        if (DatalayerUtils.isEmpty(tenantIds) || function == null) {
            return List.of();
        }
        
        String originalTenantId = getCurrentTenantId();
        try {
            return tenantIds.stream()
                    .filter(tenantId -> tenantId != null && !tenantId.trim().isEmpty())
                    .map(tenantId -> {
                        TenantContextHolder.setTenantId(tenantId);
                        return function.apply(tenantId);
                    })
                    .filter(result -> result != null)
                    .toList();
        } finally {
            if (originalTenantId != null) {
                TenantContextHolder.setTenantId(originalTenantId);
            } else {
                TenantContextHolder.clearContext();
            }
        }
    }
    
    // ========================================
    // 租户信息工具
    // ========================================
    
    /**
     * 获取租户信息摘要
     *
     * @return 租户信息摘要
     */
    public static String getTenantSummary() {
        String tenantId = getCurrentTenantId();
        if (tenantId == null || tenantId.trim().isEmpty()) {
            return "No tenant context";
        }
        
        return String.format("Tenant{id='%s', default=%s}", tenantId, "default".equals(tenantId));
    }
    
    /**
     * 获取实体租户信息摘要
     *
     * @param entity 实体对象
     * @return 实体租户信息摘要
     */
    public static String getEntityTenantSummary(TenantBaseEntity entity) {
        if (entity == null) {
            return "Entity is null";
        }
        
        return entity.getTenantSummary();
    }
    
    /**
     * 检查租户ID是否有效
     *
     * @param tenantId 租户ID
     * @return 如果有效返回true，否则返回false
     */
    public static boolean isValidTenantId(String tenantId) {
        return tenantId != null && !tenantId.trim().isEmpty() && !"null".equalsIgnoreCase(tenantId);
    }
    
    /**
     * 标准化租户ID
     *
     * @param tenantId 租户ID
     * @return 标准化后的租户ID
     */
    public static String normalizeTenantId(String tenantId) {
        if (tenantId == null || tenantId.trim().isEmpty() || "null".equalsIgnoreCase(tenantId)) {
            return "default";
        }
        return tenantId.trim().toLowerCase();
    }
}
