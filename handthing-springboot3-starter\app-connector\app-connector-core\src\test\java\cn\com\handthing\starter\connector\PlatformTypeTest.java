package cn.com.handthing.starter.connector;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * PlatformType 单元测试
 *
 * <AUTHOR>
 * @since V1.0.0
 */
class PlatformTypeTest {

    @Test
    void testFromCode() {
        // 测试有效的平台代码
        assertEquals(PlatformType.WECOM, PlatformType.fromCode("wecom"));
        assertEquals(PlatformType.DINGTALK, PlatformType.fromCode("dingtalk"));
        assertEquals(PlatformType.DOUYIN, PlatformType.fromCode("douyin"));
        assertEquals(PlatformType.ALIPAY, PlatformType.fromCode("alipay"));
        
        // 测试大小写不敏感
        assertEquals(PlatformType.WECOM, PlatformType.fromCode("WECOM"));
        assertEquals(PlatformType.WECOM, PlatformType.fromCode("WeCom"));
        
        // 测试空白字符处理
        assertEquals(PlatformType.WECOM, PlatformType.fromCode(" wecom "));
        
        // 测试无效代码
        assertNull(PlatformType.fromCode("invalid"));
        assertNull(PlatformType.fromCode(""));
        assertNull(PlatformType.fromCode(null));
    }

    @Test
    void testIsValidCode() {
        // 测试有效代码
        assertTrue(PlatformType.isValidCode("wecom"));
        assertTrue(PlatformType.isValidCode("dingtalk"));
        assertTrue(PlatformType.isValidCode("DOUYIN"));
        
        // 测试无效代码
        assertFalse(PlatformType.isValidCode("invalid"));
        assertFalse(PlatformType.isValidCode(""));
        assertFalse(PlatformType.isValidCode(null));
    }

    @Test
    void testPlatformProperties() {
        PlatformType wecom = PlatformType.WECOM;
        
        assertEquals("wecom", wecom.getCode());
        assertEquals("企业微信", wecom.getDisplayName());
        assertEquals("WeCom", wecom.getEnglishName());
    }

    @Test
    void testToString() {
        PlatformType wecom = PlatformType.WECOM;
        String expected = "企业微信(wecom)";
        assertEquals(expected, wecom.toString());
    }

    @Test
    void testAllPlatforms() {
        // 确保所有平台都有正确的属性
        for (PlatformType platform : PlatformType.values()) {
            assertNotNull(platform.getCode());
            assertNotNull(platform.getDisplayName());
            assertNotNull(platform.getEnglishName());
            assertFalse(platform.getCode().trim().isEmpty());
            assertFalse(platform.getDisplayName().trim().isEmpty());
            assertFalse(platform.getEnglishName().trim().isEmpty());
        }
    }
}
