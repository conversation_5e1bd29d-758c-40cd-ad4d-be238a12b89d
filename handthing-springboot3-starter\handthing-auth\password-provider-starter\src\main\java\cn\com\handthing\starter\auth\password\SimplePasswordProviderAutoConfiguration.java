package cn.com.handthing.starter.auth.password;

import cn.com.handthing.starter.auth.core.JwtTokenProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;

/**
 * 简单密码提供者自动配置
 * <p>
 * 负责简单密码认证提供者的自动配置。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@AutoConfiguration
@ConditionalOnProperty(prefix = "handthing.auth.password", name = "enabled", havingValue = "true", matchIfMissing = true)
public class SimplePasswordProviderAutoConfiguration {

    /**
     * 简单密码认证提供者
     *
     * @param jwtTokenProvider JWT令牌提供者
     * @return 简单密码认证提供者
     */
    @Bean
    @ConditionalOnMissingBean
    public SimplePasswordAuthenticationProvider simplePasswordAuthenticationProvider(JwtTokenProvider jwtTokenProvider) {
        log.info("Creating SimplePasswordAuthenticationProvider");
        return new SimplePasswordAuthenticationProvider(jwtTokenProvider);
    }
}
