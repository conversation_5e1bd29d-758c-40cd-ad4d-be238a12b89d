D:\code\ai-project\handthing-springboot3-starter\app-connector\wecom-connector\src\main\java\cn\com\handthing\starter\connector\wecom\api\WeComContactApi.java
D:\code\ai-project\handthing-springboot3-starter\app-connector\wecom-connector\src\main\java\cn\com\handthing\starter\connector\wecom\api\WeComCustomerApi.java
D:\code\ai-project\handthing-springboot3-starter\app-connector\wecom-connector\src\main\java\cn\com\handthing\starter\connector\wecom\api\WeComMessageApi.java
D:\code\ai-project\handthing-springboot3-starter\app-connector\wecom-connector\src\main\java\cn\com\handthing\starter\connector\wecom\config\WeComAutoConfiguration.java
D:\code\ai-project\handthing-springboot3-starter\app-connector\wecom-connector\src\main\java\cn\com\handthing\starter\connector\wecom\config\WeComConfig.java
D:\code\ai-project\handthing-springboot3-starter\app-connector\wecom-connector\src\main\java\cn\com\handthing\starter\connector\wecom\model\WeComAccessTokenResponse.java
D:\code\ai-project\handthing-springboot3-starter\app-connector\wecom-connector\src\main\java\cn\com\handthing\starter\connector\wecom\model\WeComDepartment.java
D:\code\ai-project\handthing-springboot3-starter\app-connector\wecom-connector\src\main\java\cn\com\handthing\starter\connector\wecom\model\WeComMessage.java
D:\code\ai-project\handthing-springboot3-starter\app-connector\wecom-connector\src\main\java\cn\com\handthing\starter\connector\wecom\model\WeComUser.java
D:\code\ai-project\handthing-springboot3-starter\app-connector\wecom-connector\src\main\java\cn\com\handthing\starter\connector\wecom\model\WeComUserInfoResponse.java
D:\code\ai-project\handthing-springboot3-starter\app-connector\wecom-connector\src\main\java\cn\com\handthing\starter\connector\wecom\WeComAuthProvider.java
D:\code\ai-project\handthing-springboot3-starter\app-connector\wecom-connector\src\main\java\cn\com\handthing\starter\connector\wecom\WeComService.java
