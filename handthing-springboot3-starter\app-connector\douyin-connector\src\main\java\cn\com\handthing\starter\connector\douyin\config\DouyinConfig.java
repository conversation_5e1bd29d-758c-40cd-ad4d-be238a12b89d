package cn.com.handthing.starter.connector.douyin.config;

import cn.com.handthing.starter.connector.config.PlatformConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import jakarta.validation.constraints.NotBlank;

/**
 * 抖音配置类
 * <p>
 * 继承PlatformConfig，包含抖音特有的配置项，
 * 如客户端Key、客户端密钥等。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ConfigurationProperties(prefix = "handthing.connector.douyin")
public class DouyinConfig extends PlatformConfig {

    /**
     * 客户端Key（Client Key）
     */
    @NotBlank(message = "Douyin client key cannot be blank")
    private String clientKey;

    /**
     * 客户端密钥（Client Secret）
     */
    @NotBlank(message = "Douyin client secret cannot be blank")
    private String clientSecret;

    /**
     * 抖音开放平台API基础URL
     */
    private String apiBaseUrl = "https://open.douyin.com";

    /**
     * 抖音授权URL
     */
    private String authUrl = "https://open.douyin.com/platform/oauth/connect";

    /**
     * 是否为企业号
     */
    private boolean enterprise = false;

    /**
     * 企业号ID（仅企业号需要）
     */
    private String enterpriseId;

    /**
     * 是否启用小程序
     */
    private boolean miniProgram = false;

    /**
     * 小程序AppId（仅小程序需要）
     */
    private String miniProgramAppId;

    /**
     * 小程序密钥（仅小程序需要）
     */
    private String miniProgramSecret;

    /**
     * 事件订阅的Token
     */
    private String webhookToken;

    /**
     * 事件订阅的签名密钥
     */
    private String webhookSecret;

    /**
     * 是否启用事件订阅
     */
    private boolean enableWebhook = false;

    /**
     * 事件订阅回调URL
     */
    private String webhookUrl;

    @Override
    public boolean isValid() {
        // 抖音特有的验证逻辑
        boolean baseValid = isEnabled() && 
                clientKey != null && !clientKey.trim().isEmpty() &&
                clientSecret != null && !clientSecret.trim().isEmpty();

        if (!baseValid) {
            return false;
        }

        // 如果启用企业号，需要验证企业号相关配置
        if (enterprise) {
            return enterpriseId != null && !enterpriseId.trim().isEmpty();
        }

        // 如果启用小程序，需要验证小程序相关配置
        if (miniProgram) {
            return miniProgramAppId != null && !miniProgramAppId.trim().isEmpty() &&
                   miniProgramSecret != null && !miniProgramSecret.trim().isEmpty();
        }

        return true;
    }

    /**
     * 获取有效的API基础URL
     *
     * @return API基础URL
     */
    public String getEffectiveApiBaseUrl() {
        return getEffectiveApiBaseUrl(apiBaseUrl);
    }

    /**
     * 获取有效的授权URL
     *
     * @return 授权URL
     */
    public String getEffectiveAuthUrl() {
        return getEffectiveAuthUrl(authUrl);
    }

    /**
     * 检查是否为企业号模式
     *
     * @return 如果是企业号返回true，否则返回false
     */
    public boolean isEnterpriseMode() {
        return enterprise && enterpriseId != null && !enterpriseId.trim().isEmpty();
    }

    /**
     * 检查是否为小程序模式
     *
     * @return 如果是小程序返回true，否则返回false
     */
    public boolean isMiniProgramMode() {
        return miniProgram && miniProgramAppId != null && !miniProgramAppId.trim().isEmpty();
    }

    /**
     * 检查是否启用事件订阅
     *
     * @return 如果启用事件订阅返回true，否则返回false
     */
    public boolean isWebhookEnabled() {
        return enableWebhook && 
               webhookToken != null && !webhookToken.trim().isEmpty() &&
               webhookSecret != null && !webhookSecret.trim().isEmpty();
    }

    /**
     * 获取Token URL
     *
     * @return Token获取URL
     */
    public String getTokenUrl() {
        return getEffectiveApiBaseUrl() + "/oauth/access_token";
    }

    /**
     * 获取刷新Token URL
     *
     * @return 刷新Token URL
     */
    public String getRefreshTokenUrl() {
        return getEffectiveApiBaseUrl() + "/oauth/refresh_token";
    }

    /**
     * 获取用户信息URL
     *
     * @return 用户信息获取URL
     */
    public String getUserInfoUrl() {
        return getEffectiveApiBaseUrl() + "/oauth/userinfo";
    }

    /**
     * 获取视频管理URL前缀
     *
     * @return 视频管理URL前缀
     */
    public String getVideoUrlPrefix() {
        return getEffectiveApiBaseUrl() + "/video";
    }

    /**
     * 获取数据开放URL前缀
     *
     * @return 数据开放URL前缀
     */
    public String getDataUrlPrefix() {
        return getEffectiveApiBaseUrl() + "/data";
    }

    /**
     * 获取企业号管理URL前缀
     *
     * @return 企业号管理URL前缀
     */
    public String getEnterpriseUrlPrefix() {
        return getEffectiveApiBaseUrl() + "/enterprise";
    }

    /**
     * 获取小程序URL前缀
     *
     * @return 小程序URL前缀
     */
    public String getMiniProgramUrlPrefix() {
        return getEffectiveApiBaseUrl() + "/microapp";
    }

    /**
     * 获取直播相关URL前缀
     *
     * @return 直播URL前缀
     */
    public String getLiveUrlPrefix() {
        return getEffectiveApiBaseUrl() + "/live";
    }

    /**
     * 获取电商相关URL前缀
     *
     * @return 电商URL前缀
     */
    public String getEcommerceUrlPrefix() {
        return getEffectiveApiBaseUrl() + "/goodlife";
    }
}
