package cn.com.handthing.starter.httpclient.internal;

import cn.com.handthing.starter.httpclient.RequestExecutor;
import cn.com.handthing.starter.httpclient.ResponseSpec;
import cn.com.handthing.starter.httpclient.config.EndpointConfig;
import cn.com.handthing.starter.httpclient.crypto.RequestEncryptor;
import cn.com.handthing.starter.httpclient.crypto.ResponseDecryptor;
import cn.com.handthing.starter.httpclient.exception.HandthingHttpException;
import cn.com.handthing.starter.httpclient.interceptor.CryptoInterceptor;
import cn.com.handthing.starter.httpclient.interceptor.LoggingInterceptor;
import cn.com.handthing.starter.httpclient.interceptor.RetryInterceptor;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.util.List;

/**
 * RequestExecutor 接口的 RestTemplate 实现。
 *
 * <AUTHOR>
 * @since V1.0.0
 */
public final class RestTemplateRequestExecutor implements RequestExecutor {

    private final EndpointConfigResolver configResolver;
    private final RequestEncryptor<?> requestEncryptor;
    private final ResponseDecryptor responseDecryptor;
    private final HttpMethod httpMethod;
    private final String uriTemplate;
    private final Object[] uriVariables;

    private final HttpHeaders headers = new HttpHeaders();
    private Object body;

    public RestTemplateRequestExecutor(
            EndpointConfigResolver configResolver,
            RequestEncryptor<?> requestEncryptor,
            ResponseDecryptor responseDecryptor,
            HttpMethod httpMethod,
            String uriTemplate,
            Object... uriVariables) {
        this.configResolver = configResolver;
        this.requestEncryptor = requestEncryptor;
        this.responseDecryptor = responseDecryptor;
        this.httpMethod = httpMethod;
        this.uriTemplate = uriTemplate;
        this.uriVariables = uriVariables;
    }

    @Override
    @NonNull
    public RequestExecutor header(@NonNull String name, @NonNull String value) {
        headers.add(name, value);
        return this;
    }

    @Override
    @NonNull
    public RequestExecutor body(@Nullable Object body) {
        this.body = body;
        return this;
    }

    @Override
    @NonNull
    public ResponseSpec retrieve() {
        try {
            // 构建完整的URI
            URI uri = UriComponentsBuilder.fromUriString(uriTemplate)
                    .buildAndExpand(uriVariables)
                    .toUri();

            // 解析端点配置
            EndpointConfig config = configResolver.resolve(uri.toString());

            // 创建并配置RestTemplate（包含拦截器）
            RestTemplate restTemplate = createConfiguredRestTemplate(config);

            // 执行请求
            HttpEntity<?> requestEntity = new HttpEntity<>(body, headers);
            ResponseEntity<byte[]> responseEntity = restTemplate.exchange(uri, httpMethod, requestEntity, byte[].class);

            return new RestTemplateResponseSpec(responseEntity);
        } catch (Exception e) {
            throw new HandthingHttpException("Failed to execute HTTP request", e);
        }
    }

    /**
     * 创建配置的RestTemplate，包含拦截器
     */
    private RestTemplate createConfiguredRestTemplate(EndpointConfig config) {
        RestTemplate restTemplate = new RestTemplate();

        // 添加拦截器
        List<ClientHttpRequestInterceptor> interceptors = restTemplate.getInterceptors();

        // 添加日志拦截器
        if (config.getLogging() != null && config.getLogging().isEnabled()) {
            interceptors.add(new LoggingInterceptor(config.getLogging()));
        }

        // 添加重试拦截器
        if (config.getRetry() != null && config.getRetry().isEnabled()) {
            interceptors.add(new RetryInterceptor(config.getRetry()));
        }

        // 添加加密拦截器
        if (config.getCrypto() != null && config.getCrypto().isEnabled()) {
            interceptors.add(new CryptoInterceptor(config.getCrypto(), requestEncryptor, responseDecryptor));
        }

        restTemplate.setInterceptors(interceptors);
        return restTemplate;
    }
}
