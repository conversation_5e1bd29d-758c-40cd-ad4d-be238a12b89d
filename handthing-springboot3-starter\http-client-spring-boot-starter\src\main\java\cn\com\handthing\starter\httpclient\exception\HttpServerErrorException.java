package cn.com.handthing.starter.httpclient.exception;

import org.springframework.http.HttpStatus;
import org.springframework.lang.Nullable;

/**
 * 表示收到了 HTTP 5xx 服务端错误响应的异常。
 * <p>
 * 当HTTP响应的状态码在 500-599 范围内时，应抛出此异常。
 * 它封装了状态码和响应体，通常表明远程服务或其依赖项存在问题。
 */
public class HttpServerErrorException extends HttpRequestException {

    private final HttpStatus statusCode;
    private final String responseBody;

    /**
     * 使用指定的HTTP状态码、响应体和原因构造一个新的服务端错误异常。
     *
     * @param message      详细消息。
     * @param statusCode   HTTP状态码 (必须是5xx系列)。
     * @param responseBody 作为字符串的响应体 (可能为null)。
     * @param cause        根本原因 (可能为null)。
     */
    public HttpServerErrorException(String message, HttpStatus statusCode, @Nullable String responseBody, @Nullable Throwable cause) {
        super(message, cause);
        this.statusCode = statusCode;
        this.responseBody = responseBody;
    }

    /**
     * 获取导致此异常的HTTP状态码。
     *
     * @return HTTP状态码。
     */
    public HttpStatus getStatusCode() {
        return statusCode;
    }

    /**
     * 获取作为字符串的响应体。
     *
     * @return 响应体，如果不存在则为null。
     */
    @Nullable
    public String getResponseBody() {
        return responseBody;
    }
}