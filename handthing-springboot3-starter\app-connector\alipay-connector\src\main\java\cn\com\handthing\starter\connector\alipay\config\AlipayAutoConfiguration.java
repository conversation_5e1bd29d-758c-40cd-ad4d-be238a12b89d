package cn.com.handthing.starter.connector.alipay.config;

import cn.com.handthing.starter.connector.alipay.AlipayAuthProvider;
import cn.com.handthing.starter.connector.auth.AuthService;
import cn.com.handthing.starter.connector.auth.DefaultAuthService;
import cn.com.handthing.starter.connector.token.TokenManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.web.client.RestTemplate;

import jakarta.annotation.PostConstruct;

/**
 * 支付宝自动配置类
 * <p>
 * 当支付宝配置启用时，自动装配支付宝相关的Bean，
 * 并注册到统一的认证服务中。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@RequiredArgsConstructor
@AutoConfiguration
@EnableConfigurationProperties(AlipayConfig.class)
@ConditionalOnProperty(prefix = "handthing.connector.alipay", name = "enabled", havingValue = "true")
@ConditionalOnClass(AuthService.class)
@ComponentScan(basePackages = "cn.com.handthing.starter.connector.alipay")
public class AlipayAutoConfiguration {



    private final AuthService authService;
    private final AlipayAuthProvider alipayAuthProvider;
    private final TokenManager tokenManager;
    private final AlipayConfig alipayConfig;



    /**
     * 注册支付宝认证提供者到认证服务
     */
    @PostConstruct
    public void registerAlipayAuthProvider() {
        if (authService instanceof DefaultAuthService) {
            DefaultAuthService defaultAuthService = (DefaultAuthService) authService;
            defaultAuthService.registerAuthProvider(alipayAuthProvider);
            tokenManager.registerAuthProvider(alipayAuthProvider);

            log.info("Registered Alipay auth provider - AppId: {}, SandboxMode: {}, CertMode: {}, Valid: {}",
                    alipayConfig.getAppId(),
                    alipayConfig.isSandboxMode(),
                    alipayConfig.isCertMode(),
                    alipayConfig.isValid());
        } else {
            log.warn("AuthService is not DefaultAuthService, cannot register Alipay auth provider");
        }
    }

    /**
     * 支付宝配置信息Bean
     *
     * @param alipayConfig 支付宝配置
     * @return 配置信息
     */
    @Bean
    public AlipayConfigInfo alipayConfigInfo(AlipayConfig alipayConfig) {
        AlipayConfigInfo configInfo = new AlipayConfigInfo();
        configInfo.setEnabled(alipayConfig.isEnabled());
        configInfo.setValid(alipayConfig.isValid());
        configInfo.setAppId(alipayConfig.getAppId());
        configInfo.setSandboxMode(alipayConfig.isSandboxMode());
        configInfo.setCertMode(alipayConfig.isCertMode());
        configInfo.setNotifyEnabled(alipayConfig.isNotifyEnabled());
        configInfo.setGatewayUrl(alipayConfig.getEffectiveGatewayUrl());
        configInfo.setAuthUrl(alipayConfig.getEffectiveAuthUrl());
        configInfo.setSignType(alipayConfig.getSignType());
        
        log.info("Alipay configuration: enabled={}, valid={}, appId={}, sandboxMode={}, certMode={}, notifyEnabled={}, signType={}", 
                configInfo.isEnabled(), 
                configInfo.isValid(),
                configInfo.getAppId(),
                configInfo.isSandboxMode(),
                configInfo.isCertMode(),
                configInfo.isNotifyEnabled(),
                configInfo.getSignType());
        
        return configInfo;
    }

    /**
     * 支付宝配置信息类
     */
    public static class AlipayConfigInfo {
        private boolean enabled;
        private boolean valid;
        private String appId;
        private boolean sandboxMode;
        private boolean certMode;
        private boolean notifyEnabled;
        private String gatewayUrl;
        private String authUrl;
        private String signType;

        // Getters and Setters
        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public boolean isValid() {
            return valid;
        }

        public void setValid(boolean valid) {
            this.valid = valid;
        }

        public String getAppId() {
            return appId;
        }

        public void setAppId(String appId) {
            this.appId = appId;
        }

        public boolean isSandboxMode() {
            return sandboxMode;
        }

        public void setSandboxMode(boolean sandboxMode) {
            this.sandboxMode = sandboxMode;
        }

        public boolean isCertMode() {
            return certMode;
        }

        public void setCertMode(boolean certMode) {
            this.certMode = certMode;
        }

        public boolean isNotifyEnabled() {
            return notifyEnabled;
        }

        public void setNotifyEnabled(boolean notifyEnabled) {
            this.notifyEnabled = notifyEnabled;
        }

        public String getGatewayUrl() {
            return gatewayUrl;
        }

        public void setGatewayUrl(String gatewayUrl) {
            this.gatewayUrl = gatewayUrl;
        }

        public String getAuthUrl() {
            return authUrl;
        }

        public void setAuthUrl(String authUrl) {
            this.authUrl = authUrl;
        }

        public String getSignType() {
            return signType;
        }

        public void setSignType(String signType) {
            this.signType = signType;
        }

        @Override
        public String toString() {
            return String.format("AlipayConfigInfo{enabled=%s, valid=%s, appId='%s', sandboxMode=%s, certMode=%s, notifyEnabled=%s, gatewayUrl='%s', authUrl='%s', signType='%s'}",
                    enabled, valid, appId, sandboxMode, certMode, notifyEnabled, gatewayUrl, authUrl, signType);
        }
    }
}
