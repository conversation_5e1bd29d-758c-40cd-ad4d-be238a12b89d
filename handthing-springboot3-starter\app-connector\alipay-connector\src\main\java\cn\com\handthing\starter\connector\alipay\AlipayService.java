package cn.com.handthing.starter.connector.alipay;

import cn.com.handthing.starter.connector.alipay.api.AlipayPaymentApi;
import cn.com.handthing.starter.connector.alipay.api.AlipayMiniProgramApi;
import cn.com.handthing.starter.connector.alipay.config.AlipayConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

/**
 * 支付宝服务主入口
 * <p>
 * 聚合各个API服务，提供统一的支付宝服务访问入口。
 * 包含支付API、小程序API等功能模块。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "handthing.connector.alipay", name = "enabled", havingValue = "true")
public class AlipayService {

    private final AlipayConfig config;
    private final AlipayPaymentApi paymentApi;
    private final AlipayMiniProgramApi miniProgramApi;

    /**
     * 获取支付API
     *
     * @return 支付API实例
     */
    public AlipayPaymentApi payment() {
        return paymentApi;
    }

    /**
     * 获取小程序API
     *
     * @return 小程序API实例
     */
    public AlipayMiniProgramApi miniProgram() {
        return miniProgramApi;
    }

    /**
     * 获取支付宝配置
     *
     * @return 配置实例
     */
    public AlipayConfig getConfig() {
        return config;
    }

    /**
     * 检查服务是否可用
     *
     * @return 如果服务可用返回true，否则返回false
     */
    public boolean isAvailable() {
        return config.isEnabled() && config.isValid();
    }

    /**
     * 获取服务状态信息
     *
     * @return 服务状态信息
     */
    public ServiceStatus getStatus() {
        ServiceStatus status = new ServiceStatus();
        status.setEnabled(config.isEnabled());
        status.setValid(config.isValid());
        status.setAppId(config.getAppId());
        status.setSandboxMode(config.isSandboxMode());
        status.setCertMode(config.isCertMode());
        status.setNotifyEnabled(config.isNotifyEnabled());
        status.setGatewayUrl(config.getEffectiveGatewayUrl());
        status.setSignType(config.getSignType());
        return status;
    }

    /**
     * 服务状态信息类
     */
    public static class ServiceStatus {
        private boolean enabled;
        private boolean valid;
        private String appId;
        private boolean sandboxMode;
        private boolean certMode;
        private boolean notifyEnabled;
        private String gatewayUrl;
        private String signType;

        // Getters and Setters
        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public boolean isValid() {
            return valid;
        }

        public void setValid(boolean valid) {
            this.valid = valid;
        }

        public String getAppId() {
            return appId;
        }

        public void setAppId(String appId) {
            this.appId = appId;
        }

        public boolean isSandboxMode() {
            return sandboxMode;
        }

        public void setSandboxMode(boolean sandboxMode) {
            this.sandboxMode = sandboxMode;
        }

        public boolean isCertMode() {
            return certMode;
        }

        public void setCertMode(boolean certMode) {
            this.certMode = certMode;
        }

        public boolean isNotifyEnabled() {
            return notifyEnabled;
        }

        public void setNotifyEnabled(boolean notifyEnabled) {
            this.notifyEnabled = notifyEnabled;
        }

        public String getGatewayUrl() {
            return gatewayUrl;
        }

        public void setGatewayUrl(String gatewayUrl) {
            this.gatewayUrl = gatewayUrl;
        }

        public String getSignType() {
            return signType;
        }

        public void setSignType(String signType) {
            this.signType = signType;
        }

        @Override
        public String toString() {
            return String.format("AlipayServiceStatus{enabled=%s, valid=%s, appId='%s', sandboxMode=%s, certMode=%s, notifyEnabled=%s, gatewayUrl='%s', signType='%s'}",
                    enabled, valid, appId, sandboxMode, certMode, notifyEnabled, gatewayUrl, signType);
        }
    }
}
