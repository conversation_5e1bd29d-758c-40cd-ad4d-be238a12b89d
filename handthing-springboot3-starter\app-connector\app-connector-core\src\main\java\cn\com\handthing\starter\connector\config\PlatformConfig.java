package cn.com.handthing.starter.connector.config;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import java.util.Map;

/**
 * 平台配置基类
 * <p>
 * 定义通用配置字段，如客户端ID、密钥、回调地址等，
 * 支持子类扩展特定配置。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
public abstract class PlatformConfig {

    /**
     * 是否启用该平台
     */
    private boolean enabled = false;

    /**
     * 客户端ID（应用ID）
     */
    @NotBlank(message = "Client ID cannot be blank")
    private String clientId;

    /**
     * 客户端密钥
     */
    @NotBlank(message = "Client secret cannot be blank")
    private String clientSecret;

    /**
     * 回调地址（可选，如果为空则使用全局配置）
     */
    private String redirectUri;

    /**
     * 授权范围
     */
    private String scope;

    /**
     * 是否为沙箱环境
     */
    private boolean sandbox = false;

    /**
     * API基础URL（可选，用于自定义API地址）
     */
    private String apiBaseUrl;

    /**
     * 授权URL（可选，用于自定义授权地址）
     */
    private String authUrl;

    /**
     * 令牌URL（可选，用于自定义令牌交换地址）
     */
    private String tokenUrl;

    /**
     * 用户信息URL（可选，用于自定义用户信息获取地址）
     */
    private String userInfoUrl;

    /**
     * 连接超时时间（毫秒）
     */
    private Integer connectTimeout = 10000;

    /**
     * 读取超时时间（毫秒）
     */
    private Integer readTimeout = 30000;

    /**
     * 最大重试次数
     */
    private Integer maxRetries = 3;

    /**
     * 平台特定的额外配置
     */
    private Map<String, Object> extraConfig;

    /**
     * 检查配置是否有效
     *
     * @return 如果配置有效返回true，否则返回false
     */
    public boolean isValid() {
        return enabled 
                && clientId != null && !clientId.trim().isEmpty()
                && clientSecret != null && !clientSecret.trim().isEmpty();
    }

    /**
     * 获取有效的回调地址
     *
     * @param defaultRedirectUri 默认回调地址
     * @return 有效的回调地址
     */
    public String getEffectiveRedirectUri(String defaultRedirectUri) {
        return redirectUri != null && !redirectUri.trim().isEmpty() ? redirectUri : defaultRedirectUri;
    }

    /**
     * 获取有效的授权范围
     *
     * @param defaultScope 默认授权范围
     * @return 有效的授权范围
     */
    public String getEffectiveScope(String defaultScope) {
        return scope != null && !scope.trim().isEmpty() ? scope : defaultScope;
    }

    /**
     * 获取有效的API基础URL
     *
     * @param defaultApiBaseUrl 默认API基础URL
     * @return 有效的API基础URL
     */
    public String getEffectiveApiBaseUrl(String defaultApiBaseUrl) {
        return apiBaseUrl != null && !apiBaseUrl.trim().isEmpty() ? apiBaseUrl : defaultApiBaseUrl;
    }

    /**
     * 获取有效的授权URL
     *
     * @param defaultAuthUrl 默认授权URL
     * @return 有效的授权URL
     */
    public String getEffectiveAuthUrl(String defaultAuthUrl) {
        return authUrl != null && !authUrl.trim().isEmpty() ? authUrl : defaultAuthUrl;
    }

    /**
     * 获取有效的令牌URL
     *
     * @param defaultTokenUrl 默认令牌URL
     * @return 有效的令牌URL
     */
    public String getEffectiveTokenUrl(String defaultTokenUrl) {
        return tokenUrl != null && !tokenUrl.trim().isEmpty() ? tokenUrl : defaultTokenUrl;
    }

    /**
     * 获取有效的用户信息URL
     *
     * @param defaultUserInfoUrl 默认用户信息URL
     * @return 有效的用户信息URL
     */
    public String getEffectiveUserInfoUrl(String defaultUserInfoUrl) {
        return userInfoUrl != null && !userInfoUrl.trim().isEmpty() ? userInfoUrl : defaultUserInfoUrl;
    }

    /**
     * 获取额外配置
     *
     * @param key 配置键
     * @return 配置值，如果不存在返回null
     */
    public Object getExtraConfig(String key) {
        return extraConfig != null ? extraConfig.get(key) : null;
    }

    /**
     * 获取额外配置（指定类型）
     *
     * @param key  配置键
     * @param type 配置值类型
     * @param <T>  泛型类型
     * @return 配置值，如果不存在或类型不匹配返回null
     */
    @SuppressWarnings("unchecked")
    public <T> T getExtraConfig(String key, Class<T> type) {
        Object value = getExtraConfig(key);
        if (value != null && type.isInstance(value)) {
            return (T) value;
        }
        return null;
    }

    /**
     * 检查是否包含指定的额外配置
     *
     * @param key 配置键
     * @return 如果包含返回true，否则返回false
     */
    public boolean hasExtraConfig(String key) {
        return extraConfig != null && extraConfig.containsKey(key);
    }
}
