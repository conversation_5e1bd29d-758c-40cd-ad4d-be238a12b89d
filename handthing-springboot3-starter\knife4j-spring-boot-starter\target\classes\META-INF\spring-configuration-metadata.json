{"groups": [{"name": "handthing.api-doc", "type": "cn.com.handthing.starter.knife4j.properties.Knife4jProperties", "sourceType": "cn.com.handthing.starter.knife4j.properties.Knife4jProperties"}, {"name": "handthing.api-doc.auth", "type": "cn.com.handthing.starter.apidoc.properties.ApiDocProperties$Auth", "sourceType": "cn.com.handthing.starter.knife4j.properties.Knife4jProperties", "sourceMethod": "public cn.com.handthing.starter.apidoc.properties.ApiDocProperties.Auth getAuth() "}, {"name": "handthing.api-doc.knife4j", "type": "cn.com.handthing.starter.knife4j.properties.Knife4jProperties$Knife4j", "sourceType": "cn.com.handthing.starter.knife4j.properties.Knife4jProperties", "sourceMethod": "public cn.com.handthing.starter.knife4j.properties.Knife4jProperties.Knife4j getKnife4j() "}, {"name": "handthing.api-doc.knife4j.contact", "type": "cn.com.handthing.starter.knife4j.properties.Knife4jProperties$Knife4jContact", "sourceType": "cn.com.handthing.starter.knife4j.properties.Knife4jProperties$Knife4j", "sourceMethod": "public cn.com.handthing.starter.knife4j.properties.Knife4jProperties.Knife4jContact getContact() "}, {"name": "handthing.api-doc.knife4j.enhancement", "type": "cn.com.handthing.starter.knife4j.properties.Knife4jProperties$Enhancement", "sourceType": "cn.com.handthing.starter.knife4j.properties.Knife4jProperties$Knife4j", "sourceMethod": "public cn.com.handthing.starter.knife4j.properties.Knife4jProperties.Enhancement getEnhancement() "}, {"name": "handthing.api-doc.knife4j.ui", "type": "cn.com.handthing.starter.knife4j.properties.Knife4jProperties$Ui", "sourceType": "cn.com.handthing.starter.knife4j.properties.Knife4jProperties$Knife4j", "sourceMethod": "public cn.com.handthing.starter.knife4j.properties.Knife4jProperties.Ui getUi() "}], "properties": [{"name": "handthing.api-doc.auth.enabled", "type": "java.lang.Bo<PERSON>an", "sourceType": "cn.com.handthing.starter.apidoc.properties.ApiDocProperties$Auth"}, {"name": "handthing.api-doc.auth.password", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.apidoc.properties.ApiDocProperties$Auth"}, {"name": "handthing.api-doc.auth.username", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.apidoc.properties.ApiDocProperties$Auth"}, {"name": "handthing.api-doc.description", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.knife4j.properties.Knife4jProperties"}, {"name": "handthing.api-doc.enabled", "type": "java.lang.Bo<PERSON>an", "sourceType": "cn.com.handthing.starter.knife4j.properties.Knife4jProperties"}, {"name": "handthing.api-doc.group", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.knife4j.properties.Knife4jProperties"}, {"name": "handthing.api-doc.knife4j.contact.email", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.knife4j.properties.Knife4jProperties$Knife4jContact"}, {"name": "handthing.api-doc.knife4j.contact.license-name", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.knife4j.properties.Knife4jProperties$Knife4jContact"}, {"name": "handthing.api-doc.knife4j.contact.license-url", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.knife4j.properties.Knife4jProperties$Knife4jContact"}, {"name": "handthing.api-doc.knife4j.contact.name", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.knife4j.properties.Knife4jProperties$Knife4jContact"}, {"name": "handthing.api-doc.knife4j.contact.url", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.knife4j.properties.Knife4jProperties$Knife4jContact"}, {"name": "handthing.api-doc.knife4j.enhancement.enable-filter-multipart-apis", "type": "java.lang.Bo<PERSON>an", "description": "是否过滤 multipart 接口", "sourceType": "cn.com.handthing.starter.knife4j.properties.Knife4jProperties$Enhancement"}, {"name": "handthing.api-doc.knife4j.enhancement.enable-host", "type": "java.lang.Bo<PERSON>an", "description": "是否启用 Host 显示", "sourceType": "cn.com.handthing.starter.knife4j.properties.Knife4jProperties$Enhancement"}, {"name": "handthing.api-doc.knife4j.enhancement.enable-request-cache", "type": "java.lang.Bo<PERSON>an", "description": "是否启用请求缓存", "sourceType": "cn.com.handthing.starter.knife4j.properties.Knife4jProperties$Enhancement"}, {"name": "handthing.api-doc.knife4j.enhancement.enable-sort", "type": "java.lang.Bo<PERSON>an", "description": "是否启用接口排序", "sourceType": "cn.com.handthing.starter.knife4j.properties.Knife4jProperties$Enhancement"}, {"name": "handthing.api-doc.knife4j.enhancement.production-security-enabled", "type": "java.lang.Bo<PERSON>an", "description": "生产环境保护", "sourceType": "cn.com.handthing.starter.knife4j.properties.Knife4jProperties$Enhancement"}, {"name": "handthing.api-doc.knife4j.ui.enable-debug", "type": "java.lang.Bo<PERSON>an", "description": "是否启用在线调试", "sourceType": "cn.com.handthing.starter.knife4j.properties.Knife4jProperties$Ui"}, {"name": "handthing.api-doc.knife4j.ui.enable-dynamic-parameter", "type": "java.lang.Bo<PERSON>an", "description": "是否启用动态参数", "sourceType": "cn.com.handthing.starter.knife4j.properties.Knife4jProperties$Ui"}, {"name": "handthing.api-doc.knife4j.ui.enable-export", "type": "java.lang.Bo<PERSON>an", "description": "是否启用文档导出", "sourceType": "cn.com.handthing.starter.knife4j.properties.Knife4jProperties$Ui"}, {"name": "handthing.api-doc.knife4j.ui.enable-group", "type": "java.lang.Bo<PERSON>an", "description": "是否启用接口分组", "sourceType": "cn.com.handthing.starter.knife4j.properties.Knife4jProperties$Ui"}, {"name": "handthing.api-doc.knife4j.ui.enable-open-api", "type": "java.lang.Bo<PERSON>an", "description": "是否显示 OpenAPI 规范", "sourceType": "cn.com.handthing.starter.knife4j.properties.Knife4jProperties$Ui"}, {"name": "handthing.api-doc.knife4j.ui.enable-search", "type": "java.lang.Bo<PERSON>an", "description": "是否启用搜索功能", "sourceType": "cn.com.handthing.starter.knife4j.properties.Knife4jProperties$Ui"}, {"name": "handthing.api-doc.knife4j.ui.theme", "type": "java.lang.String", "description": "主题：default, dark", "sourceType": "cn.com.handthing.starter.knife4j.properties.Knife4jProperties$Ui"}, {"name": "handthing.api-doc.title", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.knife4j.properties.Knife4jProperties"}, {"name": "handthing.api-doc.version", "type": "java.lang.String", "sourceType": "cn.com.handthing.starter.knife4j.properties.Knife4jProperties"}], "hints": [], "ignored": {"properties": []}}