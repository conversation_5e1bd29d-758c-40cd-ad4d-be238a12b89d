package cn.com.handthing.starter.datalayer.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 数据层配置属性
 * <p>
 * 提供数据层相关的配置选项，包括逻辑删除、分页、乐观锁等基础功能配置。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
@ConfigurationProperties(prefix = "handthing.datalayer")
public class DatalayerProperties {
    
    /**
     * 是否启用数据层功能
     */
    private boolean enabled = true;
    
    /**
     * 逻辑删除配置
     */
    private LogicDelete logicDelete = new LogicDelete();
    
    /**
     * 分页配置
     */
    private Pagination pagination = new Pagination();
    
    /**
     * 乐观锁配置
     */
    private OptimisticLock optimisticLock = new OptimisticLock();
    
    /**
     * 自动填充配置
     */
    private AutoFill autoFill = new AutoFill();
    
    /**
     * 逻辑删除配置
     */
    @Data
    public static class LogicDelete {
        
        /**
         * 是否启用逻辑删除
         */
        private boolean enabled = true;
        
        /**
         * 逻辑删除字段名
         */
        private String fieldName = "deleted";
        
        /**
         * 逻辑删除值（已删除）
         */
        private String deletedValue = "1";
        
        /**
         * 逻辑未删除值（未删除）
         */
        private String notDeletedValue = "0";
    }
    
    /**
     * 分页配置
     */
    @Data
    public static class Pagination {
        
        /**
         * 是否启用分页
         */
        private boolean enabled = true;
        
        /**
         * 默认页大小
         */
        private long defaultPageSize = 10L;
        
        /**
         * 最大页大小
         */
        private long maxPageSize = 1000L;
        
        /**
         * 是否启用合理化分页（页码小于1时查询第一页，页码大于总页数时查询最后一页）
         */
        private boolean reasonable = true;
        
        /**
         * 是否支持接口参数来传递 page-size 和 page-num
         */
        private boolean supportMethodsArguments = true;
    }
    
    /**
     * 乐观锁配置
     */
    @Data
    public static class OptimisticLock {
        
        /**
         * 是否启用乐观锁
         */
        private boolean enabled = true;
        
        /**
         * 版本字段名
         */
        private String fieldName = "version";
    }
    
    /**
     * 自动填充配置
     */
    @Data
    public static class AutoFill {
        
        /**
         * 是否启用自动填充
         */
        private boolean enabled = true;
        
        /**
         * 是否启用@IdSetter注解处理
         */
        private boolean idSetterEnabled = true;
        
        /**
         * 是否启用审计字段自动填充
         */
        private boolean auditEnabled = true;
        
        /**
         * 是否启用时间字段自动填充
         */
        private boolean timeEnabled = true;
        
        /**
         * 创建人字段名
         */
        private String createByField = "createBy";
        
        /**
         * 更新人字段名
         */
        private String updateByField = "updateBy";
        
        /**
         * 创建时间字段名
         */
        private String createTimeField = "createTime";
        
        /**
         * 更新时间字段名
         */
        private String updateTimeField = "updateTime";
        
        /**
         * 版本字段名
         */
        private String versionField = "version";
        
        /**
         * 逻辑删除字段名
         */
        private String deletedField = "deleted";
    }
    
    /**
     * 验证配置的有效性
     */
    public void validate() {
        if (pagination.enabled) {
            validatePaginationConfig();
        }
        
        if (autoFill.enabled) {
            validateAutoFillConfig();
        }
    }
    
    /**
     * 验证分页配置
     */
    private void validatePaginationConfig() {
        if (pagination.defaultPageSize <= 0) {
            throw new IllegalArgumentException("Default page size must be positive");
        }
        
        if (pagination.maxPageSize <= 0) {
            throw new IllegalArgumentException("Max page size must be positive");
        }
        
        if (pagination.defaultPageSize > pagination.maxPageSize) {
            throw new IllegalArgumentException("Default page size cannot be greater than max page size");
        }
    }
    
    /**
     * 验证自动填充配置
     */
    private void validateAutoFillConfig() {
        if (autoFill.createByField == null || autoFill.createByField.trim().isEmpty()) {
            throw new IllegalArgumentException("Create by field name cannot be null or empty");
        }
        
        if (autoFill.updateByField == null || autoFill.updateByField.trim().isEmpty()) {
            throw new IllegalArgumentException("Update by field name cannot be null or empty");
        }
        
        if (autoFill.createTimeField == null || autoFill.createTimeField.trim().isEmpty()) {
            throw new IllegalArgumentException("Create time field name cannot be null or empty");
        }
        
        if (autoFill.updateTimeField == null || autoFill.updateTimeField.trim().isEmpty()) {
            throw new IllegalArgumentException("Update time field name cannot be null or empty");
        }
    }
    
    /**
     * 获取配置摘要
     *
     * @return 配置摘要字符串
     */
    public String getConfigSummary() {
        StringBuilder sb = new StringBuilder();
        sb.append("DatalayerProperties{");
        sb.append("enabled=").append(enabled);
        
        if (logicDelete.enabled) {
            sb.append(", logicDelete={enabled=").append(logicDelete.enabled);
            sb.append(", field=").append(logicDelete.fieldName);
            sb.append(", deletedValue=").append(logicDelete.deletedValue);
            sb.append(", notDeletedValue=").append(logicDelete.notDeletedValue);
            sb.append("}");
        }
        
        if (pagination.enabled) {
            sb.append(", pagination={enabled=").append(pagination.enabled);
            sb.append(", defaultSize=").append(pagination.defaultPageSize);
            sb.append(", maxSize=").append(pagination.maxPageSize);
            sb.append(", reasonable=").append(pagination.reasonable);
            sb.append("}");
        }
        
        if (optimisticLock.enabled) {
            sb.append(", optimisticLock={enabled=").append(optimisticLock.enabled);
            sb.append(", field=").append(optimisticLock.fieldName);
            sb.append("}");
        }
        
        if (autoFill.enabled) {
            sb.append(", autoFill={enabled=").append(autoFill.enabled);
            sb.append(", idSetter=").append(autoFill.idSetterEnabled);
            sb.append(", audit=").append(autoFill.auditEnabled);
            sb.append(", time=").append(autoFill.timeEnabled);
            sb.append("}");
        }
        
        sb.append("}");
        return sb.toString();
    }
}
