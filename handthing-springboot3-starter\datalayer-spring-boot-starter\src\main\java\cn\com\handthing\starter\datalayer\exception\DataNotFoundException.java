package cn.com.handthing.starter.datalayer.exception;

/**
 * 数据不存在异常
 * <p>
 * 当查询的数据不存在时抛出此异常。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
public class DataNotFoundException extends DatalayerException {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 实体类型
     */
    private final String entityType;
    
    /**
     * 查询条件
     */
    private final String queryCondition;
    
    /**
     * 构造函数
     *
     * @param message 错误信息
     */
    public DataNotFoundException(String message) {
        super("DATA_NOT_FOUND", "QUERY", message);
        this.entityType = null;
        this.queryCondition = null;
    }
    
    /**
     * 构造函数
     *
     * @param entityType 实体类型
     * @param id         主键ID
     */
    public DataNotFoundException(String entityType, Object id) {
        super("DATA_NOT_FOUND", "QUERY", 
                String.format("%s with id '%s' not found", entityType, id));
        this.entityType = entityType;
        this.queryCondition = "id=" + id;
    }
    
    /**
     * 构造函数
     *
     * @param entityType     实体类型
     * @param queryCondition 查询条件
     * @param message        错误信息
     */
    public DataNotFoundException(String entityType, String queryCondition, String message) {
        super("DATA_NOT_FOUND", "QUERY", message);
        this.entityType = entityType;
        this.queryCondition = queryCondition;
    }
    
    /**
     * 构造函数
     *
     * @param entityType     实体类型
     * @param queryCondition 查询条件
     * @param message        错误信息
     * @param cause          原因异常
     */
    public DataNotFoundException(String entityType, String queryCondition, String message, Throwable cause) {
        super("DATA_NOT_FOUND", "QUERY", message, cause);
        this.entityType = entityType;
        this.queryCondition = queryCondition;
    }
    
    /**
     * 获取实体类型
     *
     * @return 实体类型
     */
    public String getEntityType() {
        return entityType;
    }
    
    /**
     * 获取查询条件
     *
     * @return 查询条件
     */
    public String getQueryCondition() {
        return queryCondition;
    }
    
    @Override
    public String getDetailMessage() {
        StringBuilder sb = new StringBuilder();
        sb.append("Data not found");
        
        if (entityType != null) {
            sb.append(" for entity: ").append(entityType);
        }
        
        if (queryCondition != null) {
            sb.append(" with condition: ").append(queryCondition);
        }
        
        sb.append(", message: ").append(getMessage());
        
        return sb.toString();
    }
    
    /**
     * 创建按ID查询不存在的异常
     *
     * @param entityClass 实体类
     * @param id          主键ID
     * @return 异常实例
     */
    public static DataNotFoundException byId(Class<?> entityClass, Object id) {
        return new DataNotFoundException(entityClass.getSimpleName(), id);
    }
    
    /**
     * 创建按条件查询不存在的异常
     *
     * @param entityClass 实体类
     * @param condition   查询条件描述
     * @return 异常实例
     */
    public static DataNotFoundException byCondition(Class<?> entityClass, String condition) {
        return new DataNotFoundException(entityClass.getSimpleName(), condition, 
                String.format("%s not found with condition: %s", entityClass.getSimpleName(), condition));
    }
}
