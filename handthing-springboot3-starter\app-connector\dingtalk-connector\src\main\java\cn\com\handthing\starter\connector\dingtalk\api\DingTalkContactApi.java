package cn.com.handthing.starter.connector.dingtalk.api;

import cn.com.handthing.starter.connector.dingtalk.config.DingTalkConfig;
import cn.com.handthing.starter.connector.dingtalk.model.DingTalkUser;
import cn.com.handthing.starter.connector.exception.ApiCallException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.List;

/**
 * 钉钉通讯录API
 * <p>
 * 提供钉钉通讯录管理功能，包括获取部门列表、用户列表等功能。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "handthing.connector.dingtalk", name = "enabled", havingValue = "true")
public class DingTalkContactApi {

    private final DingTalkConfig config;
    private final RestTemplate restTemplate;

    /**
     * 获取部门列表
     *
     * @param accessToken 访问令牌
     * @param deptId      部门ID（可选，不填则获取根部门）
     * @return 部门列表
     * @throws ApiCallException 如果获取失败
     */
    public Object getDepartmentList(String accessToken, Long deptId) throws ApiCallException {
        // TODO: 实现获取部门列表
        log.debug("Getting DingTalk department list for deptId: {}", deptId);
        throw new UnsupportedOperationException("getDepartmentList not implemented yet");
    }

    /**
     * 获取部门用户列表
     *
     * @param accessToken 访问令牌
     * @param deptId      部门ID
     * @return 用户列表
     * @throws ApiCallException 如果获取失败
     */
    public List<DingTalkUser> getDepartmentUsers(String accessToken, Long deptId) throws ApiCallException {
        // TODO: 实现获取部门用户列表
        log.debug("Getting DingTalk department users for deptId: {}", deptId);
        throw new UnsupportedOperationException("getDepartmentUsers not implemented yet");
    }

    /**
     * 获取用户详细信息
     *
     * @param accessToken 访问令牌
     * @param userId      用户ID
     * @return 用户信息
     * @throws ApiCallException 如果获取失败
     */
    public DingTalkUser getUserDetail(String accessToken, String userId) throws ApiCallException {
        // TODO: 实现获取用户详细信息
        log.debug("Getting DingTalk user detail for userId: {}", userId);
        throw new UnsupportedOperationException("getUserDetail not implemented yet");
    }
}
