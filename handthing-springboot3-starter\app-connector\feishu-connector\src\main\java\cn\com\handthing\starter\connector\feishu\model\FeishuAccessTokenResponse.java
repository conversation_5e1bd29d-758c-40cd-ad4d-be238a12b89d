package cn.com.handthing.starter.connector.feishu.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 飞书访问令牌响应
 * <p>
 * 飞书OAuth2.0认证获取访问令牌的响应数据模型。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Data
public class FeishuAccessTokenResponse {

    /**
     * 错误码，0表示成功
     */
    @JsonProperty("code")
    private Integer code;

    /**
     * 错误信息
     */
    @JsonProperty("msg")
    private String msg;

    /**
     * 响应数据
     */
    @JsonProperty("data")
    private TokenData data;

    /**
     * 检查响应是否成功
     *
     * @return 如果成功返回true，否则返回false
     */
    public boolean isSuccess() {
        return code != null && code == 0;
    }

    /**
     * 获取错误描述
     *
     * @return 错误描述
     */
    public String getErrorDescription() {
        if (isSuccess()) {
            return null;
        }
        return String.format("Error %d: %s", code, msg);
    }

    /**
     * Token数据
     */
    @Data
    public static class TokenData {

        /**
         * 访问令牌
         */
        @JsonProperty("access_token")
        private String accessToken;

        /**
         * 访问令牌有效期，单位秒
         */
        @JsonProperty("expires_in")
        private Long expiresIn;

        /**
         * 刷新令牌
         */
        @JsonProperty("refresh_token")
        private String refreshToken;

        /**
         * 刷新令牌有效期，单位秒
         */
        @JsonProperty("refresh_expires_in")
        private Long refreshExpiresIn;

        /**
         * 用户在应用内的唯一标识
         */
        @JsonProperty("open_id")
        private String openId;

        /**
         * 用户统一标识，在同一个开发者账号下的所有应用中，同一用户的union_id相同
         */
        @JsonProperty("union_id")
        private String unionId;

        /**
         * 用户所在企业标识
         */
        @JsonProperty("tenant_key")
        private String tenantKey;

        /**
         * Token类型，通常为Bearer
         */
        @JsonProperty("token_type")
        private String tokenType;

        /**
         * 授权范围
         */
        @JsonProperty("scope")
        private String scope;
    }
}
