package cn.com.handthing.starter.auth;

import cn.com.handthing.starter.auth.core.AuthenticationContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * 认证总线
 * <p>
 * 实现"认证总线"设计理念的核心组件，负责认证事件的发布和传播。
 * 提供认证过程中各个阶段的事件通知机制，支持扩展和监控。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AuthenticationBus {

    private final ApplicationEventPublisher eventPublisher;
    private final AuthProperties authProperties;

    /**
     * 发布认证开始事件
     *
     * @param context 认证上下文
     */
    public void publishAuthenticationStartedEvent(AuthenticationContext context) {
        if (authProperties.isEventsEnabled()) {
            AuthenticationStartedEvent event = new AuthenticationStartedEvent(this, context);
            eventPublisher.publishEvent(event);
            log.debug("Published authentication started event: {}", context.getDescription());
        }
    }

    /**
     * 发布认证成功事件
     *
     * @param context 认证上下文
     */
    public void publishAuthenticationSuccessEvent(AuthenticationContext context) {
        if (authProperties.isEventsEnabled()) {
            AuthenticationSuccessEvent event = new AuthenticationSuccessEvent(this, context);
            eventPublisher.publishEvent(event);
            log.debug("Published authentication success event: {}", context.getDescription());
        }
    }

    /**
     * 发布认证失败事件
     *
     * @param context 认证上下文
     */
    public void publishAuthenticationFailedEvent(AuthenticationContext context) {
        if (authProperties.isEventsEnabled()) {
            AuthenticationFailedEvent event = new AuthenticationFailedEvent(this, context);
            eventPublisher.publishEvent(event);
            log.debug("Published authentication failed event: {}", context.getDescription());
        }
    }

    /**
     * 发布认证错误事件
     *
     * @param context 认证上下文
     */
    public void publishAuthenticationErrorEvent(AuthenticationContext context) {
        if (authProperties.isEventsEnabled()) {
            AuthenticationErrorEvent event = new AuthenticationErrorEvent(this, context);
            eventPublisher.publishEvent(event);
            log.debug("Published authentication error event: {}", context.getDescription());
        }
    }

    /**
     * 发布认证完成事件
     *
     * @param context 认证上下文
     */
    public void publishAuthenticationCompletedEvent(AuthenticationContext context) {
        if (authProperties.isEventsEnabled()) {
            AuthenticationCompletedEvent event = new AuthenticationCompletedEvent(this, context);
            eventPublisher.publishEvent(event);
            log.debug("Published authentication completed event: {}", context.getDescription());
        }
    }

    /**
     * 发布令牌刷新事件
     *
     * @param context 认证上下文
     */
    public void publishTokenRefreshedEvent(AuthenticationContext context) {
        if (authProperties.isEventsEnabled()) {
            TokenRefreshedEvent event = new TokenRefreshedEvent(this, context);
            eventPublisher.publishEvent(event);
            log.debug("Published token refreshed event: {}", context.getDescription());
        }
    }

    /**
     * 发布用户登出事件
     *
     * @param context 认证上下文
     */
    public void publishUserLogoutEvent(AuthenticationContext context) {
        if (authProperties.isEventsEnabled()) {
            UserLogoutEvent event = new UserLogoutEvent(this, context);
            eventPublisher.publishEvent(event);
            log.debug("Published user logout event: {}", context.getDescription());
        }
    }

    /**
     * 认证开始事件
     */
    public static class AuthenticationStartedEvent extends AuthenticationEvent {
        public AuthenticationStartedEvent(Object source, AuthenticationContext context) {
            super(source, context);
        }
    }

    /**
     * 认证成功事件
     */
    public static class AuthenticationSuccessEvent extends AuthenticationEvent {
        public AuthenticationSuccessEvent(Object source, AuthenticationContext context) {
            super(source, context);
        }
    }

    /**
     * 认证失败事件
     */
    public static class AuthenticationFailedEvent extends AuthenticationEvent {
        public AuthenticationFailedEvent(Object source, AuthenticationContext context) {
            super(source, context);
        }
    }

    /**
     * 认证错误事件
     */
    public static class AuthenticationErrorEvent extends AuthenticationEvent {
        public AuthenticationErrorEvent(Object source, AuthenticationContext context) {
            super(source, context);
        }
    }

    /**
     * 认证完成事件
     */
    public static class AuthenticationCompletedEvent extends AuthenticationEvent {
        public AuthenticationCompletedEvent(Object source, AuthenticationContext context) {
            super(source, context);
        }
    }

    /**
     * 令牌刷新事件
     */
    public static class TokenRefreshedEvent extends AuthenticationEvent {
        public TokenRefreshedEvent(Object source, AuthenticationContext context) {
            super(source, context);
        }
    }

    /**
     * 用户登出事件
     */
    public static class UserLogoutEvent extends AuthenticationEvent {
        public UserLogoutEvent(Object source, AuthenticationContext context) {
            super(source, context);
        }
    }

    /**
     * 认证事件基类
     */
    public static abstract class AuthenticationEvent extends org.springframework.context.ApplicationEvent {
        
        private final AuthenticationContext context;

        public AuthenticationEvent(Object source, AuthenticationContext context) {
            super(source);
            this.context = context;
        }

        public AuthenticationContext getContext() {
            return context;
        }

        public String getEventType() {
            return this.getClass().getSimpleName();
        }

        @Override
        public String toString() {
            return String.format("%s{context=%s}", getEventType(), context.getDescription());
        }
    }
}
