<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>cn.com.handthing.springboot3.starter</groupId>
        <artifactId>handthing-auth</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>tenant-auth-starter</artifactId>
    <name>HandThing :: Tenant Auth Starter</name>
    <description>HandThing多租户认证启动器 - V3.0 SaaS增强版</description>

    <dependencies>
        <!-- HandThing Core -->
        <dependency>
            <groupId>cn.com.handthing.springboot3.starter</groupId>
            <artifactId>handthing-core</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- Auth Core -->
        <dependency>
            <groupId>cn.com.handthing.springboot3.starter</groupId>
            <artifactId>auth-core</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- Auth Spring Boot Starter -->
        <dependency>
            <groupId>cn.com.handthing.springboot3.starter</groupId>
            <artifactId>auth-spring-boot-starter</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- Cache Spring Boot Starter -->
        <dependency>
            <groupId>cn.com.handthing.springboot3.starter</groupId>
            <artifactId>cache-spring-boot-starter</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- Spring Boot Starter -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>

        <!-- Spring Boot Starter Web -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!-- Spring Boot Starter Data JPA -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>

        <!-- Spring Boot Starter Validation -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <!-- Spring Boot Configuration Processor -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- Test Dependencies -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
