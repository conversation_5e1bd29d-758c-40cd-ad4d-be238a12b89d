package cn.com.handthing.starter.auth.core;

/**
 * 认证失败异常
 * <p>
 * 当认证过程失败时抛出此异常。
 * 这是一个通用的认证失败异常，可以用于各种认证失败的情况。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
public class AuthenticationFailedException extends AuthenticationException {

    /**
     * 失败原因
     */
    private final FailureReason reason;

    /**
     * 构造函数
     *
     * @param message 错误消息
     */
    public AuthenticationFailedException(String message) {
        super("AUTHENTICATION_FAILED", ErrorType.AUTHENTICATION_FAILED, message);
        this.reason = FailureReason.UNKNOWN;
    }

    /**
     * 构造函数
     *
     * @param reason  失败原因
     * @param message 错误消息
     */
    public AuthenticationFailedException(FailureReason reason, String message) {
        super("AUTHENTICATION_FAILED", ErrorType.AUTHENTICATION_FAILED, message);
        this.reason = reason;
    }

    /**
     * 构造函数
     *
     * @param reason  失败原因
     * @param message 错误消息
     * @param cause   原因异常
     */
    public AuthenticationFailedException(FailureReason reason, String message, Throwable cause) {
        super("AUTHENTICATION_FAILED", ErrorType.AUTHENTICATION_FAILED, message, cause);
        this.reason = reason;
    }

    /**
     * 获取失败原因
     *
     * @return 失败原因
     */
    public FailureReason getReason() {
        return reason;
    }

    /**
     * 创建用户不存在异常
     *
     * @param identifier 用户标识
     * @return 认证失败异常
     */
    public static AuthenticationFailedException userNotFound(String identifier) {
        return new AuthenticationFailedException(FailureReason.USER_NOT_FOUND, 
                String.format("用户不存在: %s", identifier));
    }

    /**
     * 创建用户被锁定异常
     *
     * @param identifier 用户标识
     * @return 认证失败异常
     */
    public static AuthenticationFailedException userLocked(String identifier) {
        return new AuthenticationFailedException(FailureReason.USER_LOCKED, 
                String.format("用户已被锁定: %s", identifier));
    }

    /**
     * 创建用户被禁用异常
     *
     * @param identifier 用户标识
     * @return 认证失败异常
     */
    public static AuthenticationFailedException userDisabled(String identifier) {
        return new AuthenticationFailedException(FailureReason.USER_DISABLED, 
                String.format("用户已被禁用: %s", identifier));
    }

    /**
     * 创建第三方认证失败异常
     *
     * @param platform 第三方平台
     * @param reason   失败原因
     * @return 认证失败异常
     */
    public static AuthenticationFailedException thirdPartyAuthFailed(String platform, String reason) {
        return new AuthenticationFailedException(FailureReason.THIRD_PARTY_ERROR, 
                String.format("%s认证失败: %s", platform, reason));
    }

    /**
     * 失败原因枚举
     */
    public enum FailureReason {
        /**
         * 未知原因
         */
        UNKNOWN("unknown", "未知原因"),

        /**
         * 用户不存在
         */
        USER_NOT_FOUND("user_not_found", "用户不存在"),

        /**
         * 用户被锁定
         */
        USER_LOCKED("user_locked", "用户被锁定"),

        /**
         * 用户被禁用
         */
        USER_DISABLED("user_disabled", "用户被禁用"),

        /**
         * 密码错误
         */
        INVALID_PASSWORD("invalid_password", "密码错误"),

        /**
         * 验证码错误
         */
        INVALID_CODE("invalid_code", "验证码错误"),

        /**
         * 第三方认证错误
         */
        THIRD_PARTY_ERROR("third_party_error", "第三方认证错误"),

        /**
         * 网络错误
         */
        NETWORK_ERROR("network_error", "网络错误"),

        /**
         * 系统错误
         */
        SYSTEM_ERROR("system_error", "系统错误");

        private final String code;
        private final String description;

        FailureReason(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }

    @Override
    public String toString() {
        return String.format("AuthenticationFailedException{reason=%s, message='%s'}", reason, getMessage());
    }
}
