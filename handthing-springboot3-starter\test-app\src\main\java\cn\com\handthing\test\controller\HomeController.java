package cn.com.handthing.test.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 主页控制器
 * <p>
 * 提供测试应用的主页和基础信息接口。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Controller
public class HomeController {

    /**
     * 主页
     *
     * @param model 模型
     * @return 主页视图
     */
    @GetMapping("/")
    public String home(Model model) {
        model.addAttribute("title", "HandThing Test Application");
        model.addAttribute("description", "App Connector 综合测试应用");
        model.addAttribute("timestamp", LocalDateTime.now());
        return "index";
    }

    /**
     * 健康检查接口
     *
     * @return 健康状态
     */
    @GetMapping("/health")
    @ResponseBody
    public Map<String, Object> health() {
        Map<String, Object> result = new HashMap<>();
        result.put("status", "UP");
        result.put("timestamp", System.currentTimeMillis());
        result.put("application", "handthing-test-app");
        result.put("version", "1.0.0");
        return result;
    }

    /**
     * 应用信息接口
     *
     * @return 应用信息
     */
    @GetMapping("/info")
    @ResponseBody
    public Map<String, Object> info() {
        Map<String, Object> result = new HashMap<>();
        
        // 应用基础信息
        Map<String, Object> app = new HashMap<>();
        app.put("name", "HandThing Test Application");
        app.put("description", "App Connector 综合测试应用");
        app.put("version", "1.0.0");
        app.put("author", "HandThing");
        result.put("app", app);
        
        // 功能模块信息
        Map<String, Object> features = new HashMap<>();
        features.put("appConnector", "App Connector 多平台连接器");
        features.put("cache", "Redis 缓存服务");
        features.put("distributedLog", "分布式日志");
        features.put("httpClient", "HTTP 客户端");
        result.put("features", features);
        
        // 支持的平台
        Map<String, Object> platforms = new HashMap<>();
        platforms.put("wecom", "企业微信");
        platforms.put("dingtalk", "钉钉");
        platforms.put("douyin", "抖音");
        platforms.put("alipay", "支付宝");
        result.put("supportedPlatforms", platforms);
        
        // 测试接口
        Map<String, Object> testApis = new HashMap<>();
        testApis.put("appConnector", "/test/app-connector");
        testApis.put("platformServices", "/test/platform-services");
        testApis.put("cache", "/test/cache");
        result.put("testApis", testApis);
        
        result.put("timestamp", System.currentTimeMillis());
        
        return result;
    }

    /**
     * API 列表接口
     *
     * @return API 列表
     */
    @GetMapping("/api/list")
    @ResponseBody
    public Map<String, Object> apiList() {
        Map<String, Object> result = new HashMap<>();
        
        // App Connector 测试接口
        Map<String, String> appConnectorApis = new HashMap<>();
        appConnectorApis.put("GET /test/app-connector/platforms", "获取支持的平台列表");
        appConnectorApis.put("GET /test/app-connector/auth/{platform}", "生成平台授权URL");
        appConnectorApis.put("POST /test/app-connector/callback/{platform}", "处理授权回调");
        appConnectorApis.put("GET /test/app-connector/token/status", "获取Token管理器状态");
        appConnectorApis.put("GET /test/app-connector/config/test", "测试配置信息");
        
        // 平台服务测试接口
        Map<String, String> platformServiceApis = new HashMap<>();
        platformServiceApis.put("GET /test/platform-services/status", "获取所有平台服务状态");
        platformServiceApis.put("GET /test/platform-services/wecom/test", "企业微信服务测试");
        platformServiceApis.put("GET /test/platform-services/dingtalk/test", "钉钉服务测试");
        platformServiceApis.put("GET /test/platform-services/douyin/test", "抖音服务测试");
        platformServiceApis.put("GET /test/platform-services/alipay/test", "支付宝服务测试");
        
        // 基础接口
        Map<String, String> basicApis = new HashMap<>();
        basicApis.put("GET /", "主页");
        basicApis.put("GET /health", "健康检查");
        basicApis.put("GET /info", "应用信息");
        basicApis.put("GET /api/list", "API列表");
        
        result.put("appConnector", appConnectorApis);
        result.put("platformServices", platformServiceApis);
        result.put("basic", basicApis);
        result.put("timestamp", System.currentTimeMillis());
        
        return result;
    }
}
