package cn.com.handthing.starter.connector.xiaohongshu.api;

import cn.com.handthing.starter.connector.xiaohongshu.config.XiaoHongShuConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 小红书内容API
 * <p>
 * 提供小红书内容发布相关的API功能，包括笔记发布、图片上传、视频管理等。
 * 支持个人开发者、企业开发者和品牌方的内容创作功能。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "handthing.connector.xiaohongshu", name = "enabled", havingValue = "true")
public class XiaoHongShuContentApi {

    private final XiaoHongShuConfig config;
    private final RestTemplate restTemplate;

    /**
     * 发布笔记
     *
     * @param accessToken 访问令牌
     * @param title       笔记标题
     * @param content     笔记内容
     * @param images      图片URL列表
     * @param tags        标签列表
     * @return 发布结果
     */
    public Map<String, Object> publishNote(String accessToken, String title, String content, 
                                           List<String> images, List<String> tags) {
        try {
            log.debug("Publishing XiaoHongShu note: {}", title);

            String url = config.getEffectiveApiBaseUrl() + "/api/note/publish";

            Map<String, Object> note = new HashMap<>();
            note.put("title", title);
            note.put("content", content);
            note.put("images", images);
            note.put("tags", tags);
            note.put("access_token", accessToken);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(note, headers);

            Map<String, Object> response = restTemplate.postForObject(url, request, Map.class);
            log.debug("XiaoHongShu note published, response: {}", response);

            return response;

        } catch (Exception e) {
            log.error("Failed to publish XiaoHongShu note", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", -1);
            errorResponse.put("message", "Failed to publish note: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 上传图片
     *
     * @param accessToken 访问令牌
     * @param imageData   图片数据（Base64编码）
     * @param imageName   图片名称
     * @return 上传结果
     */
    public Map<String, Object> uploadImage(String accessToken, String imageData, String imageName) {
        try {
            log.debug("Uploading XiaoHongShu image: {}", imageName);

            String url = config.getEffectiveApiBaseUrl() + "/api/image/upload";

            Map<String, Object> uploadData = new HashMap<>();
            uploadData.put("image", imageData);
            uploadData.put("image_name", imageName);
            uploadData.put("access_token", accessToken);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(uploadData, headers);

            Map<String, Object> response = restTemplate.postForObject(url, request, Map.class);
            log.debug("XiaoHongShu image uploaded, response: {}", response);

            return response;

        } catch (Exception e) {
            log.error("Failed to upload XiaoHongShu image", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", -1);
            errorResponse.put("message", "Failed to upload image: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 发布视频笔记
     *
     * @param accessToken 访问令牌
     * @param title       视频标题
     * @param description 视频描述
     * @param videoUrl    视频URL
     * @param coverImage  封面图片URL
     * @param tags        标签列表
     * @return 发布结果
     */
    public Map<String, Object> publishVideo(String accessToken, String title, String description, 
                                            String videoUrl, String coverImage, List<String> tags) {
        try {
            log.debug("Publishing XiaoHongShu video: {}", title);

            String url = config.getEffectiveApiBaseUrl() + "/api/video/publish";

            Map<String, Object> video = new HashMap<>();
            video.put("title", title);
            video.put("description", description);
            video.put("video_url", videoUrl);
            video.put("cover_image", coverImage);
            video.put("tags", tags);
            video.put("access_token", accessToken);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(video, headers);

            Map<String, Object> response = restTemplate.postForObject(url, request, Map.class);
            log.debug("XiaoHongShu video published, response: {}", response);

            return response;

        } catch (Exception e) {
            log.error("Failed to publish XiaoHongShu video", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", -1);
            errorResponse.put("message", "Failed to publish video: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 获取笔记列表
     *
     * @param accessToken 访问令牌
     * @param page        页码
     * @param pageSize    每页大小
     * @param noteType    笔记类型（image, video, all）
     * @return 笔记列表
     */
    public Map<String, Object> getNoteList(String accessToken, Integer page, Integer pageSize, String noteType) {
        try {
            log.debug("Getting XiaoHongShu note list, type: {}, page: {}", noteType, page);

            String url = config.getEffectiveApiBaseUrl() + "/api/note/list";

            Map<String, Object> params = new HashMap<>();
            params.put("access_token", accessToken);
            params.put("page", page != null ? page : 1);
            params.put("page_size", pageSize != null ? pageSize : 20);
            if (noteType != null && !noteType.trim().isEmpty()) {
                params.put("note_type", noteType);
            }

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(params, headers);

            Map<String, Object> response = restTemplate.postForObject(url, request, Map.class);
            log.debug("XiaoHongShu note list retrieved, response: {}", response);

            return response;

        } catch (Exception e) {
            log.error("Failed to get XiaoHongShu note list", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", -1);
            errorResponse.put("message", "Failed to get note list: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 删除笔记
     *
     * @param accessToken 访问令牌
     * @param noteId      笔记ID
     * @return 删除结果
     */
    public Map<String, Object> deleteNote(String accessToken, String noteId) {
        try {
            log.debug("Deleting XiaoHongShu note: {}", noteId);

            String url = config.getEffectiveApiBaseUrl() + "/api/note/delete";

            Map<String, Object> deleteData = new HashMap<>();
            deleteData.put("access_token", accessToken);
            deleteData.put("note_id", noteId);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(deleteData, headers);

            Map<String, Object> response = restTemplate.postForObject(url, request, Map.class);
            log.debug("XiaoHongShu note deleted, response: {}", response);

            return response;

        } catch (Exception e) {
            log.error("Failed to delete XiaoHongShu note", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", -1);
            errorResponse.put("message", "Failed to delete note: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 获取笔记详情
     *
     * @param accessToken 访问令牌
     * @param noteId      笔记ID
     * @return 笔记详情
     */
    public Map<String, Object> getNoteDetail(String accessToken, String noteId) {
        try {
            log.debug("Getting XiaoHongShu note detail: {}", noteId);

            String url = config.getEffectiveApiBaseUrl() + "/api/note/detail";

            Map<String, Object> params = new HashMap<>();
            params.put("access_token", accessToken);
            params.put("note_id", noteId);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(params, headers);

            Map<String, Object> response = restTemplate.postForObject(url, request, Map.class);
            log.debug("XiaoHongShu note detail retrieved, response: {}", response);

            return response;

        } catch (Exception e) {
            log.error("Failed to get XiaoHongShu note detail", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", -1);
            errorResponse.put("message", "Failed to get note detail: " + e.getMessage());
            return errorResponse;
        }
    }
}
