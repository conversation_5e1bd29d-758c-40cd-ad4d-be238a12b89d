package cn.com.handthing.starter.httpclient;

import cn.com.handthing.starter.httpclient.crypto.RequestEncryptor;
import cn.com.handthing.starter.httpclient.crypto.ResponseDecryptor;
import cn.com.handthing.starter.httpclient.internal.EndpointConfigResolver;
import cn.com.handthing.starter.httpclient.internal.WebClientHandthingHttpClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;

/**
 * WebClient HTTP 客户端自动配置类
 * 
 * <AUTHOR>
 * @since V1.0.0
 */
@Configuration
@ConditionalOnClass(WebClient.class)
@ConditionalOnProperty(prefix = "handthing.http-client", name = "enabled", havingValue = "true", matchIfMissing = true)
@AutoConfigureAfter(HttpCoreAutoConfiguration.class)
public class WebClientHttpAutoConfiguration {

    private static final Logger log = LoggerFactory.getLogger(WebClientHttpAutoConfiguration.class);

    @Bean
    @ConditionalOnMissingBean(HandthingHttpClient.class)
    public HandthingHttpClient handthingWebClientHttpClient(
            EndpointConfigResolver configResolver,
            @Qualifier("handthingNoOpRequestEncryptor") RequestEncryptor<?> requestEncryptor,
            @Qualifier("handthingNoOpResponseDecryptor") ResponseDecryptor responseDecryptor) {

        log.info("Configuring WebClient-based HandthingHttpClient");
        
        return new WebClientHandthingHttpClient(configResolver, requestEncryptor, responseDecryptor);
    }

    @Bean
    @ConditionalOnMissingBean
    public WebClient.Builder webClientBuilder() {
        log.debug("Creating default WebClient.Builder");
        return WebClient.builder();
    }
}
