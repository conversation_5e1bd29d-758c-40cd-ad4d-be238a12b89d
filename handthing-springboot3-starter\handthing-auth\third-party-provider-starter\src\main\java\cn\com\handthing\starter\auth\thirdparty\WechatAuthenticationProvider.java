package cn.com.handthing.starter.auth.thirdparty;

import cn.com.handthing.starter.auth.core.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * 微信认证提供者
 * <p>
 * 实现基于微信OAuth2的认证逻辑，支持公众号网页授权、开放平台扫码登录和小程序授权。
 * 提供完整的微信认证流程，包括授权码验证、用户信息获取、自动注册等。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WechatAuthenticationProvider implements AuthenticationProvider {

    private final WechatUserService wechatUserService;
    private final WechatApiService wechatApiService;
    private final JwtTokenProvider jwtTokenProvider;

    @Override
    public GrantType getSupportedGrantType() {
        return GrantType.WECHAT;
    }

    @Override
    public boolean supports(AuthenticationRequest request) {
        return request instanceof WechatAuthenticationRequest && 
               request.getGrantType() == GrantType.WECHAT;
    }

    @Override
    public AuthenticationResponse authenticate(AuthenticationRequest request) throws AuthenticationException {
        log.debug("Starting Wechat authentication for request: {}", request);

        if (!(request instanceof WechatAuthenticationRequest)) {
            throw new AuthenticationException("INVALID_REQUEST", "Invalid authentication request type");
        }

        WechatAuthenticationRequest wechatRequest = (WechatAuthenticationRequest) request;

        try {
            // 预处理
            preAuthenticate(wechatRequest);

            // 获取微信访问令牌和用户基础信息
            WechatApiService.WechatTokenResult tokenResult = wechatApiService.getAccessToken(
                    wechatRequest.getAppId(), wechatRequest.getAppSecret(), wechatRequest.getCode());
            
            if (!tokenResult.isSuccess()) {
                throw new InvalidCredentialsException("微信授权码无效: " + tokenResult.getErrorMessage());
            }

            // 获取用户详细信息（如果授权范围允许）
            WechatApiService.WechatUserResult userInfo = null;
            if (wechatRequest.isUserInfoScope()) {
                userInfo = wechatApiService.getUserInfo(tokenResult.getAccessToken(), 
                        tokenResult.getOpenId(), wechatRequest.getLang());
                
                if (!userInfo.isSuccess()) {
                    throw new AuthenticationException("WECHAT_API_ERROR", "获取用户详细信息失败: " + userInfo.getErrorMessage());
                }
            } else {
                // 基础授权只能获取OpenID
                userInfo = WechatApiService.WechatUserResult.success();
                userInfo.setOpenId(tokenResult.getOpenId());
            }

            // 查找或创建用户
            UserInfo systemUserInfo = findOrCreateUser(wechatRequest, userInfo, tokenResult);

            // 验证用户状态
            validateUserStatus(systemUserInfo);

            // 生成令牌
            String jwtAccessToken = generateAccessToken(systemUserInfo, wechatRequest);
            String refreshToken = generateRefreshToken(systemUserInfo, wechatRequest);
            Long expiresIn = 7200L; // 2小时

            // 更新登录信息
            updateLoginInfo(systemUserInfo, wechatRequest);

            // 清除登录失败记录
            wechatUserService.clearLoginFailures(tokenResult.getOpenId(), wechatRequest.getAppId());

            // 创建成功响应
            WechatAuthenticationResponse response;
            if (wechatRequest.getAutoRegister() && wechatUserService.isFirstLogin(systemUserInfo)) {
                response = WechatAuthenticationResponse.successForNewUser(jwtAccessToken, refreshToken, expiresIn, systemUserInfo);
            } else {
                response = WechatAuthenticationResponse.success(jwtAccessToken, refreshToken, expiresIn, systemUserInfo);
            }

            // 设置额外信息
            setAdditionalInfo(response, systemUserInfo, wechatRequest, userInfo, tokenResult);

            log.info("Wechat authentication successful for user: {}, appId: {}", tokenResult.getOpenId(), wechatRequest.getAppId());
            return response;

        } catch (AuthenticationException e) {
            // 记录登录失败
            recordLoginFailure(wechatRequest, e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Wechat authentication error", e);
            recordLoginFailure(wechatRequest, "System error");
            throw new AuthenticationException("AUTHENTICATION_ERROR", "Authentication failed", e);
        }
    }

    @Override
    public boolean validateToken(String token) {
        return jwtTokenProvider.validateToken(token);
    }

    @Override
    public AuthenticationResponse refreshToken(String refreshToken) throws AuthenticationException {
        try {
            JwtClaims claims = jwtTokenProvider.parseToken(refreshToken);
            
            // 验证是否为刷新令牌
            if (!"refresh_token".equals(claims.getTokenType())) {
                throw new AuthenticationException("INVALID_REFRESH_TOKEN", "Invalid refresh token type");
            }

            // 查找用户
            Optional<UserInfo> userOptional = wechatUserService.findById(claims.getSubject());
            if (userOptional.isEmpty()) {
                throw new AuthenticationException("USER_NOT_FOUND", "User not found");
            }

            UserInfo userInfo = userOptional.get();

            // 验证用户状态
            validateUserStatus(userInfo);

            // 生成新的访问令牌
            String newAccessToken = generateAccessToken(userInfo, null);
            Long expiresIn = 7200L;

            return WechatAuthenticationResponse.success(newAccessToken, refreshToken, expiresIn, userInfo);

        } catch (AuthenticationException e) {
            throw e;
        } catch (Exception e) {
            log.error("Refresh token error", e);
            throw new AuthenticationException("REFRESH_TOKEN_ERROR", "Failed to refresh token", e);
        }
    }

    @Override
    public String getProviderName() {
        return "Wechat Authentication Provider";
    }

    @Override
    public boolean isAvailable() {
        return true;
    }

    @Override
    public int getPriority() {
        return 4;
    }

    /**
     * 预处理认证请求
     *
     * @param request 微信认证请求
     * @throws AuthenticationException 认证异常
     */
    private void preAuthenticate(WechatAuthenticationRequest request) throws AuthenticationException {
        // 验证请求参数
        if (!request.isValid()) {
            throw new AuthenticationException("INVALID_REQUEST", "Invalid authentication request");
        }

        // 验证微信配置
        if (!wechatApiService.validateConfig(request.getAppId(), request.getAppSecret())) {
            throw new AuthenticationException("INVALID_WECHAT_CONFIG", "Invalid Wechat configuration");
        }
    }

    /**
     * 查找或创建用户
     *
     * @param request     微信认证请求
     * @param userInfo    微信用户信息
     * @param tokenResult 令牌结果
     * @return 用户信息
     * @throws AuthenticationException 认证异常
     */
    private UserInfo findOrCreateUser(WechatAuthenticationRequest request, 
                                     WechatApiService.WechatUserResult userInfo,
                                     WechatApiService.WechatTokenResult tokenResult) throws AuthenticationException {
        
        String openId = tokenResult.getOpenId();
        String unionId = userInfo.getUnionId();

        // 优先根据UnionID查找用户（如果存在）
        Optional<UserInfo> userOptional = Optional.empty();
        if (unionId != null && !unionId.trim().isEmpty()) {
            userOptional = wechatUserService.findByWechatUnionId(unionId);
        }

        // 如果UnionID没找到，根据OpenID查找
        if (userOptional.isEmpty()) {
            userOptional = wechatUserService.findByWechatOpenId(openId, request.getAppId());
        }

        if (userOptional.isPresent()) {
            return userOptional.get();
        }

        // 用户不存在
        if (!request.getAutoRegister()) {
            throw AuthenticationFailedException.userNotFound(openId);
        }

        // 自动注册新用户
        try {
            UserInfo newUser = wechatUserService.createUser(userInfo, request.getAppId());
            log.info("Auto-registered new user for wechat openId: {}, appId: {}", openId, request.getAppId());
            return newUser;

        } catch (Exception e) {
            log.error("Failed to create user for wechat openId: {}, appId: {}", openId, request.getAppId(), e);
            throw new AuthenticationException("REGISTRATION_FAILED", "Failed to create user", e);
        }
    }

    /**
     * 验证用户状态
     *
     * @param userInfo 用户信息
     * @throws AuthenticationException 认证异常
     */
    private void validateUserStatus(UserInfo userInfo) throws AuthenticationException {
        // 检查用户是否有效
        if (!wechatUserService.isUserValid(userInfo)) {
            throw AuthenticationFailedException.userDisabled(userInfo.getUsername());
        }

        // 检查用户是否被锁定
        if (wechatUserService.isUserLocked(userInfo)) {
            throw AuthenticationFailedException.userLocked(userInfo.getUsername());
        }

        // 检查用户是否被禁用
        if (wechatUserService.isUserDisabled(userInfo)) {
            throw AuthenticationFailedException.userDisabled(userInfo.getUsername());
        }

        // 检查账户是否过期
        if (wechatUserService.isAccountExpired(userInfo)) {
            throw new AuthenticationException("ACCOUNT_EXPIRED", "Account has expired");
        }
    }

    /**
     * 生成访问令牌
     *
     * @param userInfo 用户信息
     * @param request  认证请求
     * @return 访问令牌
     */
    private String generateAccessToken(UserInfo userInfo, WechatAuthenticationRequest request) {
        JwtClaims claims = JwtClaims.builder()
                .subject(userInfo.getUserId())
                .username(userInfo.getUsername())
                .nickname(userInfo.getNickname())
                .email(userInfo.getEmail())
                .phone(userInfo.getPhone())
                .roles(userInfo.getRoles())
                .permissions(userInfo.getPermissions())
                .grantType(GrantType.WECHAT.getCode())
                .tokenType("access_token")
                .build();

        if (request != null) {
            claims.setClientId(request.getClientId());
            claims.setScope(request.getScope());
            claims.setIpAddress(request.getIpAddress());
            claims.setUserAgent(request.getUserAgent());
            claims.setDeviceId(request.getDeviceId());
            
            // 添加微信相关信息
            claims.addCustomClaim("app_id", request.getAppId());
            claims.addCustomClaim("auth_type", request.getAuthType());
            claims.addCustomClaim("wechat_scope", request.getScope());
        }

        return jwtTokenProvider.generateAccessToken(claims);
    }

    /**
     * 生成刷新令牌
     *
     * @param userInfo 用户信息
     * @param request  认证请求
     * @return 刷新令牌
     */
    private String generateRefreshToken(UserInfo userInfo, WechatAuthenticationRequest request) {
        JwtClaims claims = JwtClaims.builder()
                .subject(userInfo.getUserId())
                .username(userInfo.getUsername())
                .grantType(GrantType.WECHAT.getCode())
                .tokenType("refresh_token")
                .build();

        if (request != null) {
            claims.setClientId(request.getClientId());
            claims.addCustomClaim("app_id", request.getAppId());
        }

        return jwtTokenProvider.generateRefreshToken(claims);
    }

    /**
     * 更新登录信息
     *
     * @param userInfo 用户信息
     * @param request  认证请求
     */
    private void updateLoginInfo(UserInfo userInfo, WechatAuthenticationRequest request) {
        wechatUserService.updateLastLoginInfo(
                userInfo.getUserId(),
                LocalDateTime.now(),
                request.getIpAddress(),
                request.getUserAgent()
        );
    }

    /**
     * 设置额外信息
     *
     * @param response    认证响应
     * @param userInfo    用户信息
     * @param request     认证请求
     * @param wechatUser  微信用户信息
     * @param tokenResult 令牌结果
     */
    private void setAdditionalInfo(WechatAuthenticationResponse response, UserInfo userInfo, 
                                  WechatAuthenticationRequest request, WechatApiService.WechatUserResult wechatUser,
                                  WechatApiService.WechatTokenResult tokenResult) {
        response.setFirstLogin(wechatUserService.isFirstLogin(userInfo));
        response.setLastLoginTime(userInfo.getLastLoginTime());
        response.setLastLoginIp(userInfo.getLastLoginIp());
        
        // 设置微信相关信息
        response.setWechatUserInfo(tokenResult.getOpenId(), wechatUser.getUnionId(), 
                wechatUser.getNickname(), wechatUser.getHeadImgUrl(), request.getAppId(), tokenResult.getScope());
    }

    /**
     * 记录登录失败
     *
     * @param request 认证请求
     * @param reason  失败原因
     */
    private void recordLoginFailure(WechatAuthenticationRequest request, String reason) {
        try {
            // 这里使用微信用户标识记录失败
            String identifier = request.getAppId() + ":" + request.getCode();
            wechatUserService.recordLoginFailure(identifier, request.getAppId(), request.getIpAddress(), reason);
        } catch (Exception e) {
            log.error("Failed to record login failure", e);
        }
    }
}
