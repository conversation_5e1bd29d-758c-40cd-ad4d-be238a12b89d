package cn.com.handthing.starter.connector.toutiao;

import cn.com.handthing.starter.connector.toutiao.api.ToutiaoContentApi;
import cn.com.handthing.starter.connector.toutiao.api.ToutiaoAnalyticsApi;
import cn.com.handthing.starter.connector.toutiao.config.ToutiaoConfig;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

/**
 * 今日头条服务
 * <p>
 * 提供今日头条平台的核心服务功能，包括内容发布、数据分析、用户管理等。
 * 支持个人创作者和企业用户的不同业务场景。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "handthing.connector.toutiao", name = "enabled", havingValue = "true")
public class ToutiaoService {

    private final ToutiaoConfig config;
    private final ToutiaoContentApi contentApi;
    private final ToutiaoAnalyticsApi analyticsApi;

    /**
     * 获取内容API
     *
     * @return 内容API实例
     */
    public ToutiaoContentApi content() {
        return contentApi;
    }

    /**
     * 获取数据分析API
     *
     * @return 数据分析API实例
     */
    public ToutiaoAnalyticsApi analytics() {
        return analyticsApi;
    }

    /**
     * 检查服务是否可用
     *
     * @return 如果可用返回true，否则返回false
     */
    public boolean isAvailable() {
        return config.isEnabled() && config.isValid();
    }

    /**
     * 获取配置信息
     *
     * @return 配置对象
     */
    public ToutiaoConfig getConfig() {
        return config;
    }

    /**
     * 获取服务状态
     *
     * @return 服务状态
     */
    public ServiceStatus getStatus() {
        return ServiceStatus.builder()
                .enabled(config.isEnabled())
                .valid(config.isValid())
                .clientKey(config.getClientKey())
                .appType(config.getAppType())
                .enterpriseMode(config.isEnterpriseMode())
                .sandboxMode(config.isSandboxMode())
                .contentPublishEnabled(config.isContentPublishAvailable())
                .analyticsEnabled(config.isAnalyticsAvailable())
                .userManagementEnabled(config.isUserManagementAvailable())
                .apiBaseUrl(config.getEffectiveApiBaseUrl())
                .build();
    }

    /**
     * 服务状态信息
     */
    @Data
    @lombok.Builder
    public static class ServiceStatus {
        /**
         * 是否启用
         */
        private boolean enabled;

        /**
         * 配置是否有效
         */
        private boolean valid;

        /**
         * 客户端Key
         */
        private String clientKey;

        /**
         * 应用类型
         */
        private String appType;

        /**
         * 是否为企业模式
         */
        private boolean enterpriseMode;

        /**
         * 是否为沙箱模式
         */
        private boolean sandboxMode;

        /**
         * 是否启用内容发布功能
         */
        private boolean contentPublishEnabled;

        /**
         * 是否启用数据分析功能
         */
        private boolean analyticsEnabled;

        /**
         * 是否启用用户管理功能
         */
        private boolean userManagementEnabled;

        /**
         * API基础URL
         */
        private String apiBaseUrl;

        @Override
        public String toString() {
            return String.format("ToutiaoServiceStatus{enabled=%s, valid=%s, clientKey='%s', appType='%s', " +
                            "enterpriseMode=%s, sandboxMode=%s, contentPublishEnabled=%s, analyticsEnabled=%s, " +
                            "userManagementEnabled=%s, apiBaseUrl='%s'}",
                    enabled, valid, clientKey, appType, enterpriseMode, sandboxMode, 
                    contentPublishEnabled, analyticsEnabled, userManagementEnabled, apiBaseUrl);
        }
    }
}
