package cn.com.handthing.starter.auth.thirdparty;

import java.util.Map;

/**
 * 钉钉API服务接口
 * <p>
 * 定义钉钉API调用的核心接口，包括获取访问令牌、用户信息等。
 * 业务系统可以实现此接口来对接钉钉的具体API。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
public interface DingtalkApiService {

    /**
     * 获取钉钉访问令牌
     *
     * @param appKey    应用Key
     * @param appSecret 应用密钥
     * @return 访问令牌结果
     */
    DingtalkTokenResult getAccessToken(String appKey, String appSecret);

    /**
     * 根据授权码获取用户信息
     *
     * @param accessToken 访问令牌
     * @param code        授权码
     * @return 用户信息结果
     */
    DingtalkUserResult getUserInfoByCode(String accessToken, String code);

    /**
     * 根据用户ID获取用户详细信息
     *
     * @param accessToken 访问令牌
     * @param userId      用户ID
     * @return 用户详细信息结果
     */
    DingtalkUserDetailResult getUserDetail(String accessToken, String userId);

    /**
     * 验证钉钉应用配置
     *
     * @param appKey    应用Key
     * @param appSecret 应用密钥
     * @return 如果配置有效返回true，否则返回false
     */
    boolean validateConfig(String appKey, String appSecret);

    /**
     * 钉钉令牌结果
     */
    class DingtalkTokenResult {
        private boolean success;
        private String accessToken;
        private Long expiresIn;
        private String errorCode;
        private String errorMessage;

        public DingtalkTokenResult(boolean success, String accessToken, Long expiresIn) {
            this.success = success;
            this.accessToken = accessToken;
            this.expiresIn = expiresIn;
        }

        public DingtalkTokenResult(boolean success, String errorCode, String errorMessage) {
            this.success = success;
            this.errorCode = errorCode;
            this.errorMessage = errorMessage;
        }

        public boolean isSuccess() {
            return success;
        }

        public String getAccessToken() {
            return accessToken;
        }

        public Long getExpiresIn() {
            return expiresIn;
        }

        public String getErrorCode() {
            return errorCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public static DingtalkTokenResult success(String accessToken, Long expiresIn) {
            return new DingtalkTokenResult(true, accessToken, expiresIn);
        }

        public static DingtalkTokenResult failure(String errorCode, String errorMessage) {
            return new DingtalkTokenResult(false, errorCode, errorMessage);
        }

        @Override
        public String toString() {
            return String.format("DingtalkTokenResult{success=%s, accessToken='%s', expiresIn=%d, errorCode='%s', errorMessage='%s'}",
                    success, accessToken != null ? accessToken.substring(0, Math.min(accessToken.length(), 10)) + "..." : null, 
                    expiresIn, errorCode, errorMessage);
        }
    }

    /**
     * 钉钉用户结果
     */
    class DingtalkUserResult {
        private boolean success;
        private String userId;
        private String deviceId;
        private String sysLevel;
        private String errorCode;
        private String errorMessage;

        public DingtalkUserResult(boolean success, String userId, String deviceId, String sysLevel) {
            this.success = success;
            this.userId = userId;
            this.deviceId = deviceId;
            this.sysLevel = sysLevel;
        }

        public DingtalkUserResult(boolean success, String errorCode, String errorMessage) {
            this.success = success;
            this.errorCode = errorCode;
            this.errorMessage = errorMessage;
        }

        public boolean isSuccess() {
            return success;
        }

        public String getUserId() {
            return userId;
        }

        public String getDeviceId() {
            return deviceId;
        }

        public String getSysLevel() {
            return sysLevel;
        }

        public String getErrorCode() {
            return errorCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public static DingtalkUserResult success(String userId, String deviceId, String sysLevel) {
            return new DingtalkUserResult(true, userId, deviceId, sysLevel);
        }

        public static DingtalkUserResult failure(String errorCode, String errorMessage) {
            return new DingtalkUserResult(false, errorCode, errorMessage);
        }

        @Override
        public String toString() {
            return String.format("DingtalkUserResult{success=%s, userId='%s', deviceId='%s', sysLevel='%s', errorCode='%s', errorMessage='%s'}",
                    success, userId, deviceId, sysLevel, errorCode, errorMessage);
        }
    }

    /**
     * 钉钉用户详细信息结果
     */
    class DingtalkUserDetailResult {
        private boolean success;
        private String userId;
        private String name;
        private String mobile;
        private String email;
        private String avatar;
        private String jobNumber;
        private String title;
        private String workPlace;
        private String remark;
        private String[] deptIdList;
        private String[] deptOrderList;
        private Map<String, Object> extension;
        private String errorCode;
        private String errorMessage;

        public DingtalkUserDetailResult(boolean success) {
            this.success = success;
        }

        public DingtalkUserDetailResult(boolean success, String errorCode, String errorMessage) {
            this.success = success;
            this.errorCode = errorCode;
            this.errorMessage = errorMessage;
        }

        public boolean isSuccess() {
            return success;
        }

        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getMobile() {
            return mobile;
        }

        public void setMobile(String mobile) {
            this.mobile = mobile;
        }

        public String getEmail() {
            return email;
        }

        public void setEmail(String email) {
            this.email = email;
        }

        public String getAvatar() {
            return avatar;
        }

        public void setAvatar(String avatar) {
            this.avatar = avatar;
        }

        public String getJobNumber() {
            return jobNumber;
        }

        public void setJobNumber(String jobNumber) {
            this.jobNumber = jobNumber;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getWorkPlace() {
            return workPlace;
        }

        public void setWorkPlace(String workPlace) {
            this.workPlace = workPlace;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }

        public String[] getDeptIdList() {
            return deptIdList;
        }

        public void setDeptIdList(String[] deptIdList) {
            this.deptIdList = deptIdList;
        }

        public String[] getDeptOrderList() {
            return deptOrderList;
        }

        public void setDeptOrderList(String[] deptOrderList) {
            this.deptOrderList = deptOrderList;
        }

        public Map<String, Object> getExtension() {
            return extension;
        }

        public void setExtension(Map<String, Object> extension) {
            this.extension = extension;
        }

        public String getErrorCode() {
            return errorCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public static DingtalkUserDetailResult success() {
            return new DingtalkUserDetailResult(true);
        }

        public static DingtalkUserDetailResult failure(String errorCode, String errorMessage) {
            return new DingtalkUserDetailResult(false, errorCode, errorMessage);
        }

        @Override
        public String toString() {
            return String.format("DingtalkUserDetailResult{success=%s, userId='%s', name='%s', mobile='%s', email='%s', errorCode='%s', errorMessage='%s'}",
                    success, userId, name, mobile, email, errorCode, errorMessage);
        }
    }
}
