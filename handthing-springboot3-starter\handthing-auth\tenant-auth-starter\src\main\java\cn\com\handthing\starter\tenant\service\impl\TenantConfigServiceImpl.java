package cn.com.handthing.starter.tenant.service.impl;

import cn.com.handthing.starter.tenant.config.TenantConfig;
import cn.com.handthing.starter.tenant.config.TenantConfigKey;
import cn.com.handthing.starter.tenant.config.TenantConfigType;
import cn.com.handthing.starter.tenant.context.TenantStatus;
import cn.com.handthing.starter.tenant.repository.TenantConfigRepository;
import cn.com.handthing.starter.tenant.service.TenantConfigChangeListener;
import cn.com.handthing.starter.tenant.service.TenantConfigService;
import cn.com.handthing.starter.tenant.service.TenantConfigStats;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

/**
 * 租户配置服务实现类
 * <p>
 * 实现分层配置读取逻辑：租户配置 -> 全局默认配置。
 * 集成cache-starter进行重度缓存，支持配置项的类型转换和验证，提供配置变更通知机制。
 * </p>
 *
 * <AUTHOR>
 * @since V3.0.0
 */
@Service
@Transactional
public class TenantConfigServiceImpl implements TenantConfigService {

    private static final Logger log = LoggerFactory.getLogger(TenantConfigServiceImpl.class);

    /**
     * 租户配置仓库
     */
    private final TenantConfigRepository tenantConfigRepository;

    /**
     * JSON对象映射器
     */
    private final ObjectMapper objectMapper;

    /**
     * 配置变更监听器列表
     */
    private final List<TenantConfigChangeListener> changeListeners = new CopyOnWriteArrayList<>();

    /**
     * 缓存名称
     */
    private static final String CACHE_NAME = "tenant-config";

    /**
     * 构造函数
     *
     * @param tenantConfigRepository 租户配置仓库
     * @param objectMapper          JSON对象映射器
     */
    public TenantConfigServiceImpl(TenantConfigRepository tenantConfigRepository,
                                   ObjectMapper objectMapper) {
        this.tenantConfigRepository = tenantConfigRepository;
        this.objectMapper = objectMapper;
        log.info("TenantConfigService initialized");
    }

    // ========== 基础配置操作 ==========

    @Override
    @Cacheable(value = CACHE_NAME, key = "#tenantId + ':' + #configKey")
    public String getConfigValue(String tenantId, String configKey) {
        log.debug("Getting config value: tenant={}, key={}", tenantId, configKey);

        Optional<TenantConfig> config = tenantConfigRepository.findByTenantIdAndConfigKey(tenantId, configKey);
        if (config.isPresent() && config.get().isValid()) {
            String value = config.get().getConfigValue();
            log.debug("Found tenant config: tenant={}, key={}, value={}", 
                     tenantId, configKey, config.get().getMaskedValue());
            return value;
        }

        // 尝试获取全局默认配置
        TenantConfigKey configKeyEnum = TenantConfigKey.fromKey(configKey);
        if (configKeyEnum != null) {
            String defaultValue = configKeyEnum.getDefaultValue();
            log.debug("Using default config: tenant={}, key={}, value={}", 
                     tenantId, configKey, defaultValue);
            return defaultValue;
        }

        log.debug("No config found: tenant={}, key={}", tenantId, configKey);
        return null;
    }

    @Override
    public <T> T getConfigValue(String tenantId, String configKey, Class<T> clazz) {
        String value = getConfigValue(tenantId, configKey);
        if (value == null) {
            return null;
        }

        try {
            // 获取配置类型
            TenantConfigType configType = getConfigType(tenantId, configKey);
            return configType.convertValue(value, clazz);
        } catch (Exception e) {
            log.warn("Failed to convert config value: tenant={}, key={}, value={}, targetType={}", 
                    tenantId, configKey, value, clazz.getSimpleName(), e);
            return null;
        }
    }

    @Override
    public String getConfigValue(String tenantId, String configKey, String defaultValue) {
        String value = getConfigValue(tenantId, configKey);
        return value != null ? value : defaultValue;
    }

    @Override
    public <T> T getConfigValue(String tenantId, String configKey, Class<T> clazz, T defaultValue) {
        T value = getConfigValue(tenantId, configKey, clazz);
        return value != null ? value : defaultValue;
    }

    @Override
    public String getConfigValue(String tenantId, TenantConfigKey configKey) {
        return getConfigValue(tenantId, configKey.getKey());
    }

    @Override
    public <T> T getConfigValue(String tenantId, TenantConfigKey configKey, Class<T> clazz) {
        return getConfigValue(tenantId, configKey.getKey(), clazz);
    }

    // ========== 配置对象操作 ==========

    @Override
    @Cacheable(value = CACHE_NAME, key = "#tenantId + ':config:' + #configKey")
    public Optional<TenantConfig> getConfig(String tenantId, String configKey) {
        return tenantConfigRepository.findByTenantIdAndConfigKey(tenantId, configKey);
    }

    @Override
    @Cacheable(value = CACHE_NAME, key = "#tenantId + ':all'")
    public List<TenantConfig> getAllConfigs(String tenantId) {
        return tenantConfigRepository.findByTenantId(tenantId);
    }

    @Override
    @Cacheable(value = CACHE_NAME, key = "#tenantId + ':enabled'")
    public List<TenantConfig> getEnabledConfigs(String tenantId) {
        return tenantConfigRepository.findByTenantIdAndEnabled(tenantId, true);
    }

    @Override
    public Map<String, String> getConfigMap(String tenantId) {
        List<TenantConfig> configs = getAllConfigs(tenantId);
        return configs.stream()
                .filter(TenantConfig::isValid)
                .collect(Collectors.toMap(
                        TenantConfig::getConfigKey,
                        TenantConfig::getConfigValue,
                        (existing, replacement) -> replacement
                ));
    }

    @Override
    public Map<String, String> getEnabledConfigMap(String tenantId) {
        List<TenantConfig> configs = getEnabledConfigs(tenantId);
        return configs.stream()
                .collect(Collectors.toMap(
                        TenantConfig::getConfigKey,
                        TenantConfig::getConfigValue,
                        (existing, replacement) -> replacement
                ));
    }

    // ========== 配置设置操作 ==========

    @Override
    @CacheEvict(value = CACHE_NAME, key = "#tenantId + ':' + #configKey")
    public void setConfig(String tenantId, String configKey, String configValue) {
        TenantConfigType configType = inferConfigType(configKey, configValue);
        setConfig(tenantId, configKey, configValue, configType);
    }

    @Override
    @CacheEvict(value = CACHE_NAME, allEntries = true)
    public void setConfig(String tenantId, String configKey, String configValue, TenantConfigType configType) {
        log.debug("Setting config: tenant={}, key={}, type={}", tenantId, configKey, configType);

        Optional<TenantConfig> existingConfig = tenantConfigRepository.findByTenantIdAndConfigKey(tenantId, configKey);
        
        TenantConfig config;
        TenantConfig oldConfig = null;
        
        if (existingConfig.isPresent()) {
            // 更新现有配置
            config = existingConfig.get();
            oldConfig = config.copy();
            config.setConfigValue(configValue);
            config.setConfigType(configType);
            config.setUpdatedAt(LocalDateTime.now());
        } else {
            // 创建新配置
            config = TenantConfig.builder()
                    .tenantId(tenantId)
                    .configKey(configKey)
                    .configValue(configValue)
                    .configType(configType)
                    .enabled(true)
                    .sensitive(configType.isSensitive())
                    .build();
        }

        // 保存配置
        TenantConfig savedConfig = tenantConfigRepository.save(config);

        // 触发事件
        if (oldConfig == null) {
            notifyConfigCreated(savedConfig);
        } else {
            notifyConfigUpdated(oldConfig, savedConfig);
        }

        log.info("Config saved: tenant={}, key={}, type={}", tenantId, configKey, configType);
    }

    @Override
    public void setConfig(String tenantId, TenantConfigKey configKey, String configValue) {
        setConfig(tenantId, configKey.getKey(), configValue, configKey.getType());
    }

    @Override
    @CacheEvict(value = CACHE_NAME, allEntries = true)
    public void setConfigs(String tenantId, Map<String, String> configMap) {
        if (configMap == null || configMap.isEmpty()) {
            return;
        }

        List<TenantConfig> configs = new ArrayList<>();
        for (Map.Entry<String, String> entry : configMap.entrySet()) {
            String configKey = entry.getKey();
            String configValue = entry.getValue();
            TenantConfigType configType = inferConfigType(configKey, configValue);

            TenantConfig config = TenantConfig.builder()
                    .tenantId(tenantId)
                    .configKey(configKey)
                    .configValue(configValue)
                    .configType(configType)
                    .enabled(true)
                    .sensitive(configType.isSensitive())
                    .build();
            configs.add(config);
        }

        List<TenantConfig> savedConfigs = tenantConfigRepository.saveAll(configs);
        
        // 触发批量变更事件
        notifyBatchConfigChanged(tenantId, savedConfigs.size());

        log.info("Batch configs saved: tenant={}, count={}", tenantId, savedConfigs.size());
    }

    @Override
    @CacheEvict(value = CACHE_NAME, allEntries = true)
    public TenantConfig saveConfig(TenantConfig config) {
        if (config == null || !config.isValid()) {
            throw new IllegalArgumentException("Invalid config: " + config);
        }

        TenantConfig savedConfig = tenantConfigRepository.save(config);
        
        // 触发事件
        if (config.getId() == null) {
            notifyConfigCreated(savedConfig);
        } else {
            // 这里简化处理，实际应该获取旧配置进行比较
            notifyConfigUpdated(config, savedConfig);
        }

        return savedConfig;
    }

    @Override
    @CacheEvict(value = CACHE_NAME, allEntries = true)
    public List<TenantConfig> saveConfigs(List<TenantConfig> configs) {
        if (configs == null || configs.isEmpty()) {
            return Collections.emptyList();
        }

        // 验证所有配置
        for (TenantConfig config : configs) {
            if (!config.isValid()) {
                throw new IllegalArgumentException("Invalid config: " + config);
            }
        }

        List<TenantConfig> savedConfigs = tenantConfigRepository.saveAll(configs);
        
        // 按租户分组触发事件
        Map<String, List<TenantConfig>> configsByTenant = savedConfigs.stream()
                .collect(Collectors.groupingBy(TenantConfig::getTenantId));
        
        for (Map.Entry<String, List<TenantConfig>> entry : configsByTenant.entrySet()) {
            notifyBatchConfigChanged(entry.getKey(), entry.getValue().size());
        }

        return savedConfigs;
    }

    // ========== 私有辅助方法 ==========

    /**
     * 获取配置类型
     */
    private TenantConfigType getConfigType(String tenantId, String configKey) {
        Optional<TenantConfig> config = tenantConfigRepository.findByTenantIdAndConfigKey(tenantId, configKey);
        if (config.isPresent()) {
            return config.get().getConfigType();
        }

        TenantConfigKey configKeyEnum = TenantConfigKey.fromKey(configKey);
        return configKeyEnum != null ? configKeyEnum.getType() : TenantConfigType.STRING;
    }

    /**
     * 推断配置类型
     */
    private TenantConfigType inferConfigType(String configKey, String configValue) {
        // 首先尝试从枚举获取类型
        TenantConfigKey configKeyEnum = TenantConfigKey.fromKey(configKey);
        if (configKeyEnum != null) {
            return configKeyEnum.getType();
        }

        // 根据值推断类型
        if (configValue == null) {
            return TenantConfigType.STRING;
        }

        // 尝试解析为不同类型
        if ("true".equalsIgnoreCase(configValue) || "false".equalsIgnoreCase(configValue)) {
            return TenantConfigType.BOOLEAN;
        }

        try {
            Integer.parseInt(configValue);
            return TenantConfigType.INTEGER;
        } catch (NumberFormatException ignored) {
        }

        try {
            Double.parseDouble(configValue);
            return TenantConfigType.DOUBLE;
        } catch (NumberFormatException ignored) {
        }

        if (configValue.startsWith("{") && configValue.endsWith("}")) {
            return TenantConfigType.JSON;
        }

        if (configValue.startsWith("[") && configValue.endsWith("]")) {
            return TenantConfigType.JSON_ARRAY;
        }

        return TenantConfigType.STRING;
    }

    /**
     * 通知配置创建
     */
    private void notifyConfigCreated(TenantConfig config) {
        for (TenantConfigChangeListener listener : changeListeners) {
            try {
                if (listener.supports(config.getTenantId(), config.getConfigKey())) {
                    listener.onConfigCreated(config);
                }
            } catch (Exception e) {
                log.warn("Error notifying config created to listener {}: {}", 
                        listener.getName(), e.getMessage());
            }
        }
    }

    /**
     * 通知配置更新
     */
    private void notifyConfigUpdated(TenantConfig oldConfig, TenantConfig newConfig) {
        for (TenantConfigChangeListener listener : changeListeners) {
            try {
                if (listener.supports(newConfig.getTenantId(), newConfig.getConfigKey())) {
                    listener.onConfigUpdated(oldConfig, newConfig);
                }
            } catch (Exception e) {
                log.warn("Error notifying config updated to listener {}: {}", 
                        listener.getName(), e.getMessage());
            }
        }
    }

    /**
     * 通知配置删除
     */
    private void notifyConfigDeleted(TenantConfig config) {
        for (TenantConfigChangeListener listener : changeListeners) {
            try {
                if (listener.supports(config.getTenantId(), config.getConfigKey())) {
                    listener.onConfigDeleted(config);
                }
            } catch (Exception e) {
                log.warn("Error notifying config deleted to listener {}: {}", 
                        listener.getName(), e.getMessage());
            }
        }
    }

    /**
     * 通知批量配置变更
     */
    private void notifyBatchConfigChanged(String tenantId, int changeCount) {
        for (TenantConfigChangeListener listener : changeListeners) {
            try {
                if (listener.supports(tenantId)) {
                    listener.onBatchConfigChanged(tenantId, changeCount);
                }
            } catch (Exception e) {
                log.warn("Error notifying batch config changed to listener {}: {}",
                        listener.getName(), e.getMessage());
            }
        }
    }

    // ========== 配置删除操作 ==========

    @Override
    @CacheEvict(value = CACHE_NAME, key = "#tenantId + ':' + #configKey")
    public boolean deleteConfig(String tenantId, String configKey) {
        Optional<TenantConfig> config = tenantConfigRepository.findByTenantIdAndConfigKey(tenantId, configKey);
        if (config.isPresent()) {
            tenantConfigRepository.delete(config.get());
            notifyConfigDeleted(config.get());
            log.info("Config deleted: tenant={}, key={}", tenantId, configKey);
            return true;
        }
        return false;
    }

    @Override
    @CacheEvict(value = CACHE_NAME, allEntries = true)
    public long deleteAllConfigs(String tenantId) {
        long count = tenantConfigRepository.deleteByTenantId(tenantId);
        if (count > 0) {
            notifyTenantConfigCleared(tenantId);
            log.info("All configs deleted for tenant: {}, count={}", tenantId, count);
        }
        return count;
    }

    @Override
    @CacheEvict(value = CACHE_NAME, allEntries = true)
    public long deleteConfigs(String tenantId, List<String> configKeys) {
        if (configKeys == null || configKeys.isEmpty()) {
            return 0;
        }

        long count = 0;
        for (String configKey : configKeys) {
            if (deleteConfig(tenantId, configKey)) {
                count++;
            }
        }
        return count;
    }

    // ========== 租户管理操作 ==========

    @Override
    public boolean tenantExists(String tenantId) {
        return tenantConfigRepository.existsByTenantId(tenantId);
    }

    @Override
    public String getTenantName(String tenantId) {
        return getConfigValue(tenantId, TenantConfigKey.TENANT_NAME);
    }

    @Override
    public TenantStatus getTenantStatus(String tenantId) {
        String statusValue = getConfigValue(tenantId, TenantConfigKey.TENANT_STATUS);
        return statusValue != null ? TenantStatus.fromCode(statusValue) : TenantStatus.ACTIVE;
    }

    @Override
    public void setTenantStatus(String tenantId, TenantStatus status) {
        setConfig(tenantId, TenantConfigKey.TENANT_STATUS, status.getCode());
    }

    @Override
    public List<String> getAllTenantIds() {
        return tenantConfigRepository.findDistinctTenantIds();
    }

    // ========== 配置验证操作 ==========

    @Override
    public boolean validateConfig(String configKey, String configValue, TenantConfigType configType) {
        if (configType == null) {
            return false;
        }
        return configType.isValidValue(configValue);
    }

    @Override
    public boolean validateConfig(TenantConfig config) {
        if (config == null || !config.isValid()) {
            return false;
        }
        return validateConfig(config.getConfigKey(), config.getConfigValue(), config.getConfigType());
    }

    // ========== 缓存管理操作 ==========

    @Override
    @CacheEvict(value = CACHE_NAME, key = "#tenantId + '*'")
    public void clearCache(String tenantId) {
        log.debug("Clearing cache for tenant: {}", tenantId);
    }

    @Override
    @CacheEvict(value = CACHE_NAME, key = "#tenantId + ':' + #configKey")
    public void clearCache(String tenantId, String configKey) {
        log.debug("Clearing cache for tenant config: tenant={}, key={}", tenantId, configKey);
    }

    @Override
    @CacheEvict(value = CACHE_NAME, allEntries = true)
    public void clearAllCache() {
        log.debug("Clearing all tenant config cache");
    }

    @Override
    public void refreshCache(String tenantId) {
        clearCache(tenantId);
        // 预加载常用配置
        getEnabledConfigs(tenantId);
        log.debug("Refreshed cache for tenant: {}", tenantId);
    }

    // ========== 配置监听操作 ==========

    @Override
    public void addConfigChangeListener(TenantConfigChangeListener listener) {
        if (listener != null && !changeListeners.contains(listener)) {
            changeListeners.add(listener);
            // 按优先级排序
            changeListeners.sort(Comparator.comparingInt(TenantConfigChangeListener::getPriority));
            log.info("Added config change listener: {}", listener.getName());
        }
    }

    @Override
    public void removeConfigChangeListener(TenantConfigChangeListener listener) {
        if (changeListeners.remove(listener)) {
            log.info("Removed config change listener: {}", listener.getName());
        }
    }

    /**
     * 通知租户配置清空
     */
    private void notifyTenantConfigCleared(String tenantId) {
        for (TenantConfigChangeListener listener : changeListeners) {
            try {
                if (listener.supports(tenantId)) {
                    listener.onTenantConfigCleared(tenantId);
                }
            } catch (Exception e) {
                log.warn("Error notifying tenant config cleared to listener {}: {}",
                        listener.getName(), e.getMessage());
            }
        }
    }

    // ========== 配置统计操作 ==========

    @Override
    public TenantConfigStats getConfigStats(String tenantId) {
        Object[] stats = tenantConfigRepository.getConfigStatsByTenantId(tenantId);
        if (stats == null || stats.length < 4) {
            return new TenantConfigStats(tenantId, 0, 0, 0, 0);
        }

        long totalCount = ((Number) stats[0]).longValue();
        long enabledCount = ((Number) stats[1]).longValue();
        long disabledCount = ((Number) stats[2]).longValue();
        long sensitiveCount = ((Number) stats[3]).longValue();

        TenantConfigStats configStats = new TenantConfigStats(tenantId, totalCount, enabledCount, disabledCount, sensitiveCount);

        // 获取租户名称
        String tenantName = getTenantName(tenantId);
        configStats.setTenantName(tenantName);

        // 获取最后更新时间
        Optional<LocalDateTime> lastUpdated = tenantConfigRepository.findLastUpdatedTimeByTenantId(tenantId);
        lastUpdated.ifPresent(configStats::setLastUpdatedTime);

        return configStats;
    }

    @Override
    public Map<String, TenantConfigStats> getAllConfigStats() {
        List<String> tenantIds = getAllTenantIds();
        Map<String, TenantConfigStats> statsMap = new HashMap<>();

        for (String tenantId : tenantIds) {
            TenantConfigStats stats = getConfigStats(tenantId);
            statsMap.put(tenantId, stats);
        }

        return statsMap;
    }

    // ========== 配置导入导出操作 ==========

    @Override
    public String exportConfigs(String tenantId, boolean includeSensitive) {
        List<TenantConfig> configs;
        if (includeSensitive) {
            configs = getAllConfigs(tenantId);
        } else {
            configs = tenantConfigRepository.findNonSensitiveConfigsByTenantId(tenantId);
        }

        try {
            Map<String, Object> exportData = new HashMap<>();
            exportData.put("tenantId", tenantId);
            exportData.put("exportTime", LocalDateTime.now());
            exportData.put("includeSensitive", includeSensitive);
            exportData.put("configCount", configs.size());

            Map<String, Object> configMap = new HashMap<>();
            for (TenantConfig config : configs) {
                Map<String, Object> configData = new HashMap<>();
                configData.put("value", config.getConfigValue());
                configData.put("type", config.getConfigType().getCode());
                configData.put("description", config.getDescription());
                configData.put("enabled", config.getEnabled());
                configData.put("sensitive", config.getSensitive());
                configMap.put(config.getConfigKey(), configData);
            }
            exportData.put("configs", configMap);

            return objectMapper.writeValueAsString(exportData);
        } catch (Exception e) {
            log.error("Failed to export configs for tenant: {}", tenantId, e);
            throw new RuntimeException("Failed to export configs", e);
        }
    }

    @Override
    @CacheEvict(value = CACHE_NAME, allEntries = true)
    public int importConfigs(String tenantId, String configJson, boolean overwrite) {
        try {
            Map<String, Object> importData = objectMapper.readValue(configJson, new TypeReference<Map<String, Object>>() {});

            @SuppressWarnings("unchecked")
            Map<String, Object> configMap = (Map<String, Object>) importData.get("configs");
            if (configMap == null) {
                return 0;
            }

            int importCount = 0;
            for (Map.Entry<String, Object> entry : configMap.entrySet()) {
                String configKey = entry.getKey();

                @SuppressWarnings("unchecked")
                Map<String, Object> configData = (Map<String, Object>) entry.getValue();

                // 检查是否已存在
                if (!overwrite && tenantConfigRepository.existsByTenantIdAndConfigKey(tenantId, configKey)) {
                    continue;
                }

                String configValue = (String) configData.get("value");
                String typeCode = (String) configData.get("type");
                String description = (String) configData.get("description");
                Boolean enabled = (Boolean) configData.getOrDefault("enabled", true);
                Boolean sensitive = (Boolean) configData.getOrDefault("sensitive", false);

                TenantConfigType configType = TenantConfigType.fromCode(typeCode);

                TenantConfig config = TenantConfig.builder()
                        .tenantId(tenantId)
                        .configKey(configKey)
                        .configValue(configValue)
                        .configType(configType)
                        .description(description)
                        .enabled(enabled)
                        .sensitive(sensitive)
                        .build();

                tenantConfigRepository.save(config);
                importCount++;
            }

            if (importCount > 0) {
                notifyBatchConfigChanged(tenantId, importCount);
                log.info("Imported {} configs for tenant: {}", importCount, tenantId);
            }

            return importCount;
        } catch (Exception e) {
            log.error("Failed to import configs for tenant: {}", tenantId, e);
            throw new RuntimeException("Failed to import configs", e);
        }
    }

    @Override
    @CacheEvict(value = CACHE_NAME, allEntries = true)
    public int copyConfigs(String sourceTenantId, String targetTenantId, boolean overwrite) {
        List<TenantConfig> sourceConfigs = getAllConfigs(sourceTenantId);
        if (sourceConfigs.isEmpty()) {
            return 0;
        }

        int copyCount = 0;
        for (TenantConfig sourceConfig : sourceConfigs) {
            // 检查是否已存在
            if (!overwrite && tenantConfigRepository.existsByTenantIdAndConfigKey(targetTenantId, sourceConfig.getConfigKey())) {
                continue;
            }

            TenantConfig targetConfig = sourceConfig.copy();
            targetConfig.setTenantId(targetTenantId);
            targetConfig.setId(null); // 重置ID
            targetConfig.setCreatedAt(LocalDateTime.now());
            targetConfig.setUpdatedAt(LocalDateTime.now());

            tenantConfigRepository.save(targetConfig);
            copyCount++;
        }

        if (copyCount > 0) {
            notifyBatchConfigChanged(targetTenantId, copyCount);
            log.info("Copied {} configs from tenant {} to tenant {}", copyCount, sourceTenantId, targetTenantId);
        }

        return copyCount;
    }
}
