package cn.com.handthing.starter.tenant.filter;

import cn.com.handthing.starter.tenant.context.TenantContext;
import cn.com.handthing.starter.tenant.context.TenantContextHolder;
import cn.com.handthing.starter.tenant.context.TenantStatus;
import cn.com.handthing.starter.tenant.resolver.TenantResolver;
import cn.com.handthing.starter.tenant.service.TenantConfigService;
import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.Ordered;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.List;
import java.util.Arrays;

/**
 * 租户解析过滤器
 * <p>
 * 高优先级Servlet过滤器，在Spring Security之前执行，用于在请求早期识别租户。
 * 委托给TenantResolver策略进行租户识别，设置和清理TenantContextHolder。
 * </p>
 *
 * <AUTHOR>
 * @since V3.0.0
 */
public class TenantResolverFilter implements Filter, Ordered {

    private static final Logger log = LoggerFactory.getLogger(TenantResolverFilter.class);

    /**
     * 过滤器顺序 - 在Spring Security之前执行
     */
    private static final int DEFAULT_ORDER = -200;

    /**
     * 租户解析器列表
     */
    private final List<TenantResolver> tenantResolvers;

    /**
     * 租户配置服务
     */
    private final TenantConfigService tenantConfigService;

    /**
     * 默认租户ID
     */
    private final String defaultTenantId;

    /**
     * 是否要求必须有租户
     */
    private final boolean tenantRequired;

    /**
     * 过滤器顺序
     */
    private final int order;

    /**
     * 排除路径列表
     */
    private final List<String> excludePaths;

    /**
     * 构造函数
     *
     * @param tenantResolvers     租户解析器列表
     * @param tenantConfigService 租户配置服务
     * @param defaultTenantId     默认租户ID
     * @param tenantRequired      是否要求必须有租户
     */
    public TenantResolverFilter(List<TenantResolver> tenantResolvers,
                                TenantConfigService tenantConfigService,
                                String defaultTenantId,
                                boolean tenantRequired) {
        this(tenantResolvers, tenantConfigService, defaultTenantId, tenantRequired, DEFAULT_ORDER, Arrays.asList());
    }

    /**
     * 构造函数（指定顺序）
     *
     * @param tenantResolvers     租户解析器列表
     * @param tenantConfigService 租户配置服务
     * @param defaultTenantId     默认租户ID
     * @param tenantRequired      是否要求必须有租户
     * @param order               过滤器顺序
     */
    public TenantResolverFilter(List<TenantResolver> tenantResolvers,
                                TenantConfigService tenantConfigService,
                                String defaultTenantId,
                                boolean tenantRequired,
                                int order) {
        this(tenantResolvers, tenantConfigService, defaultTenantId, tenantRequired, order, Arrays.asList());
    }

    /**
     * 构造函数（完整参数）
     *
     * @param tenantResolvers     租户解析器列表
     * @param tenantConfigService 租户配置服务
     * @param defaultTenantId     默认租户ID
     * @param tenantRequired      是否要求必须有租户
     * @param order               过滤器顺序
     * @param excludePaths        排除路径列表
     */
    public TenantResolverFilter(List<TenantResolver> tenantResolvers,
                                TenantConfigService tenantConfigService,
                                String defaultTenantId,
                                boolean tenantRequired,
                                int order,
                                List<String> excludePaths) {
        this.tenantResolvers = tenantResolvers;
        this.tenantConfigService = tenantConfigService;
        this.defaultTenantId = defaultTenantId;
        this.tenantRequired = tenantRequired;
        this.order = order;
        this.excludePaths = excludePaths != null ? excludePaths : Arrays.asList();

        log.info("TenantResolverFilter initialized with {} resolvers, defaultTenant: {}, required: {}, order: {}, excludePaths: {}",
                tenantResolvers.size(), defaultTenantId, tenantRequired, order, this.excludePaths);
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {

        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;

        // 检查是否为排除路径
        if (isExcludedPath(httpRequest)) {
            chain.doFilter(request, response);
            return;
        }

        try {
            // 解析租户ID
            String tenantId = resolveTenantId(httpRequest);

            if (tenantId != null) {
                // 验证租户并设置上下文
                if (validateAndSetTenantContext(tenantId, httpRequest, httpResponse)) {
                    log.debug("Tenant context set successfully: {}", tenantId);
                } else {
                    // 租户验证失败
                    return;
                }
            } else if (tenantRequired) {
                // 要求必须有租户但未找到
                handleMissingTenant(httpRequest, httpResponse);
                return;
            } else if (defaultTenantId != null) {
                // 使用默认租户
                if (validateAndSetTenantContext(defaultTenantId, httpRequest, httpResponse)) {
                    log.debug("Default tenant context set: {}", defaultTenantId);
                } else {
                    return;
                }
            }

            // 继续过滤器链
            chain.doFilter(request, response);

        } finally {
            // 清理租户上下文
            TenantContextHolder.clearContext();
            log.debug("Tenant context cleared");
        }
    }

    /**
     * 检查是否为排除路径
     *
     * @param request HTTP请求
     * @return 如果是排除路径返回true，否则返回false
     */
    private boolean isExcludedPath(HttpServletRequest request) {
        String requestPath = getRequestPath(request);

        for (String excludePath : excludePaths) {
            if (isPathMatched(requestPath, excludePath)) {
                log.debug("Request path '{}' matches exclude pattern '{}'", requestPath, excludePath);
                return true;
            }
        }

        return false;
    }

    /**
     * 获取请求路径
     *
     * @param request HTTP请求
     * @return 请求路径
     */
    private String getRequestPath(HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        String contextPath = request.getContextPath();

        if (StringUtils.hasText(contextPath) && requestURI.startsWith(contextPath)) {
            return requestURI.substring(contextPath.length());
        }

        return requestURI;
    }

    /**
     * 检查路径是否匹配模式
     *
     * @param path    请求路径
     * @param pattern 匹配模式
     * @return 如果匹配返回true，否则返回false
     */
    private boolean isPathMatched(String path, String pattern) {
        if (pattern.endsWith("/**")) {
            String prefix = pattern.substring(0, pattern.length() - 3);
            return path.startsWith(prefix);
        } else if (pattern.endsWith("/*")) {
            String prefix = pattern.substring(0, pattern.length() - 2);
            return path.startsWith(prefix) && !path.substring(prefix.length()).contains("/");
        } else {
            return path.equals(pattern);
        }
    }

    /**
     * 解析租户ID
     *
     * @param request HTTP请求
     * @return 租户ID，如果无法解析则返回null
     */
    private String resolveTenantId(HttpServletRequest request) {
        for (TenantResolver resolver : tenantResolvers) {
            try {
                String tenantId = resolver.resolve(request);
                if (StringUtils.hasText(tenantId)) {
                    log.debug("Tenant ID '{}' resolved by {}", tenantId, resolver.getName());
                    return tenantId;
                }
            } catch (Exception e) {
                log.warn("Error resolving tenant with {}: {}", resolver.getName(), e.getMessage());
            }
        }

        log.debug("No tenant ID resolved from request");
        return null;
    }

    /**
     * 验证租户并设置上下文
     *
     * @param tenantId     租户ID
     * @param request      HTTP请求
     * @param response     HTTP响应
     * @return 如果验证成功返回true，否则返回false
     */
    private boolean validateAndSetTenantContext(String tenantId, HttpServletRequest request, HttpServletResponse response)
            throws IOException {

        try {
            // 检查租户是否存在和有效
            if (!tenantConfigService.tenantExists(tenantId)) {
                log.warn("Tenant not found: {}", tenantId);
                handleInvalidTenant(tenantId, "Tenant not found", request, response);
                return false;
            }

            // 获取租户状态
            TenantStatus tenantStatus = tenantConfigService.getTenantStatus(tenantId);
            if (tenantStatus != null && !tenantStatus.canAuthenticate()) {
                log.warn("Tenant {} is not in authenticatable status: {}", tenantId, tenantStatus);
                handleInvalidTenant(tenantId, "Tenant is " + tenantStatus.getDescription(), request, response);
                return false;
            }

            // 创建并设置租户上下文
            TenantContext tenantContext = createTenantContext(tenantId);
            TenantContextHolder.setContext(tenantContext);

            // 设置请求属性
            request.setAttribute("TENANT_ID", tenantId);
            request.setAttribute("TENANT_CONTEXT", tenantContext);

            return true;

        } catch (Exception e) {
            log.error("Error validating tenant {}: {}", tenantId, e.getMessage(), e);
            handleInvalidTenant(tenantId, "Tenant validation error", request, response);
            return false;
        }
    }

    /**
     * 创建租户上下文
     *
     * @param tenantId 租户ID
     * @return 租户上下文
     */
    private TenantContext createTenantContext(String tenantId) {
        try {
            // 从配置服务获取租户信息
            String tenantName = tenantConfigService.getTenantName(tenantId);
            TenantStatus tenantStatus = tenantConfigService.getTenantStatus(tenantId);

            TenantContext context = new TenantContext(tenantId, tenantName, tenantStatus);
            
            // 添加额外的租户信息
            context.addAttribute("resolved_at", System.currentTimeMillis());
            
            return context;

        } catch (Exception e) {
            log.warn("Error creating tenant context for {}, using basic context: {}", tenantId, e.getMessage());
            return new TenantContext(tenantId);
        }
    }

    /**
     * 处理缺失租户的情况
     *
     * @param request  HTTP请求
     * @param response HTTP响应
     */
    private void handleMissingTenant(HttpServletRequest request, HttpServletResponse response) throws IOException {
        log.warn("Tenant required but not found in request: {}", request.getRequestURI());
        
        response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
        response.setContentType("application/json;charset=UTF-8");
        response.getWriter().write("{\"error\":\"TENANT_REQUIRED\",\"message\":\"Tenant identification is required\"}");
    }

    /**
     * 处理无效租户的情况
     *
     * @param tenantId 租户ID
     * @param reason   失败原因
     * @param request  HTTP请求
     * @param response HTTP响应
     */
    private void handleInvalidTenant(String tenantId, String reason, HttpServletRequest request, HttpServletResponse response)
            throws IOException {
        
        log.warn("Invalid tenant '{}' for request {}: {}", tenantId, request.getRequestURI(), reason);
        
        response.setStatus(HttpServletResponse.SC_FORBIDDEN);
        response.setContentType("application/json;charset=UTF-8");
        response.getWriter().write(String.format(
                "{\"error\":\"INVALID_TENANT\",\"message\":\"%s\",\"tenant\":\"%s\"}", 
                reason, tenantId));
    }

    @Override
    public int getOrder() {
        return order;
    }

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        log.info("TenantResolverFilter initialized");
    }

    @Override
    public void destroy() {
        log.info("TenantResolverFilter destroyed");
    }

    /**
     * 获取租户解析器列表
     *
     * @return 租户解析器列表
     */
    public List<TenantResolver> getTenantResolvers() {
        return tenantResolvers;
    }

    /**
     * 获取默认租户ID
     *
     * @return 默认租户ID
     */
    public String getDefaultTenantId() {
        return defaultTenantId;
    }

    /**
     * 是否要求必须有租户
     *
     * @return 如果要求必须有租户返回true，否则返回false
     */
    public boolean isTenantRequired() {
        return tenantRequired;
    }
}
