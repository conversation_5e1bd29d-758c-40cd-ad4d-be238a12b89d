package cn.com.handthing.starter.crypto.core;

/**
 * 对称加密器接口。
 *
 * <AUTHOR>
 * @since V1.0.0
 */
public interface SymmetricEncryptor extends CryptoService {

    /**
     * 加密给定的明文字符串。
     *
     * @param plaintext 待加密的明文字符串 (UTF-8编码)
     * @return Base64 编码的密文字符串
     */
    String encrypt(String plaintext);

    /**
     * 解密给定的密文字符串。
     *
     * @param ciphertext Base64 编码的密文字符串
     * @return 解密后的明文字符串 (UTF-8编码)
     */
    String decrypt(String ciphertext);
}