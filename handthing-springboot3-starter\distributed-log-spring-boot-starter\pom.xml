<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.com.handthing.springboot3.starter</groupId>
        <artifactId>starter-parent</artifactId>
        <version>${revision}</version>
        <relativePath>../starter-parent</relativePath>
    </parent>

    <artifactId>distributed-log-spring-boot-starter</artifactId>
    <name>HandThing :: Distributed Log Starter</name>
    <description>提供分布式链路追踪、统一日志格式和文件管理的日志 Starter</description>

    <properties>
    </properties>

    <dependencies>
        <!-- Spring Boot 自动配置核心 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-autoconfigure</artifactId>
        </dependency>

        <!-- Spring Boot 配置属性处理器 (生成 spring-configuration-metadata.json) -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- Lombok 用于 @HandthingLog 注解的实现 -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope> <!-- 仅编译时需要，由最终用户提供 -->
        </dependency>

        <!--
          引入 Logback 作为日志实现。
          spring-boot-starter-web 或 spring-boot-starter-logging 已包含,
          这里显式声明以确保模块独立性。
        -->
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
        </dependency>

        <!-- Logstash Encoder for JSON logging -->
        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
            <optional>true</optional> <!-- JSON 日志为可选功能 -->
        </dependency>

        <!-- Janino for logback conditional processing -->
        <dependency>
            <groupId>org.codehaus.janino</groupId>
            <artifactId>janino</artifactId>
            <optional>true</optional> <!-- 条件处理为可选功能 -->
        </dependency>

        <!--
          Micrometer Tracing for TraceID.
          使用者需要引入 spring-boot-starter-actuator 和具体的追踪实现(如 brave)。
          这里只声明 optional，确保编码时可用，但不强制传递依赖。
        -->
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-tracing</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- 测试依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>