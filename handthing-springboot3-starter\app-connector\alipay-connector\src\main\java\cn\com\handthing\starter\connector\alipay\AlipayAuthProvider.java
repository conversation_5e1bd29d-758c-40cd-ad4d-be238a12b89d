package cn.com.handthing.starter.connector.alipay;

import cn.com.handthing.starter.connector.PlatformType;
import cn.com.handthing.starter.connector.alipay.config.AlipayConfig;
import cn.com.handthing.starter.connector.alipay.model.AlipayAccessTokenResponse;
import cn.com.handthing.starter.connector.alipay.model.AlipayUserInfoResponse;
import cn.com.handthing.starter.connector.alipay.util.AlipaySignatureUtil;
import cn.com.handthing.starter.connector.auth.AuthProvider;
import cn.com.handthing.starter.connector.auth.UnifiedAccessToken;
import cn.com.handthing.starter.connector.auth.UnifiedUserInfo;
import cn.com.handthing.starter.connector.exception.AuthException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.TreeMap;

/**
 * 支付宝认证提供者
 * <p>
 * 实现支付宝OAuth2.0认证流程，包括授权URL生成、Token交换、用户信息获取等功能。
 * 支持支付宝开放平台的网页授权和移动端授权。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "handthing.connector.alipay", name = "enabled", havingValue = "true")
public class AlipayAuthProvider implements AuthProvider {

    private final AlipayConfig config;
    private final RestTemplate restTemplate;

    // 支付宝API端点
    private static final String AUTH_URL = "https://openauth.alipay.com/oauth2/publicAppAuthorize.htm";
    private static final String GATEWAY_URL = "https://openapi.alipay.com/gateway.do";

    @Override
    public PlatformType getPlatformType() {
        return PlatformType.ALIPAY;
    }

    @Override
    public String getAuthorizationUrl(String state, String redirectUri) throws AuthException {
        try {
            String effectiveRedirectUri = config.getEffectiveRedirectUri(redirectUri);
            String effectiveScope = config.getEffectiveScope("auth_user");
            
            return UriComponentsBuilder.fromHttpUrl(AUTH_URL)
                    .queryParam("app_id", config.getAppId())
                    .queryParam("scope", effectiveScope)
                    .queryParam("redirect_uri", URLEncoder.encode(effectiveRedirectUri, StandardCharsets.UTF_8))
                    .queryParam("state", state != null ? state : "")
                    .build()
                    .toUriString();
                    
        } catch (Exception e) {
            log.error("Failed to generate Alipay authorization URL", e);
            throw AuthException.configurationError(PlatformType.ALIPAY, "Failed to generate authorization URL: " + e.getMessage());
        }
    }

    @Override
    public UnifiedAccessToken exchangeToken(String code, String state, String redirectUri) throws AuthException {
        try {
            // 1. 使用授权码获取访问令牌
            AlipayAccessTokenResponse tokenResponse = getAccessToken(code);
            
            // 2. 构建统一访问令牌
            return UnifiedAccessToken.builder()
                    .platform(PlatformType.ALIPAY)
                    .accessToken(tokenResponse.getAccessToken())
                    .refreshToken(tokenResponse.getRefreshToken())
                    .tokenType("Bearer")
                    .expiresIn(tokenResponse.getExpiresIn())
                    .scope(tokenResponse.getScope())
                    .userId(tokenResponse.getUserId())
                    .createdAt(LocalDateTime.now())
                    .expiresAt(LocalDateTime.now().plusSeconds(tokenResponse.getExpiresIn()))
                    .extraInfo(Map.of(
                            "reExpiresIn", tokenResponse.getReExpiresIn() != null ? tokenResponse.getReExpiresIn() : 0L,
                            "authStart", tokenResponse.getAuthStart() != null ? tokenResponse.getAuthStart() : ""
                    ))
                    .build();
                    
        } catch (Exception e) {
            log.error("Failed to exchange Alipay token for code: {}", code, e);
            if (e instanceof AuthException) {
                throw e;
            }
            throw AuthException.networkError(PlatformType.ALIPAY, e);
        }
    }

    @Override
    public UnifiedAccessToken refreshToken(String refreshToken) throws AuthException {
        try {
            // 构建刷新令牌请求参数
            Map<String, String> bizParams = new TreeMap<>();
            bizParams.put("method", "alipay.system.oauth.token");
            bizParams.put("app_id", config.getAppId());
            bizParams.put("charset", "UTF-8");
            bizParams.put("sign_type", config.getSignType());
            bizParams.put("timestamp", String.valueOf(System.currentTimeMillis()));
            bizParams.put("version", "1.0");
            bizParams.put("grant_type", "refresh_token");
            bizParams.put("refresh_token", refreshToken);
            
            // 生成签名
            String sign;
            try {
                sign = AlipaySignatureUtil.generateSignature(bizParams, config.getPrivateKey(), config.getSignType());
            } catch (Exception e) {
                throw new AuthException("Failed to generate signature for refresh token", AuthException.CONFIGURATION_ERROR, PlatformType.ALIPAY);
            }
            bizParams.put("sign", sign);
            
            // 构建请求
            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
            bizParams.forEach(params::add);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            
            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);
            
            ResponseEntity<AlipayAccessTokenResponse> response = restTemplate.postForEntity(
                    GATEWAY_URL, request, AlipayAccessTokenResponse.class);
            
            AlipayAccessTokenResponse tokenResponse = response.getBody();
            
            if (tokenResponse == null || !tokenResponse.isSuccess()) {
                throw new AuthException("Failed to refresh token: " + 
                        (tokenResponse != null ? tokenResponse.getErrorDescription() : "No response"),
                        AuthException.REFRESH_TOKEN_EXPIRED, PlatformType.ALIPAY);
            }
            
            return UnifiedAccessToken.builder()
                    .platform(PlatformType.ALIPAY)
                    .accessToken(tokenResponse.getAccessToken())
                    .refreshToken(tokenResponse.getRefreshToken())
                    .tokenType("Bearer")
                    .expiresIn(tokenResponse.getExpiresIn())
                    .scope(tokenResponse.getScope())
                    .userId(tokenResponse.getUserId())
                    .createdAt(LocalDateTime.now())
                    .expiresAt(LocalDateTime.now().plusSeconds(tokenResponse.getExpiresIn()))
                    .build();
                    
        } catch (Exception e) {
            log.error("Failed to refresh Alipay token", e);
            if (e instanceof AuthException) {
                throw e;
            }
            throw AuthException.networkError(PlatformType.ALIPAY, e);
        }
    }

    @Override
    public UnifiedUserInfo getUserInfo(String accessToken) throws AuthException {
        try {
            // 构建获取用户信息请求参数
            Map<String, String> bizParams = new TreeMap<>();
            bizParams.put("method", "alipay.user.info.share");
            bizParams.put("app_id", config.getAppId());
            bizParams.put("charset", "UTF-8");
            bizParams.put("sign_type", config.getSignType());
            bizParams.put("timestamp", String.valueOf(System.currentTimeMillis()));
            bizParams.put("version", "1.0");
            bizParams.put("auth_token", accessToken);
            
            // 生成签名
            String sign;
            try {
                sign = AlipaySignatureUtil.generateSignature(bizParams, config.getPrivateKey(), config.getSignType());
            } catch (Exception e) {
                throw new AuthException("Failed to generate signature for user info", AuthException.CONFIGURATION_ERROR, PlatformType.ALIPAY);
            }
            bizParams.put("sign", sign);
            
            // 构建请求URL
            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(GATEWAY_URL);
            bizParams.forEach(builder::queryParam);
            
            ResponseEntity<AlipayUserInfoResponse> response = restTemplate.getForEntity(
                    builder.build().toUriString(), AlipayUserInfoResponse.class);
            
            AlipayUserInfoResponse userInfoResponse = response.getBody();
            
            if (userInfoResponse == null || !userInfoResponse.isSuccess()) {
                throw new AuthException("Failed to get user info: " + 
                        (userInfoResponse != null ? userInfoResponse.getErrorDescription() : "No response"),
                        AuthException.INVALID_TOKEN, PlatformType.ALIPAY);
            }
            
            AlipayUserInfoResponse.UserInfo userInfo = userInfoResponse.getAlipayUserInfoShareResponse();
            
            return UnifiedUserInfo.builder()
                    .platform(PlatformType.ALIPAY)
                    .userId(userInfo.getUserId())
                    .openId(userInfo.getUserId()) // 支付宝使用userId作为openId
                    .nickname(userInfo.getNickName())
                    .avatar(userInfo.getAvatar())
                    .gender(parseGender(userInfo.getGender()))
                    .province(userInfo.getProvince())
                    .city(userInfo.getCity())
                    .status(userInfo.getUserStatus())
                    .fetchedAt(LocalDateTime.now())
                    .extraInfo(Map.of(
                            "userType", userInfo.getUserType() != null ? userInfo.getUserType() : "",
                            "isStudentCertified", userInfo.getIsStudentCertified() != null ? userInfo.getIsStudentCertified() : "",
                            "isCertified", userInfo.getIsCertified() != null ? userInfo.getIsCertified() : ""
                    ))
                    .build();
                    
        } catch (Exception e) {
            log.error("Failed to get Alipay user info", e);
            if (e instanceof AuthException) {
                throw e;
            }
            throw AuthException.networkError(PlatformType.ALIPAY, e);
        }
    }

    @Override
    public boolean supportsRefreshToken() {
        return true; // 支付宝支持刷新令牌
    }

    @Override
    public String getDefaultScope() {
        return "auth_user";
    }

    /**
     * 获取访问令牌
     */
    private AlipayAccessTokenResponse getAccessToken(String code) throws AuthException {
        try {
            // 构建请求参数
            Map<String, String> bizParams = new TreeMap<>();
            bizParams.put("method", "alipay.system.oauth.token");
            bizParams.put("app_id", config.getAppId());
            bizParams.put("charset", "UTF-8");
            bizParams.put("sign_type", config.getSignType());
            bizParams.put("timestamp", String.valueOf(System.currentTimeMillis()));
            bizParams.put("version", "1.0");
            bizParams.put("grant_type", "authorization_code");
            bizParams.put("code", code);
            
            // 生成签名
            String sign;
            try {
                sign = AlipaySignatureUtil.generateSignature(bizParams, config.getPrivateKey(), config.getSignType());
            } catch (Exception e) {
                throw new AuthException("Failed to generate signature for access token", AuthException.CONFIGURATION_ERROR, PlatformType.ALIPAY);
            }
            bizParams.put("sign", sign);
            
            // 构建请求
            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
            bizParams.forEach(params::add);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            
            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);
            
            ResponseEntity<AlipayAccessTokenResponse> response = restTemplate.postForEntity(
                    GATEWAY_URL, request, AlipayAccessTokenResponse.class);
            
            AlipayAccessTokenResponse tokenResponse = response.getBody();
            
            if (tokenResponse == null || !tokenResponse.isSuccess()) {
                throw new AuthException("Failed to get access token: " + 
                        (tokenResponse != null ? tokenResponse.getErrorDescription() : "No response"),
                        AuthException.INVALID_CODE, PlatformType.ALIPAY);
            }
            
            return tokenResponse;
            
        } catch (Exception e) {
            log.error("Failed to get Alipay access token for code: {}", code, e);
            throw AuthException.networkError(PlatformType.ALIPAY, e);
        }
    }

    /**
     * 解析性别
     */
    private Integer parseGender(String gender) {
        if (gender == null) return 0;
        // 支付宝：M-男，F-女
        switch (gender.toUpperCase()) {
            case "M":
                return 1;
            case "F":
                return 2;
            default:
                return 0;
        }
    }
}
