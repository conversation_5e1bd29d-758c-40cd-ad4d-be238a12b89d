package cn.com.handthing.starter.httpclient;

import cn.com.handthing.starter.httpclient.config.EndpointConfig;
import cn.com.handthing.starter.httpclient.config.HttpClientProperties;
import cn.com.handthing.starter.httpclient.crypto.NoOpCryptoProvider;
import cn.com.handthing.starter.httpclient.internal.EndpointConfigResolver;
import cn.com.handthing.starter.httpclient.internal.WebClientHandthingHttpClient;
import org.junit.jupiter.api.Test;
import org.springframework.boot.autoconfigure.AutoConfigurations;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.context.runner.ApplicationContextRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.web.reactive.function.client.WebClient;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * WebClient HTTP 自动配置测试
 * 
 * <AUTHOR>
 * @since V1.0.0
 */
class WebClientHttpAutoConfigurationTest {

    private final ApplicationContextRunner contextRunner = new ApplicationContextRunner()
            .withConfiguration(AutoConfigurations.of(
                    HttpCoreAutoConfiguration.class,
                    WebClientHttpAutoConfiguration.class
            ))
            .withUserConfiguration(TestConfig.class)
            .withPropertyValues(
                    "handthing.http-client.enabled=true"
            );

    @Test
    void testAutoConfigurationEnabled() {
        contextRunner.run(context -> {
            assertThat(context).hasSingleBean(HandthingHttpClient.class);
            assertThat(context).hasSingleBean(WebClientHandthingHttpClient.class);
            assertThat(context).hasSingleBean(WebClient.Builder.class);
        });
    }

    @Test
    void testAutoConfigurationDisabled() {
        contextRunner
                .withPropertyValues("handthing.http-client.enabled=false")
                .run(context -> {
                    assertThat(context).doesNotHaveBean(HandthingHttpClient.class);
                    assertThat(context).doesNotHaveBean(WebClientHandthingHttpClient.class);
                });
    }

    @Test
    void testWebClientImplementation() {
        contextRunner.run(context -> {
            HandthingHttpClient httpClient = context.getBean(HandthingHttpClient.class);
            assertThat(httpClient).isInstanceOf(WebClientHandthingHttpClient.class);
        });
    }

    @TestConfiguration
    static class TestConfig {
        
        @Bean
        public EndpointConfigResolver endpointConfigResolver() {
            HttpClientProperties properties = new HttpClientProperties();
            return new EndpointConfigResolver(properties);
        }
        
        @Bean("handthingNoOpRequestEncryptor")
        public NoOpCryptoProvider requestEncryptor() {
            return new NoOpCryptoProvider();
        }
        
        @Bean("handthingNoOpResponseDecryptor")
        public NoOpCryptoProvider responseDecryptor() {
            return new NoOpCryptoProvider();
        }
    }
}
