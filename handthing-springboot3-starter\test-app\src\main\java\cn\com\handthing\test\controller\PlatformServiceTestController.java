package cn.com.handthing.test.controller;

import cn.com.handthing.starter.connector.alipay.AlipayService;
import cn.com.handthing.starter.connector.dingtalk.DingTalkService;
import cn.com.handthing.starter.connector.douyin.DouyinService;
import cn.com.handthing.starter.connector.wecom.WeComService;
import cn.com.handthing.starter.connector.wechat.WeChatService;
import cn.com.handthing.starter.connector.feishu.FeishuService;
import cn.com.handthing.starter.connector.toutiao.ToutiaoService;
import cn.com.handthing.starter.connector.xiaohongshu.XiaoHongShuService;
import cn.com.handthing.starter.connector.bilibili.BilibiliService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 平台服务测试控制器
 * <p>
 * 提供各个平台服务的状态检查和功能测试接口。
 * </p>
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/test/platform-services")
public class PlatformServiceTestController {

    @Autowired(required = false)
    private WeComService weComService;

    @Autowired(required = false)
    private DingTalkService dingTalkService;

    @Autowired(required = false)
    private DouyinService douyinService;

    @Autowired(required = false)
    private AlipayService alipayService;

    @Autowired(required = false)
    private WeChatService weChatService;

    @Autowired(required = false)
    private FeishuService feishuService;

    @Autowired(required = false)
    private ToutiaoService toutiaoService;

    @Autowired(required = false)
    private XiaoHongShuService xiaoHongShuService;

    @Autowired(required = false)
    private BilibiliService bilibiliService;

    /**
     * 获取所有平台服务状态
     *
     * @return 平台服务状态
     */
    @GetMapping("/status")
    public Map<String, Object> getAllPlatformStatus() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            Map<String, Object> platforms = new HashMap<>();
            
            // 企业微信服务状态
            if (weComService != null) {
                try {
                    WeComService.ServiceStatus status = weComService.getStatus();
                    platforms.put("wecom", Map.of(
                            "available", true,
                            "enabled", status.isEnabled(),
                            "valid", status.isValid(),
                            "corpId", status.getCorpId(),
                            "agentId", status.getAgentId(),
                            "apiBaseUrl", status.getApiBaseUrl()
                    ));
                } catch (Exception e) {
                    platforms.put("wecom", Map.of(
                            "available", false,
                            "error", e.getMessage()
                    ));
                }
            } else {
                platforms.put("wecom", Map.of("available", false, "reason", "Service not configured"));
            }
            
            // 钉钉服务状态
            if (dingTalkService != null) {
                try {
                    DingTalkService.ServiceStatus status = dingTalkService.getStatus();
                    platforms.put("dingtalk", Map.of(
                            "available", true,
                            "enabled", status.isEnabled(),
                            "valid", status.isValid(),
                            "appKey", status.getAppKey(),
                            "corpId", status.getCorpId(),
                            "apiBaseUrl", status.getApiBaseUrl()
                    ));
                } catch (Exception e) {
                    platforms.put("dingtalk", Map.of(
                            "available", false,
                            "error", e.getMessage()
                    ));
                }
            } else {
                platforms.put("dingtalk", Map.of("available", false, "reason", "Service not configured"));
            }
            
            // 抖音服务状态
            if (douyinService != null) {
                try {
                    DouyinService.ServiceStatus status = douyinService.getStatus();
                    platforms.put("douyin", Map.of(
                            "available", true,
                            "enabled", status.isEnabled(),
                            "valid", status.isValid(),
                            "clientKey", status.getClientKey(),
                            "apiBaseUrl", status.getApiBaseUrl()
                    ));
                } catch (Exception e) {
                    platforms.put("douyin", Map.of(
                            "available", false,
                            "error", e.getMessage()
                    ));
                }
            } else {
                platforms.put("douyin", Map.of("available", false, "reason", "Service not configured"));
            }
            
            // 支付宝服务状态
            if (alipayService != null) {
                try {
                    AlipayService.ServiceStatus status = alipayService.getStatus();
                    platforms.put("alipay", Map.of(
                            "available", true,
                            "enabled", status.isEnabled(),
                            "valid", status.isValid(),
                            "appId", status.getAppId(),
                            "sandboxMode", status.isSandboxMode(),
                            "gatewayUrl", status.getGatewayUrl()
                    ));
                } catch (Exception e) {
                    platforms.put("alipay", Map.of(
                            "available", false,
                            "error", e.getMessage()
                    ));
                }
            } else {
                platforms.put("alipay", Map.of("available", false, "reason", "Service not configured"));
            }

            // 微信服务状态
            if (weChatService != null) {
                try {
                    WeChatService.ServiceStatus status = weChatService.getStatus();
                    platforms.put("wechat", Map.of(
                            "available", true,
                            "enabled", status.isEnabled(),
                            "valid", status.isValid(),
                            "appId", status.getAppId(),
                            "accountType", status.getAccountType(),
                            "miniProgramMode", status.isMiniProgramMode(),
                            "sandboxMode", status.isSandboxMode(),
                            "apiBaseUrl", status.getApiBaseUrl()
                    ));
                } catch (Exception e) {
                    platforms.put("wechat", Map.of(
                            "available", false,
                            "error", e.getMessage()
                    ));
                }
            } else {
                platforms.put("wechat", Map.of("available", false, "reason", "Service not configured"));
            }

            // 飞书服务状态
            if (feishuService != null) {
                try {
                    FeishuService.ServiceStatus status = feishuService.getStatus();
                    platforms.put("feishu", Map.of(
                            "available", true,
                            "enabled", status.isEnabled(),
                            "valid", status.isValid(),
                            "appId", status.getAppId(),
                            "appType", status.getAppType(),
                            "thirdPartyMode", status.isThirdPartyMode(),
                            "botEnabled", status.isBotEnabled(),
                            "larkMode", status.isLarkMode(),
                            "apiBaseUrl", status.getApiBaseUrl()
                    ));
                } catch (Exception e) {
                    platforms.put("feishu", Map.of(
                            "available", false,
                            "error", e.getMessage()
                    ));
                }
            } else {
                platforms.put("feishu", Map.of("available", false, "reason", "Service not configured"));
            }

            // 今日头条服务状态
            if (toutiaoService != null) {
                try {
                    ToutiaoService.ServiceStatus status = toutiaoService.getStatus();
                    platforms.put("toutiao", Map.of(
                            "available", true,
                            "enabled", status.isEnabled(),
                            "valid", status.isValid(),
                            "clientKey", status.getClientKey(),
                            "appType", status.getAppType(),
                            "enterpriseMode", status.isEnterpriseMode(),
                            "sandboxMode", status.isSandboxMode(),
                            "contentPublishEnabled", status.isContentPublishEnabled(),
                            "analyticsEnabled", status.isAnalyticsEnabled(),
                            "apiBaseUrl", status.getApiBaseUrl()
                    ));
                } catch (Exception e) {
                    platforms.put("toutiao", Map.of(
                            "available", false,
                            "error", e.getMessage()
                    ));
                }
            } else {
                platforms.put("toutiao", Map.of("available", false, "reason", "Service not configured"));
            }

            // 小红书服务状态
            if (xiaoHongShuService != null) {
                try {
                    XiaoHongShuService.ServiceStatus status = xiaoHongShuService.getStatus();
                    platforms.put("xiaohongshu", Map.of(
                            "available", true,
                            "enabled", status.isEnabled(),
                            "valid", status.isValid(),
                            "appKey", status.getAppKey(),
                            "appType", status.getAppType(),
                            "brandMode", status.isBrandMode(),
                            "sandboxMode", status.isSandboxMode(),
                            "contentPublishEnabled", status.isContentPublishEnabled(),
                            "communityEnabled", status.isCommunityEnabled(),
                            "apiBaseUrl", status.getApiBaseUrl()
                    ));
                } catch (Exception e) {
                    platforms.put("xiaohongshu", Map.of(
                            "available", false,
                            "error", e.getMessage()
                    ));
                }
            } else {
                platforms.put("xiaohongshu", Map.of("available", false, "reason", "Service not configured"));
            }

            // Bilibili服务状态
            if (bilibiliService != null) {
                try {
                    BilibiliService.ServiceStatus status = bilibiliService.getStatus();
                    platforms.put("bilibili", Map.of(
                            "available", true,
                            "enabled", status.isEnabled(),
                            "valid", status.isValid(),
                            "appKey", status.getAppKey(),
                            "appType", status.getAppType(),
                            "mcnMode", status.isMcnMode(),
                            "testMode", status.isTestMode(),
                            "videoUploadEnabled", status.isVideoUploadEnabled(),
                            "liveEnabled", status.isLiveEnabled(),
                            "apiBaseUrl", status.getApiBaseUrl()
                    ));
                } catch (Exception e) {
                    platforms.put("bilibili", Map.of(
                            "available", false,
                            "error", e.getMessage()
                    ));
                }
            } else {
                platforms.put("bilibili", Map.of("available", false, "reason", "Service not configured"));
            }

            result.put("success", true);
            result.put("platforms", platforms);
            result.put("timestamp", System.currentTimeMillis());
            
        } catch (Exception e) {
            log.error("Failed to get platform status", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    /**
     * 企业微信服务测试
     *
     * @return 测试结果
     */
    @GetMapping("/wecom/test")
    @ConditionalOnBean(WeComService.class)
    public Map<String, Object> testWeComService() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            if (weComService == null) {
                result.put("success", false);
                result.put("error", "WeComService not available");
                return result;
            }
            
            WeComService.ServiceStatus status = weComService.getStatus();
            
            result.put("success", true);
            result.put("service", "WeCom");
            result.put("status", status.toString());
            result.put("available", weComService.isAvailable());
            result.put("config", weComService.getConfig().getClass().getSimpleName());
            
            // 测试API可用性
            Map<String, Object> apis = new HashMap<>();
            apis.put("message", weComService.message() != null);
            apis.put("contact", weComService.contact() != null);
            apis.put("customer", weComService.customer() != null);
            result.put("apis", apis);
            
        } catch (Exception e) {
            log.error("Failed to test WeCom service", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    /**
     * 钉钉服务测试
     *
     * @return 测试结果
     */
    @GetMapping("/dingtalk/test")
    @ConditionalOnBean(DingTalkService.class)
    public Map<String, Object> testDingTalkService() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            if (dingTalkService == null) {
                result.put("success", false);
                result.put("error", "DingTalkService not available");
                return result;
            }
            
            DingTalkService.ServiceStatus status = dingTalkService.getStatus();
            
            result.put("success", true);
            result.put("service", "DingTalk");
            result.put("status", status.toString());
            result.put("available", dingTalkService.isAvailable());
            result.put("config", dingTalkService.getConfig().getClass().getSimpleName());
            
            // 测试API可用性
            Map<String, Object> apis = new HashMap<>();
            apis.put("message", dingTalkService.message() != null);
            apis.put("contact", dingTalkService.contact() != null);
            apis.put("todo", dingTalkService.todo() != null);
            result.put("apis", apis);
            
        } catch (Exception e) {
            log.error("Failed to test DingTalk service", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    /**
     * 抖音服务测试
     *
     * @return 测试结果
     */
    @GetMapping("/douyin/test")
    @ConditionalOnBean(DouyinService.class)
    public Map<String, Object> testDouyinService() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            if (douyinService == null) {
                result.put("success", false);
                result.put("error", "DouyinService not available");
                return result;
            }
            
            DouyinService.ServiceStatus status = douyinService.getStatus();
            
            result.put("success", true);
            result.put("service", "Douyin");
            result.put("status", status.toString());
            result.put("available", douyinService.isAvailable());
            result.put("config", douyinService.getConfig().getClass().getSimpleName());
            
            // 测试API可用性
            Map<String, Object> apis = new HashMap<>();
            apis.put("video", douyinService.video() != null);
            apis.put("data", douyinService.data() != null);
            result.put("apis", apis);
            
        } catch (Exception e) {
            log.error("Failed to test Douyin service", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    /**
     * 支付宝服务测试
     *
     * @return 测试结果
     */
    @GetMapping("/alipay/test")
    @ConditionalOnBean(AlipayService.class)
    public Map<String, Object> testAlipayService() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            if (alipayService == null) {
                result.put("success", false);
                result.put("error", "AlipayService not available");
                return result;
            }
            
            AlipayService.ServiceStatus status = alipayService.getStatus();
            
            result.put("success", true);
            result.put("service", "Alipay");
            result.put("status", status.toString());
            result.put("available", alipayService.isAvailable());
            result.put("config", alipayService.getConfig().getClass().getSimpleName());
            
            // 测试API可用性
            Map<String, Object> apis = new HashMap<>();
            apis.put("payment", alipayService.payment() != null);
            apis.put("miniProgram", alipayService.miniProgram() != null);
            result.put("apis", apis);
            
        } catch (Exception e) {
            log.error("Failed to test Alipay service", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    /**
     * 微信服务测试
     *
     * @return 测试结果
     */
    @GetMapping("/wechat/test")
    @ConditionalOnBean(WeChatService.class)
    public Map<String, Object> testWeChatService() {
        Map<String, Object> result = new HashMap<>();

        try {
            if (weChatService == null) {
                result.put("success", false);
                result.put("error", "WeChatService not available");
                return result;
            }

            WeChatService.ServiceStatus status = weChatService.getStatus();

            result.put("success", true);
            result.put("service", "WeChat");
            result.put("status", status.toString());
            result.put("available", weChatService.isAvailable());
            result.put("config", weChatService.getConfig().getClass().getSimpleName());

            // 测试API可用性
            Map<String, Object> apis = new HashMap<>();
            apis.put("message", weChatService.message() != null);
            apis.put("user", weChatService.user() != null);
            result.put("apis", apis);

        } catch (Exception e) {
            log.error("Failed to test WeChat service", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }

        return result;
    }

    /**
     * 飞书服务测试
     *
     * @return 测试结果
     */
    @GetMapping("/feishu/test")
    @ConditionalOnBean(FeishuService.class)
    public Map<String, Object> testFeishuService() {
        Map<String, Object> result = new HashMap<>();

        try {
            if (feishuService == null) {
                result.put("success", false);
                result.put("error", "FeishuService not available");
                return result;
            }

            FeishuService.ServiceStatus status = feishuService.getStatus();

            result.put("success", true);
            result.put("service", "Feishu");
            result.put("status", status.toString());
            result.put("available", feishuService.isAvailable());
            result.put("config", feishuService.getConfig().getClass().getSimpleName());

            // 测试API可用性
            Map<String, Object> apis = new HashMap<>();
            apis.put("message", feishuService.message() != null);
            apis.put("user", feishuService.user() != null);
            result.put("apis", apis);

        } catch (Exception e) {
            log.error("Failed to test Feishu service", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }

        return result;
    }

    /**
     * 今日头条服务测试
     *
     * @return 测试结果
     */
    @GetMapping("/toutiao/test")
    @ConditionalOnBean(ToutiaoService.class)
    public Map<String, Object> testToutiaoService() {
        Map<String, Object> result = new HashMap<>();

        try {
            if (toutiaoService == null) {
                result.put("success", false);
                result.put("error", "ToutiaoService not available");
                return result;
            }

            ToutiaoService.ServiceStatus status = toutiaoService.getStatus();

            result.put("success", true);
            result.put("service", "Toutiao");
            result.put("status", status.toString());
            result.put("available", toutiaoService.isAvailable());
            result.put("config", toutiaoService.getConfig().getClass().getSimpleName());

            // 测试API可用性
            Map<String, Object> apis = new HashMap<>();
            apis.put("content", toutiaoService.content() != null);
            apis.put("analytics", toutiaoService.analytics() != null);
            result.put("apis", apis);

        } catch (Exception e) {
            log.error("Failed to test Toutiao service", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }

        return result;
    }

    /**
     * 小红书服务测试
     *
     * @return 测试结果
     */
    @GetMapping("/xiaohongshu/test")
    @ConditionalOnBean(XiaoHongShuService.class)
    public Map<String, Object> testXiaoHongShuService() {
        Map<String, Object> result = new HashMap<>();

        try {
            if (xiaoHongShuService == null) {
                result.put("success", false);
                result.put("error", "XiaoHongShuService not available");
                return result;
            }

            XiaoHongShuService.ServiceStatus status = xiaoHongShuService.getStatus();

            result.put("success", true);
            result.put("service", "XiaoHongShu");
            result.put("status", status.toString());
            result.put("available", xiaoHongShuService.isAvailable());
            result.put("config", xiaoHongShuService.getConfig().getClass().getSimpleName());

            // 测试API可用性
            Map<String, Object> apis = new HashMap<>();
            apis.put("content", xiaoHongShuService.content() != null);
            apis.put("community", xiaoHongShuService.community() != null);
            result.put("apis", apis);

        } catch (Exception e) {
            log.error("Failed to test XiaoHongShu service", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }

        return result;
    }

    /**
     * Bilibili服务测试
     *
     * @return 测试结果
     */
    @GetMapping("/bilibili/test")
    @ConditionalOnBean(BilibiliService.class)
    public Map<String, Object> testBilibiliService() {
        Map<String, Object> result = new HashMap<>();

        try {
            if (bilibiliService == null) {
                result.put("success", false);
                result.put("error", "BilibiliService not available");
                return result;
            }

            BilibiliService.ServiceStatus status = bilibiliService.getStatus();

            result.put("success", true);
            result.put("service", "Bilibili");
            result.put("status", status.toString());
            result.put("available", bilibiliService.isAvailable());
            result.put("config", bilibiliService.getConfig().getClass().getSimpleName());

            // 测试API可用性
            Map<String, Object> apis = new HashMap<>();
            apis.put("video", bilibiliService.video() != null);
            apis.put("community", bilibiliService.community() != null);
            result.put("apis", apis);

        } catch (Exception e) {
            log.error("Failed to test Bilibili service", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }

        return result;
    }
}
